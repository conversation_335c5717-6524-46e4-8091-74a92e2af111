<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_question_first_click_area_lang}}`.
 */
class m250527_101116_create_foquz_question_first_click_area_lang_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_question_first_click_area_lang}}', [
            'id' => $this->primaryKey(),
            'setting_id' => $this->integer()->notNull(),
            'poll_lang_id' => $this->integer()->notNull(),
            'name' => $this->string()->null()->defaultValue(null)
        ]);
        $this->addForeignKey(
            'fk-foquz_question_first_click_area_lang-setting_id',
            '{{%foquz_question_first_click_area_lang}}',
            'setting_id',
            '{{%foquz_question_first_click_area}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-foquz_question_first_click_area_lang-poll_lang_id',
            '{{%foquz_question_first_click_area_lang}}',
            'poll_lang_id',
            '{{%poll_lang}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-foquz_question_first_click_area_lang-setting_id', '{{%foquz_question_first_click_area_lang}}');
        $this->dropForeignKey('fk-foquz_question_first_click_area_lang-poll_lang_id', '{{%foquz_question_first_click_area_lang}}');
        $this->dropTable('{{%foquz_question_first_click_area_lang}}');
    }
}
