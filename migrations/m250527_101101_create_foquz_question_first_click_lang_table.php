<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_question_first_click_lang}}`.
 */
class m250527_101101_create_foquz_question_first_click_lang_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_question_first_click_lang}}', [
            'id' => $this->primaryKey(),
            'setting_id' => $this->integer()->notNull(),
            'poll_lang_id' => $this->integer()->notNull(),
            'button_text' => $this->string()->null()->defaultValue(null)
        ]);
        $this->addForeignKey(
            'fk-foquz_question_first_click_lang-setting_id',
            '{{%foquz_question_first_click_lang}}',
            'setting_id',
            '{{%foquz_question_first_click}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-foquz_question_first_click_lang-poll_lang_id',
            '{{%foquz_question_first_click_lang}}',
            'poll_lang_id',
            '{{%poll_lang}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-foquz_question_first_click_lang-setting_id', '{{%foquz_question_first_click_lang}}');
        $this->dropForeignKey('fk-foquz_question_first_click_lang-poll_lang_id', '{{%foquz_question_first_click_lang}}');
        $this->dropTable('{{%foquz_question_first_click_lang}}');
    }
}
