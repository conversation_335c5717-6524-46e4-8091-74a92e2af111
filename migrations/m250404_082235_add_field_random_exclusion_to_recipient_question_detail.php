<?php

use yii\db\Migration;

/**
 * Class m250404_082235_add_field_random_exclusion_to_recipient_question_detail
 */
class m250404_082235_add_field_random_exclusion_to_recipient_question_detail extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%recipient_question_detail}}', 'random_exclusion', $this->boolean()->defaultValue(false)
            ->comment('Вариант исключен из случайного порядка'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%recipient_question_detail}}', 'random_exclusion');
    }

}
