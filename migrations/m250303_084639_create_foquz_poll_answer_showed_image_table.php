<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_poll_answer_showed_image}}`.
 */
class m250303_084639_create_foquz_poll_answer_showed_image_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_poll_answer_showed_image}}', [
            'id' => $this->primaryKey(),
            'foquz_poll_answer_id' => $this->integer(11)->notNull(),
            'foquz_question_id' => $this->integer(11)->notNull(),
            'showed' => $this->boolean()->defaultValue(true),

        ]);
        $this->addForeignKey(
            'fk_foquz_poll_answer_showed_image_answer_id',
            '{{%foquz_poll_answer_showed_image}}',
            'foquz_poll_answer_id',
            '{{%foquz_poll_answer}}',
            'id',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk_foquz_poll_answer_showed_image_question_id',
            '{{%foquz_poll_answer_showed_image}}',
            'foquz_question_id',
            '{{%foquz_question}}',
            'id',
            'CASCADE'
        );
        $this->createIndex(
            'idx_foquz_poll_answer_showed_image_answer_id_question_id',
            '{{%foquz_poll_answer_showed_image}}',
            ['foquz_poll_answer_id', 'foquz_question_id'],
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_foquz_poll_answer_showed_image_answer_id', '{{%foquz_poll_answer_showed_image}}');
        $this->dropForeignKey('fk_foquz_poll_answer_showed_image_question_id', '{{%foquz_poll_answer_showed_image}}');
        $this->dropTable('{{%foquz_poll_answer_showed_image}}');
    }
}
