<?php

use yii\db\Migration;

/**
 * Class m250312_090000_alter_status_service_column_in_foquz_poll_mailing_list_send_table
 */
class m250312_090000_alter_status_service_column_in_foquz_poll_mailing_list_send_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn(
            '{{%foquz_poll_mailing_list_send}}',
            'status_service', $this->string(2000)->comment('Статус от Sendsay через пять минут после отправки'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn(
            '{{%foquz_poll_mailing_list_send}}',
            'status_service', $this->string(500)->comment('Статус от Sendsay через пять минут после отправки'));
    }
}
