<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_poll_answer_quotes_group}}`.
 */
class m241211_111139_create_foquz_poll_answer_quotes_group_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_poll_answer_quotes_group}}', [
            'id' => $this->primaryKey(),
            'quote_id' => $this->integer(),
            'logic_operation' => $this->tinyInteger()->notNull()->defaultValue(0)->comment('Логика работы условий в группе'),
            'created_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'created_by' => $this->integer()->null(),
            'updated_by' => $this->integer()->null(),
            'deleted_at' => $this->dateTime()->null(),
            'deleted_by' => $this->integer()->null(),
        ]);

        $this->addForeignKey(
            'foquz_poll_answer_quotes_group-quote_id',
            '{{%foquz_poll_answer_quotes_group}}',
            'quote_id',
            '{{%foquz_poll_answer_quotes}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes_group-created_by',
            '{{%foquz_poll_answer_quotes_group}}',
            'created_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes_group-updated_by',
            '{{%foquz_poll_answer_quotes_group}}',
            'updated_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes_group-deleted_by',
            '{{%foquz_poll_answer_quotes_group}}',
            'deleted_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('foquz_poll_answer_quotes_group-created_by', '{{%foquz_poll_answer_quotes_group}}');
        $this->dropForeignKey('foquz_poll_answer_quotes_group-updated_by', '{{%foquz_poll_answer_quotes_group}}');
        $this->dropForeignKey('foquz_poll_answer_quotes_group-deleted_by', '{{%foquz_poll_answer_quotes_group}}');
        $this->dropForeignKey('foquz_poll_answer_quotes_group-quote_id', '{{%foquz_poll_answer_quotes_group}}');
        $this->dropTable('{{%foquz_poll_answer_quotes_group}}');
    }
}
