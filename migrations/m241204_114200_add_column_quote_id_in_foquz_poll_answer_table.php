<?php

use yii\db\Migration;

/**
 * Class m241204_114200_add_column_quote_id_in_foquz_poll_answer_table
 */
class m241204_114200_add_column_quote_id_in_foquz_poll_answer_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll_answer}}', 'quote_id', $this->integer()->null());
        $this->addForeignKey(
            'fk-foquz_poll_answer-quote_id',
            '{{%foquz_poll_answer}}',
            'quote_id',
            '{{%foquz_poll_link_quotes}}',
            'id',
            'NO ACTION'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-foquz_poll_answer-quote_id', '{{%foquz_poll_answer}}');
        $this->dropColumn('{{%foquz_poll_answer}}', 'quote_id');
    }
}
