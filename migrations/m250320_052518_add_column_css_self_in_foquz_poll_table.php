<?php

use yii\db\Migration;

/**
 * Class m250320_052518_add_column_css_self_in_foquz_poll_table
 */
class m250320_052518_add_column_css_self_in_foquz_poll_table extends Migration
{

    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll}}','css_self_type', $this->tinyInteger()->notNull()->defaultValue(0)
            ->comment('Тип своих css-стилей'));
        $this->addColumn('{{%foquz_poll}}','css_self_url', $this->string()->null()
            ->comment('Url css-файла'));
        $this->addColumn('{{%foquz_poll}}','css_self_text', $this->text()->null()
            ->comment('Текст css'));
    }


    public function safeDown()
    {
        $this->dropColumn('{{%foquz_poll}}','css_self_type');
        $this->dropColumn('{{%foquz_poll}}','css_self_url');
        $this->dropColumn('{{%foquz_poll}}','css_self_text');
    }
}
