<?php

use yii\db\Migration;

/**
 * Class m250403_090717_add_field_random_exclusion_to_foquz_question_detail_table
 */
class m250403_090717_add_field_random_exclusion_to_foquz_question_detail_table extends Migration
{
    public function safeUp()
    {
        $this->addColumn('{{%foquz_question_detail}}', 'random_exclusion', $this->boolean()->defaultValue(false)
            ->comment('Вариант исключен из случайного порядка'));
        $this->addColumn('{{%foquz_question}}', 'self_variant_random_exclusion', $this->boolean()->defaultValue(false)
            ->comment('Свой вариант исключен из случайного порядка')->after('self_variant_text'));
    }

    public function safeDown()
    {
        $this->dropColumn('{{%foquz_question}}', 'self_variant_random_exclusion');
        $this->dropColumn('{{%foquz_question_detail}}', 'random_exclusion');
    }
}
