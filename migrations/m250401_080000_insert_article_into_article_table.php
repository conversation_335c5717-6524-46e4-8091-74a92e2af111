<?php

use yii\db\Migration;

class m250401_080000_insert_article_into_article_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
       $this->insert('article', [
            'name' => 'kluchevie-izmeneniya-foquz2024',
            'date' => '2024-12-15',
            'author_id' => 2,
            'polls_category' => 'upravlenie-klientskim-opytom',
            'title' => 'Приключения FOQUZ в 2024 году: ключевые изменения сервиса',
            'seo_title' => 'Ключевые изменения платформы опросов FOQUZ в 2024 году',
            'seo_description' => 'Список изменений платформы FOQUZ в 2024 году, что изменилось, оповещения, SPSS - выгрузка и многое другое',
            'reading_time' => 11,
            'count_views' => 2000,
            'img_url' => '/img/topics/features-2024/1.jpg',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m240724_101850_insert_article_into_article_table cannot be reverted.\n";

        return false;
    }
}
