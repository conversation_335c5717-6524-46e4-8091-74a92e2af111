<?php

use yii\db\Migration;

/**
 * Class m241203_114218_add_columns_in_foquz_poll_link_quotes_table
 */
class m241203_114218_add_columns_in_foquz_poll_link_quotes_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll_link_quotes}}', 'created_at', $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'));
        $this->addColumn('{{%foquz_poll_link_quotes}}', 'created_by', $this->integer()->notNull());
        $this->addColumn('{{%foquz_poll_link_quotes}}', 'updated_at', $this->timestamp()->null()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        $this->addColumn('{{%foquz_poll_link_quotes}}', 'updated_by', $this->integer()->notNull());

        $this->createIndex('idx-foquz_poll_link_quotes-created_at', '{{%foquz_poll_link_quotes}}', 'created_at');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%foquz_poll_link_quotes}}', 'created_at');
        $this->dropColumn('{{%foquz_poll_link_quotes}}', 'created_by');
        $this->dropColumn('{{%foquz_poll_link_quotes}}', 'updated_at');
        $this->dropColumn('{{%foquz_poll_link_quotes}}', 'updated_by');
    }

}
