<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_poll_answer_quotes}}`.
 */
class m241211_111136_create_foquz_poll_answer_quotes_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_poll_answer_quotes}}', [
            'id' => $this->primaryKey(),
            'link_quote_id' => $this->integer()->notNull(),
            'answers_limit' => $this->integer()->null()->comment('Лимит количества ответов'),
            'logic_operation' => $this->tinyInteger()->notNull()->defaultValue(0)->comment('Логика работы условий/групп'),
            'created_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'created_by' => $this->integer()->null(),
            'updated_by' => $this->integer()->null(),
            'deleted_at' => $this->dateTime()->null(),
            'deleted_by' => $this->integer()->null(),
        ]);

        $this->addForeignKey(
            'foquz_poll_answer_quotes-link_quote_id',
            '{{%foquz_poll_answer_quotes}}',
            'link_quote_id',
            '{{%foquz_poll_link_quotes}}',
            'id',
            'RESTRICT',
            'CASCADE'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes-created_by',
            '{{%foquz_poll_answer_quotes}}',
            'created_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes-updated_by',
            '{{%foquz_poll_answer_quotes}}',
            'updated_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes-deleted_by',
            '{{%foquz_poll_answer_quotes}}',
            'deleted_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('foquz_poll_answer_quotes-deleted_by', '{{%foquz_poll_answer_quotes}}');
        $this->dropForeignKey('foquz_poll_answer_quotes-updated_by', '{{%foquz_poll_answer_quotes}}');
        $this->dropForeignKey('foquz_poll_answer_quotes-created_by', '{{%foquz_poll_answer_quotes}}');
        $this->dropForeignKey('foquz_poll_answer_quotes-link_quote_id', '{{%foquz_poll_answer_quotes}}');
        $this->dropTable('{{%foquz_poll_answer_quotes}}');
    }
}
