<?php

use yii\db\Migration;

/**
 * Class m241204_125223_change_columns_type_in_foquz_poll_link_quotes
 */
class m241204_125223_change_columns_type_in_foquz_poll_link_quotes extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('{{%foquz_poll_link_quotes}}', 'created_at', $this->dateTime()
            ->notNull()->defaultExpression('CURRENT_TIMESTAMP'));

        $this->alterColumn('{{%foquz_poll_link_quotes}}', 'updated_at', $this->dateTime()
            ->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

        $this->alterColumn('{{%foquz_poll_link_quotes}}', 'datetime_end', $this->dateTime()
            ->null());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('{{%foquz_poll_link_quotes}}', 'created_at', $this->timestamp()
            ->notNull()->defaultExpression('CURRENT_TIMESTAMP'));

        $this->alterColumn('{{%foquz_poll_link_quotes}}', 'updated_at', $this->timestamp()
            ->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

        $this->alterColumn('{{%foquz_poll_link_quotes}}', 'datetime_end', $this->timestamp()
            ->null());

    }

}
