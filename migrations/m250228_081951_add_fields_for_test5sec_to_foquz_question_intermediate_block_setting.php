<?php

use yii\db\Migration;

/**
 * Class m250228_081951_add_fields_for_test5sec_to_foquz_question_intermediate_block_setting
 */
class m250228_081951_add_fields_for_test5sec_to_foquz_question_intermediate_block_setting extends Migration
{
    public function safeUp()
    {
        $this->addColumn('{{%foquz_question_intermediate_block_setting}}', 'show_bg_instruction',
            $this->boolean()->notNull()->defaultValue(false)->comment('Показывать инструкцию на подложке'));
        $this->addColumn('{{%foquz_question_intermediate_block_setting}}', 'image_show_time',
            $this->tinyInteger()->null()->comment('Время показа изображения, секунд'));
        $this->addColumn('{{%foquz_question_intermediate_block_setting}}', 'show_image_button_text',
            $this->string()->null()->comment('Текст кнопки Показать изображение'));

        $this->addColumn('{{%foquz_question_intermediate_block_setting_lang}}', 'show_image_button_text',
            $this->string()->null()->comment('Текст перевода кнопки Показать изображение'));
    }

    public function safeDown()
    {
        $this->dropColumn('{{%foquz_question_intermediate_block_setting}}', 'show_bg_instruction');
        $this->dropColumn('{{%foquz_question_intermediate_block_setting}}', 'image_show_time');
        $this->dropColumn('{{%foquz_question_intermediate_block_setting}}', 'show_image_button_text');
        $this->dropColumn('{{%foquz_question_intermediate_block_setting_lang}}', 'show_image_button_text');
    }

}
