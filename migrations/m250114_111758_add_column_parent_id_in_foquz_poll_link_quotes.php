<?php

use yii\db\Migration;

/**
 * Class m250114_111758_add_column_parent_id_in_foquz_poll_link_quotes
 */
class m250114_111758_add_column_parent_id_in_foquz_poll_link_quotes extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn(
            '{{%foquz_poll_link_quotes}}',
            'parent_id',
            $this->integer()->null()->after('id')
        );

        $this->addForeignKey(
            'fk-foquz_poll_link_quotes-parent_id',
            '{{%foquz_poll_link_quotes}}',
            'parent_id',
            '{{%foquz_poll_link_quotes}}',
            'id',
            'NO ACTION'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-foquz_poll_link_quotes-parent_id', '{{%foquz_poll_link_quotes}}');
        $this->dropColumn('{{%foquz_poll_link_quotes}}', 'parent_id');
    }
}
