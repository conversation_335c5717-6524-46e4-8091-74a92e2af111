<?php

use yii\db\Migration;

/**
 * Class m250404_055150_drop_column_self_variant_random_exclusion_from_foquz_question
 */
class m250404_055150_drop_column_self_variant_random_exclusion_from_foquz_question extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->dropColumn('{{%foquz_question}}', 'self_variant_random_exclusion');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->addColumn('{{%foquz_question}}', 'self_variant_random_exclusion', $this->boolean()->defaultValue(false)
            ->comment('Свой вариант исключен из случайного порядка')->after('self_variant_text'));
    }

}
