<?php

use yii\db\Migration;


class m250124_085305_add_columns_to_foquz_poll_design_table extends Migration
{

    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll_design}}', 'in_use_cover',
            $this->tinyInteger()->defaultValue(0)->comment('Показ верхнего колонтитула'));
        $this->addColumn('{{%foquz_poll_design}}', 'cover_image',
            $this->string()->null()->comment('Изображение для верхнего колонтитула'));
        $this->addColumn('{{%foquz_poll_design}}', 'cover_position',
            $this->tinyInteger()->defaultValue(1)->comment('Видимая область изображения для верхнего колонтитула'));
        $this->addColumn('{{%foquz_poll_design}}', 'cover_only_first_page',
            $this->tinyInteger()->defaultValue(0)->comment('Показ верхнего колонтитула только на первой странице'));
        $this->addColumn('{{%foquz_poll_design}}', 'cover_full_width',
            $this->tinyInteger()->defaultValue(0)->comment('Растянуть верхний колонтитул на всю ширину экрана'));

        $this->addColumn('{{%foquz_poll_design_templates}}', 'in_use_cover',
            $this->tinyInteger()->defaultValue(0)->comment('Показ верхнего колонтитула'));
        $this->addColumn('{{%foquz_poll_design_templates}}', 'cover_image',
            $this->string()->null()->comment('Изображение для верхнего колонтитула'));
        $this->addColumn('{{%foquz_poll_design_templates}}', 'cover_position',
            $this->tinyInteger()->defaultValue(1)->comment('Видимая область изображения для верхнего колонтитула'));
        $this->addColumn('{{%foquz_poll_design_templates}}', 'cover_only_first_page',
            $this->tinyInteger()->defaultValue(0)->comment('Показ верхнего колонтитула только на первой странице'));
        $this->addColumn('{{%foquz_poll_design_templates}}', 'cover_full_width',
            $this->tinyInteger()->defaultValue(0)->comment('Растянуть верхний колонтитул на всю ширину экрана'));
    }


    public function safeDown()
    {
        $this->dropColumn('{{%foquz_poll_design}}', 'in_use_cover');
        $this->dropColumn('{{%foquz_poll_design}}', 'cover_image');
        $this->dropColumn('{{%foquz_poll_design}}', 'cover_position');
        $this->dropColumn('{{%foquz_poll_design}}', 'cover_only_first_page');
        $this->dropColumn('{{%foquz_poll_design}}', 'cover_full_width');

        $this->dropColumn('{{%foquz_poll_design_templates}}', 'in_use_cover');
        $this->dropColumn('{{%foquz_poll_design_templates}}', 'cover_image');
        $this->dropColumn('{{%foquz_poll_design_templates}}', 'cover_position');
        $this->dropColumn('{{%foquz_poll_design_templates}}', 'cover_only_first_page');
        $this->dropColumn('{{%foquz_poll_design_templates}}', 'cover_full_width');
    }
}
