<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_poll_link_quotes}}`.
 */
class m241129_094237_create_foquz_poll_link_quotes_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_poll_link_quotes}}', [
            'id' => $this->primaryKey(),
            'poll_id' => $this->integer()->null(),
            'key' => $this->string(255)->null(),
            'name' => $this->string(255)->null()->comment('Название ссылки'),
            'limit' => $this->integer()->null()->comment('Максимум ответов по ссылке'),
            'datetime_end' => $this->timestamp()->null()->comment('Дата окончания приёма ответов по ссылке'),
            'active' => $this->smallInteger()->notNull()->defaultValue(1)->comment('Активность ссылки'),
        ]);

        $this->addForeignKey(
            'fk-foquz_poll_link_quotes-poll_id',
            '{{%foquz_poll_link_quotes}}',
            'poll_id',
            '{{%foquz_poll}}',
            'id',
            'CASCADE'
        );

        $this->addColumn('{{%filial_poll_key}}', 'quote_id', $this->integer()->null());
        $this->createIndex('idx-filial_poll_key-quote_id', '{{%filial_poll_key}}', 'quote_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%filial_poll_key}}', 'quote_id');
        $this->dropForeignKey('fk-foquz_poll_link_quotes-poll_id', '{{%foquz_poll_link_quotes}}');
        $this->dropTable('{{%foquz_poll_link_quotes}}');
    }
}
