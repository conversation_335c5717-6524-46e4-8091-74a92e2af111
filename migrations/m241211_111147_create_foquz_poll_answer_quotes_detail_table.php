<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_poll_answer_quotes_detail}}`.
 */
class m241211_111147_create_foquz_poll_answer_quotes_detail_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_poll_answer_quotes_detail}}', [
            'id' => $this->primaryKey(),
            'quote_id' => $this->integer(),
            'group_id' => $this->integer()->null(),
            'question_id' => $this->integer(),
            'behavior'=> $this->integer()->comment('Варианты действий респондента'),
            'variants' => $this->json()->null()->comment('Варианты ответов'),
            'created_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->dateTime()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'created_by' => $this->integer()->null(),
            'updated_by' => $this->integer()->null(),
            'deleted_at' => $this->dateTime()->null(),
            'deleted_by' => $this->integer()->null(),
        ]);

        $this->addForeignKey(
            'foquz_poll_answer_quotes_detail-quote_id',
            '{{%foquz_poll_answer_quotes_detail}}',
            'quote_id',
            '{{%foquz_poll_answer_quotes}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes_detail-group_id',
            '{{%foquz_poll_answer_quotes_detail}}',
            'group_id',
            '{{%foquz_poll_answer_quotes_group}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes_detail-question_id',
            '{{%foquz_poll_answer_quotes_detail}}',
            'question_id',
            '{{%foquz_question}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes_detail-created_by',
            '{{%foquz_poll_answer_quotes_detail}}',
            'created_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes_detail-updated_by',
            '{{%foquz_poll_answer_quotes_detail}}',
            'updated_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'foquz_poll_answer_quotes_detail-deleted_by',
            '{{%foquz_poll_answer_quotes_detail}}',
            'deleted_by',
            '{{%user}}',
            'id',
            'SET NULL'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('foquz_poll_answer_quotes_detail-deleted_by', '{{%foquz_poll_answer_quotes_detail}}');
        $this->dropForeignKey('foquz_poll_answer_quotes_detail-updated_by', '{{%foquz_poll_answer_quotes_detail}}');
        $this->dropForeignKey('foquz_poll_answer_quotes_detail-created_by', '{{%foquz_poll_answer_quotes_detail}}');
        $this->dropForeignKey('foquz_poll_answer_quotes_detail-question_id', '{{%foquz_poll_answer_quotes_detail}}');
        $this->dropForeignKey('foquz_poll_answer_quotes_detail-group_id', '{{%foquz_poll_answer_quotes_detail}}');
        $this->dropForeignKey('foquz_poll_answer_quotes_detail-quote_id', '{{%foquz_poll_answer_quotes_detail}}');
        $this->dropTable('{{%foquz_poll_answer_quotes_detail}}');
    }
}
