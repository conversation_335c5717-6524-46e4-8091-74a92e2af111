<?php

use yii\db\Migration;

/**
 * Class m250214_053648_add_position_to_foquz_poll_answer_quotes_group
 */
class m250214_053648_add_position_to_foquz_poll_answer_quotes_group extends Migration
{
    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll_answer_quotes_group}}', 'position', $this->integer()->null()->comment('Позиция элемента'));
        $this->addColumn('{{%foquz_poll_answer_quotes_detail}}', 'position', $this->integer()->null()->comment('Позиция элемента'));
    }

    public function safeDown()
    {
        $this->dropColumn('{{%foquz_poll_answer_quotes_group}}', 'position');
        $this->dropColumn('{{%foquz_poll_answer_quotes_detail}}', 'position');
    }

}
