Browserslist: caniuse-lite is outdated. Please run:
  npx update-browserslist-d[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by path *.js 89.2 MiB
  assets by status 60.5 MiB [emitted]
    assets by info 1.84 MiB [immutable] 128 assets
    assets by status 58.7 MiB [compared for emit]
      asset poll.process.js 5.7 MiB [emitted] [compared for emit] (name: poll.process) 1 related asset
      + 24 assets
  + 58 assets
assets by path *.css 9.79 MiB
  asset poll.process.css 2.04 MiB [compared for emit] (name: poll.process) 1 related asset
  asset layout-requests.css 1.74 MiB [compared for emit] (name: layout-requests) 1 related asset
  asset layout-base.css 1.73 MiB [compared for emit] (name: layout-base) 1 related asset
  asset layout-executor.css 1.03 MiB [compared for emit] (name: layout-executor) 1 related asset
  asset layout-external.css 1.03 MiB [compared for emit] (name: layout-external) 1 related asset
  asset main.2.css 275 KiB [compared for emit] (name: main.2) 1 related asset
  + 72 assets
runtime modules 261 KiB 648 modules
orphan modules 8.34 KiB [orphan] 7 modules
modules by path ./ko/ 10 MiB (javascript) 8.8 MiB (css/mini-extract)
  cacheable modules 10 MiB 4234 modules
  css modules 8.8 MiB 720 modules
  ./ko/utils/engine/ lazy ^.*$ namespace object 160 bytes [built] [code generated]
  ./ko/icons/ sync \.svg$ 7.69 KiB [built] [code generated]
modules by path ./node_modules/ 3.73 MiB (javascript) 47.8 KiB (css/mini-extract) 1259 modules
modules by path ./messages/ 493 KiB 123 modules
1 WARNING in child compilations (Use 'stats.children: true' resp. '--stats-children' for more details)
webpack 5.81.0 compiled with 1 warning in 63849 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.3c3f6398d17877436cc1.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.3c3f6398d17877436cc1.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6606 modules
runtime modules 200 KiB 384 modules
javascript modules 8 KiB
  ./ko/utils/engine/ lazy ^.*$ namespace object 160 bytes [built]
  ./ko/icons/ sync \.svg$ 7.69 KiB [built]
  ./messages/ lazy ^\.\/.*\.json$ namespace object 160 bytes [built]
webpack 5.81.0 compiled successfully in 4944 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 36.2 MiB [cached] 134 assets
assets by path *.js 60.5 MiB
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.1fbfa5126e8150bf6e26.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.1fbfa5126e8150bf6e26.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.1fbfa5126e8150bf6e26.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.1fbfa5126e8150bf6e26.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.1fbfa5126e8150bf6e26.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.1fbfa5126e8150bf6e26.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.1fbfa5126e8150bf6e26.js 28.5 KiB [emitted] [immutable]
    + 121 assets
  + 25 assets
assets by path *.css 2.31 MiB
  asset poll.process.css 2.04 MiB [emitted] (name: poll.process) 1 related asset
  asset main.2.css 276 KiB [emitted] (name: main.2) 1 related asset
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6604 modules
runtime modules 200 KiB 384 modules
modules by layer 44.6 KiB (javascript) 4.26 KiB (css/mini-extract)
  modules by path ./ko/dialogs/first-click-areas-sidesheet/*.less 50 bytes (javascript) 4.26 KiB (css/mini-extract)
    ./ko/dialogs/first-click-areas-sidesheet/style.less 50 bytes [built]
    css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/less-loader/dist/cjs.js!./ko/dialogs/first-click-areas-sidesheet/style.less 4.26 KiB [code generated]
  ./ko/components/image-click-areas/model.js 27.9 KiB [built] [code generated]
  ./ko/dialogs/first-click-areas-sidesheet/model.js 10.9 KiB [built] [code generated]
  ./ko/dialogs/first-click-areas-sidesheet/template.html 5.88 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 17491 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.834001d0ae9043f4cf25.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.834001d0ae9043f4cf25.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
javascript modules 16.3 KiB
  ./ko/dialogs/first-click-areas-sidesheet/model.js 10.8 KiB [built] [code generated]
  ./ko/dialogs/first-click-areas-sidesheet/template.html 5.53 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 44352 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.41d079e1a729f56ba32e.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.41d079e1a729f56ba32e.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/template.html 5.54 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 25650 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 36.2 MiB [cached] 134 assets
assets by path *.js 60.5 MiB
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.d1643c68a874c17d5833.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.d1643c68a874c17d5833.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.d1643c68a874c17d5833.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.d1643c68a874c17d5833.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.d1643c68a874c17d5833.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.d1643c68a874c17d5833.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.d1643c68a874c17d5833.js 28.5 KiB [emitted] [immutable]
    + 121 assets
  + 25 assets
assets by path *.css 2.31 MiB
  asset poll.process.css 2.04 MiB [emitted] (name: poll.process) 1 related asset
  asset main.2.css 276 KiB [emitted] (name: main.2) 1 related asset
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
modules by layer 50 bytes (javascript) 4.16 KiB (css/mini-extract)
  ./ko/dialogs/first-click-areas-sidesheet/style.less 50 bytes [built]
  css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/less-loader/dist/cjs.js!./ko/dialogs/first-click-areas-sidesheet/style.less 4.16 KiB [code generated]
webpack 5.81.0 compiled successfully in 26639 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 36.2 MiB [cached] 134 assets
assets by path *.js 60.5 MiB
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.df113dfc2036f4ff3ebd.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.df113dfc2036f4ff3ebd.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.df113dfc2036f4ff3ebd.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.df113dfc2036f4ff3ebd.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.df113dfc2036f4ff3ebd.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.df113dfc2036f4ff3ebd.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.df113dfc2036f4ff3ebd.js 28.5 KiB [emitted] [immutable]
    + 121 assets
  + 25 assets
assets by path *.css 2.31 MiB
  asset poll.process.css 2.04 MiB [emitted] (name: poll.process) 1 related asset
  asset main.2.css 276 KiB [emitted] (name: main.2) 1 related asset
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
modules by layer 50 bytes (javascript) 4.1 KiB (css/mini-extract)
  ./ko/dialogs/first-click-areas-sidesheet/style.less 50 bytes [built]
  css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/less-loader/dist/cjs.js!./ko/dialogs/first-click-areas-sidesheet/style.less 4.1 KiB [code generated]
webpack 5.81.0 compiled successfully in 65389 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.3e9e3cdf3c4e2a5073d3.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.3e9e3cdf3c4e2a5073d3.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 10.5 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 35412 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6992 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 10.5 KiB [built]
webpack 5.81.0 compiled successfully in 5544 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.5e126026b772a03da2a4.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.5e126026b772a03da2a4.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 11.4 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 22953 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.3ea830b5230ff423f987.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.3ea830b5230ff423f987.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/template.html 5.58 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 25119 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.17f874578869d8db8dfc.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.17f874578869d8db8dfc.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
javascript modules 17.2 KiB
  ./ko/dialogs/first-click-areas-sidesheet/model.js 11.6 KiB [built] [code generated]
  ./ko/dialogs/first-click-areas-sidesheet/template.html 5.54 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 68217 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6992 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 11.6 KiB [built]
webpack 5.81.0 compiled successfully in 6847 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.04bd711169e5d5a25dbc.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.04bd711169e5d5a25dbc.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 15 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 19903 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6992 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 15 KiB [built]
webpack 5.81.0 compiled successfully in 6039 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.27739f9da14eacb7d54e.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.27739f9da14eacb7d54e.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/template.html 4.8 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 27903 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.d2a6e59fe61d01678408.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.d2a6e59fe61d01678408.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/components/image-click-areas/model.js 28.6 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 20388 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.1f1111fb4ed5128f63d5.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.1f1111fb4ed5128f63d5.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 19.1 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 19602 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6991 modules
javascript modules 47.6 KiB
  ./ko/components/image-click-areas/model.js 28.6 KiB [built]
  ./ko/dialogs/first-click-areas-sidesheet/model.js 19.1 KiB [built]
webpack 5.81.0 compiled successfully in 2972 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.1690eadb5b588a0de74e.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.1690eadb5b588a0de74e.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/components/image-click-areas/model.js 31.1 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 34123 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.3e56c034ebe7ce3d10d6.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.3e56c034ebe7ce3d10d6.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
javascript modules 41.4 KiB
  ./ko/components/image-click-areas/model.js 31.1 KiB [built] [code generated]
  ./ko/dialogs/first-click-areas-sidesheet/model.js 10.2 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 30390 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.a10d24d3909bd7dedcdb.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.a10d24d3909bd7dedcdb.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 13.2 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 18590 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.33417fe64d1e974edf53.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.33417fe64d1e974edf53.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
javascript modules 436 bytes
  ./ko/components/image-click-areas/index.js 397 bytes [code generated]
  ./ko/components/image-click-areas/model.js 39 bytes [built] [code generated] [1 error]

ERROR in ./ko/components/image-click-areas/model.js
Module build failed (from ./node_modules/babel-loader/lib/index.js):
SyntaxError: /Users/<USER>/projects/foquz-repositories/foquz-core/ko/components/image-click-areas/model.js: Unexpected token, expected "," (86:55)

  84 |             if (!area || !ko.isObservable(area.height)) return 50;
  85 |             if (!original || original.height === 0) return 50;
> 86 |             return Math.round(ko.unwrap(are            const original = self.originalImageDimensions();
     |                                                        ^
  87 | a.height) * original.height);
  88 |           } catch (e) {
  89 |             console.warn('[IMAGE-CLICK-AREAS] Error in pixelHeight computed read:', e);
    at constructor (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:351:19)
    at Parser.raise (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3233:19)
    at Parser.unexpected (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3253:16)
    at Parser.expect (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3557:28)
    at Parser.parseCallExpressionArguments (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10742:14)
    at Parser.parseCoverCallAndAsyncArrowHead (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10671:29)
    at Parser.parseSubscript (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10608:19)
    at Parser.parseSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10581:19)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10572:17)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11918:12)
    at Parser.parseMaybeAssignAllowIn (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:17)
    at Parser.parseExprListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11678:18)
    at Parser.parseCallExpressionArguments (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10754:22)
    at Parser.parseCoverCallAndAsyncArrowHead (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10671:29)
    at Parser.parseSubscript (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10608:19)
    at Parser.parseSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10581:19)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10572:17)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at Parser.parseExpressionBase (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10280:23)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10276:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11913:16)
    at Parser.parseExpression (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10276:17)
    at Parser.parseReturnStatement (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12601:28)
    at Parser.parseStatementContent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12252:21)
    at Parser.parseStatementLike (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12221:17)
    at Parser.parseStatementListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12201:17)
    at Parser.parseBlockOrModuleBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12778:61)
    at Parser.parseBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12771:10)
    at Parser.parseBlock (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12759:10)
    at Parser.parseTryStatement (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12667:23)
    at Parser.parseStatementContent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12258:21)
    at Parser.parseStatementLike (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12221:17)
    at Parser.parseStatementListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12201:17)
    at Parser.parseBlockOrModuleBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12778:61)
    at Parser.parseBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12771:10)
    at Parser.parseBlock (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12759:10)
    at Parser.parseFunctionBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11598:24)
    at Parser.parseFunctionBodyAndFinish (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11584:10)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12901:12
    at Parser.withSmartMixTopicForbiddingContext (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11895:14)
    at Parser.parseFunction (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12900:10)
    at Parser.parseFunctionOrFunctionSent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11067:17)
    at Parser.parseExprAtom (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10856:21)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10568:23)
 @ ./ko/components/image-click-areas/index.js 1:0-36 9:17-26
 @ ./ko/components/index.js 16:0-29
 @ ./ko/foquz.js 8:0-22

webpack 5.81.0 compiled with 1 error in 34879 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6992 modules
./ko/components/image-click-areas/model.js 39 bytes [built] [1 error]

ERROR in ./ko/components/image-click-areas/model.js
Module build failed (from ./node_modules/babel-loader/lib/index.js):
SyntaxError: /Users/<USER>/projects/foquz-repositories/foquz-core/ko/components/image-click-areas/model.js: Unexpected token, expected "," (86:55)

  84 |             if (!area || !ko.isObservable(area.height)) return 50;
  85 |             if (!original || original.height === 0) return 50;
> 86 |             return Math.round(ko.unwrap(are            const original = self.originalImageDimensions();
     |                                                        ^
  87 | a.height) * original.height);
  88 |           } catch (e) {
  89 |             console.warn('[IMAGE-CLICK-AREAS] Error in pixelHeight computed read:', e);
    at constructor (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:351:19)
    at Parser.raise (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3233:19)
    at Parser.unexpected (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3253:16)
    at Parser.expect (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3557:28)
    at Parser.parseCallExpressionArguments (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10742:14)
    at Parser.parseCoverCallAndAsyncArrowHead (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10671:29)
    at Parser.parseSubscript (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10608:19)
    at Parser.parseSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10581:19)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10572:17)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11918:12)
    at Parser.parseMaybeAssignAllowIn (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:17)
    at Parser.parseExprListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11678:18)
    at Parser.parseCallExpressionArguments (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10754:22)
    at Parser.parseCoverCallAndAsyncArrowHead (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10671:29)
    at Parser.parseSubscript (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10608:19)
    at Parser.parseSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10581:19)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10572:17)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at Parser.parseExpressionBase (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10280:23)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10276:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11913:16)
    at Parser.parseExpression (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10276:17)
    at Parser.parseReturnStatement (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12601:28)
    at Parser.parseStatementContent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12252:21)
    at Parser.parseStatementLike (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12221:17)
    at Parser.parseStatementListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12201:17)
    at Parser.parseBlockOrModuleBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12778:61)
    at Parser.parseBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12771:10)
    at Parser.parseBlock (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12759:10)
    at Parser.parseTryStatement (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12667:23)
    at Parser.parseStatementContent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12258:21)
    at Parser.parseStatementLike (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12221:17)
    at Parser.parseStatementListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12201:17)
    at Parser.parseBlockOrModuleBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12778:61)
    at Parser.parseBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12771:10)
    at Parser.parseBlock (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12759:10)
    at Parser.parseFunctionBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11598:24)
    at Parser.parseFunctionBodyAndFinish (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11584:10)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12901:12
    at Parser.withSmartMixTopicForbiddingContext (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11895:14)
    at Parser.parseFunction (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12900:10)
    at Parser.parseFunctionOrFunctionSent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11067:17)
    at Parser.parseExprAtom (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10856:21)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10568:23)
 @ ./ko/components/image-click-areas/index.js 1:0-36 9:17-26
 @ ./ko/components/index.js 16:0-29
 @ ./ko/foquz.js 8:0-22

webpack 5.81.0 compiled with 1 error in 13509 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6992 modules
./ko/components/image-click-areas/model.js 39 bytes [built] [1 error]

ERROR in ./ko/components/image-click-areas/model.js
Module build failed (from ./node_modules/babel-loader/lib/index.js):
SyntaxError: /Users/<USER>/projects/foquz-repositories/foquz-core/ko/components/image-click-areas/model.js: Unexpected token, expected "," (86:55)

  84 |             if (!area || !ko.isObservable(area.height)) return 50;
  85 |             if (!original || original.height === 0) return 50;
> 86 |             return Math.round(ko.unwrap(are            const original = self.originalImageDimensions();
     |                                                        ^
  87 | a.height) * original.height);
  88 |           } catch (e) {
  89 |             console.warn('[IMAGE-CLICK-AREAS] Error in pixelHeight computed read:', e);
    at constructor (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:351:19)
    at Parser.raise (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3233:19)
    at Parser.unexpected (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3253:16)
    at Parser.expect (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3557:28)
    at Parser.parseCallExpressionArguments (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10742:14)
    at Parser.parseCoverCallAndAsyncArrowHead (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10671:29)
    at Parser.parseSubscript (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10608:19)
    at Parser.parseSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10581:19)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10572:17)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11918:12)
    at Parser.parseMaybeAssignAllowIn (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:17)
    at Parser.parseExprListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11678:18)
    at Parser.parseCallExpressionArguments (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10754:22)
    at Parser.parseCoverCallAndAsyncArrowHead (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10671:29)
    at Parser.parseSubscript (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10608:19)
    at Parser.parseSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10581:19)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10572:17)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at Parser.parseExpressionBase (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10280:23)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10276:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11913:16)
    at Parser.parseExpression (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10276:17)
    at Parser.parseReturnStatement (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12601:28)
    at Parser.parseStatementContent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12252:21)
    at Parser.parseStatementLike (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12221:17)
    at Parser.parseStatementListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12201:17)
    at Parser.parseBlockOrModuleBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12778:61)
    at Parser.parseBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12771:10)
    at Parser.parseBlock (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12759:10)
    at Parser.parseTryStatement (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12667:23)
    at Parser.parseStatementContent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12258:21)
    at Parser.parseStatementLike (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12221:17)
    at Parser.parseStatementListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12201:17)
    at Parser.parseBlockOrModuleBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12778:61)
    at Parser.parseBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12771:10)
    at Parser.parseBlock (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12759:10)
    at Parser.parseFunctionBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11598:24)
    at Parser.parseFunctionBodyAndFinish (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11584:10)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12901:12
    at Parser.withSmartMixTopicForbiddingContext (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11895:14)
    at Parser.parseFunction (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12900:10)
    at Parser.parseFunctionOrFunctionSent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11067:17)
    at Parser.parseExprAtom (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10856:21)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10568:23)
 @ ./ko/components/image-click-areas/index.js 1:0-36 9:17-26
 @ ./ko/components/index.js 16:0-29
 @ ./ko/foquz.js 8:0-22

webpack 5.81.0 compiled with 1 error in 5991 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.0d58c300e70ff66018cc.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.0d58c300e70ff66018cc.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/components/image-click-areas/model.js 39 bytes [built] [1 error]

ERROR in ./ko/components/image-click-areas/model.js
Module build failed (from ./node_modules/babel-loader/lib/index.js):
SyntaxError: /Users/<USER>/projects/foquz-repositories/foquz-core/ko/components/image-click-areas/model.js: Unexpected token (86:52)

  84 |             if (!area || !ko.isObservable(area.height)) return 50;
  85 |             if (!original || original.height === 0) return 50;
> 86 |             return Math.round(ko.unwrap(            const original = self.originalImageDimensions();
     |                                                     ^
  87 | a.height) * original.height);
  88 |           } catch (e) {
  89 |             console.warn('[IMAGE-CLICK-AREAS] Error in pixelHeight computed read:', e);
    at constructor (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:351:19)
    at Parser.raise (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3233:19)
    at Parser.unexpected (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:3253:16)
    at Parser.parseExprAtom (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10952:16)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10568:23)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11918:12)
    at Parser.parseMaybeAssignAllowIn (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:17)
    at Parser.parseExprListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11678:18)
    at Parser.parseCallExpressionArguments (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10754:22)
    at Parser.parseCoverCallAndAsyncArrowHead (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10671:29)
    at Parser.parseSubscript (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10608:19)
    at Parser.parseSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10581:19)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10572:17)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11918:12)
    at Parser.parseMaybeAssignAllowIn (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10296:17)
    at Parser.parseExprListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11678:18)
    at Parser.parseCallExpressionArguments (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10754:22)
    at Parser.parseCoverCallAndAsyncArrowHead (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10671:29)
    at Parser.parseSubscript (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10608:19)
    at Parser.parseSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10581:19)
    at Parser.parseExprSubscripts (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10572:17)
    at Parser.parseUpdate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10551:21)
    at Parser.parseMaybeUnary (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10529:23)
    at Parser.parseMaybeUnaryOrPrivate (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10383:61)
    at Parser.parseExprOps (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10388:23)
    at Parser.parseMaybeConditional (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10365:23)
    at Parser.parseMaybeAssign (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10326:21)
    at Parser.parseExpressionBase (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10280:23)
    at /Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10276:39
    at Parser.allowInAnd (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:11913:16)
    at Parser.parseExpression (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:10276:17)
    at Parser.parseReturnStatement (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12601:28)
    at Parser.parseStatementContent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12252:21)
    at Parser.parseStatementLike (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12221:17)
    at Parser.parseStatementListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12201:17)
    at Parser.parseBlockOrModuleBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12778:61)
    at Parser.parseBlockBody (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12771:10)
    at Parser.parseBlock (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12759:10)
    at Parser.parseTryStatement (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12667:23)
    at Parser.parseStatementContent (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12258:21)
    at Parser.parseStatementLike (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12221:17)
    at Parser.parseStatementListItem (/Users/<USER>/projects/foquz-repositories/foquz-core/node_modules/@babel/parser/lib/index.js:12201:17)
 @ ./ko/components/image-click-areas/index.js 1:0-36 9:17-26
 @ ./ko/components/index.js 16:0-29
 @ ./ko/foquz.js 8:0-22

webpack 5.81.0 compiled with 1 error in 49956 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 40.3 MiB [cached] 264 assets
assets by status 58.7 MiB [emitted]
  asset poll.process.js 5.71 MiB [emitted] (name: poll.process) 1 related asset
  asset main.2.js 5.18 MiB [emitted] (name: main.2) 1 related asset
  asset answers.main.js 4.98 MiB [emitted] (name: answers.main) 1 related asset
  asset contacts.main.js 4.55 MiB [emitted] (name: contacts.main) 1 related asset
  asset poll.answers-external.js 4.5 MiB [emitted] (name: poll.answers-external) 1 related asset
  asset poll.answers.js 4.48 MiB [emitted] (name: poll.answers) 1 related asset
  asset answers.feedback.js 4.35 MiB [emitted] (name: answers.feedback) 1 related asset
  asset poll.questions.js 3.73 MiB [emitted] (name: poll.questions) 1 related asset
  asset poll.stats.js 3.37 MiB [emitted] (name: poll.stats) 1 related asset
  asset poll.stats.external.js 3.34 MiB [emitted] (name: poll.stats.external) 1 related asset
  asset contact-points.main.js 2.55 MiB [emitted] (name: contact-points.main) 1 related asset
  asset settings.main.js 1.57 MiB [emitted] (name: settings.main) 1 related asset
  + 13 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
javascript modules 31.5 KiB
  ./ko/components/image-click-areas/index.js 397 bytes [code generated]
  ./ko/components/image-click-areas/model.js 31.1 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 77051 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6991 modules
javascript modules 44.3 KiB
  ./ko/components/image-click-areas/model.js 31.1 KiB [built]
  ./ko/dialogs/first-click-areas-sidesheet/model.js 13.2 KiB [built]
webpack 5.81.0 compiled successfully in 9988 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.2d9314c531d2160845a0.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.2d9314c531d2160845a0.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 13.1 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 26639 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.a45dbb5a3e647439a3aa.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.a45dbb5a3e647439a3aa.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 12.4 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 31821 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.a7e1befe7fd94bb81249.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.a7e1befe7fd94bb81249.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/components/image-click-areas/model.js 30.7 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 11610 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6992 modules
./ko/components/image-click-areas/model.js 30.7 KiB [built]
webpack 5.81.0 compiled successfully in 4446 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6992 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 12.4 KiB [built]
webpack 5.81.0 compiled successfully in 12369 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.bf1c040e1c1edf915875.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.bf1c040e1c1edf915875.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/components/image-click-areas/template.html 3.61 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 27056 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 36.2 MiB [cached] 134 assets
assets by path *.js 60.5 MiB
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.b090236eaea9a037e0ec.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.b090236eaea9a037e0ec.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.b090236eaea9a037e0ec.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.b090236eaea9a037e0ec.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.b090236eaea9a037e0ec.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.b090236eaea9a037e0ec.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.b090236eaea9a037e0ec.js 28.5 KiB [emitted] [immutable]
    + 121 assets
  + 25 assets
assets by path *.css 2.31 MiB
  asset poll.process.css 2.04 MiB [emitted] (name: poll.process) 1 related asset
  asset main.2.css 276 KiB [emitted] (name: main.2) 1 related asset
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
modules by layer 50 bytes (javascript) 3.15 KiB (css/mini-extract)
  ./ko/components/image-click-areas/style.less 50 bytes [built]
  css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/less-loader/dist/cjs.js!./ko/components/image-click-areas/style.less 3.15 KiB [code generated]
webpack 5.81.0 compiled successfully in 18803 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.e4dd01048992ef897370.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.e4dd01048992ef897370.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/template.html 5.02 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 32941 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 36.2 MiB [cached] 134 assets
assets by path *.js 60.5 MiB
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.805ac5991a3d7302db2a.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.805ac5991a3d7302db2a.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.805ac5991a3d7302db2a.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.805ac5991a3d7302db2a.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.805ac5991a3d7302db2a.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.805ac5991a3d7302db2a.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.805ac5991a3d7302db2a.js 28.5 KiB [emitted] [immutable]
    + 121 assets
  + 25 assets
assets by path *.css 2.31 MiB
  asset poll.process.css 2.04 MiB [emitted] (name: poll.process) 1 related asset
  asset main.2.css 276 KiB [emitted] (name: main.2) 1 related asset
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
modules by layer 50 bytes (javascript) 4.53 KiB (css/mini-extract)
  ./ko/dialogs/first-click-areas-sidesheet/style.less 50 bytes [built]
  css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/less-loader/dist/cjs.js!./ko/dialogs/first-click-areas-sidesheet/style.less 4.53 KiB [code generated]
webpack 5.81.0 compiled successfully in 21301 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 36.2 MiB [cached] 134 assets
assets by path *.js 60.5 MiB
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.5013e3c87921b3972827.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.5013e3c87921b3972827.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.5013e3c87921b3972827.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.5013e3c87921b3972827.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.5013e3c87921b3972827.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.5013e3c87921b3972827.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.5013e3c87921b3972827.js 28.5 KiB [emitted] [immutable]
    + 121 assets
  + 25 assets
assets by path *.css 2.31 MiB
  asset poll.process.css 2.04 MiB [emitted] (name: poll.process) 1 related asset
  asset main.2.css 276 KiB [emitted] (name: main.2) 1 related asset
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
modules by layer 50 bytes (javascript) 4.49 KiB (css/mini-extract)
  ./ko/dialogs/first-click-areas-sidesheet/style.less 50 bytes [built]
  css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/less-loader/dist/cjs.js!./ko/dialogs/first-click-areas-sidesheet/style.less 4.49 KiB [code generated]
webpack 5.81.0 compiled successfully in 15377 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.eef243346b98ed468646.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.eef243346b98ed468646.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/template.html 5.04 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 24608 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 36.2 MiB [cached] 134 assets
assets by path *.js 60.5 MiB
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.101d8bfdfe1e872bb843.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.101d8bfdfe1e872bb843.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.101d8bfdfe1e872bb843.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.101d8bfdfe1e872bb843.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.101d8bfdfe1e872bb843.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.101d8bfdfe1e872bb843.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.101d8bfdfe1e872bb843.js 28.5 KiB [emitted] [immutable]
    + 121 assets
  + 25 assets
assets by path *.css 2.31 MiB
  asset poll.process.css 2.04 MiB [emitted] (name: poll.process) 1 related asset
  asset main.2.css 276 KiB [emitted] (name: main.2) 1 related asset
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6607 modules
runtime modules 200 KiB 384 modules
modules by layer 50 bytes (javascript) 4.13 KiB (css/mini-extract)
  ./ko/dialogs/first-click-areas-sidesheet/style.less 50 bytes [built]
  css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/less-loader/dist/cjs.js!./ko/dialogs/first-click-areas-sidesheet/style.less 4.13 KiB [code generated]
webpack 5.81.0 compiled successfully in 20470 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 99 MiB [cached] 289 assets
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 261 KiB (runtime) [cached] 6992 modules
./ko/dialogs/first-click-areas-sidesheet/style.less 50 bytes [built]
webpack 5.81.0 compiled successfully in 29445 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.ad26872b1191f239c928.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.ad26872b1191f239c928.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.2 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/model.js 12.5 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 27510 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.5 MiB [cached] 136 assets
assets by status 60.5 MiB [emitted]
  assets by info 1.84 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.07841d50006b196cb94f.js 1.11 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.07841d50006b196cb94f.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.85 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6608 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/first-click-areas-sidesheet/template.html 5.06 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 27438 ms
[webpack-cli] watching files for updates...
