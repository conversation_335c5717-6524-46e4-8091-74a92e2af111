<?php

use app\components\GetHostComponent;
use app\models\company\Company;
use app\models\User;
use webvimark\modules\UserManagement\models\UserVisitLog;

$params = array_merge(
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php'
);
$db = require __DIR__ . '/test_db.php';

$config = [
    'id' => 'basic-test',
    'basePath' => dirname(__DIR__),
    'bootstrap' => [
        'log',
        GetHostComponent::class,
    ],
    'language' => 'ru-RU',
    'defaultRoute' => 'site/index',
    'homeUrl' => '/foquz',
    'sourceLanguage' => 'ru-RU',
    'timeZone' => 'Europe/Moscow',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm' => '@vendor/npm-asset',
    ],
    'components' => [
        'session' => [
            'cookieParams' => [
                //'domain' => $params['company_domen'],
                'httpOnly' => true,
                'path' => '/',
            ],
        ],
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => 'testcookie',
            'csrfCookie' => [
                'name' => '_csrf',
                'path' => '/',
                //'domain' => $params['company_domen'],
            ],
            'parsers' => [
                'multipart/form-data' => 'yii\web\MultipartFormDataParser',
            ]
        ],
        'response' => 'app\response\Response',
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        /*   'user' => [
               'identityClass' => 'app\models\User',
               'enableAutoLogin' => true,
           ],*/
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
        ],
        'assetManager' => [
            'basePath' => __DIR__ . '/../web/assets',
        ],
        'user' => [
            'identityCookie' => [
                'path' => '/',
                'name' => '_identity',
                //'domain' => $params['company_domen'],
            ],
            'class' => 'webvimark\modules\UserManagement\components\UserConfig',
            'identityClass' => User::class,
            // Comment this if you don't want to record user logins
            'on afterLogin' => function ($event) {
                UserVisitLog::newVisitor($event->identity->id);
            }
        ],
        'errorHandler' => [
            'errorAction' => 'foquz/default/error',
        ],
       // 'mailer' => require __DIR__ . '/mailer.php',
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'db' => $db,
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
                'GET /p/<id:\w+>' => 'foquz/default/anonymous',
                'GET /q<id:\w+>' => 'site/shortlink'
            ],
        ],
        'view' => [
            'theme' => [
                'pathMap' => [
                    '@vendor/webvimark/module-user-management/views' => '@app/views/user',
                ],
            ],
        ],
        'qr' => [
            'class' => '\Da\QrCode\Component\QrCodeComponent',
        ],
        'consoleRunner' => [
            'class' => 'vova07\console\ConsoleRunner',
            'file' => '@app/yii' // or an absolute path to console file
        ]
    ],
    'modules' => [
        'foquz' => [
            'class' => 'app\modules\foquz\Module',
            'layout' => 'foquz',
            'on beforeAction' => function(yii\base\ActionEvent $event) {
                if(!Yii::$app->user->isGuest) {
                    $host = $_SERVER['HTTP_HOST'];
                    $company = Company::findOne(Yii::$app->user->identity->company->id);
                    if($host !== $company->alias) {
                        header('Location: '.'https://'.strtolower($company->alias).Yii::$app->request->url);
                        //return Yii::$app->response->redirect('https://'.$company->alias.Yii::$app->request->url);
                    }
                }
            }
        ],
        'user-management' => [
            'class' => 'webvimark\modules\UserManagement\UserManagementModule',
            'controllerNamespace' => 'app\controllers\user',
            'layout' => '@app/views/layouts/report',

            // 'enableRegistration' => true,

            // Add regexp validation to passwords. Default pattern does not restrict user and can enter any set of characters.
            // The example below allows user to enter :
            // any set of characters
            // (?=\S{8,}): of at least length 8
            // (?=\S*[a-z]): containing at least one lowercase letter
            // (?=\S*[A-Z]): and at least one uppercase letter
            // (?=\S*[\d]): and at least one number
            // $: anchored to the end of the string

            //'passwordRegexp' => '^\S*(?=\S{8,})(?=\S*[a-z])(?=\S*[A-Z])(?=\S*[\d])\S*$',


            // Here you can set your handler to change layout for any controller or action
            // Tip: you can use this event in any module
            'on beforeAction' => function (yii\base\ActionEvent $event) {
                if ($event->action->uniqueId == 'user-management/auth/login') {
                    $event->action->controller->layout = 'loginLayout.php';
                };
            },
        ],

        'oauth2' => [
            'class' => 'filsh\yii2\oauth2server\Module',
            'tokenParamName' => 'accessToken',
            'tokenAccessLifetime' => 3600 * 24,
            'storageMap' => [
                'user_credentials' => 'app\models\User',
            ],
            'grantTypes' => [
                'user_credentials' => [
                    'class' => 'OAuth2\GrantType\UserCredentials',
                ],
                'refresh_token' => [
                    'class' => 'OAuth2\GrantType\RefreshToken',
                    'always_issue_new_refresh_token' => true
                ]
            ]
        ]
    ],
    'params' => $params,
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
//    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];
}

return $config;
