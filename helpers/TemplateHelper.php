<?php

namespace app\helpers;

class TemplateHelper {

    public static function hexToRgb($color)
    {
        list($r, $g, $b) = sscanf($color, "#%02x%02x%02x");
        return [
            $r,
            $g,
            $b
        ];
    }

    public static function getGradientPoint($start, $finish, $count, $point)
    {
        $count = $count - 1;

        $x = $start[0];
        $y = $start[1];
        $z = $start[2];

        if ($point === $count) {
            $x = $finish[0];
            $y = $finish[1];
            $z = $finish[2];
        } else if ($point > 0) {
            $k = $point / $count / (($count - $point) / $point);
            $x = ($start[0] + $k * $finish[0]) / (1 + $k);
            $y = ($start[1] + $k * $finish[1]) / (1 + $k);
            $z = ($start[2] + $k * $finish[2]) / (1 + $k);
        }

        return "rgb(" . (int)$x . "," . (int)$y . "," . (int)$z . ")";
    }

    public static function gradient($startColor, $endColor, $count)
    {
        $start = self::hexToRgb($startColor);
        $finish = self::hexToRgb($endColor);

        $gradient = [];

        for ($i = 0; $i < $count; $i++) {
            $gradient[] = self::getGradientPoint($start, $finish, $count, $i);
        }

        return $gradient;
    }

    public static function npsGradient($startColor, $endColor)
    {
        $neutralColor = '#ACACB6';

        $left = self::gradient($startColor, $neutralColor, 6);
        $right = self::gradient($neutralColor, $endColor, 6);


        return array_merge($left, array_slice($right, 1));
    }
}