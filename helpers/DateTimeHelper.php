<?php

namespace app\helpers;
use DateTime;

class DateTimeHelper
{
    public static $month = [
        "января",
        "февраля",
        "марта",
        "апреля",
        "мая",
        "июня",
        "июля",
        "августа",
        "сентября",
        "октября",
        "ноября",
        "декабря"
    ];

    public static function dateString2dateWithMonthAsWord($date)
    {
        $parsed = date_parse($date);
        return $parsed['day'].' '.self::$month[$parsed['month']-1].' '.$parsed['year'];
    }

    /**
     * @param string $date дата в формате dd.mm
     * @return string
     */
    public static function shortDateString2dateWithMonthAsWord(string $date)
    {
        $ret = '';
        $dm = explode('.', $date);
        if (!empty($dm[1]) && is_numeric($dm[1])) {
            $months = DateTimeHelper::$month;
            if (isset($months[(int)$dm[1]-1])) {
                $ret = $dm[0] . ' ' . $months[(int)$dm[1]-1];
            }
        }
        return $ret;
    }

    public static function fromStringDateTime($strDateTime)
    {
        return DateTime::createFromFormat("Y-m-d H:i:s", $strDateTime);
    }

    public static function timeStringToSeconds($timeString)
    {
        if(!$timeString)
            return null;

        $timeParsed = explode(":", $timeString);
        return $timeParsed[0] * 3600 + $timeParsed[1] * 60 + $timeParsed[2];
    }

    public static function addGMT3($datetime)
    {
        return $datetime ? (new \DateTime($datetime))->format("D M d Y H:i:s \G\M\T+0300") : null;
    }

    public static function timeZoneDateToGMT3($timeZoneDateTime)
    {
        return $timeZoneDateTime ? (new \DateTime($timeZoneDateTime))->setTimezone(new \DateTimeZone("GMT+3"))->format("Y-m-d H:i:s") : null;
    }

    public static function secsToMinSecs($seconds)
    {
        $mins = intval($seconds / 60);
        $secs = $seconds % 60;
        if($secs < 10) $sec = '0'.$secs;
        return $mins .':' .$secs;

    }

    public static function secsToHoursMins($seconds)
    {
        return sprintf("%02d:%02d", floor($seconds/3600),  ($seconds/60)%60);
    }
}