<?php
namespace app\helpers;

class ReportHelper
{
	public static function persent($part, $all)
	{
		if ($all==0) return 0;
		return round($part/$all*100, 1);
	}	

	public static function titleOrder($model, $name, $url, $nameFilter, $label, $noCaret=false)
	{
		if ($model->o!=$name) {
			return "<a href=\"{$url}&{$nameFilter}%5Bo%5D={$name}&{$nameFilter}%5Bd%5D=ASC\">" . $label . "</a>";
		} else if ($model->d=="DESC") {
			return "<a href=\"{$url}&{$nameFilter}%5Bo%5D={$name}&{$nameFilter}%5Bd%5D=ASC\">" . $label . "</a>"
				.($noCaret ? "" : "  <span class=\"caret up\"></span>");
		} else { 
			return "<a href=\"{$url}&{$nameFilter}%5Bo%5D={$name}&{$nameFilter}%5Bd%5D=DESC\">" . $label . "</a>"
				.($noCaret ? "" : "  <span class=\"caret\"></span>");
		}
	}

	public static function titleOrderCaret($model, $name)
	{
		if ($model->o!=$name) {
			return "";
		} else if ($model->d=="DESC") {
			return "  <span class=\"caret up\"></span>";
		} else { 
			return "  <span class=\"caret\"></span>";
		}
	}

	public static function phone($phone) 
	{
		if (preg_match_all("@^(\d{1})(\d{3})(\d{3})(\d{4})$@", $phone, $arr)){
			$phone = $arr[1][0] == "7" || $arr[1][0] == "8" ? "+7 " : $arr[1][0]. " ";
			$phone .= $arr[2][0] . " " .$arr[3][0] . "-" . $arr[4][0];
		}
		return $phone;
	}

	public static function getScores($scores)
	{
		$result['delivery'] = '-';
		$result['operator'] = '-';
		foreach ($scores as $score) {
			if ($score['type_code'] == 'delivery') $result['delivery'] = $score['score'];
			elseif($score['type_code'] == 'operator') $result['operator'] = $score['score'];
		}
		return $result;
	}

	public static function getScoreDish($scores)
	{
		$sum = 0;
		foreach ($scores as $score) {
			$sum += $score['score'];
		}
		$result = round($sum/count($scores),1);
		return $result;
	}

	public static function randomGuid()
    {
        if (function_exists('com_create_guid') === true) {
            return trim(com_create_guid(), '{}');
        }

        return strtolower(sprintf('%04X%04X-%04X-%04X-%04X-%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(16384, 20479), mt_rand(32768, 49151), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535)));
    }

    public static function getLinkFromPollResponse($response)
    {
        return substr($response,strpos($response,(\Yii::$app->getRequest()->isSecureConnection ? 'https' : 'http').'://'));
    }
}
