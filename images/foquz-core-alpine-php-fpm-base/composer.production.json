{"name": "yiisoft/yii2-app-basic", "description": "Yii 2 Basic Project Template", "keywords": ["yii2", "framework", "basic", "project template"], "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": "^8.2", "ext-exif": "*", "ext-gd": "*", "ext-intl": "*", "ext-json": "*", "ext-redis": "*", "ext-zip": "*", "2amigos/qrcode-library": "~1.1", "alexandernst/yii2-device-detect": "0.0.12", "amocrm/oauth2-amocrm": "dev-master", "asasmoyo/yii2-saml": "*", "avadim/fast-excel-writer": "^5.8", "doctrine/annotations": "2.0.x-dev", "dompdf/dompdf": "dev-master", "donatj/phpuseragentparser": "dev-master", "enqueue/amqp-lib": "^0.10.19", "filsh/yii2-oauth2-server": "^2.1@dev", "himiklab/yii2-recaptcha-widget": "*", "intervention/image": "2.3.8", "jdorn/sql-formatter": "^1.3@dev", "kartik-v/yii2-markdown": "dev-master", "kartik-v/yii2-widget-select2": "^2.2@dev", "kub-at/php-simple-html-dom-parser": "dev-master", "mohorev/yii2-upload-behavior": "~0.2", "olegsoft/first-or-create": "dev-master", "php-amqplib/php-amqplib": "^3.7", "php-ffmpeg/php-ffmpeg": "^1.2.0@dev", "phpmailer/phpmailer": "dev-master", "phpoffice/phpspreadsheet": "^1.19", "picqer/php-barcode-generator": "dev-main", "rmrevin/yii2-fontawesome": "~2.17", "vova07/yii2-console-runner-extension": "*", "wbraganca/yii2-dynamicform": "dev-master", "webvimark/module-user-management": "dev-master", "yii2tech/html2pdf": "^1.0@dev", "yiisoft/yii2": "~2.0", "yiisoft/yii2-httpclient": "^2.0@dev", "yiisoft/yii2-imagine": "^2.0@dev", "yiisoft/yii2-queue": "2.x-dev", "yiisoft/yii2-redis": "~2.0.0", "yiisoft/yii2-swiftmailer": "~2.1.0", "yiisoft/yii2-twig": "^2.4", "codemix/yii2-streamlog": "^1.3", "yiisoft/yii2-bootstrap": "~2.0.0", "yiisoft/yii2-bootstrap4": "^1.0@dev"}, "require-dev": {}, "config": {"process-timeout": 1800, "sort-packages": true, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true, "codeception/c3": true, "php-http/discovery": true}}, "scripts": {"post-install-cmd": ["yii\\composer\\Installer::postInstall"], "post-create-project-cmd": ["yii\\composer\\Installer::postCreateProject", "yii\\composer\\Installer::postInstall"]}, "extra": {"yii\\composer\\Installer::postCreateProject": {"setPermission": [{"runtime": "0777", "web/assets": "0777", "yii": "0755"}]}, "yii\\composer\\Installer::postInstall": {"generateCookieValidationKey": ["config/web.php"]}}, "repositories": {"asset-packagist": {"type": "composer", "url": "https://asset-packagist.org"}}}