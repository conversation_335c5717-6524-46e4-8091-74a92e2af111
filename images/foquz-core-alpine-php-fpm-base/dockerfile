# Образ php + fpm + alpine из внешнего репозитория
FROM  cr.yandex/crpsvv5iscafkhlubkmt/php:8.2-fpm-alpine3.21

COPY images/foquz-core-alpine-php-fpm-base/composer.json /var/www/foquz/composer.json
COPY images/foquz-core-alpine-php-fpm-base/php.ini /usr/local/etc/php/conf.d/50-php.ini
COPY images/foquz-core-alpine-php-fpm-base/zzz-foquz.ini /usr/local/etc/php-fpm.d/zzz-foquz.ini

ARG PHPEXT_REDIS_VERSION=6.1.0
ARG PHPEXT_ZIP_VERSION=1.22.4
ARG MODE= production

RUN set -eux; \
  persistentDeps=" \
    libzip \
    icu \
    freetype \
    libjpeg-turbo \
    libpng \
    libwebp \
    libxpm \
  "; \
  \
  buildDeps=" \
    libzip-dev \
    icu-dev \
    linux-headers \
    freetype-dev \
    libjpeg-turbo-dev \
    libpng-dev \
    libwebp-dev \
    libxpm-dev \
  "; \
  \
  apk add --no-cache --virtual .persistent-deps ${persistentDeps}; \
  apk add --no-cache --virtual .build-deps ${buildDeps}; \
  \
  pecl bundle -d /usr/src/php/ext zip-${PHPEXT_ZIP_VERSION}; \
  pecl bundle -d /usr/src/php/ext redis-${PHPEXT_REDIS_VERSION}; \
  \
  docker-php-ext-configure gd \
    --with-freetype \
    --with-jpeg \
    --with-webp \
    --with-xpm \
  ; \
  docker-php-ext-install -j$(nproc) \
   exif \
   gd \
   intl \
   pdo_mysql \
   sockets \
   redis \
   zip; \
  \
  apk add --no-cache  \
    ffmpeg \
    fcgi \
		busybox-extras; \
  apk del --no-cache --no-network .build-deps; \
  docker-php-source delete; \
	  mv "$PHP_INI_DIR/php.ini-$MODE" "$PHP_INI_DIR/php.ini"; \
	php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"; \
	php -r "if (hash_file('sha384', 'composer-setup.php') === 'dac665fdc30fdd8ec78b38b9800061b4150413ff2e3b6f88543c636f7cd84f6db9189d43a81e5503cda447da73c7e5b6') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"; \
  php composer-setup.php; \
	mv composer.phar /usr/local/bin/composer; \
  rm composer-setup.php; \
  cd /var/www/foquz; \
  composer install --no-cache --no-dev --optimize-autoloader;  \
	composer clearcache;  \
  composer audit --abandoned=ignore;  \
	rm /usr/local/bin/composer; \
  rm -rf /tmp/* /var/tmp/*;
