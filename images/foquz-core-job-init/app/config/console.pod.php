<?php

use yii\queue\amqp_interop\Queue;
use yii\queue\LogBehavior;
use yii\queue\serializers\JsonSerializer;


$config = [
    'components' => [
        'redis'        => [
            'class'    => yii\redis\Connection::class,
            'hostname' => getenv('REDIS_HOSTNAME'),
            'password' => getenv('REDIS_PASSWORD'),
            'database' => getenv('REDIS_DATABASE'),
            'port'     => getenv('REDIS_PORT'),
        ],
        'rabbit_queue' => [
            'host'         => getenv('RABBITMQ_HOSTNAME'),
            'port'         => getenv('RABBITMQ_PORT'),
            'user'         => getenv('RABBITMQ_USER'),
            'password'     => getenv('RABBITMQ_PASSWORD'),
            'vhost'        => getenv('RABBITMQ_VHOST'),
            'class'        => Queue::class,
            'queueName'    => 'foquz',
            'driver'       => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'serializer'   => JsonSerializer::class,
            'exchangeName' => 'foquz',
            'as log'       => LogBehavior::class,
        ],
    ],
    'params'     => [],
];

return $config;