ARG TAG_BASE
FROM cr.yandex/crpsvv5iscafkhlubkmt/foquz-core-cli-php-base:${TAG_BASE}

COPY . /app

RUN  mkdir /app/runtime && \
		chmod -R 777 /app/runtime && \
		addgroup -S -g 1099 foquz  && \
    adduser  -S -u 1099 -G foquz foquz  && \
		chown foquz:foquz -R /app/

ENV CLUSTER  1
USER foquz
WORKDIR /app


CMD php /app/yii site-notifications/cache-notifications && php /app/yii site-notifications/restart-all-daily && php /app/yii fix/02042024-add-stored-procedures-and-functions && php /app/yii foquz-sender/recreate-cache-mailing-after-poll && php /app/yii cache/flush-all
