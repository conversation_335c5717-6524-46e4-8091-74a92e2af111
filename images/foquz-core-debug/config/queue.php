<?php

use app\components\RabbitMQComponent;
use yii\queue\amqp_interop\Queue;
use yii\queue\LogBehavior;

//use app\modules\foquz\behaviors\QueueWorkerBehavior;

return [
    'components' => [

        'redis' => [
            'class'    => yii\redis\Connection::class,
            'hostname' => getenv('REDIS_HOSTNAME'),
            'password' => getenv('REDIS_PASSWORD'),
            'database' => getenv('REDIS_DATABASE'),
            'port'     => getenv('REDIS_PORT'),
        ],


        'rabbit_queue' => [
            'host'         => getenv('RABBITMQ_HOSTNAME'),
            'port'         => getenv('RABBITMQ_PORT'),
            'user'         => getenv('RABBITMQ_USER'),
            'password'     => getenv('RABBITMQ_PASSWORD'),
            'vhost'        => getenv('RABBITMQ_VHOST'),
            'class'        => Queue::class,
            'queueName'    => 'foquz',
            'driver'       => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'serializer'   => \yii\queue\serializers\JsonSerializer::class,
            'exchangeName' => 'foquz',
            'as log'       => LogBehavior::class,
        ],

        'queue_mailings_out' => [
            'host'                   => getenv('RABBITMQ_HOSTNAME'),
            'port'                   => getenv('RABBITMQ_PORT'),
            'user'                   => getenv('RABBITMQ_USER'),
            'password'               => getenv('RABBITMQ_PASSWORD'),
            'vhost'                  => getenv('RABBITMQ_VHOST'),
            'class'                  => yii\queue\amqp_interop\Queue::class,
            'strictJobType'          => false,
            'serializer'             => \yii\queue\serializers\JsonSerializer::class,
            'queueName'              => 'mailings_external',
            'exchangeName'           => 'mailings_external',
            'driver'                 => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'as log'                 => \yii\queue\LogBehavior::class,
            'queueOptionalArguments' => ['x-max-priority' => 10],
        ],

        'rabbit' => [
            'class'    => RabbitMQComponent::class,
            'hostname' => getenv('RABBITMQ_HOSTNAME'),
            'port'     => getenv('RABBITMQ_PORT'),
            'user'     => getenv('RABBITMQ_USER'),
            'password' => getenv('RABBITMQ_PASSWORD'),
            'vhost'    => getenv('RABBITMQ_VHOST'),
        ],
   ],

    'bootstrap'  => [
        'rabbit_queue',
        'rabbit',
    ],
];
