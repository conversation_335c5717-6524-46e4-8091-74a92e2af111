<?php

use app\components\JsonStdoutLogTarget;
use yii\log\FileTarget;

return [
    'components' => [
        'log' => [
            'flushInterval' => 1,
            'targets'       => [
                'errors2'             => [
                    'class'  => FileTarget::class,
                    'levels' => ['error', 'warning'],
                ],
                'errors'             => [
                    'class'  => JsonStdoutLogTarget::class,
                    'url'     => 'php://stderr',
                    'levels' => ['error', 'warning'],
                ],
                'notifications'      => [
                    'class'  => JsonStdoutLogTarget::class,
                    'url'     => 'php://stdout',
                    'categories'     => ['notifications'],
                    'logVars'        => [],
                    'exportInterval' => 1
                ],
                'queue'              => [
                    'class'  => JsonStdoutLogTarget::class,
                    'url'     => 'php://stdout',
                    'levels'         => ['info'],
                    'categories'     => [
                        'yii\queue\*',
                        'app\modules\foquz\queue\*'
                    ],
                    'logVars'        => [],
                    'exportInterval' => 1
                ],
                'queue-mailing'      => [
                    'class'  => JsonStdoutLogTarget::class,
                    'url'     => 'php://stdout',
                    'levels'         => ['info', 'trace', 'profile'],
                    'categories'     => [
                        'yii\queue\mailing\*',
                    ],
                    'logVars'        => [],
                    'exportInterval' => 1
                ],
                'queue-mailing-list' => [
                    'class'  => JsonStdoutLogTarget::class,
                    'url'     => 'php://stdout',
                    'levels'         => ['info', 'trace', 'profile'],
                    'categories'     => [
                        'yii\queue\mailing_list\*',
                    ],
                    'logVars'        => [],
                    'exportInterval' => 1
                ],
                'object_changes'     => [
                    'class'  => JsonStdoutLogTarget::class,
                    'url'     => 'php://stdout',
                    'levels'         => ['info'],
                    'logVars'        => [],
                    'exportInterval' => 1,
                    'categories'     => ['object_changes'],
                ],
            ],
        ]
    ]
];
