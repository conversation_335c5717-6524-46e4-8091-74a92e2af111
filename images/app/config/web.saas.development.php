<?php
$cookieDomain = "." . getenv('HOST');

$config = [
    'as hostControl' => [
        'class'        => 'yii\filters\HostControl',
        'allowedHosts' => [
            'doxswf.ru',
            '*.doxswf.ru',
            'devfoquz.ru',
            '*.devfoquz.ru',
            'foquzdev.ru',
            '*.foquzdev.ru',
            'localhost',
            '*.localhost',
            'foquz-nginx',
            getenv("INTERNAL_HOST"),
        ]
    ],
    "components"     => [
        'session'       => [
            'cookieParams' => [
                'domain'   => $cookieDomain,
            ],
        ],
        'request'       => [
            'csrfCookie'          => [
                'domain'   =>  $cookieDomain,
            ],
        ],
        'user'          => [
            'identityCookie'  => [
                'domain' => $cookieDomain,
            ],
        ],
    ],
    "params"         => [

    ]
];

return $config;




