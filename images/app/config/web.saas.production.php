<?php
$cookieDomain = "." . getenv('HOST');
if (!empty($_SERVER["SERVER_NAME"]) && in_array($_SERVER["SERVER_NAME"], ['aa.4qz.ru', 'opros.eae-consult.ru', 'opros.mai3.ru', 'poll.rskh.ru'])) {
    $cookieDomain = $_SERVER["SERVER_NAME"];
}

$config = [
    'as hostControl' => [
        'class'        => 'yii\filters\HostControl',
        'allowedHosts' => [
            'foquz.ru',
            '*.foquz.ru',
            '*.r-ulybka.ru',
            'opros.eae-consult.ru',
            '*.4qz.ru',
            '4qz.ru',
            '3qz.ru',
            '*.mai3.ru',
            '*.eae-consult.ru',
            'poll.rskh.ru',
            getenv("INTERNAL_HOST"),
        ]
    ],
    "components"     => [

    ],
    "params"         => [
        'instance'                     => false,
        'lead_to_emails'               => empty(getenv('LEAD_TO_EMAILS')) ? [] : explode(",", getenv('LEAD_TO_EMAILS')),
        'lead_sales_email'             => empty(getenv('LEAD_SALES_EMAIL')) ? '' : explode(",", getenv('LEAD_SALES_EMAIL')),
        'sales_email'                  => empty(getenv('SALES_EMAIL')) ? '' : explode(",", getenv('SALES_EMAIL')),
        'wiki_index'                   => 'wiki_prod',
        'new_companies_sender_token'   => empty(getenv('NEW_COMPANIES_SENDER_TOKEN')) ? "" : getenv('NEW_COMPANIES_SENDER_TOKEN'),
        'new_companies_sender_poll_id' => empty(getenv('NEW_COMPANIES_SENDER_POLL_ID')) ? "" : getenv('NEW_COMPANIES_SENDER_POLL_ID'),
        'new_companies_sender_url'     => empty(getenv('NEW_COMPANIES_SENDER_URL')) ? "" : getenv('NEW_COMPANIES_SENDER_URL'),
        'after_registration_poll_id'   => empty(getenv('AFTER_REGISTRATION_POLL_ID')) ? "" : getenv('AFTER_REGISTRATION_POLL_ID'),
        'amo_client_id'                => empty(getenv('AMO_CLIENT_ID')) ? "" : getenv('AMO_CLIENT_ID'),
        'amo_client_secret'            => empty(getenv('AMO_CLIENT_SECRET')) ? "" : getenv('AMO_CLIENT_SECRET'),
        'amo_domain'                   => empty(getenv('AMO_CLIENT_SECRET')) ? "" : getenv('AMO_CLIENT_SECRET'),
        'sms_login'                    => empty(getenv('SMS_LOGIN')) ? "" : getenv('SMS_LOGIN'),
        'sms_password'                 => empty(getenv('SMS_PASSWORD')) ? "" : getenv('SMS_PASSWORD'),
        'sentry_cli_dsn'               => 'https://<EMAIL>/13',
        'smsc'                         => [
            'login'    => empty(getenv('SMSC_LOGIN')) ? "" : getenv('SMSC_LOGIN'),
            'password' => empty(getenv('SMSC_PASSWORD')) ? "" : getenv('SMSC_PASSWORD')
        ],
        'telegram'                     => [
            'bot_token' => empty(getenv('TELEGRAM_TOKEN')) ? "" : getenv('TELEGRAM_TOKEN')
        ],
        'template_company_id'          => empty(getenv('TEMPLATE_COMPANY_ID')) ? 0 : getenv('TEMPLATE_COMPANY_ID'),
        'template_company_folder_id'   => empty(getenv('TEMPLATE_FOLDER_ID')) ? 0 : getenv('TEMPLATE_FOLDER_ID'),
        'smart_captcha_client_key'     => empty(getenv('SMART_CAPTCHA_CLIENT_KEY')) ? "" : getenv('SMART_CAPTCHA_CLIENT_KEY'),
        'smart_captcha_server_key'     => empty(getenv('SMART_CAPTCHA_SERVER_KEY')) ? "" : getenv('SMART_CAPTCHA_SERVER_KEY'),
        'company_domen'                => $_SERVER['HTTP_HOST'] ?? getenv('HOST'),
    ]
];

return $config;




