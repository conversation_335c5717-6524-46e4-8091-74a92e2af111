<?php

use app\components\RabbitMQComponent;

return [
    'components' => [
        'rabbit' => [
            'class'                 => RabbitMQComponent::class,
            "hostname"              => empty(getenv("RABBITMQ_WIDGET_HOSTNAME")) ? getenv("RABBITMQ_HOSTNAME") : getenv("RABBITMQ_WIDGET_HOSTNAME"),
            "port"                  => empty(getenv("RABBITMQ_WIDGET_PORT")) ? getenv("RABBITMQ_PORT") : getenv("RABBITMQ_WIDGET_PORT"),
            "user"                  => empty(getenv("RABBITMQ_WIDGET_USER")) ? getenv("RABBITMQ_USER") : getenv("RABBITMQ_WIDGET_USER"),
            "password"              => empty(getenv("RABBITMQ_WIDGET_PASSWORD")) ? getenv("RABBITMQ_PASSWORD") : getenv("RABBITMQ_WIDGET_PASSWORD"),
            "vhost"                 => empty(getenv("RABBITMQ_WIDGET_VHOST")) ? getenv("RABBITMQ_VHOST") : getenv("RABBITMQ_WIDGET_VHOST"),
            'connectionTimeout'     => 10,
            'readWriteTimeout'      => 30,
            'heartbeat'             => 3,
            'keepalive'             => true,

        ],
    ],

    'params' =>  [
        'widgetsUrl'          => empty(getenv('WIDGETS_URL')) ? "" : getenv('WIDGETS_URL'),
        'widgetsAuthKey'      => empty(getenv('WIDGETS_AUTH_KEY')) ? "" : getenv('WIDGETS_AUTH_KEY'),
        'widgetCodeSalt'      => empty(getenv('WIDGETS_CODE_SALT')) ? "" : getenv('WIDGETS_CODE_SALT'),
        'widgetAnswersExpire' => empty(getenv('WIDGETS_ANSWERS_EXPIRE')) ? 24 : getenv('WIDGETS_ANSWERS_EXPIRE'),
    ]
];
