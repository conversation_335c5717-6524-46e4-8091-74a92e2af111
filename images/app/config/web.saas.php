<?php

$config = [
    "components"     => [
        'urlManager' => [
            'rules' => [
                'GET /google-sheets/create/<table:\w+>/<user_id:\w+>' => 'google-sheets/create',
                'GET /p/<id:\w+>' => 'foquz/default/anonymous',
                'GET /pkn/<id:\w+>' => 'foquz/default/anonymous',
                'GET /p/<tablet:t>/<id:\w+>' => 'foquz/default/anonymous',
                'GET /r/<r:\w+>' => 'site/index',
                'GET /cases/<id:\w+>' => 'site/case',
                'GET /site/cases/<id:[\w\-]+>' => 'site/case',
                'GET /foquz/user-wiki/<name:[\w|-]+>' => 'foquz/user-wiki/index',
                'GET /activate/<token:\w+>' => 'user-management/auth/activate-email',
                'GET /obrazcy-anket' => 'site/solutions',
                'GET /obrazcy-anket/<code>' => 'site/solutions',
                'GET /author/<name:[\-\w]+>' => 'site/author',
                '/<name:(^(?!foquz\/|foquz$|iiko\/|iiko$|zapier\/|zapier$|adfs\/|adfs$|api20\/|api20$|api\/|api$|base\/|base$|companyaccess\/|companyaccess$|company\/|company$|googleoauth\/|googleoauth$|googlesheets\/|googlesheets$|interview\/|interview$|oauth\/|oauth$|polls\/|polls$|report\/|report$|site\/|site$|telegram\/|telegram$|auth\/|auth$|authitemgroup\/|authitemgroup$|permission\/|permission$|role\/|role$|user\/|user$|userpermission\/|userpermission$|uservisitlog\/|uservisitlog$)([\w-]+)$)+>' => 'site/article',
            ],
        ],
        'markdown' => [
            'class' => 'kartik\markdown\Module'
        ]
    ],
    "params"         => [
        'instance'                     => false,
        'lead_to_emails'               => empty(getenv('LEAD_TO_EMAILS')) ? [] : explode(",", getenv('LEAD_TO_EMAILS')),
        'lead_sales_email'             => empty(getenv('LEAD_SALES_EMAIL')) ? '' : explode(",", getenv('LEAD_SALES_EMAIL')),
        'sales_email'                  => empty(getenv('SALES_EMAIL')) ? '' : explode(",", getenv('SALES_EMAIL')),
        'wiki_index'                   => 'wiki_prod',
        'new_companies_sender_token'   => empty(getenv('NEW_COMPANIES_SENDER_TOKEN')) ? "" : getenv('NEW_COMPANIES_SENDER_TOKEN'),
        'new_companies_sender_poll_id' => empty(getenv('NEW_COMPANIES_SENDER_POLL_ID')) ? "" : getenv('NEW_COMPANIES_SENDER_POLL_ID'),
        'new_companies_sender_url'     => empty(getenv('NEW_COMPANIES_SENDER_URL')) ? "" : getenv('NEW_COMPANIES_SENDER_URL'),
        'after_registration_poll_id'   => empty(getenv('AFTER_REGISTRATION_POLL_ID')) ? "" : getenv('AFTER_REGISTRATION_POLL_ID'),
        'amo_client_id'                => empty(getenv('AMO_CLIENT_ID')) ? "" : getenv('AMO_CLIENT_ID'),
        'amo_client_secret'            => empty(getenv('AMO_CLIENT_SECRET')) ? "" : getenv('AMO_CLIENT_SECRET'),
        'amo_domain'                   => empty(getenv('AMO_CLIENT_SECRET')) ? "" : getenv('AMO_CLIENT_SECRET'),
        'sms_login'                    => empty(getenv('SMS_LOGIN')) ? "" : getenv('SMS_LOGIN'),
        'sms_password'                 => empty(getenv('SMS_PASSWORD')) ? "" : getenv('SMS_PASSWORD'),
        'sentry_cli_dsn'               => 'https://<EMAIL>/13',
        'smsc'                         => [
            'login'    => empty(getenv('SMSC_LOGIN')) ? "" : getenv('SMSC_LOGIN'),
            'password' => empty(getenv('SMSC_PASSWORD')) ? "" : getenv('SMSC_PASSWORD')
        ],
        'telegram'                     => [
            'bot_token' => empty(getenv('TELEGRAM_TOKEN')) ? "" : getenv('TELEGRAM_TOKEN')
        ],
        'template_company_id'          => empty(getenv('TEMPLATE_COMPANY_ID')) ? 0 : getenv('TEMPLATE_COMPANY_ID'),
        'template_company_folder_id'   => empty(getenv('TEMPLATE_FOLDER_ID')) ? 0 : getenv('TEMPLATE_FOLDER_ID'),
        'smart_captcha_client_key'     => empty(getenv('SMART_CAPTCHA_CLIENT_KEY')) ? "" : getenv('SMART_CAPTCHA_CLIENT_KEY'),
        'smart_captcha_server_key'     => empty(getenv('SMART_CAPTCHA_SERVER_KEY')) ? "" : getenv('SMART_CAPTCHA_SERVER_KEY'),
        'company_domen'                => $_SERVER['HTTP_HOST'] ?? getenv('HOST'),
    ]
];

return $config;




