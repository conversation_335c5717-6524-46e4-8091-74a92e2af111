<?php

$config = [
    "components" => [
        "queue_quiz" => [
            "class"    => \app\components\RabbitMQComponent::class,
            "hostname"              => empty(getenv("RABBITMQ_QUIZ_HOSTNAME")) ? getenv("RABBITMQ_HOSTNAME") : getenv("RABBITMQ_QUIZ_HOSTNAME"),
            "port"                  => empty(getenv("RABBITMQ_QUIZ_PORT")) ? getenv("RABBITMQ_PORT") : getenv("RABBITMQ_QUIZ_PORT"),
            "user"                  => empty(getenv("RABBITMQ_QUIZ_USER")) ? getenv("RABBITMQ_USER") : getenv("RABBITMQ_QUIZ_USER"),
            "password"              => empty(getenv("RABBITMQ_QUIZ_PASSWORD")) ? getenv("RABBITMQ_PASSWORD") : getenv("RABBITMQ_QUIZ_PASSWORD"),
            "vhost"                 => empty(getenv("RABBITMQ_QUIZ_VHOST")) ? getenv("RABBITMQ_VHOST") : getenv("RABBITMQ_QUIZ_VHOST"),
            'connectionTimeout'     => 10,
            'readWriteTimeout'      => 30,
            'heartbeat'             => 3,
            'keepalive'             => true,
        ]
    ],
    "params"     => [
        "externalQuiz" => true,
        "quizAuthKey"  => getenv("QUIZ_AUTHKEY"),
    ]
];

return $config;
