<?php

use OneLogin\Saml2\Utils;

$spBaseUrl = "https://" . getenv("HOST");
Utils::setProxyVars(true);
$config = [
    'components' => [
        'urlManager'    => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
            'rules'           => [
                'GET /user-management/auth/logout'                  => '/sso/saml/logout',
            ],
        ],
        'saml' => [
            'class'  => 'asasmoyo\yii2saml\Saml',
            'config' => [
                'sp'  => [
                    'entityId'                 => $spBaseUrl . '/sso/saml/metadata',
                    'assertionConsumerService' => [
                        'url' => $spBaseUrl . '/sso/saml/acs',
                    ],
                    'singleLogoutService'      => [
                        'url' => $spBaseUrl . '/sso/saml/sls',
                    ],
                    'NameIDFormat'             => getenv("SAML_NAME_ID_FORMAT"),
                ],
                'idp' => [
                    'entityId'            => getenv("SAML_ENTITY_ID"),
                    'singleSignOnService' => [
                        'url' => getenv("SAML_SINGLE_SIGN_ON_SERVICE"),
                    ],
                    'singleLogoutService' => [
                        'url' => getenv("SAML_SINGLE_LOGOUT_SERVICE"),
                    ],
                    'x509cert'            => getenv("SAML_SINGLE_X50_CERT"),
                ],
            ],
        ]
    ],

    'params' =>  [
        'saml' => [
            'on'             => true,
            'session_length' => empty(getenv("SAML_SESSION_LENGTH")) ? 60  : getenv("SAML_SESSION_LENGTH"),
            'company_id'     => empty(getenv("SAML_COMPANY_ID")) ? 773  : getenv("SAML_COMPANY_ID"),
        ],
    ]

];

return $config;
