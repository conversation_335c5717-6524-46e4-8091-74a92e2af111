<?php

use app\components\JsonStdoutLogTarget;
use notamedia\sentry\SentryTarget;

$params = [
    'host'           => getenv('HOST'),
    'sentry_cli_dsn' => getenv('SENTRY_CLI'),
];

$db = [
    'class'    => 'yii\db\Connection',
    'dsn'      => 'mysql:host=' . getenv('DB_HOST') . ';dbname=' . getenv('DB_DBNAME'),
    'username' => getenv('DB_USER'),
    'password' => getenv('DB_PASSWORD'),
    'charset'  => 'utf8mb3',
];

$config = [
    'modules'             => [
    ],
    'id'                  => 'basic-console',
    'basePath'            => dirname(__DIR__),
    'bootstrap'           => ['log'],
    'controllerNamespace' => 'app\commands',
    'timeZone'            => 'Europe/Moscow',
    'aliases'             => [
        '@bower'   => '@vendor/bower-asset',
        '@npm'     => '@vendor/npm-asset',
        '@web'     => '@app/web',
        '@uploads' => '@app/web/uploads',
    ],
    'components'          => [
        'log'         => [
            'flushInterval' => 1,
            'targets'       => [
                'errors' => [
                    'class'   => JsonStdoutLogTarget::class,
                    'url'     => 'php://stderr',
                    'levels'  => ['error', 'warning'],
                    'except'  => [
                        'yii\web\HttpException:404',
                    ],
                    'logVars' => ['_FILES', '_COOKIE', '_SESSION'],
                ],
                'info'   => [
                    'class'          => JsonStdoutLogTarget::class,
                    'url'            => 'php://stdout',
                    'logVars'        => [],
                    'exportInterval' => 1
                ],
            ],
        ],
        'db'  => $db,

    ],
    'params'              => $params,
];

if (!empty($params['sentry_cli_dsn'])) {
    $config['components']['log']['targets'][] = [
        'class'   => SentryTarget::class,
        'dsn'     => $params['sentry_cli_dsn'],
        'levels'  => ['error', 'warning'],
        'context' => true,
    ];
}

return $config;
