<?php

use app\components\RabbitMQComponent;

//use app\modules\foquz\behaviors\QueueWorkerBehavior;

return [
    'components' => [
        'rabbit'     => [
            'class'             => RabbitMQComponent::class,
            'hostname'          => getenv('RABBITMQ_HOSTNAME'),
            'port'              => getenv('RABBITMQ_PORT'),
            'user'              => getenv('RABBITMQ_USER'),
            'password'          => getenv('RABBITMQ_PASSWORD'),
            'vhost'             => getenv('RABBITMQ_VHOST'),
            'connectionTimeout' => 10,
            'readWriteTimeout'  => 30,
            'heartbeat'         => 3,
            'keepalive'         => true,
        ],
        'queue_quiz' => [
            'class'             => RabbitMQComponent::class,
            'hostname'          => getenv('RABBITMQ_HOSTNAME'),
            'port'              => getenv('RABBITMQ_PORT'),
            'user'              => getenv('RABBITMQ_USER'),
            'password'          => getenv('RABBITMQ_PASSWORD'),
            'vhost'             => getenv('RABBITMQ_VHOST'),
            'connectionTimeout' => 10,
            'readWriteTimeout'  => 30,
            'heartbeat'         => 3,
            'keepalive'         => true,
        ],
    ],
    'bootstrap'  => [
        'rabbit',
    ],
    "params"     => [
        'quizAuthKey' => getenv('QUIZ_AUTHKEY'),
    ],
];

