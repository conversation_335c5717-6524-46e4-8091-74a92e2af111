<?php

use app\components\RabbitMQComponent;
use app\modules\foquz\behaviors\queue\DiagnosticBehavior;
use yii\queue\amqp_interop\Queue;
use yii\queue\LogBehavior;
use yii\queue\serializers\JsonSerializer;
use yii\redis\Cache;


$config = [
    'components' => [
        'cache'              => [
            'class' => Cache::class,
            'redis' => [
                'hostname' => getenv('REDIS_HOSTNAME'),
                'password' => getenv('REDIS_PASSWORD'),
                'database' => getenv('REDIS_CACHE_DATABASE'),
                'port'     => getenv('REDIS_PORT'),
            ],
        ],
        'redis'              => [
            'class'    => yii\redis\Connection::class,
            'hostname' => getenv('REDIS_HOSTNAME'),
            'password' => getenv('REDIS_PASSWORD'),
            'database' => getenv('REDIS_DATABASE'),
            'port'     => getenv('REDIS_PORT'),
        ],
        'rabbit_queue'       => [
            'host'              => getenv('RABBITMQ_HOSTNAME'),
            'port'              => getenv('RABBITMQ_PORT'),
            'user'              => getenv('RABBITMQ_USER'),
            'password'          => getenv('RABBITMQ_PASSWORD'),
            'vhost'             => getenv('RABBITMQ_VHOST'),
            'connectionTimeout' => 10,
            'readTimeout'       => 30,
            'writeTimeout'      => 30,
            'heartbeat'         => 3,
            'keepalive'         => true,
            'class'             => Queue::class,
            'queueName'         => 'foquz',
            'driver'            => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'serializer'        => JsonSerializer::class,
            'exchangeName'      => 'foquz',
            'as log'            => LogBehavior::class,
            'as diagnostic'     => DiagnosticBehavior::class,
        ],
        'queue_mailings_out' => [
            'host'                   => getenv('RABBITMQ_HOSTNAME'),
            'port'                   => getenv('RABBITMQ_PORT'),
            'user'                   => getenv('RABBITMQ_USER'),
            'password'               => getenv('RABBITMQ_PASSWORD'),
            'vhost'                  => getenv('RABBITMQ_VHOST'),
            'connectionTimeout'      => 10,
            'readTimeout'            => 30,
            'writeTimeout'           => 30,
            'heartbeat'              => 3,
            'keepalive'              => true,
            'class'                  => yii\queue\amqp_interop\Queue::class,
            'strictJobType'          => false,
            'serializer'             => JsonSerializer::class,
            'queueName'              => 'mailings_external',
            'exchangeName'           => 'mailings_external',
            'driver'                 => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'as log'                 => LogBehavior::class,
            'as diagnostic'          => DiagnosticBehavior::class,
            'queueOptionalArguments' => ['x-max-priority' => 10],
        ]

    ],
    'bootstrap'  => [
        'rabbit_queue',
    ],
    'params'     => [],
];

if (!empty(getenv("QUIZ_AUTHKEY"))) {
    $config["params"]["externalQuiz"] = true;
    $config["params"]["quizAuthKey"] = getenv("QUIZ_AUTHKEY");

    $config["components"]["queue_quiz"] = [
        'class'    => RabbitMQComponent::class,
        'hostname' => getenv('RABBITMQ_HOSTNAME'),
        'port'     => getenv('RABBITMQ_PORT'),
        'user'     => getenv('RABBITMQ_USER'),
        'password' => getenv('RABBITMQ_PASSWORD'),
        'vhost'    => getenv('RABBITMQ_VHOST'),
        'connectionTimeout' => 10,
        'readWriteTimeout'  => 30,
        'heartbeat'         => 3,
        'keepalive'         => true,
    ];
}

if (!empty(getenv('URL_SHORTENER_HOSTPORT'))) {
    $params['urlShortenerHostPort'] = getenv('URL_SHORTENER_HOSTPORT');
    $params['urlShortenerAuthKey'] = getenv('URL_SHORTENER_AUTHKEY');
}

return $config;