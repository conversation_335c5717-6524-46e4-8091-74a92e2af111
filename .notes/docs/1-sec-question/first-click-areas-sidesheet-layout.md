## First Click Areas Sidesheet Layout and Blocks

**Reference Screenshot:** `@sidesheet.png`

**Overall Structure:**

The `first-click-areas-sidesheet` is a full-height UI panel designed for configuring interactive click areas on an image. It slides in, typically from the right side of the screen.

The sidesheet is primarily divided into three horizontal sections:

1.  **Header (Implied but Standard)**:
    *   **Title**: E.g., "Настройка областей для клика" (Configure Click Areas).
    *   **Close Button**: To dismiss the sidesheet.

2.  **Main Content Area**: Vertically split into two main panels.
    *   **Left Panel (Image Interaction Area)**:
        *   Hosts the `image-click-areas` component (see separate documentation: `.notes/docs/1-sec-question/image-click-areas-component.md`).
        *   **Key Behavior**: The image displayed by `image-click-areas` will adapt to the size of this panel (e.g., using `background-size: contain`). All click areas drawn or manipulated on this image will have their positions and dimensions responsive to the displayed image size.
    *   **Right Panel (Area Management & Properties)**:
        *   Manages the list of defined areas and their properties.

3.  **Footer**:
    *   **"Отменить" (Cancel) Button**: Discards changes and closes.
    *   **"Применить" (Apply) Button**: Saves the configuration and closes.

---

**Proportional Coordinate and Dimension Handling (Critical Design Point):**

*   **Storage**: All `x, y, width, height` data for click areas must be stored as **proportional values** (e.g., percentages or decimals from 0.0 to 1.0) relative to the **original dimensions of the uploaded image**.
*   **`image-click-areas` Component Responsibility**:
    *   It will take the original image URL.
    *   Internally, it will determine the original image's native dimensions.
    *   It will display the image adaptively within its container (e.g., fitting while maintaining aspect ratio).
    *   When rendering areas, it will convert stored proportional coordinates/dimensions to absolute pixel values based on the *current displayed size* of the image.
    *   When the user draws, moves, or resizes areas, the interactions (in pixels on the screen) will be converted back to proportional values relative to the original image dimensions before these values are updated in the shared `areas` array and propagated to the sidesheet.
*   **`first-click-areas-sidesheet` Model Responsibility**:
    *   The `areas` array managed by the sidesheet's ViewModel will hold objects with these proportional `x, y, width, height`.
    *   When displaying area dimensions (W, H) in the right panel's input fields, these proportional values should be converted to pixel values for user readability. This conversion should ideally be based on the *original image dimensions* to provide consistent pixel values to the user, regardless of how the image is currently displayed in the left panel.
    *   When the user edits these pixel values in the right panel, they must be converted back to proportional values (relative to the original image dimensions) before updating the area data.

---

**Right Panel: Detailed Block Description**

1.  **Informational Text Block (Top)**:
    *   Static text: "В статистике будет указано количество кликов по добавленным областям".

2.  **Primary "Добавить область" (Add Area) Button**:
    *   Icon: "+", Text: "Добавить область".
    *   **Action**:
        *   Adds a new area object to the `areas` array.
        *   Its initial `x, y, width, height` will be default *proportional* values (e.g., a 50x50px equivalent on the original image, centered).
        *   This new area becomes selected in the `image-click-areas` component and its corresponding block (see below) appears in this list, ready for editing.

3.  **Area List / Editor Section**:
    *   Vertically stacked, scrollable list of blocks, each representing an area.
    *   **Individual Area Block**:
        *   **Area Name Input / Display**:
            *   Text input. `placeholder`: "Область N". `maxlength`: 250. Defaults to "Область N" if empty.
        *   **Click Count Display**: Numeric value (e.g., "48", "60"). For display.
        *   **Delete Area Button**: Trash can icon. Removes area.
        *   **Dimension Inputs (Width & Height)**:
            *   Two input fields, displayed as `[Input W] x [Input H]`.
            *   Labels/placeholders could indicate "px".
            *   `maxlength`: 4. `placeholder`: "50". Default to 50px equivalent if empty.
            *   **Data Flow**:
                1.  When an area is selected, its stored proportional `width` and `height` are converted to pixels (based on original image dimensions) and displayed here.
                2.  When user edits these pixel values, they are converted back to proportional values (relative to original image dimensions) and the area object is updated. This change then reflects in the `image-click-areas` component.
        *   **Selection Highlight**: When an area is selected (via image or this panel), its block here is highlighted (e.g., gray background).

4.  **Secondary "Добавить область" (Add Area) Button (Conditional)**:
    *   Visible at the bottom if at least one area exists.
    *   Identical function to the primary "Добавить область" button. 