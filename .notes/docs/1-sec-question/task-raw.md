Опрос / Вопросы

опрос
https://devfoquz.ru/foquz/foquz-question/update?id=187303&pollId=56659

Настройка вопроса
https://www.figma.com/design/VfmUpjti8Rj8fAYX2DsfVS/fqz_poll_settings-questions?node-id=12041-1351&t=gT6ypjubmn5ZAD8s-1

1.
Нужно добавить для контрола Тип вопроса - значение Тест первого клика.
https://disk.yandex.ru/i/m7Ryy6XF0F8Fzw

Пример в выбранном состоянии.
https://disk.yandex.ru/i/TvFQW4f8f1FEzw
2.
Параметры, как и для других типов вопоросов:

    Тумблер Обязательный
    Служебное название
    Название вопроса
    Текст вопроса
    Дополнительное описание

3.
Блок Загрузка изображения
https://disk.yandex.ru/i/qFH2HH0pFhqSnw

    Загружать можно файлы форматов: jpg, jpeg, png, gif, svg
    Ограничение 5 МБ
    Обязательный параметр

    Для выбранного изображения отображается (https://disk.yandex.ru/i/qPlYrKoj9WmUNw):
        Превью
        https://disk.yandex.ru/i/ytf0SyE_aHpoPA
        При клике просмотр в полноэкранном режиме
        Кнопка Удалить
        https://disk.yandex.ru/i/YRRfOvH9k3BELw
        Кнопка Добавить области клика
        https://disk.yandex.ru/i/a3kyos7gxtnV0Q
        Информационный текст
        https://disk.yandex.ru/i/fM62DFwMEf4kTQ

    Тексты ошибок (на примере типа вопроса Текст 5 секунд):
        Необходимо загрузить изображение
        https://disk.yandex.ru/i/a7BxgIxpDkCUqQ
        Файл должен быть меньше 5 Мб
        https://disk.yandex.ru/i/IcSPQJEH0Ds1yQ
        Ещё сделать проверку на формат файла:
        Выбранный формат файла не поддерживается

4.
При клике на кнопку Добавить области клика показать сайдшит с добавлением областей.
https://disk.yandex.ru/i/kTY4EyUIPyH7qQ --> https://disk.yandex.ru/i/LOvXYjrBvXyJKg

5.
В сайдшите с добавлением областей можно добавить области на загруженное изображение.
https://disk.yandex.ru/i/6BGsQifFAxL0WA
Для каждой области параметры:

    Название
    Размеры
    Координаты XY

6.
В сайдшите при клике на кнопку Добавить область (https://disk.yandex.ru/i/igXeymmWZ6r_6w):

    На изображении по центру появляется дефолтная область 50x50 px в выделенном состоянии
    https://disk.yandex.ru/i/6k_Z-yScw9g1dg

    Справа плавно показать блок с размерами и полем для наименования для добавления области в выделенном состоянии
    https://disk.yandex.ru/i/iW-XvLzCsDDkKw
        Поле для наименование - стандартное поле для ввода, maxlength="250", placeholder="Область N" (где N - порядковый номер области)
        https://disk.yandex.ru/i/pMN_Ma0ZJAI6pw
        Если значение не введено, то название области будет Область N
        Поля для ввода размеров - стандартное поле для ввода, maxlength="4", placeholder="50"
        https://disk.yandex.ru/i/DjFeldUvNMJr6A
        Если значение не введено в поле ввода, то размер 50
        Кнопка Удалить
        При клике на кнопку Удалить
            Плавно убирать блок справа для области
            Менять порядковые номер в placeholder в названиях областей
            Убирать соответствующую фигуру на изображении
            Если областей нет, то отображается только одна кнопка Добавить область

    Область на изображении (https://disk.yandex.ru/i/Occj6gRM_Lg-PQ):
        Можно перетаскивать
        Менять размеры
        За пределы изображения выносить нельзя
        Если область активная, то соответствующий блок справа выделен серым цветом
        Для области отображается название
        Удалять область можно также при клике на клавишу Delete

    Пример, когда несколько областей на изображении
    https://disk.yandex.ru/i/gNK2IBOObnHWtA

    Если больше одной области добавлено, то отображается вторая кнопка Добавить область внизу блока
    https://disk.yandex.ru/i/RS-VHt1ISvVrjQ

7.
Ещё один способ добавления:
Зажимаем левую клавишу мыши в области изображения и тянем на нужный размер. Когда кнопку отпускаем - справа также появляется блок с дефолтным наименованием и размерами области.

Реализовать только, если не будет сложностей. Можно оставить добавление только при клике на кнопку Добавить область.

8.
Добавление областей для изображения необязательно.

9.
Отображение на сматрфоне - селект2 с одним выбором, без строки поиска, без кнопки сброса
https://disk.yandex.ru/i/x5NSEP9T8p1cuQ

    Справочник значений:
        По ширине
        По высоте

    По умолчанию выбрано значение По ширине

10.
Min кол-во кликов - стандартное поле для ввода, без возможности сбросить значение
https://disk.yandex.ru/i/F68HczHsXV1oiw

    По умолчанию проставлено значение 1
    Маска: целые положительный числа
    Ограничение maxlength=3
    Обязательный параметр

11.
Max кол-во кликов - стандартное поле для ввода, без возможности сбросить значение
https://disk.yandex.ru/i/YgIv8nlsfGGe7w

    По умолчанию поле пустое
    Маска: целые положительный числа
    Ограничение maxlength=3
    Валидация: если значение введено, то оно должно быть >= Min кол-во кликов
    Необязательный параметр

12.
Время показа изображения, секунд - стандартное поле для ввода, без возможности сбросить значение
https://disk.yandex.ru/i/4QWtyEjB2OKXbA

    По умолчанию поле пустое
    Маска: целые положительный числа
    Ограничение maxlength=5
    Необязательный параметр

13.
Текст кнопки - стандартное поле для ввода, без возможности сбросить значение, со счётчиком символов, maxlength=30, placeholder="Показать изображение"
https://disk.yandex.ru/i/tdKcoWuE2KX1Mw

    Если значение не введено, то в прохождении нужно использовать дефолтное значение Показать изображение
    Необязательный параметр

14.
Возможность отменить клик - стилизованный чекбокс, по умолчанию выключен
https://disk.yandex.ru/i/ZWxsIrFR5oklPw

15.
Опция Пропуск ответа
https://disk.yandex.ru/i/R1mAGPlTHxpgbQ

    placeholder="Затрудняюсь ответить"
    Если значения для параметра не введено, то использовать значение Затрудняюсь ответить

16.
Опция Комментарий
https://disk.yandex.ru/i/Y7yQrWbWIW_70w

Пример в типе вопроса Приоритет
https://disk.yandex.ru/i/TG4knzKVUwcBbw
https://devfoquz.ru/foquz/foquz-question/update?id=187922&pollId=56659
