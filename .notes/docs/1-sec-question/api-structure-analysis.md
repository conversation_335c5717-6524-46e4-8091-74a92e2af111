# API Structure Analysis: First Click Test Question Type

## Based on Postman Collection Analysis

### Key Findings from API Structure

The Postman collection reveals the actual server-side data structure for the First Click Test question type. This analysis updates our implementation to match the real API expectations.

**Important Update**: Click areas are now saved together with the question update - there is no longer a separate endpoint for creating/updating areas. Areas are included in the main question update request using the `FoquzQuestion[firstClickArea]` namespace.

## API Data Structure

### Question Type Identification
```
FoquzQuestion[main_question_type] = "24"
```
- Confirms our `FIRST_CLICK_TEST = "24"` constant is correct

### Image Handling
```
FoquzQuestion[enableGallery] = "1"
FoquzQuestion[gallery][0][id] = "55850"
```
- **Important**: Reuses existing gallery system instead of custom image upload
- `enableGallery` flag enables gallery functionality
- `gallery` array contains uploaded image IDs
- This means we should integrate with existing gallery components

### First Click Specific Data
All first-click settings are under `FoquzQuestion[firstClick]` namespace:

```
FoquzQuestion[firstClick][mobile_view] = "0"     // 0=width, 1=height
FoquzQuestion[firstClick][min_click] = "1"       // Required minimum clicks
FoquzQuestion[firstClick][max_click] = ""        // Optional maximum clicks
FoquzQuestion[firstClick][show_time] = ""        // Optional display time in seconds
FoquzQuestion[firstClick][button_text] = ""      // Optional button text
FoquzQuestion[firstClick][allow_cancel_click] = "0"  // 0=false, 1=true
```

### Standard Question Fields
```
FoquzQuestion[skip] = "1"
FoquzQuestion[skip_text] = "Затрудняюсь ответить"
```

### Image fields
```
FoquzQuestion[gallery][0][id] = "55850"
FoquzQuestion[galleryEnabled] = "1" // will always be 1
```

### Comment Functionality
```
FoquzQuestion[comment_enabled] = "1"
FoquzQuestion[placeholder_text] = "Подсказка для коммента"
FoquzQuestion[comment_minlength] = "0"
FoquzQuestion[comment_maxlength] = "100"
FoquzQuestion[comment_label] = "Коммент"
FoquzQuestion[comment_required] = "0"
```

## API Endpoints

### 1. Question Settings Update (Including Click Areas)
```
POST /foquz/api/questions/update?id={questionId}&access-token={token}
```
Updates main question settings including first click configuration **and click areas**. This is now the single endpoint for saving all question data including areas.

### 2. Image Upload
```
POST /foquz/api/questions/image-upload?access-token={token}&id={questionId}&scenario=first_click
```
Uploads image file for the first click test.

### 3. Click Area Deletion
```
DELETE /foquz/api/questions/delete-first-click-area?id={questionId}&areaId={areaId}&access-token={token}
```
Deletes a specific click area.

## Click Areas Data Structure

The API now includes click areas directly in the question update request using `FoquzQuestion[firstClickArea]` namespace:

```
FoquzQuestion[firstClickArea][0][id] = "118"     // ID of record, 0 for new record
FoquzQuestion[firstClickArea][0][height] = "20"  // Area height in pixels
FoquzQuestion[firstClickArea][0][width] = "333"  // Area width in pixels
FoquzQuestion[firstClickArea][0][x_coord] = "50" // X coordinate of area
FoquzQuestion[firstClickArea][0][y_coord] = "155" // Y coordinate of area
FoquzQuestion[firstClickArea][0][name] = "Кнопка входа 1"  // Area name/label
```

### Click Area Properties
- **id**: Unique identifier (0 for new areas)
- **height**: Area height in pixels
- **width**: Area width in pixels
- **x_coord**: X coordinate position
- **y_coord**: Y coordinate position
- **name**: Human-readable area name

### Multiple Areas Support
The API supports multiple click areas using array notation:
```
FirstClickArea[0][...] // First area
FirstClickArea[1][...] // Second area
FirstClickArea[2][...] // Third area
```

## Implementation Updates Required

### 1. Gallery Integration
Instead of custom image upload, integrate with existing gallery system:
- Use existing gallery components
- Leverage `enableGallery` flag
- Handle `gallery` array for image IDs

### 2. Data Structure Alignment
Update formatters to match API structure:
- Use `firstClick` namespace for specific settings
- Map `mobile_view` to 0/1 instead of string values
- Handle empty strings for optional fields

### 3. Comment System Integration
Integrate with existing comment system:
- Use standard comment fields
- Leverage existing comment validation
- Reuse comment UI components

### 4. Click Areas Implementation
Implement click areas management:
- Include areas in the main question update using `FoquzQuestion[firstClickArea]` data structure
- Support multiple areas with array notation
- Handle area deletion via dedicated endpoint
- Map coordinates and dimensions properly
- Areas are saved together with question settings in a single request

## Updated Implementation Strategy

### Frontend: FoquzFileLoader with API Transformation
```javascript
// Frontend uses FoquzFileLoader for better UX
this.imageLoader = new FoquzImageLoader(null, {
  presets: ["image"],
  formats: ["jpg", "jpeg", "png", "gif", "svg"],
  maxSize: 5 * 1024 * 1024, // 5MB
});

// But formatters transform to gallery API format
```

### Data Mapping (already implemented)
```javascript
// Client to Server mapping (in server formatter)
const serverData = {
  enableGallery: this.imageFile() ? 1 : 0,
  // gallery array will be populated by file upload process
  firstClick: {
    mobile_view: this.mobileDisplay() === "height" ? 1 : 0,
    min_click: parseInt(this.minClicks()) || 1,
    max_click: this.maxClicks() || "",
    show_time: this.displayTime() || "",
    button_text: this.buttonText() || "",
    allow_cancel_click: this.allowClickCancel() ? 1 : 0
  }
};
```

### Server to Client mapping (already implemented)
```javascript
// Server to Client mapping (in client formatter)
const clientData = {
  imageFile: data.imageFile || null,
  imagePreview: data.imagePreview || "",
  mobileDisplay: data.firstClick?.mobile_view === 1 ? "height" : "width",
  minClicks: data.firstClick?.min_click || 1,
  maxClicks: data.firstClick?.max_click || "",
  displayTime: data.firstClick?.show_time || "",
  buttonText: data.firstClick?.button_text || "",
  allowClickCancel: data.firstClick?.allow_cancel_click === 1
};
```

### Click Areas Data Mapping

Click areas are now included directly in the question update request:

```javascript
// Client to Server mapping for areas
const serverData = {
  // ... other question data ...
  firstClickArea: this.clickAreas().map((area, index) => ({
    id: area.id || 0,  // 0 for new areas
    height: area.height,
    width: area.width,
    x_coord: area.x,
    y_coord: area.y,
    name: area.name
  }))
};
```

The areas are sent as part of the main question update using the `FoquzQuestion[firstClickArea]` namespace. The formData structure will be:
```
FoquzQuestion[firstClickArea][0][id] = "118"
FoquzQuestion[firstClickArea][0][height] = "20"
FoquzQuestion[firstClickArea][0][width] = "333"
FoquzQuestion[firstClickArea][0][x_coord] = "50"
FoquzQuestion[firstClickArea][0][y_coord] = "155"
FoquzQuestion[firstClickArea][0][name] = "Button 1"
```

To handle this data mapping structure properly, check out how other requests are made in this repository, it should already handle formData and this format.

## Action Items

### High Priority
1. **Update formatters** to match API structure
2. **Include click areas in question update** - areas are now saved with the main question data
3. **Keep FoquzFileLoader** for frontend UX but transform to gallery format in formatters
4. **Use existing comment components** for comment functionality

### Medium Priority
1. **Handle file upload to gallery transformation** in the submission process
2. **Implement click areas UI** with drag-and-drop functionality
3. **Update server formatter** to include `firstClickArea` data in the question update
4. **Implement area deletion** using the dedicated delete endpoint
5. **Ensure proper error handling** for file upload failures
6. **Add click areas validation** (coordinates, dimensions)

### Low Priority
1. **Update documentation** to reflect complete API structure
2. **Add API validation** for data integrity
3. **Create migration guide** if needed
4. **Optimize click areas performance** for large images

## Benefits of Complete API Integration

1. **Full Feature Support**: All click areas functionality is now available
2. **API Compatibility**: Complete alignment with backend expectations
3. **Proper CRUD Operations**: Dedicated endpoints for area management
4. **Scalability**: Support for multiple click areas
5. **Maintainability**: Uses established API patterns
6. **Data Integrity**: Proper validation and error handling

This updated analysis provides complete coverage of the First Click Test API structure, including the previously missing click areas functionality. The implementation can now be fully aligned with the actual backend API.
