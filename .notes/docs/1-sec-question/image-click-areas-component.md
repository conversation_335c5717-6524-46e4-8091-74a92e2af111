## Image-Click-Areas Component (`ko/components/image-click-areas/`)

**Reference Screenshot:**

The `image-click-areas` component is the visual tool on the left side of the interface shown below, responsible for displaying an image and allowing users to draw, select, and manipulate rectangular areas on it. The panel on the right (listing areas and their properties) is part of a parent component (e.g., `first-click-areas-sidesheet`) that uses and interacts with `image-click-areas`.

![Sidesheet with image-click-areas component on the left and area editor on the right](@sidesheet.png)

**Initial Implementation Details:** See `@detailed-implementation-guide.md` (Phase 4).

### 1. Overview

The `image-click-areas` Knockout component provides an interactive interface for users to define, modify, and manage multiple rectangular "click areas" on a given image. Its primary function is to handle the visual representation and direct manipulation (drawing, moving, resizing, selecting, deleting via keyboard) of these areas. It uses DOM elements (e.g., `div`s) to represent the image container and the individual areas.

It is designed to be embedded within a parent component (like a sidesheet or modal) which would typically provide the UI for managing area properties (like name and exact dimensions) and would react to changes made within the `image-click-areas` component. The `areas` data it receives and manipulates will contain **proportional coordinates and dimensions**.

### 2. Core Visual Elements & Interaction (DOM-based on the Left)

*   **Image Display Container**:
    *   A primary `div` element acts as the container.
    *   The component takes an `imageUrl` as input. It will load this image to determine its **original (native) dimensions** and also display it as a `background-image` for this container `div`.
    *   The background image will be styled to fit adaptively within the container while maintaining its aspect ratio (e.g., using `background-size: contain; background-repeat: no-repeat; background-position: center;`). The container defines the boundaries for all area manipulations based on the *displayed* image size.
*   **Click Areas (DOM Elements)**:
    *   Each area is rendered as a `div` element, absolutely positioned within the image display container.
    *   The position (`left`, `top`) and size (`width`, `height`) of these `div`s are calculated pixel values, converted from the **proportional** values in the `areas` array, based on the *current displayed size* of the image within the container.
    *   These `div`s will have styling (background color, border, opacity) to make them visible as overlays.
    *   Each area `div` will contain its `name` property as text content.
    *   **Selection**: Clicking an area `div` selects it.
    *   **Manipulation**: Selected areas can be dragged or resized. These interactions modify the pixel-based position/size on screen. These changes are then converted back to **proportional values** (relative to the *original image dimensions*) before updating the corresponding area object in the `areas` array passed by the parent.
    *   **Deletion**: Via `Delete` key.

---
### 3. Proportional Coordinate and Dimension Handling (Critical Design Point)

*   **Input Data (`params.areas`)**: This component expects the `areas` array from its parent to contain objects where `x, y, width, height` are **proportional values** (e.g., decimals from 0.0 to 1.0) relative to the **original dimensions of the source image**.
*   **Internal State**:
    *   The component must fetch and store the **original (native) width and height** of the image specified by `imageUrl`.
    *   It must also know the **current displayed width and height** of the background image within its container. This can change if the container is resized.
*   **Rendering (Proportional to Pixels)**:
    *   When rendering each area `div`, its `left, top, width, height` style properties (in pixels) are calculated by multiplying the stored proportional values (`area.x, area.y, area.width, area.height`) by the *current displayed dimensions* of the image.
    *   Example: `div.style.left = area.x * currentDisplayedImageWidth + 'px';`
*   **Interaction (Pixels to Proportional)**:
    *   When a user draws, moves, or resizes an area, the new pixel-based coordinates and dimensions are captured.
    *   These pixel values must be converted back into **proportional values** relative to the *original image dimensions* before the component calls `onAreasChange` or modifies the `areas` array.
    *   Example for `x`: `updatedArea.x = newPixelLeft / originalImageWidth;` (Assuming `newPixelLeft` is relative to the original image's scale. If `newPixelLeft` is relative to the *displayed* image, then: `updatedArea.x = (newPixelLeftOnDisplayedImage / currentDisplayedImageWidth);`) The latter is more likely for direct manipulation.
    *   This ensures that the stored area definitions remain consistent regardless of the viewing scale.

---

### 4. Adding New Areas (Methods unchanged, but underlying data is proportional)

*   **Method 1: Programmatic Addition**: Parent creates a new area object with default **proportional** properties.
*   **Method 2: Drawing Directly**: User draws on the displayed image. The pixel dimensions of the drawn rectangle are converted to **proportional** values (relative to original image dimensions) to create the new area object.

### 5. Area Properties (as seen by this component via `params.areas`)

Each object in the `params.areas()` observableArray is expected to have:
*   `id`: Unique identifier.
*   `name`: String.
*   `x`: **Proportional** X-coordinate (0.0 to 1.0) of the top-left corner.
*   `y`: **Proportional** Y-coordinate (0.0 to 1.0) of the top-left corner.
*   `width`: **Proportional** width (0.0 to 1.0).
*   `height`: **Proportional** height (0.0 to 1.0).
*   `isSelected` (optional, managed by parent or two-way bound).

### 6. Component Model (`ko/components/image-click-areas/model.js`)

*   **Key Parameters (`params`):**
    *   `imageUrl`: `ko.observable` or string.
    *   `areas`: `ko.observableArray` of area objects (with **proportional** dimensions/coordinates).
    *   `onAreasChange`: `function` callback.
    *   `selectedAreaObservable` (optional).

*   **Internal Observables/Properties (Additions/Changes):**
    *   `originalImageDimensions`: `ko.observable({ width: 0, height: 0 })` - Stores native dimensions of the source image.
    *   `displayedImageDimensions`: `ko.observable({ width: 0, height: 0 })` - Stores current rendered dimensions of the background image within the container. This needs to update if the container resizes.
    *   (ViewModel needs methods to convert between pixel values on screen and proportional values for storage).

*   **Core Methods (Updates for Proportionality):**
    *   `initializeComponent(element)`:
        *   Loads `imageUrl` to get `originalImageDimensions`.
        *   Sets up a way to monitor the `imageContainerElement` size to update `displayedImageDimensions` (e.g., ResizeObserver).
    *   `renderAreas()` / Knockout `foreach` binding: The `style` bindings for area divs will now involve calculations:
        *   `left: (area.x() * displayedImageDimensions().width) + 'px'`
        *   `top: (area.y() * displayedImageDimensions().height) + 'px'`
        *   `width: (area.width() * displayedImageDimensions().width) + 'px'` (Note: width proportion is relative to original image width, so for display use displayedImageDimensions.width, but for storage, it's based on originalImageDimensions.width)
        *   `height: (area.height() * displayedImageDimensions().height) + 'px'`
    *   `handleMouseMove(event)`:
        *   If drawing/dragging/resizing, pixel changes are recorded.
    *   `handleMouseUp(event)`:
        *   Finalized pixel changes are converted to **proportional** values (e.g., `newXprop = newPixelX / displayedImageDimensions().width`) before updating the `areas` array and calling `onAreasChange`.
    *   `addAreaFromDrawing(pixelCoords)`: Helper to convert pixel-based drawing to a proportional area object.

### 7. Template (`ko/components/image-click-areas/template.html`)

The data-bind for `style` on area divs will need to reflect the conversion from proportional to pixel values:

```html
<div class="image-click-areas-container"
     data-bind="style: {
                    backgroundImage: 'url(' + imageUrl() + ')',
                    /* Container size might be CSS-driven or bound if image dictates it */
                  },
                  event: { mousedown: handleMouseDown /* ... */ },
                  attr: { tabindex: 0 }">

  <!-- Render existing areas -->
  <!-- ko foreach: areas -->
  <div class="image-click-area"
       data-bind="style: {
                    left: ($parent.calculatePixelX($data.x()) || 0) + 'px', /* Example helper */
                    top: ($parent.calculatePixelY($data.y()) || 0) + 'px',
                    width: ($parent.calculatePixelWidth($data.width()) || 0) + 'px',
                    height: ($parent.calculatePixelHeight($data.height()) || 0) + 'px'
                  },
                  css: { 'selected': $parent.selectedArea() === $data },
                  event: { mousedown: $parent.onAreaMouseDown.bind($parent, $data) }">
    <span class="area-name" data-bind="text: name"></span>
    <!-- Resize Handles -->
    <!-- ko if: $parent.selectedArea() === $data -->
    <!-- ... resize handles ... -->
    <!-- /ko -->
  </div>
  <!-- /ko -->

  <!-- Temporary area for drawing (dimensions also need to be in pixels) -->
  <!-- ko if: isDrawing() && drawingAreaElementProps() -->
  <div class="image-click-area drawing"
       data-bind="style: { /* pixel values directly from mouse events */ }">
  </div>
  <!-- /ko -->

</div>
```
Helper functions in ViewModel like `calculatePixelX(propX)` would be: `return propX * displayedImageDimensions().width;`.

### 8. Styling (`ko/components/image-click-areas/style.less`)
(No major changes, but ensure container styles allow adaptive background image)

*   `image-click-areas-container`: Add `background-size: contain; background-repeat: no-repeat; background-position: center;`

This update ensures the component correctly handles adaptive image display and stores area data in a resolution-independent, proportional format. 