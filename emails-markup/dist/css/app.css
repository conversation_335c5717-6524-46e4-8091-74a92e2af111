body {
  width: 100% !important;
  min-width: 100%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  margin: 0;
  Margin: 0;
  padding: 0;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

img {
  outline: none;
  text-decoration: none;
  -ms-interpolation-mode: bicubic;
  width: auto;
  max-width: 100%;
  clear: both;
  display: block;
}

center {
  width: 100%;
}

a img {
  border: none;
}

table {
  border-spacing: 0;
  border-collapse: collapse;
}

td, th {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  border-collapse: collapse !important;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

table, tr, td, th {
  padding: 0;
  vertical-align: top;
  text-align: left;
}

@media only screen {
  html {
    min-height: 100%;
    background: #f3f3f3;
  }
}
table.body {
  background: #f3f3f3;
  height: 100%;
  width: 100%;
}
table.container {
  background: #fefefe;
  width: 600px;
  margin: 0 auto;
  Margin: 0 auto;
  text-align: inherit;
}
table.row {
  padding: 0;
  width: 100%;
  position: relative;
}
table.spacer {
  width: 100%;
}
table.spacer td {
  mso-line-height-rule: exactly;
}

table.container table.row {
  display: table;
}

th.columns {
  margin: 0 auto;
  Margin: 0 auto;
  padding-left: 16px;
  padding-bottom: 0;
}
th.columns .columns.first {
  padding-left: 0 !important;
}
th.columns .columns.last {
  padding-right: 0 !important;
}
th.columns .columns:not([class*=large-offset]) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

th.columns.last {
  padding-right: 16px;
}

th.columns table {
  width: 100%;
}
th.columns table.button {
  width: auto;
}

th.large-1 {
  width: 34px;
  padding-left: 8px;
  padding-right: 8px;
}

th.large-1.first {
  padding-left: 16px;
}
.collapse > tbody > tr th.large-1.first {
  width: 58px;
}

.body .columns th.large-1 {
  width: 8.333333%;
}

th.large-5 {
  width: 234px;
  padding-left: 8px;
  padding-right: 8px;
}

th.large-5.first {
  padding-left: 16px;
}

th.large-5.last {
  padding-right: 16px;
}
.collapse > tbody > tr th.large-5.first,
.collapse > tbody > tr th.large-5.last {
  width: 258px;
}

.body .columns th.large-5 {
  width: 41.666666%;
}

th.large-6 {
  width: 284px;
  padding-left: 8px;
  padding-right: 8px;
}

th.large-6.first {
  padding-left: 16px;
}

th.large-6.last {
  padding-right: 16px;
}

.collapse > tbody > tr > th.large-6:not([class*=large-offset]) {
  padding-right: 0;
  padding-left: 0;
  width: 300px;
}
.collapse > tbody > tr th.large-6.first,
.collapse > tbody > tr th.large-6.last {
  width: 308px;
}

th.large-7 {
  width: 334px;
  padding-left: 8px;
  padding-right: 8px;
}

th.large-7.last {
  padding-right: 16px;
}
.collapse > tbody > tr th.large-7.last {
  width: 358px;
}

.body .columns th.large-7 {
  width: 58.333333%;
}

th.large-12 {
  width: 584px;
  padding-left: 8px;
  padding-right: 8px;
}

th.large-12.first {
  padding-left: 16px;
}

th.large-12.last {
  padding-right: 16px;
}

th.expander {
  visibility: hidden;
  width: 0;
  padding: 0 !important;
}

h4.text-center {
  text-align: center;
}
p.text-right {
  text-align: right;
}

img.float-right {
  float: right;
  text-align: right;
}

table.float-center {
  margin: 0 auto;
  Margin: 0 auto;
  float: none;
  text-align: center;
}

th.columns[valign=middle] {
  vertical-align: middle;
}

.hide-for-large {
  display: none;
  mso-hide: all;
  overflow: hidden;
  max-height: 0;
  font-size: 0;
  width: 0;
  line-height: 0;
}
@media only screen and (max-width: 616px) {
  .hide-for-large {
    display: block !important;
    width: auto !important;
    overflow: visible !important;
    max-height: none !important;
    font-size: inherit !important;
    line-height: inherit !important;
  }
}

table.body table.container .hide-for-large * {
  mso-hide: all;
}

@media only screen and (max-width: 616px) {
  table.body table.container .hide-for-large {
    display: table !important;
    width: 100% !important;
  }
}

@media only screen and (max-width: 616px) {
  table.body table.container .show-for-large {
    display: none !important;
    width: 0;
    mso-hide: all;
    overflow: hidden;
  }
}

body,
table.body,
h1,
h2,
h4,
p,
td,
th {
  color: #0a0a0a;
  font-family: Helvetica, Arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  Margin: 0;
  text-align: left;
  line-height: 1.3;
}

h1,
h2,
h4 {
  color: inherit;
  word-wrap: normal;
  font-family: Helvetica, Arial, sans-serif;
  font-weight: normal;
  margin-bottom: 0;
  Margin-bottom: 0;
}

h1 {
  font-size: 18px;
}

h2 {
  font-size: 16px;
}

h4 {
  font-size: 24px;
}

body,
table.body,
p,
td,
th {
  font-size: 16px;
  line-height: 1.3;
}

p {
  margin-bottom: 0;
  Margin-bottom: 0;
}
p a {
  margin: default;
  Margin: default;
}

a {
  color: #3F65F1;
  text-decoration: none;
  font-family: Helvetica, Arial, sans-serif;
  font-weight: normal;
  padding: 0;
  text-align: left;
  line-height: 1.3;
}
a:hover {
  color: #1140ec;
}
a:active {
  color: #1140ec;
}
a:visited {
  color: #3F65F1;
}

h4 a,
h4 a:visited {
  color: #3F65F1;
}

span.preheader {
  display: none !important;
  visibility: hidden;
  mso-hide: all !important;
  font-size: 1px;
  color: #f3f3f3;
  line-height: 1px;
  max-height: 0px;
  max-width: 0px;
  opacity: 0;
  overflow: hidden;
}
table.button {
  width: auto;
  margin: 0 0 20px 0;
  Margin: 0 0 20px 0;
}
table.button table td {
  text-align: left;
  color: #fefefe;
  background: #3F65F1;
  border: 2px solid #3F65F1;
}
table.button table td a {
  font-family: Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: bold;
  color: #fefefe;
  text-decoration: none;
  text-align: left;
  display: inline-block;
  padding: 8.5px 25px 8.5px 25px;
  border: 0 solid #3F65F1;
  border-radius: 4px;
}
table.button.radius table td {
  border-radius: 4px;
  border: none;
}

table.button:not(.expand):not(.expanded) table {
  width: auto;
}

table.button:hover table tr td a,
table.button:active table tr td a,
table.button table tr td a:visited {
  color: #fefefe;
}

table.button:hover table td,
table.button:visited table td,
table.button:active table td {
  background: #1140ec;
  color: #fefefe;
}

table.button:hover table a,
table.button:visited table a,
table.button:active table a {
  border: 0 solid #1140ec;
}

@media only screen and (max-width: 616px) {
  table.body img {
    width: auto;
    height: auto;
  }
  table.body center {
    min-width: 0 !important;
  }
  table.body .container {
    width: 100% !important;
  }
  table.body .columns {
    height: auto !important;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
  table.body .collapse > tbody > tr > .columns {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  th.small-1 {
    display: inline-block !important;
    width: 8.333333% !important;
  }
  th.small-4 {
    display: inline-block !important;
    width: 33.333333% !important;
  }
  th.small-5 {
    display: inline-block !important;
    width: 41.666666% !important;
  }
  th.small-6 {
    display: inline-block !important;
    width: 50% !important;
  }
  th.small-7 {
    display: inline-block !important;
    width: 58.333333% !important;
  }
  th.small-8 {
    display: inline-block !important;
    width: 66.666666% !important;
  }
  th.small-12 {
    display: inline-block !important;
    width: 100% !important;
  }
  table.button.small-expand {
    width: 100% !important;
  }
  table.button.small-expand table {
    width: 100%;
  }
  table.button.small-expand table a {
    text-align: center !important;
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
body,
html,
.body {
  background: #f3f3f3 !important;
}

.header {
  background: #8a8a8a;
  padding-left: 20px;
  padding-right: 20px;
}

.header .columns {
  padding-bottom: 0;
}

.header-logo {
  margin-top: 25px;
  margin-left: 20px;
  margin-bottom: 23px;
}

.header-contacts a, .header-contacts--mobile a {
  font-family: Arial;
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
  color: black;
  text-decoration: none; /* no underline */
}

@media only screen and (max-width: 616px) {
  table.body table.container.header .header-contacts img, table.body table.container.header .header-contacts--mobile img {
    width: 16px !important;
    height: auto !important;
  }
}

.header-contacts .header-contacts__email-icon {
  margin-top: 30px;
}
.header-contacts .header-contacts__email-value {
  margin-top: 26px;
}
.header-contacts .header-contacts__phone-icon {
  margin-top: 28px;
}
.header-contacts .header-contacts__phone-value {
  margin-top: 26px;
}

.header-contacts--mobile .header-contacts__email-icon {
  margin-top: 19px;
}
.header-contacts--mobile .header-contacts__email-value {
  margin-top: 15px;
}
.header-contacts--mobile .header-contacts__phone-icon {
  margin-top: 0;
}
.header-contacts--mobile .header-contacts__phone-value {
  margin-top: 0;
}

.mail-container h1 {
  font-family: Arial;
  font-weight: 700;
  line-height: 16px;
}

.mail-container h2 {
  font-family: "Roboto", Arial;
  font-weight: 700;
  line-height: 18px;
}

.client-data {
  font-family: "Roboto", Arial;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

hr {
  border: 1px solid #E6EBFF;
  margin-block-start: 15px;
  margin-block-end: 15px;
}

.footer__sign {
  font-family: "Roboto", Arial;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
}

.footer__copyright {
  font-family: Arial;
  font-size: 10px;
  font-weight: 400;
  line-height: 14px;
}

.answer__question {
  font-family: "Roboto", Arial;
  font-size: 13px;
  font-weight: 400;
  line-height: 17px;
  color: #73808D;
}

.answer li {
  font-family: "Roboto", Arial;
  font-size: 13px;
  font-weight: 400;
  line-height: 17px;
  color: #73808D;
}

.answer__question--asterisk, .answer__text--skip {
  color: #F96261;
}

.answer ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 20px !important;
}

.answer__text {
  font-family: "Roboto", Arial;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
}

table.button.small-expand table {
  width: 100% !important;
}

.answer__text strong {
  font-family: "Roboto", Arial;
  font-size: 16px;
  font-weight: 700;
  line-height: 17.6px;
}

.answer__variant {
  margin-bottom: 10px;
}
.answer__variant :not(:last-child) {
  margin-bottom: 5px;
}