---
subject: Новый ответ в опросе
---
<container class="header">
  <row class="collapse">
    <columns small="4" large="6">
      <a href="https://foquz.ru" target="_blank">
        <img class="header-logo" src="{{{{raw}}}}{{ url(['/mail/logo-blue.png']) }}{{{{/raw}}}}">
      </a>
    </columns>
    <columns small="8" large="6">
      <row class="header-contacts">
        <columns small="1" valign="middle">
          <img class="show-for-large header-contacts__email-icon" src="{{{{raw}}}}{{ url(['/mail/email.png']) }}{{{{/raw}}}}" alt="">
        </columns>
        <columns small="5" valign="middle">
          <h4 class="show-for-large header-contacts__email-value">
            <a href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </h4>
        </columns>
        <columns small="1" valign="middle">
          <img class="show-for-large header-contacts__phone-icon" src="{{{{raw}}}}{{ url(['/mail/phone.png']) }}{{{{/raw}}}}" alt="">
        </columns>
        <columns small="5" valign="middle">
          <h4 class="show-for-large header-contacts__phone-value">
            <a href="tel:88005002637">
              8 ************
            </a>
          </h4>
        </columns>
      </row>
      <row class="header-contacts--mobile">
        <columns small="5" valign="middle">
          <img class="hide-for-large header-contacts__email-icon float-right" src="{{{{raw}}}}{{ url(['/mail/email.png']) }}{{{{/raw}}}}" alt="">
        </columns>
        <columns small="7" valign="middle">
          <h4 class="hide-for-large header-contacts__email-value text-center">
            <a href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </h4>
        </columns>
      </row>
      <row class="header-contacts--mobile">
        <columns small="5" valign="middle">
          <img class="hide-for-large header-contacts__phone-icon float-right" src="{{{{raw}}}}{{ url(['/mail/phone.png']) }}{{{{/raw}}}}" alt="">
        </columns>
        <columns small="7" valign="middle">
          <h4 class="hide-for-large header-contacts__phone-value text-center">
            <a href="tel:+78005002637">
              ****** 500 2637
            </a>
          </h4>
        </columns>
      </row>
    </columns>
  </row>
</container>

<spacer size="2"></spacer>

<container class="mail-container">
  <spacer size="31"></spacer>

  <row>
    <columns>
      <h1>
        {{{{raw}}}}{% if type == 'new_answers_extended' %}{{{{/raw}}}}
          Новый ответ в опросе
        {{{{raw}}}}{% elseif type == 'answer_assessment_less' %}{{{{/raw}}}}
          Получен ответ с низкой оценкой
        {{{{raw}}}}{% endif %}{{{{/raw}}}}
      </h1>
    </columns>
  </row>

  <spacer size="15"></spacer>

  <row>
    <columns>
      <p>
        В опросе <strong>«{{{{raw}}}}{{ answer.foquzPoll.name }}{{{{/raw}}}}»</strong>
        {{{{raw}}}}{% if type == 'new_answers_extended' %}{{{{/raw}}}}
          появился новый ответ
        {{{{raw}}}}{% elseif type == 'answer_assessment_less' %}{{{{/raw}}}}
          есть ответ с оценкой меньше {percent}% от максимальной
        {{{{raw}}}}{% endif %}{{{{/raw}}}}
      </p>
    </columns>
  </row>

  <spacer size="20"></spacer>

  <row>
    <columns>
      <button href="https://{{{{raw}}}}{{ answer.foquzPoll.company.alias }}{{{{/raw}}}}/foquz/foquz-poll/answer?id={{{{raw}}}}{{ answer.foquz_poll_id }}{{{{/raw}}}}&reviewId={{{{raw}}}}{{ answer.id }}{{{{/raw}}}}&reviewId={{{{raw}}}}{{ answer.id }}{{{{/raw}}}}" class="radius small-expand">
        Посмотреть ответ
      </button>
    </columns>
  </row>

  {{{{raw}}}}{% if answer.contact_id is not empty %}{{{{/raw}}}}
    <row>
      <columns>
        <h2>Клиент</h2>
      </columns>
    </row>

    <spacer size="5"></spacer>

    <row class="client-data">
      <columns>
        <p>
          {{{{raw}}}}{% if answer.contact.last_name is not empty or answer.contact.first_name is not empty or answer.contact.patronymic is not empty %}{{{{/raw}}}}
            {{{{raw}}}}{{ answer.contact.last_name }} {{ answer.contact.first_name }} {{ answer.contact.patronymic }}{{{{/raw}}}}<br>
          {{{{raw}}}}{% endif %}{{{{/raw}}}}
          {{{{raw}}}}{% if answer.contact.formattedPhone is not empty %}{{{{/raw}}}}
            {{{{raw}}}}{{ answer.contact.formattedPhone }}{{{{/raw}}}}<br>
          {{{{raw}}}}{% endif %}{{{{/raw}}}}
          {{{{raw}}}}{% if answer.contact.email is not empty %}{{{{/raw}}}}
            {{{{raw}}}}<a href="mailto:{{ answer.contact.email }}">{{ answer.contact.email }}</a>{{{{/raw}}}}<br>
          {{{{raw}}}}{% endif %}{{{{/raw}}}}
        </p>
      </columns>
    </row>
  {{{{raw}}}}{% endif %}{{{{/raw}}}}

  <spacer size="20"></spacer>

  <row>
    <columns>
      <h2>Ответы</h2>
    </columns>
  </row>

  <row>
    <columns>
      <hr>
    </columns>
  </row>

  {{{{raw}}}}{% for mainQuestion in answer.answerContentForEmail %}{{{{/raw}}}}
    <row class="answer answer-star-rating">
      <columns>
        <ol start="{{{{raw}}}}{{ mainQuestion.id }}{{{{/raw}}}}">
          <li>
            {{{{raw}}}}{% for question in mainQuestion.item %}{{{{/raw}}}}
              {{{{raw}}}}{% if question.is_variant == true %}{{{{/raw}}}}
                <div class="answer__variants">
                {{{{raw}}}}{% if question.is_3d_matrix == true %}{{{{/raw}}}}
                  <div class="answer-3d-matrix__variant">
                {{{{raw}}}}{% else %}{{{{/raw}}}}
                  <div class="answer__variant">
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
              {{{{raw}}}}{% endif %}{{{{/raw}}}}
              {{{{raw}}}}{% if question.question is not empty %}{{{{/raw}}}}
                <p class="answer__question">
                  {{{{raw}}}}{% if question.is_3d_matrix_row == true %}{{{{/raw}}}}
                    <strong>{{{{raw}}}}{{ question.question }}{{{{/raw}}}}</strong>
                    <spacer size="5"></spacer>
                  {{{{raw}}}}{% else %}{{{{/raw}}}}
                    {{{{raw}}}}{{ question.question }}{{{{/raw}}}}
                  {{{{raw}}}}{% endif %}{{{{/raw}}}}
                  {{{{raw}}}}{% if question.is_required == true %}{{{{/raw}}}}
                  <span class="answer__question--asterisk">*</span>
                  {{{{raw}}}}{% endif %}{{{{/raw}}}}
                  {{{{raw}}}}{% if question.type_name is not empty %}{{{{/raw}}}}
                  <br><em class="answer__question-type">{{{{raw}}}}{{ question.type_name }}{{{{/raw}}}}</em>
                  {{{{raw}}}}{% endif %}{{{{/raw}}}}
                </p>
              {{{{raw}}}}{% endif %}{{{{/raw}}}}
              {{{{raw}}}}{% for answer in question.answers %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'question-skipped' and question.is_question_hidden != true %}{{{{/raw}}}}
                  <p class="answer__question">
                    <em>Вопрос пропущен</em>
                  </p>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'question-skipped' and question.is_question_hidden == true %}{{{{/raw}}}}
                  <p class="answer__question">
                    <em>Вопрос для респондента не отображался</em>
                  </p>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'answer-empty' %}{{{{/raw}}}}
                  <p class="answer__question">
                    <em>Ответ пропущен</em>
                  </p>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'answer-skipped' %}{{{{/raw}}}}
                  <p class="answer__text">
                      <span class="answer__text--skip">
                      Пропуск ответа
                      </span>
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'rating-skipped' %}{{{{/raw}}}}
                  <p class="answer__text">
                        <span class="answer__text--skip">
                        Пропуск оценки
                        </span>
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'rating' %}{{{{/raw}}}}
                  <p class="answer__text">
                    <strong>{{{{raw}}}}{{ answer.value }}{{{{/raw}}}}</strong> из
                    {{{{raw}}}}{{ answer.max }}{{{{/raw}}}}{{{{raw}}}}{{ answer.mark }}{{{{/raw}}}}
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'nps' %}{{{{/raw}}}}
                  <p class="answer__text">
                    <strong>{{{{raw}}}}{{ answer.value }}{{{{/raw}}}}</strong> {{{{raw}}}}{{ answer.mark }}{{{{/raw}}}}
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'answer' %}{{{{/raw}}}}
                  <p class="answer__text">
                    {{{{raw}}}}{{ answer.value }}{{{{/raw}}}}
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'email' %}{{{{/raw}}}}
                  <p class="answer__text">
                    <a href="mailto:{{{{raw}}}}{{ answer.value }}{{{{/raw}}}}">{{{{raw}}}}{{ answer.value }}{{{{/raw}}}}</a><br>
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'link' %}{{{{/raw}}}}
                  <p class="answer__text">
                    <a href="{{{{raw}}}}{{ answer.value }}{{{{/raw}}}}" target="_blank">
                      {{{{raw}}}}
                        {% if answer.name is not empty %}
                          {{ answer.name }}
                        {% else %}
                          {{ answer.value }}
                        {% endif %}
                      {{{{/raw}}}}
                    </a><br>
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'links' %}{{{{/raw}}}}
                  <p class="answer__text">
                    {{{{raw}}}}{% for value in answer.values %}{{{{/raw}}}}
                      {{{{raw}}}}{% if value.first_item != true %},{% endif %}{{{{/raw}}}}
                      <a href="{{{{raw}}}}{{ value.value }}{{{{/raw}}}}" target="_blank" class="answer__text--link">
                        {{{{raw}}}}
                          {% if value.name is not empty %}
                            {{ value.name }}
                          {% else %}
                            {{ value.value }}
                          {% endif %}
                        {{{{/raw}}}}
                      </a>
                    {{{{raw}}}}{% endfor %}{{{{/raw}}}}
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'file' %}{{{{/raw}}}}
                  <p class="answer__text">
                    <a href="{{{{raw}}}}{{ answer.url }}{{{{/raw}}}}" target="_blank" class="answer__text--link">
                      {{{{raw}}}}{{ answer.name }}{{{{/raw}}}}
                    </a>
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'priority' %}{{{{/raw}}}}
                  <p class="answer__text">
                    <ol class="answer__text--list">
                      {{{{raw}}}}{% for value in answer.values %}{{{{/raw}}}}
                        <li>
                          <span>
                            {{{{raw}}}}{{ value }}{{{{/raw}}}}
                          </span>
                        </li>
                      {{{{raw}}}}{% endfor %}{{{{/raw}}}}
                    </ol>
                  </p>
                  <spacer size="5"></spacer>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                {{{{raw}}}}{% if answer.type == 'dictionary' %}{{{{/raw}}}}
                  <div class="answer__variant">
                    <p class="answer__text">
                      {{{{raw}}}}{% if answer.categories is not empty %}{{{{/raw}}}}
                        <span class="answer__text--path">{{{{raw}}}}{{ answer.categories }}{{{{/raw}}}} /</span>
                      {{{{raw}}}}{% endif %}{{{{/raw}}}}
                      {{{{raw}}}}{{ answer.element }}{{{{/raw}}}}
                    </p>
                  </div>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
              {{{{raw}}}}{% endfor %}{{{{/raw}}}}
              {{{{raw}}}}{% if question.is_variant == true %}{{{{/raw}}}}
                {{{{raw}}}}{% if question.is_3d_matrix == true %}{{{{/raw}}}}
                  </div>
                {{{{raw}}}}{% elseif question.is_variant == true %}{{{{/raw}}}}
                  </div>
                {{{{raw}}}}{% endif %}{{{{/raw}}}}
                </div>
              {{{{raw}}}}{% endif %}{{{{/raw}}}}
              {{{{raw}}}}{% if question.type == 'points' %}{{{{/raw}}}}
                <div class="answer__variant">
                  <p class="answer__text">
                  <span class="answer__text--score">
                    <em>{{{{raw}}}}{{ question.value }} из {{ question.max }} баллов{{{{/raw}}}}</em>
                  </span>
                  </p>
                </div>
              {{{{raw}}}}{% endif %}{{{{/raw}}}}
            {{{{raw}}}}{% endfor %}{{{{/raw}}}}
          </li>
        </ol>
      </columns>
    </row>

    <row>
      <columns>
        <hr>
      </columns>
    </row>
  {{{{raw}}}}{% endfor %}{{{{/raw}}}}

  <spacer size="5"></spacer>

  <spacer size="5"></spacer>

  <row>
    <columns>
      <button href="https://{{{{raw}}}}{{ answer.foquzPoll.company.alias }}{{{{/raw}}}}/foquz/foquz-poll/answer?id={{{{raw}}}}{{ answer.foquz_poll_id }}{{{{/raw}}}}" class="radius small-expand">
        Посмотреть ответ
      </button>
    </columns>
  </row>

  <row>
    <columns>
      <hr>
    </columns>
  </row>

  <row class="footer">
    <columns small="6">
      <p class="footer__sign">
        С уважением,<br>
        команда FOQUZ<br>
        <a href="mailto:<EMAIL>"><EMAIL></a><br>
        <a href="tel:88005002637">8 ************</a>
      </p>
    </columns>
    <columns small="6" valign="top">
      <p class="footer__copyright text-right">
        © Foquz, {{{{raw}}}}{{ answer.updated_at|date('Y') }}{{{{/raw}}}} г.
      </p>
    </columns>
  </row>

  <spacer size="20"></spacer>

</container>
