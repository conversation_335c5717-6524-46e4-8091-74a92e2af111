// These are the styles to make some of the templates look extra nice.
// Feel free to remove these styles for production!

// Body Styles 
body,
html, 
.body {
  background: #f3f3f3 !important;
}

.body-drip {
  border-top: 8px solid #663399;
}

// Full Width Headers
.header {
  background: #8a8a8a;
  padding-left: 20px;
  padding-right: 20px;
}

.header .columns {
  padding-bottom: 0;
}

.header p {
  color: #fff;
  padding-top: 15px;
}

.header .wrapper-inner {
  padding: 20px;
}

.header .container {
  background: transparent;
}

// Social Media
table.button.facebook table td {
  background: #3B5998 !important;
  border-color: #3B5998;
}

table.button.twitter table td {
  background: #1daced !important;
  border-color: #1daced;
}

table.button.google table td {
  background: #DB4A39 !important;
  border-color: #DB4A39;
}

.wrapper.secondary {
  background: #f3f3f3;
}

.header-logo {
  margin-top: 25px;
  margin-left: 20px;
  margin-bottom: 23px;
}

.header-contacts a,.header-contacts--mobile a {
  font-family: Arial;
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
  color: black;
  text-decoration: none; /* no underline */
}

table.body table.container.header .header-contacts img,table.body table.container.header .header-contacts--mobile img {
  @media only screen and (max-width: 616px) {
    width: 16px !important;
    height: auto !important;
  }
}

.header-contacts {
  .header-contacts__email-icon {
    margin-top: 30px;
  }
  .header-contacts__email-value {
    margin-top: 26px;
  }
  .header-contacts__phone-icon {
    margin-top: 28px;
  }
  .header-contacts__phone-value {
    margin-top: 26px;
  }
}

.header-contacts--mobile {
  .header-contacts__email-icon {
    margin-top: 19px;
  }
  .header-contacts__email-value {
    margin-top: 15px;
  }
  .header-contacts__phone-icon {
    margin-top: 0;
  }
  .header-contacts__phone-value {
    margin-top: 0;
  }
}

.mail-container h1 {
  font-family: Arial;
  font-weight: 700;
  line-height: 16px;
}

.mail-container h2 {
  font-family: 'Roboto', Arial;
  font-weight: 700;
  line-height: 18px;
}

.client-data {
  font-family: 'Roboto', Arial;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

hr {
  border: 1px solid #E6EBFF;
  margin-block-start: 15px;
  margin-block-end: 15px;
}

.footer__sign {
  font-family: 'Roboto', Arial;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
}

.footer__copyright {
  font-family: Arial;
  font-size: 10px;
  font-weight: 400;
  line-height: 14px;
}

.answer__question {
  font-family: 'Roboto', Arial;
  font-size: 13px;
  font-weight: 400;
  line-height: 17px;
  color: #73808D;
}

.answer li {
  font-family: 'Roboto', Arial;
  font-size: 13px;
  font-weight: 400;
  line-height: 17px;
  color: #73808D;
}

.answer__question--asterisk, .answer__text--skip {
  color: #F96261;
}

.answer__text--score {
  color: #CAAD46;
}

.answer ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 20px !important;
}

.answer__text {
  font-family: 'Roboto', Arial;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  margin-bottom: 10px;
}

table.button.small-expand table, table.button.small-expanded table {
  width: 100% !important;
}

.answer__text--link {
  font-family: 'Roboto', Arial;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}

.answer__text--list {
  list-style-position: inside;
  padding-left: 0 !important;

  li {
    margin-bottom: 10px;
    font-family: 'Roboto', Arial;
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
    color: #000000;

    span {
      font-weight: 400;
    }
  }
}

.answer__text strong {
  font-family: 'Roboto', Arial;
  font-size: 16px;
  font-weight: 700;
  line-height: 17.6px;
}

.answer__text--path {
  color: #73808D;
}

.answer li>.answer__question:first-child {
  margin-bottom: 10px;
}

.answer-star-rating__text {
  margin-bottom: 10px;
}

.answer__variant {
  margin-top: 10px;
  margin-bottom: 10px;

  :not(:last-child) {
    margin-bottom: 5px;
  }
}

.answer__variants {
  margin-bottom: -10px;
  
  .answer__variant {
    line-height: 1.3;
    margin-bottom: 10px;
  }
}

.answer-3d-matrix__variant {
  margin-bottom: 10px;
  
  .answer__question {
    font-family: 'Roboto', Arial;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;

    strong {
      font-size: 13px;
      font-weight: 700;
      line-height: 17px;
    }
  }
}

.client-contact__header th {
  font-family: 'Roboto', Arial;
  font-size: 13px;
  font-weight: 400;
  line-height: 17px;
  color: #73808D;
}

.client-contact__text th {
  font-family: 'Roboto', Arial;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}

.client-contact__text--success th {
  font-family: 'Roboto', Arial;
  font-size: 14px;
  font-weight: 700;
  line-height: 18px;
  color: #029A15;
}

.client-text th {
  font-family: 'Roboto', Arial;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
}

.client-text__link {
  font-family: 'Roboto', Arial;
  font-size: 14px;
  font-weight: 400;
  line-height: 15px;
  color: #3F65F1;
}

.answer__question-type {
  display: block;
  margin-bottom: 10px;
}
