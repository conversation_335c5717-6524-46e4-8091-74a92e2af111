<?php

namespace app\consumers;

use app\components\RabbitMQComponent;
use app\consumers\widget\AnswerParamsConsumer;
use Yii;

class Router
{
    private const ROUTES = [
        'widget' => [
            'answer_params' => AnswerParamsConsumer::class,
        ]
    ];

    public function execute($queueName, $message):void
    {
        if (!preg_match('/^' . Yii::$app->params['queueNamePrefix'] . '\.([a-z_]+)$/', $queueName, $matches)) {
            throw new \InvalidArgumentException('Invalid queue name (prefix)');
        }
        [$type, $messageBody] = RabbitMQComponent::parseMessage($message->getBody());
        if (!isset(self::ROUTES[$matches[1]][$type])) {
            throw new \InvalidArgumentException('Invalid queue name (route not found)');
        }
        $className = self::ROUTES[$matches[1]][$type];
        $router = new $className();
        Yii::$app->db->open();
        $file = Yii::getAlias(Yii::$app->params['queueDiagnosticFile']);
        file_put_contents($file, time());
        if (!isset($message->get_properties()['correlation_id'])) {
            $router->execute($messageBody);
        } else {
            $result = $router->getResult($messageBody);
            $rabbit = Yii::$app->queue;
            $rabbit->queue($message->get('reply_to'))
                ->correlationID($message->get('correlation_id'))
                ->type($type)
                ->push($result);
        }
        file_put_contents($file, '');
        Yii::$app->db->close();
    }
}
