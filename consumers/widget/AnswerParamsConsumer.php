<?php

namespace app\consumers\widget;

use app\modules\foquz\models\FoquzPollMailingListSend;
use yii\db\Exception;

class AnswerParamsConsumer
{
    /**
     * @throws Exception
     */
    public function execute(array $attributes): void
    {
        if (!isset($attributes['key'], $attributes['params'])) {
            throw new Exception('Invalid attributes');
        }

        $key = $attributes['key'];
        $params = $attributes['params'];

        /** @var FoquzPollMailingListSend|null $send */
        $send = FoquzPollMailingListSend::find()
            ->with('answer')
            ->where(['key' => $key])
            ->andWhere(['NOT', ['widget_id' => null]])
            ->andWhere(['NOT', ['answer_id' => null]])
            ->one();

        if (!$send) {
            return;
        }

        $customFields = json_decode($send->answer->custom_fields, true) ?? [];
        $params = array_filter($params, static function ($key) {
            return !in_array($key, ['email', 'phone', 'lastname', 'firstname', 'телефон', 'фамилия', 'имя', 'client_id', 'lang']);
        }, ARRAY_FILTER_USE_KEY);
        $customFields = array_merge($customFields, $params);

        $send->answer->custom_fields = json_encode($customFields, JSON_UNESCAPED_UNICODE);
        $send->answer->save();
    }
}
