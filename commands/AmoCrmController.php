<?php

namespace app\commands;
use app\components\AmoCrmComponent;

use AmoCRM\OAuth2\Client\Provider\AmoCRM;
use app\modules\foquz\models\FoquzQuestion;
use League\OAuth2\Client\Grant\AuthorizationCode;
use League\OAuth2\Client\Grant\RefreshToken;
use League\OAuth2\Client\Token\AccessToken;
use yii\console\Controller;

class AmoCrmController extends Controller
{
    public function actionTest()
    {
                $amo = new AmoCrmComponent();
            //amo->checkRefreshToken();
                $contact = $amo->findContact('79268444100');
                print_r($contact); 
                //$contactId = $contact ? $contact['id'] : $amo->createContact('test test', null, '79268444100', null);
                //$field = $amo->createLeadNew('TEST TEST 16', 0, null, $contactId, ['utm_term'=>'test', 'openstat_service'=>'test'], 'Запрос презентации');
               exit;
                $companyId = $amo->createContact("test 1243124", '<EMAIL>', '79268444037', 27557651, 1);
        print($companyId); exit;
                if($companyId) {
                    $contactId = $amo->createContact($user->name, $user->email, '', $companyId);
                    $dealId = $amo->createLead('Регистрация Foquz', 0, $companyId);
                    $taskId = $amo->createTask('Связаться с клиентом', time()+(24*60*60), $dealId);
                }

    }


    public function actionGetToken($code)
    {
        $provider = new AmoCRM([
            'clientId' => \Yii::$app->params['amo_client_id'],
            'clientSecret' => \Yii::$app->params['amo_client_secret'],
            'redirectUri' => 'https://foquz.ru',
        ]);

        $provider->setBaseDomain(\Yii::$app->params['amo_domain']);



        if(true || !file_exists(\Yii::getAlias('@app').'/libs/amo/credentials.json')) {

          //  try {
                /** @var \League\OAuth2\Client\Token\AccessToken $access_token */
                $accessToken = $provider->getAccessToken(new AuthorizationCode(), [
                    'code' => $code
                ]);


                print_r($accessToken);// exit;
                if (!$accessToken->hasExpired()) {
                    $this->saveToken([
                        'accessToken' => $accessToken->getToken(),
                        'refreshToken' => $accessToken->getRefreshToken(),
                        'expires' => $accessToken->getExpires(),
                        'baseDomain' => $provider->getBaseDomain(),
                    ]);
                }
           // } catch (\Exception $e) {
           //     \Yii::error((string)$e, __METHOD__);
          //  }
        } else {
            $accessToken = $this->getToken();

            if ($accessToken->hasExpired()) {
              ///  try {
                    $accessToken = $provider->getAccessToken(new RefreshToken(), [
                        'refresh_token' => $accessToken->getRefreshToken(),
                    ]);

                    $this->saveToken([
                        'accessToken' => $accessToken->getToken(),
                        'refreshToken' => $accessToken->getRefreshToken(),
                        'expires' => $accessToken->getExpires(),
                        'baseDomain' => $provider->getBaseDomain(),
                    ]);

              /** } catch (\Exception $e) {
                    \Yii::error((string)$e, __METHOD__);
                }*/
            }
        }
    }

    private function saveToken($accessToken)
    {
        if (
            isset($accessToken)
            && isset($accessToken['accessToken'])
            && isset($accessToken['refreshToken'])
            && isset($accessToken['expires'])
            && isset($accessToken['baseDomain'])
        ) {
            $data = [
                'accessToken' => $accessToken['accessToken'],
                'expires' => $accessToken['expires'],
                'refreshToken' => $accessToken['refreshToken'],
                'baseDomain' => $accessToken['baseDomain'],
            ];

            try {
                file_put_contents(\Yii::getAlias('@app').'/libs/amo/credentials.json', json_encode($data));
            } catch (\Exception $e) {
                \Yii::error($e->getMessage(), __METHOD__);
            }
        } else {
            \Yii::error('Invalid access token ' . var_export($accessToken, true), __METHOD__);
        }
    }

    private function getToken()
    {
        $accessToken = json_decode(file_get_contents(\Yii::getAlias('@app').'/libs/amo/credentials.json'), true);

        if (
            isset($accessToken)
            && isset($accessToken['accessToken'])
            && isset($accessToken['refreshToken'])
            && isset($accessToken['expires'])
            && isset($accessToken['baseDomain'])
        ) {
            return new AccessToken([
                'access_token' => $accessToken['accessToken'],
                'refresh_token' => $accessToken['refreshToken'],
                'expires' => $accessToken['expires'],
                'baseDomain' => $accessToken['baseDomain'],
            ]);
        } else {
            \Yii::error('Invalid access token ' . var_export($accessToken, true), __METHOD__);
        }
    }
}