<?php

namespace app\commands;

use app\components\messenger\SMSService;
use app\models\company\Company;
use app\models\db\Contact;
use app\models\DictionaryElement;
use app\models\Filial;
use app\models\FilialCategory;
use app\models\User;
use app\modules\foquz\models\Channel;
use app\modules\foquz\models\Feedback;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\log\Log;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessing;
use app\modules\foquz\models\RecipientQuestionDetail;
use app\modules\foquz\services\custom\CustomQuotaService;
use Doctrine\Common\Annotations\PhpParser;
use yii\console\Controller;
use yii\helpers\ArrayHelper;
use yii\helpers\FileHelper;

/**
 * Контроллер для исправления ошибок в БД
 */
class FixController extends Controller
{
    public function action120124FixVariantAnswers($start = null, $end = null): void
    {
        $startTime = microtime(true);
        $answerItemsQuery = FoquzPollAnswerItem::find()
            ->leftJoin('foquz_question', 'foquz_question.id = foquz_poll_answer_item.foquz_question_id')
            ->where(['foquz_question.main_question_type' => FoquzQuestion::TYPE_VARIANTS])
            ->andWhere(['LIKE', 'detail_item', '{']);
        if ($start) {
            $answerItemsQuery->andWhere(['>=', 'foquz_poll_answer_item.created_at', date('Y-m-d', strtotime($start))]);
        }
        if ($end) {
            $answerItemsQuery->andWhere(['<=', 'foquz_poll_answer_item.created_at', date('Y-m-d', strtotime($end))]);
        }
        $saved = 0;
        $error = 0;
        foreach ($answerItemsQuery->batch(1000) as $answerItems) {
            /** @var FoquzPollAnswerItem $answerItem */
            foreach ($answerItems as $answerItem) {
                $variants = json_decode($answerItem->detail_item, true) ?? [];
                $variants = array_filter($variants, static function ($variant) {
                    return $variant !== 'is_self_answer';
                });
                $answerItem->detail_item = json_encode(array_values($variants));
                if ($answerItem->save(false)) {
                    print 'Saved: ' . $answerItem->id . PHP_EOL;
                    $saved++;
                } else {
                    print 'Error: ' . $answerItem->id . PHP_EOL;
                    $error++;
                }
            }
        }
        print 'Total saved: ' . $saved . PHP_EOL;
        print 'Total error: ' . $error . PHP_EOL;
        print 'Duration: ' . round(microtime(true) - $startTime) . ' sec.' . PHP_EOL;
    }

    public function action150124FixQuestionsSort($start = null, $end = null): void
    {
        $problemPolls = FoquzQuestion::find()
            ->select('foquz_question.poll_id, foquz_question.position, COUNT(*) AS c')
            ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_question.poll_id')
            ->where([
                'NOT',
                ['foquz_question.poll_id' => null, 'foquz_question.is_deleted' => 1, 'foquz_question.is_tmp' => 1]
            ])
            ->groupBy('foquz_question.poll_id, foquz_question.position')
            ->having('c > 1');

        if ($start) {
            $problemPolls->andWhere(['>=', 'foquz_poll.created_at', date('Y-m-d', strtotime($start))]);
        }
        if ($end) {
            $problemPolls->andWhere(['<=', 'foquz_poll.created_at', date('Y-m-d', strtotime($end))]);
        }

        $problemPolls = $problemPolls->column();

        $problemPolls = array_values(array_unique($problemPolls));
        print 'Problem polls: ' . count($problemPolls) . PHP_EOL;

        $questions = FoquzQuestion::find()
            ->where(['poll_id' => $problemPolls])
            ->orderBy(['position' => SORT_ASC])
            ->all();

        $questions = ArrayHelper::index($questions, null, 'poll_id');

        $saved = 0;
        $error = 0;
        foreach ($questions as $pollId => $pollQuestions) {
            $position = 1;
            if (!$pollId) {
                continue;
            }
            print 'Start fixing order for poll #' . $pollId . PHP_EOL;
            $transaction = FoquzQuestion::getDb()->beginTransaction();
            try {
                /** @var FoquzQuestion $question */
                foreach ($pollQuestions as $question) {
                    $question->position = $position;
                    if ($question->save(false)) {
                        print 'Saved order for question #' . $question->id . PHP_EOL;
                        $saved++;
                    } else {
                        print 'Error while saving order for question #' . $question->id . PHP_EOL;
                        $error++;
                    }
                    $position++;
                }
                print 'Finish fixing order for poll #' . $pollId . PHP_EOL;
                $transaction->commit();
            } catch (\Throwable $e) {
                $transaction->rollBack();
                print 'Error while fixing order for poll #' . $pollId . PHP_EOL;
                print $e->getMessage() . PHP_EOL;
            }
        }
        print 'Total saved: ' . $saved . PHP_EOL;
        print 'Total error: ' . $error . PHP_EOL;
    }

    /**
     * проставить связку для РХ анкет с клиентами
     * где не проставилась автоматически
     *
     * @return void
     */
    public function action190124FixRHLinkClients(): void
    {
        $polls = FoquzPoll::find()->where(["company_id" => 3308])->with("foquzQuestions")->all();
        foreach ($polls as $p) {
            $answers = FoquzPollAnswer::find()->where([
                "foquz_poll_id" => $p->id,
                "status"        => ["done", "in-progress"]
            ])->andWhere("contact_id is null")->all();
            if (count($answers)) {
                print($p->name . PHP_EOL);
                foreach ($p->foquzQuestions as $q) {
                    if ($q->link_with_client_field || FoquzQuestionFormField::find()->where([
                            'question_id'            => $q->id,
                            'link_with_client_field' => 1
                        ])->one()) {
                        print("\t{$q->id}" . PHP_EOL);
                        foreach ($answers as $a) {
                            $item = FoquzPollAnswerItem::find()->where([
                                "foquz_question_id"    => $q->id,
                                "foquz_poll_answer_id" => $a->id
                            ])->one();
                            if ($item->answer) {
                                $answer = json_decode($item->answer, true);
                                $a->linkWithClientIfPossible($q, $answer);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * обновление филиалов для компании из файла
     *
     * @return void
     */
    public function action300124AddFilials(): void
    {
        $file = fopen("../advb-c.txt", "r");
        $companyId = 3444;

        $data = [0, 0, 0, 0, 0, 0];
        while ($row = fgets($file)) {
            $row = explode("\t", $row);

            $crm_id = trim($row[0]);
            $filial = Filial::find()->where(["company_id" => $companyId, "crm_id" => $crm_id])->one();
            $name = trim($row[1]);
            $isChange = false;
            if (!$filial) {
                print("Новый филиал          \t{$crm_id}\t{$name}\n");
                $filial = new Filial;
                $filial->company_id = $companyId;
                $filial->crm_id = $crm_id;
                $data[0]++;
                $isChange = true;
            } elseif ($filial->name !== $name) {
                print("Изменилось имя филиала\t{$crm_id}\t{$name}\t$filial->name\n");
                $data[1]++;
                $isChange = true;
            } elseif (!$filial->is_active) {
                print("Филиал будет включен\t{$crm_id}\t{$name}\t$filial->name\n");
                $filial->is_active = true;
                $data[3]++;
                $isChange = true;
            } else {
                print("Филиал без изменения\t{$crm_id}\t{$name}\t$filial->name\n");
                $data[2]++;
            }
            $filial->name = $name;
            //$filial->address = $row[2] ? trim($row[2]) : null;
            /*$filial->param1 = $row[3] ? trim($row[3]) : null;
            $filial->param2 = $row[4] ? trim($row[4]) : null;
            $filial->param3 = $row[5] ? trim($row[5]) : null;*/
            if ($isChange) {
                $data[4] += $filial->save();
            }
            $ids[] = $filial->id;
        }
        fclose($file);

        $filials = Filial::find()->where(["company_id" => $companyId])->andWhere([
            "not in",
            "id",
            $ids
        ])->andWhere(["is_active" => 1])->all();
        foreach ($filials as $f) {
            print("Филиал будет выключен\t{$f->crm_id}\t{$f->name}\n");
            $f->is_active = 0;
            $data[4] += $f->save();
            $data[5]++;
        }

        print("\n\n");
        print("Новых филиалов $data[0]\n");
        print("Изменилось филиалов $data[1]\n");
        print("Включено филиалов $data[3]\n");
        print("Без изменений филиалов $data[2]\n");
        print("Включено филиалов $data[5]\n\n");

        print("Всего изменилось филиалов $data[4]\n");

    }

    /**
     * Проставляет максимальный балл для ответов в foquz_poll_answer_item
     * @param $company_id
     * @return void
     */
    public function action160224CalcMaxPoint($company_id = null)
    {
        ini_set('memory_limit', '2048M');
        $questionsWithoutPoints = FoquzQuestion::find()
            ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_question.poll_id')
            ->leftJoin('foquz_question_detail', 'foquz_question_detail.foquz_question_id = foquz_question.id')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_question_id = foquz_question.id')
            ->where(['foquz_poll.point_system' => true, 'foquz_poll.deleted' => false])
            ->andWhere(['foquz_question_detail.without_points' => true])
            ->andWhere([
                'OR',
                ['>', 'foquz_poll.in_progress_answers_count', 0],
                ['>', 'foquz_poll.filled_answers_count', 0]
            ])
            ->andFilterWhere(['foquz_poll.company_id' => $company_id])
            ->all();

        $questions = FoquzQuestion::find()
            ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_question.poll_id')
            ->leftJoin('foquz_question_detail', 'foquz_question_detail.foquz_question_id = foquz_question.id')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_question_id = foquz_question.id')
            ->where(['foquz_poll.point_system' => true, 'foquz_poll.deleted' => false])
            ->andWhere(['NOT', ['foquz_question.id' => ArrayHelper::getColumn($questionsWithoutPoints, 'id')]])
            ->andWhere([
                'OR',
                ['>', 'foquz_poll.in_progress_answers_count', 0],
                ['>', 'foquz_poll.filled_answers_count', 0]
            ])
            ->andFilterWhere(['foquz_poll.company_id' => $company_id])
            ->all();

        /** @var FoquzQuestion $question */
        foreach ($questions as $question) {
            FoquzPollAnswerItem::updateAll(['max_points' => $question->maxPoints],
                ['foquz_question_id' => $question->id]);
        }

        foreach ($questionsWithoutPoints as $question) {
            $answerItems = FoquzPollAnswerItem::find()
                ->where(['foquz_question_id' => $question->id])
                ->all();

            /** @var FoquzPollAnswerItem $answerItem */
            foreach ($answerItems as $answerItem) {
                $answerItem->max_points = $answerItem->calculateMaxPoints();
                $answerItem->save();
            }
        }
    }

    /**
     * Удаляет дублирующие записи для ответов в таблице
     * @param null $start
     * @param null $end
     * @return void
     */
    public function action070324DeleteProccesingDuplicates($start = null, $end = null): void
    {
        $duplicatesIDs = [];
        $subQuery = FoquzPollAnswerProcessing::find()
            ->select('foquz_poll_answer_processings.poll_answer_id')
            ->groupBy(['foquz_poll_answer_processings.poll_answer_id'])
            ->having('COUNT(foquz_poll_answer_processings.poll_answer_id) > 1');
        $records = FoquzPollAnswerProcessing::find()
            ->select(['foquz_poll_answer_processings.id', 'foquz_poll_answer_processings.poll_answer_id'])
            ->where(['NOT', ['foquz_poll_answer_processings.poll_answer_id' => null]])
            ->andWhere(['foquz_poll_answer_processings.poll_answer_id' => $subQuery])
            ->orderBy(['foquz_poll_answer_processings.id' => SORT_ASC]);

        if ($start || $end) {
            $records->leftJoin('foquz_poll_answer',
                'foquz_poll_answer.id = foquz_poll_answer_processings.poll_answer_id');
            if ($start) {
                $records->andWhere(['>=', 'foquz_poll_answer.created_at', date('Y-m-d', strtotime($start))]);
            }
            if ($end) {
                $records->andWhere(['<=', 'foquz_poll_answer.created_at', date('Y-m-d', strtotime($end))]);
            }
        }

        $records = $records->asArray()->all();

        print 'Problematic records: ' . count($records) . ' in ' . count(array_unique(ArrayHelper::getColumn($records,
                'poll_answer_id'))) . ' answers' . PHP_EOL;
        $records = ArrayHelper::index($records, null, 'poll_answer_id');
        foreach ($records as $pollAnswerProcessings) {
            unset($pollAnswerProcessings[0]);
            $duplicatesIDs = ArrayHelper::merge($duplicatesIDs, ArrayHelper::getColumn($pollAnswerProcessings, 'id'));
        }
        print 'Total duplicates: ' . count($duplicatesIDs) . PHP_EOL;
        FoquzPollAnswerProcessing::deleteAll(['id' => $duplicatesIDs]);
        print 'Deleted duplicates: ' . count($duplicatesIDs) . PHP_EOL;
    }

    /**
     * Добавляет функции и процедуры к БД на 02.04.2024
     * @return void
     */
    public function action02042024AddStoredProceduresAndFunctions(): void
    {
        $connection = \Yii::$app->getDb();
        $command = $connection->createCommand("
            DROP FUNCTION IF EXISTS score_to_percents;
            CREATE FUNCTION score_to_percents(score double, min double, max double) RETURNS DOUBLE 
                DETERMINISTIC
            BEGIN
                RETURN ((score - min)/(max - min)*100);
            END
        ");
        $command->execute();

        $command = $connection->createCommand("
            DROP FUNCTION IF EXISTS score_to_five;
             CREATE FUNCTION score_to_five(score double, min double, max double) RETURNS DOUBLE 
                DETERMINISTIC
            BEGIN
                RETURN ((score - min)/(max - min)*4 + 1);
            END
        ");
        $command->execute();

        $command = $connection->createCommand("
            DROP FUNCTION IF EXISTS  avg_score;
            
            CREATE FUNCTION `avg_score`(
                `a` json
            )
            RETURNS double
            LANGUAGE SQL
            DETERMINISTIC
            CONTAINS SQL
            SQL SECURITY DEFINER
            COMMENT ''
            BEGIN
                DECLARE score INT;
                DECLARE i INT DEFAULT 0;
                DECLARE l INT DEFAULT 0;
                DECLARE s BIGINT DEFAULT 0;
                DECLARE c BIGINT DEFAULT 0;
                 IF a IS NOT NULL AND JSON_TYPE(a)='ARRAY' THEN 
                    SET l = JSON_LENGTH(a);
                     WHILE i < l DO
                       SET score = JSON_EXTRACT(a, concat('$[',i,']'));
                       IF score > 0 THEN 
                        SET c = c + 1;
                        SET s = s + score;
                       END IF;
                       SET i = i + 1;
                    END WHILE;
                    IF c > 0 THEN 
                        RETURN (s/c);
                    END IF;
                END IF;
                RETURN (NULL);
            END
        ");
        $command->execute();

        $command = $connection->createCommand("
            DROP PROCEDURE IF EXISTS `update_poll_stat`;
            
            CREATE PROCEDURE `update_poll_stat`(IN `answer_id` int)
            BEGIN
                DECLARE poll_id INT DEFAULT 0;
                DECLARE first_answer_at DATETIME DEFAULT NULL;
                
                SELECT foquz_poll.id, foquz_poll.first_answer_at FROM foquz_poll LEFT JOIN foquz_poll_answer ON foquz_poll_answer.foquz_poll_id = foquz_poll.id WHERE foquz_poll_answer.id = answer_id INTO poll_id, first_answer_at;
                
                IF first_answer_at IS NOT NULL THEN
                    UPDATE foquz_poll SET foquz_poll.last_answer_at = NOW() WHERE foquz_poll.id = poll_id;
                ELSE
                    UPDATE foquz_poll SET foquz_poll.first_answer_at = NOW(), foquz_poll.last_answer_at = NOW() WHERE foquz_poll.id = poll_id;
                END IF;
            END
        ");
        $command->execute();

        $command = $connection->createCommand("
            DROP TRIGGER IF EXISTS `update_poll_stat`;
            
            CREATE TRIGGER `update_poll_stat` AFTER INSERT ON `foquz_poll_answer_item` 
            FOR EACH ROW 
            CALL update_poll_stat(NEW.`foquz_poll_answer_id`);
        ");
        $command->execute();
    }

    /**
     * Заполнить справочник для адвантадже их файла
     * @return void
     */
    public function action08042024AdvantageFillDictionary(): void
    {
        $dictionaryId = 62;
        $fileName = "../advb-c.txt";
        $file = fopen($fileName, "r");
        $i = 0;

        while ($row = fgets($file)) {
            $i++;
            $row = trim($row, "\n\r\t ");
            $row = explode("\t", $row);
            if (count($row) < 5) {
                continue;
            }

            $catName = trim($row[1], "\n\r\t ");
            $subCatName = trim($row[3], "\n\r\t ");
            $itemName = trim($row[4], "\n\r\t ");
            $description = trim(@$row[5], "\n\r\t ");
            print("$i " . $itemName . "\n");
            if (!$catName || !$subCatName || !$itemName) {
                continue;
            }

            $category = DictionaryElement::find()->where([
                "dictionary_id" => 62,
                "type"          => "category",
                "deleted"       => 0,
                "title"         => $catName,
                "parent_id"     => 0
            ])->one();
            if (!$category) {
                $category = new DictionaryElement();
                $category->dictionary_id = $dictionaryId;
                $category->type = "category";
                $category->title = $catName;
                $category->description = "";
                $category->parent_id = 0;
                $position = DictionaryElement::find()->where([
                    "dictionary_id" => 62,
                    "parent_id"     => 0
                ])->max("position");
                if (!$position) {
                    $position = 0;
                }
                $category->position = $position + 1;
                $category->save();
            }

            $subCategory = DictionaryElement::find()->where([
                "dictionary_id" => 62,
                "type"          => "category",
                "deleted"       => 0,
                "title"         => $subCatName,
                "parent_id"     => $category->id
            ])->one();
            if (!$subCategory) {
                $subCategory = new DictionaryElement();
                $subCategory->dictionary_id = $dictionaryId;
                $subCategory->type = "category";
                $subCategory->title = $subCatName;
                $subCategory->description = "";
                $subCategory->parent_id = $category->id;
                $position = DictionaryElement::find()->where([
                    "dictionary_id" => 62,
                    "parent_id"     => $category->id
                ])->max("position");
                if (!$position) {
                    $position = 0;
                }
                $subCategory->position = $position + 1;
                $subCategory->save();
            }

            $item = DictionaryElement::find()->where([
                "dictionary_id" => 62,
                "type"          => "element",
                "deleted"       => 0,
                "title"         => $itemName,
                "parent_id"     => $subCategory->id
            ])->one();
            if (!$item) {
                $item = new DictionaryElement();
                $item->dictionary_id = $dictionaryId;
                $item->type = "element";
                $item->title = $itemName;
                $item->description = $description;
                $item->parent_id = $subCategory->id;
                $position = DictionaryElement::find()->where([
                    "dictionary_id" => 62,
                    "parent_id"     => $subCategory->id
                ])->max("position");
                if (!$position) {
                    $position = 0;
                }
                $item->position = $position + 1;
                if (!$item->save()) {
                    print_r($item->getErrors());
                }
            }

        }
        fclose($file);
    }

    /**
     * добавление филиалов с категориями для Золотого
     *
     * @return void
     */
    public function action11042024AddFilialsWithCategories(): void
    {
        $file = fopen("../advb-c.txt", "r");
        $companyId = 2112;
        $notSave = false;

        $idsCategories = [];
        $idsFilials = [];

        $newCategories = 0;
        $oldCategories = 0;
        $newFilials = 0;
        $oldFilials = 0;
        while ($row = fgets($file)) {
            $row = explode("\t", $row);

            $catName = trim($row[0], "\n\r\t ");
            $name = trim($row[1], "\n\r\t ");
            if (!$name || !$catName) {
                print("Некорректные данные\n");
                continue;
            }
            $category = FilialCategory::find()->where(["company_id" => $companyId, "name" => $catName])->one();
            if (!$category) {
                print("Новая категория $catName\n");
                $newCategories++;
                $category = new FilialCategory;
                $category->company_id = $companyId;
                $category->name = $catName;
            } elseif (!in_array($category->id, $idsCategories)) {
                $oldCategories++;
                //print("Найдена категория $catName\n");
            }
            $category->is_deleted = 0;

            if (!$notSave) {
                if (!$category->save(false)) {
                    print("Категория не сохранена");
                    print_r($category);
                    print_r($category->getErrors());
                }
            }
            if (!in_array($category->id, $idsCategories)) {
                $idsCategories[] = $category->id;
            }

            $filial = Filial::find()->where(["company_id" => $companyId, "name" => $name])->one();
            if (!$filial) {
                $newFilials++;
                print("Новый филиал $catName $name\n");
                $filial = new Filial;
                $filial->company_id = $companyId;
                $filial->name = $name;
            } elseif (!in_array($filial->id, $idsFilials)) {
                $oldFilials++;
                //print("Найден филиал $catName $name\n");
            }
            $filial->is_active = 1;
            $filial->category_id = $category->id;

            if (!$notSave) {
                if (!$filial->save(false)) {
                    print("Филиал не сохранен");
                    print_r($filial);
                    print_r($filial->getErrors());
                }
            }

            if (!in_array($filial->id, $idsFilials)) {
                $idsFilials[] = $filial->id;
            }
        }

        print("\n\nКатегорий создано $newCategories \n");
        print("Категорий найдено $oldCategories \n");
        $notCategories = FilialCategory::find()->where(["company_id" => $companyId])->andWhere([
            "not in",
            "id",
            $idsCategories
        ])->andWhere(['is_deleted' => 0])->select("id")->column();
        print("Категорий будет удалено " . count($notCategories) . "\n");
        if (!$notSave && count($notCategories) > 0) {
            FilialCategory::updateAll(["is_deleted" => 1], ["id" => $notCategories]);
        }
        print("Филиалов создано $newFilials \n");
        print("Филиалов найдено $oldFilials \n");
        $notFilials = Filial::find()->where(["company_id" => $companyId])->andWhere([
            "not in",
            "id",
            $idsFilials
        ])->andWhere(['is_active' => 1])->select("id")->column();
        print("Филиалов будет удалено " . count($notFilials) . "\n");
        if (!$notSave && count($notFilials) > 0) {
            Filial::updateAll(["is_active" => 0], ["id" => $notFilials]);
        }
        fclose($file);
    }

    /**
     * Перенос удаленных записей в foquz_poll_answer_deleted
     * @param null $start
     * @param null $end
     * @return void
     */
    public function action06052024SaveAnswers($start = null, $end = null): void
    {
        $answers = FoquzPollAnswer::find()
            ->select('id')
            ->where(['is_deleted' => 1]);

        if ($start) {
            $answers->andWhere(['>=', 'created_at', date('Y-m-d', strtotime($start))]);
        }

        if ($end) {
            $answers->andWhere(['<=', 'created_at', date('Y-d', strtotime($end))]);
        }

        foreach ($answers->batch(1000) as $answers) {
            $IDs = ArrayHelper::getColumn($answers, 'id');
            FoquzPollAnswer::saveDeletedAnswers($IDs);

            FoquzPollAnswer::deleteAll(['id' => $IDs]);

            print 'Deleted: ' . count($IDs) . PHP_EOL;
        }
    }

    /**
     * Удаление колонок из таблицы foquz_poll_answer
     * @return void
     * @throws \yii\db\Exception
     */
    public function action06052024DeleteAnswerColumns()
    {
        $connection = \Yii::$app->getDb();
        $command = $connection->createCommand("
            ALTER TABLE `foquz_poll_answer` DROP FOREIGN KEY `fk-foquz_poll_answer-deleted_by`;

            ALTER TABLE `foquz_poll_answer` DROP INDEX `idx-foquz_poll_answer-deleted_by`;
            ALTER TABLE `foquz_poll_answer` DROP INDEX `idx-foquz_poll_answer-is_deleted`;

            ALTER TABLE `foquz_poll_answer` DROP COLUMN `deleted_by`;
            ALTER TABLE `foquz_poll_answer` DROP COLUMN `deleted_at`;
            ALTER TABLE `foquz_poll_answer` DROP COLUMN `is_deleted`;
        ");
        $command->execute();

        print 'Columns deleted' . PHP_EOL;
    }

    public function action29052024FixScale()
    {
        $count = 0;
        $questions = FoquzQuestion::find()
            ->leftJoin('foquz_question_scale_rating_settings',
                'foquz_question_scale_rating_settings.foquz_question_id = foquz_question.id')
            ->where(['main_question_type' => FoquzQuestion::TYPE_SCALE, 'is_deleted' => 0, 'is_tmp' => 0])
            ->andWhere(['foquz_question_scale_rating_settings.id' => null]);

        foreach ($questions->batch(1000) as $questions) {
            $insert = [];
            /** @var FoquzQuestion $question */
            foreach ($questions as $question) {
                $insert[] = [
                    $question->id,
                    0,
                    100,
                    10
                ];
                $count++;
            }
            \Yii::$app->db->createCommand()
                ->batchInsert('foquz_question_scale_rating_settings', ['foquz_question_id', 'start', 'end', 'step'],
                    $insert)
                ->execute();
        }
        print 'Added: ' . $count . ' scale settings' . PHP_EOL;
    }

    public function action13052024SortVariants()
    {
        $questionsWithNothingVariant = FoquzQuestionDetail::find()
            ->select('DISTINCT(foquz_question_detail.foquz_question_id)')
            ->leftJoin('foquz_question', 'foquz_question.id = foquz_question_detail.foquz_question_id')
            ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_question.poll_id')
            ->where(['foquz_question_detail.type' => FoquzQuestionDetail::TYPE_NOTHING])
            ->andWhere([
                'foquz_question_detail.is_deleted' => 0,
                'foquz_question.is_deleted'        => 0,
                'foquz_poll.deleted'               => 0
            ]);

        $variants = FoquzQuestionDetail::find()
            ->andWhere(['foquz_question_detail.foquz_question_id' => $questionsWithNothingVariant])
            ->andWhere(['foquz_question_detail.is_deleted' => 0])
            ->orderBy(['type' => SORT_DESC, 'position' => SORT_ASC])
            ->all();

        $variants = ArrayHelper::index($variants, null, 'foquz_question_id');

        foreach ($variants as $questionVariants) {
            $position = 1;
            /** @var FoquzQuestionDetail $questionVariant */
            foreach ($questionVariants as $questionVariant) {
                $questionVariant->position = $position;
                $questionVariant->save();
                $position++;
            }
        }

        print 'Variants sorted: ' . count($variants) . PHP_EOL;

        $recipientQuestionsWithNothingVariant = RecipientQuestionDetail::find()
            ->select('DISTINCT(recipient_question_detail.recipient_id)')
            ->leftJoin('foquz_question', 'foquz_question.id = recipient_question_detail.recipient_id')
            ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_question.poll_id')
            ->where(['recipient_question_detail.type' => FoquzQuestionDetail::TYPE_NOTHING])
            ->andWhere(['foquz_question.is_deleted' => 0, 'foquz_poll.deleted' => 0]);

        $recipientVariants = RecipientQuestionDetail::find()
            ->andWhere(['recipient_question_detail.recipient_id' => $recipientQuestionsWithNothingVariant])
            ->orderBy(['type' => SORT_DESC, 'position' => SORT_ASC])
            ->all();

        $recipientVariants = ArrayHelper::index($recipientVariants, null, 'recipient_id');

        foreach ($recipientVariants as $recipientQuestionVariants) {
            $position = 1;
            /** @var RecipientQuestionDetail $recipientQuestionVariant */
            foreach ($recipientQuestionVariants as $recipientQuestionVariant) {
                $recipientQuestionVariant->position = $position;
                $recipientQuestionVariant->save();
                $position++;
            }
        }

        print 'Recipient variants sorted: ' . count($recipientVariants) . PHP_EOL;
    }


    public function actionRemoveAnswers($companyId, $date, $step = 1, $confirm = 1)
    {
        if ($date > date("Y-m-d H:i:s", strtotime("today") - 60 * 60 * 24 * 120)) {
            print "Нельзя удалять анкеты младше 4 месяцев" . PHP_EOL;
            return false;
        }
        $company = Company::findOne($companyId);
        if (!$company) {
            print "Компания не найдена" . PHP_EOL;
            return false;
        }

        print "Компания $company->alias" . PHP_EOL . PHP_EOL;
        $pollIds = FoquzPoll::find()->where(["company_id" => $companyId])->select(["id"])->column();

        $data = FoquzPollAnswer::find()->where(["foquz_poll_id" => $pollIds])
            ->groupBy("YEAR(updated_at), MONTH(updated_at)")
            ->orderBy("YEAR(updated_at) asc, MONTH(updated_at) asc")
            ->select("YEAR(updated_at), MONTH(updated_at), count(*)")
            ->asArray()
            ->all();
        foreach ($data as $row) {
            print(implode("\t", $row) . PHP_EOL);
        }
        print(PHP_EOL . PHP_EOL);

        $rows = FoquzPollAnswer::find()->where(["foquz_poll_id" => $pollIds, "status" => FoquzPollAnswer::STATUS_NEW])
            ->andWhere(["<", "updated_at", $date])
            ->count();
        print "Всего строк будет удалено $rows" . PHP_EOL;

        $rDate = FoquzPollAnswer::find()->where(["foquz_poll_id" => $pollIds, "status" => FoquzPollAnswer::STATUS_NEW])
            ->andWhere(["<", "updated_at", $date])
            ->select("updated_at")
            ->orderBy("updated_at")
            ->limit(1)
            ->scalar();

        if (!$rDate) {
            return false;
        }

        $step = 60 * 60 * 24 * $step;
        $rDate = strtotime($rDate);
        $rDate += $step;
        $nextDate = date("Y-m-d", $rDate);
        while ($nextDate < $date) {
            print("$nextDate\t");
            $filters = [
                "and",
                ["foquz_poll_id" => $pollIds],
                ["status" => FoquzPollAnswer::STATUS_NEW],
                ["<", "updated_at", $nextDate]
            ];
            if ($confirm) {
                $rows = FoquzPollAnswer::find()->where($filters)->count();
                print("будет удалено строк $rows" . PHP_EOL);
                readline();
            }
            $deleted = FoquzPollAnswer::deleteAll($filters);
            print("удалено строк $deleted");
            $rDate += $step;
            $nextDate = date("Y-m-d", $rDate);
            print(PHP_EOL);
        }
    }

    public function actionRemoveFeedback($step = 1, $confirm = 1)
    {
        $data = Feedback::find()->where(['view' => 'poll'])
            ->select(['c' => 'count(*)', 'mi' => 'min(id)', 'ma' => 'max(id)'])
            ->asArray()
            ->one();

        printf('всего %s  min = %s  max = %s' . PHP_EOL, $data['c'], $data['mi'], $data['ma']);
        $step = $step * 10000;
        $steps = ceil(($data['ma'] - $data['mi']) / $step);
        printf('Шагов %d' . PHP_EOL, $steps);


        $s = 0;
        do {
            $s++;
            $limit = $data['mi'] + $s * $step;
            printf('%d/%d ', $s, $steps);

            $rows = Feedback::deleteAll([
                'and',
                ['view' => 'poll'],
                ['<=', 'id', $limit]
            ]);
            printf('Удалено строк %s', $rows);

            print(PHP_EOL);
        } while ($limit <= $data['ma']);

        exit;

        $step = 60 * 60 * 24 * $step;
        $rDate = strtotime($rDate);
        $rDate += $step;
        $nextDate = date("Y-m-d", $rDate);
        while ($nextDate < $date) {
            print("$nextDate\t");
            $filters = [
                "and",
                ["foquz_poll_id" => $pollIds],
                ["status" => FoquzPollAnswer::STATUS_NEW],
                ["<", "updated_at", $nextDate]
            ];
            if ($confirm) {
                $rows = FoquzPollAnswer::find()->where($filters)->count();
                print("будет удалено строк $rows" . PHP_EOL);
                readline();
            }
            $deleted = FoquzPollAnswer::deleteAll($filters);
            print("удалено строк $deleted");
            $rDate += $step;
            $nextDate = date("Y-m-d", $rDate);
            print(PHP_EOL);
        }
    }


    public function action20240904SetAnswerLimit($pollID = null)
    {
        print '--- Start polls update ---' . PHP_EOL;
        $pollsQuery = FoquzPoll::find()->where(['deleted' => 0])->andFilterWhere(['id' => $pollID]);
        $count = $pollsQuery->count();
        $i = 0;

        print 'Polls count: ' . $count . PHP_EOL;

        foreach ($pollsQuery->batch(5000) as $polls) {
            $answerLimit = [];
            /** @var FoquzPoll $poll */
            foreach ($polls as $poll) {
                $answerLimit[(int)$poll->isAnswerLimit] = $poll->id;
            }
            if (!empty($answerLimit[0])) {
                FoquzPoll::updateAll(['is_answer_limited' => 0], ['id' => $answerLimit[0]]);
            }
            if (!empty($answerLimit[1])) {
                FoquzPoll::updateAll(['is_answer_limited' => 1], ['id' => $answerLimit[1]]);
            }
            $i += count($polls);
            print 'Polls updated: ' . $i . ' (' . round($i / $count * 100) . '%)' . PHP_EOL;
        }
        print '--- Finish polls update ---' . PHP_EOL;

        print '--- Start companies update ---' . PHP_EOL;

        $companiesQuery = Company::find()->where(['deleted' => 0]);
        $count = $companiesQuery->count();
        $i = 0;

        print 'Companies count: ' . $count . PHP_EOL;

        foreach ($companiesQuery->batch(5000) as $companies) {
            $answerLimit = [];
            /** @var Company $company */
            foreach ($companies as $company) {
                $answerLimit[(int)$company->isAnswersLimitsOver()] = $company->id;
            }
            if (!empty($answerLimit[0])) {
                Company::updateAll(['is_answer_limited' => 0], ['id' => $answerLimit[0]]);
            }
            if (!empty($answerLimit[1])) {
                Company::updateAll(['is_answer_limited' => 1], ['id' => $answerLimit[1]]);
            }
            $i += count($companies);
            print 'Companies updated: ' . $i . ' (' . round($i / $count * 100) . '%)' . PHP_EOL;
        }
        print '--- Finish companies update ---' . PHP_EOL;
    }

    public function action20092024FixSetAnsersHashId()
    {
        $count = 0;
        $answers = FoquzPollAnswer::find();

        foreach ($answers->batch(10000) as $answers) {
            $update = [];

            /** @var FoquzPollAnswer $answers */
            foreach ($answers as $answer) {
                \Yii::$app->db->createCommand()
                    ->update('foquz_poll_answer', ['hash_id' => $answer->buildHash()], 'id=' . $answer->id)
                    ->execute();
                $count++;
            }

        }
        print 'Updated: ' . $count . ' answers' . PHP_EOL;
    }

    public function action20241025FixMaxPoint()
    {
        $ids = "9970616,10805298,10759399,10759533,10760378,10760403,10760404,10760409,10760433,10795639,10795676,10760563,10760612,10760691,10756360,10756383,10756398,10756407,10756416,10756417,10756449,10756495,10756559,10756585,10756690,10756747,10756366,10754127,10754128,10754200,10754311,10754318,10757823,10757883,10757923,10757941,10757963,10758031,10758060,10758087,10758112,10758119,10758145,10854331,10854337,10855645,10821366,10821058,10821072,10821078,10821132,10821208,10737441,10737445,10737451,10737452,10805051,10805026,10805568,10805778,10805914,10805917,10157482,10648620,10648626,10648764,10637672,10588113,10597741,10597794,10597867,10597920,10598184,10645576,10645664,10646234,10646252,10700375,10701177,10701236,10701389,10718598,10718632,10718707,10718720,10718610,10718734,10646422,10629879,10659562,10675932,10692264,10692268,10726499,10640139,10640162,10640225,10646283,10645901,10646319,10646324,10646328,10646339,10646340,10646359,10646404,10646603,10727166,10726986,10726861,10723345,10664082,10664102,10664104,10664113,10664119,10664121,10664129,10664140,10664146,10664155,10664165,10664181,10664195,10630971,10615335,10615401,10714223,10705387,10701719,10701308,10701305,10633424,10633425,10633427,10633429,10633431,10645392,10645393,10645396,10645404,10645406,10645419,10645426,10645421,10657521,10657617,10657659,10657717,10657744,10657760,10658057,10632850,10633101,10633106,10633125,10633126,10633134,10633188,10633300,10690392,10689262,10685087,10683313,10682702,10680279,10680223,10680151,10680127,10676410,10675458,10675358,10669475,10664188,10662664,10639862,10645744,10645386,10645321,10645243,10645158,10645080,10636409,10636280,10636215,10631991,10631853,10631809,10631787,10631640,10631614,10631608,10631600,10631588,10631456,10631419,10631418,10628697,10616435,10616446,10623466,10607780,10607794,10607809,10290606,10290607,10583596,10583724,10597739,10597871,10597981,10598011,10472330,10472486,10284834,10284840,10284896,10264105,10264109,10215511,10215521,10215531,10215532,10215548,10217094,10217100,10217101,10217102,10217103,10217113,10217116,10217127,10217128,10217130,10215518,10285139,10285155,10285162,10284753,10284765,10284784,10284792,10284799,10284830,10264740,10264744,10300277,10300348,10300280,10285050,10285109,10200140,10200394,10213850,10214086,10112028,10112150,10225715,10225726,10226027,10203423,10284976,10264202,10284902,10284945,10265104,10309431,10309733,10468083,10468135,10468157,10468169,10468181,10468182,10468185,10468193,10468148,10472083,10472164,10472174,10481792,10481797,10481848,10481850,10481861,10482009,10322815,10322827,10322874,10322885,10322927,10322945,10311230,10323904,10323956,10323979,10317634,10317643,10317644,10317653,10324148,10325041,10326078,10326138,10326198,10326209,10326241,10326249,10326266,10326275,10322107,10322156,10322170,10322184,10322217,10322461,10322472,10258557,10258697,10258789,10258903,10259186,10259189,10259283,10259286,10259343,10258943,10265244,10322074,10322103,10322144,10322214,10314728,10314767,10314771,10314788,10281074,10281103,10281217,10281243,10266368,10266262,10266267,10263045,10258906,10258945,10273053,10273124,10273166,10273255,10154929,10187841,10177068,10177156,10177246,10142936,10150570,10129346,10184038,10184133,10184138,10184153,10141167,10141017,10154208,10154786,10154793,10226289,10226295,10226296,10226308,10226318,10226322,10226334,10226290,10216290,10216312,10216335,10203662,10203775,10211174,10211333,10211581,10211785,10201162,10199634,10199637,10154219,10151166,10151225,10151229,10151180,10213471,10213491,10213661,10213766,10213846,10167582,10202756,10202768,10202769,10202775,10202794,10202840,10202880,10235345,10235300,10235307,10235317,10235320";
        $ids = explode(",", $ids);
        foreach ($ids as $id) {
            $f = FoquzPollAnswerItem::findOne($id);

            $oldMaxPoints = $f->max_points;
            $f->max_points = $f->calculateMaxPoints();
            $f->save();
            //print($oldMaxPoints."\t{$f->answer->max_points}\t" . $f->max_points."\n");

        }
        //print_r($ids);
        ///exit;
    }

    public function actionTest()
    {
        $to = FoquzContact::preformatPhone('79268444037');
        $sender = Channel::findOne(56566)->getGlobalSetting()->one()->attributes;
        $messenger = new SMSService();
        $messenger->globalSetting = $sender;
        $result = $messenger->send($to, 'test');
        print_r($result);
    }

    public function actionOldLogs()
    {
        $id = 2169344;
        $page = 40000;
        $logs = Log::find()->orderBy('id desc')
            ->where(['<', 'id', $id])->limit($page)->all();

        $users = [];

        while (count($logs) > 0) {
            foreach ($logs as $log) {
                $id = $log->id;
                print($id . '  ' . $log->date . PHP_EOL);
                $data = $log->attributes;
                // $data[$data['table']]['changes'] = $data['changes'];
                //unset($data['changes']);
                if (!empty($data['user_id'] && !isset($users[$data['user_id']]))) {
                    $users[$data['user_id']] = User::findOne($data['user_id'])->company->alias;
                }
                $data['alias'] = $users[$data['user_id']] ?? null;
                //print_r($data);exit;
                \Yii::info($data, 'object_changes_log_old');
            }
            sleep(20);
            $logs = Log::find()->orderBy('id desc')
                ->where(['<', 'id', $id])->limit($page)->all();

        }
    }

    public function actionLog()
    {
        $i = 0;
        $file = fopen('runtime/logs/test.log', 'r');
        while ($row = fgets($file)) {
            preg_match_all('@\[(.*)\].* (\d+) \d+ @', $row, $arr);
            $date = date('2024-12-18    H:i:s', strtotime($arr[1][0]) - 60 * 60 * 3);
            $date = date('d/M/Y:H:i:s P', strtotime($date));
            $data = [
                'timestamp' => $date,
                'code'      => $arr[2][0],
                'uri'       => '/example',
                'instance'  => 'example',
                'company'   => 'example.com'
            ];
            print(json_encode($data) . PHP_EOL);

        }
        fclose($file);
    }

    public function actionCustomQuotaDelete(int $pollId)
    {
        CustomQuotaService::clearQuota($pollId, \Yii::$app->redis);
    }

    public function actionCustomQuotaPrint()
    {
        $data = CustomQuotaService::getInfoAllQuota(\Yii::$app->redis);
        foreach ($data as $pollID => $answers) {
            $poll = FoquzPoll::findOne($pollID);
            printf("%s %d %s" . PHP_EOL, $poll->company->alias, $poll->id, $poll->name);
            foreach ($answers as $detailId => $counter) {
                $detail = FoquzQuestionDetail::findOne($detailId);
                printf('>> %d %s %d %s %d' . PHP_EOL,
                    $detail->foquzQuestion->id, $detail->foquzQuestion->description,
                    $detail->id, $detail->question, $counter);
            }
            print (PHP_EOL);
        }
    }

    /**
     * Перенос аватарок пользователей в web/uploads/avatars
     * @return false|void
     */
    public function actionTransferAvatars()
    {
        $source = \Yii::getAlias('@app') . '/web/avatars/';
        $destination = \Yii::getAlias('@app') . '/web/uploads/avatars/';
        printf("---Transfer user`s avatars from %s to %s---" . PHP_EOL, $source, $destination);
        try {

            if (!is_dir($destination)) {
                if (!FileHelper::createDirectory($destination, 0777)) {
                    throw new \Exception("Failed to create directory $destination");
                }
            }

            foreach (glob($source . '*') as $file) {
                if (is_dir($file)) {
                    $dirname = substr($file, strrpos($file, '/') + 1);
                    FileHelper::copyDirectory($file, $destination . $dirname);
                    printf("The directory %s has been copied" . PHP_EOL, $dirname);

                }
            }

        } catch (\Throwable $e) {
            print($e->getMessage() . PHP_EOL);
            return false;
        }
    }

    /**
     * Перенос кастомных смайлов в web/uploads/smiles (с обновлением ссылок в бд)
     * @return false|void
     */
    public function actionTransferSmiles()
    {
        $source = \Yii::getAlias('@app') . '/web/img/smiles/';
        $destination = \Yii::getAlias('@app') . '/web/uploads/smiles/';

        printf("---Transfer custom smiles from %s to %s---" . PHP_EOL, $source, $destination);

        try {

            if (!is_dir($destination)) {
                if (!FileHelper::createDirectory($destination, 0777)) {
                    throw new \Exception("Failed to create directory $destination");
                }
            }

            foreach (glob($source . '*') as $file) {
                if (!is_dir($file)) {
                    continue;
                }

                $dirname = substr($file, strrpos($file, '/') + 1);
                if (str_starts_with($dirname, 'custom')) {
                    FileHelper::copyDirectory($file, $destination . $dirname);
                    printf("The directory %s has been copied" . PHP_EOL, $dirname);
                }
            }

            // меняем ссылки на смайлы в бд (идемпотентно)
            $questionSmiles = FoquzQuestionSmile::find()->where(['like', 'smile_url', '/smiles/custom']);
            foreach ($questionSmiles->each() as $questionSmile) {
                if (str_starts_with($questionSmile->smile_url, '/img/smiles/custom')) {
                    $questionSmile->smile_url = str_replace('/img/smiles/', '/uploads/smiles/', $questionSmile->smile_url);
                    if ($questionSmile->save()) {
                        printf("smile_url changed to %s" . PHP_EOL, $questionSmile->smile_url);
                    }
                }
            }

        } catch (\Throwable $e) {
            print($e->getMessage() . PHP_EOL);
            return false;
        }
    }
}
