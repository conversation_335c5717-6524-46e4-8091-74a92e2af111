<?php

namespace app\commands;

use app\modules\foquz\models\CompanyFeedback;
use app\modules\foquz\models\Feedback;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\requests_project\Answer;
use RuntimeException;
use yii\console\Controller;
use yii\db\ActiveRecord;

class FillFeedbackController extends Controller
{
    private $start;
    private $end;

    /**
     * Заполнение общей таблицы ответов
     *
     * @param string $start Начало периода (формат ДД.ММ.ГГГГ)
     * @param string|null $end Конец периода (формат ДД.ММ.ГГГГ)
     * @param bool $truncate Очистить таблицу
     * @return void
     */
    public function actionIndex(string $start, string $end = null, bool $truncate = false): void
    {
        $time = microtime(true);

        $pattern = '/[0-3]\d\.[01]\d\.[12]\d{3}/';
        if (!preg_match($pattern, $start) || ($end && !preg_match($pattern, $end))) {
            throw new RuntimeException('Неверный формат даты');
        }
        $this->start = date('Y-m-d H:i:s', strtotime($start . ' 00:00:00'));
        $this->end = $end ? date('Y-m-d H:i:s', strtotime($end . ' 23:59:59')) : date('Y-m-d H:i:s', strtotime('+1 day'));

        if ($truncate) {
            $this->clearFeedbackId(new FoquzPollAnswer());
            $this->clearFeedbackId(new Answer());
            $this->clearFeedbackId(new CompanyFeedback());
            Feedback::deleteAll();
        }
        $this->handleEntity(new FoquzPollAnswer());
        $this->handleEntity(new Answer());
        $this->handleEntity(new CompanyFeedback());

        echo 'Время выполнения скрипта: ' . round(microtime(true) - $time) . ' сек.' . PHP_EOL;
    }

    /**
     * @param ActiveRecord $class
     * @return void
     */
    private function clearFeedbackId(ActiveRecord $class): void
    {
        foreach ($class::find()->where(['IS NOT', 'feedback_id', null])->each() as $answer) {
            $answer->feedback_id = null;
            $answer->detachBehaviors();
            $answer->save(false);
        }
    }

    /**
     * @param ActiveRecord $className
     * @return void
     */
    private function handleEntity(ActiveRecord $className): void
    {
        $query = $className::find()->where([
            'AND',
            ['>=', 'created_at', $this->start],
            ['<=', 'created_at', $this->end],
            ['feedback_id' => null],
        ]);

        print($query->count()."\n");
        $i=0;
        foreach ($query->batch() as $batch) {
            /** @var FoquzPollAnswer|Answer|CompanyFeedback $answer */
            foreach ($batch as $answer) {
                $i++;
                if ($i%1000==0) print($i."\n");
                $feedback = new Feedback();
                switch (get_class($answer)) {
                    case FoquzPollAnswer::class:
                        $feedback->view = 'poll';
                        //if (in_array($answer->status, [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE])) {
                            $feedback->passed_at = $answer->updated_at;
                        //}
                        break;
                    case Answer::class:
                        $feedback->view = 'request';
                        $feedback->passed_at = $answer->updated_at;
                        break;
                    case CompanyFeedback::class:
                        $feedback->view = 'feedback';
                        $feedback->passed_at = $answer->created_at;
                }
                $feedback->processing_id = $answer->processing->id ?? null;
                $feedback->save();
                $answer->feedback_id = $feedback->id;
                $answer->detachBehaviors();
                $answer->save(false);
            }
        }
    }
}