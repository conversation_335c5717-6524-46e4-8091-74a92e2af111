<?php

namespace app\commands;

use app\modules\foquz\models\DiscountPoolCode;
use yii\console\Controller;
use yii\console\ExitCode;

class DiscountPoolCodeController extends Controller
{
    public function actionImportCsv($user_id, $pool_id, $file, $stat_id)
    {
        $res = DiscountPoolCode::importCSV($pool_id, $file, $stat_id);
        if ($res['status'] == 'ok' || $res['status'] == 'done') {
            echo "Обработано: " . $res['stat']['total'] ." записей" . "\n";
            echo "Внесено: " . $res['stat']['inserted'] ." записей" . "\n";
            echo "Обновлено: " . $res['stat']['updated'] ." записей" . "\n";
            echo "Ошибочных: " . $res['stat']['failed'] ." записей" . "\n";
            return ExitCode::OK;
        } else {
            if (isset($res['errors'])) echo implode('\n', $res['errors']) . "\n";
            else var_dump($res);
            return ExitCode::UNSPECIFIED_ERROR;
        }
    }
}