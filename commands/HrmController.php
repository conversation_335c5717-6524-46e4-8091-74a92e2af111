<?php


namespace app\commands;


use app\modules\foquz\models\HrmIntegration;
use yii\console\Controller;

class HrmController extends Controller
{
    public function actionGetEmployees()
    {
        $hrmIntegrations = HrmIntegration::find()->all();
        foreach($hrmIntegrations as $integration) {
            try {
                $integration->getEmployees();
            } catch (\Exception $e) {
                \Yii::error($e->getMessage(), __METHOD__);
            }
        }
    }

    public function actionGetFines()
    {
        $hrmIntegrations = HrmIntegration::find()->all();
        foreach($hrmIntegrations as $integration) {
            try {
                $integration->getFines();
            } catch (\Exception $e) {
                \Yii::error($e->getMessage(), __METHOD__);
            }
            try {
                $integration->getFoulTypes();
            } catch (\Exception $e) {
                \Yii::error($e->getMessage(), __METHOD__);
            }
        }
    }
}