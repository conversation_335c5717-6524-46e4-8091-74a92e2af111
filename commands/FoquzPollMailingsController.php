<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use PhpParser\Node\Expr\Print_;
use Yii;
use yii\base\Event;
use yii\db\ActiveRecord;
use yii\console\Controller;
use yii\console\ExitCode;

use app\modules\foquz\models\FoquzPollMailingListSearch;
use app\modules\foquz\models\FoquzPollMailingListContact;
use app\modules\foquz\models\FoquzPollMailingList;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class FoquzPollMailingsController extends Controller
{
    /**
     * This command echoes what you have entered as the message.
     * @param string $message the message to be echoed.
     */
    public function actionAddContacts($user_id, $company_id, $stat_id)
    {
        $res = FoquzPollMailingList::addContacts($company_id, $stat_id, $user_id);

        if ($res['status'] == 'ok' || $res['status'] == 'done') {
            echo "Обработано: " . $res['stat']['total'] ." записей" . "\n";
            echo "Внесено: " . $res['stat']['inserted'] ." записей" . "\n";
            echo "Повторов: " . $res['stat']['duplicated'] ." записей" . "\n";
            return ExitCode::OK;
        } else {
            if (isset($res['errors'])) echo implode('\n', $res['errors']) . "\n";
            else var_dump($res);
            return ExitCode::UNSPECIFIED_ERROR;
        }

    }

    public function actionLaunch()
    {
        $listsToLaunch = FoquzPollMailingList::find()
            ->where(['<=', 'launched_at', date('Y-m-d H:i:s')])
            ->andWhere(['status' => FoquzPollMailingList::STATUS_NEW])
            ->all();

        foreach($listsToLaunch as $list) {
            $list->status = FoquzPollMailingList::STATUS_STARTED;
            $list->save();
        }
    }
}
