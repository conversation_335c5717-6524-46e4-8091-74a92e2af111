<?php

namespace app\commands;

use app\models\Filial;
use app\models\Word;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionFormField;
use app\modules\foquz\models\WordAnswer;
use Sheronov\PhpMyStem\Exceptions\MyStemException;
use She<PERSON>ov\PhpMyStem\Exceptions\MyStemNotFoundException;
use She<PERSON>ov\PhpMyStem\MyStem;
use Yii;
use yii\web\NotFoundHttpException;

class WordCloudController extends \yii\console\Controller
{
    /**
     * Поиск уникальных слов в ответах респондентов
     *
     * @param string $start Начало периода 
     * @param string $end Конец периода
     * @return void
     * @throws MyStemException
     * @throws MyStemNotFoundException|NotFoundHttpException
     */
    public function actionCollectPeriod($start, $end)
    {
        $key = $wordCounter = $wordAnswerCounter = 0;
        $answers = FoquzPollAnswer::find()
            ->where(['>=', 'updated_at', date('Y-m-d H:i', strtotime($start))])
            ->andWhere(['<=', 'updated_at', date('Y-m-d H:i', strtotime($end))])
            ->andWhere(['status'=>['done', 'in-progress']])
            ->orderBy('updated_at desc')
            ->all();
        if (!$answers) {
            exit("Для указанного количества минут анкет не найдено\n");
        }
        $answeredCount = 0;
        foreach ($answers as $key => $answer) {
            print($answer->foquz_poll_id."\t".$answer->updated_at."\n");
            $answeredCount = WordAnswer::find()->where(['answer_id' => $answer->id])->count();
            WordAnswer::deleteAll(['answer_id' => $answer->id]);
            foreach (FoquzPollAnswerItem::findAll(['foquz_poll_answer_id' => $answer->id]) as $answerItem) {
                $text = $answerItem->answer;
                $question = FoquzQuestion::findOne($answerItem->foquz_question_id);
                if (!$question) {
                    throw new NotFoundHttpException('Вопрос опроса не найден');
                }
                if (in_array($question->main_question_type, [
                    FoquzQuestion::TYPE_PRIORITY,
                    FoquzQuestion::TYPE_DATE,
                    FoquzQuestion::TYPE_ADDRESS,
                    FoquzQuestion::TYPE_INTERMEDIATE_BLOCK,
                ], true)) {
                    continue;
                }
                if ($question->main_question_type === FoquzQuestion::TYPE_FORM) {
                    $formDetails = json_decode($answerItem->answer);
                    foreach ($formDetails as $id => $detail) {
                        $formDetail = FoquzQuestionFormField::findOne($id);
                        if ($formDetail) {
                            if ($detail && !$formDetail->mask_type && !$formDetail->variants_type) {
                                $text .= " $detail";
                            }
                            if (!$formDetail->mask_type && $formDetail->variants_type) {
                                foreach (explode('\n', $detail) as $string) {
                                    if ($string) {
                                        $text .= " $string";
                                    }
                                }
                            }
                        }
                    }
                }
                $details = is_array($answerItem->detail_item) ? $answerItem->detail_item : json_decode($answerItem->detail_item);
                if ($details) {
                    foreach ($details as $item) {
                        if ($item == '-1') {
                            $donor = FoquzQuestion::findOne($question->donor);
                            while ($donor && $donor->donor && $donor->donor != $donor->id) {
                                $donor = FoquzQuestion::findOne($donor->donor);
                            }
                            $donorAnswerItem = FoquzPollAnswerItem::findOne([
                                'foquz_poll_answer_id' => $answer->id,
                                'foquz_question_id' => $donor->id,
                            ]);
                            if ($donorAnswerItem && $donorAnswerItem->self_variant) {
                                $text .= " $donorAnswerItem->self_variant";
                            }
                        }
                        if ($question->main_question_type === FoquzQuestion::TYPE_FILIAL) {
                            if ($detail = Filial::findOne($item)) {
                                $statistic = $this->saveWord($detail->name, $answer, $answerItem->foquz_question_id);
                                $wordCounter += $statistic['wordCounter'];
                                $wordAnswerCounter += $statistic['wordAnswerCounter'];
                            }
                        } else if (is_numeric($item) && $detail = FoquzQuestionDetail::findOne($item)) {
                            $statistic = $this->saveWord($detail->question, $answer, $answerItem->foquz_question_id);
                            $wordCounter += $statistic['wordCounter'];
                            $wordAnswerCounter += $statistic['wordAnswerCounter'];
                        }
                    }
                }
                if ($answerItem->self_variant) {
                    $text .= " $answerItem->self_variant";
                }
                foreach (MyStem::lemmatization(str_replace("\n", ' ', $text)) as $variant) {
                    if ((in_array($variant['part_letter'], ['A', 'ADV', 'S', 'V', 'NUM']))) {
                        $statistic = $this->saveWord($variant['lemma'], $answer, $answerItem->foquz_question_id);
                        $wordCounter += $statistic['wordCounter'];
                        $wordAnswerCounter += $statistic['wordAnswerCounter'];
                    }
                }
            }
        }
        ++$key;
        $wordAnswerCounter -= $answeredCount;
        echo "Обработано анкет: $key\nДобавлено новых слов: $wordCounter\nДобавлено новых связок: $wordAnswerCounter\n";
    }

    /**
     * Поиск уникальных слов в ответах респондентов
     *
     * @param int $minutes Количество минут от текущего времени, в течении которых были заполнены анкеты
     * @return void
     * @throws MyStemException
     * @throws MyStemNotFoundException|NotFoundHttpException
     */
    public function actionCollect(int $minutes=90)
    {
        date_default_timezone_set('Europe/Moscow');
        $this->actionCollectPeriod(date('Y-m-d H:i', strtotime("- $minutes minutes")), date('Y-m-d H:i', strtotime("+ $minutes minutes")));
            //->where(['>=', 'updated_at', ])
    }

    /**
     * Очистить таблицу собранных слов
     *
     * @return void
     */
    public function actionTruncate()
    {
        $this->prompt('Эта команда сотрет все собранные слова. Вы уверены?', ['required' => true, 'validator' => function ($input, &$error) {
            if (in_array($input, ['ДА', 'да', 'Да', 'Д', 'д', 'YES', 'yes', 'Yes', 'Y', 'y'])) {
                Word::deleteAll();
                Yii::$app->db->createCommand(<<<SQL
ALTER TABLE word AUTO_INCREMENT = 1;
ALTER TABLE word_answer AUTO_INCREMENT = 1;
SQL
)->execute();
                echo 'Таблица слов очищена' . PHP_EOL;
            } elseif (in_array($input, ['НЕТ', 'нет', 'Нет', 'Н', 'н', 'NO', 'no', 'No', 'N', 'n'])) {
                echo 'Таблица слов осталась без изменений' . PHP_EOL;
            } else {
                $error = 'Введено неверное значение';
                return false;
            }
            return true;
        }]);
    }

    private function saveWord(string $lemma, FoquzPollAnswer $answer, int $questionId): array
    {
        $wordCounter = $wordAnswerCounter = 0;
        $word = Word::findOne(['word' => $lemma]);
        if (!$word) {
            $word = new Word(['word' => $lemma]);
            $word->save();
            $wordCounter++;
        }
        $wordAnswer = WordAnswer::findOne([
            'word_id' => $word->id,
            'answer_id' => $answer->id,
            'question_id' => $questionId,
        ]);
        if (!$wordAnswer) {
            $wordAnswer = new WordAnswer([
                'word_id' => $word->id,
                'answer_id' => $answer->id,
                'question_id' => $questionId,
            ]);
            $wordAnswerCounter++;
        }
        $wordAnswer->number = !$wordAnswer->number ? 1 : ++$wordAnswer->number;
        $wordAnswer->save();

        return ['wordCounter' => $wordCounter, 'wordAnswerCounter' => $wordAnswerCounter];
    }
}
