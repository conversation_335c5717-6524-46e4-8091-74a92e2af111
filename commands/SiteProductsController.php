<?php

namespace app\commands;

use app\modules\foquz\models\site\SiteCategory;
use app\modules\foquz\models\site\SiteCurrency;
use app\modules\foquz\models\site\SiteProduct;
use yii\console\Controller;
use app\models\company\Company;

class SiteProductsController extends Controller
{
    public function actionCron() 
    {
        $companies = Company::find()->with("endpoints")->all();
        foreach ($companies as $company) {
            foreach ($company->endpoints as $endpoint) {
                if ($endpoint->products_url) {
                    //print_r($endpoint->products_url."\n");
                    $this->actionUpdate($endpoint->products_url, $company->id, $endpoint->endpoint_id);
                }
            }
        }
    }



    public function actionUpdate($url = "https://www.hatimaki.ru/upload/Hatimaki_price_MSK%20_test_iiko_cod.xml", $company_id = 1, $endpoint_id = 'hatimaki.ru')
    {
        try {
            $xml_data = file_get_contents($url);
            if(!$xml_data) {
                throw new \ParseError('Dont see XML-file by this link');
            }
            $data = new \SimpleXMLElement($xml_data);

            /*\Yii::$app->db
                ->createCommand('DELETE FROM site_categories WHERE company_id ='.$company_id.' AND endpoint_id ="'.$endpoint_id.'"')
                ->execute();*/
            SiteCurrency::deleteAll(['company_id' => $company_id, 'endpoint_id' => $endpoint_id]);
            SiteProduct::deleteAll(['company_id' => $company_id, 'endpoint_id' => $endpoint_id]);
            foreach($data->shop->currencies as $currency) {
                $siteCurrency = new SiteCurrency([
                    'id' => (string)$currency->currency['id'],
                    'rate' => (int)$currency->currency['rate'],
                    'company_id' => $company_id,
                    'endpoint_id' => $endpoint_id,
                ]);
                $siteCurrency->save();
            }

            foreach($data->shop->categories->category as $category) {
                $siteCategory = SiteCategory::find()->where(["company_id"=>$company_id, "endpoint_id"=>$endpoint_id, "id"=>(int)$category['id']])->one();

                if (!$siteCategory) {
                    $siteCategory = new SiteCategory([
                        'id' => (int)$category['id'],
                        'company_id' => $company_id,
                        'endpoint_id' => $endpoint_id
                    ]);
                }
                $siteCategory->name = (string)$category;
                $siteCategory->save();
            }

            $offers = [];
            $ids = [];
            foreach($data->shop->offers->offer as $offer) {
                $siteProduct = SiteProduct::find()->where([
                    'id' => (int)$offer['id'],
                    'company_id' => $company_id,
                    'endpoint_id' => $endpoint_id,
                ])->one();
                if(!$siteProduct) $siteProduct = new SiteProduct([
                    'id' => (int)$offer['id'],
                    'company_id' => $company_id,
                    'endpoint_id' => $endpoint_id,
                ]);
                $siteProduct->url = $offer->url;
                $siteProduct->picture = $offer->picture;
                $siteProduct->price = $offer->price;
                $siteProduct->oldprice = $offer->oldprice;
                $siteProduct->currencyId = $offer->currencyId;
                $siteProduct->categoryId = $offer->categoryId;
                $siteProduct->name = $offer->name;
                $siteProduct->guid = $offer->guid;
                $siteProduct->save();
                $ids[] = $siteProduct->id;
            }
            if(count($ids) > 0) {
                SiteProduct::deleteAll(['AND', ['endpoint_id' => $endpoint_id], ['NOT IN', 'id', $ids]]);
            } else {
                SiteProduct::deleteAll(['endpoint_id' => $endpoint_id]);
            }
            echo "Added ".count($ids)." products for link - ".$url."\n";
        } catch (\Exception $e) {
            echo "Couldn't parse file by link - ".$url."\n";
            echo $e->getMessage()."\n";
        }
    }
}