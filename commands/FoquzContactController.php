<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use app\models\Export;
use app\models\Filial;
use app\models\vo\Phone;
use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\FoquzContactSearch;
use app\modules\foquz\models\FoquzPollAnswer;
use Yii;
use yii\base\Event;
use yii\db\ActiveRecord;
use yii\console\Controller;
use yii\console\ExitCode;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzContactTag;
use app\modules\foquz\models\FoquzCompanyTag;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class FoquzContactController extends Controller
{
    public function actionTest()
    {

$connection = Yii::$app->getDb();
$command = $connection->createCommand("
        
            select fc.id contact_id, a.id from foquz_poll_answer  a 
            left join orders o ON (a.order_id=o.id)
            left join clients c ON (o.client_id=c.id)
            left join foquz_contact fc ON (fc.client_id=c.id)
            where fc.id  is not null and a.contact_id is null        
");

$result = $command->queryAll();
    
        foreach ($result as $row) {
            FoquzPollAnswer::updateAll(['contact_id' => $row["contact_id"]], ['id' => $row["id"]]);
        }

print_r($result);

    }


    /**
     * This command echoes what you have entered as the message.
     * @param string $message the message to be echoed.
     */
    public function actionImportCsv($user_id, $company_id, $file, $stat_id)
    {

        Event::on(FoquzContactTag::className(), ActiveRecord::EVENT_BEFORE_INSERT, 
            function ($event) { 
                if($event->sender) {
                    $event->sender->created_by = $event->data;
                    $event->sender->updated_by = $event->data;
                }
            },
            $user_id
        );
        Event::on(FoquzContactTag::className(), ActiveRecord::EVENT_BEFORE_UPDATE, 
            function ($event) { 
                if($event->sender) {
                    $event->sender->updated_by = $event->data;
                }
            },
            $user_id
        );
        Event::on(FoquzCompanyTag::className(), ActiveRecord::EVENT_BEFORE_INSERT, 
            function ($event) { 
                if($event->sender) {
                    $event->sender->created_by = $event->data;
                    $event->sender->updated_by = $event->data;
                }
            },
            $user_id
        );
        Event::on(FoquzCompanyTag::className(), ActiveRecord::EVENT_BEFORE_UPDATE, 
            function ($event) { 
                if($event->sender) {
                    $event->sender->updated_by = $event->data;
                }
            },
            $user_id
        );
        Event::on(FoquzContact::className(), ActiveRecord::EVENT_BEFORE_INSERT, 
            function ($event) { 
                if($event->sender) {
                    $event->sender->created_by = $event->data;
                    $event->sender->updated_by = $event->data;
                }
            },
            $user_id
        );
        Event::on(FoquzContact::className(), ActiveRecord::EVENT_BEFORE_UPDATE, 
            function ($event) { 
                if($event->sender) {
                    $event->sender->updated_by = $event->data;
                }
            },
            $user_id
        );


        $res = FoquzContact::importCSV($company_id, $file, $stat_id);
        


        if ($res['status'] == 'ok' || $res['status'] == 'done') {
            echo "Обработано: " . $res['stat']['total'] ." записей" . "\n";
            echo "Внесено: " . $res['stat']['inserted'] ." записей" . "\n";
            echo "Обновлено: " . $res['stat']['updated'] ." записей" . "\n";
            echo "Ошибочных: " . $res['stat']['failed'] ." записей" . "\n";
        return ExitCode::OK;
        } else {
            if (isset($res['errors'])) echo implode('\n', $res['errors']) . "\n";
            else var_dump($res);
            return ExitCode::UNSPECIFIED_ERROR;
        }
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionExportCsv($id)
    {
        $export = Export::findOne($id);
        if (!$export) {
            throw new NotFoundHttpException('Export entry not found');
        }

        $filePath = '/csv/contacts' . time() . '.csv';
        $export->file = $filePath;
        $export->status = Export::STATUS_PROCESSING;
        $export->save();

        $cols = [];
        $additional = false;
        try {
            if (isset($export->params['cols'])) {
                $fp = fopen(Yii::getAlias('@web') . $filePath, 'ab+');
                foreach ($export->params['cols'] as $c) {
                    switch ($c) {
                        case 'client':
                            $cols[] = 'Фамилия';
                            $cols[] = 'Имя';
                            $cols[] = 'Отчество';
                            break;
                        case 'phone':
                            $cols[] = 'Телефон';
                            break;
                        case 'email':
                            $cols[] = 'Email';
                            break;
                        case 'gender':
                            $cols[] = 'Пол';
                            break;
                        case 'filials':
                            $cols[] = 'Филиал контакта';
                            break;
                        case 'birthday':
                            $cols[] = 'Дата рождения';
                            break;
                        case 'tags':
                            $cols[] = 'Теги';
                            break;
                        case 'lastOrderDate':
                            $cols[] = 'Дата последнего заказа';
                            break;
                        case 'ltv':
                            $cols[] = 'LTV';
                            break;
                        default:
                            $additionalField = ContactAdditionalField::find()
                                ->where([
                                    'company_id' => $export->params['advanced']['company'],
                                    'id' => $c
                                ])->one();
                            if ($additionalField) {
                                $additional = true;
                                $adFields[$c] = $additionalField->text;
                                $cols[] = $additionalField->text;
                            }
                    }
                }
                fputcsv($fp, $cols, ';');
                fclose($fp);

                $sample = isset($export->params['sample']) && $export->params['sample'];
                if (!$sample) {
                    $cs = FoquzContactSearch::search($export->params, false, true);
                } else {
                    $filials = Filial::findAll(['company_id' => $export->params['advanced']['company']]);
                    $cs = [
                        [
                            'last_name' => 'Иванов',
                            'first_name' => 'Пётр',
                            'patronymic' => 'Сергеевич',
                            'phone' => '79998888888',
                            'email' => '<EMAIL>',
                            'gender' => FoquzContact::GENDER_MALE,
                            'birthday' => '01.01.1990',
                            'tags' => [['tag' => 'тег1'], ['tag' => 'тег2']],
                            'additionalFieldValuesKeyArray' => isset($adFields) ? array_combine($adFields, array_fill(0, count($adFields), 'Значение')) : null,
                            'contactFilials' => count($filials) >= 2 ? [
                                ['filial.name' => ArrayHelper::toArray($filials[0])['name']],
                                ['filial.name' => ArrayHelper::toArray($filials[1])['name']],
                            ] : (count($filials) == 1 ? [
                                ['filial.name' => ArrayHelper::toArray($filials[0])['name']],
                            ] : []),
                        ],
                        [
                            'last_name' => 'Петрова',
                            'first_name' => 'Анастасия',
                            'patronymic' => 'Васильевна',
                            'phone' => '79998888887',
                            'email' => '<EMAIL>',
                            'gender' => FoquzContact::GENDER_FEMALE,
                            'birthday' => '01.01.1990',
                            'tags' => [['tag' => 'тег2']],
                            'additionalFieldValuesKeyArray' => isset($adFields) ? array_combine($adFields, array_fill(0, count($adFields), 'Значение')) : null,
                            'contactFilials' => count($filials) >= 2 ? [
                                ['filial.name' => ArrayHelper::toArray($filials[1])['name']],
                            ] : [],
                        ],
                    ];
                }
                $counter = 0;
                foreach ($cs as $contacts) {
                    $fp = fopen(Yii::getAlias('@web') . $filePath, 'ab+');
                    $res = [];
                    foreach ($contacts as $contact) {
                        $item = [];
                        if (in_array('Фамилия', $cols)) {
                            $item[] = $contact['last_name'];
                        }
                        if (in_array('Имя', $cols)) {
                            $item[] = $contact['first_name'];
                        }
                        if (in_array('Отчество', $cols)) {
                            $item[] = $contact['patronymic'];
                        }
                        if (in_array('Телефон', $cols)) {
                            $item[] = $contact['phone'];
                        }
                        if (in_array('Email', $cols)) {
                            $item[] = $contact['email'];
                        }
                        if (in_array('Филиал контакта', $cols)) {
                            $filials = ArrayHelper::getColumn((!$sample ? $contact->contactFilials : $contact['contactFilials']), "filial.name");

                            $item[] = implode(", ", $filials);
                        }
                        if (in_array('Пол', $cols)) {
                            if ($contact['gender'] == FoquzContact::GENDER_MALE) {
                                $gender = 'муж.';
                            } else if ($contact['gender'] == FoquzContact::GENDER_FEMALE) {
                                $gender = 'жен.';
                            } else {
                                $gender = '';
                            }
                            $item[] = $gender;
                        }
                        if (in_array('Дата рождения', $cols)) {
                            $item[] = $contact['birthday'] ? date_format(date_create($contact['birthday']), 'd.m.Y') : '';
                        }
                        if (in_array('Теги', $cols)) {
                            $item[] = implode(",", ArrayHelper::getColumn($contact['tags'], 'tag'));
                        }
                        if (in_array('Дата последнего заказа', $cols)) {
                            $item[] = $contact->computedFields && $contact->computedFields->last_order_date ? date('d.m.Y', strtotime($contact->computedFields->last_order_date)) : '';
                        }
                        if (in_array('LTV', $cols)) {
                            $item[] = $contact->computedFields->ltv_amount ?? '';
                        }
                        if (in_array('Добавлен', $cols)) {
                            $item[] = $contact['created_at'] ? date_format(date_create_from_format('U', $contact['created_at']), 'd.m.Y') : '';
                        }
                        if (in_array('Обновлён', $cols)) {
                            $item[] = $contact['updated_at'] ? date_format(date_create_from_format('U', $contact['updated_at']), 'd.m.Y') : '';
                        }
                        if ($additional) {
                            $valuesKeyArray = !$sample ? $contact->additionalFieldValuesKeyArray : $contact['additionalFieldValuesKeyArray'];
                            foreach ($adFields as $k => $v) {
                                $item[] = $valuesKeyArray[$sample ? $v : $k] ?? '';
                            }
                        }
                        $res[] = $item;
                    }
                    foreach ($res as $c) {
                        fputcsv($fp, $c, ';');
                    }
                    fclose($fp);
                    $export->total = $counter += count($contacts);
                    $export->save();
                }
                $export->status = Export::STATUS_DONE;
                $export->save();
            }
        } catch (\Exception $exception) {
            $export->status = Export::STATUS_FAILED;
            $export->error = $exception->getMessage();
            $export->save();
        }
    }

    public function actionFormatPhones()
    {
        $badPhones = FoquzContact::find()
            ->select(['id', 'phone'])
            ->where(['NOT REGEXP', 'phone', '^[0-9]*$'])
            ->all();

        foreach($badPhones as $badPhone) {

            $phone = (string)(new Phone($badPhone->phone));

            Yii::$app->db->createCommand()->update(FoquzContact::tableName(), [
                'phone' => $phone !== '' ? $phone : null,
            ], [
                'id' => $badPhone->id
            ])->execute();
        }
    }
}
