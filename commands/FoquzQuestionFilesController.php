<?php


namespace app\commands;


use app\modules\foquz\models\FoquzQuestionFile;
use yii\console\Controller;

class FoquzQuestionFilesController extends Controller
{
    public function actionRemoveTmpFiles()
    {
        $tmpFiles = FoquzQuestionFile::find()->where(['<', 'created_at', strtotime('-20 minutes')])->andWhere(['question_id' => null])->all();
        foreach($tmpFiles as $tmpFile) {
            if($tmpFile->attachment_type === 'file') {
                try {
                    unlink($tmpFile->file_full_path);
                } catch (\Exception $e) {}
                if($tmpFile->type === 'video') {
                    try {
                        unlink($tmpFile->file_full_path.'.jpg');
                    } catch (\Exception $e) {}
                }
            }
            $tmpFile->delete();
        }
    }
}