<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use yii\console\Controller;
use yii\console\ExitCode;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class HelloController extends Controller
{

    public function actionVariantsToQuestion($fileName, $questionId)
    {
        $question = FoquzQuestion::find()->where(["id"=>$questionId])->with("poll.company")->one();
        if ($question) {
            print("{$question->poll->company->name}-{$question->poll->name}-{$question->name}\n");
            $file = fopen($fileName, 'r');
            $deleted = FoquzQuestionDetail::deleteAll(['foquz_question_id' => $questionId]);
            $added = 0;
            $all = 0;
            $sort = 1;
            while ($row = fgets($file)) {
                $row = trim($row, " \n\r\t");
                if ($row) {
                    $all++;
                    $item = FoquzQuestionDetail::find()
                        ->where([
                            'foquz_question_id' => $questionId,
                            'question' => $row
                        ])->one();
                    if (!$item) {
                        $item = new FoquzQuestionDetail; 
                        $item->foquz_question_id = $questionId;
                        $item->question = $row;
                        $item->position = $sort++;
                        $added+=$item->save();
                        //dded++;
                    }
                }
                
            }
            fclose($file);
            print("всего {$all}\n");
            print("добавлено {$added}\n");
            print("удалено {$deleted}\n");
        }
    }

    /**
     * This command echoes what you have entered as the message.
     * @param string $message the message to be echoed.
     */
    public function actionIndex($message = 'hello world')
    {
        echo $message . "\n";

        return ExitCode::OK;
    }


    /**
     * This command echoes what you have entered as the message.
     * @param string $message the message to be echoed.
     */
    public function actionDummyData()
    {
        $sql1 = "
                INSERT INTO 
                    `foquz_poll_dish_score` 
                (`answer_id`, `dish_id`, `sum`, `score`)
                    VALUES
                (955, 354, 317, :score)
                ";

        for ($i = 0; $i < 10; $i++) {
            \Yii::$app->getDb()->createCommand($sql1, [
                ':score' => mt_rand(1, 5),
            ])->execute();
        }

        $sql2 = "
                INSERT INTO 
                    `foquz_poll_dish_score` 
                (`answer_id`, `dish_id`, `sum`, `score`)
                    VALUES
                (955, 70, 317, :score)
                ";

        for ($i = 0; $i < 11; $i++) {
            \Yii::$app->getDb()->createCommand($sql2, [
                ':score' => mt_rand(1, 5),
            ])->execute();
        }

        $sql3 = "
                INSERT INTO 
                    `foquz_poll_dish_score` 
                (`answer_id`, `dish_id`, `sum`, `score`)
                    VALUES
                (955, 336, 317, :score)
                ";

        for ($i = 0; $i < 876; $i++) {
            \Yii::$app->getDb()->createCommand($sql3, [
                ':score' => mt_rand(1, 5),
            ])->execute();
        }

        $sql4 = "
                INSERT INTO 
                    `foquz_poll_dish_score` 
                (`answer_id`, `dish_id`, `sum`, `score`)
                    VALUES
                (955, 623, 317, :score)
                ";

        for ($i = 0; $i < 999; $i++) {
            \Yii::$app->getDb()->createCommand($sql4, [
                ':score' => mt_rand(1, 5),
            ])->execute();
        }
        $sql5 = "
                INSERT INTO 
                    `foquz_poll_dish_score` 
                (`answer_id`, `dish_id`, `sum`, `score`)
                    VALUES
                (955, 475, 317, :score)
                ";

        for ($i = 0; $i < 1000; $i++) {
            \Yii::$app->getDb()->createCommand($sql5, [
                ':score' => mt_rand(1, 5),
            ])->execute();
        }
        $sql6 = "
                INSERT INTO 
                    `foquz_poll_dish_score` 
                (`answer_id`, `dish_id`, `sum`, `score`)
                    VALUES
                (955, 258, 317, :score)
                ";

        for ($i = 0; $i < 10000; $i++) {
            \Yii::$app->getDb()->createCommand($sql6, [
                ':score' => mt_rand(1, 5),
            ])->execute();
        }
        $sql7 = "
                INSERT INTO 
                    `foquz_poll_dish_score` 
                (`answer_id`, `dish_id`, `sum`, `score`)
                    VALUES
                (955, 462, 317, :score)
                ";

        for ($i = 0; $i < 10000; $i++) {
            \Yii::$app->getDb()->createCommand($sql7, [
                ':score' => mt_rand(1, 5),
            ])->execute();
        }

        return ExitCode::OK;
    }
}
