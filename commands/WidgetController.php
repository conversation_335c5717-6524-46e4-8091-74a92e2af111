<?php

namespace app\commands;

use app\components\RabbitMQComponent;
use app\helpers\DateTimeHelper;
use app\models\company\Company;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzPollWidget;
use Yii;
use yii\console\Controller;
use yii\helpers\ArrayHelper;
use yii\httpclient\Client;
use yii\web\ServerErrorHttpException;

class WidgetController extends Controller
{
    public function actionSync()
    {
        if (empty(Yii::$app->params['widgetsUrl']) || empty(Yii::$app->params['widgetsAuthKey'])) {
            throw new ServerErrorHttpException('Не настроены параметры widgetsUrl или widgetsAuthKey');
        }
        ini_set('memory_limit', '2048M');

        $result = [];
        $widgets = FoquzPollWidget::find()->with('poll');
        foreach ($widgets->batch(5000) as $batch) {
            /** @var FoquzPollWidget $widget */
            foreach ($batch as $widget) {
                $item = $widget->attributes;
                $item['code'] = $widget->code;
                $item['company_id'] = $widget->poll->company_id;
                $item['is_active'] = $widget->poll->status === FoquzPoll::STATUS_NEW && !$widget->poll->deleted && $widget->poll->is_active && $widget->is_active;
                $result[] = $item;
            }
        }

        $client = new Client();
        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl(Yii::$app->params['widgetsUrl'] . '/v1/widget/sync?authKey=' . urlencode(Yii::$app->params['widgetsAuthKey']))
            ->setHeaders(['Content-Type' => 'application/json'])
            ->setData(['data' => json_encode($result)])
            ->send();

        print $response->content . PHP_EOL;
        if (!$response->isOk) {
            throw new ServerErrorHttpException('Ошибка отправки виджетов');
        }

        /** @var RabbitMQComponent $rabbit */
        $rabbit = Yii::$app->rabbit;

        $companiesQuery = Company::find()
            ->select([
                'id AS company_id',
                'IF (widget_display_limit = 1, widget_display_frequency, NULL) AS display_limit',
                'is_answer_limited'
            ])
            ->where(['deleted' => 0])
            ->asArray();

        foreach ($companiesQuery->batch(5000) as $companies) {
            $rabbit->queue('widget.company_settings')
                ->type('sync_settings')
                ->push($companies);
            print 'Pushed ' . count($companies) . ' company settings' . PHP_EOL;
        }

        $pollsQuery = FoquzPoll::find()
            ->select([
                'id',
                'widget_display_ignore_limit',
                'widget_display_limit_type',
                'widget_display_limit_days',
                'widget_display_limit_time',
                'is_published',
                'is_answer_limited',
                'datetime_start',
                'datetime_end',
            ])
            ->where(['deleted' => 0]);

        foreach ($pollsQuery->batch(5000) as $polls) {
            $data = [];
            /** @var FoquzPoll $poll */
            foreach ($polls as $poll) {
                $displayLimitTime = 0;
                if ($poll->widget_display_limit_type === FoquzPoll::WIDGET_DISPLAY_LIMIT_TYPE_PERIOD) {
                    $displayLimitTime = $poll->widget_display_limit_days * 24 * 60;
                    if (preg_match('/^(\d{2}):(\d{2})$/', $poll->widget_display_limit_time, $matches)) {
                        $displayLimitTime += (int)$matches[1] * 60 + (int)$matches[2];
                    }
                }
                $data[] = [
                    'poll_id' => $poll->id,
                    'is_answer_limited' => (int) $poll->is_answer_limited,
                    'is_published' => (int) $poll->is_published,
                    'display_ignore_limit' => $poll->widget_display_ignore_limit,
                    'display_limit_type' => $poll->widget_display_limit_type,
                    'display_limit_time' => $displayLimitTime,
                    'date_start' => $poll->datetime_start ? date('Y-m-d H:i', strtotime($poll->datetime_start)) : null,
                    'date_end' => $poll->datetime_end ? date('Y-m-d H:i', strtotime($poll->datetime_end)) : null,
                ];
            }
            $rabbit->queue('widget.poll_settings')
                ->type('sync_settings')
                ->push($data);
            print 'Pushed ' . count($data) . ' poll settings' . PHP_EOL;
        }
    }

    public function actionClearAnswers()
    {
        $date = date('Y-m-d H:i:s', time() - 60 * 60 * Yii::$app->params['widgetAnswersExpire']);
        $sends = FoquzPollMailingListSend::find()
            ->leftJoin('foquz_poll_answer', 'foquz_poll_answer.id = foquz_poll_mailing_list_send.answer_id')
            ->where(['foquz_poll_answer.status' => FoquzPollAnswer::STATUS_NEW])
            ->andWhere(['<', 'foquz_poll_mailing_list_send.sended', $date])
            ->andWhere(['NOT', ['foquz_poll_mailing_list_send.widget_id' => null]]);

        foreach ($sends->batch(1000) as $batch) {
            $keys = ArrayHelper::getColumn($batch, 'key');

            $client = new Client();
            $response = $client->createRequest()
                ->setMethod('POST')
                ->setUrl(Yii::$app->params['widgetsUrl'] . '/v1/answer/bulk-delete?authKey=' . urlencode(Yii::$app->params['widgetsAuthKey']))
                ->setHeaders(['Content-Type' => 'application/json'])
                ->setData(['keys' => $keys])
                ->send();

            if (!$response->isOk || $response->format !== Client::FORMAT_JSON || empty($response->data['success'])) {
                print 'Error delete answers' . PHP_EOL;
                continue;
            }
            FoquzPollMailingListSend::deleteAll(['key' => $keys]);
        }
    }
}
