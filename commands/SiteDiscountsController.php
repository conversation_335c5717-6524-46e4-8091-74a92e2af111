<?php

namespace app\commands;

use app\modules\foquz\models\site\SiteCategory;
use app\modules\foquz\models\site\SiteDiscount;
use yii\console\Controller;
use app\models\company\Company;

class SiteDiscountsController extends Controller
{

    public function actionCron() 
    {
        $companies = Company::find()->with("endpoints")->all();
        foreach ($companies as $company) {
            foreach ($company->endpoints as $endpoint) {
                if ($endpoint->discounts_url) {
                    //print($endpoint->discounts_url."\n");
                    $this->actionUpdate($endpoint->discounts_url, $company->id, $endpoint->endpoint_id);
                }
            }
        }
    }


    public function actionUpdate($url = "http://opros.hatimaki.local/uploads/Hatimaki_discounts_MSK.xml", $company_id = 1, $endpoint_id = 'hatimaki.ru')
    {
        try {
            $xml_data = file_get_contents($url);
            if(!$xml_data) {
                throw new \ParseError('Dont see XML-file by this link');
            }
            $data = new \SimpleXMLElement($xml_data);
            $ids = [];
            foreach($data->shop->offers->offer as $offer) {
                $params = [];
                foreach($offer->param as $customParam) {
                    $params[(string)$customParam['name']] = (string)$customParam;
                }
                $discount = SiteDiscount::find()->where([
                    'id' => (int)$offer['id'],
                    'company_id' => $company_id,
                    'endpoint_id' => $endpoint_id,
                ])->one();
                if(!$discount) {
                    $discount = new SiteDiscount([
                        'id' => $offer['id'],
                        'company_id' => $company_id,
                        'endpoint_id' => $endpoint_id,
                    ]);
                }
                $discount->available = $offer['available'] ?? true;
                $discount->url = $offer->url;
                $category = SiteCategory::find()->where([
                    'id' => $offer->categoryId,
                    'company_id' => $company_id,
                    'endpoint_id' => $endpoint_id,
                ])->one();
                if(!$category) {
                    $category = new SiteCategory([
                        'id' => $offer->categoryId,
                        'company_id' => $company_id,
                        'endpoint_id' => $endpoint_id,
                    ]);
                    $category->save();
                }
                $discount->categoryId = $category->id;
                $discount->picture = $offer->picture;
                $discount->name = $offer->name;
                $discount->description = $offer->description;
                $discount->custom_fields = json_encode($params, JSON_UNESCAPED_UNICODE);
                if(!$discount->save()) {
                    print_r($discount->errors);
                }
                $ids[] = $offer['id'];
            }
            if(count($ids) > 0) {
                SiteDiscount::deleteAll(['AND', ['endpoint_id' => $endpoint_id], ['NOT IN', 'id', $ids]]);
            } else {
                SiteDiscount::deleteAll(['endpoint_id' => $endpoint_id]);
            }
            echo "Added ".count($ids)." discounts for link - ".$url."\n";
        } catch (\Exception $e) {
            echo "Couldn't parse file by link - ".$url."\n";
            echo $e->getMessage()."\n";
        }
    }
}