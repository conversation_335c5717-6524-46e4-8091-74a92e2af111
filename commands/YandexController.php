<?php

namespace app\commands;

use app\components\YandexReviewsComponent;
use app\models\Filial;
use app\modules\foquz\models\FilialReview;
use yii\console\Controller;
use \Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\DesiredCapabilities;

class YandexController extends Controller
{
    public function actionYandex()
    {
        $host = 'https://yandex.ru/maps/org/kiabi/1160651438/reviews/?ll=37.587392%2C55.913063&z=18';

        $host = 'https:/hatimaki.ru/';

        $desired_capabilities = DesiredCapabilities::chrome();
        //$desired_capabilities->setJavascriptEnabled(false);
        

        $driver = RemoteWebDriver::create("http://localhost:4444/wd/hub", $desired_capabilities);


        //$driver->get($host);


        die("123123123");

        $filialsWithYandexIds = Filial::find()
            ->where(['is not', 'yandex_link', null])
            ->all();
        foreach($filialsWithYandexIds as $filial) {
            $yrComponent = new YandexReviewsComponent($filial->yandex_link);
            $reviews = $yrComponent->parse();
            foreach($reviews as $review) {
                $reviewModel = FilialReview::find()
                    ->where([
                        'filial_id' => $filial->id,
                        'user_fio' => $review['user_fio'],
                        'text' => $review['text'],
                        'source' => $review['source']
                    ])->one();
                if(!$reviewModel) {
                    $reviewModel = new FilialReview([
                        'filial_id' => $filial->id,
                        'user_fio' => $review['user_fio'],
                        'text' => $review['text'],
                        'source' => $review['source']
                    ]);
                }
                $reviewModel->load($review, '');
                $reviewModel->save();
            }
        }
    }
}