<?php


namespace app\commands;


use app\models\Export;
use yii\console\Controller;

class ExportController extends Controller
{
    /**
     * Осуществляет очистку старых файлов экспорта (старше суток)
     * @return void
     */
    public function actionClean(): void
    {
        /** @var Export[] $models */
        $models = Export::find()
            ->where(['status' => Export::STATUS_DONE])
            ->andWhere(['<', 'updated_at', date('Y-m-d H:i:s', strtotime('-1 day'))])
            ->all();

        foreach ($models as $model) {
            $model->deleteFile();
        }
        print('Удалено ' . count($models) . ' файл(ов)' . PHP_EOL);
    }
}
