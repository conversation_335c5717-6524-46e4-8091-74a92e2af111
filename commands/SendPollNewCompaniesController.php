<?php


namespace app\commands;


use app\models\company\Company;
use app\models\company\CompanyStaff;
use yii\console\Controller;
use yii\db\Expression;

class SendPollNewCompaniesController extends Controller
{
    public function actionSend()
    {
        $companiesRegisteredWeekAgo = Company::find()
            ->where(new Expression("DATE_FORMAT(created_at, '%Y-%m-%d') = '".date('Y-m-d', strtotime('-3 days'))."'"))
            ->all();

        foreach($companiesRegisteredWeekAgo as $sendCompany)
        {

            $user = CompanyStaff::find()->where([
                'company_id' => $sendCompany->id
            ])->one();
            if($user) {
                $url = \Yii::$app->params['new_companies_sender_url'].'?access-token='.
                    \Yii::$app->params['new_companies_sender_token'].'&id='.
                    \Yii::$app->params['new_companies_sender_poll_id'];

                

                print_r(                    ['contact' => [
                        'Email' => $user->user->email,
                        'Телефон' => $user->user->phone,
                        'Фамилия' => $user->user->name,
                        'Название компании' => $sendCompany->name,
                        'Домен компании' => $sendCompany->alias,
                        'Дата регистрации' => date('Y-m-d H:i:s', $user->user->created_at),
                        'Логин' => $user->user->username
                    ]]
);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
                    'contact' => [
                        'Email' => $user->user->email,
                        'Телефон' => $user->user->phone,
                        'Фамилия' => $user->user->name,
                        'Название компании' => $sendCompany->name,
                        'Домен компании' => $sendCompany->alias,
                        'Дата регистрации' => date('Y-m-d H:i:s', $user->user->created_at),
                        'Логин' => $user->user->username
                    ]
                ]));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $output = curl_exec($ch);
                curl_close($ch);
                $response = json_decode($output);
                if(!empty($response)) {
                    \Yii::error($response, __METHOD__);
                }
            }
        }
    }
}