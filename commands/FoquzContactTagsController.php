<?php


namespace app\commands;


use app\modules\foquz\models\FoquzCompanyTag;
use yii\console\Controller;

class FoquzContactTagsController extends Controller
{
    public function actionAutoRefresh()
    {
        $autoTags = FoquzCompanyTag::find()->where([
            'auto_add' => 1
        ])->all();
        foreach($autoTags as $tag) {
            if($tag->getConditions()->count() > 0) {

                $tag->refreshForContacts();
            }
        }
    }
}