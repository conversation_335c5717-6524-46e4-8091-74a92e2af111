<?php

namespace app\commands;

use app\modules\foquz\models\FoquzPoll;
use yii\console\Controller;


class FoquzPollController extends Controller
{
    public function actionRecalcPollStats($start = null, $end = null, $companyID = null)
    {
        $polls = FoquzPoll::find()
            ->select('id')
            ->filterWhere(['company_id' => $companyID]);

        if ($start) {
            $polls->andWhere(['OR',
                ['>=', 'first_answer_at', date('Y-m-d', strtotime($start))],
                ['last_answer_at' => null],
            ]);
        }
        if ($end) {
            $polls->andWhere(['OR',
                ['<=', 'last_answer_at', date('Y-m-d', strtotime($end))],
                ['last_answer_at' => null],
            ]);
        }

        $polls = $polls->column();

        if (empty($polls)) {
            print 'No polls found' . PHP_EOL;
            return;
        }

        $startTime = microtime(true);
        print 'Start recalc' . PHP_EOL;
        $pollsCount = count($polls);
        print 'Polls count: ' . $pollsCount . PHP_EOL;
        foreach (array_chunk($polls, 5000) as $chunk) {
            FoquzPoll::recalcStat($chunk);
            print 'Recalc ' . count($chunk) . ' polls' . PHP_EOL;
        }
        print 'Duration: ' . round(microtime(true) - $startTime) . ' sec.' . PHP_EOL;
    }
}
