<?php

namespace app\commands;

use app\models\DictionaryElement;
use app\models\Filial;
use app\models\FilialCategory;
use yii\console\Controller;

/**
 * Экспорт филиалов для особого кейса рассылки по оценке отделов
 */
class HRManagerController extends Controller
{
    public function actionCreateFilials($fileName, $companyId, $dictionaryId)
    {
        $firstRow = true;
        $cols = [];
        $file = fopen($fileName, "r");
        while ($filialNames = fgetcsv($file, null, "\t",)) {

            $parentId = null;
            $parentDic = 0;
            //$dictIds
            foreach ($filialNames as $i => $filialName) {
                $filialName = trim($filialName);
                $type = $i == count($filialNames) - 1 ? "element" : "category";
                $dict = DictionaryElement::find()->where([
                    'dictionary_id' => $dictionaryId,
                    "title"         => $filialName,
                    "parent_id"     => $parentDic,
                    "type"  => $type
                ])->one();
                if (!$dict) {
                    $dict = new DictionaryElement();
                    $dict->dictionary_id = $dictionaryId;
                    $dict->title = $filialName;
                    $dict->type = $type;
                    $dict->parent_id = $parentDic;
                }
                $dict->position = $i;
                $dict->is_active = 1;
                if (!$dict->save()) {
                    print_r($dict->getErrors());
                }
                $parentDic = $dict->id;




                if ($i == count($filialNames) - 1) {
                    $filial = Filial::find()->where([
                        "company_id" => $companyId,
                        "name" => $filialName,
                        "category_id" => $parentId
                    ])->one();
                    if (!$filial) {
                        $filial = new Filial;
                        $filial->company_id = $companyId;
                        $filial->name = $filialName;
                    }
                    $filial->is_active = 1;
                    $filial->category_id = $parentId;
                    if (!$filial->save(false)) {
                        print_r($filial->getErrors());
                    }
                } else {
                    $category = FilialCategory::find()->where([
                        "company_id" => $companyId,
                        "name"       => $filialName
                    ])->one();
                    if (!$category) {
                        $category = new FilialCategory;
                        $category->company_id = $companyId;
                        $category->name = $filialName;
                    }
                    $category->is_deleted = 0;
                    if (!$category->save(false)) {
                        print_r($category->getErrors());
                        die("Error!");
                    }
                    $parentId = $category->id;
                }
            }

        }

    }
}