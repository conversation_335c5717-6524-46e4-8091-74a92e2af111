<?php

namespace app\commands;

use app\modules\foquz\models\FoquzContact;
use yii\console\Controller;

class FoquzContactComputedFieldsController extends Controller
{
    public function actionFavoriteFilial()
    {
        FoquzContact::updateFavoriteFililal();
    }

    public function actionCompany($company_id)
    {
        if(!$company_id) {
            echo 'Не передан ID компании';
            exit;
        }
        $contacts = FoquzContact::find()->where([
            'company_id' => $company_id
        ])->andWhere(['is not', 'client_id', null])->all();
        foreach($contacts as $contact) {
            $contact->updateComputedFields();
        }
        echo 'Обработано контактов: '.count($contacts);
    }

    public function actionContact($contact_id = null, $is_client = false) {
        if(!$contact_id) {
            echo 'Не передан ID клиента';
            exit;
        }
        $is_client = (bool)$is_client;
        $contact = $is_client ? FoquzContact::find()->where(['client_id' => $contact_id])->one() : FoquzContact::find()->where(['id' => $contact_id])->one();
        if(!$contact) {
            echo "Клиент не найден";
            exit;
        }
        $contact->updateComputedFields();
    }

    public function actionRefreshAll()
    {
        $contacts = FoquzContact::find()->where(['is not', 'client_id', null])->all();
        foreach($contacts as $contact) {
            $contact->updateComputedFields();
        }
        echo 'Обработано контактов: '.count($contacts);
    }
}