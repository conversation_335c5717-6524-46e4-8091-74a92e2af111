<?php

namespace app\commands;

use app\modules\foquz\models\DiscountPool;
use app\modules\foquz\models\FoquzPollCode;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSetting;
use app\modules\foquz\models\mailings\MailingCode;
use yii\console\Controller;

class DiscountPoolController extends Controller
{
    public function actionCheck()
    {
        $activePools = DiscountPool::find()->where(['is_active' => 1])->all();
        foreach($activePools as $pool) {
            if($pool->notif_coupon_less_than && $pool->isPoolType()) {
                if($pool->getCodes()->where(['sended_at' => null])->count() <= $pool->coupon_less_than_value) {
             //       print($pool->notificationUser->email."\n");
                    $url = \Yii::$app->params['protocol'].'://'.$pool->company->alias;
                    \Yii::$app->mailer->compose('pool_coupons_less_than', ['url' => $url, 'pool' => $pool])
                        ->setFrom(\Yii::$app->params['main_sender'])
                        ->setTo($pool->notificationUser->email)
                        ->setSubject('Количество промокодов в пуле купонов '.$pool->title.' остаётся меньше '.$pool->coupon_less_than_value)
                        ->send();
                }
            }
            if($pool->notif_available_to_end) {
                $now = time();
                $dateEnd = strtotime($pool->available_to);
                $dateDiff = $dateEnd - $now;
                $diffInDays = floor($dateDiff / (60 * 60 * 24));
                if($diffInDays <= $pool->count_days && $diffInDays > 0) {
                 //   print($pool->notificationUser->email."\n");
                    $url = \Yii::$app->params['protocol'].'://'.$pool->company->alias;
                    \Yii::$app->mailer->compose('pool_will_end_soon', ['url' => $url, 'pool' => $pool, 'days' => $diffInDays])
                        ->setFrom(\Yii::$app->params['main_sender'])
                        ->setTo($pool->notificationUser->email)
                        ->setSubject('Срок действия пула купонов '.$pool->title.' заканчивается через '.$diffInDays.' '.$pool->pluralDays($diffInDays))
                        ->send();
                }
            }
        }
    }

    public function actionMigratePollCodes()
    {
        $db = \Yii::$app->db;
        $transaction = $db->beginTransaction();

        $foquzPollCodes = FoquzPollCode::find()
            ->where(['pool_id' => null])
            ->all();

        try {

            foreach ($foquzPollCodes as $code) {

                $pool = DiscountPool::find()
                    ->where([
                        'company_id' => $code->poll->company_id,
                        'code' => $code->code,
                        'type' => DiscountPool::TYPE_REUSABLE
                    ])
                    ->one();

                if ($pool === null) {

                    $pool = new DiscountPool();

                    $pool->code = $code->code;
                    $pool->title = $code->code;
                    $pool->type = DiscountPool::TYPE_REUSABLE;
                    $pool->company_id = $code->poll->company_id;

                    if (!$pool->save()) {
                        \Yii::error([
                            'class' => DiscountPool::class,
                            'attributes' => $pool->getAttributes(),
                            $pool->errors
                        ]);
                        print_r($pool->errors);
                        throw new \Exception("Невозможно сохранить Пул. Код: $pool->code \n");
                    }
                }

                $code->pool_id = $pool->id;

                if (!$code->save()) {
                    \Yii::error([
                        'class' => FoquzPollCode::class,
                        'attributes' => $code->getAttributes(),
                        $pool->errors
                    ]);
                    throw new \Exception("Невозможно сохранить FoquzPollCode\n");
                }
            }

            $transaction->commit();
        }catch(\Throwable $e) {
            $transaction->rollBack();
            \Yii::error($e->getTraceAsString());
            echo $e->getMessage();
        }
    }


    public function actionMigrateIntermediateBlocks()
    {
        $db = \Yii::$app->db;
        $transaction = $db->beginTransaction();

        $blocks = FoquzQuestionIntermediateBlockSetting::find()
            ->where(['not', ['code' => null]])
            ->andWhere(['not', ['code' => '']])
            ->all();

        try {

            foreach ($blocks as $block) {

                $pool = DiscountPool::find()
                    ->where([
                        'company_id' => $block->question->company_id,
                        'code' => $block->code,
                        'type' => DiscountPool::TYPE_REUSABLE
                    ])
                    ->one();

                if ($pool === null) {

                    $pool = new DiscountPool();

                    $pool->code = $block->code;
                    $pool->title = $block->code;
                    $pool->type = DiscountPool::TYPE_REUSABLE;
                    $pool->company_id = $block->question->company_id;

                    if (!$pool->save()) {
                        \Yii::error([
                            'class' => DiscountPool::class,
                            'attributes' => $pool->getAttributes(),
                            $pool->errors
                        ]);
                        print_r($pool->errors);
                        throw new \Exception("Невозможно сохранить Пул. Код: $pool->code");
                    }
                }

                $block->pool_id = $pool->id;

                if (!$block->save()) {
                    \Yii::error([
                        'class' => FoquzQuestionIntermediateBlockSetting::class,
                        'attributes' => $block->getAttributes(),
                        $pool->errors
                    ]);
                    throw new \Exception("Невозможно сохранить FoquzQuestionIntermediateBlockSetting");
                }
            }

            $transaction->commit();
        }catch(\Throwable $e) {
            $transaction->rollBack();
            \Yii::error($e->getTraceAsString());
            echo $e->getMessage();
        }
    }

    public function actionMigrateMailingCodes()
    {
        $db = \Yii::$app->db;
        $transaction = $db->beginTransaction();

        $mailingCodes = MailingCode::find()
            ->where(['not', ['code' => null]])
            ->andWhere(['not', ['code' => '']])
            ->all();

        try {

            foreach ($mailingCodes as $code) {

                $pool = DiscountPool::find()
                    ->where([
                        'company_id' => $code->mailing->company_id,
                        'code' => $code->code,
                        'type' => DiscountPool::TYPE_REUSABLE
                    ])
                    ->one();

                if ($pool === null) {

                    $pool = new DiscountPool();

                    $pool->code = $code->code;
                    $pool->title = $code->code;
                    $pool->type = DiscountPool::TYPE_REUSABLE;
                    $pool->company_id = $code->mailing->company_id;

                    if (!$pool->save()) {
                        \Yii::error([
                            'class' => DiscountPool::class,
                            'attributes' => $pool->getAttributes(),
                            $pool->errors
                        ]);
                        print_r($pool->errors);
                        throw new \Exception("Невозможно сохранить Пул. Код: $pool->code");
                    }
                }

                $code->pool_id = $pool->id;

                if (!$code->save()) {
                    \Yii::error([
                        'class' => FoquzQuestionIntermediateBlockSetting::class,
                        'attributes' => $code->getAttributes(),
                        $pool->errors
                    ]);
                    throw new \Exception("Невозможно сохранить FoquzQuestionIntermediateBlockSetting");
                }
            }

            $transaction->commit();
        }catch(\Throwable $e) {
            $transaction->rollBack();
            \Yii::error($e->getTraceAsString());
            echo $e->getMessage();
        }
    }
}