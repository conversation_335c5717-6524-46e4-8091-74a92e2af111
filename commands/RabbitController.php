<?php

namespace app\commands;

use app\components\RabbitMQComponent;
use app\consumers\Router;
use Yii;
use yii\console\Controller;

class RabbitController extends Controller
{

    public function actionListen($queueName, $componentName="rabbit")
    {
        if (!isset(Yii::$app->rabbit)) {
            throw new \RuntimeException('RabbitMQ component not found');
        }
        if (!preg_match('/^' . \Yii::$app->params['queueNamePrefix'] . '\.([a-z_]+)$/', $queueName)) {
            throw new \InvalidArgumentException('Invalid queue name (prefix)');
        }
        /** @var RabbitMQComponent $rabbit */
        $rabbit = Yii::$app->$componentName;
        $rabbit->queue($queueName)->class(Router::class)->listen();
    }
}
