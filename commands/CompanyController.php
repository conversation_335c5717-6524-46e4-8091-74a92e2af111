<?php

namespace app\commands;

use app\components\dto\AddCompanyTariffDto;
use app\components\helpers\CompanyTariffHelper;
use app\components\helpers\InvoiceHelper;
use app\models\Client;
use app\models\ClientEmail;
use app\models\company\CompanyFilterAccess;
use app\models\company\CompanyIikoAccess;
use app\models\company\CompanyIikoAccessCondition;
use app\models\company\CompanyRequestProcessingSettings;
use app\models\company\CompanyRequisites;
use app\models\company\CompanySetting;
use app\models\company\RequestSettings2Poll;
use app\models\Complain;
use app\models\Dictionary;
use app\models\Dish;
use app\models\DishCategory;
use app\models\Emails;
use app\models\Export;
use app\models\google\GoogleOauthToken;
use app\models\google\GoogleSheetsParams;
use app\models\Order;
use app\models\OrderDishes;
use app\models\Poll;
use app\models\User;
use app\models\WidgetPreferences;
use app\models\Worker;
use app\modules\foquz\models\ApiRequestLog;
use app\modules\foquz\models\ApiSendPoll;
use app\modules\foquz\models\AutoPollRequest;
use app\modules\foquz\models\BrowserNotificationUserDevice;
use app\modules\foquz\models\Channel;
use app\modules\foquz\models\channels\ChannelDiscountBlock;
use app\modules\foquz\models\channels\ChannelDiscountBlockDiscount;
use app\modules\foquz\models\channels\ChannelProductBlock;
use app\modules\foquz\models\channels\ChannelProductBlockProduct;
use app\modules\foquz\models\channels\GlobalEmailSettings;
use app\modules\foquz\models\channels\GlobalEmailSettingsDomainVerificationTable;
use app\modules\foquz\models\channels\GlobalPushSettings;
use app\modules\foquz\models\channels\GlobalSmsSettings;
use app\modules\foquz\models\channels\GlobalTelegramSettings;
use app\modules\foquz\models\channels\GlobalViberSettings;
use app\modules\foquz\models\CompanyAffiliateCode;
use app\modules\foquz\models\CompanyFeedback;
use app\modules\foquz\models\CompanyFeedbackTheme;
use app\modules\foquz\models\CompanyTariff;
use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\ContactAdditionalFieldValue;
use app\modules\foquz\models\coupons\BonusAccount;
use app\modules\foquz\models\DiscountPool;
use app\modules\foquz\models\DiscountPoolCode;
use app\modules\foquz\models\EditorFolder;
use app\modules\foquz\models\EmployeePost;
use app\modules\foquz\models\ExportLog;
use app\modules\foquz\models\ExternalResource;
use app\modules\foquz\models\Feedback;
use app\modules\foquz\models\FilialEmployeeSettings;
use app\modules\foquz\models\FilialEmployeeUserFilial;
use app\modules\foquz\models\FilialPollKey;
use app\modules\foquz\models\FilialReview;
use app\modules\foquz\models\FilialReviewReply;
use app\modules\foquz\models\FoquzCompanyTag;
use app\modules\foquz\models\FoquzCompanyTagCondition;
use app\modules\foquz\models\FoquzCompanyTagTask;
use app\modules\foquz\models\FoquzComplaint;
use app\modules\foquz\models\FoquzComplaintFile;
use app\modules\foquz\models\FoquzContactComputedField;
use app\modules\foquz\models\FoquzContactComputedFieldsFavoriteDish;
use app\modules\foquz\models\FoquzContactEndpointClient;
use app\modules\foquz\models\FoquzContactFilial;
use app\modules\foquz\models\FoquzContactTag;
use app\modules\foquz\models\FoquzPointCondition;
use app\modules\foquz\models\FoquzPointItem;
use app\modules\foquz\models\FoquzPointItemHasBusinessType;
use app\modules\foquz\models\FoquzPointSelected;
use app\modules\foquz\models\FoquzPointTypeRelation;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerHiddenQuestion;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPollAnswerItemFile;
use app\modules\foquz\models\FoquzPollCode;
use app\modules\foquz\models\FoquzPollDesign;
use app\modules\foquz\models\FoquzPollDesignTemplate;
use app\modules\foquz\models\FoquzPollDishScore;
use app\modules\foquz\models\FoquzPollDisplayPage;
use app\modules\foquz\models\FoquzPollDisplayPageQuestion;
use app\modules\foquz\models\FoquzPollDisplaySetting;
use app\modules\foquz\models\FoquzPollFavorite;
use app\modules\foquz\models\FoquzPollKey;
use app\modules\foquz\models\FoquzPollLang;
use app\modules\foquz\models\FoquzPollMailingCondition;
use app\modules\foquz\models\FoquzPollMailingContactLinkStat;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListContact;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzPollNoOrderAsUsualTriggerSettings;
use app\modules\foquz\models\FoquzPollPage;
use app\modules\foquz\models\FoquzPollPageSocialNetworksOptions;
use app\modules\foquz\models\FoquzPollQuestionsLogic;
use app\modules\foquz\models\FoquzPollQuestionViewLogic;
use app\modules\foquz\models\FoquzPollStatFilterSettings;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\models\FoquzPollStatWidgetKey;
use app\modules\foquz\models\FoquzPollWidget;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionAddressCodes;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionDifferentialRow;
use app\modules\foquz\models\FoquzQuestionEndScreenLogo;
use app\modules\foquz\models\FoquzQuestionFile;
use app\modules\foquz\models\FoquzQuestionFileLang;
use app\modules\foquz\models\FoquzQuestionFormField;
use app\modules\foquz\models\FoquzQuestionFormFieldLang;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSetting;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSettingLang;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSettingSocNetworks;
use app\modules\foquz\models\FoquzQuestionLang;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionMatrixElementLang;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariant;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariantLang;
use app\modules\foquz\models\FoquzQuestionNpsRatingSetting;
use app\modules\foquz\models\FoquzQuestionPrioritySettings;
use app\modules\foquz\models\FoquzQuestionRightAnswer;
use app\modules\foquz\models\FoquzQuestionScaleRatingSetting;
use app\modules\foquz\models\FoquzQuestionSemDifSetting;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\FoquzQuestionStarRatingOptions;
use app\modules\foquz\models\HrmIntegration;
use app\modules\foquz\models\Invoice;
use app\modules\foquz\models\MailingFilterSettings;
use app\modules\foquz\models\mailings\Mailing;
use app\modules\foquz\models\mailings\MailingChannel;
use app\modules\foquz\models\mailings\MailingChannelDiscountBlock;
use app\modules\foquz\models\mailings\MailingChannelDiscountBlockDiscount;
use app\modules\foquz\models\mailings\MailingChannelProductBlock;
use app\modules\foquz\models\mailings\MailingChannelProductBlockProduct;
use app\modules\foquz\models\mailings\MailingChannelRepeat;
use app\modules\foquz\models\mailings\MailingChannelRepeatDiscountBlock;
use app\modules\foquz\models\mailings\MailingChannelRepeatDiscountBlockDiscount;
use app\modules\foquz\models\mailings\MailingChannelRepeatProductBlock;
use app\modules\foquz\models\mailings\MailingChannelRepeatProductBlockProduct;
use app\modules\foquz\models\mailings\MailingCode;
use app\modules\foquz\models\mailings\MailingContactLinkStat;
use app\modules\foquz\models\mailings\MailingException;
use app\modules\foquz\models\mailings\MailingFavorite;
use app\modules\foquz\models\mailings\MailingList;
use app\modules\foquz\models\mailings\MailingListContact;
use app\modules\foquz\models\mailings\MailingListSend;
use app\modules\foquz\models\mailings\MailingNoOrderAsUsualTriggerSettings;
use app\modules\foquz\models\mailings\MailingProhibition;
use app\modules\foquz\models\mailings\MailingsMailingCondition;
use app\modules\foquz\models\notifications\NotificationAnswerProcessingChannel;
use app\modules\foquz\models\notifications\NotificationScript;
use app\modules\foquz\models\notifications\NotificationScriptChannel;
use app\modules\foquz\models\notifications\NotificationScriptChannelRepeat;
use app\modules\foquz\models\notifications\SiteNotification;
use app\modules\foquz\models\notifications\SiteNotificationPoll;
use app\modules\foquz\models\processing\FineEmployeePost;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessing;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingCompensation;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingEmployee;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingFile;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingFine;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingFineCategory;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingReason;
use app\modules\foquz\models\processing\ProcessingEmployeePost;
use app\modules\foquz\models\processing\ProcessingFile;
use app\modules\foquz\models\RecipientQuestionDetail;
use app\modules\foquz\models\Repeat;
use app\modules\foquz\models\Report;
use app\modules\foquz\models\ReportLink;
use app\modules\foquz\models\ReportShare;
use app\modules\foquz\models\ReportVersion;
use app\modules\foquz\models\requests_project\Answer;
use app\modules\foquz\models\requests_project\AnswerHasFiles;
use app\modules\foquz\models\requests_project\GeneralSettings;
use app\modules\foquz\models\requests_project\Project;
use app\modules\foquz\models\requests_project\ProjectHasField;
use app\modules\foquz\models\requests_project\Theme;
use app\modules\foquz\models\RespondentPoll;
use app\modules\foquz\models\ScoresInterpretationRange;
use app\modules\foquz\models\SettingTableFilters;
use app\modules\foquz\models\SettingTables;
use app\modules\foquz\models\site\SiteCategory;
use app\modules\foquz\models\site\SiteCurrency;
use app\modules\foquz\models\site\SiteDiscount;
use app\modules\foquz\models\site\SiteProduct;
use app\modules\foquz\models\Tariff;
use app\modules\foquz\models\TariffChangeRequest;
use app\modules\foquz\models\UserChangeEmail;
use app\modules\foquz\models\v1\ProductList;
use app\modules\foquz\models\v1\ProductView;
use app\modules\foquz\models\WordAnswer;
use filsh\yii2\oauth2server\models\OauthAccessTokens;
use filsh\yii2\oauth2server\models\OauthAuthorizationCodes;
use filsh\yii2\oauth2server\models\OauthClients;
use filsh\yii2\oauth2server\models\OauthRefreshTokens;
use webvimark\modules\UserManagement\models\UserVisitLog;
use Yii;
use yii\console\Controller;
use app\models\company\Company;
use app\models\company\CompanyStaff;
use yii\helpers\ArrayHelper;
use app\models\Filial;
use app\models\FilialCategory;

use app\models\DictionaryElement;
use app\modules\foquz\models\FoquzQuestionDetailLang;


class CompanyController extends Controller
{
    public function actionClearQr()
    {
        $path  = "./web/uploads/";
        $files = scandir('./web/uploads');
        foreach($files as $file) {
            if($file == '.' || $file == '..') continue;
            $fPath = $path . $file;
            if (!is_file($fPath)) continue;
            if (!preg_match("@^QR-code-email@", $file))  continue;
            $t = date("Y-m-d", filectime($fPath));
            print $fPath . "\t" . $t;
            if ($t<"2024-04-11") {
                rename($fPath, "../old_files/$file");
                print("\t unlink");
            } else {

            }
            print("\n");
        }
    }

    public function actionTest2() {

        $p = 0;
        for ($j=0; $j<1000000; $j++) {
            $arr = [];
            for ($i = 0; $i <= 1; $i++) {
                $arr[] = $i;

            }
            usort($arr, static function ($a, $b) {
                return mt_rand(0, 1) == 0 ? -1 : 1;
                //return $b['priority'] <=> $a['priority'];
            });

            $p += ($arr[0] == 0 ? 1 : 0);
        }

        $s =1 ; $s2=1;
        $l = $s <=> $s2;
        print($l."\n");
        print($p/1000000);
        print("\n");

    }

    public static function createDictoinayElement($did, $parent, $position, $type, $name)
    {
        $e = DictionaryElement::find()->where([
            "dictionary_id" => $did,
            "parent_id" => $parent,
            "title" => $name,
            "type" => $type
        ])->one();
        if (!$e) {
            $e = new DictionaryElement;
            $e->dictionary_id = $did;
            $e->parent_id = $parent;
            $e->title = $name;
            $e->type = $type;
            $e->position = $position;
            $e->save();
        }
        return $e->id;
    }

    public function actionLoadDictionary()
    {
        $did =  17;

        $catId = null;
        $subCatId = null;
        $position = 0;
        $file = fopen("sibur.txt", "r");
        while ($row = fgets($file)) {
            $row = explode("\t", $row);
            $catName = trim($row[0], "\n\r\t ");
            $catSubName = trim($row[1], "\n\r\t ");
            $itemName = trim($row[2], "\n\r\t ");
            if ($catName) {
                $catId = self::createDictoinayElement($did, 0, $position++, 'category', $catName);
            }
            if ($catSubName) {
                $subCatId = self::createDictoinayElement($did, $catId, $position++, 'category', $catSubName);
            }
            if ($itemName) {
                self::createDictoinayElement($did, $subCatId, $position++, 'element', $itemName);
            }
            print_r($catName . "\n");
        }
        fclose($file);
    }


    public function actionTranslateDictionary()
    {
        $did =  17;

        $catId = null;
        $subCatId = null;
        $position = 0;
        $file = fopen("sibur.txt", "r");
        $file2 = fopen("sibur_lang.txt", "r");
        $translate = [];
        while ($row = fgets($file)) {
            $row = explode("\t", $row);
            $catName = trim($row[0], "\n\r\t ");
            $catSubName = trim($row[1], "\n\r\t ");
            $itemName = trim($row[2], "\n\r\t ");

            $row=fgets($file2);
            //print_r($row); exit;
            $row = explode("\t", $row);
            $catName2 = trim($row[0], "\n\r\t ");
            $catSubName2 = trim($row[1], "\n\r\t ");
            $itemName2 = trim($row[2], "\n\r\t ");


            if ($catName) {
                if (isset($translate[$catName]) && $translate[$catName] != $catName2) die('7');
                $translate[$catName] = $catName2;
            }
            if ($catSubName) {
                if (isset($translate[$catSubName]) && $translate[$catSubName] != $catSubName2) die('7');
                $translate[$catSubName] = $catSubName2;
            }
            if ($itemName) {
                if (isset($translate[$itemName]) && $translate[$itemName] != $itemName2) die('7');
                $translate[$itemName] = $itemName2;
            }
        }

        //print_r($translate); exit;

        $langs = FoquzQuestionDetailLang::find()->where(["foquz_question_id"=>[1569488, 1569458, 1562819, 1569491, 1562822, 1569461]])->all();
        foreach ($langs as $lang) {
            if ($lang->detail_id) {
                $e = DictionaryElement::findOne($lang->detail_id);
                if ($e && $translate[$e->title]) {
                    $lang->question = $translate[$e->title];
                    $lang->save();
                }
            }
        }
        print_r(count($langs) . "\n");
        exit;
        print_r($translate);
        fclose($file);
        fclose($file2);
    }

    public function actionRemoveAnswerById($pollID, $answerID)
    {
        $answers = FoquzPollAnswer::find()->where(["foquz_poll_id" => $pollID])
            ->select([
                "created_at",
                "t" => "UNIX_TIMESTAMP(created_at)-10800",
            ])
            ->asArray()->all();
        foreach ($answers as $a) {
            print(strtotime($a["created_at"]) . "\t" . $a["t"] . "\t" . (strtotime($a["created_at"]) - $a["t"]) . "\n");
        }
        /*
        SELECT id FROM foquz_poll_answer a WHERE 
a.foquz_poll_id=158868 AND 4614439522=(UNIX_TIMESTAMP(a.created_at)-1557158285-10800)*(id%100)
         */
        //print_r($answers); exit;
        //print(mktime());
        //(strtotime($answer->created_at) - 1557158285) * ($answer->id%100)
    }

    /**
     * пересчет статистики пройденных ответов
     * @param  [type] $start [description]
     * @param  [type] $end   [description]
     * @return [type]        [description]
     */
    public function actionCountAnswers($start = null, $end = null)
    {
        $start = $start ? strtotime($start) : time() - 60 * 60 * 4.5;
        $start = date("Y-m-d H:i:s", $start);

        print($start . " ");

        //это все теперь не нужно, при условии что корректно работает счетчик при прохождении опросов

        /*$queryAll=FoquzPollAnswer::find()->select("foquz_poll_id")->distinct()->where([">=", "updated_at", $start])->andWhere(["status"=>["done", "in-progress"]]);
        if ($end) {
            $end = date("Y-m-d H:i:s", strtotime($end));
            print("$end");
            $queryAll->andWhere(["<=", "updated_at", $end]);
        }
        print("\n");
        $queryAll = $queryAll->column();
        print("polls=".count($queryAll)."\n");

        $stats = FoquzPollAnswer::find()->select("foquz_poll_id, count(*) as c")->where(["foquz_poll_id"=>$queryAll,  "status"=>["done", "in-progress"]])->asArray()->groupBy("foquz_poll_id")->all();
        $polls = [];
        foreach ($stats as $row) {
            $id = $row["foquz_poll_id"]; $c=$row["c"];
            $i = FoquzPoll::updateAll(["answers_count"=>$c], ["id"=>$id]);
            //перестали считаться компании потому что $i всегда 0, так как счетчик считается в прохождении
           // if ($i)
        $polls[]=$id;
            print("$id\t$c\t$i\n");
        }*/

        $polls = FoquzPoll::find()->where([">=", "last_answer_at", $start])->select("id")->column();

        //print(FoquzPoll::find()->where([">=", "last_answer_at", $start])->select("id")->createCommand()->getRawSql()); exit;
        $companies = FoquzPoll::find()->select("company_id")->distinct()->where(["id" => $polls])->column();
        $companies = Company::find()->where(["id" => $companies])->with("currentTariff")->all();
        print("companies=" . count($companies) . "\n");
        foreach ($companies as $c) {
            print("{$c->alias}\t");
            if ($c->currentTariff) {
                print("{$c->currentTariff->from}\t{$c->currentTariff->to}\t{$c->currentTariff->have_answers} => ");
                $c->currentTariff->updateHaveAnswers();
                print("{$c->currentTariff->have_answers}");
            }
            print("\n");
        }
    }

    public function actionCreateFilials()
    {
        $company_id = 1756;
        $file = fopen("filials.txt", "r");
        $fids = []; $cids = [];
        while ($row = fgets($file)) {
            $fName = trim($row, "\t\r\n ");

            //print($fName."\n\r");
            //continue;

            $filial = Filial::find()->where(['company_id' => $company_id, "name" => $fName])->one();
            if (!$filial) {
                $filial = new Filial;
                $filial->name = $fName;
                $filial->company_id = $company_id;
                $filial->save();
            }

            //$fids[] = $filial->id; $cids[]=$category->id;
            print_r($fName. "\t" . $filial->id . "\n");
            //exit;
        }

        exit;
        $filials = Filial::find()->where(['company_id' => $company_id])->andWhere(["not in", "id", $fids])->all();
        foreach ($filials as $filial) {
            print($filial->category_id."\t".$filial->name."\n");
            //$filial->delete(); !!!флпг
        }

        print_r(count($fids));
        fclose($file);
    }

    public function actionLiga()
    {
        $answers = FoquzPollAnswer::find()->where(['foquz_poll_id' => [70918, 70917], 'contact_id' => 438002])->all();

        foreach ($answers as $a) {
            $customer = null;
            $cf = $a->custom_fields;
            if ($cf) {
                $customer = trim(@json_decode($cf, true)['client_id']);
            }
            if ($customer) {
                print_r($customer . "\n");
                $customData = [];
                $foquzContact = FoquzContact::createContactByCompanyID($customer, [], 1055, $customData);
                $a->contact_id = $foquzContact->id;
                $a->save();
            } else {
                print("no\n");
            }
        }
    }


    public function actionCheckTariff()
    {
        $cts = CompanyTariff::find()->andWhere(["<>", "tariff_id", 1])
            ->select(["company_id"])
            ->asArray()
            ->distinct()
            ->all();


        $companies = Company::find()->where(["id" => ArrayHelper::getColumn($cts, "company_id")])->all();

        foreach ($companies as $c) {
            print($c->id . "\t" . $c->name . "\t" . $c->currentTariff->tariff_id . "\n");
        }
    }

    public function actionUpdateTariff()
    {
        $today = strtotime(date("Y-m-d"));

        $companies = Company::find()->all();
        foreach ($companies as $c) {
            if (!$c->currentTariff) {
                $company = $c;
                $companyTariff = new \app\modules\foquz\models\CompanyTariff(['company_id' => $company->id]);
                $companyTariff->from = $company->tariff_from ?? date('Y-m-d');
                $companyTariff->to = $company->tariff_to ?? date('Y-m-d', strtotime('+1 month'));
                $companyTariff->tariff_id = $company->tariff_id ?? 1;
                $companyTariff->answer_cost = $company->answer_cost ? $company->answer_cost : $companyTariff->tariff->answer_cost;
                $companyTariff->save();
                Invoice::getOrCreateInvoice($companyTariff);
                $c->load("currentTariff");
            }
            $c->currentTariff->updateHaveAnswers();


            if ($c->currentTariff->to <= date('Y-m-d')) {
                $newTariffRecord = new CompanyTariff([
                    'company_id' => $c->id,
                    'from' => date('Y-m-d'),
                    'to' => date('Y-m-d', strtotime('+30 days')),
                    'tariff_id' => $c->currentTariff->tariff_id,
                    'legal_entity_id' => $c->legal_entity_id,
                    'answer_cost' => $c->currentTariff->tariff->title != 'Базовый' ? ($c->answer_cost ? $c->answer_cost : $c->currentTariff->tariff->answer_cost) : null,
                ]);
                $newTariffRecord->save();
            }
        }
    }

    public function actionRefreshTariff()
    {
        $companies = Company::find()->all();
        foreach ($companies as $c) {
            if (!$c->currentTariff) {
                $lastTariff = CompanyTariff::find()
                    ->where(['company_id' => $c->id])
                    ->orderBy(['id' => SORT_DESC])
                    ->one();
                $newTariffRecord = new CompanyTariff([
                    'company_id' => $c->id,
                    'from' => date('Y-m-d'),
                    'to' => date('Y-m-d', strtotime('+30 days')),
                    'tariff_id' => $lastTariff->tariff_id
                ]);
                $newTariffRecord->save();
            }
        }
    }

    public function actionCopyTemplatesSupport()
    {
        $companyIDS = [454, 458, 450, 460, 459, 457, 453, 451, 448, 449, 455, 456, 452, 461];
        foreach ($companyIDS as $company_id) {
            $cf = CompanyStaff::find()->where(["company_id" => $company_id])->one();
            $company_user_id = $cf->user_id;

            $companyTemplatesFolder = FoquzPoll::find()->where([
                'company_id' => $company_id,
                'is_folder' => true,
                'name' => 'Шаблоны'
            ])->one();

            $templatePolls = FoquzPoll::find()
                ->where([
                    'status' => FoquzPoll::STATUS_NEW,
                    'is_tmp' => 0,
                    'id' => [13940],
                ])->orderBy('id')->all();

            print("$company_user_id, $company_id, $companyTemplatesFolder->id\n");
            $this->copyPolls($templatePolls, $company_user_id, $company_id, $companyTemplatesFolder->id);
        }
        exit;

        /*if(Yii::$app->params['template_company_id']) {
            $tCompany = Company::findOne(Yii::$app->params['template_company_id']);
            if(count($tCompany->designTemplates) > 0) {
                $tCompany->copyDesignTemplates($company_id);
            }
        }*/

        $id = [8565, 4611, 5903];


        $templatePolls = FoquzPoll::find()
            ->where([
                //  'company_id' => Yii::$app->params['template_company_id'],
                'status' => FoquzPoll::STATUS_NEW,
                'is_tmp' => 0,
                'id' => $id,
            ])->orderBy('id');


        //if(Yii::$app->params['template_company_folder_id']) {
        //   $templatePolls->andWhere(['folder_id' => Yii::$app->params['template_company_folder_id']]);
        // }

        $templatePolls = $templatePolls->all();
        $company_id = 32;

        $companyTemplatesFolder = FoquzPoll::find()->where([
            'company_id' => $company_id,
            'is_folder' => true,
            'name' => 'Тестирование'
        ])->one();

        $company_user_id = 272;

        $this->copyPolls($templatePolls, $company_user_id, $company_id, $companyTemplatesFolder->id);
    }


    public function actionCopyTemplates($company_id, $company_user_id)
    {
        if (Yii::$app->params['template_company_id']) {
            $tCompany = Company::findOne(Yii::$app->params['template_company_id']);
            if (count($tCompany->designTemplates) > 0) {
                $tCompany->copyDesignTemplates($company_id);
            }
        }

        $templatePolls = FoquzPoll::find()
            ->where([
                'company_id' => Yii::$app->params['template_company_id'],
                'status' => FoquzPoll::STATUS_NEW,
                'is_tmp' => 0,
                'deleted' => 0,
                'is_active' => 1,
            ])->orderBy('id');


        if (Yii::$app->params['template_company_folder_id']) {
            $templatePolls->andWhere(['folder_id' => Yii::$app->params['template_company_folder_id']]);
        }

        $templatePolls = $templatePolls->all();

        $companyTemplatesFolder = FoquzPoll::find()->where([
            'company_id' => $company_id,
            'is_folder' => true,
            'name' => 'Шаблоны'
        ])->one();

        if (!$companyTemplatesFolder) {
            $companyTemplatesFolder = new FoquzPoll([
                'created_at' => time(),
                'updated_at' => time(),
                'created_by' => $company_user_id,
                'updated_by' => $company_user_id,
                'name' => 'Шаблоны',
                'is_tmp' => false,
                'status' => FoquzPoll::STATUS_NEW,
                'company_id' => $company_id,
                'is_folder' => true,
            ]);
            $companyTemplatesFolder->save();
        }

        $this->copyPolls($templatePolls, $company_user_id, $company_id, $companyTemplatesFolder->id, true);
    }

    private function clearPoll($poll)
    {
        $unlink = false;

        $fullPath = \Yii::getAlias("@app/web/uploads/foquz/{$poll->id}");
        if (file_exists($fullPath)) {
            foreach ($poll->pages as $page) {
                if ($page->image_url) {
                    $img = \Yii::getAlias("@app/web{$page->image_url}");
                    if (file_exists($img)) {
                        if (preg_match("@/{$poll->id}/@", $img)) {
                            print("\t\tunlink page\t{$img}\n");
                            unlink($img);
                            $unlink = true;
                        } else {
                            //  print ("\t\t!!!!!unlink page\t{$img}\n");
                            //   exit;
                        }
                    }
                }
            }

            if ($poll->design) {
                $design = $poll->design;
                if ($design->background_image && $design->background_image !== FoquzPollDesign::DEFAULT_BG_IMAGE) {
                    $img = \Yii::getAlias("@app/web{$design->background_image}");
                    if (file_exists($img)) {
                        if (preg_match("@/{$poll->id}/@", $img)) {
                            print("\t\tunlink design\t{$img}\n");
                            $unlink = true;
                            unlink($img);
                        } else {
                            //print ("!!!!\t\tunlink design\t{$img}\n");
                            // $unlink = true;
                            //exit;
                        }
                    }
                }
                if ($design->logo_image && $design->logo_image !== FoquzPollDesign::DEFAULT_LOGO_IMAGE) {
                    $img = \Yii::getAlias("@app/web{$design->logo_image}");
                    if (file_exists($img)) {
                        if (preg_match("@/{$poll->id}/@", $img)) {
                            print("\t\tunlink design\t{$img}\n");
                            $unlink = true;
                            unlink($img);
                        } else {
                            //print ("!!!!\t\tunlink design\t{$img}\n");
                            //$unlink = true;

                        }
                    }
                }
            }

            foreach ($poll->foquzQuestions as $tQuestion) {
                if ($tQuestion->questionFiles) {
                    foreach ($tQuestion->questionFiles as $tqqf) {
                        if ($tqqf->attachment_type !== 'link' && stristr('img/', $tqqf->file_path) === false) {
                            $img = \Yii::getAlias("@app/web/{$tqqf->file_path}");
                            if (file_exists($img)) {
                                if (preg_match("@/{$tQuestion->id}/@", $img)) {
                                    print("\t\tunlink question\t{$img}\n");
                                    $unlink = true;
                                    unlink($img);
                                } else {
                                    // print ("!!!!\t\t{$tQuestion->id}\tunlink question\t{$img}\n");
                                    //$unlink = true;
                                }
                            }
                            $img = \Yii::getAlias("@app/web/{$tqqf->file_path}.jpg");
                            if (file_exists($img)) {
                                if (preg_match("@/{$tQuestion->id}/@", $img)) {
                                    print("\t\tunlink question\t{$img}\n");
                                    $unlink = true;
                                    unlink($img);
                                } else {
                                    // print ("!!!!\t\t{$tQuestion->id}\tunlink question\t{$img}\n");
                                    //  $unlink = true;
                                }
                            }
                        }
                    }
                }

                if ($tQuestion->foquzQuestionEndScreenLogos) {
                    foreach ($tQuestion->foquzQuestionEndScreenLogos as $tqqf) {
                        //die("!!!!!!!!!!!!!!!!!!!!!!!!!logos!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n");
                    }
                }

                //print_r($tQuestion); exit;

            }
        }

        if ($unlink) print("\tpoll\t{$poll->id}\t{$poll->name}\n");
    }

    private function clearFolder($company_id, $folder)
    {
        print("\tfolder\t{$folder->id}\t{$folder->name}\n");

        $polls = FoquzPoll::find()
            ->with(["pages", "design", "foquzQuestions" => function ($q) {
                $q->with(["foquzQuestionEndScreenLogos", "questionFiles"]);
            }])
            ->andWhere(["company_id" => $company_id])->andWhere(["company_id" => $company_id])->andWhere(["folder_id" => $folder->id])->andWhere(["is_folder" => 0])->all();
        foreach ($polls as $poll) {
            $this->clearPoll($poll);
        }

        $folders = FoquzPoll::find()->andWhere(["company_id" => $company_id])->andWhere(["company_id" => $company_id])->andWhere(["folder_id" => $folder->id])->andWhere(["is_folder" => 1])->all();
        foreach ($folders as $folder) {
            $this->clearFolder($company_id, $folder);
        }
    }
    public function actionClear($date)
    {
        $connection = Yii::$app->getDb();
        $command = $connection->createCommand("
            select c.id, c.alias, c.name, c.created_at, count(distinct a.id) cc from company c 
            left join foquz_poll p ON (c.id=p.company_id)
            left join foquz_poll_answer a ON (a.foquz_poll_id=p.id) 
            where c.created_at<:date and c.created_at>'2024-06-15'
            group by c.id 
            having count(distinct a.id)<5
            order by c.created_at", [':date' => $date]);
        $companies = $command->queryAll();
        foreach ($companies as $c) {
            $company_id = $c["id"];
            print("$company_id\t{$c['name']}\t{$c['created_at']}\t{$c['alias']}\t{$c['cc']}\n");
            $folder = FoquzPoll::find()->andWhere(["company_id" => $company_id])->andWhere(["company_id" => $company_id])->andWhere(["name" => "Шаблоны"])->andWhere(["is_folder" => 1])->one();
            if ($folder) {
                $this->clearFolder($company_id, $folder);
            }
        }
    }

    public function actionMatrix()
    {
        function correctS($a) {
            if (is_array($a[0])) {
                $a=correctS($a[0]);
            }
            return $a;
        }


        $connection = Yii::$app->getDb();
        $command = $connection->createCommand("
            SELECT i.id, i.answer FROM foquz_poll p 
            LEFT JOIN foquz_poll_answer a ON a.foquz_poll_id=p.id
            LEFT JOIN foquz_poll_answer_item i ON i.foquz_poll_answer_id=a.id
            LEFT JOIN foquz_question q ON i.foquz_question_id=q.id
            WHERE  a.`status` IN ('done', 'in-progress')  AND q.main_question_type=13 AND i.answer LIKE '%[[%'
            and i.created_at>'2023-10-01'
            ORDER BY i.created_at DESC 
            ");
        $rows = $command->queryAll();
        foreach ($rows as $row) {
            $id = $row["id"];
            $answer = json_decode($row["answer"], true);
            foreach ($answer as $k => $a) {
                $answer[$k]=correctS($a);
            }
            print_r($row);
            $answer = json_encode($answer);
            print("\n".$answer);
            FoquzPollAnswerItem::updateAll(["answer"=>$answer], ["id"=>$id]);
        }

    }

    private function copyPages($pages, $newPollId)
    {
        foreach ($pages as $tPage) {
            $page = new FoquzPollPage();
            $page->attributes = $tPage->attributes;
            $page->created_at = date('d.m.Y H:i:s');
            $page->updated_at = date('d.m.Y H:i:s');
            $page->foquz_poll_id = $newPollId;
            if ($tPage->image_url) {
                $basePath = "uploads/foquz/" . $newPollId;
                $fullPath = \Yii::getAlias("@app/web/{$basePath}");

                if (false === file_exists($fullPath)) {
                    mkdir($fullPath, 0777, true);
                }

                $page->image_url = str_replace($tPage->foquz_poll_id, $newPollId, $tPage->image_url);

                try {
                    copy(\Yii::getAlias("@app/web{$tPage->image_url}"), \Yii::getAlias("@app/web{$page->image_url}"));
                } catch (\Exception $e) {
                    echo "Can't copy image for poll " . $newPollId . "\n";
                    echo $e->getMessage() . "\n";
                }
            }
            $page->save();

            if ($tPage->socNetworksOptions) {
                $sno = new FoquzPollPageSocialNetworksOptions();
                $sno->attributes = $tPage->socNetworksOptions->attributes;
                $sno->foquz_poll_page_id = $page->id;
                $sno->save();
            }
        }
    }

    private function copyDesign($design, $newPollId)
    {
        $newPollDesign = new FoquzPollDesign();
        $newPollDesign->attributes = $design->attributes;
        $newPollDesign->foquz_poll_id = $newPollId;
        if ($design->background_image !== FoquzPollDesign::DEFAULT_BG_IMAGE) {
            $fullPath = \Yii::getAlias("@app/web/uploads/foquz/poll/" . $newPollId . "/bg");
            if (false === file_exists($fullPath)) {
                mkdir($fullPath, 0777, true);
            }
            $newPollDesign->background_image = str_replace($design->foquz_poll_id, $newPollId, $design->background_image);
            try {
                copy(\Yii::getAlias("@app/web{$design->background_image}"), \Yii::getAlias("@app/web{$newPollDesign->background_image}"));
            } catch (\Exception $e) {
                echo "Can't copy background design image for poll " . $newPollId . "\n";
            }
        }
        if ($design->logo_image !== FoquzPollDesign::DEFAULT_LOGO_IMAGE) {
            $fullPath = \Yii::getAlias("@app/web/uploads/foquz/poll/" . $newPollId . "/logo");
            if (false === file_exists($fullPath)) {
                mkdir($fullPath, 0777, true);
            }
            $newPollDesign->logo_image = str_replace($design->foquz_poll_id, $newPollId, $design->logo_image);
            try {
                copy(\Yii::getAlias("@app/web{$design->logo_image}"), \Yii::getAlias("@app/web{$newPollDesign->logo_image}"));
            } catch (\Exception $e) {
                echo "Can't copy logo design image for poll " . $newPollId . "\n";
            }
        }
        $newPollDesign->save();
    }

    private function copyPolls($polls, $company_user_id, $company_id, $folder_id, $newCompany = false)
    {
        /** @var FoquzPoll $tPoll */
        foreach ($polls as $tPoll) {
            $companyPoll = new FoquzPoll(['createFirstQuestion' => false]);
            $companyPoll->attributes = $tPoll->attributes;
            $companyPoll->created_at = time();
            $companyPoll->updated_at = time();
            $companyPoll->created_by = $company_user_id;
            $companyPoll->updated_by = $company_user_id;
            $companyPoll->key = null;
            $companyPoll->company_id = $company_id;
            $companyPoll->is_qr_code = false;
            $companyPoll->folder_id = $folder_id;
            $companyPoll->notification_script_id = null;
            $companyPoll->is_folder = $tPoll->is_folder;
            $companyPoll->is_template = true;
            $companyPoll->is_published = false;
            $companyPoll->goal_text = null;
            $companyPoll->answers_count = 0;
            $companyPoll->sent_answers_count = 0;
            $companyPoll->opened_answers_count = 0;
            $companyPoll->in_progress_answers_count = 0;
            $companyPoll->filled_answers_count = 0;
            $companyPoll->show_foquz_link = 1;
            $companyPoll->save();

            if($companyPoll->is_folder) {
                $this->copyPolls($tPoll->getFolders()->andWhere(['deleted'=>0, 'is_active'=>1, 'is_tmp'=>0])->andWhere(["<>", "status", FoquzPoll::STATUS_ARCHIVE])->all(), $company_user_id, $company_id, $companyPoll->id);
            }

            if ($tPoll->pages)
                $this->copyPages($tPoll->pages, $companyPoll->id);

            if ($tPoll->design)
                $this->copyDesign($tPoll->design, $companyPoll->id);

            if ($tPoll->scoresInterpretationRanges) {
                $tPoll->copyScoresInterpretationRanges($companyPoll->id);
            }

            $tPoll->copyQuestions($company_user_id, $companyPoll->id, $newCompany);
            sleep(1);
        }
    }

    /**
     * @throws \app\components\exceptions\AddCompanyTariffException
     */
    public function actionIssueInvoice()
    {
        $dateTime = new \DateTime();
        $dateTime->modify('first day of next month');

        //Ищем компании которым уже выставили счет
        $invoiceExistCompanies = CompanyTariff::find()
            ->where(['=', 'from', $dateTime->format('Y-m-d')])
            ->andWhere(['=', 'to', $dateTime->format('Y-m-t')])
            ->andWhere(['=', 'payment_type', CompanyTariff::PAYMENT_TYPE_PREPAYMENT])
            ->select(['company_id'])
            ->column();

        $companies = Company::find()
            ->where(['=', 'deleted', Company::NOT_DELETED])
            ->andWhere(['NOT IN', 'id', $invoiceExistCompanies])
            ->all();

        /** @var Company $company */
        foreach ($companies as $company) {
            try {
                if ($company->currentTariff->tariff_id === Tariff::DEFAULT) {
                    continue;
                }
                $addCompanyTariffDto = new AddCompanyTariffDto();
                $addCompanyTariffDto->companyId = $company->id;
                $addCompanyTariffDto->tariffId = $company->currentTariff->tariff_id;
                $addCompanyTariffDto->from = $dateTime->format('Y-m-d');
                $addCompanyTariffDto->to = $dateTime->format('Y-m-t');
                $addCompanyTariffDto->paymentType = CompanyTariff::PAYMENT_TYPE_PREPAYMENT;

                // TODO: Через сервис бы это все....
                $companyTariff = CompanyTariffHelper::addCompanyTariff($addCompanyTariffDto);
                InvoiceHelper::createFromCompanyTariff($companyTariff);
            } catch (\Exception $exception) {
                // TODO: Логирование, оповещение
            }
        }
    }

    /**
     * @throws \app\components\exceptions\AddCompanyTariffException
     */
    public function actionIssueInvoiceSupplement()
    {
        $dateTime = new \DateTime();
        $dateTime->modify('first day of next month');

        //Ищем компании которым уже выставили счет
        $invoiceExistCompanies = CompanyTariff::find()
            ->where(['=', 'from', $dateTime->format('Y-m-d')])
            ->andWhere(['=', 'to', $dateTime->format('Y-m-t')])
            ->andWhere(['=', 'payment_type', CompanyTariff::PAYMENT_TYPE_SUPPLEMENT])
            ->select(['company_id'])
            ->column();

        $companies = Company::find()
            ->where(['=', 'deleted', Company::NOT_DELETED])
            ->andWhere(['NOT IN', 'id', $invoiceExistCompanies])
            ->all();

        foreach ($companies as $company) {
            try {
                $tariff = $company->tariff;
                if ($tariff->id === Tariff::DEFAULT) {
                    continue;
                }
                $lastCompanyTariff = CompanyTariff::find()
                    ->andWhere(['=', 'company_id', $company->id])
                    ->andWhere(['like', 'from', (new \DateTime())->format('Y-m-') . '%', false])
                    ->one();

                $unpaidAnswersCost = 0;
                /* @var Tariff $tariff  */
                if ($lastCompanyTariff->have_answers > $tariff->answers) {
                    $unpaidAnswersCost = ($lastCompanyTariff->have_answers - $tariff->answers) * $tariff->answer_cost;
                }

                if (0 >= $unpaidAnswersCost) {
                    continue;
                }

                $addCompanyTariffDto = new AddCompanyTariffDto();
                $addCompanyTariffDto->companyId = $company->id;
                $addCompanyTariffDto->tariffId = $tariff;
                $addCompanyTariffDto->from = $dateTime->format('Y-m-d');
                $addCompanyTariffDto->to = $dateTime->format('Y-m-t');
                $addCompanyTariffDto->paymentType = CompanyTariff::PAYMENT_TYPE_SUPPLEMENT;
                $addCompanyTariffDto->cost = $unpaidAnswersCost;

                // TODO: Через сервис бы это все....
                $companyTariff = CompanyTariffHelper::addCompanyTariff($addCompanyTariffDto);
                InvoiceHelper::createFromCompanyTariff($companyTariff, $unpaidAnswersCost);
            } catch (\Exception $exception) {
                // TODO: Логирование, оповещение
            }
        }
    }


    public function actionDeleteCompanies($fileName) 
    {
        $file = fopen($fileName, "r");
        while ($row=fgets($file)) {
            $row = intval($row);
            if ($row)  {
                print("\n\n\n".$row."\n");
                $this->actionDeleteCompany($row);
            }
        }
        fclose($file);
    }

    public function actionDeleteCompany($companyID)
    {
        ini_set('memory_limit', '2048M');
        print 'Start delete records for company ' . $companyID . "\n";
        $start = microtime(true);
        $companyUsersID = CompanyStaff::find()->where(['company_id' => $companyID])->select('user_id')->column();
        $pollsID = FoquzPoll::find()->where(['company_id' => $companyID])->select('id')->column();
        $questionsID = FoquzQuestion::find()->where(['poll_id' => $pollsID])->orWhere(['point_id' => FoquzPointItem::find()->where(['company_id' => $companyID])->select('id')])->select('id')->column();
        print 'Start delete records from table ' . ApiRequestLog::tableName() . "\n";
        $count = ApiRequestLog::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . ApiRequestLog::tableName() . "\n";
        print 'Start delete records from table ' . ApiSendPoll::tableName() . "\n";
        $count = ApiSendPoll::deleteAll(['contact_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . ApiSendPoll::tableName() . "\n";
        print 'Start delete records from table auth_assignment' . "\n";
        if (!empty($companyUsersID)) {
            $count = Yii::$app->db->createCommand('DELETE FROM auth_assignment WHERE user_id IN (' . implode(',', $companyUsersID) . ')')->execute();
        } else {
            $count = 0;
        }
        print 'Deleted ' . $count . ' records from table auth_assignment' . "\n";
        print 'Start delete records from table ' . AutoPollRequest::tableName() . "\n";
        $count = AutoPollRequest::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . AutoPollRequest::tableName() . "\n";
        print 'Start delete records from table ' . BonusAccount::tableName() . "\n";
        $count = BonusAccount::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . BonusAccount::tableName() . "\n";
        print 'Start delete records from table ' . BrowserNotificationUserDevice::tableName() . "\n";
        $count = BrowserNotificationUserDevice::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . BrowserNotificationUserDevice::tableName() . "\n";
        print 'Start delete records from table ' . ChannelDiscountBlockDiscount::tableName() . "\n";
        $count = ChannelDiscountBlockDiscount::deleteAll(['channel_db_id' => ChannelDiscountBlock::find()->where(['channel_id' => Channel::find()->where(['poll_id' => $pollsID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ChannelDiscountBlockDiscount::tableName() . "\n";
        print 'Start delete records from table ' . ChannelDiscountBlock::tableName() . "\n";
        $count = ChannelDiscountBlock::deleteAll(['channel_id' => Channel::find()->where(['poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ChannelDiscountBlock::tableName() . "\n";
        print 'Start delete records from table ' . ChannelProductBlockProduct::tableName() . "\n";
        $count = ChannelProductBlockProduct::deleteAll(['channel_pb_id' => ChannelProductBlock::find()->where(['channel_id' => Channel::find()->where(['poll_id' => $pollsID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ChannelProductBlockProduct::tableName() . "\n";
        print 'Start delete records from table ' . ChannelProductBlock::tableName() . "\n";
        $count = ChannelProductBlock::deleteAll(['channel_id' => Channel::find()->where(['poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ChannelProductBlock::tableName() . "\n";
        print 'Start delete records from table ' . Complain::tableName() . "\n";
        $count = Complain::deleteAll(['poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . Complain::tableName() . "\n";
        print 'Start delete records from table ' . ContactAdditionalFieldValue::tableName() . "\n";
        $count = ContactAdditionalFieldValue::deleteAll(['additional_field_id' => ContactAdditionalField::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ContactAdditionalFieldValue::tableName() . "\n";
        print 'Start delete records from table ' . ContactAdditionalField::tableName() . "\n";
        $count = ContactAdditionalField::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . ContactAdditionalField::tableName() . "\n";
        print 'Start delete records from table ' . EditorFolder::tableName() . "\n";
        $count = EditorFolder::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . EditorFolder::tableName() . "\n";
        print 'Start delete records from table ' . Emails::tableName() . "\n";
        $count = Emails::deleteAll(['poll_id' => Poll::find()->where(['order_id' => Order::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . Emails::tableName() . "\n";
        print 'Start delete records from table ' . ProcessingEmployeePost::tableName() . "\n";
        $count = ProcessingEmployeePost::deleteAll(['employee_post_id' => EmployeePost::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ProcessingEmployeePost::tableName() . "\n";
        print 'Start delete records from table ' . FineEmployeePost::tableName() . "\n";
        $count = FineEmployeePost::deleteAll(['employee_post_id' => EmployeePost::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FineEmployeePost::tableName() . "\n";
        print 'Start delete records from table ' . EmployeePost::tableName() . "\n";
        $count = EmployeePost::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . EmployeePost::tableName() . "\n";
        print 'Start delete records from table ' . ExportLog::tableName() . "\n";
        $count = ExportLog::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . ExportLog::tableName() . "\n";
        print 'Start delete records from table ' . Export::tableName() . "\n";
        $count = Export::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . Export::tableName() . "\n";
        print 'Start delete records from table ' . ExternalResource::tableName() . "\n";
        $count = ExternalResource::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . ExternalResource::tableName() . "\n";
        print 'Start delete records from table ' . FilialEmployeeSettings::tableName() . "\n";
        $count = FilialEmployeeSettings::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . FilialEmployeeSettings::tableName() . "\n";
        print 'Start delete records from table ' . FilialEmployeeUserFilial::tableName() . "\n";
        $count = FilialEmployeeUserFilial::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . FilialEmployeeUserFilial::tableName() . "\n";
        print 'Start delete records from table ' . FilialPollKey::tableName() . "\n";
        $count = FilialPollKey::deleteAll(['filial_id' => Filial::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FilialPollKey::tableName() . "\n";
        print 'Start delete records from table ' . FilialReviewReply::tableName() . "\n";
        $count = FilialReviewReply::deleteAll(['filial_review_id' => FilialReview::find()->where(['filial_id' => Filial::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FilialReviewReply::tableName() . "\n";
        print 'Start delete records from table ' . FilialReview::tableName() . "\n";
        $count = FilialReview::deleteAll(['filial_id' => Filial::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FilialReview::tableName() . "\n";
        print 'Start delete records from table ' . FoquzCompanyTagCondition::tableName() . "\n";
        $count = FoquzCompanyTagCondition::deleteAll(['tag_id' => FoquzCompanyTag::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzCompanyTagCondition::tableName() . "\n";
        print 'Start delete records from table ' . FoquzCompanyTagTask::tableName() . "\n";
        $count = FoquzCompanyTagTask::deleteAll(['tag_id' => FoquzCompanyTag::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzCompanyTagTask::tableName() . "\n";
        print 'Start delete records from table ' . FoquzCompanyTag::tableName() . "\n";
        $count = FoquzCompanyTag::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzCompanyTag::tableName() . "\n";
        print 'Start delete records from table ' . FoquzComplaintFile::tableName() . "\n";
        $count = FoquzComplaintFile::deleteAll(['foquz_poll_answer_id' => FoquzPollAnswer::find()->where(['foquz_poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzComplaintFile::tableName() . "\n";
        print 'Start delete records from table ' . FoquzComplaint::tableName() . "\n";
        $count = FoquzComplaint::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzComplaint::tableName() . "\n";
        print 'Start delete records from table ' . FoquzContactComputedFieldsFavoriteDish::tableName() . "\n";
        $count = FoquzContactComputedFieldsFavoriteDish::deleteAll(['foquz_contact_computed_fields_id' => FoquzContactComputedField::find()->where(['contact_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzContactComputedFieldsFavoriteDish::tableName() . "\n";
        print 'Start delete records from table ' . FoquzContactComputedField::tableName() . "\n";
        $count = FoquzContactComputedField::deleteAll(['contact_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzContactComputedField::tableName() . "\n";
        print 'Start delete records from table ' . FoquzContactEndpointClient::tableName() . "\n";
        $count = FoquzContactEndpointClient::deleteAll(['foquz_contact_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzContactEndpointClient::tableName() . "\n";
        print 'Start delete records from table ' . FoquzContactFilial::tableName() . "\n";
        $count = FoquzContactFilial::deleteAll(['contact_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzContactFilial::tableName() . "\n";
        print 'Start delete records from table ' . FoquzContactTag::tableName() . "\n";
        $count = FoquzContactTag::deleteAll(['contact_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzContactTag::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPointSelected::tableName() . "\n";
        $count = FoquzPointSelected::deleteAll(['foquz_point_item_id' => FoquzPointItem::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPointSelected::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPointCondition::tableName() . "\n";
        $count = FoquzPointCondition::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPointCondition::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPointTypeRelation::tableName() . "\n";
        $count = FoquzPointTypeRelation::deleteAll(['foquz_point_item_id' => FoquzPointItem::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPointTypeRelation::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPointItemHasBusinessType::tableName() . "\n";
        $count = FoquzPointItemHasBusinessType::deleteAll(['item_id' => FoquzPointItem::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPointItemHasBusinessType::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerHiddenQuestion::tableName() . "\n";
        $count = FoquzPollAnswerHiddenQuestion::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerHiddenQuestion::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerItemFile::tableName() . "\n";
        $count = FoquzPollAnswerItemFile::deleteAll(['foquz_poll_answer_item_id' => FoquzPollAnswerItem::find()->where(['foquz_question_id' => $questionsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerItemFile::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerItem::tableName() . "\n";
        $count = FoquzPollAnswerItem::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerItem::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerProcessingFile::tableName() . "\n";
        $count = FoquzPollAnswerProcessingFile::deleteAll(['foquz_poll_answer_processing_id' => FoquzPollAnswerProcessing::find()->where(['poll_answer_id' => FoquzPollAnswer::find()->where(['foquz_poll_id' => $pollsID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerProcessingFile::tableName() . "\n";
        print 'Start delete records from table ' . NotificationAnswerProcessingChannel::tableName() . "\n";
        $count = NotificationAnswerProcessingChannel::deleteAll(['answer_processing_id' => FoquzPollAnswerProcessing::find()->where(['poll_answer_id' => FoquzPollAnswer::find()->where(['foquz_poll_id' => $pollsID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . NotificationAnswerProcessingChannel::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerProcessing::tableName() . "\n";
        Feedback::updateAll(['processing_id' => null], ['processing_id' => FoquzPollAnswerProcessing::find()->where(['OR', ['poll_answer_id' => FoquzPollAnswer::find()->where(['foquz_poll_id' => $pollsID])->select('id')], ['request_project_answer_id' => Answer::find()->where(['request_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')])->select('id')]])->select('id')]);
        $count = FoquzPollAnswerProcessing::deleteAll(['OR', ['poll_answer_id' => FoquzPollAnswer::find()->where(['foquz_poll_id' => $pollsID])->select('id')], ['request_project_answer_id' => Answer::find()->where(['request_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')])->select('id')]]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerProcessing::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerProcessingCompensation::tableName() . "\n";
        $count = FoquzPollAnswerProcessingCompensation::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerProcessingCompensation::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerProcessingEmployee::tableName() . "\n";
        $count = FoquzPollAnswerProcessingEmployee::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerProcessingEmployee::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerProcessingFine::tableName() . "\n";
        $count = FoquzPollAnswerProcessingFine::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerProcessingFine::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerProcessingFineCategory::tableName() . "\n";
        $count = FoquzPollAnswerProcessingFineCategory::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerProcessingFineCategory::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswerProcessingReason::tableName() . "\n";
        $count = FoquzPollAnswerProcessingReason::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswerProcessingReason::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollCode::tableName() . "\n";
        $count = FoquzPollCode::deleteAll(['poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollCode::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollDesign::tableName() . "\n";
        $count = FoquzPollDesign::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollDesign::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollDesignTemplate::tableName() . "\n";
        $count = FoquzPollDesignTemplate::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollDesignTemplate::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollDishScore::tableName() . "\n";
        $count = FoquzPollDishScore::deleteAll(['dish_id' => Dish::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollDishScore::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollDisplayPageQuestion::tableName() . "\n";
        $count = FoquzPollDisplayPageQuestion::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollDisplayPageQuestion::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollDisplayPageQuestion::tableName() . "\n";
        $count = FoquzPollDisplayPageQuestion::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollDisplayPageQuestion::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollQuestionsLogic::tableName() . "\n";
        $count = FoquzPollQuestionsLogic::deleteAll(['OR', ['question_id' => $questionsID], ['question_id' => $questionsID]]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollQuestionsLogic::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollDisplayPage::tableName() . "\n";
        $count = FoquzPollDisplayPage::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollDisplayPage::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollDisplaySetting::tableName() . "\n";
        $count = FoquzPollDisplaySetting::deleteAll(['poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollDisplaySetting::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollFavorite::tableName() . "\n";
        $count = FoquzPollFavorite::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollFavorite::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollKey::tableName() . "\n";
        $count = FoquzPollKey::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollKey::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollMailingCondition::tableName() . "\n";
        $count = FoquzPollMailingCondition::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollMailingCondition::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollMailingContactLinkStat::tableName() . "\n";
        $count = FoquzPollMailingContactLinkStat::deleteAll(['mailing_id' => FoquzPollMailingList::find()->where(['foquz_poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollMailingContactLinkStat::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollMailingListSend::tableName() . "\n";
        $count = FoquzPollMailingListSend::deleteAll(['answer_id' => FoquzPollAnswer::find()->where(['foquz_poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollMailingListSend::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollMailingListContact::tableName() . "\n";
        $count = FoquzPollMailingListContact::deleteAll(['mailing_list_id' => FoquzPollMailingList::find()->where(['foquz_poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollMailingListContact::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollMailingList::tableName() . "\n";
        $count = FoquzPollMailingList::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollMailingList::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollNoOrderAsUsualTriggerSettings::tableName() . "\n";
        $count = FoquzPollNoOrderAsUsualTriggerSettings::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollNoOrderAsUsualTriggerSettings::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollPageSocialNetworksOptions::tableName() . "\n";
        $count = FoquzPollPageSocialNetworksOptions::deleteAll(['foquz_poll_page_id' => FoquzPollPage::find()->where(['foquz_poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollPageSocialNetworksOptions::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollPage::tableName() . "\n";
        $count = FoquzPollPage::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollPage::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollQuestionViewLogic::tableName() . "\n";
        $count = FoquzPollQuestionViewLogic::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollQuestionViewLogic::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollStatFilterSettings::tableName() . "\n";
        $count = FoquzPollStatFilterSettings::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollStatFilterSettings::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollStatWidgetKey::tableName() . "\n";
        $count = FoquzPollStatWidgetKey::deleteAll(['poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollStatWidgetKey::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollStatsLink::tableName() . "\n";
        $count = FoquzPollStatsLink::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollStatsLink::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollWidget::tableName() . "\n";
        $count = FoquzPollWidget::deleteAll(['poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollWidget::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionAddressCodes::tableName() . "\n";
        $count = FoquzQuestionAddressCodes::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionAddressCodes::tableName() . "\n";
        print 'Start delete records from table ' . RecipientQuestionDetail::tableName() . "\n";
        $count = RecipientQuestionDetail::deleteAll(['recipient_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . RecipientQuestionDetail::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionDetailLang::tableName() . "\n";
        $count = FoquzQuestionDetailLang::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionDetailLang::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionMatrixElementVariantLang::tableName() . "\n";
        $count = FoquzQuestionMatrixElementVariantLang::deleteAll(['poll_lang_id' => FoquzPollLang::find()->where(['foquz_poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionMatrixElementVariantLang::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionMatrixElementVariant::tableName() . "\n";
        $count = FoquzQuestionMatrixElementVariant::deleteAll(['matrix_element_id' => FoquzQuestionMatrixElement::find()->where(['foquz_question_id' => $questionsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionMatrixElementVariant::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionMatrixElementLang::tableName() . "\n";
        $count = FoquzQuestionMatrixElementLang::deleteAll(['matrix_element_id' => FoquzQuestionMatrixElement::find()->where(['foquz_question_id' => $questionsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionMatrixElementLang::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionMatrixElement::tableName() . "\n";
        $count = FoquzQuestionMatrixElement::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionMatrixElement::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionDetail::tableName() . "\n";
        $count = FoquzQuestionDetail::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionDetail::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionDifferentialRow::tableName() . "\n";
        $count = FoquzQuestionDifferentialRow::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionDifferentialRow::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionEndScreenLogo::tableName() . "\n";
        $count = FoquzQuestionEndScreenLogo::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionEndScreenLogo::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionFileLang::tableName() . "\n";
        $count = FoquzQuestionFileLang::deleteAll(['foquz_question_file_id' => FoquzQuestionFile::find()->where(['question_id' => $questionsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionFileLang::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionFile::tableName() . "\n";
        $count = FoquzQuestionFile::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionFile::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionFormFieldLang::tableName() . "\n";
        $count = FoquzQuestionFormFieldLang::deleteAll(['form_field_id' => FoquzQuestionFormField::find()->where(['question_id' => $questionsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionFormFieldLang::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionFormField::tableName() . "\n";
        $count = FoquzQuestionFormField::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionFormField::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionIntermediateBlockSettingSocNetworks::tableName() . "\n";
        $count = FoquzQuestionIntermediateBlockSettingSocNetworks::deleteAll(['intermediate_block_id' => FoquzQuestionIntermediateBlockSetting::find()->where(['question_id' => $questionsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionIntermediateBlockSettingSocNetworks::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionIntermediateBlockSettingLang::tableName() . "\n";
        $count = FoquzQuestionIntermediateBlockSettingLang::deleteAll(['setting_id' => FoquzQuestionIntermediateBlockSetting::find()->where(['question_id' => $questionsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionIntermediateBlockSettingLang::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionIntermediateBlockSetting::tableName() . "\n";
        $count = FoquzQuestionIntermediateBlockSetting::deleteAll(['question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionIntermediateBlockSetting::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionLang::tableName() . "\n";
        $count = FoquzQuestionLang::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionLang::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionNpsRatingSetting::tableName() . "\n";
        $count = FoquzQuestionNpsRatingSetting::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionNpsRatingSetting::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionPrioritySettings::tableName() . "\n";
        $count = FoquzQuestionPrioritySettings::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionPrioritySettings::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionRightAnswer::tableName() . "\n";
        $count = FoquzQuestionRightAnswer::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionRightAnswer::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionScaleRatingSetting::tableName() . "\n";
        $count = FoquzQuestionScaleRatingSetting::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionScaleRatingSetting::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionSemDifSetting::tableName() . "\n";
        $count = FoquzQuestionSemDifSetting::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionSemDifSetting::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionSmile::tableName() . "\n";
        $count = FoquzQuestionSmile::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionSmile::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestionStarRatingOptions::tableName() . "\n";
        $count = FoquzQuestionStarRatingOptions::deleteAll(['foquz_question_id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestionStarRatingOptions::tableName() . "\n";
        print 'Start delete records from table ' . FoquzQuestion::tableName() . "\n";
        $count = FoquzQuestion::deleteAll(['id' => $questionsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzQuestion::tableName() . "\n";
        print 'Start delete records from table ' . GlobalEmailSettingsDomainVerificationTable::tableName() . "\n";
        $count = GlobalEmailSettingsDomainVerificationTable::deleteAll(['global_email_setting_id' => GlobalEmailSettings::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . GlobalEmailSettingsDomainVerificationTable::tableName() . "\n";
        print 'Start delete records from table ' . GlobalEmailSettings::tableName() . "\n";
        $count = GlobalEmailSettings::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . GlobalEmailSettings::tableName() . "\n";
        print 'Start delete records from table ' . GlobalPushSettings::tableName() . "\n";
        $count = GlobalPushSettings::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . GlobalPushSettings::tableName() . "\n";
        print 'Start delete records from table ' . GlobalSmsSettings::tableName() . "\n";
        $count = GlobalSmsSettings::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . GlobalSmsSettings::tableName() . "\n";
        print 'Start delete records from table ' . GlobalTelegramSettings::tableName() . "\n";
        $count = GlobalTelegramSettings::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . GlobalTelegramSettings::tableName() . "\n";
        print 'Start delete records from table ' . GlobalViberSettings::tableName() . "\n";
        $count = GlobalViberSettings::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . GlobalViberSettings::tableName() . "\n";
        print 'Start delete records from table ' . GoogleOauthToken::tableName() . "\n";
        $count = GoogleOauthToken::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . GoogleOauthToken::tableName() . "\n";
        print 'Start delete records from table ' . GoogleSheetsParams::tableName() . "\n";
        $count = GoogleSheetsParams::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . GoogleSheetsParams::tableName() . "\n";
        print 'Start delete records from table ' . HrmIntegration::tableName() . "\n";
        $count = HrmIntegration::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . HrmIntegration::tableName() . "\n";
        print 'Start delete records from table ' . Invoice::tableName() . "\n";
        $count = Invoice::deleteAll(['company_tariff_id' => CompanyTariff::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . Invoice::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelDiscountBlockDiscount::tableName() . "\n";
        $count = MailingChannelDiscountBlockDiscount::deleteAll(['mailing_channel_db_id' => MailingChannelDiscountBlock::find()->where(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelDiscountBlockDiscount::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelDiscountBlock::tableName() . "\n";
        $count = MailingChannelDiscountBlock::deleteAll(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelDiscountBlock::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelProductBlockProduct::tableName() . "\n";
        $count = MailingChannelProductBlockProduct::deleteAll(['mailing_channel_pb_id' => MailingChannelProductBlock::find()->where(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelProductBlockProduct::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelProductBlock::tableName() . "\n";
        $count = MailingChannelProductBlock::deleteAll(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelProductBlock::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelRepeatDiscountBlockDiscount::tableName() . "\n";
        $count = MailingChannelRepeatDiscountBlockDiscount::deleteAll(['repeat_db_id' => MailingChannelRepeatDiscountBlock::find()->where(['mailing_channel_repeat_id' => MailingChannelRepeat::find()->where(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelRepeatDiscountBlockDiscount::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelRepeatDiscountBlock::tableName() . "\n";
        $count = MailingChannelRepeatDiscountBlock::deleteAll(['mailing_channel_repeat_id' => MailingChannelRepeat::find()->where(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelRepeatDiscountBlock::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelRepeatProductBlockProduct::tableName() . "\n";
        $count = MailingChannelRepeatProductBlockProduct::deleteAll(['repeat_pb_id' => MailingChannelRepeatProductBlock::find()->where(['mailing_channel_repeat_id' => MailingChannelRepeat::find()->where(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelRepeatProductBlockProduct::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelRepeatProductBlock::tableName() . "\n";
        $count = MailingChannelRepeatProductBlock::deleteAll(['mailing_channel_repeat_id' => MailingChannelRepeat::find()->where(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelRepeatProductBlock::tableName() . "\n";
        print 'Start delete records from table ' . MailingCode::tableName() . "\n";
        $count = MailingCode::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingCode::tableName() . "\n";
        print 'Start delete records from table ' . MailingContactLinkStat::tableName() . "\n";
        $count = MailingContactLinkStat::deleteAll(['mailing_list_id' => MailingList::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingContactLinkStat::tableName() . "\n";
        print 'Start delete records from table ' . MailingException::tableName() . "\n";
        $count = MailingException::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingException::tableName() . "\n";
        print 'Start delete records from table ' . MailingFavorite::tableName() . "\n";
        $count = MailingFavorite::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingFavorite::tableName() . "\n";
        print 'Start delete records from table ' . MailingFilterSettings::tableName() . "\n";
        $count = MailingFilterSettings::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . MailingFilterSettings::tableName() . "\n";
        print 'Start delete records from table ' . MailingListSend::tableName() . "\n";
        $count = MailingListSend::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingListSend::tableName() . "\n";
        print 'Start delete records from table ' . MailingListContact::tableName() . "\n";
        $count = MailingListContact::deleteAll(['mailing_list_id' => MailingList::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingListContact::tableName() . "\n";
        print 'Start delete records from table ' . MailingList::tableName() . "\n";
        $count = MailingList::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingList::tableName() . "\n";
        print 'Start delete records from table ' . MailingNoOrderAsUsualTriggerSettings::tableName() . "\n";
        $count = MailingNoOrderAsUsualTriggerSettings::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingNoOrderAsUsualTriggerSettings::tableName() . "\n";
        print 'Start delete records from table ' . MailingProhibition::tableName() . "\n";
        $count = MailingProhibition::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingProhibition::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannelRepeat::tableName() . "\n";
        $count = MailingChannelRepeat::deleteAll(['mailing_channel_id' => MailingChannel::find()->where(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannelRepeat::tableName() . "\n";
        print 'Start delete records from table ' . MailingChannel::tableName() . "\n";
        $count = MailingChannel::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingChannel::tableName() . "\n";
        print 'Start delete records from table ' . MailingsMailingCondition::tableName() . "\n";
        $count = MailingsMailingCondition::deleteAll(['mailing_id' => Mailing::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . MailingsMailingCondition::tableName() . "\n";
        print 'Start delete records from table ' . Mailing::tableName() . "\n";
        $count = Mailing::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Mailing::tableName() . "\n";
        print 'Start delete records from table ' . NotificationScriptChannelRepeat::tableName() . "\n";
        $count = NotificationScriptChannelRepeat::deleteAll(['notification_script_channel_id' => NotificationScriptChannel::find()->where(['notification_script_id' => NotificationScript::find()->where(['company_id' => $companyID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . NotificationScriptChannelRepeat::tableName() . "\n";
        print 'Start delete records from table ' . NotificationScriptChannel::tableName() . "\n";
        $count = NotificationScriptChannel::deleteAll(['notification_script_id' => NotificationScript::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . NotificationScriptChannel::tableName() . "\n";
        print 'Start delete records from table ' . OrderDishes::tableName() . "\n";
        $count = OrderDishes::deleteAll(['order_id' => Order::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . OrderDishes::tableName() . "\n";
        print 'Start delete records from table ' . ProcessingFile::tableName() . "\n";
        $count = ProcessingFile::deleteAll(['processing_id' => FoquzPollAnswerProcessing::find()->where(['poll_answer_id' => FoquzPollAnswer::find()->where(['foquz_poll_id' => $pollsID])->select('id')])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ProcessingFile::tableName() . "\n";
        print 'Start delete records from table ' . ProductList::tableName() . "\n";
        $count = ProductList::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . ProductList::tableName() . "\n";
        print 'Start delete records from table ' . ProductView::tableName() . "\n";
        $count = ProductView::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . ProductView::tableName() . "\n";
        print 'Start delete records from table ' . Repeat::tableName() . "\n";
        $count = Repeat::deleteAll(['channel_id' => Channel::find()->where(['poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . Repeat::tableName() . "\n";
        print 'Start delete records from table ' . ReportVersion::tableName() . "\n";
        $count = ReportVersion::deleteAll(['report_id' => Report::find()->where(['owner_id' => $companyUsersID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ReportVersion::tableName() . "\n";
        print 'Start delete records from table ' . ReportLink::tableName() . "\n";
        $count = ReportLink::deleteAll(['report_id' => Report::find()->where(['owner_id' => $companyUsersID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ReportLink::tableName() . "\n";
        print 'Start delete records from table ' . ReportShare::tableName() . "\n";
        $count = ReportShare::deleteAll(['report_id' => Report::find()->where(['owner_id' => $companyUsersID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ReportShare::tableName() . "\n";
        print 'Start delete records from table ' . Report::tableName() . "\n";
        $count = Report::deleteAll(['owner_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . Report::tableName() . "\n";
        print 'Start delete records from table ' . RequestSettings2Poll::tableName() . "\n";
        $count = RequestSettings2Poll::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . RequestSettings2Poll::tableName() . "\n";
        print 'Start delete records from table ' . AnswerHasFiles::tableName() . "\n";
        $count = AnswerHasFiles::deleteAll(['answer_id' => Answer::find()->where(['OR', ['request_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')], ['client_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')]])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . AnswerHasFiles::tableName() . "\n";
        print 'Start delete records from table ' . Answer::tableName() . "\n";
        Feedback::updateAll(['processing_id' => null], ['processing_id' => FoquzPollAnswerProcessing::find()->where(['request_project_answer_id' => Answer::find()->where(['OR', ['request_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')], ['client_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')]])->select('id')])->select('id')]);
        FoquzPollAnswerProcessing::deleteAll(['request_project_answer_id' => Answer::find()->where(['OR', ['request_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')], ['client_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')]])->select('id')]);
        $count = Answer::deleteAll(['OR', ['request_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')], ['client_id' => FoquzContact::find()->where(['company_id' => $companyID])->select('id')]]);
        print 'Deleted ' . $count . ' records from table ' . Answer::tableName() . "\n";
        print 'Start delete records from table ' . GeneralSettings::tableName() . "\n";
        $count = GeneralSettings::deleteAll(['requests_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . GeneralSettings::tableName() . "\n";
        print 'Start delete records from table ' . ProjectHasField::tableName() . "\n";
        $count = ProjectHasField::deleteAll(['request_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ProjectHasField::tableName() . "\n";
        print 'Start delete records from table ' . Theme::tableName() . "\n";
        $count = Theme::deleteAll(['requests_project_id' => Project::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . Theme::tableName() . "\n";
        print 'Start delete records from table ' . Project::tableName() . "\n";
        $count = Project::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Project::tableName() . "\n";
        print 'Start delete records from table ' . RespondentPoll::tableName() . "\n";
        $count = RespondentPoll::deleteAll(['poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . RespondentPoll::tableName() . "\n";
        print 'Start delete records from table ' . ScoresInterpretationRange::tableName() . "\n";
        $count = ScoresInterpretationRange::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . ScoresInterpretationRange::tableName() . "\n";
        print 'Start delete records from table ' . SettingTableFilters::tableName() . "\n";
        $count = SettingTableFilters::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . SettingTableFilters::tableName() . "\n";
        print 'Start delete records from table ' . SettingTables::tableName() . "\n";
        $count = SettingTables::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . SettingTables::tableName() . "\n";
        print 'Start delete records from table ' . SiteCurrency::tableName() . "\n";
        $count = SiteCurrency::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . SiteCurrency::tableName() . "\n";
        print 'Start delete records from table ' . SiteDiscount::tableName() . "\n";
        $count = SiteDiscount::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . SiteDiscount::tableName() . "\n";
        print 'Start delete records from table ' . SiteCategory::tableName() . "\n";
        $count = SiteCategory::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . SiteCategory::tableName() . "\n";
        print 'Start delete records from table ' . SiteNotificationPoll::tableName() . "\n";
        $count = SiteNotificationPoll::deleteAll(['site_notification_id' => SiteNotification::find()->where(['user_id' => $companyUsersID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . SiteNotificationPoll::tableName() . "\n";
        print 'Start delete records from table ' . SiteNotification::tableName() . "\n";
        $count = SiteNotification::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . SiteNotification::tableName() . "\n";
        print 'Start delete records from table ' . SiteProduct::tableName() . "\n";
        $count = SiteProduct::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . SiteProduct::tableName() . "\n";
        print 'Start delete records from table ' . TariffChangeRequest::tableName() . "\n";
        $count = TariffChangeRequest::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . TariffChangeRequest::tableName() . "\n";
        print 'Start delete records from table ' . UserChangeEmail::tableName() . "\n";
        $count = UserChangeEmail::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . UserChangeEmail::tableName() . "\n";
        print 'Start delete records from table ' . UserVisitLog::tableName() . "\n";
        $count = UserVisitLog::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . UserVisitLog::tableName() . "\n";
        print 'Start delete records from table ' . WidgetPreferences::tableName() . "\n";
        $count = WidgetPreferences::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . WidgetPreferences::tableName() . "\n";
        print 'Start delete records from table ' . WordAnswer::tableName() . "\n";
        $count = WordAnswer::deleteAll(['answer_id' => FoquzPollAnswer::find()->where(['foquz_poll_id' => $pollsID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . WordAnswer::tableName() . "\n";
        print 'Start delete records from table ' . ClientEmail::tableName() . "\n";
        $count = ClientEmail::deleteAll(['client_id' => Client::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . ClientEmail::tableName() . "\n";
        print 'Start delete records from table ' . CompanyAffiliateCode::tableName() . "\n";
        Company::updateAll(['affiliate_code_id' => null], ['affiliate_code_id' => CompanyAffiliateCode::find()->where(['company_id' => $companyID])->select('id')]);
        $count = CompanyAffiliateCode::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyAffiliateCode::tableName() . "\n";
        print 'Start delete records from table ' . CompanyFeedback::tableName() . "\n";
        $count = CompanyFeedback::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyFeedback::tableName() . "\n";
        print 'Start delete records from table ' . CompanyFeedbackTheme::tableName() . "\n";
        $count = CompanyFeedbackTheme::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyFeedbackTheme::tableName() . "\n";
        print 'Start delete records from table ' . CompanyFilterAccess::tableName() . "\n";
        $count = CompanyFilterAccess::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyFilterAccess::tableName() . "\n";
        print 'Start delete records from table ' . CompanyIikoAccessCondition::tableName() . "\n";
        $count = CompanyIikoAccessCondition::deleteAll(['iiko_access_id' => CompanyIikoAccess::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . CompanyIikoAccessCondition::tableName() . "\n";
        print 'Start delete records from table ' . CompanyIikoAccess::tableName() . "\n";
        $count = CompanyIikoAccess::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyIikoAccess::tableName() . "\n";
        print 'Start delete records from table ' . CompanyRequestProcessingSettings::tableName() . "\n";
        $count = CompanyRequestProcessingSettings::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyRequestProcessingSettings::tableName() . "\n";
        print 'Start delete records from table ' . CompanyRequisites::tableName() . "\n";
        $count = CompanyRequisites::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyRequisites::tableName() . "\n";
        print 'Start delete records from table ' . CompanySetting::tableName() . "\n";
        $count = CompanySetting::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanySetting::tableName() . "\n";
        print 'Start delete records from table ' . CompanyTariff::tableName() . "\n";
        $count = CompanyTariff::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyTariff::tableName() . "\n";
        print 'Start delete records from table ' . DiscountPoolCode::tableName() . "\n";
        $count = DiscountPoolCode::deleteAll(['pool_id' => DiscountPool::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . DiscountPoolCode::tableName() . "\n";
        print 'Start delete records from table ' . DiscountPool::tableName() . "\n";
        FoquzPollCode::deleteAll(['pool_id' => DiscountPool::find()->where(['company_id' => $companyID])->select('id')]);
        $count = DiscountPool::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . DiscountPool::tableName() . "\n";
        print 'Start delete records from table ' . Dish::tableName() . "\n";
        OrderDishes::deleteAll(['dish_id' => Dish::find()->where(['company_id' => $companyID])->select('id')]);
        $count = Dish::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Dish::tableName() . "\n";
        print 'Start delete records from table ' . DishCategory::tableName() . "\n";
        $count = DishCategory::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . DishCategory::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollLang::tableName() . "\n";
        $count = FoquzPollLang::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollLang::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPollAnswer::tableName() . "\n";
        $count = FoquzPollAnswer::deleteAll(['foquz_poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPollAnswer::tableName() . "\n";
        print 'Start delete records from table ' . Feedback::tableName() . "\n";
        $count = Feedback::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Feedback::tableName() . "\n";
        print 'Start delete records from table ' . Order::tableName() . "\n";
        $count = Order::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Order::tableName() . "\n";
        print 'Start delete records from table ' . Channel::tableName() . "\n";
        $count = Channel::deleteAll(['poll_id' => $pollsID]);
        print 'Deleted ' . $count . ' records from table ' . Channel::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPointItem::tableName() . "\n";
        $count = FoquzPointItem::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPointItem::tableName() . "\n";
        print 'Start delete records from table ' . FoquzPoll::tableName() . "\n";
        $count = FoquzPoll::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzPoll::tableName() . "\n";
        print 'Start delete records from table ' . NotificationScript::tableName() . "\n";
        $count = NotificationScript::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . NotificationScript::tableName() . "\n";
        print 'Start delete records from table ' . OauthAccessTokens::tableName() . "\n";
        $count = OauthAccessTokens::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . OauthAccessTokens::tableName() . "\n";
        print 'Start delete records from table ' . OauthAuthorizationCodes::tableName() . "\n";
        $count = OauthAuthorizationCodes::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . OauthAuthorizationCodes::tableName() . "\n";
        print 'Start delete records from table ' . OauthClients::tableName() . "\n";
        $count = OauthClients::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . OauthClients::tableName() . "\n";
        print 'Start delete records from table ' . OauthRefreshTokens::tableName() . "\n";
        $count = OauthRefreshTokens::deleteAll(['user_id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . OauthRefreshTokens::tableName() . "\n";
        print 'Start delete records from table ' . FoquzContact::tableName() . "\n";
        $count = FoquzContact::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FoquzContact::tableName() . "\n";
        print 'Start delete records from table ' . Client::tableName() . "\n";
        $count = Client::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Client::tableName() . "\n";
        print 'Start delete records from table ' . DictionaryElement::tableName() . "\n";
        $count = DictionaryElement::deleteAll(['dictionary_id' => Dictionary::find()->where(['company_id' => $companyID])->select('id')]);
        print 'Deleted ' . $count . ' records from table ' . DictionaryElement::tableName() . "\n";
        print 'Start delete records from table ' . Dictionary::tableName() . "\n";
        $count = Dictionary::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Dictionary::tableName() . "\n";
        print 'Start delete records from table ' . Filial::tableName() . "\n";
        $count = Filial::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Filial::tableName() . "\n";
        print 'Start delete records from table ' . FilialCategory::tableName() . "\n";
        $count = FilialCategory::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . FilialCategory::tableName() . "\n";
        print 'Start delete records from table ' . Worker::tableName() . "\n";
        Order::deleteAll(['OR', ['driver_id' => Worker::find()->where(['company_id' => $companyID])->select('id')], ['operator_id' => Worker::find()->where(['company_id' => $companyID])->select('id')]]);
        $count = Worker::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Worker::tableName() . "\n";
        print 'Start delete records from table ' . CompanyStaff::tableName() . "\n";
        $count = CompanyStaff::deleteAll(['company_id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . CompanyStaff::tableName() . "\n";
        print 'Start delete records from table ' . User::tableName() . "\n";
        $count = User::deleteAll(['id' => $companyUsersID]);
        print 'Deleted ' . $count . ' records from table ' . User::tableName() . "\n";
        print 'Start delete records from table ' . Company::tableName() . "\n";
        $count = Company::deleteAll(['id' => $companyID]);
        print 'Deleted ' . $count . ' records from table ' . Company::tableName() . "\n";
        print 'Durations: ' . (microtime(true) - $start) . " seconds\n";
    }
}
