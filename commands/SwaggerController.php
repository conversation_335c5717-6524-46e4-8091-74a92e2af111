<?php
namespace app\commands; 
use yii\console\Controller;
use Yii;
use OpenApi\Generator;
 
class SwaggerController extends Controller
{
 
    public function actionGo($fileName, $api = null)
    {
        if (!$api) {
            $openapi = Generator::scan(\OpenApi\Util::finder(Yii::getAlias('@app/modules/foquz/controllers'), ['v1', 'v2']));
        } else {
            $openapi = Generator::scan([
                Yii::getAlias('@app/modules/foquz/controllers/api/v1'),
                Yii::getAlias('@app/modules/foquz/controllers/api/v2'),
            ], ['version' => '3.1.0']);
        }
        if ($openapi) {
            $handle = fopen($fileName, 'wb');
            fwrite($handle, $openapi->toYaml());
            fclose($handle);
        }
    }
 
}