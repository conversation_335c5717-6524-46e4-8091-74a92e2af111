<?php

namespace app\commands;

use app\models\vo\Phone;
use app\modules\foquz\models\CompanyFeedback;
use yii\console\Controller;
use yii\db\ActiveQuery;

class PhoneController extends Controller
{
    public function actionCompanyFeedbackFormat()
    {
        $this->update($this->find(CompanyFeedback::find()), CompanyFeedback::tableName());
    }

    private function find(ActiveQuery $query)
    {
        return $query->select(['id', 'phone'])
            ->where(['NOT REGEXP', 'phone', '^[0-9]*$'])
            ->all();
    }

    private function update($entries, $table)
    {
        foreach($entries as $entry) {

            $phone = (string)(new Phone($entry->phone));

            \Yii::$app->db->createCommand()->update($table, [
                'phone' => $phone !== '' ? $phone : null,
            ], [
                'id' => $entry->id
            ])->execute();
        }
    }
}