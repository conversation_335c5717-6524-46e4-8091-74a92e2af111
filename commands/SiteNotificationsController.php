<?php

namespace app\commands;

use app\components\SendsayService;
use app\models\User;
use app\modules\foquz\models\notifications\SiteNotification;
use app\modules\foquz\services\notifications\cache\NotificationsCacheService;
use app\modules\foquz\services\notifications\NotificationsDailyService;
use Codeception\Lib\Notification;
use yii\console\Controller;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\redis\Connection;


class SiteNotificationsController extends Controller
{
    /**
     * Полная очистка и создание кэша по всем настройкам уведомлений в Redis
     * @return void
     */
    public function actionCacheNotifications()
    {
        $service = new NotificationsCacheService();
        $service->updateAllCache();
    }

    /**
     * Отмена и постановка актуальных задач еа сегодня.
     * Постановка задачи на новое планирование на завтра.
     * @return void
     */
    public function actionRestartAllDaily()
    {
        /** @var Connection $redis */
        $redis = \Yii::$app->redis;
        NotificationsDailyService::runAllForToday($redis);
    }

}