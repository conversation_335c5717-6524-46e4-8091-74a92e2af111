<?php

namespace app\commands;

use app\models\Filial;
use app\modules\foquz\models\FoquzContact;

use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzQuestion;
use yii\console\Controller;
use yii\db\Expression;

class AnswersController extends Controller
{
    /**@deprecated */
    /*public function actionToGoogleSheets($company_id, $sheet_id, $from = null, $to = null)
    {
        $answers = FoquzPollAnswer::find()
            ->select('foquz_poll_answer.*')
            ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_poll_answer.foquz_poll_id')
            ->where(['in', 'foquz_poll_answer.status', [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE]])
            ->andWhere(['foquz_poll.company_id' => $company_id])
            ->orderBy('foquz_poll_answer.updated_at ASC');

        if($from !== null) {
            if($to === null)
                $to = $from;
            $answers->andWhere(new Expression("foquz_poll_answer.updated_at BETWEEN '".$from."' AND '".$to." 23:59:59'"));
        }

        $data = [];
        array_push($data, [
            'ID опроса',
            'Название опроса',
            'Тип опроса',
            'Триггер',
            'Канал доставки',
            'Повтор',
            'Дата/время ответа',
            'IP-адрес',
            'OC',
            'Браузер',
            'ФИО клиента',
            'Телефон',
            'Email',
        ]);

        $maxAnswers = 0;

        foreach($answers->all() as $answerModel)
        {
            $answersNpsOrRating = $answerModel
                ->getFoquzAnswer()
                ->leftJoin('foquz_question', 'foquz_question.id = foquz_poll_answer_item.foquz_question_id')
                ->where(['in', 'foquz_question.main_question_type', [
                    FoquzQuestion::TYPE_VARIANTS,
                    FoquzQuestion::TYPE_TEXT_ANSWER,
                    FoquzQuestion::TYPE_DATE,
                    FoquzQuestion::TYPE_ADDRESS,
                    FoquzQuestion::TYPE_PRIORITY,
                    FoquzQuestion::TYPE_GALLERY_RATING,
                    FoquzQuestion::TYPE_NPS_RATING
                ]])
                ->orWhere(new Expression('foquz_question.rating_type = '.FoquzQuestion::RATING_TYPE_START.' AND foquz_question.main_question_type = '.FoquzQuestion::TYPE_ASSESSMENT))
                ->all();

            $item = [
                $answerModel->foquzPoll->id,
                $answerModel->foquzPoll->name,
                ($answerModel->foquzPoll->is_auto ? 'Автоматический' : 'Ручной'),
                ($answerModel->foquzPoll->is_auto ? $answerModel->foquzPoll->triggerString : ' '),
                $answerModel->getSendChannelName(),
                $answerModel->getSendRepeatValue(),
                date('d.m.Y H:i', strtotime($answerModel->updated_at)),
                $answerModel->ip_address ?? ' ',
                $answerModel->os ?? ' ',
                $answerModel->useragent ?? ' ',
                ($answerModel->contact ? trim(implode(' ', [$answerModel->contact->last_name, $answerModel->contact->first_name, $answerModel->contact->patronymic])) : ' '),
                ($answerModel->contact ? FoquzContact::preformatPhone($answerModel->contact->phone) : ' '),
                ($answerModel->contact->email ?? ' ')
            ];

            if(count($answersNpsOrRating) > 0) {
                if(count($answersNpsOrRating) > $maxAnswers) {
                    $maxAnswers = count($answersNpsOrRating);
                }
                foreach($answersNpsOrRating as $answerItem) {
                    $item[] = $answerItem->foquzQuestion->mainTypeString;
                    $item[] = ($answerItem->foquzQuestion->name == '' ? $answerItem->foquzQuestion->description : $answerItem->foquzQuestion->name);
                    $item[] = $answerItem->answerColumn;
                }
            }
            array_push($data, $item);
        }

        for($i = 0; $i < $maxAnswers; $i++) {
            $data[0][] = 'Тип вопроса';
            $data[0][] = 'Название вопроса';
            $data[0][] = 'Ответ';
        }

        $client = $this->getClient();
        $service = new \Google_Service_Sheets($client);

        $request = new \Google_Service_Sheets_UpdateCellsRequest([
            'updateCells' => [
                'range' => [
                    'sheetId' => 0
                ],
                'fields' => '*'
            ]
        ]);
        $requestB = new \Google_Service_Sheets_Request([
            'repeatCell' => [
                "range" => [
                    "startRowIndex"    => 0,
                    "endRowIndex"      => 1,
                ],
                "cell"  => [
                    "userEnteredFormat" => [
                        "horizontalAlignment" => "CENTER",
                        "textFormat"          => [
                            "bold"      => true,
                        ]
                    ]
                ],
                "fields" => "UserEnteredFormat(horizontalAlignment,textFormat)"
            ]
        ] );
        $requestC = new \Google_Service_Sheets_Request([
            'repeatCell' => [
                "range" => '*',
                "cell"  => [
                    "userEnteredFormat" => [
                        "verticalAlignment" => "TOP",
                    ]
                ],
                "fields" => "UserEnteredFormat(verticalAlignment)"
            ]
        ] );
        $requestBody = new \Google_Service_Sheets_BatchUpdateSpreadsheetRequest();
        $requestBody->setRequests([$request, $requestB, $requestC]);
        $service->spreadsheets->batchUpdate($sheet_id, $requestBody);

        $body = new \Google_Service_Sheets_ValueRange([
            'values' => $data
        ]);

        $range = 'A1';

        $params = [
            'valueInputOption' => 'RAW'
        ];

        $service->spreadsheets_values->update($sheet_id, $range, $body, $params);

        $requestD = new \Google_Service_Sheets_Request([
            'autoResizeDimensions' => [
                'dimensions' => [
                    'sheetId' => 0,
                    'dimension' => 'COLUMNS',
                    'startIndex' => 0,
                    'endIndex' => count($data[0]),
                ]
            ]
        ]);

        $requestBody = new \Google_Service_Sheets_BatchUpdateSpreadsheetRequest();
        $requestBody->setRequests([$requestD]);
        $service->spreadsheets->batchUpdate($sheet_id, $requestBody);
    } */

    /** @deprecated  */
  /*  private function getClient()
    {
        $client = new \Google_Client();
        $client->setApplicationName('Google Sheets API PHP Quickstart');
        $client->setScopes(\Google_Service_Sheets::SPREADSHEETS);
        $client->setAuthConfig(\Yii::getAlias('@app').'/libs/google-api-php-client/credentials.json');
        $client->setAccessType('offline');
        $client->setPrompt('select_account consent');

        $tokenPath = \Yii::getAlias('@app').'/libs/google-api-php-client/token.json';
        if (file_exists($tokenPath)) {
            $accessToken = json_decode(file_get_contents($tokenPath), true);
            $client->setAccessToken($accessToken);
        }

        if ($client->isAccessTokenExpired()) {
            // Refresh the token if possible, else fetch a new one.
            if ($client->getRefreshToken()) {
                $client->fetchAccessTokenWithRefreshToken($client->getRefreshToken());
            } else {
                // Request authorization from the user.
                $authUrl = $client->createAuthUrl();
                printf("Open the following link in your browser:\n%s\n", $authUrl);
                print 'Enter verification code: ';
                $authCode = trim(fgets(STDIN));

                // Exchange authorization code for an access token.
                $accessToken = $client->fetchAccessTokenWithAuthCode($authCode);
                $client->setAccessToken($accessToken);

                // Check to see if there was an error.
                if (array_key_exists('error', $accessToken)) {
                    throw new Exception(join(', ', $accessToken));
                }
            }
            // Save the token to a file.
            if (!file_exists(dirname($tokenPath))) {
                mkdir(dirname($tokenPath), 0700, true);
            }
            file_put_contents($tokenPath, json_encode($client->getAccessToken()));
        }
        return $client;
    }*/

    public function actionCheckKiabiFilial() 
    {
        $answers = FoquzPollAnswer::find()->where(["foquz_poll_id"=>[8077, 7337]])->with("foquzPoll")->all();
        foreach ($answers as $a) {
            if (!$a->answer_filial_id && $a->custom_fields && $a->foquzPoll) {
                $cf = @json_decode($a->custom_fields, true);
                if (is_array($cf) && isset($cf["z_open"]) && $cf["z_open"]) {
                    if (preg_match_all("@:(\w+)$@", $cf["z_open"], $code)) {
                        if (count($code)==2) {
                            $code = $code[1][0];
                            $filial = Filial::find()->where(["company_id"=>$a->foquzPoll->company_id, "iiko_id"=>$code])->one();
                            if ($filial) {
                                $a->answer_filial_id = $filial->id;
                                $a->save();
                                print ($code."\t".$filial->name."\n");
                            }   else {
                                print ($code."\t{$a->custom_fields}\n");
                            }
                            
                        }

                    }
                }
            }
        }

        print(count($answers)."\n");
    }

    public function actionCheckKiabiProcessing()
    {
        $answers = FoquzPollAnswer::find()->where(["foquz_poll_id"=>[8077, 7337]])->with("foquzPoll")->all();
        foreach ($answers as $model) {
            $model->addProcessingIfNeeded();
        }

        die("!!");
    }

    /**
     * одноразовое добавление обработок во все анкеты одной компании
     * @return void
     */
    public function actionSetProcessingRH()
    {
        $ids = FoquzPoll::find()->where(["company_id"=>3308])->select(["id"])->column();
        $answers = FoquzPollAnswer::find()->where(["foquz_poll_id"=>$ids, "status"=>["done", "in-progress"]])->with(["foquzPoll", "processing"])->all();
        print("Всего ответов " . count($answers) . "\n");
        $yes = 0; $no = 0;
        foreach ($answers as $i=>$model) {
            if ($model->processing) $yes++;
            else {
                $no++;
                $model->addProcessingIfNeeded();
            }
        }
        print("Анкет с обработкой $yes\n");
        print("Анкет без обработки (добавлена) $no\n");
    }

    public function actionCheckInProgressAnswers($company_id)
    {
        FoquzPollAnswerItem::deleteAll(['foquz_poll_answer_item.id' => FoquzPollAnswerItem::find()->select('foquz_poll_answer_item.id')->leftJoin('foquz_question', 'foquz_question.id = foquz_poll_answer_item.foquz_question_id')->where(['foquz_question.main_question_type' => FoquzQuestion::TYPE_INTERMEDIATE_BLOCK])]);
        $badAnswers = FoquzPollAnswer::find()
            ->where(['in', 'status', [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE]])
            ->having(new Expression("(SELECT COUNT(foquz_poll_answer_item.id) FROM foquz_poll_answer_item WHERE foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id) = 0"))
            ->andHaving(new Expression("(SELECT COUNT(foquz_complaint.id) FROM foquz_complaint WHERE foquz_complaint.foquz_poll_answer_id = foquz_poll_answer.id) = 0"))
            ->all();
        foreach($badAnswers as $badAnswer) {
            $badAnswer->status = FoquzPollAnswer::STATUS_OPEN;
            $badAnswer->save();
        }
    }
}