<?php


namespace app\commands;


use app\modules\foquz\models\Channel;
use app\modules\foquz\models\channels\GlobalEmailSettings;
use app\modules\foquz\models\channels\GlobalPushSettings;
use app\modules\foquz\models\channels\GlobalSmsSettings;
use app\modules\foquz\models\channels\GlobalTelegramSettings;
use app\modules\foquz\models\channels\GlobalViberSettings;
use app\modules\foquz\models\mailings\Mailing;
use app\modules\foquz\models\mailings\MailingChannel;
use yii\console\Controller;

class ChannelsController extends Controller
{
    public function actionSetSenderId()
    {
        $emails = [];
        $sms = [];
        $viber = [];
        $telegram = [];
        $push = [];

        $globalEmailSettings = GlobalEmailSettings::find()->all();
        foreach($globalEmailSettings as $email) {
            $emails[$email->company_id][$email->sender] = $email->id;
        }

        $globalSmsSettings = GlobalSmsSettings::find()->all();
        foreach($globalSmsSettings as $smsSetting) {
            $sms[$smsSetting->company_id][$smsSetting->sender] = $smsSetting->id;
        }

        $globalViberSettings = GlobalViberSettings::find()->all();
        foreach($globalViberSettings as $viberSetting) {
            $viber[$viberSetting->company_id][$viberSetting->sender_name] = $viberSetting->id;
        }

        $globalTelegramSettings = GlobalTelegramSettings::find()->all();
        foreach($globalTelegramSettings as $tgSetting) {
            $telegram[$tgSetting->company_id][$tgSetting->sender] = $tgSetting->id;
        }

        $globalPushSettings = GlobalPushSettings::find()->all();
        foreach($globalPushSettings as $pushSetting) {
            $push[$pushSetting->company_id][$pushSetting->sender] = $pushSetting->id;
        }

        $settings = [
            'Email' => $emails,
            'SMS' => $sms,
            'Telegram' => $telegram,
            'Push' => $push,
            'Viber' => $viber,
        ];

        $channelsWithSender = Channel::find()
            ->where(['is not', 'sender', null])
            ->all();

        foreach($channelsWithSender as $channel) {
            if($channel->foquzPoll) {
                $channel->sender_id = $settings[$channel->name][$channel->foquzPoll->company_id][$channel->sender] ?? null;
                $channel->save();
            }
        }

        $mailingChannelsWithSender = MailingChannel::find()
            ->where(['is not', 'sender', null])
            ->all();

        foreach($mailingChannelsWithSender as $channel) {
            $channel->sender_id = $settings[$channel->name][$channel->mailing->company_id][$channel->sender] ?? null;
            $channel->save();
        }
    }
}