<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use app\components\mailings\jobs\SendPollContactJob;
use app\components\mailings\MailingPollService;
use app\components\mailings\models\ListContact;
use app\components\messenger\models\SenderLog;
use app\components\messenger\SMSService;
use app\components\messenger\TelegramService;
use app\components\SendsayService;
use app\models\Order;
use app\models\TelegramChat;
use app\modules\foquz\models\Channel;
use app\modules\foquz\models\channels\GlobalEmailSettings;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzSendPhoneForm;
use app\modules\foquz\models\mailings\Mailing;
use app\modules\foquz\models\mailings\MailingChannel;
use app\modules\foquz\models\mailings\MailingList;
use app\modules\foquz\models\mailings\MailingListSend;
use app\modules\foquz\queue\mailings\StatusMailingPollJob;
use app\modules\foquz\services\mailings\cache\MailingsAfterPollCache;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\PHPMailer;
use Yii;
use yii\console\Controller;
use yii\helpers\Url;
use yii\queue\amqp_interop\Queue;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *

 */
class FoquzSenderController extends Controller
{

    public function actionTestSendsay()
    {
        $sendsayClient = new  SendsayService(\Yii::$app->params['sendsay']['login'],
            \Yii::$app->params['sendsay']['password']);

        $file = fopen("../advb-c.txt", "r");
        $fileR = fopen("../res.txt", "a+");
        $i=0;
        while ($data=fgetcsv($file)) {
            $i++;
            print($i."\t");
            if ($i==1) continue;
            $row = json_decode($data[4], true);
            if (!is_array($row) || empty($row['track.id'])) {
                if (is_array($row)) {
                    $r = $row['errors'][0]['id'] . " " . ($row['errors'][0]['explain'] ?? '');
                } else {
                    $r = json_encode($data);
                }
            } else {
                $result = $sendsayClient->trackStatus($row['track.id']);
                $r = $result['obj']['status'] . "\t" . $result['obj']['info'] .  "\t" . $result['obj']['error.info'] . "\t" . $result['obj']['param']['email'];
            }

            print_r($i . "\t" . $r.PHP_EOL);
            fputs($fileR, $r.PHP_EOL);
        }



        $res = $sendsayClient->sendEmail('<EMAIL>', 'test', '<EMAIL>', 'test', 'test');
        //print_r($sendsayClient);
        //print_r();
        print_r($res);
        exit;
    }


    /**
     * Пересоздать кэш для рассылок после опроса.
     * @return void
     */
    public function actionRecreateCacheMailingAfterPoll()
    {
        $cache = new MailingsAfterPollCache(\Yii::$app->redis);
        $cache->updateAllActivePolls();
    }

    public const AUTHORIZED_EMAIL = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        '<EMAIL>',
        '<EMAIL>',
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ];
    public const ALLOW_COMPANY = [
        638,
        1221,
        1462,
        32,
        1075,
        1809,
        1917,
        620,
        280,
        1366,
        35,
        2060,
        1756,
        1,
        40,
        2042,
        2108,
        2089,
        2143,
        2400,
        2673,
        2742,
        2329,
        2781,
        2774,
        2987,
        2991,
        2613,
        3179,
        2112,
        3226,
        85,
        3151,
        2975,
        3487,
        3308,
        3213,
        1033,
        3762,
        2950,
        1531,
        3705,
        3844,
        3958,
        4101
    ];

    public function actionSms() {
        \Yii::$app->queue_mailings_out->priority(9)->delay(0)->push([
            'job'         => 'notifications\\SendEmailJob',
            'company'     => 'sfsdf',
            'type'        => 'smsreg',
            'subject'     => '',
            'message'     => 'tets',
            'fromEmail'   => 'sfsaf',
            'users'       => [['id' => 1, 'email' => '79268444037']],
        ]);
    }
    public function actionHandSendMailing($id1 = 0, $id2 = 1000000000000000000, $withoutSending = false)
    {
        $path = preg_replace("@/commands@", "", dirname(__FILE__));
        exec("ps auxwww|grep $path.*foquz-sender/hand-send-mailing|grep -v grep", $output);
        //print("ps auxwww|grep $path.*foquz-sender/hand-send-mailing|grep -v grep");
        if (count($output) > 2) {
            return;
        }

        global $minID, $maxID;
        $minID = $id1;
        $maxID = $id2;

        $aId = 495;
        if ($path == "/var/www/hatimaki.foquz.ru/project") {
            $aId = 661;
        }

        if ($path == "/var/www/doxswf.ru/project") {
            $aId = 416;
        }

        if ($path == "/var/www/devfoquz.ru/project") {
            $aId = 441;
        }

        print ($minID . " " . $maxID . "\n");

        while (true) {
            \Yii::$app->db->close();
            \Yii::$app->db->open();

            $lists = MailingList::find()->where(["or", "status=1", "status=0 and launched_at is not null"])
                //->andWhere([">=", "id", 108])
                ->andWhere([">=", "id", $aId])
                //->andWhere(["=", "id", 647])
                ->andWhere(["deleted" => 0])
                ->andWhere(["not in", "id", [664, 662]])
                ->with([
                    "notSentMessages" => function ($q) {
                        global $minID, $maxID;
                        $q->with("contact")->andWhere("id>=$minID and id<=$maxID")->orderBy("id");
                    },
                    "mailing"         => function ($q) {
                        $q->with([
                            "channels" => function ($q) {
                                $q->where(["active" => 1])->orderBy("position");
                            },
                            "company",
                        ]);

                    }
                ])
                ->all();


            foreach ($lists as $list) {


                $sendsayClient = new  SendsayService(\Yii::$app->params['sendsay']['login'],
                    \Yii::$app->params['sendsay']['password']);

                $company = $list->mailing->company;
                print("$list->created_at\t{$list->mailing->company->name} {$list->mailing->id} {$list->id} " . count($list->notSentMessages) . "\n");
                print(count($list->notSentMessages) . "\n");
                print(@$list->notSentMessages[0]->id . "\n");
                print(@$list->notSentMessages[count($list->notSentMessages) - 1]->id . "\n");

                if ($list->status == 1) {
                    //$status = 1;
                    print ("запущена прямо сейчас\n");
                } else {
                    if ($list->launched_at) {
                        $l = strtotime($list->launched_at);
                        if (time() < $l) {
                            print("запускать еще рано {$list->launched_at}\n");
                            continue;
                        } else {
                            $list->status = 1;
                            $list->save();
                        }
                    } else {
                        print ("дата запуска не указана\n");
                        continue;
                    }
                }

                $mailing = $list->mailing;
                foreach ($list->notSentMessages as $c) {
                    //exit;
                    $l = MailingList::findOne($list->id);
                    if ($l->status == 2) {
                        break;
                    }

                    if ($c && $c->contact) {
                        $contact = $c->contact;

                        if ($contact->is_subscribed && $list->mailing->isAvailableConditions($contact)) {
                            foreach ($list->mailing->channels as $channel) {
                                $senderSetting = $channel->getGlobalSetting()->one();
                                $sended = false;
                                if ($channel->active) {
                                    if ($channel->name == "Email" && $contact->email && $channel->subject != '') {
                                        //	$contact->email = "<EMAIL>";
                                        if (!in_array($list->mailing->company_id, self::ALLOW_COMPANY)) {
                                            continue;
                                        }

                                        $listSend = new MailingListSend([
                                            'foquz_contact_id'        => $contact->id,
                                            'channel_id'              => $channel->id,
                                            'mailing_id'              => $mailing->id,
                                            'mailing_list_id'         => $list->id,
                                            'mailing_list_contact_id' => $c->id,
                                            'status'                  => MailingListSend::STATUS_SEND,
                                            'key'                     => md5(time() . uniqid()),
                                            'sended'                  => date('Y-m-d H:i:s'),
                                            'order_id'                => null,
                                        ]);

                                        $code = $channel->getCode($mailing, $contact);
                                        $unsub_link = Url::to(['/foquz/mailings/unsubscribe', 'key' => $listSend->key],
                                            true);
                                        $unsub_link = str_replace("foquz.ru", $company->alias, $unsub_link);

                                        $fio = implode(' ', array_filter([
                                            $contact->first_name,
                                            $contact->patronymic,
                                            $contact->last_name
                                        ]));
                                        $text = MailingChannel::getEmailText($channel['text'], $fio,
                                            $contact->first_name, $contact->last_name, $contact->patronymic,
                                            $unsub_link, null, null, $code, $contact->customFields);

                                        //$text = Channel::getEmailText($channel->text, $contact->first_name . ' ' . $contact->last_name . ' ' . $contact->patronymic, $contact->first_name, $contact->last_name, $contact->patronymic, $link, Url::to([$qr_link], true), $unsub_link, "", "", $code, $list->foquzPoll);
                                        $c->passed_at = date('Y-m-d H:i:s');
                                        $c->status = 2;
                                        $c->save();

                                        $senderName = "Опрос Foquz";
                                        if ($channel->sender_name) {
                                            $senderName = $channel->sender_name;
                                        }

                                        $senderEmail = \Yii::$app->params['main_sender_email'];

                                        $sender = $senderSetting;

                                        if (!$sender) {
                                            $sender = GlobalEmailSettings::find()->where([
                                                "company_id" => $mailing->company_id,
                                                'sender'     => $channel->sender
                                            ])->one();
                                        }

                                        if ($sender && $sender->email && in_array($sender->email,
                                                self::AUTHORIZED_EMAIL)) {
                                            $senderEmail = $sender->email;
                                        }
                                        $char_set = 'UTF-8';
                                        $email = $contact->email;

                                        $text = MailingChannel::addPixel($text, $listSend->key);

                                        $mt = microtime(true);
                                        if ($company->id == 2112) {
                                            $sender->email = 'MysteryShopper';
                                            $mail = new PHPMailer();
                                            $mail->IsSMTP();
                                            $mail->CharSet = 'UTF-8';
                                            $mail->Host = $sender->host;    // SMTP server example
                                            $mail->SMTPAuth = true;                  // enable SMTP authentication
                                            $mail->Port = $sender->port;                    // set the SMTP port for the GMAIL server
                                            $mail->Username = $sender->email;            // SMTP account username example
                                            $mail->Password = $sender->password;
                                            $mail->setFrom($sender->sender ? $sender->sender : $sender->email,
                                                $senderName);
                                            $mail->addAddress($contact->email);
                                            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                                            $mail->isHTML(true);                       // Set email format to HTML
                                            $mail->Subject = $channel->subject;
                                            $mail->Body = $text;
                                            $messageId = $mail->send();
                                            echo("Email sent {$contact->email}! $senderEmail $senderName, Message ID: " . json_encode($messageId) . "\n");
                                        } else {
                                            $mt = 0;
                                            if (!$withoutSending) {
                                                try {
                                                    $result = $sendsayClient->sendEmail($senderEmail, $senderName,
                                                        $email, $channel->subject, $text);
                                                } catch (Exception $e) {
                                                    print_r($e->getMessage());
                                                    print("\n");
                                                }
                                                $messageId = json_encode($result);
                                                echo("Email sent! Message ID: $email $messageId $mt" . "\n");
                                            }
                                        }
                                        //exit;
//			         					$mt =0// microtime(true)-$mt;

                                        $listSend->save();
                                        $listSend->refresh();


                                        //  exit;
                                        //exit;


                                    } else {
                                        if ($channel->name == "SMS" || $channel->name == "Viber") {
                                            if ($contact->phone) {
                                                $listSend = new MailingListSend([
                                                    'foquz_contact_id'        => $contact->id,
                                                    'channel_id'              => $channel->id,
                                                    'mailing_id'              => $mailing->id,
                                                    'mailing_list_id'         => $list->id,
                                                    'mailing_list_contact_id' => $c->id,
                                                    'status'                  => MailingListSend::STATUS_SEND,
                                                    'key'                     => md5(time() . uniqid()),
                                                    'sended'                  => date('Y-m-d H:i:s'),
                                                    'order_id'                => null,
                                                ]);

                                                $code = $channel->getCode($mailing, $contact);
                                                $unsub_link = Url::to([
                                                    '/foquz/mailings/unsubscribe',
                                                    'key' => $listSend->key
                                                ], true);
                                                $unsub_link = str_replace("foquz.ru", $company->alias, $unsub_link);
                                                $fio = implode(' ', array_filter([
                                                    $contact->first_name,
                                                    $contact->patronymic,
                                                    $contact->last_name
                                                ]));
                                                $text = MailingChannel::getEmailText($channel['text'], $fio,
                                                    $contact->first_name, $contact->last_name, $contact->patronymic,
                                                    $unsub_link, null, null, $code, $contact->customFields);

                                                $c->passed_at = date('Y-m-d H:i:s');

                                                //$contact->phone = "79268444037";
                                                $c->status = 2;
                                                $c->save();
                                                //
                                                switch ($channel->name) {
                                                    case 'SMS':
                                                        $serviceId = FoquzSendPhoneForm::SERVICE_SMS;
                                                        $globalSetting = $channel->globalSetting;
                                                        $telegramMessenger = new SMSService([
                                                            'logger'        => new SenderLog([]),
                                                            'globalSetting' => $globalSetting
                                                        ]);
                                                        $resp = $telegramMessenger->send(
                                                            $contact->phone,
                                                            $text
                                                        );
                                                        print($contact->phone . "\tsend phone\n$resp\n");

                                                        break;
                                                    case 'Viber':
                                                        $serviceId = FoquzSendPhoneForm::SERVICE_VIBER;
                                                        $globalSetting = $channel->globalSetting;
                                                        break;
                                                }

                                                $listSend->save();
                                                $listSend->refresh();


                                            }
                                        } else {
                                            if ($channel->name == 'Telegram') {
                                                $to = preg_replace("@[^\d]@", "", $contact->phone);
                                                $to = preg_replace("@^[87]@", "", $to);
                                                $ch = TelegramChat::find()->where(["like", "phone", $to])->one();
                                                if (!$ch) {
                                                    continue;
                                                }
                                                $listSend = new MailingListSend([
                                                    'foquz_contact_id'        => $contact->id,
                                                    'channel_id'              => $channel->id,
                                                    'mailing_id'              => $mailing->id,
                                                    'mailing_list_id'         => $list->id,
                                                    'mailing_list_contact_id' => $c->id,
                                                    'status'                  => MailingListSend::STATUS_SEND,
                                                    'key'                     => md5(time() . uniqid()),
                                                    'sended'                  => date('Y-m-d H:i:s'),
                                                    'order_id'                => null,
                                                ]);
                                                $code = $channel->getCode($mailing, $contact);
                                                $unsub_link = Url::to([
                                                    '/foquz/mailings/unsubscribe',
                                                    'key' => $listSend->key
                                                ], true);
                                                $unsub_link = str_replace("foquz.ru", $company->alias, $unsub_link);
                                                $fio = implode(' ', array_filter([
                                                    $contact->first_name,
                                                    $contact->patronymic,
                                                    $contact->last_name
                                                ]));
                                                $text = MailingChannel::getEmailText($channel['text'], $fio,
                                                    $contact->first_name, $contact->last_name, $contact->patronymic,
                                                    $unsub_link, null, null, $code);
                                                $text = trim(preg_replace("@<(!DOCTYPE|html|head|body|/head|/body|/html).*?>@",
                                                    "", $text), " \n\t\r");
                                                $c->passed_at = date('Y-m-d H:i:s');
                                                $c->status = 2;
                                                $c->save();
                                                $to = preg_replace("@[^\d]@", "", $contact->phone);
                                                $to = preg_replace("@^8@", "7", $to);
                                                $m = new TelegramService();
                                                $r = $m->send($to, $text, [], $channel->globalSetting->token);
                                                $listSend->save();
                                                $listSend->refresh();

                                            } else {
                                                if ($channel->name == "Push" && $contact->mobile_device_id) {
                                                    $listSend = new MailingListSend([
                                                        'foquz_contact_id'        => $contact->id,
                                                        'channel_id'              => $channel->id,
                                                        'mailing_id'              => $mailing->id,
                                                        'mailing_list_id'         => $list->id,
                                                        'mailing_list_contact_id' => $c->id,
                                                        'status'                  => MailingListSend::STATUS_SEND,
                                                        'key'                     => md5(time() . uniqid()),
                                                        'sended'                  => date('Y-m-d H:i:s'),
                                                        'order_id'                => null,
                                                    ]);

                                                    $code = $channel->getCode($mailing, $contact);
                                                    $unsub_link = Url::to([
                                                        '/foquz/mailings/unsubscribe',
                                                        'key' => $listSend->key
                                                    ], true);
                                                    $unsub_link = str_replace("foquz.ru", $company->alias, $unsub_link);

                                                    $text = MailingChannel::getEmailText($channel['text'], $fio,
                                                        $contact->first_name, $contact->last_name, $contact->patronymic,
                                                        $unsub_link, null, null, $code);

                                                    $text = trim(preg_replace("@<(!DOCTYPE|html|head|body|/head|/body|/html).*?>@",
                                                        "", $text), " \n\t\r");
                                                    $c->passed_at = date('Y-m-d H:i:s');
                                                    $c->status = 2;
                                                    $c->save();

                                                    $apiKey = "AAAABl4sNsQ:APA91bE35jBypnJIZeskgR4nObVBgAxSriax89EDdzByHoPw7-y9Rm86DMymHFz9Xn-VNVmejhZZ1hviL6AvAufrXtg5abJs-xW060UmbFJG6A4pD2fAdywZJrComOMTXz8UuQbl8SDV";

                                                    if ($senderSetting->token) {
                                                        $apiKey = $senderSetting->token;
                                                    }
                                                    //print($contact->mobile_device_id."\n"); exit;

                                                    $token = urldecode($contact->mobile_device_id);
                                                    print($token . "\n");
                                                    print($contact->phone . "\n");
                                                    $fcmMsg = array(
                                                        'body'  => $text,
                                                        'title' => $channel->subject,
                                                        'sound' => "default",
                                                        'icon'  => "hatimaki",
                                                    );
                                                    if ($channel->link) {
                                                        $fcmMsg["click_action"] = trim($channel->link);
                                                    }

                                                    $fcmFields = array(
                                                        'to'           => $token,
                                                        'priority'     => 'high',
                                                        'notification' => $fcmMsg
                                                    );


                                                    if ($channel->payload) {
                                                        $data = @json_decode($channel->payload, true);
                                                        $fcmFields["data"] = $data;;
                                                    }

                                                    $headers = array(
                                                        'Authorization: key=' . $apiKey,
                                                        'Content-Type: application/json'
                                                    );
                                                    $ch = curl_init();
                                                    curl_setopt($ch, CURLOPT_URL,
                                                        'https://fcm.googleapis.com/fcm/send');
                                                    curl_setopt($ch, CURLOPT_POST, true);
                                                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmFields));
                                                    $result = curl_exec($ch);
                                                    curl_close($ch);
                                                    echo $result . "\n";
                                                    $listSend->save();
                                                    $listSend->refresh();

                                                }
                                            }
                                        }
                                    }
                                    //	exit;
                                }
                            }


                        }
                    }


                }
                //	exit;
            }


            unset($lists);
            print ("\n=========\n");

            sleep(5 * 60);
        }

    }

    public function actionAutoSendMailing()
    {
        exec("ps auxwww|grep hatimaki\.foquz\.ru.*foquz-sender/auto-send-mailing|grep -v grep", $output);
        if (count($output) > 2) {
            return;
        }


        while (true) {
            \Yii::$app->db->close();
            \Yii::$app->db->open();

            $mailings = Mailing::find()->where([
                "id" => [
                    14,
                    12,
                    13,
                    11,
                    15,
                    9,
                    58,
                    80,
                    85,
                    86,
                    147,
                    142,
                    141,
                    140,
                    131,
                    148,
                    178,
                    177,
                    175,
                    180,
                    181,
                    182,
                    185,
                    183,
                    238,
                    259,
                    260,
                    261,
                    364,
                    360,
                    359,
                    355
                ]
            ])
                //$mailings = Mailing::find()->where(["id"=>[238]])
                ->andWhere(["stopped" => 0, "is_auto" => 1])
                ->andWhere(["or", "date_from is null", "date_from<='" . date("Y-m-d") . "'"])
                ->andWhere(["or", "date_to is null", "date_to>='" . date("Y-m-d") . "'"])
                ->with([
                    "channels" => function ($q) {
                        $q->where(["active" => 1])->orderBy("position");
                    },
                    "mailingConditions",
                    "company",
                    "noOrderAsUsualTriggerSetting"
                ])
                ->orderBy("id asc")
                ->all(); //вставить все проверки fixme


            foreach ($mailings as $mailing) {
                $sendsayClient = new SendsayService(\Yii::$app->params['sendsay']['login'],
                    \Yii::$app->params['sendsay']['password']);

                $company = $mailing->company;

                $sendTime = "15:00";
                if ($mailing->trigger == Mailing::TRIGGER_NO_ORDER_AS_USUAL && $mailing->noOrderAsUsualTriggerSetting && $mailing->noOrderAsUsualTriggerSetting->time) {
                    $tt = strtotime($mailing->noOrderAsUsualTriggerSetting->time);
                    if ($tt) {
                        $sendTime = date("H:i", $tt);
                    }
                } else {
                    if ($mailing->send_time) {
                        $tt = strtotime($mailing->send_time);
                        if ($tt) {
                            $sendTime = date("H:i", $tt);
                        }
                    }
                }

                print ("\nMAILING {$mailing->id}\n");
                if ($sendTime > date("H:i")) {
                    print ("рассылку отправлять еще рано $sendTime\n");
                    //continue;
                }

                foreach ($mailing->channels as $channel) {
                    $senderSetting = $channel->getGlobalSetting()->one();
                    $contacts = $mailing->getContactsForList($channel);

                    print ("\nChannel {$channel->name}\n");
                    print (count($contacts) . "\n");
                    //continue;

                    foreach ($contacts as $contact) {
                        $stop = Mailing::findOne($mailing->id)->stopped;
                        if ($stop) {
                            print("stopped\n");
                            continue;
                        }
                        //continue;
                        $order = $contact->currentOrder;

                        if ($contact->is_subscribed && $mailing->isAvailableConditions($contact, $order)) {
                            if ($channel->active) {
                                $listSend = new MailingListSend([
                                    'foquz_contact_id' => $contact->id,
                                    'channel_id'       => $channel->id,
                                    'mailing_id'       => $mailing->id,
                                    'status'           => MailingListSend::STATUS_SEND,
                                    'key'              => md5(time() . uniqid()),
                                    'sended'           => date('Y-m-d H:i:s'),
                                    'order_id'         => $order ? $order->id : null,
                                ]);
                                $listSend->save();
                                $listSend->refresh();

                                $unsub_link = Url::to(['/foquz/mailings/unsubscribe', 'key' => $listSend->key], true);
                                $unsub_link = str_replace("foquz.ru", $company->alias, $unsub_link);

                                $code = $channel->getCode($mailing, $contact);
                                if (!in_array($mailing->trigger, [
                                    Mailing::TRIGGER_DAYS_WITHOUT_ORDER,
                                    Mailing::TRIGGER_BIRTHDAY,
                                    Mailing::TRIGGER_NO_ORDER_AS_USUAL,
                                    Mailing::TRIGGER_ORDERS
                                ])) {
                                    $text = MailingChannel::getEmailText($channel['text'],
                                        implode(' ', [$contact->last_name, $contact->first_name, $contact->patronymic]),
                                        $contact->first_name, $contact->last_name, $contact->middle_name, $unsub_link,
                                        $order->id, $order->created_time, $code);
                                } else {
                                    $text = MailingChannel::getEmailText($channel['text'],
                                        implode(' ', [$contact->last_name, $contact->first_name, $contact->patronymic]),
                                        $contact->first_name, $contact->last_name, $contact->middle_name, $unsub_link,
                                        null, null, $code);
                                }

                                if ($channel->name == "Push" && $contact->mobile_device_id) {
                                    $apiKey = "AAAABl4sNsQ:APA91bE35jBypnJIZeskgR4nObVBgAxSriax89EDdzByHoPw7-y9Rm86DMymHFz9Xn-VNVmejhZZ1hviL6AvAufrXtg5abJs-xW060UmbFJG6A4pD2fAdywZJrComOMTXz8UuQbl8SDV";
                                    $token = urldecode($contact->mobile_device_id);
                                    print($token . "\t{$contact->phone}\n");
                                    $fcmMsg = array(
                                        'body'  => $text,
                                        'title' => $channel->subject,
                                        'sound' => "default",
                                        'icon'  => "hatimaki",
                                    );
                                    $fcmFields = array(
                                        'to'           => $token,
                                        'priority'     => 'high',
                                        'notification' => $fcmMsg
                                    );
                                    $headers = array(
                                        'Authorization: key=' . $apiKey,
                                        'Content-Type: application/json'
                                    );
                                    $ch = curl_init();
                                    curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
                                    curl_setopt($ch, CURLOPT_POST, true);
                                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmFields));
                                    $result = curl_exec($ch);
                                    curl_close($ch);
                                    echo $result . "\n";
                                } else {
                                    if ($channel->name == "Email" && $contact->email && $channel->subject != '') {
                                        $text = MailingChannel::addPixel($text, $listSend->key);
                                        $senderName = "Опрос Foquz";
                                        if ($channel->sender_name) {
                                            $senderName = $channel->sender_name;
                                        }
                                        $senderEmail = \Yii::$app->params['main_sender_email'];
                                        $sender = $senderSetting;
                                        if (!$sender) {
                                            $sender = GlobalEmailSettings::find()->where([
                                                "company_id" => $mailing->company_id,
                                                'sender'     => $channel->sender
                                            ])->one();
                                        }
                                        if ($sender && $sender->email && in_array($sender->email,
                                                self::AUTHORIZED_EMAIL)) {
                                            $senderEmail = $sender->email;
                                        }
                                        $char_set = 'UTF-8';
                                        $email = $contact->email;
                                        $result = $sendsayClient->sendEmail($senderEmail, $senderName, $email,
                                            $channel->subject, $text);
                                        $messageId = json_encode($result);
                                        echo("Email sent! Message ID: $email $messageId" . "\n");
                                    } else {
                                        if ($channel->name == "SMS" and $contact->phone) {
                                            $phone = $contact->phone;
                                            $to = preg_replace("@[^\d]@", "", $phone);
                                            $to = preg_replace("@^8@", "7", $to);
                                            $m = new SMSService([
                                                'logger'        => new SenderLog([]),
                                                'globalSetting' => $senderSetting
                                            ]);
                                            $res = $m->send($to, $text);
                                            print ($to . " sms ");
                                            print_r($res . "\n");
                                        } else {
                                            $listSend->delete();
                                        }
                                    }
                                }
                            }
                        }

                    }

                }
            }

            exit;
        }
    }

    public function actionAutoSend()
    {
        exec("ps auxwww|grep hatimaki\.foquz\.ru.*foquz-sender/auto-send$|grep -v grep", $output);

        if (count($output) > 2) {
            return;
        }
        //die("4436");
        while (true) {
            \Yii::$app->db->close();
            \Yii::$app->db->open();

            //$polls = FoquzPoll::find()->where(["id"=>[849,876]])
            $polls = FoquzPoll::find()->where(["id" => [2915, 3191]])
                //$polls = FoquzPoll::find()->where(["id"=>[2915]])
                ->with([
                    "company",
                    "channels" => function ($q) {
                        $q->with("repeats")->where(["active" => 1])->orderBy("position");
                    }
                ])
                ->orderBy("id asc")
                ->all(); //вставить все проверки fixme


            foreach ($polls as $poll) {

                $mailer = null;
                $from = null;
                if ($poll->id == 2915) {
                    $mailer = Yii::$app->mailer2;
                    $from = ["<EMAIL>" => "ЗаОбеЩеки"];
                } else {
                    $from = ["<EMAIL>" => "Хатимаки"];
                }
                //if ($poll->id!==876) continue ;

                $date = date("Y-m-d H:i:s", time() - 60 * 60 * 1);

                $orders = Order::find()->where([
                    "and",
                    ["orders.status" => "CLOSED"],
                    ["company_id" => $poll->company->id],
                    [">", "close_time", $date],
                    ["source_type" => [1, 3]],
                    //"filial_id<>16"
                ])
                    ->leftJoin("foquz_poll_answer",
                        "foquz_poll_answer.foquz_poll_id={$poll->id} AND foquz_poll_answer.order_id=orders.id")
                    ->andWhere([
                        "or",
                        "foquz_poll_answer.id is null",
                        "foquz_poll_answer.status in ('new', 'open')"
                    ])
                    ->orderBy("close_time desc")
                    ->with([
                        "client" => function ($q) {
                            $q->with("contact");
                        },
                        "dishes",
                    ]);


                if ($poll->id == 2915) {
                    //$orders->andWhere(["filial_id"=>[23,24]]);
                } else {
                    $orders->andWhere("filial_id not in (16, 23, 24)");
                }


                $orders = $orders->all();


                /** @var Order $order */
                foreach ($orders as $order) {
                    print($order->number . "\t" . $order->close_time . "\n");
                    //если не отписан
                    if (!($order->client && $order->client->contact && !$order->client->contact->is_subscribed)) {
                        $email = $order->clientEmail;
                        $phone = $order->clientPhone;
                        $name = $order->clientName;
                        $firstname = $order->contact->first_name ?? '';
                        $lastname = $order->contact->last_name ?? '';
                        $patronymic = $order->contact->patronymic ?? '';
                        //$email = "<EMAIL>";


                        $answer = FoquzPollAnswer::createForMailingAuto($poll->id, $order);

                        //если не начали заполнять
                        if ($answer->status == FoquzPollAnswer::STATUS_NEW || $answer->status == FoquzPollAnswer::STATUS_OPEN) {

                            $lastTime = 0; //время с которого считаем паузу fixme
                            /** @var Channel $channel */
                            foreach ($poll->channels as $channel) {
                                $senderSetting = $channel->getGlobalSetting()->one();

                                $delay = $channel->delaySeconds;
                                $sended = time() - strtotime($lastTime) < $delay;


                                if (!$sended) {
                                    //if ($channel->name=="Email") continue;
                                    $send = FoquzPollMailingListSend::find()->where([
                                        "channel_id" => $channel->id,
                                        "repeat_id"  => 0,
                                        "answer_id"  => $answer->id
                                    ])->one();

                                    if (!$send) { //не отправляли по этому каналу
                                        //$phone = "79268444037";
                                        $needSend = $channel->checkContactsBeforeSend($phone, $email);
                                        $send = FoquzPollMailingListSend::createAuto($answer->id, $channel->id, 0,
                                            $needSend, $channel->name);

                                        print("send {$channel->name} {$email} {$phone} 0\n");


                                        if ($needSend) {

                                            try {
                                                print_r($from);
                                                $channel->sendPoll($poll, $send->key, $name, $firstname, $lastname,
                                                    $patronymic, $email, $phone, null, $mailer, $from, $senderSetting);
                                            } catch (\Swift_TransportException  $e) {
                                                print_r($e->getMessage());
                                                sleep(5);
                                            }
                                        }

                                        $sended = true;
                                        break;
                                    } else {
                                        $lastTime = $send->sended;
                                    }


                                    if (!$sended) {
                                        foreach ($channel->repeats as $i => $repeat) {
                                            $delay = $repeat->delaySeconds;

                                            print("send {$channel->name} {$email} {$phone} repeat={$delay}\n");

                                            $sended = time() - strtotime($lastTime) < $delay;

                                            if (!$sended) {
                                                $send = FoquzPollMailingListSend::find()->where([
                                                    "channel_id" => $channel->id,
                                                    "repeat_id"  => $repeat->id,
                                                    "answer_id"  => $answer->id
                                                ])->one();

                                                if (!$send) {
                                                    $needSend = $channel->checkContactsBeforeSend($phone, $email);
                                                    $send = FoquzPollMailingListSend::createAuto($answer->id,
                                                        $channel->id, $repeat->id, $needSend, $channel->name);
                                                    print("send {$channel->name} {$email} {$phone} repeat={$i}\n");


                                                    if ($needSend) {
                                                        $channel->sendPoll($poll, $send->key, $name, $firstname,
                                                            $lastname, $patronymic, $email, $phone, $repeat, $mailer,
                                                            $from, $senderSetting);
                                                    }

                                                    $sended = true;
                                                    break;
                                                } else {
                                                    $lastTime = $send->sended;
                                                }

                                            } else {
                                                break;
                                            }

                                        }
                                    }

                                    if ($sended) {
                                        break;
                                    }

                                }
                            }

                        } else {
                            print("заполнена\n");
                        }

                    }


                }
            }

            unset($polls);
            sleep(60 * 30);
        }

    }

    /**
     * Запуск задачи рассылки
     * @param int $listId ID Рассылки
     * @return void
     */
    public function actionPushJobMailing(int $listId): void
    {
        $model = FoquzPollMailingList::findOne($listId);
        if (!$model) {
            print('Рассылки не существует' . PHP_EOL);
        }
        print($listId . ' ' . $model->foquzPoll->company->alias . ' ' . $model->foquz_poll_id . ' ' . $model->foquzPoll->name . '  ' . $model->name . PHP_EOL);

        if ($model->status == FoquzPollMailingList::STATUS_NEW && !empty($model->launched_at)) {
            print('Запуск по расписанию ' . $model->launched_at . PHP_EOL);
        } elseif ($model->status == FoquzPollMailingList::STATUS_STARTED) {
            print('Запущенная рассылка ' . $model->launched_at . PHP_EOL);
        } elseif ($model->status == FoquzPollMailingList::STATUS_STOPPED) {
            print('Остановленная рассылка ' . $model->launched_at . PHP_EOL);
        } elseif ($model->status == FoquzPollMailingList::STATUS_NEW) {
            print('Новая рассылка' . PHP_EOL);
        }

        $uid = time();
        $delayed = 2;
        if ($model->status == 0 && $model->launched_at) {
            $delayed = strtotime($model->launched_at) - time();
            if ($delayed < 2) {
                $delayed = 2;
            }
        }

        print('Запуск задачи в ' . date('Y-m-d H:i:s', time() + $delayed) . PHP_EOL);

        $model->queue_job_uid = $uid;
        $model->save(false);
        /** @var Queue $queue */
        $queue = \Yii::$app->rabbit_queue;
        $queueId = $queue
            ->delay($delayed)
            ->push(new StatusMailingPollJob([
                'id'      => $model->id,
                'uidTask' => $uid,
            ]));

        print('Задача ' . $queueId . PHP_EOL);
    }


}
