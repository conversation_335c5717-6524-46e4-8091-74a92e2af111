<?php

use app\modules\foquz\assets\FoquzLandingAsset;
use app\modules\foquz\models\Article;

/** @var Article $article */

$contentList = [
    ['1. Стоп, не паниковать! Перехват негативных отзывов', '#stop'],
    ['2. Великое переселение. Виджеты внутри опросов', '#widgets'],
    ['3. «Читайте-читайте, не буду вас отвлекать». Новый вид отображения — Hello-board', '#hello-board'],
    ['4. Что всегда всплывает? Конечно, тултипы!', '#tooltips'],
    ['5. Плитки — не кафельные, не керамические, а для вариантов ответов', '#tiles'],
    ['6. Расширенные настройки дизайна опросов — да-да, мы все никак не можем остановиться :)', '#design'],
    ['7. Оповещения о событиях на сервере, которым всегда рады', '#notifications'],
    ['8. SPSS–выгрузка для супер аналитики', '#spss'],
    ['9. Ква, ква.. А нет, КВОтирование', '#quotas'],
    ['10. Тегирование анкет', '#tagging'],
    ['Маленькое, но заметное обновление ', '#small-update'],
];

$this->registerJS(
    " window.pageData = { articleId: '" . $article->id . "' } ",
    $this::POS_HEAD
);
$asset = FoquzLandingAsset::register($this);
$this->registerJSFile('/js/landing.article.js', [
    'depends' =>
        [app\modules\foquz\assets\FoquzLandingAsset::class]
]);
$this->registerCSSFile('/js/landing.article.css', [
    'depends' =>
        [app\modules\foquz\assets\FoquzLandingAsset::class]
]); ?>

<?= $this->render('_templates.php') ?>

<div class="b-landing topic-page" data-page data-bind="">
    <div class="b-landing__container">
        <?= $this->render('../templates/landing/header.php', ['page' => 'article'])
        ?>
    </div>
    <div class="b-landing__grow">
        <div class="b-landing__container">
            <div class="foquz-topic__wrapper">
                <article class="foquz-topic foquz-topic--sticky">
                    <?= $this->render('_article-top-sticky.php', [
                        'article' => $article,
                        'contentList' => $contentList
                    ]) ?>
                    <div class="foquz-topic__content ugc">
                        <p>
                            В этом году произошло много интересного. Мы «подросли», стали еще умнее и функциональней. FOQUZ меняется к лучшему, чтобы у вас росли показатели лояльности, увеличивался коэффициент отклика, улучшилась аналитика, а значит, и бизнес-процессы в целом. Но обо всем по порядку.
                        </p>
                        <a id="stop"></a>
                        <h2><a href="#stop">1. Стоп, не паниковать! Перехват негативных отзывов</a></h2>
                        <p>
                            Вы верите в чудеса? Например, что плохие отзывы перестали появляться на отзовиках, картах и официальном сайте, а хороших при этом становится все больше.
                        </p>
                        <p>
                            Мечты воплощаются в реальность с механизмом перехвата негативных отзывов. С помощью инструмента можно улучшить рейтинг компании на различных ресурсах, в том числе за счет увеличения количества положительных отзывов.
                        </p>

                        <img-gallery params="slides: [
                        '/img/topics/features-2024/1.jpg'
                        ]"></img-gallery>

                        <p class="subtitle-big">
                            Как же работает перехват негатива?
                        </p>
                        <p>
                            Представьте, что ваши менеджеры плохо обслужили клиента и он ушел в жутком негативе. Естественной реакцией потребителя будет прийти домой, открыть ноутбук и написать все, что думает о вашей компании, на популярном ресурсе — чтобы «известить» о событии как можно больше потенциальных покупателей или заказчиков.
                        </p>
                        <p>
                            В то же время, если клиент останется довольным продуктом или услугой, то вряд ли с таким же рвением будет оставлять положительный отзыв — в потоке дел об этом можно просто забыть.
                        </p>
                        <p class="fc-list__title">
                            С механизмом перехвата клиентам быстрее и удобнее выражать свое мнение:
                        </p>
                        <ul class="list list--inner">
                            <li>
                                Сразу после события потребитель получает ссылку на короткий опрос о качестве обслуживания.
                            </li>
                            <li>
                                При негативном опыте клиент информируется о том, что жалоба ушла во внутренний профиль компании на отработку ответственным лицам.
                            </li>
                            <li>
                                При положительной оценке клиенту предлагается перейти по ссылке и оставить отзыв на внешней площадке, вроде Яндекс Карты.
                            </li>
                        </ul>
                        <p>
                            В результате процент положительных отзывов в публичном пространстве будет увеличиваться, а негативных снижаться — если бизнес уделит должное внимание отработке жалоб, конечно.
                        </p>
                        <a id="widgets"></a>
                        <h2><a href="#widgets">2. Великое переселение. Виджеты внутри опросов</a></h2>
                        <p>
                            Виджет — это визуальный элемент, который всплывает перед пользователем на сайте. Виджет позволяет собирать оценки посетителей сайта по удобству интерфейса (методика CES), степени удовлетворенности (CSAT) и другим метрикам. Он также может быть чисто информационным, сообщать об условиях акции или технических работах.
                        </p>
                        <p>
                            Ранее код каждого виджета нужно было отдельно встраивать на сайт, и располагались они в разделе «Виджеты». Теперь виджеты есть и внутри опросов, а <b>код достаточно один раз добавить на сайт,</b> чтобы далее формировать, удалять, видоизменять виджеты на свое усмотрение, в том числе из разных опросов. Все настройки осуществляются через интерфейс FOQUZ.
                        </p>
                        <img-gallery params="slides: [
                        '/img/topics/features-2024/2.jpg'
                        ]"></img-gallery>
                        <p>
                            В настройках виджетов опроса появились <b>таргетинги</b> (по тегам, cookie, устройствам и долям пользователей) и <b>триггеры</b> (по посещению страниц, событиям и передаваемым переменным).  Например, виджет можно показывать только пользователям мобильных устройств, которые провели на сайте определенное время.
                        </p>



                        <a id="hello-board"></a>
                        <h2><a href="#hello-board">3. «Читайте-читайте, не буду вас отвлекать». Новый вид отображения — hello-board</a></h2>
                        <p>
                            Среди виджетов, которые показываются автоматически (без дополнительных действий со стороны пользователя, вроде клика на иконку или кнопку), появился новый вид — hello-board.
                        </p>
                        <p>
                            В отличие от page-stop <b>hello-board занимает незначительную часть экрана и не блокирует содержимое страницы.</b> Посетитель либо продолжает изучать контент, либо ставит оценку, проваливается далее в анкету, закрывает виджет, нажав на крестик.
                        </p>
                        <img-gallery params="slides: [
                        '/img/topics/features-2024/3.jpg'
                        ]"></img-gallery>
                        <p>
                            Hello-board меньше раздражает клиентов, ведь при желании его можно просто проигнорировать. А еще его очень легко настраивать!
                        </p>

                        <a id="tooltips"></a>
                        <h2><a href="#tooltips">4. Что всегда всплывает? Конечно, тултипы!</a></h2>
                        <p>
                            В типы вопросов «Варианты ответов», «Классификатор», «3D-матрица» теперь можно добавлять <b>тултипы</b> — всплывающие подсказки.  Да-да, это те самые умники, которые прячутся за знаком вопроса рядом со строкой, столбцом, вариантом ответа. По клику на вопрос возникает краткий поясняющий текст, а за ним и прозрение)
                        </p>
                        <img-gallery params="slides: [
                        '/img/topics/features-2024/4.jpg'
                        ]"></img-gallery>

                        <a id="tiles"></a>
                        <h2><a href="#tiles">5. Плитки — не кафельные, не керамические, а для вариантов ответов</a></h2>
                        <p>
                            Опрос с вариантами ответов обычно выглядит так: вопрос, ниже списком готовые ответы, в некоторых случаях есть еще окошко, где респондент может вписать свой комментарий.
                        </p>
                        <p>
                            Мы расширили возможности этого типа вопросов. <b>Варианты ответов теперь можно добавлять плиткой, включающей изображение или видео.</b>
                        </p>
                        <img-gallery params="slides: [
                        '/img/topics/features-2024/5.jpg'
                        ]"></img-gallery>
                        <p>
                            Это особенно удобно, когда при ответе нужно вспомнить, как что-то выглядит (например, стиль интерьера), либо выбрать наиболее привлекательный вариант (допустим, какая иконка в интерфейсе приложения самая понятная). Теперь вы можете сделать опросы еще красивее и проявить свои творческие способности!
                        </p>
                        <a id="design"></a>
                        <h2><a href="#design">6. Расширенные настройки дизайна опросов — да-да, мы все никак не можем остановиться :)</a></h2>
                        <p>
                            В FOQUZ и раньше было много возможностей настройки дизайна — от выбора фона, цвета шапки, размера и наименования шрифтов, до отображения процесса прохождения анкеты (номера страниц, шкала прохождения и прочее). Теперь вариантов визуализации стало еще больше.
                        </p>
                        <p class="fc-list__title">
                            Что добавили:
                        </p>
                        <ul>
                            <li>
                                радиус скругления углов для регулирования формы кнопок;
                            </li>
                            <li>
                                цвета обводки, чтобы задавать кнопкам желаемый контур;
                            </li>
                            <li>
                                разделение дизайн для разных кнопок;
                            </li>
                            <li>
                                настраиваемую зону под логотип.
                            </li>
                        </ul>

                        <img-gallery params="slides: [
                        '/img/topics/features-2024/6.jpg'
                        ]"></img-gallery>
                        <p>
                            Все новые настройки можно сохранить как тему, чтобы использовать в последующих опросах.
                        </p>

                        <a id="notifications"></a>
                        <h2><a href="#notifications">7. Оповещения о событиях на сервере, которым всегда рады</a></h2>
                        <p>
                            Webhook (вебхук) всегда в курсе дел. Это технология, которая позволяет узнавать о событиях на стороннем сервере, в частности, о новых ответах на опрос. Передаваемые данные включают ФИО и номер респондента, наименование опроса, филиал, ID, статус прохождения опроса и прочее. Перечень передаваемых параметров можно расширять через настройку пользовательских полей.
                        </p>

                        <img-gallery params="slides: [
                        '/img/topics/features-2024/7.jpg'
                        ]"></img-gallery>

                        <p>
                            Зачем мы про это рассказываем? Конечно, потому что наши пользователи теперь могут автоматически получать ответы на опрос на свой сервер.
                        </p>
                        <p>
                            Напоминаем, что подключение вебхука осуществляется через нашу команду техподдержки!
                        </p>

                        <a id="spss"></a>
                        <h2><a href="#spss">8. SPSS–выгрузка для супер аналитики</a></h2>
                        <p>
                            Ответы на опросы FOQUZ можно будет выгружать в формате для SPSS Statistics, чтобы у вас было больше возможностей анализа полученных ответов и базы контактов.
                        </p>
                        <img-gallery params="slides: [
                        '/img/topics/features-2024/8.jpg'
                        ]"></img-gallery>
                        <a id="quotas"></a>
                        <h2><a href="#quotas">9. Ква, ква.. А нет, КВОтирование</a></h2>
                        <p class="fc-list__title">
                            В сервисе появилась возможность устанавливать квоты (нормы) на:
                        </p>
                        <ul>
                            <li>
                                <span><b>Ссылки на опрос.</b> Создавать теперь можно множество ссылок для каждого опроса и каждого филиала.</span>
                            </li>
                            <li>
                                <span><b>Ответы на вопросы.</b> Квоты применяется как к отдельным вопросам, так и к нескольким сразу.</span>
                            </li>
                        </ul>
                        <img-gallery params="slides: [
                        '/img/topics/features-2024/9.jpg'
                        ]"></img-gallery>
                        <p>
                            Например, вам нужно собрать оценки 1 000 респондентов из столицы в возрасте до 30 лет. Если квота по ответам на вопросы про город и возраст будет заполнена, то респонденты будут попадать на конечный экран.
                        </p>

                        <a id="tagging"></a>
                        <h2><a href="#tagging">10. Тегирование анкет</a></h2>
                        <p>
                           <b>Теги</b> — это метки, которые используются для категоризации заполненных анкет, то есть их разделения по определенным значениям.
                        </p>
                        <p>
                            Сейчас в FOQUZ можно тегировать вопросы и контакты. Теперь это работает и для анкет. Тег можно присваивать каждой анкете, чтобы распределять их на категории и какие-либо группы. Все это расширяет возможности для обработки анкет по статусам или отделам, которые должны обработать ответ респондента.
                        </p>
                        <img-gallery params="slides: [
                        '/img/topics/features-2024/10.jpg'
                        ]"></img-gallery>

                        <a id="small-update"></a>
                        <h2><a href="#small-update">Маленькое, но заметное обновление</a></h2>
                        <p>
                            В сервисе исчез раздел «Клиенты», но все клиенты по прежнему с нами :) Опять чудеса, скажете вы. Но здесь все просто: мы всего лишь переименовали раздел и теперь он называется «Контакты».
                        </p>
                        <p>
                            Среди наших пользователей уже давно не только исследователи клиентского опыта и маркетологи, но и HR-менеджеры, эксперты, аудиторы и многие другие. Мы решили, что этот раздел должен включать всех, кто проходит опросы, а не только клиентов. Поэтому и совершили этот «отчаянный» ход с переименованием :)
                        </p>

                        <div class="include__edit_margin">
                            <?= $this->render('_include.php'); ?>
                        </div>

                    </div>
                    <?= $this->render('_article-bottom.php', ['pollsCategory' => $article->polls_category]) ?>
                </article>
                <aside class="foquz-topic__sidebar foquz-topic__sidebar--sticky">
                    <div class="action-block__sticky">
                        <?= $this->render('_article-sidebar.php') ?>
                    </div>
                </aside>
            </div>
            <?= $this->render('_article-recommendations.php') ?>
        </div>
    </div>
    <?= $this->render('../templates/landing/footer.php', [
        'page' => 'article',
        'article' => $article->name
    ]) ?>
</div>

