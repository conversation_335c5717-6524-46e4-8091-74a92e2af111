 <?
$html_email = file_get_contents("../mail/mail.php");
$text_email = file_get_contents("../mail/mail_text.txt");
$admin_email_1 = file_get_contents("../use_config/admin_1.txt");
$admin_email_2 = file_get_contents("../use_config/admin_2.txt");


?>

 <style>
	form
	{
		margin: 40px 0;
	}
	var
	{
		clear: both;
		margin: 0 50px;
	}
	input.switch:empty
	{
		margin-left: -999px;
	}
	input.switch:empty ~ label
	{
		position: relative;
		float: left;
		line-height: 1.6em;
		text-indent: 4em;
		margin: 0.2em 0;
		cursor: pointer;
	    
	      -webkit-user-select: none;
	         -moz-user-select: none;
	          -ms-user-select: none;
	              user-select: none;
	}
	input.switch:empty ~ label:before, 
	input.switch:empty ~ label:after
	{
		position: absolute;
		display: block;
		top: 0;
		bottom: 0;
		left: 0;
		content: ' ';
		width: 3.6em;
		background-color: #1bef71;
		border-radius: 0.3em;
		box-shadow: inset 0 0.2em 0 rgba(0,0,0,0.3);
	    
		  -webkit-transition: all 100ms ease-in;
	                  transition: all 100ms ease-in;
	}
	input.switch:empty ~ label:after
	{
		width: 1.4em;
		top: 0.1em;
		bottom: 0.1em;
		margin-left: 0.1em;
		background-color: #fff;
		border-radius: 0.15em;
		box-shadow: inset 0 -0.2em 0 rgba(0,0,0,0.2);
	}
	input.switch:checked ~ label:before
	{
		background-color: #393;
	}
	input.switch:checked ~ label:after
	{
		margin-left: 2.1em;
	}
		}

</style>
 <ul class="breadcrumb">
	<li>
		<a href="/settings">Главная</a> <span class="divider">/</span>
	</li>
	<li class="active">
		<a href="/settings">Настройки</a> <span class="divider">/</span>
	</li>
	<li class="active">
		Настройка почты
	</li>
</ul>
<div class="col-xs-12">
	<div class="jumbotron box text-center">
	<h4 class="text-center">Настройка почты и отправки письма для Администрирование</h4>
	<hr>
	<form action="javascript:void(null);" id='edit_email'  >
		<h4 class="" >Настройки почты </h4>
		<div class="jumbotron box " style="padding-left: 25%; padding-top: 0px">
			<div class="row">
				<div class="form-group">
				    <label class="col-sm-2 control-label">Host</label>
				    <div class="col-sm-6">
		      			<input type="text" name='host' value="<?=$email_config->host?>" class="form-control" id="inputPassword3" placeholder="Host">
				    </div>
				</div>
			</div><br>
			<div class="row">
				<div class="form-group">
					<label class="col-sm-2 control-label">Username</label>
					<div class="col-sm-6">
						<input type="text" name='username' value="<?=$email_config->username?>" class="form-control" id="inputPassword3" placeholder="username">
					</div>
				</div>
			</div><br>
			<div class="row">
				<div class="form-group">
					<label class="col-sm-2 control-label">Password</label>
					<div class="col-sm-6">
						<input type="text" name='password' value="<?=$email_config->password?>" class="form-control" id="inputPassword3" placeholder="Password">
					</div>
				</div>
			</div><br>
			<div class="row">
				<div class="form-group">
					<label class="col-sm-2 control-label">Port</label>
					<div class="col-sm-6">
						<input type="text" name='port' value="<?=$email_config->port?>" class="form-control" id="inputPassword3" placeholder="Port">
					</div>
				</div>
			</div><br>
			<div class="row">
				<div class="form-group">
					<label class="col-sm-2 control-label">Encryption</label>
					<div class="col-sm-6">
						<input type="text" name='encryption' value="<?=$email_config->encryption?>" class="form-control" id="inputPassword3" placeholder="Encryption">
					</div>
				</div>
			</div><br>
			<div class="col-sm-3" style="padding-right: 0; padding-left: 0;
">
				<input type="submit" id='submit' value="Сохранить" name='submit' class="btn btn-default form-control" ">
			</div>
		</form>
		<div class="col-sm-5" >
			<button id='backup' data-backup='1' value="" class="btn btn-primary form-control">
				Сделать бэкап конфига
			</button>
		</div><br>
		<div class="col-sm-8" style="padding-top: 10px; padding-left: 0;">

			<button id='backup_revert' data-backup='1' value="" class="btn btn-default form-control">
					Вернуть конфиг email c бэкапа
				</button>
			</div>
		</div>
	</div>
</div>
<div class="col-xs-12">
	<div class="jumbotron box text-center">
		<h4>Отправка письма Администратору, если будет ошибки сервиса:</h4><br>
		<div class="row">
			<div class="form-group text-center">
	      		<div class="row col-sm-offset-2">
				    <label class="col-sm-2  control-label">Email admin 1</label>
					<div class="col-sm-6 email_admins">
		      			<input type="text" name='admin_email_1' data-admin='1'  value="<?=$admin_email_1?>" class="form-control" id="inputPassword3" placeholder="email administartor">
		      		</div>
		      	</div>
		      	<br>
	      		<div class="row col-sm-offset-2">
				    <label class="col-sm-2 control-label">Email admin 2</label>
					<div class="col-sm-6 email_admins">
		      			<input type="text" name='admin_email_2' data-admin='2' value="<?=$admin_email_2?>" class="form-control" id="inputPassword3" placeholder="email administartor">
				    </div>
				</div>
			</div>
		</div>
	</div>
</div>


<div class="col-xs-12">
	<div class="jumbotron box text-center">
	<p>Настройка отправки письма для клиентов</p>
		<form class="form-horizontal">
	  <div class="form-group">
	    <label for="inputEmail3" class="col-sm-2 control-label">Тема письма (subject)</label>
	    <div class="col-sm-10">
	      <input type="text" value="<?=$data->email_texts->subject?>" class="form-control" id="subject" placeholder="Тема письма">
	    </div>
	  </div>
	  <div class="form-group">
	    <label for="inputPassword3" class="col-sm-2 control-label">SetForm (от кого)</label>
	    <div class="col-sm-10">
	      <input type="text" value="<?=$data->email_texts->setFrom[0]?>" class="form-control" id="setfrom_1" placeholder="Email">
	      <input type="text" value="<?=$data->email_texts->setFrom[1]?>" class="form-control" id="setfrom_2" placeholder="Имя">
	    </div>
	  </div>
	</form>
	
</div>

<div class="col-xs-12">
<div class="row">
	<div class="jumbotron box">
		<p class="text-info text-left">Обычное письмо \ HTML+PHP письмо</p>
		<var>
		  <input id="switch1" class="switch" name="switch1" data-checked="<?=$data->email_texts->is_html ? '1' : '0'?>" type="checkbox" <?=$data->email_texts->is_html?'checked' : ''; ?> />
		  <label for="switch1">Текст\Html+Текст</label>
		</var>
	</div>
	</div>
</div>
	<div class="jumbotron box text-center">
	<h4 class="text-center">Тело письма</h4>
	<p class="text-center text-success">Для письма с содержанием HTML+PHP</p>
	<textarea cols='120' class='form-control' id="email_html" <? echo $data->email_texts->is_html ? "" : 'disabled'; ?> rows='10' wrap="soft | hard">
		<?=$html_email?>
	</textarea><br>
	<p class="text-center text-success">Для письма без содержанием HTML+PHP</p>
	<textarea cols='120' class='form-control' <? echo $data->email_texts->is_html ? "disabled" : ''; ?> id="email_text" rows='10' wrap="soft | hard">
		<?=$text_email?>
	</textarea><br>
	<form id="form">
	<h4 class="text-info">Отправить письмо на email почту (Для теста)</h4>
	<input type="email" id="to_email" class="form-control" placeholder="Укажите email получателья">
	<br>
	<input type="submit" class="btn " id="send_email" value="Отправить">
	</form>
	</div>

</div>



<script type="text/javascript">
$('#send_email').click(function () {
		$('#send_email').attr('value','Отправляется...');
		$('#send_email').prop('disabled',"disabled");
		$.ajax({
			url: 'send_email',
			data: 'email_adress='+$('#to_email').val()+'&link='+$('#link').text(),
			success: function (data) {
				if (data == '1'){
					$('#send_email').attr('value','Отправлен');
					$('#to_email').val('');
					$('#send_email').removeClass('btn-warning');
					$('#send_email').addClass('btn-success');
				}else{
					$('#send_email').attr('value','Не удалось');
					$('#to_email').attr('value', '');
					$('#send_email').removeClass('btn-success');
					$('#send_email').addClass('btn-error');
				}
				$('#send_email').prop('disabled',false);
			}

		});
});
	
$('#switch1').change(function () {
	if ($(this).attr('data-checked') != "0"){
		$(this).attr("data-checked", "0")
	}else{
		$(this).attr("data-checked", "1")
	}
	if ($(this).attr("data-checked") =='0'){
		$('#email_html').attr('disabled', 'disabled');
		$('#email_text').removeAttr('disabled');
		$.ajax({
			url: 'config/changetypeemail',
			data: {'type': "text"},
			success: function (res) {
				if (res != 'Success')
					alert('Переключатель сработал не корректно (проверти обнавив страницу)');
			},
			error: function () {
				alert('Ошибка сервера');
			}
		})
	}else{
		$('#email_html').removeAttr('disabled');
		$('#email_text').attr('disabled', 'disabled');
		$.ajax({
			url: 'config/changetypeemail',
			data: {'type': "html"},
			success: function (res) {
				if (res != 'Success')
					alert('Переключатель сработал не корректно (проверти обнавив страницу)');
			},
			error: function () {
				alert('Ошибка сервера');
			}
		})
	}
})


$("#backup_revert").on('click', function () {
	$.ajax({
			url: 'config/revertemail',
			beforeSend: function () {
				$(this).text("Попытка вернуть бэкап...");
			},
			success: function (response, error){
				location.reload()
				

			},
			error: function () {
				$(this).text("Ошибка сервера");
				
			},
			complete: function () {
				
			}
		})
})

$("#backup").on('click', function () {
	if( $(this).attr('data-backup') == "1" ) {
		$.ajax({
			url: 'config/savebackup',
			beforeSend: function () {
				$("#backup").text("Сохранение бэкап конфига");
			},
			success: function (response, error){
				if (response == 'Success'){
					setTimeout(function () {
						$("#backup").text("Сделать бэкап конфига");
						$("#backup").removeClass("btn-success");
					}, 2000);
				}else{
					alert(response);
					$("#backup").text("Ошибка сохранение файла");
				}
			},
			error: function () {
				$("#backup").text("Ошибка сервера");
				
			}
		})
	}else if ( $(this).attr('data-backup') == "2") {
		$.ajax({
			url: 'config/revertemail',
			beforeSend: function () {
				$("#backup").text("Попытка вернуть бэкап...");
			},
			success: function (response, error){
				location.reload()

			},
			error: function () {
				$("#backup").text("Ошибка сервера");
				
			},
			complete: function () {
				
			}
		})
	}
})

$("#submit").on('click', function () {
	$.ajax({
		url: 'config/email',
		data: $('#edit_email').serialize(),
		beforeSend: function () {
			$("#submit").attr("value", "Сохраняется...");
		},
		success: function (response, error){
			if (response == 'Success saved'){
				$("#submit").addClass("btn-success");
				$("#submit").attr("value", "Данные обновлены");
				setTimeout(function () {
					$("#submit").attr("value", "Сохранить");
					$("#submit").removeClass("btn-success");
				}, 5000);
			}else{
				alert("Ошибка во время обновлены конфига");
				$("#submit").addClass("btn-error");
				$("#submit").attr("value", "Данные не обновлены");
				setTimeout(function () {
					$("#submit").attr("value", "Сохранить");
					$("#submit").removeClass("btn-success");
				}, 5000);
				$('#backup').text('Вернуть последний бэкап');
				$('#backup').addClass('btn-success');
				$('#backup').attr('data-backup', 2)
			}

		},
		error: function () {
			alert("Ошибка во время обновлены конфига");
			$("#submit").addClass("btn-danger");
			$("#submit").attr("value", "Данные не обновлены");
			setTimeout(function () {
				$("#submit").attr("value", "Ошибка конфига");
				$("#submit").removeClass("btn-success");
			}, 5000);
			$('#backup').text('Вернуть последний бэкап');
			$('#backup').addClass('btn-success');
			$('#backup').attr('data-backup', 2)
		}
	});
})


$('.email_admins > input').blur(function () {
	var number_admin = $(this).attr('data-admin');

	$.ajax({
		url: 'config/emailadmin',
		data: {'number_admin' : number_admin, "admin_email" :$(this).val() },
		success: function (response) {
			if (response == 'Success'){
				$('input[data-admin='+ number_admin +']').css('border', '1px solid green');
			}else if (response == 'error'){
				$('input[data-admin='+ number_admin +']').css('border', '1px solid red');
				alert('Ошибка сохранение')

			}
		},
		error: function () {
			alert('Ошибка передачи')
		}
	})
})

$('#email_text').blur(function () {
	$.ajax({
		url: 'config/changeemail',
		data: {"email_text" :$(this).val(), 'type':'text' },
		success: function (response) {
			if (response == 'Success'){
				$('#email_text').css('border', '1px solid green');
			}else if (response == 'error'){
				$('#email_text').css('border', '1px solid red');
				alert('Ошибка сохранение');

			}
		},
		error: function () {
			alert('Ошибка передачи');
		}
	})
})

$('#email_html').blur(function () {
	$.ajax({
		url: 'config/changeemail',
		data: {"email_text" :$(this).val(), 'type':'html' },
		success: function (response) {
			if (response == 'Success'){
				$('#email_html').css('border', '1px solid green');
			}else if (response == 'error'){
				$('#email_html').css('border', '1px solid red');
				alert('Ошибка сохранение');

			}
		},
		error: function () {
			alert('Ошибка передачи');
		}
	})
})


$('#subject').blur(function () {
	$.ajax({
		url: 'config/chengesublect',
		data: {"subject" :$(this).val()},
		success: function (response) {
			if (response == 'Success'){
				$('#subject').css('border', '1px solid green');
			}else if (response == 'error'){
				$('#subject').css('border', '1px solid red');
				alert('Ошибка сохранение');

			}
		},
		error: function () {
			alert('Ошибка передачи');
		}
	})
})

$('#setfrom_1').blur(function () {
	$.ajax({
		url: 'config/setfrom',
		data: {"setfrom_1" :$(this).val()},
		success: function (response) {
			if (response == 'Success'){
				$('#setfrom_1').css('border', '1px solid green');
			}else if (response == 'error'){
				$('#setfrom_1').css('border', '1px solid red');
				alert('Ошибка сохранение');

			}
		},
		error: function () {
			alert('Ошибка передачи');
		}
	})
})

$('#setfrom_2').blur(function () {
	$.ajax({
		url: 'config/setfrom',
		data: {"setfrom_2" :$(this).val()},
		success: function (response) {
			if (response == 'Success'){
				$('#setfrom_2').css('border', '1px solid green');
			}else if (response == 'error'){
				$('#setfrom_2').css('border', '1px solid red');
				alert('Ошибка сохранение');

			}
		},
		error: function () {
			alert('Ошибка передачи');
		}
	})
})

</script>