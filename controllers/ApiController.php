<?php
namespace app\controllers;

use Yii;

use yii\web\Controller;
use yii\helpers\ArrayHelper;
use app\models\Poll;
use app\modules\foquz\models\FoquzPollAnswer;

/** @deprecated чот-нибудь отвалится у ХМ, но надо бы удалить */
class ApiController extends Controller
{
    /*
	public $enableCsrfValidation = false;
    const DISBLEDEAT = [53];

	public function actionIndex($name='Гость')
	{
        exit;
		return $name;
	}



    public function actionForPbi()  {
        $this->layout = null;
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $key =\Yii::$app->request->post("key", null);
        if ($key && $key==\Yii::$app->params["authkey2"]) {
            $start = \Yii::$app->request->post("start_time", null);
            $end = \Yii::$app->request->post("end_time", null);
            if ($start && $end) {
                $start = @strtotime($start); $end = @strtotime($end);
                if ($start && $end) {
                    $start = date("Y-m-d H:i:s", $start);
                    $end = date("Y-m-d H:i:s", $end);

                    $sql = "
                        select o.order_number order_id, p.created postdate, f.iiko_id filial_id, s.type_code post_code, s.score from scores s 
                        inner join polls p on (s.poll_id=p.id and p.updated>'$start' and p.updated<'$end')
                        inner join orders o on (p.order_id=o.id)
                        inner join filials f on (o.filial_id=f.id)
                    ";
                    $posts = Yii::$app->db->createCommand($sql)->queryAll();

                    $sql = "
                        select o.order_number order_id, p.created postdate, f.iiko_id filial_id, d.crm_id dish_id, s.score from score_dishes s 
                        inner join polls p on (s.poll_id=p.id and p.updated>'$start' and p.updated<'$end')
                        inner join orders o on (p.order_id=o.id)
                        inner join filials f on (o.filial_id=f.id)
                        inner join dishes d on (s.dish_id=d.id)                        
                    ";
                    $dishes = Yii::$app->db->createCommand($sql)->queryAll();

                    $sql = "
                        select o.order_number order_id, p.created postdate, f.iiko_id filial_id, s.type_code post_code, sto.id option_id, sto.name option_name from scores s 
                        inner join polls p on (s.poll_id=p.id and p.updated>'$start' and p.updated<'$end')
                        inner join score_options so ON (so.score_id=s.id)
                        inner join score_types_options sto ON (sto.id=so.option_id)
                        inner join orders o on (p.order_id=o.id)
                        inner join filials f on (o.filial_id=f.id)
                    ";
                    $options = Yii::$app->db->createCommand($sql)->queryAll();

                    $data = [
                        "posts" => $posts,
                        "dishes" => $dishes,
                        "options" => $options,
                    ];

                    print(json_encode($data));

                    exit;


                }
            }
        }
    }

    public function actionKeysOpros() {
        $this->layout = null;
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $key =\Yii::$app->request->post("key", null);
        if ($key && $key==\Yii::$app->params["authkey2"]) {
            $start = \Yii::$app->request->post("start", null);
            $end = \Yii::$app->request->post("end", null);
            $start = @strtotime($start); $end = @strtotime($end);
            if ($start && $end) {
                $start = date("Y-m-d H:i:s", $start);
                $end = date("Y-m-d H:i:s", $end);
                $sql = "
                    select o.order_number o, p.key k, p.status_id s from 
                    (select * from polls where created>='$start' and created<='$end') p 
                    inner join orders o ON (p.order_id = o.id)
                ";
                $rows = Yii::$app->db->createCommand($sql)->queryAll();
                print(json_encode($rows));
                exit;
            }
        }        

        die("no!");
    }



    public function actionMailings() {
        $result = []; 

        $this->layout = null;
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $key =\Yii::$app->request->post("key", null);
        if ($key && $key==\Yii::$app->params["authkey2"]) {
            $start = \Yii::$app->request->post("start_time", null);
            $end = \Yii::$app->request->post("end_time", null);
            $id = intval(\Yii::$app->request->post("id", null));
            $company_id = intval(\Yii::$app->request->post("company_id", null));
            if ($start && $end && $company_id) {
                $start = @strtotime($start); $end = @strtotime($end);
                if ($start && $end) {            
                    $start = date("Y-m-d H:i:s", $start);
                    $end = date("Y-m-d H:i:s", $end);


                    $sql = "select m.id mid, s.id, m.description mname, p.id pid, p.title pname, s.sended, c.email, c.phone, cc.name t, dpc.code from mailing_list_send s 
                    left join foquz_contact c ON (s.foquz_contact_id=c.id) 
                    left join mailings_channel_repeats r ON (s.repeat_id=r.id) 
                    left join mailings_channels cc ON (s.channel_id=cc.id)
                    left join mailings m ON (cc.mailing_id=m.id)
                    left join discount_pool p ON (cc.pool_id=p.id)
                    left join discount_pool_code dpc ON (dpc.pool_id=p.id and dpc.mailing_id=m.id and dpc.contact_id=c.id)
                    where s.sended>='$start' and s.sended<'$end' and m.company_id = $company_id and s.id>$id and cc.name in ('Email', 'SMS')
                    order by s.id limit 5000 ";

                    $rows = Yii::$app->db->createCommand($sql)->queryAll();
                    print(json_encode($rows));
                    exit;                    
                }
            }
        }
    }

    public function actionScoresPbi() {
        $this->layout = null;
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $pollID = 849;
        $site = \Yii::$app->request->post("site", null);
        //print($site); exit;
        if ($site=="z2s.foquz.ru") {
            $pollID = 2915;
        }

        $result = [];

        $key =\Yii::$app->request->post("key", null);
        if ($key && $key==\Yii::$app->params["authkey2"]) {
            $start = \Yii::$app->request->post("start_time", null);
            $end = \Yii::$app->request->post("end_time", null);

            if ($start && $end) {
                $start = @strtotime($start); $end = @strtotime($end);
                if ($start && $end) {
                    $start = date("Y-m-d H:i:s", $start);
                    $end = date("Y-m-d H:i:s", $end);

                    $answers= FoquzPollAnswer::find() 
                    ->andWhere(["between", "updated_at", $start, $end])
                    ->andWhere(["foquz_poll_id"=>$pollID])
                    ->andWhere(["IN", "status", ['done', 'in-progress']])
                    ->with([
                        //"contact",
                        "order.filial",
                        "foquzPollDishes",
                       // "processing" => function ($q) { $q->with("moderator"); },
                    ])
                    ->all();

                    foreach ($answers as $answer) {
                        $date = date("Y-m-d", strtotime($answer->updated_at));
                        if (!isset($result[$date])) $result[$date] = [];
                        if ($answer->order && $answer->order->filial) {
                            $filial = $answer->order->filial->name;
                            if (!isset($result[$date][$filial])) $result[$date][$filial] = [];

                            foreach ($answer->foquzAnswer as $i) {
                                $question = $i->question_name;
                                $attributes=$i->attributes;

                                $rating = 0;
                                if ($question=="Товар") {
                                    foreach ($answer->foquzPollDishes as $s) {
                                       $sum=0; $number=0;
                                        if ($s->score &&  $s->score>=1 && $s->score<=5)  {
                                            $sum += $s->score; $number++;
                                        }
                                    }
                                    if ($number>0) $rating=$sum/1.0/$number;
                                } else {
                                    if (isset($attributes["rating"]) && $attributes["rating"]) {
                                        $rating=$attributes["rating"];
                                    }
                                }
                                if ($rating>=1 and $rating<=5) {
                                    if (!isset($result[$date][$filial][$question])) $result[$date][$filial][$question] = ["sum"=>0, "number"=>0];
                                    $result[$date][$filial][$question]["sum"]+=$rating;
                                    $result[$date][$filial][$question]["number"]++;
                                }
                            }

                        }
                    }
                }
            }

            return $result;
        }

    }

    public function actionPolls() {
        $result = []; 

        $this->layout = null;
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $key =\Yii::$app->request->post("key", null);
        if ($key && $key==\Yii::$app->params["authkey2"]) {
            $start = \Yii::$app->request->post("start_time", null);
            $end = \Yii::$app->request->post("end_time", null);

            if ($start && $end) {
                $start = @strtotime($start); $end = @strtotime($end);
                if ($start && $end) {
                    $start = date("Y-m-d H:i:s", $start);
                    $end = date("Y-m-d H:i:s", $end);

                    $polls= FoquzPollAnswer::find() 
                    ->andWhere(["between", "updated_at", $start, $end])
                    ->andWhere(["foquz_poll_id"=>849])
                    ->andWhere(["IN", "status", ['done', 'in-progress']])
                    ->with([
                        "contact",
                        "order",
                        "foquzPollDishes" => function ($q) { $q->with("dish"); },
                        "processing" => function ($q) { $q->with("moderator"); },
                    ])
                    ->all();





                    foreach ($polls as $poll) {
                        $r  = [
                            "key" => $poll->auth_key,
                            "comment" => "",
                            "created" => $poll->created_at,
                            "updated" => $poll->updated_at,
                            "order_id" => null,
                            "status" => null,
                            "processing_status" => null,
                            "scores" => [],
                            "dishes" => [],
                            "phone" => $poll->contact? $poll->contact->phone : null,
                            "email" => $poll->contact? $poll->contact->email : null
                        ];

                        foreach ($poll->foquzAnswer as $i) {
                            $code = null;
                            if ($i->question_name=="Курьер") $code = "driver";
                            else if ($i->question_name=="Оператор") $code = "operator";

                            if ($i->self_variant) {
                                $r["comment"] .= $r["comment"] ? " \n\r" : "";
                                $r["comment"] .= $i->self_variant;
                            }
                            
                            if ($code) {
                                $r["scores"][] = [
                                    "code" => $code,
                                    "comment" => $i->self_variant,
                                    "score" => $i->rating,
                                ]; 
                            }
                        }

                        foreach ($poll->foquzPollDishes as $s) {
                            $r["dishes"][] = [
                                "dish_id" => $s->dish->crm_id,
                                "score" => $s->score,
                            ];
                        }

                      //  if ($poll->status) {
                            $r["status"] = [
                                "id" => $poll->status=="done" ? 7: 6,
                                "name" => $poll->status=="done" ? "Завершена" : "Пользователь начал заполнять"
                            ];
                        
                      
                        if ($poll->processing) {

                            $r["processing_status"] = [
                                "id" => $poll->processing->status,
                                "name" => $poll->processing->statusName,
                                "history" => [],
                                "result" => [
                                    "datetime" => $poll->processing->resolved_at, 
                                    "comment" => $poll->processing->comment,
                                    "username" => $poll->processing->moderator ? $poll->processing->moderator->name : '',
                                ]
                            ];


                        }
                        
                        if ($poll->order) {
                            $r["order_id"] = $poll->order->order_number;
                        }


                        //if ($r["comment"]) return $r;

                        $result[] = $r;
                    }
                }
            }
        }

        print(json_encode($result));
        exit;
    }

    public function actionDeliveryScores() {
        $this->layout = null;
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $rows=[];

        $key =\Yii::$app->request->post("key", null);
        if ($key && $key==\Yii::$app->params["authkey2"]) {
            $start = \Yii::$app->request->post("start_time", null);
            $end = \Yii::$app->request->post("end_time", null);
            if ($start && $end) {
                $start = @strtotime($start); $end = @strtotime($end);
                if ($start && $end) {
                    $start = date("Y-m-d H:i:s", $start);
                    $end = date("Y-m-d H:i:s", $end);

                    $polls= FoquzPollAnswer::find() 
                    ->andWhere(["between", "updated_at", $start, $end])
                    ->andWhere(["foquz_poll_id"=>849])
                    ->andWhere(["IN", "status", ['done', 'in-progress']])
                    ->with([
                        "order",
                    ])
                    ->all();

                    foreach ($polls as $poll) {
                        foreach ($poll->foquzAnswer as $i) {
                            if ($i->question_name=="Курьер") {
                                if ($i->rating) {
                                    $rows[] = [
                                        "score" => $i->rating,
                                        "order_number" => $poll->order->order_number,
                                        "updated" => $poll->updated_at
                                    ];
                                }
                                break;
                            }
                        }
                    }

                    print(json_encode($rows));

                    exit;


                }
            }
        }
        return false;
    }

    public function actionSend_message(
        $phone=Null, $email=Null,
        $order_number=Null, $order_list=Null,
        $name_driver=Null, $name_operator=Null,
        $branch=Null, $key=Null)
    {
        $order_number = intval($order_number);

        if ((int) $key != (int) \Yii::$app->params["authkey"]) {
            return  "Error key";
        }

        $filial = \app\models\Filial::find()->where(["name"=>$branch])->one();
        if (!$filial) {
            return "Not branch in base";
        }

        $dList = explode(',', $order_list); $dishes = [];
        if (is_array($dList) && count($dList)>0) {
            $dishes = \app\models\Dish::find()->where(["crm_id"=>$dList])->all();
            if (count($dishes)!=count($dList)) {
                $dishesName = ArrayHelper::map($dishes, "crm_id", "name");
                $notDishes = [];

                foreach ($dList as $d) {
                    if (!isset($dishesName[$d])) {
                        if ($d != '' && !in_array($d, self::DISBLEDEAT)){
                            $notDishes[] = $d;
                        }
                    }
                }

                if (count($notDishes)>0) {
                  //  Email::admin_send(implode(", ", $notDishes) ." - Нету в базе опросника","Блюда, которого нет в базе");
                }
            }
        }

        if (count($dishes)==0) return  "Not eat in base";
        if ((int)$phone == 0) return 'Not validated number';
        if ($name_driver == "" and $name_operator == "") return "No staff selected";

        $poll = false;
        $order = \app\models\Order::find()->where(["order_number"=>$order_number])->one();
        if ($order) {
            $poll = \app\models\Poll::find()->where(["order_id"=>$order->id])->exists();
        }

        if ($poll)
            return 'Already sent';
        else {
            $result = \app\models\Poll::create(
                $phone=$phone, 
                $email=$email,
                $order_number=$order_number, 
                $name_driver=$name_driver, 
                $name_operator=$name_operator,
                $filial=$filial, 
                $dishes=$dishes
            );
        }
        

        return $result;
    }
*/
}