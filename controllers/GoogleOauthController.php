<?php
namespace app\controllers;

use app\models\google\GoogleClientService;
use app\models\google\GoogleSheetsParams;
use app\models\google\sheets\Answers;
use app\models\google\sheets\Answers2;
use yii\web\BadRequestHttpException;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * @deprecated Функционал не опубликован - так как на средних выгрузках работает очень плохо
 */
class GoogleOauthController extends Controller
{
    /**
     * @deprecated Никогда не был опубликован
     * @throws BadRequestHttpException
     */
    public function actionOauth($code = null, $state = null)
    {
        throw new NotFoundHttpException('Функционал отключен');

        $service = new GoogleClientService($state);
        $params = GoogleSheetsParams::findOne($state);
        if ($params === null) {
            throw new BadRequestHttpException(['Не найдены параметры запроса']);
        }
        $client = $service->getClient();

        if ($code) {
            $token = $client->fetchAccessTokenWithAuthCode($code);
            $client->setAccessToken($token);

            if ($params->table === GoogleSheetsParams::ANSWERS) {
                $sheet = new Answers($client, $params->params);
            }
            if ($params->table === GoogleSheetsParams::ANSWERS2) {
                $sheet = new Answers2($client, $params->params);
            }

            return $this->redirect($sheet->getTableUrl());
        }

        return false;
    }
}