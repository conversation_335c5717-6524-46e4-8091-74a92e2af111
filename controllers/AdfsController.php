<?php

namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\helpers\Url;
use app\models\company\Company;
use app\modules\foquz\models\FoquzPoll;
use app\models\User;
use app\models\company\CompanyStaff;
use app\modules\foquz\models\EditorFolder;
use yii\web\ForbiddenHttpException;


class AdfsController extends Controller {

    // Remove CSRF protection
    public $enableCsrfValidation = false;

    public function actions() {
        return [
            'saml' => [
                'class' => 'asasmoyo\yii2saml\actions\LoginAction',
                'returnTo' => Url::to('foquz/index'), //\Yii::app()->user->returnUrl
                'nameIdValueReq' => '<EMAIL>'
            ],
            'metadata' => [
                'class' => 'asasmoyo\yii2saml\actions\MetadataAction'
            ],                        
            'acs' => [
                'class' => 'asasmoyo\yii2saml\actions\AcsAction',
                'successCallback' => [$this, 'callback'],
                'successUrl' => 'https://foquz.ru/foquz',
            ]
        ];
    }

    /**
     * @param array $param has 'attributes', 'nameId' , 'sessionIndex', 'nameIdNameQualifier' and 'nameIdSPNameQualifier' from response
     */
    public function callback($param) {
        $file = fopen("/var/www/opros/saml.txt", 'a+');
        fwrite($file, print_r($param, true));
        fclose($file);
        $nameId = null; $email = null;
        if (isset($param['nameId']) && $param['nameId']) {
            $nameId = strtolower($param['nameId']);
            $folders = ['foquz_adfs'];
            $email = $nameId; $attributes = [];
            if (isset($param['attributes']) && is_array($param['attributes'])) {
                foreach ($param['attributes'] as $key => $value) {
                    if (preg_match_all("@/([^/]+)$@", $key, $arr)) {
                        $attributes[strtolower($arr[1][0])] = $value;
                    }
                }
            }

            if (isset($attributes['group']) && is_array($attributes['group']) && count($attributes['group'])>0) {
                $folders=$attributes['group'];
            }

            if (isset($attributes['emailaddress']) && is_array($attributes['emailaddress']) && count($attributes['emailaddress'])>0) {
                $email = strtolower($attributes['emailaddress'][0]);
            }
            if ($nameId) {
                if (preg_match("/.*@snck.ru$/", $nameId)) {
                    throw new ForbiddenHttpException('Доступ запрещен');
                }
                
                $name = $nameId; $hasFio = false;
                if (isset($attributes['surname']) && is_array($attributes['surname']) && count($attributes['surname'])>0 && $attributes['surname'][0]) {
                    $name = trim($attributes['surname'][0]);
                    $hasFio = true;
                }
                if (isset($attributes['givenname']) && is_array($attributes['givenname']) && count($attributes['givenname'])>0 && $attributes['givenname'][0]) {
                    if ($hasFio)
                        $name .= " " . trim($attributes['givenname'][0]);
                    else 
                        $name = trim($attributes['givenname'][0]);
                }

                $company = Company::find()->where(['alias'=>'sibur.foquz.ru'])->one();
                if ($company) {
                    $user = User::find()->leftJoin('company_staff s', 'user.id=s.user_id')->where(["username"=>$nameId, 's.company_id'=>$company->id])->one();
                    if (!$user) {
                        $user = User::find()->leftJoin('company_staff s', 'user.id=s.user_id')->where(["email"=>$nameId, 's.company_id'=>$company->id])->one();
                    }

                    if (!$user) {
                        $model = new User(['scenario' => 'newUser']);
                        $model->email = $email; 
                        $model->username = $nameId;
                        $model->email_confirmed = 1;
                        $model->password = md5($email. time());
                        $model->repeat_password = $model->password;
                        $model->name = $name;
                        if ($model->save()) {
                            (new CompanyStaff([
                                'user_id' => $model->id,
                                'company_id' => $company->id
                            ]))->save(); 
                            User::assignRole($model->id, 'editor');
                            $user = $model;
                        } else {

                        }
                    }
                    if ($user) {
                        if ($user->name!==$name) {
                            $user->name = $name;
                            $user->save(false);
                        }

                        $isAdmin = false;
                        if ($user->getRoles()->where(['name'=>'foquz_admin'])->one()) $isAdmin=true;

                        if (!$isAdmin) {
                            $fc=FoquzPoll::find()->where(['company_id'=>$company->id, 'is_folder'=>1, 'is_tmp'=>0, 'is_template'=>0, 'deleted'=>0, 'folder_id'=>null])->all();
                            $foldersCompany = [];
                            foreach ($fc as $f) {
                                $foldersCompany[strtolower($f->name)] = $f->id;
                            }
                            $foldersUserNeed = [];
                            foreach ($folders as $f) {
                                if (isset($foldersCompany[strtolower($f)])) $foldersUserNeed[] = $foldersCompany[strtolower($f)];
                                else {
                                    $model = new FoquzPoll([
                                        'is_folder' => true,
                                        'folder_id' => null,
                                        'name' => $f,
                                        'is_tmp' => false,
                                        'company_id' => $company->id, 
                                    ]);
                                    if ($model->save()) {
                                      $foldersUserNeed[] = $model->id; 
                                    }
                                }
                            }
                            $fc =EditorFolder::find()->where(['user_id'=>$user->id])->all();
                            $foldersUser = [];
                            foreach ($fc as $f) $foldersUser[]=$f->folder_id;

                            foreach ($foldersUser as $f) {
                                if (!in_array($f, $foldersUserNeed)) EditorFolder::deleteAll(['folder_id'=>$f, 'user_id'=>$user->id]);
                            }
                            foreach ($foldersUserNeed as $f) {
                                if (!in_array($f, $foldersUser)) {
                                    $ef = new EditorFolder;
                                    $ef->user_id = $user->id;
                                    $ef->folder_id = $f;
                                    $ef->save();
                                }
                            }
                        }

                        //print_r(User::findOne($user->id)); exit;
                        \Yii::$app->user->login(User::findOne($user->id), 60*60*24);
                        $session = Yii::$app->session;
                        if (!$session->isActive) {
                            $session->open();
                        }
                        $url = $session->get('sso_redirect');
                        if ($url) {
                            $session->remove('sso_redirect');
                            return $this->redirect($url);
                        }

                        return $this->redirect('/foquz');                        
                    }
                }
            }
        }
    }
}
