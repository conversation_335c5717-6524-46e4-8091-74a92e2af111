<?php

namespace app\controllers\user;

use app\models\company\Company;
use app\models\LoginForm;
use app\models\User;
use app\modules\foquz\models\OpenRegistration;
use app\modules\foquz\services\api\registration\AuthLogService;
use app\modules\foquz\services\api\registration\RegistrationService;
use webvimark\modules\UserManagement\components\UserAuthEvent;
use webvimark\modules\UserManagement\controllers\AuthController as BaseAuthController;
use app\models\PasswordRecoveryForm;
use app\models\ChangeOwnPasswordForm;
use webvimark\modules\UserManagement\UserManagementModule;
use Yii;
use yii\db\Expression;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\widgets\ActiveForm;

class AuthController extends BaseAuthController
{
    public $freeAccessActions = ['login', 'logout', 'password-recovery', 'password-recovery-receive','activate-email'];
    private AuthLogService $authLogService;

    public function __construct($id, $module, $config = [])
    {
        parent::__construct($id, $module, $config);

        $this->authLogService = new AuthLogService();
    }

    /**
     * Login form
     *
     * @return string
     */
    public function actionLogin()
    {

        Yii::$app->response->headers->add('Feature-Policy', "accelerometer 'self'");

        if ( !Yii::$app->user->isGuest )
        {
            $this->checkDomainLevel(Yii::$app->getHomeUrl());
        }

        $model = new LoginForm();


        if ( Yii::$app->request->isAjax AND $model->load(Yii::$app->request->post()) )
        {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }

        $post = Yii::$app->request->post();
        if(isset($post['rememberMe'])) $post['rememberMe'] = 1;

        $isRespondent = false;
        if (!empty($post['username']) && ($user = User::findByUsername($post['username'])) && $user->isRespondent()) {
            $model->password = 1;
            $model->addError('password', 'Неверный логин или пароль');
            $isRespondent = true;

            $this->authLogService->loginFailed(login: $post['username']);
        }

        if(!$isRespondent && $model->load(['LoginForm' => $post]) && $model->login()) {

            $this->authLogService->loginSuccess(userId: $model->user->id, company: $model->user->company->alias);

            $this->checkDomainLevel(Yii::$app->getUser()->getReturnUrl());
        }

        if (!empty($post['username']) && !$model->getUser()) {
            $this->authLogService->loginFailed(login: $post['username']);
        }

        $this->layout = '@app/views/layouts/auth';
        return $this->render('@app/views/auth/login', [
            'model' => $model,
            'host' => Yii::$app->params['host'],
            'protocol' => Yii::$app->params['protocol']
        ]);

    }

    public function actionPasswordRecovery()
    {
        if ( !Yii::$app->user->isGuest )
        {
            return $this->goHome();
        }

        $model = new PasswordRecoveryForm();

        if ( Yii::$app->request->isAjax AND $model->load(Yii::$app->request->post()) )
        {
            Yii::$app->response->format = Response::FORMAT_JSON;

            // Ajax validation breaks captcha. See https://github.com/yiisoft/yii2/issues/6115
            // Thanks to TomskDiver
            $validateAttributes = $model->attributes;
            unset($validateAttributes['captcha']);

            if($model->validate(array_keys($validateAttributes))) {
                if ( $this->triggerModuleEvent(UserAuthEvent::BEFORE_PASSWORD_RECOVERY_REQUEST, ['model'=>$model]) )
                {
                    if ( $model->sendEmail(false) )
                    {
                        if ( $this->triggerModuleEvent(UserAuthEvent::AFTER_PASSWORD_RECOVERY_REQUEST, ['model'=>$model]) )
                        {
                            return ['status' => 'ok'];
                        }
                    }
                    else
                    {
                        $this->authLogService->passwordRecoveryRequestFail($model->email);
                        return ['email' => "Пользователя с указанной почтой не найдено в системе"];
                    }
                }
            } else {
                $this->authLogService->passwordRecoveryRequestFail($model->email);
                return $model->errors;
            }
        }

        if ( $model->load(Yii::$app->request->post()) AND $model->validate() )
        {
            if ( $this->triggerModuleEvent(UserAuthEvent::BEFORE_PASSWORD_RECOVERY_REQUEST, ['model'=>$model]) )
            {
                if ( $model->sendEmail(false) )
                {
                    if ( $this->triggerModuleEvent(UserAuthEvent::AFTER_PASSWORD_RECOVERY_REQUEST, ['model'=>$model]) )
                    {
                        $this->layout = '@app/views/layouts/auth';
                        return $this->renderIsAjax('@app/views/auth/passwordRecoverySuccess');
                    }
                }
                else
                {
                    $this->authLogService->passwordRecoveryRequestFail($model->email);
                    Yii::$app->session->setFlash('error', "Пользователя с указанной почтой не найдено в системе");
                }
            }
        }

        $this->layout = '@app/views/layouts/auth';
        return $this->renderIsAjax('@app/views/auth/passwordRecovery', compact('model'));
    }

    public function actionPasswordRecoveryReceive($token)
    {
        if ( !Yii::$app->user->isGuest )
        {
            return $this->goHome();
        }

        $user = User::findByConfirmationToken($token);

        if ( !$user )
        {
            throw new NotFoundHttpException(UserManagementModule::t('front', 'Token not found. It may be expired. Try reset password once more'));
        }

        $this->layout = '@app/views/layouts/auth';

        $model = new ChangeOwnPasswordForm([
            'scenario'=>'restoreViaEmail',
            'user'=>$user,
        ]);

        if ($model->load(Yii::$app->request->post()) && $model->validate())
        {
            if ( $this->triggerModuleEvent(UserAuthEvent::BEFORE_PASSWORD_RECOVERY_COMPLETE, ['model'=>$model]) )
            {
                if (!$model->changePassword(false)) {
                    Yii::$app->response->statusCode = 400;
                    return $this->asJson(['errors' => ['password' => $model->getFirstError('password')]]);
                }

                if ( $this->triggerModuleEvent(UserAuthEvent::AFTER_PASSWORD_RECOVERY_COMPLETE, ['model'=>$model]) )
                {
                    $this->authLogService->passwordRecoverySuccess($user);

                    return $this->renderIsAjax('@app/views/auth/changeOwnPasswordSuccess');
                }
            }
        }
        if ($model->getFirstError('password')) {
            $this->authLogService->passwordRecoveryFail($user);
            Yii::$app->response->statusCode = 400;
            return $this->asJson(['errors' => ['password' => $model->getFirstError('password')]]);
        }
        if ($model->getFirstError('repeat_password')) {
            $this->authLogService->passwordRecoveryFail($user);
            Yii::$app->response->statusCode = 400;
            return $this->asJson(['errors' => ['repeat_password' => $model->getFirstError('repeat_password')]]);
        }
        return $this->renderIsAjax('@app/views/auth/changeOwnPassword', compact('model'));
    }

    /**
     * Подтверждение eMail по ссылке из письма
     * @param string $token
     * @return string
     * @throws NotFoundHttpException
     * @throws \yii\db\Exception
     */
    public function actionActivateEmail(string $token): string
    {
        $openRegistration = OpenRegistration::find()
            ->where(['code' => $token])
            ->andWhere(['>', 'available_to', new Expression('NOW()')])
            ->one();

        if (!$openRegistration) {
            throw new NotFoundHttpException(UserManagementModule::t('front', 'Token not found. Try again with valid token.'));
        }
        $this->layout = '@app/views/layouts/auth';
        $service = new RegistrationService();

        /** @var OpenRegistration $openRegistration */
        if (!$user = $service->confirmEmail($openRegistration)) {
            throw new NotFoundHttpException(UserManagementModule::t('front', 'Something went wrong while email confirming. Try again.'));
        }
        $this->authLogService->emailConfirmationByTokenSuccess($user);
        return $this->render('@app/views/auth/emailConfirmationSuccess');
    }

    public function checkDomainLevel($url): Response
    {
        $parsedURL = parse_url($url);
        $requestUri = $parsedURL['path'] ?? '/';
        if (isset($parsedURL['query'])) {
            $requestUri .= '?' . $parsedURL['query'];
        }
        if (isset($parsedURL['fragment'])) {
            $requestUri .= '#' . $parsedURL['fragment'];
        }
        $alias = Yii::$app->user->identity->company->alias ?? $parsedURL['host'];
        if(Yii::$app->user->identity->isWikiEditor()) {
            return $this->redirect(Yii::$app->params['protocol'].'://'.Yii::$app->params['host'].'/foquz/wiki');
        }
        if(Yii::$app->user->identity->isExecutor()) {
            return $this->redirect(($_SERVER["REQUEST_SCHEME"] ?? Yii::$app->params['protocol']) . '://' . $alias . '/foquz/answers');
        }
        return $this->redirect(($_SERVER["REQUEST_SCHEME"] ?? Yii::$app->params['protocol']) . '://' . $alias . $requestUri);
    }

}
