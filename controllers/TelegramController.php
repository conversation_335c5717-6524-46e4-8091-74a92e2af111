<?php


namespace app\controllers;


use TelegramBot\Api\Client;
use TelegramBot\Api\Exception;
use yii\web\Controller;
use app\models\company\Company;
use yii\web\ForbiddenHttpException;
use yii\httpclient\Client as Client2;
use  app\models\TelegramChat;
use app\modules\foquz\models\channels\GlobalTelegramSettings;
use yii\web\NotFoundHttpException;


/**
 * Class TelegramController
 * @package app\controllers
 */
class TelegramController extends Controller
{

    public $enableCsrfValidation = false;
    private $key = '1234567890';
    public $freeAccess = true;

    /**
     * @todo никто не использует эту штуку, она работала
     * может пригодиться
     */
    public function actionIndex()
    {
        //работал, но никто не использует
        throw new NotFoundHttpException();
        $host = $_SERVER['HTTP_HOST'];
        $token = null;
        $company = Company::find()->where(['alias'=>$host])->one();
        if ($company) {
            $gs = GlobalTelegramSettings::find()->where(['company_id'=>$company->id])->andWhere("token is not null")->orderBy("id desc")->one();
            if ($gs) $token = $gs->token;
        }
        if (!$token) return '';
        $logFile =\Yii::getAlias('@app/runtime/telegram-log.txt');
        $post = \Yii::$app->request->getRawBody();
        file_put_contents($logFile,print_r($post, true),FILE_APPEND);
        file_put_contents($logFile,print_r('dgsdg', true),FILE_APPEND);
        //print($token);
        if ($post) {
            $message = json_decode($post, true);
            if (is_array($message) && isset($message["message"]) && is_array($message["message"])) {
                $message = $message["message"];
                file_put_contents($logFile,print_r($message, true),FILE_APPEND);

                if (isset($message["chat"]) && is_array($message["chat"])) {
                    $chat = $message["chat"];
                    $chatId = @$chat["id"];
                    if ($chat["type"]=="private" && $chatId) {
                        if (isset($message["contact"]) && is_array($message["contact"])) {
                            $contact = $message["contact"];
                            $phone = @$contact["phone_number"];
                            if ($phone) {
                                $c = TelegramChat::findOne($phone);
                                if (!$c) {
                                    $c = new TelegramChat;
                                    $c->phone = $phone;
                                } 
                                $c->chat_id = $chatId;
                                $c->last_name = @$contact["last_name"] ? $contact["last_name"] : null;
                                $c->first_name = @$contact["first_name"] ? $contact["first_name"] : null;
                                $c->save();
                            }

                        } else {
                            try {
                                $c = TelegramChat::find()->where(["chat_id"=>$chatId])->one();
                                if ($c && $c->phone) {
                                } else {
                                    $client2 = new Client2();
                                    $response = $client2->get('https://api.telegram.org/bot'.$token.'/sendMessage',
                                        [
                                            "chat_id" => $chatId,
                                            "text" =>  "Для того, чтобы мы смогли присылать вам информацию по заказам, отправьте, пожалуйста, свои контакты",
                                            "reply_markup" => json_encode(["one_time_keyboard"=>true, "keyboard"=>[[["text"=>"Отправить контакты", "request_contact"=>true]]]])
                                        ]
                                    )->send();
                                }

                            } catch (Exception $e) {
                                file_put_contents($logFile,print_r($e, true),FILE_APPEND);;
                            }
                        }
                        
                    }
                }

            }
        }

    }
}
