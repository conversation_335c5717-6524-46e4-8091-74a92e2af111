<?php

namespace app\controllers;

use app\components\AmoCrmComponent;
use app\components\SendsayService;
use app\components\UtmService;
use app\models\ApiController;
use app\models\ClientEmail;
use app\models\Email;
use app\models\KeyClent;
use app\models\Lead;
use app\models\Poll;
use app\models\PromoStat;
use app\modules\foquz\models\Article;
use app\modules\foquz\models\ArticleAuthor;
use app\modules\foquz\models\CompanyAffiliateCode;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollDesign;
use app\modules\foquz\models\FoquzPollShortLink;
use app\modules\foquz\models\Review;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use yii\helpers\Url;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;
   

class SiteController extends Controller
{
    /**
     * @inheritdoc
     */

    public $solutionsCode = null;
    public $solutionDescription = null;
    public $topicTitle = null;
    public $topicDescription = null;
    public $caseId = null;

    public function beforeAction($action)
    {
        $utmService = new UtmService();
        $utmService->setUtm();
        if ($action->id == 'error') {
            $this->layout = 'error';
        }

        if ($action->id == 'send-lead') {
            $this->enableCsrfValidation = false;
        }

        return parent::beforeAction($action);
    }

    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'only' => [
                    'logout', 'statistics', 'settings', 'saveinterview', 'send-lead', 'styleguide',
                    'solutions', 'solutions-data', 'shortlink', 'cases', 'case'
                ],
                'rules' => [
                    [
                        'actions' => ['logout', 'Send_sms', 'statistics', 'settings', 'saveinterview'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'actions' => [
                            'send-lead', 'styleguide', 'solutions', 'solutions-data', 'shortlink', 'cases', 'case',
                            'questionnaires-poll', 'questions-types', 'loyalty-index', 'feedback', 'consumer-polls',
                            'marketing', 'brand', 'customer-experience', 'similar-actions',
                            'life-hack',
                            'tochki-kontakta', 'kak-pravilno-sobirat-obratnuyu-svyaz', 'stranica-blagodarnosti', 'kolichestvennye-issledovaniya', 'sostavlenie-ankety-dlya-marketingovogo-issledovaniya', 'onlajn-oprosy', 'foquz-pro', 'zakrytye-voprosy-v-ankete', 'onlajn-oprosy-dostoinstva-i-nedostatki', 'opros-udovletvorennosti-sotrudnikov',
                            'anketa-psihologa-dlya-klienta', 'anketa-kosmetologa-dlya-klienta', 'anketa-klienta-massazhnogo-kabineta-obrazec',
                            'anketa-anonimnaya-primer', 'chto-takoe-anketa',
                            'anketa-postoyannogo-pokupatelya-obrazec', 'anketa-kachestva-obsluzhivaniya-klientov', 'anketa-dlya-oprosa-potrebitelej-tovarov-i-uslug', 'chek-list-tajnogo-pokupatelya-obrazec',
                            'anketa-novogo-klienta',
                            'anketa-kompanii',
                            'anketa-turagentstva-dlya-klienta',
                            'anketa-posetitelya-vystavki-ili-stenda',
                            'anketa-dlya-restorana-oprosnik',
                            'anketa-dlya-diskontnyh-kart', 'anketa-dlya-izucheniya-sprosa', 'anketa-klienta-fizicheskogo-lica', 'opros-otkuda-vy-o-nas-uznali', 'kak-otvechat-na-voprosy-v-ankete-pri-prieme-na-rabotu',
                            'obyasnenie-modeli-kano-analiz-i-primery'

                        ],
                        'allow' => true,
                        'roles' => ['?', '@']
                    ]
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'logout' => ['post'],
                ],
            ],
            [
                'class' => 'yii\filters\HttpCache',
                'lastModified' => function ($action, $params) {
                    return strtotime(date("Y-m-d"));
                },
            ],

        ];
    }


    /**
     * @inheritdoc
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
                // 'view' => '@app/views/site/error.php'
            ],
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    public function actionIndex($r = null)
    {
        $this->layout = 'landing';
        if (!isset($_SERVER['HTTP_HOST'])) {
            throw new NotFoundHttpException('Страница не найдена');
        }
        $host = $_SERVER['HTTP_HOST'];
        $level = explode('.', $host);
        if ($r) {
            $affiliateCode = CompanyAffiliateCode::findOne(['url' => \Yii::$app->params['protocol'] . '://' . \Yii::$app->params['host'] . '/' . \Yii::$app->request->pathInfo]);
            if ($affiliateCode) {
                setcookie('affiliate_code_id', $affiliateCode->id, time() + (24 * 60 * 60 * 7), '/');
            }
        }
        if (count($level) > 2) {
            return $this->redirect(Url::to('/foquz'));
        }
        $reviews = Review::find()->select([
            'id', 'name', 'author', 'logo', 'task', 'answers', 'rating', 'case_url', 'site_url'
        ])->orderBy(['position' => SORT_ASC])->all();

       if (preg_match("@^/site($|/$|/\\?)@", $_SERVER["REQUEST_URI"])) {
           throw new NotFoundHttpException('Страница не найдена');
       }
        return $this->render('landing', ['page' => 'main', 'reviews' => $reviews]);
    }

    public function actionTariffs()
    {
        if (isset($_SERVER["REQUEST_URI"]) && $_SERVER["REQUEST_URI"] == "/site/tariffs/") {
            return $this->redirect("/site/tariffs", 301);
        }

        $this->layout = 'landing';
        return $this->render('tariffs');
    }

    public function actionSolutions($id = null, $code = null)
    {
        if ($id && in_array($id, [5080, 5081, 5083, 5084, 5085, 5115, 5116, 5117])) {
            return $this->redirect("/obrazcy-anket", 301);
        }

        $this->layout = 'landing';
        $host = $_SERVER['HTTP_HOST'];
        $level = explode('.', $host);
        if (count($level) > 2) {
            return $this->redirect(Url::to('/foquz'));
        }

        $foldersCodes = FoquzPoll::find()
            ->select('code')
            ->where(['is_folder' => 1])
            ->andWhere(['not', ['code' => null]])
            ->column();

        $this->solutionsCode = $code;

        if (!$id && (!$code || in_array($code, $foldersCodes, true))) {
            if ($_SERVER['REQUEST_URI'] == "/site/solutions") {
                return $this->redirect("/obrazcy-anket", 301);
            }

            return $this->render('solutions');
        }

        $template = FoquzPoll::find()
            ->where([
                'company_id' => \Yii::$app->params['template_company_id'],
                'deleted' => 0,
                'status' => FoquzPoll::STATUS_NEW
            ]);

        if ($id !== null) {

            $template = $template->andWhere(['id' => $id]);
        } else {
            $template = $template->andWhere(['code' => $code]);
        }

        $template = $template->one();
        if ($template === null) {
            throw new NotFoundHttpException();
        }
        if (!$code && $template->code) {
            return $this->redirect("/obrazcy-anket/" . $template->code, 301);
            throw new NotFoundHttpException();
        }


        $this->solutionDescription = $template->goal_text;
            $this->topicTitle = $template->title; 
            $this->topicDescription= $template->description;

        return $this->render('solution', [
            'id' => $template->id,
            'background_image' => $template->design->background_image ?? FoquzPollDesign::DEFAULT_BG_IMAGE,
            'name' => $template->name,
            'folderName' => $template->folder_id && $template->parentFolder->folder_id ? $template->parentFolder->name : '',
            'folderCode' => $template->folder_id && $template->parentFolder->folder_id ? $template->parentFolder->code : '',
            'description' => $template->goal_text,
            'preview' => '/foquz/default/poll?authKey=dummyDesign&questionId=' . ($template->foquzQuestions[0]->id ?? ''),

        ]);
    }

    public function actionPollExamples($code)
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $category = FoquzPoll::findOne(['is_folder' => 1, 'code' => $code, 'company_id' => \Yii::$app->params['template_company_id']]);
        $result = [];
        if ($category) {
            $polls = FoquzPoll::find()->where([
                'folder_id' => $category->id,
                'is_tmp' => 0,
                'company_id' => \Yii::$app->params['template_company_id'],
                'is_folder' => 0,
                'deleted' => 0,
                'status' => FoquzPoll::STATUS_NEW
            ])->limit(5)->all();
            foreach ($polls as $template) {
                $result[] = [
                    'id' => $template->id,
                    'background_image' => $template->design->background_image ?? FoquzPollDesign::DEFAULT_BG_IMAGE,
                    'name' => $template->name,
                    'folderName' => $template->folder_id ? $template->parentFolder->name : '',
                    'description' => $template->goal_text,
                    'preview' => '/foquz/default/poll?authKey=dummyDesign&questionId=' . ($template->foquzQuestions[0]->id ?? ''),
                    'code' => $template->code,
                    'folderCode' => $template->folder_id ? $template->parentFolder->code : null,
                    'folder_id' => $template->folder_id, 

                ];
            }
        }

        return $result;
    }

    public function actionSolutionsData()
    {
        $all = [];
        $folders = [];
        $folderIds = [];

        if (\Yii::$app->params['template_company_id']) {
            $mailFolderName = null;
            $innerFolders = FoquzPoll::find()
                ->where([
                    'is_folder' => 1,
                    'company_id' => \Yii::$app->params['template_company_id']
                ]);
            if (\Yii::$app->params['template_company_folder_id'] != '') {
                $innerFolders->andWhere(['folder_id' => \Yii::$app->params['template_company_folder_id']]);
                $folderIds[] = \Yii::$app->params['template_company_folder_id'];
                $mailFolderName = FoquzPoll::find()->select('name')->where(['id' => \Yii::$app->params['template_company_folder_id']])->scalar();
            } else {
                $innerFolders->andWhere(['folder_id' => null]);
            }
            $innerFolders = $innerFolders->all();
            foreach ($innerFolders as $iFolder) {
                $folderIds[] = $iFolder->id;
            }
            $allTemplates = FoquzPoll::find()
                ->where([
                    'is_tmp' => 0,
                    'company_id' => \Yii::$app->params['template_company_id'],
                    'is_folder' => 0,
                    'deleted' => 0,
                    'status' => FoquzPoll::STATUS_NEW
                ])
                ->distinct()
                ->andWhere(['in', 'folder_id', $folderIds])
                ->having('(SELECT COUNT(foquz_question.id) FROM foquz_question WHERE foquz_question.poll_id = foquz_poll.id) > 0');
            foreach ($allTemplates->all() as $template) {
                $all[] = [
                    'id' => $template->id,
                    'background_image' => $template->design->background_image ?? FoquzPollDesign::DEFAULT_BG_IMAGE,
                    'name' => $template->name,
                    'folderName' => $template->folder_id ? $template->parentFolder->name : '',
                    'description' => $template->goal_text,
                    'preview' => '/foquz/default/poll?authKey=dummyDesign&questionId=' . ($template->foquzQuestions[0]->id ?? ''),
                    'code' => $template->code,
                    'folderCode' => $template->folder_id ? $template->parentFolder->code : null,
                    'folder_id' => $template->folder_id
                ];
            }

            $folders = [];

            foreach ($innerFolders as $folder) {
                if ($folder->name !== $mailFolderName) {
                    $fold = [
                        'id' => $folder->id,
                        'name' => $folder->name,
                        'code' => $folder->code,
                        'description' => $folder->description,
                        'items' => []
                    ];
                    foreach ($all as $template) {
                        if ($template['folder_id'] && $template['folder_id'] === $folder->id) {
                            $fold['items'][] = $template;
                        }
                    }
                    if (!empty($fold['items'])) {
                        $folders[] = $fold;
                    }
                }
            }
        }
        \Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'all' => $all,
            'folders' => $folders
        ];
    }


    public function actionUnsubscribe($key)
    {
        $this->layout = 'interview2';
        $this->enableCsrfValidation = false;
        //    public function actionLogoit


        if (null === ($poll = Poll::findOne(['key' => $key]))) {
            throw new NotFoundHttpException('Poll is not found');
        }

        if (null === ($clientEmail = ClientEmail::findOne(['id' => $poll->email_id]))) {
            throw new NotFoundHttpException('Email is not found');
        }

        $clientEmail->is_subscribe = false;
        $clientEmail->save();

        return $this->render('unsubscribe', [
            'key' => $key,
        ]);
    }
    public function actionSubscribe($key)
    {
        $this->layout = 'interview2';
        $this->enableCsrfValidation = false;

        if (null === ($poll = Poll::findOne(['key' => $key]))) {
            throw new NotFoundHttpException('Poll is not found');
        }

        if (null === ($clientEmail = ClientEmail::findOne(['id' => $poll->email_id]))) {
            throw new NotFoundHttpException('Email is not found');
        }

        $clientEmail->is_subscribe = true;
        $clientEmail->save();

        return $this->render('subscribe', [
            'key' => $key,
        ]);
    }

    public function actionSendLead()
    {
        $post = \Yii::$app->request->post();
        if (!$post) throw new NotFoundHttpException('');
        $model = new Lead();
        $model->name = $post['name'];
        //$model->phone = $post['phone'] ? $post['phone'] : $post['email'];
        $tpl = $post['phone'] ? 'lead-send' : 'lead-send2';
        //$model->email = $post['email'];
        $model->email = @$post['email'];
        $model->phone = @$post['phone'];
        if (!$model->phone) {
            $model->phone = $model->email;
        }
        $model->rate = $post['rate'] ?? null;
        if ($model->save()) {
            $s = new SendsayService(\Yii::$app->params['sendsay']['login'], \Yii::$app->params['sendsay']['password']);
            foreach (\Yii::$app->params['lead_to_emails'] as $email) {
                $s->sendEmail(
                    \Yii::$app->params['main_sender_email'],
                    "Опросы Foquz.ru",
                    $email,
                    $post['phone'] ? 'Foquz. Новая заявка' : "Foquz. Запрос презентации",
                    \Yii::$app->mailer->render($tpl, ['lead' => $model])
                );
            }

            /*
            \Yii::$app->mailer->compose($tpl, ['lead' => $model])
                ->setFrom(['<EMAIL>' => "Опросы Foquz.ru"])
                ->setTo(\Yii::$app->params['lead_to_emails'][0])
                ->setReplyTo(['<EMAIL>' => "Опросы Foquz.ru"])
                ->setSubject($post['phone'] ? 'Foquz. Новая заявка' : "Foquz. Запрос презентации")
                ->send();*/

            if (isset($post['email']) && $post['email'] != '') {
                $r = $s->sendEmail(
                    \Yii::$app->params['main_sender_email'],
                    "Опросы Foquz.ru",
                    $post['email'],
                    'Презентация Foquz',
                    \Yii::$app->mailer->render("load-presentation")
                );
                /*
                \Yii::$app->mailer->compose('load-presentation')
                    ->setFrom(['<EMAIL>' => "Опросы Foquz.ru"])
                    ->setTo($post['email'])
                    ->setReplyTo(['<EMAIL>' => "Опросы Foquz.ru"])
                    ->setSubject('Презентация Foquz')
                    ->send();
                */
            }

            $typeLead = null;
            $taskName = null;
            if (isset($post['email']) && $post['email'] != '') {
                $query = $post['email'];
                $taskName = 'ПРЕЗЕНТАЦИЯ';
                $typeLead = 'Запрос презентации';
            } elseif ($model->rate == null) {
                $query = FoquzContact::preformatPhone($post['phone']);
                $taskName = 'ЗВОНОК';
                $typeLead = 'Обратный звонок';
            } else {
                $query = FoquzContact::preformatPhone($post['phone']);
                $taskName = 'ЗАКАЗ ТАРИФА ';
                $typeLead = 'Смена тарифа';
                if ($model->rate != null) {
                    $taskName .= $model->rate === 'business' ? 'Бизнес' : 'Корпоративный';
                }
            }

            if ($taskName && !empty(\Yii::$app->params['amo_client_id'])) {
                $taskName .= " " . date("d.m.Y H:i");
                $amo = new AmoCrmComponent();
                $contact = $amo->findContact($query);
                $contactId = $contact ? $contact['id'] : $amo->createContact($post['name'], @$post['email'], FoquzContact::preformatPhone(@$post['phone']), null);
                print_r($contactId);
                $leadID = $amo->createLeadNew($taskName, 0, null, $contactId, UtmService::getUtmTags(), $typeLead);
                print_r($leadID);
            }
        }
        return true;
    }

    public function actionStyleguide()
    {
        $this->layout = false;
        return $this->render('style-guide');
    }

    public static function mime_header_encode($str, $data_charset, $send_charset)
    {
        if ($data_charset != $send_charset) {
            $str = iconv($data_charset, $send_charset, $str);
        }
        return '=?' . $send_charset . '?B?' . base64_encode($str) . '?=';
    }

    public function actionShortlink($id)
    {
        $shortLink = FoquzPollShortLink::find()->where(['code' => $id])->one();
        if (!$shortLink) {
            throw new NotFoundHttpException();
        }
        return $this->redirect($shortLink->link);
    }

    public function actionCases()
    {
        if (isset($_SERVER["REQUEST_URI"]) && $_SERVER["REQUEST_URI"] == "/site/cases/") {
            return $this->redirect("/site/cases", 301);
        }
        $this->layout = 'cases';
        return $this->render('cases/list');
    }

    public function actionCase($id)
    {
        $this->layout = 'case';
        $this->caseId = $id;
        if (!file_exists(\Yii::getAlias('@cases/' . $id . '.php'))) {
            throw new NotFoundHttpException();
        }
        return $this->render('cases/' . $id);
    }

    public function actionArticle($name)
    {
        $article = Article::find()->with('author')->where(['name' => $name])->one();
        if (!$article) {
            throw new NotFoundHttpException('Страница не найдена');
        }
        /** @var Article $article */
        Article::updateAllCounters(['count_views' => 1], ['id' => $article->id]);
        $article->count_views++;
        $this->layout = 'landing';
        $this->topicTitle = $article->seo_title;
        $this->topicDescription = $article->seo_description;
        return $this->render('articles/' . $name, ['article' => $article]);
    }

    public function actionAuthor($name)
    {
        $author = ArticleAuthor::find()
            ->with(['articles' => function ($q) {
                $q->orderBy(['date' => SORT_DESC]);
            }])
            ->where(['url' => $name])
            ->one();
        if (!$author) {
            throw new NotFoundHttpException('Страница не найдена');
        }
        /** @var ArticleAuthor $author */
        $this->layout = 'landing';
        $this->topicTitle = $author->seo_title ?: $author->name;
        $this->topicDescription = $author->seo_description;
        return $this->render('author', ['author' => $author]);
    }

    public function actionStat()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;

        /* $npsStat = FoquzPollAnswerItem::find()
            ->leftJoin("foquz_question q", 'q.id=foquz_poll_answer_item.foquz_question_id')
            ->where(["q.main_question_type"=>FoquzQuestion::TYPE_NPS_RATING])
            ->andWhere("(foquz_poll_answer_item.created_at>DATE_ADD(now(), INTERVAL -1 DAY)) and rating>0 and rating<11")
            ->select([
                "all" => "count(*)",
                "promoters" => "SUM(IF(rating>8, 1, 0))",
                "critics" => "SUM(IF(rating<7, 1, 0))",
            ])
            ->asArray()->one();

        $starStat = FoquzPollAnswerItem::find()
            ->leftJoin("foquz_question q", 'q.id=foquz_poll_answer_item.foquz_question_id')
            ->leftJoin("foquz_question_star_rating_options o", 'o.foquz_question_id=q.id')
            ->where("(foquz_poll_answer_item.created_at>DATE_ADD(now(), INTERVAL -1 DAY)) AND rating>0")
            ->where(["q.main_question_type"=>FoquzQuestion::TYPE_STAR_RATING])
            ->select([
                "r" => "avg((rating-1)/(count-1)*4+1)",
            ])
            ->asArray()->one();*/

        $stat = PromoStat::stat();

        $result = [
            'emails' => round($stat->emails * 4.99),
            'sms' => round($stat->sms * 4.93),
            'polls' => round($stat->polls * 4.97),
            'answers' => round($stat->answers * 4.92),
            'rating' => 4.8, //round($starStat["r"], 1),
            'nps' => [
                'promoters' => 91.4, //round($npsStat["promoters"]/$npsStat["all"]*100, 1),
                'critics' => 4.6, //round($npsStat["critics"]/$npsStat["all"]*100, 1)
            ]
        ];
        $result["nps"]["neutrals"] = 100 - $result["nps"]["promoters"] - $result["nps"]["critics"];
        $result["nps"]["result"] = $result["nps"]["promoters"] - $result["nps"]["critics"];

        return $result;
    }

    public function actionSimilarArticles($article = null, $count = 3): Response
    {
        //\Yii::$app->response->format = Response::FORMAT_JSON;
        $id = preg_replace('/\D/', '', $article);
        $result = [];
        /** @var Article[] $pages */
        $pages = Article::find()->andFilterWhere(['<>', 'id', $id])->orderBy('RAND()')->limit($count)->all();

        foreach ($pages as $page) {
            $result[] = [
                'url' => '/' . $page->name,
                'title' => $page->title,
                'img' => $page->img_url,
            ];
        }

        return $this->asJson($result);
    }

    public static function getArticlesForFooter(): array
    {
        $articles = [];
        /** @var Article $article */
        foreach (Article::find()->all() as $article) {
            $articles[] = [
                'link' => '/' . $article->name,
                'id' => $article->name,
                'title' => $article->title,
                'text' => $article->title,
                'visible' => $article->footer_visible,
            ];
        }
        return $articles;
    }

    public function actionTestEmailRegistrattion()
    {
        echo \Yii::$app->mailer->render('new-user-registered', [
            'token' => 'code'
        ]);
        exit;
    }
}
