<?php

namespace app\controllers;

use app\models\google\GoogleClientService;
use app\models\google\GoogleSheetsParams;
use app\models\google\sheets\Answers2;
use yii\web\Controller;
use yii\helpers\ArrayHelper;
use yii\web\BadRequestHttpException;
use Yii;
use yii\web\NotFoundHttpException;

/**
 * @deprecated Функционал не опубликован - так как на средних выгрузках работает очень плохо
 */
class GoogleSheetsController extends Controller
{
    public function beforeAction($action)
    {
        throw new NotFoundHttpException("Функционал отключен");
        $this->enableCsrfValidation = false;
        return parent::beforeAction($action);
    }

    public function actionCreate($table)
    {
        throw new NotFoundHttpException("Функционал отключен");

        if (!in_array($table, GoogleSheetsParams::getTablesTypes(), true)) {
            throw new BadRequestHttpException('Неверный тип таблицы');
        }

        $user_id = Yii::$app->request->get('user_id') ?? Yii::$app->user->id;
        $data = Yii::$app->getRequest()->getBodyParams();
        $queryParams = $this->request->queryParams;

        $params = GoogleSheetsParams::create(
            $user_id,
            $table,
            ArrayHelper::merge($data, $queryParams)
        );
        $client = (new GoogleClientService($params->id))->getClient();
//return new Answers2($client, $params);
        return $this->redirect($client->createAuthUrl());
    }
}