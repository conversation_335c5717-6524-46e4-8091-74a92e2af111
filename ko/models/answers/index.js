import { get as _get } from "lodash";
import { openModal } from "./open-modal";
import { handleFolders } from "./handle-folders";
import { getSourceType } from "Legacy/data/order-source-types";

import { FiltersModel } from "./filters";

import { SortModel } from "./sort";
import { SearchModel } from "./search";
import { ReviewsModel } from "./reviews";
import { TableModel } from "./table";

import { Observer } from "./observer";

import "Legacy/components/sendings-history";

import "./components";

import "Components/sending-list";

import "Components/input/select/moderators-select";

import "Components/tags-filter";
import "Components/answer-group-actions";

import "Components/tag-input";

import { DialogsModule } from "Utils/dialogs-module";
import { TagsDirectory } from "Utils/directory/tags";
import { FilialsDataCollection } from "Models/data-collection/filials";
import { ClientFieldsCollection } from "Models/data-collection/client-fields";

// id текущего пользователя
window.currentUserId = USER_ID;

function getCellWidth(text) {
  return Math.max(text.length * 7.2, 150);
}

function updateUrl(params) {
  console.log(4752, params)
  let sp = deparam(location.search.slice(1));
  let filters = { ...params.filters };
  if (!("from" in filters)) {
    filters.from = "";
    filters.to = "";
  }
  if (!("order" in params)) {
    params.order = "";
  }

  if (!("sortAdditional" in params)) {
    params.sortAdditional = "";
  }

  let urlParams = {
    ...sp,
    ...params,
  };

  delete urlParams["access-token"];
  delete urlParams["delete"];
  
  if (!("withComment" in params)) {
    delete urlParams["withComment"];
  }

  let paramsString = $.param(urlParams);
  utils.url.saveState(paramsString);
}

import "Dialogs/review-sidesheet";
import "Dialogs/download-archive-dialog";

import { FiltersController } from "./filters/filtersController";
import deparam from "deparam";
import { request } from "Utils/api/request";
import { FILE_QUESTION } from "Data/question-types";

const EXPORT_CSV_SUCCESS_STATUS = 4;

const loadReviewSidesheet = (function () {
  return function () {
    return Promise.resolve();
  };
})();

export class AnswersModel {
  constructor(config) {
    this.preparing = ko.observable(true);
    this.noAnswers = ko.observable(false);
    this.initializing = ko.observable(true);
    this.archivePreparing = ko.observable(false);
    this.archiveData = ko.observable(null);
    this.exportCSVProcessing = ko.observable(false);
    this.exportCSVProgress = ko.observable(1);

    config = config || {};
    this.executorMode = config.executorMode;

    TableModel.preload(this.columnsKey);

    DialogsModule(this);
    window.ANSWERS = this;
    this.screenWidth = ko.observable($(window).width());
    $(window).resize(() => {
      this.screenWidth($(window).width());
    });
    this.isMobile = ko.pureComputed(() => {
      return this.screenWidth() <= 767;
    });
    const searchParams = new URLSearchParams(location.search);
    this.searchParams = searchParams;
    this.modalOpens = ko.observableArray([]);
    this.openModal = openModal(this.modalOpens);
    this.dialogs = ko.observable(null);
    this.detailsDialogs = ko.observable(null);
    this.detailsModalOpens = ko.observableArray([]);

    this.observers = {};
    this.viewTopIntersecting = ko.observable(false);
    this.activePollFixed = ko.observable(false);
    this.currentStats = ko.observable(null);
    this.statsInited = ko.observable(false);
    this.answersTotalCount = ko.observable(0);
    this.answersFilteredCount = ko.observable(0);
    this.hasSizeAnswers = ko.observable(false);

    this.refs = {
      tableContainer: ko.observable(null),
    };

    this.directories = {
      filials: new FilialsDataCollection({
        undefinedFilialOption: !this.single || !this.isAuto || !this.hasOrder,
        selectableCategories: true,
      }),
      users: new Directory("user"),
      // polls: new Directory("poll/list"),
      folders: new Directory("poll/folders", handleFolders),
      processingStatuses: new Directory(
        "answer-processing/statuses?" +
          $.param({
            filters: {
              ...(this.single ? { polls: [this.pollId] } : {}),
              ...(EXECUTOR_MODE ? { executors: [CURRENT_USER.id] } : {}),
            },
            onlyNames: 1,
          })
      ),
      orderTypes: new Directory("order-type"),
      executors: new Directory("answers/executors"),
      tags: new TagsDirectory(),
    };

    /** Проверка статусов обработки - есть ли вообще анкеты */
    let params = {};
    if (this.single) {
      params.filters = {
        polls: [this.pollId],
      };
    }
    const processingStatusesLoaded = this.updateStats(params);

    /** Получение настроек обработки заявок */
    this.processingEnabled = ko.observable(false);
    this.hasProcessingOptions = ko.observable(false);

    const processingSettingsLoaded = (() => {
      if (this.executorMode) {
        this.processingEnabled(true);
        this.hasProcessingOptions(false);
        return Promise.resolve();
      } else {
        return new Promise((res, rej) => {
          $.ajax({
            url: "/foquz/settings/request-processing-settings",
            success: (response) => {
              if (response.request_processing_enabled) {
                this.processingEnabled(true);

                if (
                  response.rate_answer ||
                  response.answer_with_complain ||
                  response.rate_answer_with_comment
                ) {
                  this.hasProcessingOptions(true);
                }
              }
              res();
            },
            error: (response) => {
              console.error(response.responseJSON);
              rej();
            },
          });
        });
      }
    })();

    this.clientSystemFields = [
      {
        id: "filials",
        fullName: "clientFilials",
        text: _t("Филиал контакта"),
        width: 200,
        placeholder: "Все",
      },
      {
        id: "gender",
        fullName: "clientGender",
        text: _t("Пол"),
        width: 70,
        placeholder: "Все",
      },
      {
        id: "birthday",
        fullName: "clientBirthday",
        text: _t("День рождения"),
        placeholder: "00.00.0000",
      },
      {
        id: "tags",
        fullName: "clientTags",
        text: _t("Теги контакта"),
        width: 200,
        placeholder: "Все",
      },
      {
        id: "ltv",
        fullName: "clientLtv",
        text: _t("LTV"),
        placeholder: "0",
      },
      {
        id: "lastOrderDate",
        fullName: "clientLastOrderDate",
        text: _t("Дата послед. заказа"),
        placeholder: "00.00.0000",
      },
      {
        id: "addedAt",
        fullName: "clientAddedAt",
        text: _t("Добавлен"),
        placeholder: "00.00.0000",
      },
      {
        id: "updatedAt",
        fullName: "clientUpdatedAt",
        text: _t("Обновлен"),
        placeholder: "00.00.0000",
      },
      {
        id: "customerId",
        fullName: "clientCustomerId",
        text: _t("Внешний ID"),
        placeholder: "0",
      },
      {
        id: "personalData",
        fullName: "clientPersonalData",
        text: _t("Персональные данные"),
        short: _t("ПД"),
        width: 40,
      },
    ].map((f) => {
      return {
        ...f,
        system: true,
        width: f.width || getCellWidth(f.text),
      };
    });
    this.clientFields = ko.observableArray([]);
    const clientFieldsLoaded = new Promise((res, rej) => {
      let collection = ClientFieldsCollection.getInstance();
      collection.on("load", (fields) => {
        fields = fields.map((f) => {
          let fullName = "client" + f.id;

          let width = getCellWidth(f.text);
          return {
            ...f,
            fullName,
            width,
          };
        });
        this.clientFields(fields);
        res();
      });
      collection.on("error", () => {
        this.clientFields([]);
        res();
      });
      collection.load();
    });

    const prepare = new Promise((res, rej) => {
      processingStatusesLoaded.then(() => {
        processingSettingsLoaded.then(() => {
          clientFieldsLoaded.then(() => {
            res();
          });
        });
      });
    });

    prepare
      .then(() => this.initFilters())
      .then(() => {
        this.afterPrepare();
        this.subscribeAfterPrepare();
        this.preparing(false);
        const url = new URL(window.location.href)
        const reviewId = url.searchParams.get('reviewId')
        if (reviewId) {
          this.openDetailsModal({id: reviewId})
        }
      })
      .catch((msg) => {
        console.error("PREPARE ERROR", msg);

        this.preparing(false);
      });

      
  }

  initFilters() {
    this.filtersController = new FiltersController({
      key: this.filtersKey,
      pollId: this.pollId,
      single: this.single,
      withPoints: this.withPoints,
      hasOrder: this.hasOrder,
      isAuto: this.isAuto,
      executorMode: this.executorMode,
      hasProcessingOptions: this.hasProcessingOptions(),
      processingEnabled: this.processingEnabled(),
    });
    return this.filtersController
      .init()
      .then(() => {
        let { filters, points, withProcessing, withComment, groupByPolls } =
          deparam(location.search.slice(1));
        this.filtersController.setDefaultValues({
          filters: {
            ...(filters || {}),
            withProcessing,
            withComment,
            groupByPolls,
          },
          rating: points,
        });
      })
      .catch((e) => {
        console.error(e);
      });
  }

  openDetailsModal(review) {
    loadReviewSidesheet().then(() => {
      this.detailsDialogs().add({
        name: "review-sidesheet",
        params: {
          reviewId: review.id,
          executorMode: this.executorMode,
          answers: review.answers,
          enableActions: true,
          isRequestProcessingEnabled: this.processingEnabled(),
          pollId: this.pollId,
        },
        events: {
          update: (data) => {
            const reviewId = Number(data.reviewId);
            const action = data.action;
            const review = this.reviews
              .reviews()
              .find((el) => el.id === reviewId);
            if (action === "delete") {
              this.reviews.reviews.remove(review);
              this.reviews.saveMessageText("Анкета удалена");
              this.reviews.showSaveMessage(true);
              this.applyFilters();
              return;
            } else {
              review.update(review);
            }
          },
          hide: () => {
            const url = new URL(window.location.href)
            url.searchParams.delete('reviewId')
            window.history.pushState(null, null, url);
          }
        },
      });
    });
  }

  afterPrepare() {
    this.userResultTemplate = select2templates.user.result;
    this.folderResultTemplate = select2templates.folder.result;
    this.folderSelectionTemplate = select2templates.folder.selection;

    this.viewMode = ko
      .observable(this.searchParams.get("viewmode") || "list")
      .extend({ notify: "always" });

    this.page = ko.observable(1).extend({ notify: "always" });
    this.page.subscribe((v) => {
      if (v == 1) {
        let container = this.refs.tableContainer();
        if (container) {
          container.resetScroll();
        }
      }
    });

    this.search = new SearchModel(
      () => this.applyFilters(),
      this.searchParams,
      this.clientFields()
    );
    this.sort = new SortModel(() => this.applyFilters(), this.searchParams, {
      fields: this.sortFields,
    });
    this.reviews = new ReviewsModel(this);
    this.reviews.reviews.subscribe((newReviewsArray) => {
      this.getHasSizeAnswers(newReviewsArray);
    });
    this.getSourceType = getSourceType;

    Object.keys(this.directories).forEach((key) => {
      this.directories[key].load();
    });
    if (!this.processingEnabled()) {
      this.viewMode("list");
    }

    let columns = this.columns;

    if (!this.processingEnabled()) {
      let removeColumns = [
        "processingTime",
        "moderator",
        "executor",
        "processingStatus",
      ];
      columns = columns.filter((c) => {
        return removeColumns.indexOf(c[0]) === -1;
      });
    }

    this.table = new TableModel(this, {
      columnsKey: this.columnsKey,
      columns,
      clientSystemFields: this.clientSystemFields.map((f) => f.fullName),
      clientFields: this.clientFields().map((f) => f.fullName),
    });

    this.kanban = {
      update: null,
    };

    this.query = ko.observable("");
    if (this.searchParams.has("query")) {
      this.query(this.searchParams.get("query"));
    }

    this.hasFilials = ko.computed(() => {
      if (!this.directories.filials.list().length) return false;
      if (!this.single) return true;
      if (!this.isAuto) return true;
      if (this.hasOrder) return true;
      return false;
    });
  }

  subscribeAfterPrepare() {
    this.viewMode.subscribe(() => {
      this.onViewModeChange();
    });
  }

  openComments(review, comments) {
    if (comments) {
      this.openDialog({
        name: "review-rating-dialog",
        params: {
          answers: review.answersWithComments,
        },
      });
      return;
    }

    this.openDialog({
      name: "review-rating-dialog",
      params: {
        answers: review.ratingAnswers,
      },
    });
  }

  getCurrentStats(id) {
    if (!this.currentStats()) return "0";
    if (!this.statsInited()) return "0";
    let stat = this.currentStats().find((s) => s.id == id);
    if (stat) return stat.total || "0";
    return "0";
  }

  get withPoints() {
    return true;
  }

  get moderatorsDirectoryUrl() {
    return `${APIConfig.baseApiUrlPath}answers/moderators?withProcessings=1&access-token=${APIConfig.apiKey}`;
  }

  get isAuto() {
    return false;
  }

  get isManual() {
    return false;
  }

  get single() {
    return false;
  }

  get directoryParams() {
    return null;
  }

  get sortFields() {
    return [
      "passedAt",
      "filial",
      "id",
      "pollName",
      "status",
      "processingTimeAt",
      "clientName",
      "clientPhone",

      "complaint",
      "sourceType",
      "orderSum",
      "orderTime",
      "orderAddress",

      "pollType",
      "comment",
      "executorName",
      "moderatorName",
      "orderType",
      "orderNumber",
      "clientEmail",
      "channel",
      "pointPercents",
      "device",
      "answerTime",
      "lang",
      "mailing",

      ...this.clientSystemFields.map((f) => f.fullName),
      ...this.clientFields().map((f) => f.id),
    ];
  }

  get hasOrder() {
    return true;
  }

  get withLangs() {
    return true;
  }

  get columnsKey() {}

  get filtersKey() {}

  get columns() {
    let set = [];

    if (window.isAutoPoolEnabled) {
      console.log("alo", this.clientFields());
      set = [
        ["id", "ID", true, null, "Все"],
        ["mode", _t("Вид"), true],
        ["type", _t("Тип"), !this.single],
        ["pollName", _t("Опрос"), !this.single],
        ["passedAt", _t("Пройден"), true, null, "00.00.0000"],
        [
          "filial",
          _t("Филиал"),
          !this.single || !this.isAuto || this.hasOrder,
          null,
          "Все",
        ],
        [
          "processingStatus",
          _t("answers", "Статус обработки"),
          true,
          null,
          "Все",
        ],
        [
          "processingTime",
          _t("answers", "Срок обработки"),
          true,
          null,
          "00.00.0000",
        ],
        ["channel", _t("Канал связи"), true, null, "Все"],
        ["device", _t("Устройство"), true, null, "Все"],
        [
          "answerTime",
          _t("Время заполнения анкеты"),
          true,
          _t("Время"),
          "00:00:00",
        ],
        ["lang", _t("Язык прохождения"), this.withLangs, null, "Все"],
        [
          "mailing",
          _t("Приглашения к опросу"),
          this.single && !this.isAuto,
          null,
          "Все",
        ],
        ["points", _t("Оценки"), true, null, "Все"],
        ["comments", _t("Комментарий"), true, null, "Все"],
        ["answerTags", _t("Теги анкеты"), true, null, "Все"],

        ["complaint", _t("Жалоба"), true, null, "Все"],
        ["pointsCollect", _t("Баллов набрано"), this.withPoints],

        ["clientName", _t("ФИО"), true, null, "Все"],
        ["clientPhone", _t("Телефон"), true, null, "Все"],
        ["clientEmail", _t("Email"), true, null, "Все"],

        ...this.clientSystemFields.map((f) => {
          return [f.fullName, f.text, true, null, f.placeholder];
        }),
        ...this.clientFields().map((f) => {
          return [f.fullName, f.text, true, null, f.placeholder];
        }),

        ["moderator", _t("answers", "Модератор"), true, null, "Все"],
        [
          "executor",
          _t("answers", "Исполнитель"),
          !window.EXECUTOR_MODE,
          null,
          "Все",
        ],

        ["orderNumber", _t("Номер заказа"), this.hasOrder, null, "Все"],
        ["orderTime", _t("Время заказа"), this.hasOrder, null, "00.00.0000"],
        ["orderSum", _t("Сумма заказа"), this.hasOrder, null, "0"],
        ["orderType", _t("Тип доставки"), this.hasOrder, null, "Все"],
        ["sourceType", _t("Способ оформления"), this.hasOrder, null, "Все"],
        ["orderAddress", _t("Адрес доставки"), this.hasOrder, null, "Все"],
      ];
    } else {
      set = [
        ["id", "ID", true, null, "Все"],
        ["mode", _t("Вид"), true],
        ["type", _t("Тип"), !this.single],
        ["pollName", _t("Опрос"), !this.single],
        ["passedAt", _t("Пройден"), true, null, "00.00.0000"],
        [
          "filial",
          _t("Филиал"),
          !this.single || !this.isAuto || this.hasOrder,
          null,
          "Все",
        ],
        [
          "processingStatus",
          _t("answers", "Статус обработки"),
          true,
          null,
          "Все",
        ],
        [
          "processingTime",
          _t("answers", "Срок обработки"),
          true,
          null,
          "00.00.0000",
        ],
        ["channel", _t("Канал связи"), true, null, "Все"],
        ["device", _t("Устройство"), true, null, "Все"],
        [
          "answerTime",
          _t("Время заполнения анкеты"),
          true,
          _t("Время"),
          "00:00:00",
        ],
        ["answerTags", _t("Теги анкеты"), true, null, "Все"],
        ["lang", _t("Язык прохождения"), this.withLangs, null, "Все"],
        [
          "mailing",
          _t("Приглашения к опросу"),
          this.single && !this.isAuto,
          null,
          "Все",
        ],
        ["points", _t("Оценки"), true, null, "Все"],
        ["comments", _t("Комментарий"), true, null, "Все"],
        ["complaint", _t("Жалоба"), true, null, "Все"],
        ["pointsCollect", _t("Баллов набрано"), this.withPoints],

        ["clientName", _t("ФИО"), true, null, "Все"],
        ["clientPhone", _t("Телефон"), true, null, "Все"],
        ["clientEmail", _t("Email"), true, null, "Все"],

        ...this.clientSystemFields.map((f) => {
          return [f.fullName, f.text, true];
        }),

        ...this.clientFields().map((f) => {
          return [f.fullName, f.text, true];
        }),

        ["moderator", _t("answers", "Модератор"), true, null, "Все"],
        [
          "executor",
          _t("answers", "Исполнитель"),
          !window.EXECUTOR_MODE,
          null,
          "Все",
        ],
      ];
    }

    return set.filter((column) => column[2]);
  }

  get defaultColumnsSet() {
    let columns = [
      "id",
      "mode",
      "pollName",
      "passedAt",
      "filial",
      "channel",
      "device",
      "lang",
      "points",
      "comments",
      "clientName",
      "clientPhone",
      "clientEmail",
      "pointsCollect",
    ];

    return this.columns
      .filter((c) => {
        return columns.includes(c[0]);
      })
      .map((c) => c[0]);
  }

  onViewModeChange() {
    this.applyFilters();
  }

  getOrderType(id) {
    if (!id) return "";
    const orderType = this.directories.orderTypes.getById("" + id);
    if (orderType) return orderType.name;
    return "";
  }

  confirmStatusChange(review, from, to, move) {
    this.openModal(
      "change-status-modal-template",
      {
        currentStatus: from,
        selectedStatus: to,
        review: review,
      },
      (newStatus) => {
        if (!newStatus) {
          move(from);
        } else {
          move(newStatus);
        }
      }
    );
  }

  updateReviewStatus(review, newStatus, success, error) {
    review.updateReviewStatus(newStatus, success, error);
  }

  resetFilters() {
    this.filtersController.reset();
    this.search.reset();
    this.query("");
    this.applyFilters();
    this.exportCSVProcessing = ko.observable(false);
    this.exportCSVProgress = ko.observable(1);
  }

  getQueryParams() {
    let { filters, rating } = this.filtersController.getValues();
    let { withProcessing, withComment, groupByPolls, ...otherFilters } = filters;

    const params = {
      filters: otherFilters,
      search: this.search.getParams(),
      points: rating,
      page: this.page(),
    };

    if (this.sort.additional) {
      params.sortAdditional = this.sort.getParams();
    } else {
      params.order = this.sort.getParams();
    }

    if (this.hasProcessingOptions() && withProcessing) {
      params.withProcessing = 1;
    }

    if (withComment) {
      params.withComment = 1;
    }

    if (groupByPolls) {
      params.groupByPolls = 1;
    }

    return params;
  }

  applyFilters() {
    if (!this.filtersController.submit()) return;

    this.page(1);
    this.reviews.blocked(true);
    this.reviews.reset();

    this.filtersController.afterSubmit();

    let params = this.getQueryParams();

    this.reviews.loadReviews(params, this.viewMode());
    this.updateStats(params);
  }

  getHasSizeAnswers(reviews) {
    const localReviews = reviews;
    let result = false;
    for (let i = 0; i < localReviews.length; i++) {
      for (let j = 0; j < localReviews[i].answers.length; j++) {
        if (localReviews[i].answers[j].type == FILE_QUESTION) {
          result = true;
          break;
        }
      }
    }

    this.hasSizeAnswers(!result);
  }

  updateStats(params) {
    if (params && params.page && params.page > 1) {
      return Promise.resolve();
    }

    if (Object.keys(params).length === 1) {
      const tempParams = {}

      for (let [key, value] of this.searchParams.entries()) {
        if (!tempParams[key]) {
          tempParams[key] = value
        } else {
          tempParams[key] = [tempParams[key], value]
        }
        

      }
      params = tempParams;
    }
    const { filters, ...fields } = params;
    
    const statsParams = {
      filter: filters,
      ...fields,
    };

    return new Promise((resolve, reject) => {
      $.ajax({
        url: `${APIConfig.baseApiUrlPath}answer-processing/statuses?access-token=${APIConfig.apiKey}`,
        data: statsParams || {},
        success: ({ total: current, items: currentItems }) => {
          this.noAnswers(false);
          this.answersFilteredCount(current);
          this.currentStats(currentItems.map(({id, ...data}) => ({id: String(id), ...data})));
          this.statsInited(true);
          resolve();
        },
        error: (response) => {
          console.error(response.responseJSON);
          reject();
        },
      });
    });
  }

  init() {
    const action = () => {
      if (this.noAnswers()) {
        this.initializing(false);
      } else {
        let params = this.getQueryParams();
        //this.updateStats(params);
        this.reviews.loadReviews(params, this.viewMode());
        this.initializing(false);
      }
    };
    if (this.preparing()) {
      let sp = this.preparing.subscribe((v) => {
        action();
        sp.dispose();
      });
    } else {
      action();
    }
  }

  nextPage() {
    if (
      this.initializing() ||
      this.reviews.loading() ||
      this.reviews.isLastRequestEmpty()
    )
      return;
    this.page(this.page() + 1);
    let params = this.getQueryParams();
    this.reviews.loadReviews(params, this.viewMode());
    this.updateStats(params);
  }

  initObservers() {
    const that = this;
    this.observers.reviewsList = new Observer(
      function (entries, observer) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            that.nextPage();
          }
        });
      },
      {
        rootMargin: "50px",
      }
    );

    this.observers.reviewsKanban = new Observer(
      function (entries, observer) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            that.nextPage();
          }
        });
      },
      {
        rootMargin: "50px",
      }
    );

    this.observers.reviewsListHorizontal = new Observer(function (
      entries,
      observer
    ) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          that.nextPage();
        }
      });
    },
    {});

    this.observers.reviewsList.setTarget(
      document.getElementById("reviews-view-bottom")
    );
    this.observers.reviewsList.activate();

    this.observers.reviewsKanban.setTarget(
      document.getElementById("reviews-kanban-bottom")
    );
    this.observers.reviewsKanban.activate();

    this.observers.reviewsListHorizontal.setTarget(
      document.getElementById("reviews-view-right")
    );
    this.observers.reviewsListHorizontal.activate();

    this.observers.topView = new Observer(function (entries, observer) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          that.viewTopIntersecting(false);
        } else {
          that.viewTopIntersecting(true);
        }
      });
    });
    this.observers.topView.setTarget(
      document.getElementById("reviews-view-top")
    );
    this.observers.topView.activate();

    $(window).resize();
  }

  reinitObservers() {
    if (this.observers.reviewsList) {
      this.observers.reviewsList.deactivate();
      this.observers.reviewsList.setTarget(
        document.getElementById("reviews-view-bottom")
      );
      this.observers.reviewsList.activate();
    }

    if (this.observers.reviewsKanban) {
      this.observers.reviewsKanban.deactivate();
      this.observers.reviewsKanban.setTarget(
        document.getElementById("reviews-kanban-bottom")
      );
      this.observers.reviewsKanban.activate();
    }

    if (this.observers.reviewsListHorizontal) {
      this.observers.reviewsListHorizontal.deactivate();
      this.observers.reviewsListHorizontal.setTarget(
        document.getElementById("reviews-view-right")
      );
      this.observers.reviewsListHorizontal.activate();
    }
  }

  getRatingLabel(value) {
    switch (value) {
      case 1:
        return _t("Ужасно");
      case 2:
        return _t("Плохо");
      case 3:
        return _t("Нормально");
      case 4:
        return _t("Хорошо");
      case 5:
        return _t("Отлично");
    }
  }

  trigger(eventName, params) {
    if (eventName === "review.status.updated") {
      this.directories.processingStatuses.load("force");
      let params = this.getQueryParams();
      this.updateStats(params);

      if (this.viewMode() === "kanban") {
        this.kanban.update();
      }
    }

    if (eventName === "update.url.params") {
      const updatedParams = {
        ...params,
        viewmode: this.viewMode(),
      };

      updateUrl(updatedParams);
    }
  }

  onInit() {
    this.init();
  }

  completePollForClient() {
    this.dialogs().add({
      name: "take-poll-sidesheet",
      params: {},
    });
  }

  toExcel() {
    let params = this.getQueryParams();
    if (this.single) params.id = this.id;

    const columns = this.table.getVisibleColumns().join(",");
    params.columns = columns;

    params.page = -1;
    let url = `/foquz/answers/to-excel-file?${$.param(params)}`;
    location.href = url;
  }

  toExcel2() {
    let params = this.getQueryParams();
    if (this.single) params.id = this.id;

    const columns = this.table.getVisibleColumns().join(",");
    params.columns = columns;

    params.page = -1;
    let url = `/foquz/answers/excel2?${$.param(params)}`;
    location.href = url;
  }
  async toExcel3(type) {
    this.exportCSVProcessing(true);
    this.exportCSVProgress(1);
    const params = {
      ...this.getQueryParams(),
      "access-token": APIConfig.apiKey,
      file_type: "xlsx",
      entity_type: type,
      id: this.id,
    };
    if (this.single) params.id = this.id;

    const columns = this.table.getVisibleColumns().join(",");
    params.columns = columns;

    params.page = -1;

    try {
      let data = await $.post(`/foquz/api/export/request?${$.param(params)}`);
      data = { data };
      const checkStatus = async () => {
        data = await $.post(
          `/foquz/api/export/status?id=${data.data.id}&access-token=${APIConfig.apiKey}`
        );
        if (data && data.error) {
          throw new Error(JSON.stringify(data.error));
        }
        if (_get(data, "data.status", -1) === EXPORT_CSV_SUCCESS_STATUS) {
          this.exportCSVProcessing(false);
          window.location.href = `/foquz/api/export/get-file?id=${data.data.id}&access-token=${APIConfig.apiKey}`;
          return;
        }
        this.exportCSVProgress((100 * data.data.processed) / data.data.total);
        setTimeout(checkStatus, 3000);
      };
      checkStatus();
    } catch (error) {
      this.exportCSVProcessing(false);
      throw error;
    }
    /* let url = `/foquz/answers/excel2?${$.param(params)}`;
    location.href = url; */
  }

  async toArchive(root) {
    this.archivePreparing(true);
    const reviews = this.reviews.reviews();
    const hasSizeAnswers = this.hasSizeAnswers();
    const answersId = reviews.map((i) => i.id);

    let params = this.getQueryParams();
    if (this.single) params.id = this.id;

    params.columns = this.table.getVisibleColumns().join(",");

    params.page = -1;
    let bodyParamsInfo = {
      "access-token": ACCESS_TOKEN,
      getAnswerFiles: "info",
    };

    let bodyParamsZip = {
      "access-token": ACCESS_TOKEN,
      getAnswerFiles: "zip",
    };
    let infoUrl = `/foquz/api/answers?${$.param(bodyParamsInfo)}&${$.param(
      params
    )}`;
    let zipUrl = `/foquz/api/answers?${$.param(bodyParamsZip)}&${$.param(
      params
    )}`;

    this.dialogs().add({
      name: "download-archive-dialog",
      params: {
        data: this.archiveData,
        answersId,
        hasSizeAnswers,
        preparing: this.archivePreparing,
        url: zipUrl,
      },
    });

    const { data, error } = await request(infoUrl, {
      method: "GET",
    });

    this.archiveData({ data, error });
    this.archiveData.valueHasMutated();
    this.archivePreparing(false);
  }

  async exportCSV(type) {
    this.exportCSVProcessing(true);
    this.exportCSVProgress(1);

    const params = {
      ...this.getQueryParams(),
      "access-token": APIConfig.apiKey,
      file_type: "csv",
      entity_type: type,
      id: this.id,
    };

    if (this.single) {
      params.id = this.id;
    }

    const columns = this.table.getVisibleColumns().join(",");
    params.columns = columns;

    params.page = -1;

    try {
      let data = await $.post(`/foquz/api/export/request?${$.param(params)}`);
      data = { data };
      const checkStatus = async () => {
        data = await $.post(
          `/foquz/api/export/status?id=${data.data.id}&access-token=${APIConfig.apiKey}`
        );
        if (data && data.error) {
          throw new Error(JSON.stringify(data.error));
        }
        if (_get(data, "data.status", -1) === EXPORT_CSV_SUCCESS_STATUS) {
          this.exportCSVProcessing(false);
          window.location.href = `/foquz/api/export/get-file?id=${data.data.id}&access-token=${APIConfig.apiKey}`;
          return;
        }
        this.exportCSVProgress((100 * data.data.processed) / data.data.total);
        setTimeout(checkStatus, 5000);
      };
      checkStatus();
    } catch (error) {
      this.exportCSVProcessing(false);
      throw error;
    }
  }

  toGoogle() {
    let params = this.getQueryParams();
    if (this.single) params.id = this.id;

    const columns = this.table.getVisibleColumns().join(",");
    params.columns = columns;

    params.page = -1;
    let url = `/google-sheets/create/answers/${USER_ID}?${$.param(params)}`;
    window.open(url);
  }

  toGoogle2() {
    let params = this.getQueryParams();
    if (this.single) params.id = this.id;

    const columns = this.table.getVisibleColumns().join(",");
    params.columns = columns;

    params.page = -1;
    let url = `/google-sheets/create/answers2/${USER_ID}?${$.param(params)}`;
    window.open(url);
  }
}
