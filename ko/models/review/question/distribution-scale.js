import { DISTRIBUTION_SCALE_QUESTION } from "Data/question-types";
import { ReviewQuestion } from "./question";
import { getRecipientVariants } from "Models/review/question/utils";

export class ReviewQuestionDistributionScale extends ReviewQuestion {
  constructor(data, questions) {
    super(data);
    this.type = DISTRIBUTION_SCALE_QUESTION;

    this.rating = 0;

    this.donorIndex = null;
    this.donorQuestion = null;

    let config = data.scaleRatingSetting;
    this.config = {
      minValue: config.start,
      maxValue: config.end,
      stepValue: config.step,
    };
    this.variants = (data.variants || []).map((v, i) => {
      return {
        id: "" + v.id,
        points: v.points,
        text: v.variant,
        index: i + 1,
      };
    });

    this.skipped = false;
    this.answerData = {};

    this.indicatorText = data.self_variant_text || 'Распределено';

    if (data.answer) {
      this.skipped = !!data.answer.skipped;
      

      if (!this.skipped) {
        if (data.answer?.answer) {
          this.answerData = Object.entries(data.answer?.answer).map(i => [i[0], parseInt(i[1])]).reduce((acc, i) => {
            acc[i[0]] = i[1]
            return acc
          }, {})
          this.totalRating = Object.values(data.answer?.answer || {}).reduce((sum, current) => sum + Number(current), 0)
          this.maxRating = data.scaleRatingSetting?.end || 100;
        } else {
          this.answerData = {}
        }
        this.rating = data.answer?.rating || data.answer?.answer?.rating;
      }
      this.answerMaxPoints = data.answer.max_points
    }
  }

  checkAnswer(answer) {
    if (!answer) return false;
    if (answer?.skipped) return true;

    if (answer?.comment || answer?.answer?.comment) return true;

    if (this.variants && this.variants.length) {
      return answer.rating > -1 || answer.answer;
    }

    answer = answer.answer;
    if (!answer) return false;
    if (answer.rating != null && answer.rating > -1) return true;

    return false;
  }
}
