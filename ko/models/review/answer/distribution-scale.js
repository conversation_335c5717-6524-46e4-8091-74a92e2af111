import { DISTRIBUTION_SCALE_QUESTION } from 'Data/question-types';
import { ReviewAnswer } from './answer';

export class ReviewAnswerScale extends ReviewAnswer {
  constructor(data) {
    super(data);
    this.minValue = 0;
    this.maxValue = 1000;
    this.variantSkipped = data.variant_skipped;
    if (data.scaleRatingSetting) {
      this.minValue = data.scaleRatingSetting.start;
      this.maxValue = data.scaleRatingSetting.end;
    }

  }

  get type() {
    return DISTRIBUTION_SCALE_QUESTION;
  }

  get withRating() {
    return this.rating !== null && this.rating >= 0;
  }

  get ratingPoint() {
    return this.getRatingPoint(this.rating, this.minValue, this.maxValue);
  }
}
