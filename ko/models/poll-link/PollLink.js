import { ApiUrl } from "Utils/url/api-url";
import { LinkModel } from './LinkModel';

export class PollLink {
  constructor(poll) {
    this.allowCreateQuote = true;
    this.questions = [];
    this.pollId = poll.id;
    this.poll = poll;
    this.linkModels = ko.observableArray([]);
  }
  
  async getQuestions() {
    await new Promise((res) => {
      $.ajax({
        url: ApiUrl("test-poll/get-questions", { id: this.pollId }),
        method: "GET",
        success: ({ allow_create_quote, items }) => {
          this.allowCreateQuote = allow_create_quote;
          this.questions = items;
          res();
        },
        error: (response) => {
          console.error(response)
          res();
        },
      });
    })
  }

  async getLink() {
    return new Promise((resolve) => {
      $.ajax({
        url: ApiUrl("test-poll/link", {
          id: this.pollId,
        }),
        success: (response) => {
          response.items
            .reverse()
            .forEach((item) => {
              this.linkModels
                .push(new LinkModel(item, this.pollId));
            });
          resolve();
        },
      });
    });
  }

  async addLink() {
    return new Promise((resolve) => {
      $.ajax({
        method: 'POST',
        url: ApiUrl("test-poll/create-link", {
          id: this.pollId,
        }),
        success: (response) => {
          const linkModel = new LinkModel(response.items[0], this.pollId);
          this.linkModels.push(linkModel);
          resolve(linkModel)
        },
      });
    });
  }
}