export class AnswerQuota {
  constructor(
    {
      answers_limit = '',
      groups = [],
      criteria = [],
      id = 0,
      logic_operation = 0,
      end_screen = 0,
      position = 0,
    } = {},
    group = false
  ) {
    this.position = ko.observable(position);
    this.isGroup = group;
    this.id = ko.observable(id);
    this.endScreen = ko.observable(end_screen || '');
    this.answersLimit = ko.observable(answers_limit || '');
    this.logicOperation = ko.observable(logic_operation);
    this.criteria = ko.observableArray(criteria.map(item => new AnswerQuotaCriteria(item)));
    if (this.isGroup && !this.criteria().length) {
      this.criteria.push(new AnswerQuotaCriteria())
    }
    this.groups = ko.observableArray(groups.map(group => new AnswerQuota(group, true)));
    this.conditions = ko.computed(() => {
      const criteria = this.criteria();
      const groups = this.groups();
      return [
        ...criteria,
        ...groups,
      ].sort((a, b) => (a.position() || 0) - (b.position() || 0));
    });

    this.addCriteria = this.addCriteria.bind(this);
    this.addGroup = this.addGroup.bind(this);
    this.getData = this.getData.bind(this);
    this.deleteCondition = this.deleteCondition.bind(this);
    this.update = this.update.bind(this);

    this.isValid = ko.computed(() => {
      const conditions = this.conditions();
      const isValid = !conditions.some(condition => !condition.isValid());
      return isValid;
    });
  }

  update({ id = 0 } = {}) {
    this.id(id);
  }

  deleteCondition(idToDelete, positionToDelete) {
    const groups = this.groups()
      .filter(({ id, position }) => {
        return !((id() === idToDelete()) && (position() === positionToDelete));
      });
    const criteria = this.criteria()
      .filter(({ id, position }) => {
        return !((id() === idToDelete()) && (position() === positionToDelete));
      });
    
    this.groups(groups);
    this.criteria(criteria);

    this.conditions().forEach((condition, index) => {
      condition.position(index);
    });
  }

  addCriteria(position) {
    this.criteria.push(new AnswerQuotaCriteria({ position }));
  }

  addGroup(position) {
    this.groups.push(new AnswerQuota({ position }, true));
  }

  getData() {
    const answerQuota = {
      id: Number(this.id()),
      answers_limit: Number(this.answersLimit()) || 0,
      logic_operation: Number(this.logicOperation()),
      end_screen: Number(this.endScreen()) || 0,
      position: Number(this.position()) || 0,
      criteria: [],
      groups: [],
    };

    const conditions = this.conditions();
    conditions.forEach(item => {
      const data = item.getData();
      if (item.isGroup) {
        answerQuota.groups.push(data);
      } else {
        answerQuota.criteria.push(data);
      }
    });

    return answerQuota;
  }
}

export class AnswerQuotaCriteria {
  constructor({
    behavior = 1,
    id = 0,
    question_id = 0,
    variants = 0,
    position = 0,
  } = {}) {
    this.position = ko.observable(position);
    this.id = ko.observable(id);
    this.questionId = ko.observable(String(question_id));
    this.behavior = ko.observable(behavior);
    this.variants = ko.observableArray(variants ? variants.map(variant => String(variant)) : null)

    this.questionId.subscribe(() => this.variants([]))

    this.getData = this.getData.bind(this);

    this.isValid = ko.computed(() => {
      const behavior = this.behavior();
      const enableVariants = behavior == 1 || behavior == 2;
      if (!enableVariants) return true;

      const variants = this.variants();
      return !!variants.length;
    });
  }

  getData() {
    const behavior = Number(this.behavior());
    const enableVariants = behavior == 1 || behavior == 2;
    const criteria = {
      id: Number(this.id()),
      behavior: Number(behavior),
      question_id: Number(this.questionId()),
      variants: enableVariants ? this.variants() : [],
      position: Number(this.position()),
    };

    return criteria;
  }
}