import { ApiUrl } from "Utils/url/api-url";

export class LinkPreview {
  constructor({
    key,
    short_link,
    filialId,
    pollId,
    quoteId,
    mainLinkKey,
  }) {
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher();
    this.shortLink = ko.observable(short_link);
    this.filialId = filialId;
    this.pollId = pollId;
    this.quoteId = quoteId;
    this.mainLinkKey = mainLinkKey;

    this.key = ko.observable(key).extend({
      required: {
        message: "Обязательное поле",
      },
      minLength: {
        params: 2,
        message: "Должно быть введено хотя бы 2 символа",
      },
      pattern: {
        params: "^[0-9a-zA-Z_]*$",
        message: "Неверный формат",
      },
      validation: {
        validator: (value) => {
          return value.replace(/_/g, "").length > 0;
        },
        message: "Неверный формат",
      },
    });
    this.tablet = ko.observable(false);
    this.lang = ko.observable('');
    
    this.fullLink = ko.computed(() => {
      const key = this.key();
      return this.getBase() + key;
    })
    this.qr = ko.computed(() => {
      const link = this.fullLink();
      return this.getQR(link);
    });

        
    this.fullLinkWithParams = ko.computed(() => {
      const key = this.key();
      const tablet = this.tablet();
      const lang = this.lang();
      return this.getBase(tablet) + key + this.getQuery(lang);
    });
        
    this.baseWithParams = ko.computed(() => {
      const tablet = this.tablet();
      return this.getBase(tablet);
    });
    this.queryWithParams = ko.computed(() => {
      const lang = this.lang();
      return this.getQuery(lang);
    });

    this.qrWithParams = ko.computed(() => {
      const link = this.fullLinkWithParams();
      return this.getQR(link);
    });
  }

  getBase(tablet = false) {
    const origin = location.origin;
    const folder = '/p/' + (tablet ? 't/' : '');
    return origin + folder;
  }

  getQuery(lang = '') {
    const query = lang ? `?lang=${lang}` : '';
    return query;
  }

  getQR(link) {
    return `/foquz/foquz-poll/qr?v=${link}`;
  }

  reset() {
    this.tablet(false);
    this.lang('');
  }

  async changeLink() {
    if (this.key.isValid()) {
      return new Promise((res, rej) => {
        $.ajax({
          url: ApiUrl("test-poll/change-link", {
            id: this.pollId,
            filialId: this.filialId,
            quoteId: this.quoteId,
          }),
          method: "POST",
          data: {
            newKey: this.key(),
          },
          success: (response) => {
            if (!this.filialId && this.mainLinkKey) {
              this.mainLinkKey(this.key())
            }
            res(response)
          },
          error: (jqXHR) => {
            rej(jqXHR)
          },
        });
      })
    }
  }
}