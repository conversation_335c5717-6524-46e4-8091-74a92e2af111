import { AnswerQuota } from './AnswerQuota';
import { ApiUrl } from "Utils/url/api-url";
import { LinkPreview } from './LinkPreview';

export class LinkModel {
  constructor({
    link_name = '',
    active = 1,
    datetime_end = null,
    limit = null,
    key,
    short_link,
    quote_id,
    filial_links,
    has_answer_quotes,
  }, pollId) {
    this.pending = ko.observable(false);

    this.pollId = pollId;
    this.limit = limit;
    this.shortLink = short_link;
    this.quoteId = quote_id;
    this.filialLinks = filial_links;
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher();

    this.active = ko.observable(!!active);
    this.active.subscribe((v) => {
      setTimeout(() => {
        this.toggleActive(v);
      }, 0)
    });

    this.linkName = ko.observable(link_name);

    this.link = new LinkPreview({ key, short_link, pollId, quoteId: this.quoteId });

    this.savedLinkQuotes = {
      limit,
      datetime_end,
    };

    const datetimeEnd = moment(datetime_end, "YYYY-MM-DDTHH:mm:ssZ")
      .format("DD.MM.YYYY");
    const datetimeEndTime = moment(datetime_end, "YYYY-MM-DDTHH:mm:ssZ")
      .format("HH:mm");
    this.datetimeEndTime = ko.observable(datetimeEndTime === 'Invalid date' ? '' : datetimeEndTime).extend({
      validation: {
        validator: (value) => {
          const timeIsValid = moment(value, "HH:mm").isValid();
          return !value || (value === '00:00') || timeIsValid;
        },
        message: "Неверный формат",
      },
    });
    this.datetimeEnd = ko.observable(datetimeEnd === 'Invalid date' ? '' : datetimeEnd).extend({
      validation: [
        {
          validator: (value) => {
            const date = value;
            const time = this.datetimeEndTime();
            return !time || date;
          },
          message: "Обязательное поле",
        },
        {
          validator: (value) => {
            const dateIsValid = moment(value, "DD.MM.YYYY").isValid();
            return !value || (value === '00.00.0000') || dateIsValid;
          },
          message: "Неверный формат",
        },
      ]
    });

    this.isChanged = ko.observable(false);

    this.savedAnswerQuotes = null;
    this.isDownloadAnswerQuotes = ko.observable(false);
    this.answerQuotes = ko.observableArray([]);
    this.hasAnswerQuotes = ko.observable(has_answer_quotes);
    
    const {
      subscribeAll,
      unsubscribeAll,
    } = this.createSubscriber(() => {
      this.isChanged(true);
    });

    this.subscribeAll = subscribeAll;
    this.unsubscribeAll = unsubscribeAll;

    this.limitAnswer = ko.observable(this.limit);
    this.limitAnswerError = ko.observable('');
    this.formattedLimitAnswer = ko.pureComputed({
      read() {
        const limitAnswer = this.limitAnswer();
        if (limitAnswer === null) {
          return '';
        } 
        return limitAnswer;
      },
      write(value) {
        this.limitAnswerError('');
        const initLimitAnswer = this.limitAnswer();
        let newLimitAnswer;
        if (value === null) {
          newLimitAnswer = null;
        } else if (value === '') {
          newLimitAnswer = null;
        } else {
          value = String(value).replace(/\D/ig, '');
          if (value === '') {
            newLimitAnswer = null;
          } else {
            newLimitAnswer = Number(value);
          }
        }
        if (initLimitAnswer === newLimitAnswer) {
          this.limitAnswer(NaN);
        }
        this.limitAnswer(newLimitAnswer);
      },
      owner: this
    });

    this.hasQuote = {
      link: ko.computed(() => {
        const limitAnswer = this.limitAnswer();
        const date = {
          date: this.datetimeEnd(),
          time: this.datetimeEndTime(),
        };
    
        let datetimeEnd = null;
        let timeString = '';
    
        if (date.date && date.date !== 'Invalid date' && date.date !== '00.00.0000') {
          timeString = date.date;
          if (date.time && date.time !== 'Invalid date' && date.time !== '00:00') {
            timeString += date.time;
          }
        }
        if (timeString) {
          datetimeEnd = moment(timeString, "DD-MM-YYYY HH:mm")
            .format("YYYY-MM-DDTHH:mm:ssZ");
    
          if (datetimeEnd === 'Invalid date') {
            datetimeEnd = null;
          }
        }
        return !!limitAnswer || !!datetimeEnd;
      }),
      answers: ko.computed(() => {
        const isDownloadAnswerQuotes = this.isDownloadAnswerQuotes();
        const hasAnswerQuotes = this.hasAnswerQuotes();
        const answerQuotes = this.answerQuotes();
        return isDownloadAnswerQuotes
          ? !!answerQuotes.length
          : hasAnswerQuotes;
      }),
    }

    this.datetimeIsValid = ko.computed(() => {
      const datetimeEndError = this.datetimeEnd.isValid();
      const datetimeEndTimeError = this.datetimeEndTime.isValid();
      return datetimeEndError && datetimeEndTimeError;
    })
  }

  createSubscriber(fn) {
    const subscriptions = [];

    const unsubscribeAll = () => {
      subscriptions.forEach(subscription => {
        subscription.dispose();
      })
    }

    const cullback = () => {
      fn();
      unsubscribeAll();
    }
    
    const subscribeAll = (variable) => {
      const isObservable = ko.isObservable(variable);

      if (isObservable) {
        const subscription = variable.subscribe(cullback)
        subscriptions.push(subscription);
      }

      const value = isObservable
        ? variable()
        : variable;

      if (Array.isArray(value)) {
        value.forEach((value) => setTimeout(() => subscribeAll(value)))
      } else if (Object.values(value).length) {
        Object.values(value)
          .forEach((value) => setTimeout(() => subscribeAll(value)))
      }
    }

    return {
      subscribeAll,
      unsubscribeAll,
    }
  }

  async getAnswerQuotes() {
    await new Promise((res) => {
      $.ajax({
        url: ApiUrl("test-poll/get-answer-quotes", {
          id: this.pollId,
          linkQuoteId: this.quoteId,
        }),
        method: "GET",
        success: ({ items: [{ answer_quotes }] }) => {
          this.savedAnswerQuotes = answer_quotes;
          const answerQuotes = (answer_quotes || [])
            .map(item => new AnswerQuota(item));
          this.answerQuotes(answerQuotes);
          res();
        },
        error: (response) => {
          console.error(response)
          res();
        },
      });
    })
  }

  addAnswerQuotes() {
    this.answerQuotes.push(new AnswerQuota());
  }

  deleteAnswerQuotes(idToDelete, positionToDelete) {
    const answerQuotes = this.answerQuotes()
      .filter(({ id, position }) => {
        return !((id() === idToDelete()) && (position() === positionToDelete));
      })
      .map((quota, index) => {
        quota.position(index);
        return quota;
      });
    this.answerQuotes(answerQuotes);
  }

  async saveAnswerQuotes() {
    const answerQuotes = this.answerQuotes().map(quota => quota.getData());
    await new Promise((res) => {
      $.ajax({
        url: ApiUrl("test-poll/save-answer-quotes", {
          id: this.pollId,
          linkQuoteId: this.quoteId,
        }),
        method: "POST",
        data: { answerQuotes },
        success: ({ items: [{ answer_quotes }] }) => {
          this.savedAnswerQuotes = answer_quotes;
          const answerQuotes = answer_quotes || [];
          this.answerQuotes().forEach((quota, i) => {
            quota.update(answerQuotes[i])
          });
          this.hasAnswerQuotes(answerQuotes.length);
          res();
        },
        error: (response) => {
          console.error(response)
          res();
        },
      });
    })
  }

  resetLinkQuotes() {
    const {
      datetime_end,
      limit,
    } = this.savedLinkQuotes;

    this.formattedLimitAnswer(limit);
    const datetimeEnd = moment(datetime_end, "YYYY-MM-DDTHH:mm:ssZ")
      .format("DD.MM.YYYY");
    const datetimeEndTime = moment(datetime_end, "YYYY-MM-DDTHH:mm:ssZ")
      .format("HH:mm");
    this.datetimeEndTime(datetimeEndTime === 'Invalid date' ? '' : datetimeEndTime);
    this.datetimeEnd(datetimeEnd === 'Invalid date' ? '' : datetimeEnd);
  }

  resetAnswerQuotes() {
    const answerQuotes = (this.savedAnswerQuotes || [])
      .map(item => new AnswerQuota(item));
    this.answerQuotes(answerQuotes);
    this.hasAnswerQuotes(answerQuotes.length)
  }

  async getLinkPreview(filialId) {
    const pollId = this.pollId;
    const quoteId = this.quoteId;
    
    if (filialId) {
      if (!this.filialLinks.length) {
        await this.createFilialLinks();
      }
      const link = this.filialLinks
        .find(({ filial_id }) => String(filial_id) === String(filialId));
      if (link) {
        const { key, short_link } = link;
        return new LinkPreview({ key, short_link, filialId, pollId, quoteId });
      } else {
        return null;
      }
    } else {
      return new LinkPreview({
        key: this.link.key(),
        short_link: this.shortLink,
        pollId,
        quoteId,
        mainLinkKey: this.link.key
      });
    }
  }

  async createFilialLinks() {
    await new Promise((res) => {
      $.ajax({
        url: ApiUrl("test-poll/create-filial-links", {
          id: this.pollId,
          quoteId: this.quoteId,
        }),
        method: "POST",
        success: (response) => {
          this.filialLinks = response?.items[0]?.filial_links;
          res();
        },
        error: (response) => {
          console.error(response)
          res();
        },
      });
    })
  }

  async editQuote() {
    if (!this.datetimeIsValid()) return;
    this.pending(true);
    const date = {
      date: this.datetimeEnd(),
      time: this.datetimeEndTime(),
    };

    let datetimeEnd = null;
    let timeString = '';

    if (date.date && date.date !== 'Invalid date' && date.date !== '00.00.0000') {
      timeString = date.date;
      if (date.time && date.time !== 'Invalid date' && date.time !== '00:00') {
        timeString += date.time;
      }
    }
    if (timeString) {
      datetimeEnd = moment(timeString, "DD-MM-YYYY HH:mm")
        .format("YYYY-MM-DDTHH:mm:ssZ");

      if (datetimeEnd === 'Invalid date') {
        datetimeEnd = null;
      }
    }

    await new Promise((res, rej) => {
      $.ajax({
        url: ApiUrl("test-poll/edit-quote", {
          id: this.pollId,
          quoteId: this.quoteId,
        }),
        method: "POST",
        data: {
          limit : this.formattedLimitAnswer(),
          datetimeEnd: datetimeEnd,
        },
        success: ({ items }) => {
          const {
            datetime_end,
            limit,
          } = items.find(({ quote_id }) => quote_id === this.quoteId);
          this.savedLinkQuotes = {
            datetime_end,
            limit,
          }
          res()
        },
      });
    });
    this.pending(false);
  }

  async saveLinkName(value) {
    this.pending(true);
    await new Promise((res) => {
      $.ajax({
        url: ApiUrl("test-poll/edit-link", {
          id: this.pollId,
          quoteId: this.quoteId,
        }),
        method: "POST",
        data: {
          name: value,
        },
        success: (response) => {
          this.linkName(value);
          this.pending(false);
          res();
        },
        error: (response) => {
          this.pending(false);
          res();
        },
      });
    })
  }

  downloadAllLinks() {
    const params = {
      id: this.pollId,
      linkKey: this.link.key(),
    };
    location.href = ApiUrl("test-poll/links-all", params);
  }

  toggleActive(value) {
    this.pending(true);
    $.ajax({
      url: ApiUrl("test-poll/toggle-link", {
        id: this.pollId,
        quoteId: this.quoteId,
        linkKey: this.link.key(),
      }),
      method: "POST",
      data: {
        active: value ? 1 : 0,
      },
      success: (response) => {
        this.pending(false);
      },
      error: (response) => {
        console.error(response)
        this.pending(false);
      },
    });
  }
}