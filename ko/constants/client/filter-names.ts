import { ContactFilterType } from "@/entities/structures/conditions/contact-filter-types";

export const ContactFilterNames = {
  [ContactFilterType.Search]: "Фамилия, телефон или email",
  [ContactFilterType.Gender]: "Пол",
  [ContactFilterType.Birthday]: "По дате рождения",
  [ContactFilterType.CreatedAt]: "По дате добавления",
  [ContactFilterType.UpdatedAt]: "По дате обновления",
  [ContactFilterType.ClientFilial]: "Филиал контакта",
  [ContactFilterType.ContactData]: "Данные контакта",
  [ContactFilterType.TagsInclude]: "Включить контакты с тегами",
  [ContactFilterType.TagsExclude]: "Исключить контакты с тегами",

  [ContactFilterType.LastOrderDate]: "Дата последнего заказа",
  [ContactFilterType.Complaint]: "Наличие жалобы в заказе",
  [ContactFilterType.AvgOrdersNumberPerYear]:
    "Среднее количество заказов в год",
  [ContactFilterType.AvgOrdersNumberPerMonth]:
    "Среднее количество заказов в месяц",
  [ContactFilterType.RevenueForYear]:
    "Выручка от заказов, сделанных контактом за год",
  [ContactFilterType.RevenueForMonth]:
    "Выручка от заказов, сделанных контактом за месяц",
  [ContactFilterType.AvgCheck]: "Средний чек по заказам контакта",
  [ContactFilterType.OrderDays]: "Основные дни заказов",
  [ContactFilterType.OrderTime]: "Любимое время заказа",
  [ContactFilterType.FavoriteDish]: "Любимое блюдо",
  [ContactFilterType.OrderType]: "Тип заказа",
  [ContactFilterType.SourceType]: "Способ оформления",
  [ContactFilterType.Filial]: "Филиал",

  [ContactFilterType.PollsParticipation]: "Участие в опросах, %",
  [ContactFilterType.UsedPromocodesPercent]:
    "Процент использованных промокодов (от выданных)",
  [ContactFilterType.Promocode]: "Промокод",
  [ContactFilterType.PushToken]: "Токен контакта для Push-уведомлений",
};
