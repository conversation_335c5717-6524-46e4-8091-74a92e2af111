export enum ClientConditions {
  lastOrderDate = "last-order-date",
  complaint = "complaint-in-the-order",
  avgOrdersNumberPerYear = "avg-year-count-orders",
  avgOrdersNumberPerMonth = "avg-month-count-orders",
  revenueForYear = "order-amount-from-client-by-year",
  revenueForMonth = "order-amount-from-client-by-month",
  avgCheck = "avg-check-customer-orders",
  orderDays = "main-orders-day",
  orderTime = "favorite-order-time",
  favoriteDish = "favorite-dish",
  pollsParticipation = "polls-participation",
  orderType = "order-type",
  sourceType = "source-type",
  filial = "filial",
  usedPromocodesPercent = "codes-percent",
  promocode = "promocode",
  clientData = "contact-data",
}

export const ClientConditionNames = {
  [ClientConditions.lastOrderDate]: "Дата последнего заказа",
  [ClientConditions.complaint]: "Наличие жалобы в заказе",
  [ClientConditions.avgOrdersNumberPerYear]: "Среднее количество заказов в год",
  [ClientConditions.avgOrdersNumberPerMonth]:
    "Среднее количество заказов в месяц",
  [ClientConditions.revenueForYear]:
    "Выручка от заказов, сделанных контактом за год",
  [ClientConditions.revenueForMonth]:
    "Выручка от заказов, сделанных контактом за месяц",
  [ClientConditions.avgCheck]: "Средний чек по заказам контакта",
  [ClientConditions.orderDays]: "Основные дни заказов",
  [ClientConditions.orderTime]: "Любимое время заказа",
  [ClientConditions.favoriteDish]: "Любимое блюдо",
  [ClientConditions.pollsParticipation]: "Участие в опросах, %",
  [ClientConditions.orderType]: "Тип заказа",
  [ClientConditions.sourceType]: "Способ оформления",
  [ClientConditions.filial]: "Филиал",
  [ClientConditions.usedPromocodesPercent]:
    "Процент использованных промокодов (от выданных)",
  [ClientConditions.promocode]: "Промокод",
  [ClientConditions.clientData]: "Данные контакта",
};
