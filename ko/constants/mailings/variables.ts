interface LinkButtonConfig {
  text?: string;
  link?: string;
}

export function getLinkButton(config?: LinkButtonConfig) {
  return `
  <a target="_blank" style="display: inline-block; align-items: center; justify-content: center; height: 40px; line-height: 40px; text-decoration: none; background: #2E2F31; border-radius: 100px; font-family: Robot<PERSON>, Arial, sans-serif; font-style: normal; font-weight: bold; font-size: 15px; text-align: center; color: #FFFFFF; outline: none; box-shadow: none; border: none; padding-left: 25px; padding-right: 25px; margin-left: -4px; margin-right: -4px;" href="{${
    config?.link || "Ссылка"
  }}">
  ${config?.text || "Пройти опрос"}
 </a>`;

  return `<span style="display: inline-block; align-items: center; justify-content: center; min-width: 150px; height: 40px; line-height: 40px; text-decoration: none; background: #2E2F31; border-radius: 100px; font-family: <PERSON><PERSON>, <PERSON><PERSON>, sans-serif; font-style: normal; font-weight: bold; font-size: 15px; text-align: center; color: #FFFFFF; outline: none; box-shadow: none; border: none; padding-left: 20px; padding-right: 20px;">
      <a target="_blank" style="display: inline-block; height: 40px; line-height: 40px; text-decoration: inherit; font-family: inherit; font-style: inherit; font-weight: inherit; font-size: inherit; text-align: center; color: inherit; outline: none; box-shadow: none; border: none; " href="{${
        config?.link || "Ссылка"
      }}">
      ${config?.text || "Пройти опрос"}
     </a></span>`;
}
