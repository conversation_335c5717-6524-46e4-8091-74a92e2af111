export enum UserRoles {
  Superadmin = "foquz_superadmin",
  Admin = "foquz_admin",
  Executor = "foquz_executor",
  Editor = "editor",
  WikiEditor = "wiki_editor",
  FilialEmployee = "filial_employee",
}

export const RoleNames = {
  [UserRoles.Superadmin]: 'Суперадминистратор',
  [UserRoles.Admin]: 'Администратор',
  [UserRoles.Executor]: 'Исполнитель',
  [UserRoles.Editor]: 'Редактор',
  [UserRoles.WikiEditor]: 'Редактор справки',
  [UserRoles.FilialEmployee]: 'Сотрудник филиала',

}