export const RATE_QUESTION = '0'; // оценка
export const VARIANTS_QUESTION = '1'; // варианты ответов
export const TEXT_QUESTION = '2'; // текстовый ответ
export const DATE_QUESTION = '3'; // дата и время
export const ADDRESS_QUESTION = '4'; // адрес
export const FILE_QUESTION = '5'; // выбор файла
export const QUIZ_QUESTION = '6'; // анкета
export const PRIORITY_QUESTION = '8'; // приоритет
export const MEDIA_VARIANTS_QUESTION = '9'; // выбор изображения/видео
export const GALLERY_QUESTION = '10'; // рейтинг галереи
export const SMILE_QUESTION = '11'; // смайл-рейтинг
export const NPS_QUESTION = '12'; // nps-рейтинг
export const MATRIX_QUESTION = '13'; // простая матрица
export const DIFF_QUESTION = '14'; // семантический дифференциал
export const STARS_QUESTION = '15'; // звездный рейтинг
export const INTER_BLOCK = '16'; // промежуточный блок
export const SCALE_QUESTION = '20'; // промежуточный блок

export const types = [
  {
    id: STARS_QUESTION,
    label: 'Звездный рейтинг',
    name: 'stars',
  },

  {
    id: VARIANTS_QUESTION,
    label: 'Варианты ответов',
    name: 'variants',
  },
  {
    id: TEXT_QUESTION,
    label: 'Текстовый ответ',
    name: 'text',
  },
  {
    id: DATE_QUESTION,
    label: 'Дата/время',
    name: 'date',
  },
  {
    id: ADDRESS_QUESTION,
    label: 'Адрес',
    name: 'address',
  },
  {
    id: FILE_QUESTION,
    label: 'Загрузка файла',
    name: 'file',
  },
  {
    id: QUIZ_QUESTION,
    label: 'Анкета',
    name: 'quiz',
  },
  {
    id: PRIORITY_QUESTION,
    label: 'Приоритет',
    name: 'priority',
  },
  {
    id: MEDIA_VARIANTS_QUESTION,
    label: 'Выбор изображения/видео',
    name: 'media-variants',
  },
  {
    id: GALLERY_QUESTION,
    label: 'Рейтинг фото/видео галереи',
    name: 'gallery',
  },
  {
    id: SMILE_QUESTION,
    label: 'Смайл-рейтинг',
    name: 'smile',
  },
  {
    id: NPS_QUESTION,
    label: 'Рейтинг NPS',
    name: 'nps',
  },
  {
    id: MATRIX_QUESTION,
    label: 'Простая матрица',
    name: 'matrix',
  },
  {
    id: DIFF_QUESTION,
    label: 'Семантический дифференциал',
    name: 'diff',
  },
  {
    id: RATE_QUESTION,
    label: 'Оценка',
    name: 'rate',
  },
  {
    id: SCALE_QUESTION,
    label: 'Шкала',
    name: 'scale',
  },

  {
    id: INTER_BLOCK,
    label: 'Промежуточный блок',
    name: 'inter',
  },
];
