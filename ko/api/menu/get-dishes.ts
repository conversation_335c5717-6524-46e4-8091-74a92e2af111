import { DishesCategory } from "@/entities/structures/dishes/types";
import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";

interface DishesResponse {
  items: DishesCategory[];
}

export async function getDishesRequest(): Promise<DishesCategory[]> {
  const { data, error } = await request<DishesResponse>(
    "/foquz/api/dish-categories",
    {
      method: "GET",
    }
  );
  if (error) throw new Error(error);

  return data.items;
}

const [cachedRequestFn] = useApiCache<DishesCategory[]>(getDishesRequest);

export async function getDishes(): Promise<DishesCategory[]> {
  return cachedRequestFn();
}
