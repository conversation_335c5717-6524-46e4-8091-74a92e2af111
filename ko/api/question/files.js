import { request } from "@/utils/api/request";

export function uploadLangGalleryImage(lang, id, file) {
  const fd = new FormData();
  fd.append("UploadForm[file]", file);

  return request("/foquz/api/questions/image-upload", {
    method: "POST",
    body: fd,
    params: {
      fileId: id,
      langId: lang,
    },
  });
}

export function uploadLangGalleryVideo(lang, id, file) {
  const fd = new FormData();
  fd.append("VideoUploadForm[file]", file);

  return request("/foquz/api/questions/video-upload", {
    method: "POST",
    body: fd,
    params: {
      fileId: id,
      langId: lang,
    },
  });
}

export function uploadLangInterscreenImage(lang, id, file) {
  const fd = new FormData();
  fd.append("UploadForm[file]", file);

  return request("/foquz/api/questions/image-upload", {
    method: "POST",
    body: fd,
    params: {
      logoId: id,
      langId: lang,
    },
  });
}
