import { DiscountPool } from "@/entities/structures/discount-poll/types";
import { DishesCategory } from "@/entities/structures/dishes/types";
import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";

interface DiscountPoolResponse {
  items: DiscountPool[];
}

export async function getDiscountPoolsRequest(): Promise<DiscountPool[]> {
  const { data, error } = await request<DiscountPoolResponse>(
    "/foquz/api/discount-pool",
    {
      method: "GET",
      params: {
        all: 1
      }
    }
  );
  if (error) throw new Error(error);

  return data.items;
}

const [cachedRequestFn] = useApiCache<DiscountPool[]>(getDiscountPoolsRequest);

export async function getDiscountPools(): Promise<DiscountPool[]> {
  return cachedRequestFn();
}
