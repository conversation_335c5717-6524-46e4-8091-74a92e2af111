import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";
import { ClientTag, ClientTagsList } from "./tags.types";

async function getClientTagsRequest(): Promise<ClientTag[]> {
  const { data, error } = await request<{ items: ClientTag[] }>(
    "/foquz/api/poll/tags",
    {
      params: {
        all: 1,
      },
    }
  );

  if (error) throw new Error(error);

  return data.items;
}

const [cachedRequestFn] = useApiCache<ClientTag[]>(getClientTagsRequest);

export async function getClientTags(): Promise<ClientTagsList> {
  const data = await cachedRequestFn();
  const tagsWithConditions = data.filter(
    (tag) => tag.conditions && tag.conditions.length
  );
  const tagsWithoutCondition = data.filter(
    (tag) => !tag.conditions || !tag.conditions.length
  );
  return {
    withConditions: tagsWithConditions,
    withoutConditions: tagsWithoutCondition,
  };
}
