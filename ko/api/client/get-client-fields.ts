import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";

export type ClientField = {
  id: string;
  text: string;
};

export type ClientFieldsList = {
  additional: ClientField[];
  system: ClientField[];
};


async function getClientFieldsRequest(): Promise<ClientFieldsList> {
  const { data, error } = await request<ClientFieldsList>(
    "/foquz/foquz-question/contact-fields"
  );

  if (error) throw new Error(error);

  return data;
}

const [cachedRequestFn] = useApiCache<ClientFieldsList>(getClientFieldsRequest);

export async function getClientFields(): Promise<ClientFieldsList> {
  const data = await cachedRequestFn();
  return data;
}
