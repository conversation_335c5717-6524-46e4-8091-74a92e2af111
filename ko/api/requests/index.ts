import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";

export type ClientField = {
  id: string;
  text: string;
};

export type ClientFieldsList = {
  additional: ClientField[];
  system: ClientField[];
};


async function getRequestsRequest(): Promise<ClientFieldsList> {
  const { data, error } = await request<ClientFieldsList>(
    "/foquz/api/requests-projects/themes"
  );

  if (error) throw new Error(error);

  return data;
}

const [cachedRequestFn] = useApiCache<ClientFieldsList>(getRequestsRequest);

export async function getRequests(): Promise<ClientFieldsList> {
  const data = await cachedRequestFn();
  return data;
}
