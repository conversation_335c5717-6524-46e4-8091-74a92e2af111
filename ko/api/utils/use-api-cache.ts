type RequestFn<T> = (...params: any[]) => Promise<T>;
type CachedRequestFn<T> = (...params: any[]) => Promise<T>;


const CACHE = new Map();

export function useApiCache<T>(requestFn: RequestFn<T>): [CachedRequestFn<T>, () => void] {
  const cachedRequestFn = (...params: any[]): Promise<T> => {
    if (CACHE.has(requestFn)) {
      return CACHE.get(requestFn);
    }

    const request = requestFn(...params);
    CACHE.set(requestFn, request);

    return request;
  };

  const clearCache = () => {
    CACHE.delete(requestFn);
  }

  return [cachedRequestFn, clearCache]
}
