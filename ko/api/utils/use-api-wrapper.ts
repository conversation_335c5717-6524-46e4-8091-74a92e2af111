type UnpackApiFnResult<T> = T extends (
  ...params: any[]
) => Promise<infer K>
  ? K
  : any;
type UnpackHandlerFnResult<T> = T extends (data: any) => infer K
  ? K
  : any;
type UnpackData<T, K> = K extends (...params: any[]) => any ? UnpackHandlerFnResult<K> : UnpackApiFnResult<T>

const { observable } = ko;

export function useApiWrapper<T, K>(apiFn: T, handler?: K) {
  const loading = observable(true);
  const error = observable(null);
  const originalData = observable<UnpackApiFnResult<T> | null>(null);
  const data = observable<UnpackData<T, K> | null>(null);

  return (...params: any[]) => {
    if (typeof apiFn === "function") {
      apiFn(...params)
        .then((result) => {
          originalData(result);
          if (typeof handler === "function") {
            data(handler(result));
          } else {
            data(result);
          }
        })
        .catch((err) => {
          error(err);
        })
        .finally(() => loading(false));
    }

    return { loading, error, data, originalData };
  };
}
