import { QuestionTypes } from "@/constants/question/questionTypes";
import { ShortPollQuestionModel } from "@/entities/structures/short-poll-question";
import {
  ShortPollQuestion,
  ShortPollQuestionVars,
} from "@/entities/structures/short-poll-question/types";
import { request } from "@/utils/api/request";

type PollQuestionsResponseVars = {
  pollName: string;
  items: Array<ShortPollQuestionVars>;
};

type PollQuestionsResponse = {
  pollName: string;
  items: Array<ShortPollQuestion>;
};

export function getPollQuestions(
  pollId: string
): Promise<PollQuestionsResponse> {
  return request<PollQuestionsResponseVars>("/foquz/api/poll/get-questions", {
    params: {
      id: pollId,
    },
  }).then((response) => {
    const { pollName, items } = response.data;
    return {
      pollName,
      items: items.map((item) => ShortPollQuestionModel(item)),
    };
  });
}
