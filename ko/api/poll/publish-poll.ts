import { request } from "@/utils/api/request";
enum PollStatuses {
  Stopped = 0,
  Active = 1,
}

async function pollStatusRequest(pollId: number, status: PollStatuses) {
  const { data, error } = await request("/foquz/api/poll/change-active", {
    method: "POST",
    params: {
      id: pollId,
    },
    body: {
      status,
    },
  });

  if (error) throw new Error(error);

  return data;
}

export function stopPoll(pollId: number) {
  return pollStatusRequest(pollId, PollStatuses.Stopped);
}

export function startPoll(pollId: number) {
  return pollStatusRequest(pollId, PollStatuses.Active);
}
