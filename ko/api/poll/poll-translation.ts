import { request } from "@/utils/api/request";

export function getPollTranslation(pollId: string, langId: string) {
  return request("/foquz/api/poll/get-translate", {
    params: {
      id: pollId,
      langId,
    },
  });
}

export function savePollTranslation(pollId: string, langId: string, data: any) {
  return request("/foquz/api/poll/translate", {
    method: 'POST',
    params: {
      id: pollId,
      langId,
    },
    body: data
  });
}
