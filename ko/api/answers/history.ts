import { FeedbackProcessingModel } from "@/entities/models/processing/feedback";
import { ProcessingModel } from "@/entities/models/processing/poll";
import {
  Processing,
  ProcessingVars,
} from "@/entities/models/processing/types";
import { RequestProcessingModel } from "@/entities/models/processing/request";
import { request } from "@/utils/api/request";

export async function getProcessingHistory(
  answerId: string,
  mode: string
): Promise<Array<Processing>> {
  const { data, error } = await request<Array<ProcessingVars>>(
    "/foquz/api/answer-processing/history",
    {
      params: {
        id: answerId,
        view: mode,
      },
    }
  );

  if (error) throw new Error(error);

  const models = {
    request: RequestProcessingModel,
    feedback: FeedbackProcessingModel,
  };

  const model = models[mode] || ProcessingModel;

  return data.map((item) => model(item));
}