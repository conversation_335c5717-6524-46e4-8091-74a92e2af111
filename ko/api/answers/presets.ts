import { AnswersPreset } from "@/entities/models/presets/answers.preset";
import { request } from "@/utils/api/request";
import { MultipleError } from "@/utils/error/multiple-error";

type PresetUser = {
  id: number;
  username: string;
};

// создать пресет
export async function createPreset(
  name: string,
  presetData: any
): Promise<AnswersPreset> {
  const { data, error } = await request<{ data: AnswersPreset }>(
    "/foquz/api/v1/company-filter/create",
    {
      method: "POST",
      body: {
        name,
        settings: JSON.stringify(presetData),
      },
    }
  );

  if (error) throw new Error(error);

  return data.data;
}

export async function updatePreset(
  id: number,
  name: string,
  presetData: any
): Promise<AnswersPreset> {
  const { data, error } = await request<{
    data?: AnswersPreset;
    error?: {
      [key: string]: string[];
    };
  }>("/foquz/api/v1/company-filter/update", {
    method: "POST",
    params: {
      id,
    },
    body: {
      name,
      settings: JSON.stringify(presetData),
    },
  });

  if (data?.error) {
    throw new MultipleError(
      Object.values(data.error).reduce(
        (list, errors) => [...list, ...errors],
        []
      )
    );
  }
  if (error) throw new Error(error);

  return data.data;
}

export async function deletePreset(id: number): Promise<boolean> {
  const { data, error } = await request("/foquz/api/v1/company-filter/delete", {
    method: "DELETE",
    params: {
      id,
    },
  });

  if (error) throw new Error(error);

  return true;
}

export async function getPresetSettings(id: number): Promise<any> {
  const { data, error } = await request<{ data: string }>(
    "/foquz/api/v1/company-filter/json",
    {
      params: {
        id,
      },
    }
  );

  if (error) throw new Error(error);

  return JSON.parse(data.data);
}

export async function getPresetInfo(id: number): Promise<{
  preset: AnswersPreset;
  users: PresetUser[];
}> {
  const { data, error } = await request<{
    data: {
      preset: AnswersPreset;
      users: PresetUser[];
    };
  }>("/foquz/api/v1/company-filter/info", {
    params: {
      id,
    },
  });

  if (error) throw new Error(error);

  return data.data;
}

interface PresetsListRequestParams {
  "per-page"?: string | number;
  page?: number;
  name?: string;
  updated?: string;
  author?: string;
  sort?: string;
}

type MetaData = {
  totalCount: number;
  pageCount: number;
  currentPage: number;
  perPage: number;
};

export async function getPresetsListRequest(
  params: PresetsListRequestParams
): Promise<{ items: AnswersPreset[]; _meta: MetaData }> {
  const { data, error } = await request<{
    data:
      | AnswersPreset[]
      | {
          items: AnswersPreset[];
          _meta: MetaData;
        };
  }>("/foquz/api/v1/company-filter/list", {
    params,
  });

  if (error) throw new Error(error);

  const items = "items" in data.data ? data.data.items : data.data;
  const _meta = "_meta" in data.data ? data.data._meta : null;

  return { items, _meta };
}

export async function getPresetsList(): Promise<AnswersPreset[]> {
  return getPresetsListRequest({ "per-page": "all" }).then((res) => res.items);
}

export async function sharePreset(
  presetId: number,
  userIds: Array<string>,
  unlinkUserIds: Array<string>
): Promise<{
  share: {
    error: number[];
    success: number[];
  };
  unlink: {
    error: number[];
    success: number[];
  };
}> {
  const { data, error } = await request<{
    data: {
      share: {
        error: number[];
        success: number[];
      };
      unlink: {
        error: number[];
        success: number[];
      };
    };
  }>("/foquz/api/v1/company-filter/share", {
    method: "PUT",
    params: {
      id: presetId,
    },
    body: {
      share_to: userIds || [],
      unlink: unlinkUserIds || [],
    },
  });

  if (error) throw new Error(error);

  return data.data;
}
