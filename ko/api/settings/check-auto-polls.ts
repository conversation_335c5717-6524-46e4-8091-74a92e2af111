import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";

type IsAutoPollsEnabledRequestParams = {
  auto_poll_enabled: 1 | 0;
};

export async function isAutoPollsEnabledRequest(): Promise<boolean> {
  const { data, error } = await request<IsAutoPollsEnabledRequestParams>(
    "/foquz/api/settings/check-auto-poll-enabled"
  );

  if (error) throw new Error(error);

  return data.auto_poll_enabled === 1;
}

const [cachedRequestFn] = useApiCache<boolean>(isAutoPollsEnabledRequest);

export async function isAutoPollsEnabled(): Promise<boolean> {
  return cachedRequestFn();
}
