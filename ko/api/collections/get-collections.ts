import { CompanyCollectionModel } from "@/entities/structures/company-collection";
import {
  CompanyCollection,
  CompanyCollectionVars,
} from "@/entities/structures/company-collection/types";

import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";

export async function getCompanyCollectionsRequest(params?: any): Promise<
  CompanyCollection[]
> {
  let paramsString = '';
  if (params) {
    paramsString = `?${new URLSearchParams(params).toString()}`;
  }
  const { data, error } = await request<CompanyCollectionVars[]>(
    `/foquz/api/settings/dictionaries${paramsString}`
  );
  if (error) throw new Error(error);

  return data.map((collection) => CompanyCollectionModel(collection));
}

const [cachedRequestFn, clearCache] = useApiCache<CompanyCollection[]>(
  getCompanyCollectionsRequest
);

export async function getCompanyCollections(
  force?: boolean,
  params?: any,
): Promise<CompanyCollection[]> {
  if (force) clearCache();
  return cachedRequestFn(params);
}
