import {
  CompanyCollectionItem,
  CompanyCollectionItemTypes,
  CompanyCollectionItemVars,
  CompanyCollectionVars,
} from "@/entities/structures/company-collection/types";
import { request } from "@/utils/api/request";
import { CompanyCollectionItemModel } from "../../entities/structures/company-collection/item";

type CollectionElementRequestParams = {
  collectionId?: string;
  id?: string | null;
  type: CompanyCollectionItemTypes;
  name: string;
  description: string;
  parentId: string;
};

type CollectionElementActionResponse = {
  dictionary_element: CompanyCollectionItemVars;
};

type CopyCollectionResponse = {
  success: boolean;
  newCategory: CompanyCollectionItemVars;
};

export async function collectionElementAction(
  elementData: CollectionElementRequestParams
): Promise<CompanyCollectionItem> {
  const { collectionId, id, type, name, description, parentId } = elementData;

  const url = id
    ? "/foquz/api/dictionaries/elements/update"
    : "/foquz/api/dictionaries/elements/create";

  const params = id
    ? {
        id,
      }
    : {
        dictionary_id: collectionId,
      };

  const { data, error } = await request<CollectionElementActionResponse>(url, {
    method: "POST",
    params: params,
    body: {
      title: name,
      description,
      type,
      parent_id: parentId || "0",
    },
  });

  if (error) throw new Error(error);

  return CompanyCollectionItemModel(data.dictionary_element);
}

export async function removeCollectionElement(id: string) {
  const { data, error } = await request<CollectionElementActionResponse>(
    "/foquz/api/dictionaries/elements/delete",
    {
      params: { id },
    }
  );

  if (error) throw new Error(error);

  return true;
}

export async function reorderCollectionElements(order: {
  [id: string]: {
    position: number;
    parent: string;
  };
}) {
  const { data, error } = await request(
    "/foquz/api/dictionaries/elements/resort",
    {
      method: "POST",
      body: {
        element: order,
      },
    }
  );

  if (error) throw new Error(error);

  return true;
}

export async function copyCollectionElement(id: string, position: string, title: string): Promise<CompanyCollectionItem> {
  const { data, error } = await request<CopyCollectionResponse>(
    "/foquz/api/dictionaries/dictionary/copy",
    {
      method: "POST",
      params: {
        id,
        position,
      },
      body: {
        title,
      },
    },
  );

  if (error || !data.success) throw new Error(error || data);

  return CompanyCollectionItemModel(data.newCategory);
}
