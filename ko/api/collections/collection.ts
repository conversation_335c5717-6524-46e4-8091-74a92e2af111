import {
  CompanyCollection,
  CompanyCollectionVars,
} from "@/entities/structures/company-collection/types";
import { ServerDateTime } from "@/types";
import { request } from "@/utils/api/request";
import {
  CompanyCollectionItem,
  CompanyCollectionItemVars,
} from "@/entities/structures/company-collection/types";
import { CompanyCollectionModel } from "@/entities/structures/company-collection";
import { CompanyCollectionItemModel } from "@/entities/structures/company-collection/item";

export interface CollectionRequestParams {
  id?: string;
  name: string;
  description: string;
}

type CollectionActionResponse = {
  dictionaries: {
    company_id: number;
    title: string;
    description: string;
    id: number;
    is_active: 0 | 1;
    deleted: 0 | 1;
    created_at: ServerDateTime;
    updated_at: ServerDateTime;
  };
};

type GetCollectionResponse = {
  dictionary: CompanyCollectionVars & {
    elements: CompanyCollectionItemVars[];
  };
};

// TODO
export async function collectionAction(
  collectionData: CollectionRequestParams
): Promise<Partial<CompanyCollection>> {
  const url = collectionData.id
    ? "/foquz/api/dictionaries/dictionary/update"
    : "/foquz/api/dictionaries/dictionary/create";

  const params = collectionData.id
    ? {
        id: collectionData.id,
      }
    : {};

  const { data, error } = await request<CollectionActionResponse>(url, {
    method: "POST",
    params,
    body: {
      title: collectionData.name,
      description: collectionData.description,
    },
  });

  if (error) throw new Error(error);

  const { dictionaries } = data;
  const { id, title, description } = dictionaries;

  return {
    id: `${id}`,
    name: title,
    description,
  };
}

export async function toggleCollectionActivity(
  collectionId: string,
  isActive: boolean
) {
  const { data, error } = await request(
    "/foquz/api/dictionaries/dictionary/active",
    {
      method: "POST",
      params: { id: collectionId },
      body: { active: isActive ? 1 : 0 },
    }
  );

  if (error) throw new Error(error);

  return true;
}

export async function removeCollection(id: string) {
  const { data, error } = await request(
    "/foquz/api/dictionaries/dictionary/delete",
    {
      params: {
        id,
      },
    }
  );

  if (error) throw new Error(error);

  return true;
}

const COLLECTIONS_CACHE = {};

export async function getCollectionRequest(
  collectionId: string,
  companyId?: number
) {
  return request<GetCollectionResponse>("/foquz/api/dictionaries/dictionary", {
    params: {
      id: collectionId,
      company_id: companyId,
    },
  });
}

let unique = 1;
function getItem(category = true, children = []): CompanyCollectionItem {
  const id = unique++;
  return {
    id: `${id}`,
    name: `Element ${id}`,
    description: "",
    category,
    parentId: null,
    position: 1,
    deleted: false,
    children,
  };
}

function getItems(
  categoriesCount,
  subcategoriesCount,
  itemsCount
): CompanyCollectionItem[] {
  const categories = [];

  for (let i = 0; i < categoriesCount; i++) {
    const subcategories = [];

    for (let j = 0; j < subcategoriesCount; j++) {
      const children = Array(itemsCount)
        .fill(null)
        .map(() => getItem(false, []));

      const subcategory = getItem(true, children);
      children.forEach((item) => (item.parentId = subcategory.id));
      subcategories.push(subcategory);
    }

    const category = getItem(true, subcategories);
    subcategories.forEach((item) => (item.parentId = category.id));
    categories.push(category);
  }

  return categories;
}

const collection = {
  id: 25,
  name: "Custom Collection 1",
  count: 3,
  description:
    "custom collection description custom collection description custom collection description custom collection description custom collection description",
  system: 0,
  used: 0,
  is_active: 1,
};
const _elements = getItems(10, 10, 10)

export async function getCollection(
  collectionId: string,
  companyId?: number
): Promise<{
  collection: CompanyCollection;
  elements: CompanyCollectionItem[];
}> {
 // return { collection, elements: _elements }

  const { data, error } = await getCollectionRequest(collectionId, companyId);

  if (error) throw new Error(error);

  const { elements, ...fields } = data.dictionary;

  const collectionData = {
    collection: CompanyCollectionModel(fields),
    elements: elements.map(CompanyCollectionItemModel),
  };

  console.log(collectionData);

  return collectionData;
}

export async function getCachedCollection(
  collectionId: string,
  companyId?: number
): Promise<{
  collection: CompanyCollection;
  elements: CompanyCollectionItem[];
}> {
  //return { collection, elements: _elements }

  if (!COLLECTIONS_CACHE[collectionId]) {
    COLLECTIONS_CACHE[collectionId] = getCollectionRequest(
      collectionId,
      companyId
    );
  }

  const { data, error } = await COLLECTIONS_CACHE[collectionId];

  if (error) throw new Error(error);

  const { elements, ...fields } = data.dictionary;

  const collectionData = {
    collection: CompanyCollectionModel(fields),
    elements: elements.map(CompanyCollectionItemModel),
  };

  return collectionData;
}
