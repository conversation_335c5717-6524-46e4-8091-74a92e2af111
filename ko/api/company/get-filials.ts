import {
  FilialModel,
  FilialsCategoryModel,
} from "@/entities/structures/filial";
import { Filial } from "@/entities/structures/filial/types";
import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";
import { getCurrentCompany } from "./index";
import { CompanyFilialsItem, CompanyFilialsItemVars, CompanyFilialsResponse } from "./types";

export async function getCompanyFilialsRequest(userId): Promise<
  CompanyFilialsItem[]
> {
  const currentCompany = getCurrentCompany();

  const { data, error } = await request<CompanyFilialsResponse>(
    `/foquz/api/filials${userId ? '?userID=' + userId : ''}`,
    {
      method: "GET",
      params: currentCompany ? { company: currentCompany.id } : {},
    }
  );
  if (error) throw new Error(error);

  const categories = data.items.map((item: CompanyFilialsItemVars) => {
    const { category, items } = item;

    const filials = items.map((filial) => FilialModel(filial));
    filials.sort((a, b) => (a.name < b.name ? -1 : 1));

    return {
      category: FilialsCategoryModel(category),
      items: filials,
    };
  });

  categories.sort((a, b) => {
    if (a.category.id.toString() === "0") return 1;
    if (b.category.id.toString() === "0") return -1;
    return a.category.name < b.category.name ? -1 : 1;
  });

  return categories;
}

const [cachedRequestFn] = useApiCache<CompanyFilialsItem[]>(
  getCompanyFilialsRequest
);

export async function getCompanyFilials(params,userId): Promise<CompanyFilialsItem[]> {
  return cachedRequestFn(userId);
}

export async function getCompanyFilialsList(): Promise<Filial[]> {
  const filials = await cachedRequestFn();
  let list = [];
  filials.forEach((item) => {
    list = [...list, ...item.items];
  });
  return list;
}
