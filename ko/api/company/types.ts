import {
  Filial,
  FilialsCategory,
  FilialsCategoryVars,
  FilialVars,
} from "@/entities/structures/filial/types";

export type CompanyFilialsItemVars = {
  category: FilialsCategoryVars;
  items: Array<FilialVars>;
};

export type CompanyFilialsItem = {
  category: FilialsCategory;
  items: Array<Filial>;
};

export type CompanyFilialsResponse = {
  allFilials: boolean;
  items: Array<CompanyFilialsItemVars>;
};
