import { request } from "@/utils/api/request";
import { useApiCache } from "../utils/use-api-cache";
import { UserModel } from "@/entities/models/user";
import { User, UserData } from "@/entities/models/user/types";

export async function getUsersListRequest(): Promise<User[]> {
  const { data, error } = await request<{ data: UserData[] }>(
    "/foquz/api/v1/company/user-list"
  );
  if (error) throw new Error(error);
  return data.data.map(UserModel);
}

const [cachedRequestFn] = useApiCache<User[]>(getUsersListRequest);

export async function getUsersList(): Promise<User[]> {
  return cachedRequestFn();
}
