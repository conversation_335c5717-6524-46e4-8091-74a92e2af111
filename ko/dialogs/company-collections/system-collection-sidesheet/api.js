import { ApiUrl } from "@/utils/url/api-url";
import axios from "axios";

export async function getCategories(collectionId) {
  if (collectionId === "filials") {
    const { data } = await axios.get(ApiUrl("filial-category/index"));
    return data;
  }
}

export async function createCategory(collectionId, categoryData) {
  if (collectionId === "filials") {
    const { data } = await axios.post(
      ApiUrl("filial-category/create"),
      categoryData
    );
    return data;
  }
}

export async function updateCategory(collectionId, categoryData) {
  const { id, ...categoryFields } = categoryData;
  if (collectionId === "filials") {
    const { data } = await axios.put(
      ApiUrl("filial-category/update", { id }),
      categoryFields
    );
    return data;
  }
}

export async function deleteCategory(collectionId, categoryId) {
  if (collectionId === "filials") {
    const { data } = await axios.delete(
      ApiUrl("filial-category/delete", { id: categoryId })
    );
    return data;
  }
}
