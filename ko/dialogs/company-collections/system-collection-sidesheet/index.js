import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";

import "Components/collection-table";
import "./collection-item-dialog";

ko.components.register("system-collection-sidesheet", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("collection-sidesheet");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
