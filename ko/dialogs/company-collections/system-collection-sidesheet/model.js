import { DialogWrapper } from "@/dialogs/wrapper";
import { CollectionItemsTableFactory } from "./items";
import { ApiUrl } from "@/utils/url/api-url";
import { DialogsModule } from "@/utils/dialogs-module";
import { collections as systemCollections } from "Data/collections";
import { Collection as SystemCollection } from "Models/collection";

import * as CollectionCategoriesDialog from "./collection-categories-dialog";
import { registerComponent } from "@/utils/engine/register-component";
import { getCsvLoadInfoText } from "../getCsvLoadInfoText.js";

registerComponent("d-collection-categories", CollectionCategoriesDialog);

const tableComponents = {
  fines: "fines-collection-table",
  filials: "filials-collection-table",
  themes: "themes-collection-table",
  default: "collection-table",
};

const itemModalComponents = {
  fines: "fines-collection-item-modal",
  filials: "filials-collection-item-modal",
  themes: "themes-collection-item-modal",
  default: "collection-item-modal",
};

let systemCollectionModels = systemCollections.map(
  (collectionData) => new SystemCollection(collectionData)
);

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);

    const systemCollection = systemCollectionModels.find(
      (collection) => collection.serverId === params.collection.id
    );

    this.collection = systemCollection;
    systemCollection.count(params.collection.count);

    this.collectionId = this.collection.serverId;

    this.isEmpty = ko.observable(this.collection.count() == 0);

    this.collectionItems = CollectionItemsTableFactory(this.collection);

    this.sidesheetContainer = params.sidesheetContainer || DialogsModule({});

    this.loading = ko.observable(false);
  }

  get collectionModalComponent() {
    return (
      itemModalComponents[this.collection.serverId] ||
      itemModalComponents.default
    );
  }

  get collectionTableComponent() {
    return tableComponents[this.collection.serverId] || tableComponents.default;
  }

  _openItemModal(params) {
    if (this.collectionId === "filials") {
      this.sidesheetContainer.openSidesheet({
        name: this.collectionModalComponent,
        params,
      });
    } else {
      this.openDialog({
        name: this.collectionModalComponent,
        params,
      });
    }
  }

  addItem() {
    this._openItemModal({
      collection: this.collection,
      backdrop: true,
      onUpdate: (itemData) => {
        this.collectionItems.addFirst(itemData);
        this.isEmpty(false);
      },
    });
  }

  editItem(item) {
    this._openItemModal({
      collection: this.collection,
      item: item,
      backdrop: true,
      onUpdate: (itemData) => {
        item.update(itemData);
      },
    });
  }

  addFromCSV() {
    this.openDialog({
      name: "add-from-csv-dialog",
      params: {
        isFilials: true,
        onChange: async(promis) => {
          this.loading(true);
          const { stat } = await promis;
          await this.collectionItems.reset();

          this.info({
            title: 'Загрузка элементов',
            text: getCsvLoadInfoText(stat),
            close: 'Закрыть'
          });
          this.loading(false);
        },
      },
    });
  }

  removeItem(item) {
    this.confirm({
      title: this.collection.texts.removeItemTitle,
      text: this.collection.texts.removeItemMessage,
      confirm: "Удалить",
      cancel: "Отменить",
      mode: "danger",
    }).then(() => {
      let url = ApiUrl(`dictionaries/${this.collection.serverId}/delete`, {
        id: item.id,
      });
      $.ajax({
        url,
        method: "DELETE",
        success: () => {
          this.collectionItems.remove(item);
          if (this.collectionItems.items().length == 0) this.isEmpty(true);
          // TODO обновить кол-во в коллекции
        },
      });
    });
  }

  openCategoryModal() {
    // filials
    this.openDialog({
      name: "d-collection-categories",
      params: {
        collectionId: this.collectionId,
      },
      events: {
        hide: () => {
          this.collectionItems.reset();
        },
      },
    });
  }
}
