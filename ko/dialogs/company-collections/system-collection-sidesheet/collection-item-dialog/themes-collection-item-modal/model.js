import * as Parent from '../model';
import { ApiUrl } from 'Utils/url/api-url';
import { FoquzLoaderWithFile } from 'Models/file-loader/loader-with-file';

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
    this.button = ko.observable('');
    this.color = ko.observable('#3f65f1');
    this.icon = ko.observable('');

    if (this.item) {
      this.button(this.item.button() || '');
      this.color(this.item.color() || '#3f65f1');
      this.icon(this.item.icon() || '');
    }

    this.iconLoading = ko.observable(false);
    this.loader = new FoquzLoaderWithFile(
      (newFile) => {
        return new Promise((res) => {
          this.iconLoading(true);
          if (!newFile) {
            $.ajax({
              url: ApiUrl('dictionaries/themes/upload-icon'),
              method: 'POST',
              data: {
                remove: this.icon()
              },
              success: (response) => {
                this.icon('');
                this.iconLoading(false);
              },
              error: (response) => {
                console.error(response.responseJSON);
                this.iconLoading(false);
              }
            });
          } else {
            var fd = new FormData();

            fd.append('icon', newFile);

            if (this.icon()) {
              fd.append('remove', this.icon());
            }

            $.ajax({
              url: ApiUrl('dictionaries/themes/upload-icon'),
              method: 'POST',
              data: fd,
              contentType: false,
              processData: false,
              success: (response) => {
                this.icon(response.icon);
                this.iconLoading(false);
              },
              error: (response) => {
                console.error(response.responseJSON);
                this.iconLoading(false);
              }
            });
          }
        });
      },
      this.item ? this.item.icon() : '',
      {
        presets: ['imageWithSvg'],
        errors: {
          format: 'Можно загружать файлы форматов: jpg, jpeg, png, gif, svg'
        }
      }
    );
  }

  getParams() {
    let params = {};
    params.theme = this.value();
    params.button_text = this.button();
    params.color = this.color();
    params.icon = this.icon();

    return params;
  }

  setServerErrors(response) {
    let data = response.responseJSON;
    if (data.errors) {
      this.serverError(data.errors.theme);
    }
  }
}
