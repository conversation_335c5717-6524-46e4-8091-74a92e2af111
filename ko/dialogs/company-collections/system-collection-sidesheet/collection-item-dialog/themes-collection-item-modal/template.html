
<foquz-dialog class="collection-item-modal-modal"
             params="ref: modal, dialogWrapper: $component">

  <foquz-dialog-header>
    <!-- ko text: $parent.item ?  $parent.collection.texts.editItem :  $parent.collection.texts.addItem -->
    <!-- /ko -->
  </foquz-dialog-header>


  <div class="foquz-dialog__body">

    <div class="form-group">
      <label class="form-label">Наименование темы</label>
      <foquz-chars-counter params="value: value, max: 255">
        <input class="form-control"
               data-bind="
          textInput: value, attr: {
            placeholder: $parent.collection.texts.itemPlaceholder
          }, css: {
            'is-invalid': $parent.formControlErrorStateMatcher($parent.value)
          }">
      </foquz-chars-counter>

      <validation-feedback params="text: value.error, show: formControlErrorStateMatcher(value)"></validation-feedback>
    </div>

    <div class="form-group">
      <label class="form-label d-flex justify-content-between align-items-center">
        <div class="">
          Текст кнопки отправки сообщения
        </div>
        <div class="f-color-service font-weight-normal f-fs-1">необязательное</div>
      </label>
      <foquz-chars-counter params="value: button, max: 60">
        <input class="form-control"
               data-bind="
          textInput: $parent.button"
               placeholder="Отправить">
      </foquz-chars-counter>
    </div>

    <div class="form-group">
      <label class="form-label">Цвет заголовка и кнопки отправки</label>
      <color-picker params="value: color"></color-picker>
    </div>





    <div>
      <label class="form-label">
        Иконка
        <question-button params="text: 'Иконка'"></question-button>
      </label>
      <div class="d-flex align-items-center">
        <media-load-button params="loader: loader"
                           class="media-load-button--transparent">

          <svg-icon params="name: 'clip'"></svg-icon>
          <div class="mt-10p">
            .jpg .png .svg
            <br>
            &lt;5 Мб
          </div>

        </media-load-button>

      </div>
      <file-loader-error params="error: loader.error"></file-loader-error>
    </div>


  </div>

  <footer class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link"
              data-bind="click: function() { $dialog.hide('close'); }">
        Отменить
      </button>

      <button type="button"
              class="f-btn f-btn-base f-btn-success"
              data-bind="click: submit, disable: pending">
        Сохранить
      </button>

    </div>
  </footer>
 

</foquz-dialog>


