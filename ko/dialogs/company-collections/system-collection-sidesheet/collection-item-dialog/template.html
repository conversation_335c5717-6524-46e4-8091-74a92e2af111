<foquz-dialog
  class="collection-item-modal-modal"
  params="ref: modal, dialogWrapper: $component"
>
  <foquz-dialog-header>
    <!-- ko text:  $parent.item ?  $parent.collection.texts.editItem :  $parent.collection.texts.addItem -->
    <!-- /ko -->
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <foquz-chars-counter params="value: value, max: 150">
        <input
          class="form-control"
          data-bind="
          textInput: value, attr: {
            placeholder: $parent.collection.texts.itemPlaceholder
          }, css: {
            'is-invalid': $parent.formControlErrorStateMatcher($parent.value)
          }"
        />
      </foquz-chars-counter>

      <!-- ko template: {
          foreach: formControlErrorStateMatcher(value),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
      <div class="form-error" data-bind="text: $parent.value.error()"></div>
      <!-- /ko -->
    </div>
  </div>

  <footer class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="click: function() { $dialog.hide('close'); }"
      >
        Отменить
      </button>

      <button
        type="button"
        class="f-btn f-btn-base f-btn-success"
        data-bind="click: submit, disable: pending"
      >
        Сохранить
      </button>
    </div>
  </footer>
</foquz-dialog>
