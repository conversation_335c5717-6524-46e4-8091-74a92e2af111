<foquz-dialog
  class="collection-item-modal-modal"
  params="ref: modal, dialogWrapper: $component"
>
  <foquz-dialog-header>
    <!-- ko text:  $parent.item ?  $parent.collection.texts.editItem :  $parent.collection.texts.addItem -->
    <!-- /ko -->
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <label class="form-label">Взыскание/нарушение</label>
      <div class="select2-wrapper">
        <select
          data-bind="value: type,
          valueAllowUnset: true,
          select2: {
              containerCssClass: 'form-control',
              wrapperCssClass: 'select2-container--form-control',
              allowClear: true,
          }"
          data-placeholder="Не указан"
        >
          <option></option>
          <option value="1">Взыскание</option>
          <option value="2">Нарушение</option>
        </select>
      </div>
    </div>

    <div class="form-group">
      <label class="form-label">Название</label>
      <foquz-chars-counter params="value: value, max: 150">
        <input
          class="form-control"
          data-bind="
          textInput: value, attr: {
            placeholder: $parent.collection.texts.itemPlaceholder
          }, css: {
            'is-invalid': $parent.formControlErrorStateMatcher($parent.value)
          }"
        />
      </foquz-chars-counter>

      <validation-feedback
        params="text: value.error, show: formControlErrorStateMatcher(value)"
      ></validation-feedback>
    </div>

    <!-- ko template: {
      foreach: templateIf(type(), $data),
    } -->
    <div class="form-group">
      <!-- ko if: type() == '1' -->
      <label class="form-label">Сумма взыскания, ₽</label>
      <!-- /ko -->
      <!-- ko if: type() == '2' -->
      <label class="form-label">Баллы за нарушение</label>
      <!-- /ko -->
      <div class="row">
        <div class="col-6">
          <input
            type="text"
            class="form-control text-center"
            data-bind="onlyNumbers, textInput: amount, css: {
              'is-invalid': formControlErrorStateMatcher(amount)
            }"
            placeholder="0"
          />
        </div>
      </div>

      <validation-feedback
        params="text: amount.error, show: formControlErrorStateMatcher(amount)"
      ></validation-feedback>
    </div>
    <!-- /ko -->
  </div>

  <footer class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="click: function() {$dialog.hide('close'); }"
      >
        Отменить
      </button>

      <button
        type="button"
        class="f-btn f-btn-base f-btn-success"
        data-bind="click: submit, disable: pending"
      >
        Сохранить
      </button>
    </div>
  </footer>
</foquz-dialog>
