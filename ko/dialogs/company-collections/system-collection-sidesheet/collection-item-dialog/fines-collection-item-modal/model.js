import * as Parent from '../model';

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
    this.type = ko.observable('');
    this.amount = ko.observable('').extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.type() == '1'
      }
    });
    if (this.item) {
      this.type(this.item.type() || '');
      let amount = this.item.amount();

      this.amount(amount || amount === 0 ? amount : '');
    }

    this.type.subscribe((v) => {
      this.amount('');
    });
  }


  isValid() {
    return super.isValid() && this.amount.isValid();
  }

  getParams() {
    let params = super.getParams();
    params.hrm_type = this.type();

    if (this.type()) {
      params.amount = this.amount();
    }
    return params;
  }
}
