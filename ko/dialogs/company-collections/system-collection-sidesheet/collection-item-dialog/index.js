import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './fines-collection-item-modal';
import './filials-collection-item-modal';
import './themes-collection-item-modal';

ko.components.register('collection-item-modal', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;

      return new ViewModel(params, element);
    }
  },
  template: html
});
