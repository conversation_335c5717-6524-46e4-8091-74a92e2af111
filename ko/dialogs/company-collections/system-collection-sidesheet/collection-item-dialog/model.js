import { DialogWrapper } from 'Dialogs/wrapper';


export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.pending = ko.observable(false)

    this.collection = params.collection;
    this.item = params.item;
    this.onUpdate = params.onUpdate;

    this.serverError = ko.observable('');

    this.value = ko.observable('').extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: {
        validator: () => !this.serverError(),
        message: () => this.serverError()
      }
    });
    if (this.item) {
      this.value(this.item.name());
    }

    this.subscriptions.push(this.value.subscribe((_) => this.serverError('')));
  }

  isValid() {
    return this.value.isValid();
  }

  getParams() {
    let params = {};
    params[this.collection.titleField] = this.value();
    return params;
  }

  beforeSubmit() {
    this.value(this.value().trim());
  }

  setServerErrors(response) {
    let data = response.responseJSON;
    if (data.errors) {
      this.serverError(data.errors.title);
    }
  }

  submit() {
    this.isSubmitted(true);
    this.beforeSubmit();
    if (!this.isValid()) return;

    this.pending(true);

    let url = this.item
      ? `${APIConfig.baseApiUrlPath}dictionaries/${this.collection.serverId}/update?id=${this.item.id}&access-token=${APIConfig.apiKey}`
      : `${APIConfig.baseApiUrlPath}dictionaries/${this.collection.serverId}/create?access-token=${APIConfig.apiKey}`;
    let method = this.item ? 'PUT' : 'POST';

    let params = this.getParams();

    $.ajax({
      url,
      data: params,
      method,
      success: (data) => {
        let itemData = data[this.collection.itemServerId];
        if (typeof this.onUpdate == 'function') this.onUpdate(itemData);
        this.hide();
      },
      error: (response) => {
        this.setServerErrors(response)
        this.pending(false)
      }
    });
  }
}
