<sidesheet params="ref: modal, dialogWrapper: $component"
  class="filials-collection-item-modal"
>

  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">
        <!-- ko text: item ? collection.texts.editItem : collection.texts.addItem -->
        <!-- /ko -->
      </h2>
      <!-- ko if: !!item?.crm_id && !!item?.crm_id() -->
      <div class="mt-10p f-color-service">
        Внешний ID <span class="bold" data-bind="text: item.crm_id()"></span>
      </div>
      <!-- /ko -->
    </div>
  </div>


  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container">

        <div class="filial-form">
          <div class="row">
            <div class="col-6">
              <div class="form-group">
                <label class="form-label">Название филиала</label>
                <foquz-chars-counter params="value: value, max: 255">
                  <input
                    class="form-control"
                    data-bind="
                    textInput: value, attr: {
                      placeholder: $parent.collection.texts.itemPlaceholder
                    }, css: {
                      'is-invalid': $parent.formControlErrorStateMatcher($parent.value)
                    }"
                  />
                </foquz-chars-counter>
          
                <validation-feedback
                  params="text: value.error, show: formControlErrorStateMatcher(value)"
                ></validation-feedback>
              </div>
            </div>
            <div class="col-6">
              <div class="form-group">
                <label class="form-label">Категория филиала</label>
                <fc-select params="options: getCategories, value: category, clearable: true, placeholder: 'Без категории'"></fc-select>
              </div>
            </div>
            <div class="col-12">
              <div class="form-group">
                <label class="form-label">Адрес</label>
                <foquz-chars-counter params="value: address, max: 500">
                  <input
                    class="form-control"
                    data-bind="
                    textInput: $parent.address"
                  />
                </foquz-chars-counter>
              </div>
            </div>
  
            <div class="col-6">
              <div class="form-group">
                <label class="form-label"
                  >Идентификатор места (locationId) в Google-карте</label
                >
          
                <input
                  class="form-control"
                  data-bind="
                    textInput: googleLocation"
                  maxlength="500"
                />
          
                <div class="f-fs-1 f-color-service mt-2">
                  Для получения отзывов с <span class="bold">Google-карт</span> в сервис <span class="bold">FOQUZ</span>.
                  <a href="https://foquz.ru/foquz/user-wiki?id=72" target="_blank">Ссылка на документацию</a>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="form-group">
                <label class="form-label">Идентификатор места в Яндекс-карте</label>
          
                <input
                  class="form-control"
                  data-bind="
                    textInput: yandexLocation"
                  maxlength="500"
                />
          
                <div class="f-fs-1 f-color-service mt-2">
                  Для получения отзывов с <span class="bold">Yandex-карт</span> в сервис <span class="bold">FOQUZ</span>
                  <a href="https://foquz.ru/foquz/user-wiki?id=72" target="_blank">Ссылка на документацию</a>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="form-group">
                <label class="form-label">
                  Логотип
                  <question-button params="text: 'Логотип'"></question-button>
                </label>
                <div class="d-flex align-items-center">
                  <media-load-button
                    class="media-load-button--logo"
                    params="loader: loader"
                  >
                    <div class="d-flex align-items-center">
                      <svg-icon params="name: 'clip'"></svg-icon>
                      <div class="ml-10p text-left">
                        .jpg .png
                        <br />
                        &lt;5 Мб
                      </div>
                    </div>
                  </media-load-button>
          
                  <div class="f-fs-1 f-color-service ml-3">
                    Для корректного отображения логотипа в виджете обратной связи
                    используйте изображения с пропорциями 2:3
                  </div>
                </div>
                <file-loader-error params="error: loader.error"></file-loader-error>
              </div>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col-8">
              <div class="form-label filial-form-extra-params-title">Дополнительные параметры</div>
              <div class="f-fs-1 f-color-service mt-2 pr-4">
                Значения параметров филиалов можно автоматически подставлять в текст, если анкета привязана к конкретному филиалу. Скопируйте нужную переменную и вставьте в <span class="bold">Текст вопроса, Дополнительное описание</span> или <span class="bold">Текст на странице.</span>
              </div>
            </div>
            
          </div>
          <!-- ko foreach: extraParams -->
          <div class="row mt-4 filial-form-extra-params-row">
            <div class="col-8">
             <div class="d-flex align-items-center">
              <span class="filial-form-extra-params-index" data-bind="text: $index() + 1"></span>
              <fc-textarea
                class="form-control-extra-filial-param"
                params="value: $data.value, maxlength: 2048, height: 'input', placeholder:'Значение параметра'"
              ></fc-textarea>
              <!-- <textarea
                  class="form-control form-control-extra-filial-param"
                  data-bind="
                    textInput: $data.value, attr: {
                      placeholder: 'Значение параметра',
                      rows: $data.rows
                    }"
                  maxlength="2048"
                ></textarea> -->
             </div>
            </div>
            <div class="col-4 d-flex align-items-center form-control-extra-filial-param-var">
              <div class="d-flex align-items-center position-relative">
                <span data-bind="text: $data.name, class: 'copy-filial-param' + $index(), style: { opacity: $data.value().length ? '1' : '0.5' }"></span>
                <fc-button class="copy-btn ml-3"
                  params="mode: 'text', size: 'auto', icon: {name: 'copy', size: 20}, click: function() {
                  $parent.copyParamName($data)
                }"
                  data-bind="fbPopper, title: 'Скопировать в буфер', style: { opacity: $data.value().length ? '1' : '0.5' }"
                ></fc-button>
                <fc-status-popper params="target: 'copy-filial-param' + $index(), show: $data.copied, mode: 'success'">
                  Переменная&nbsp;скопирована
                </fc-status-popper>
              </div>
            </div>
            
          </div>
          <!-- /ko -->
        </div>


        
    
        
    
        
    
        
    
        
      </div>
    </div>


    
  </div>

  <footer class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="click: function() {$dialog.hide('close'); }"
      >
        Отменить
      </button>

      <button
        type="button"
        class="f-btn f-btn-base f-btn-success"
        data-bind="click: submit, disable: pending"
      >
        Сохранить
      </button>
    </div>
  </footer>
</foquz-dialog>
