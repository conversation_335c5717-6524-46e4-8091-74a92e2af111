import * as Parent from "../model";
import { FoquzLoaderWithFile } from "@/models/file-loader/loader-with-file";
import { ApiUrl } from "@/utils/url/api-url";
import { getCategories } from "../../api";
import { copyToClipboard } from "Utils/copy-to-clipboard";
import { HidePoppersEvent } from "@/utils/events/modal";

const extraParam = (index) => {
  const param = {
    name: `{FILIAL.param${index}}`,
    value: ko.observable(""),
    rows: ko.observable(1),
    copied: ko.observable(false)
  }
  param.value.subscribe((v) => {
    const rows = +Math.floor(v.length / 66).toFixed(0) + 1
    param.rows(rows  || 1)
})
  return param
}
export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
    this.category = ko.observable("");
    this.address = ko.observable("");
    this.googleLocation = ko.observable("");
    this.yandexLocation = ko.observable("");

    this.extraParams = ko.observableArray([])

    while (this.extraParams().length !== 3) {
      const index = this.extraParams().length + 1
      const param = extraParam(index)
      this.extraParams.push(param)
    }

    this.logo = ko.observable("");

    if (this.item) {
      this.category(this.item.category() || "");
      this.address(this.item.address());
      this.googleLocation(this.item.googleLocation() || "");
      this.yandexLocation(this.item.yandexLocation() || "");
      this.logo(this.item.logo() || "");
      this.extraParams()[0].value(this.item.param1())
      this.extraParams()[1].value(this.item.param2())
      this.extraParams()[2].value(this.item.param3())
    }

    this.logoLoading = ko.observable(false);
    this.loader = new FoquzLoaderWithFile(
      (newFile) => {
        return new Promise((res) => {
          this.logoLoading(true);
          if (!newFile) {
            $.ajax({
              url: ApiUrl("dictionaries/filials/upload-logo"),
              method: "POST",
              data: {
                remove: this.logo(),
              },
              success: (response) => {
                this.logo("");
                this.logoLoading(false);
              },
              error: (response) => {
                console.error(response.responseJSON);
                this.logoLoading(false);
              },
            });
          } else {
            var fd = new FormData();

            fd.append("logo", newFile);

            if (this.logo()) {
              fd.append("remove", this.logo());
            }

            $.ajax({
              url: ApiUrl("dictionaries/filials/upload-logo"),
              method: "POST",
              data: fd,
              contentType: false,
              processData: false,
              success: (response) => {
                this.logo(response.logo);
                this.logoLoading(false);
              },
              error: (response) => {
                console.error(response.responseJSON);
                this.logoLoading(false);
              },
            });
          }
        });
      },
      this.item ? this.item.logoUrl() : ""
    );
  }

  getCategories(_, cb) {
    getCategories("filials").then((data) =>
      cb(data.map((c) => ({ id: c.id.toString(), text: c.name })))
    );
  }

  copyParamName(data) {
    copyToClipboard(data.name);
    HidePoppersEvent.emit();
    data.copied(true);
  }
  getParams() {
    let params = super.getParams();
    params.address = this.address();

    params.category_id = this.category();

    if (this.googleLocation()) params.google_location = this.googleLocation();
    if (this.yandexLocation()) params.yandex_link = this.yandexLocation();

    params.logo = this.logo();

    params.params = this.extraParams().forEach((i, index) => {
      params[`param${index+1}`] = i.value()
    })

    return params;
  }
}
