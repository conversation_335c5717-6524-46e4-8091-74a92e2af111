import { DialogWrapper } from "Dialogs/wrapper";
import {
  createCategory,
  deleteCategory,
  getCategories,
  updateCategory,
} from "../api";
import { CategoryModel, updateCategoryModel } from "./models/category";

import * as CollectionCategory from "./components/collection-category";
import { registerComponent } from "@/utils/engine/register-component";
import { useFilteredList } from "@/utils/list/useFilteredList";

registerComponent("c-collection-category", CollectionCategory);

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.collectionId = params.collectionId;

    this.categories = ko.observableArray([]);
    this.loading = ko.observable(true);

    this.hasCategories = ko.observable(false);
    this.categories.subscribe((v) => {
      this.hasCategories(v.some(c => c.id));
    });

    const { query, filteredList } = useFilteredList(
      this.categories,
      (item, q) => {
        return item.name().toLowerCase().includes(q);
      }
    );
    this.query = query;
    this.filteredCategories = filteredList;

    this.categoryEvents = {
      edit: (_, e) => {
        const { unique } = e.detail;
        this.editCategory(unique);
      },
      reset: (_, e) => {
        this.editCategory(null);
      },
      update: (_, e) => {
        const { unique, name } = e.detail;
        this.updateCategory(unique, name);
      },
      remove: (_, e) => {
        const { unique } = e.detail;
        this.removeCategory(unique);
      },
    };

    this.editingCategory = ko.observable(null);

    const editingSb = this.editingCategory.subscribe((v) => {
      this.scrollToCategory(v);
    });
    const resetEditingSb = this.editingCategory.subscribe(
      (v) => {
        this.onResetEditing(v);
      },
      null,
      "beforeChange"
    );
    this.subscriptions.push(editingSb);
    this.subscriptions.push(resetEditingSb);

    this.loadCategories().then((data) => {
      this.categories(data);
      this.loading(false);
    });
  }

  getCategory(categoryUnique) {
    return this.categories().find((c) => c.unique === categoryUnique);
  }

  loadCategories() {
    return getCategories(this.collectionId).then((data) => {
      return data.map((category) => {
        const model = CategoryModel(category);
        return model;
      });
    });
  }

  onResetEditing(categoryUnique) {
    if (!categoryUnique) return;

    const category = this.getCategory(categoryUnique);

    if (!category) return;

    if (!category.id) {
      this.categories.remove(category);
    }
  }

  editCategory(categoryUnique) {
    this.editingCategory(categoryUnique);
  }

  updateCategory(categoryUnique, newName) {
    let category = this.getCategory(categoryUnique);
    if (!category) return;

    // optimistic

    if (!category.id) {
      category.id = "tmp";
      category.name(newName);
      this.hasCategories(true);

      createCategory(this.collectionId, { name: newName }).then((response) => {
        updateCategoryModel(category, response);
      });
    } else {
      updateCategory(this.collectionId, {
        id: category.id,
        name: newName,
      }).then((response) => {
        updateCategoryModel(category, response);
      });
    }

    this.editingCategory(null);
  }

  removeCategory(categoryUnique) {
    let category = this.getCategory(categoryUnique);
    if (!category) return;
    // optimistic
    this.categories.remove(category);

    deleteCategory(this.collectionId, category.id).then((response) => {});
  }

  addCategory() {
    this.query("");
    const newCategory = CategoryModel();
    this.categories.push(newCategory);
    this.editCategory(newCategory.unique);
  }

  scrollToCategory(categoryUnique) {}
}
