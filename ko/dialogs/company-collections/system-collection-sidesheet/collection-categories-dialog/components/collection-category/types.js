/**
 * @event CollectionCategory#edit
 * @type {Object}
 * @property {Number} unique
 */

/**
 * @event CollectionCategory#update
 * @type {Object}
 * @property {Number} unique
 * @property {String} name
 */

/**
 * @event CollectionCategory#remove
 * @type {Object}
 * @property {Number} unique
 */

/**
 * @event CollectionCategory#cancel
 * @type {Object}
 * @property {Number} unique
 */

/**
 * @typedef {Object} CollectionCategoryComponentParams 
 * @property {Category} category
 * @property {Observable<Boolean>} editing
 */

/**
 * @typedef {Object} CollectionCategoryComponent
 * @property {Observable<String>} name
 * @property {Number} count 
 * @property {Observable<String>} tmpName 
 * @property {Observable<Boolean>} editing
 * @property {Observable<Boolean>} disableSaveButton
 * @property {Function} cancel
 * @property {Function} edit
 * @property {Function} update
 * @property {Function} remove
 * @property {Function} dispose
 */