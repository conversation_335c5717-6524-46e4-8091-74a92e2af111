<!-- ko template: {
   foreach: templateIf(editing(), $data),
   afterAdd: fadeAfterAddFactory(250),
   beforeRemove: slideBeforeRemoveFactory(250)
} -->

<div class="c-collection-category__form" data-bind="descendantsComplete: onFormRender">
  <input
    type="text"
    class="c-collection-category__input"
    data-bind="textInput: tmpName, fbOnEnter: function() { update() }"
    maxlength="255"
  />
  <div class="c-collection-category__actions">
    <fc-button
      class="c-collection-category__cancel"
      params="shape: 'circle', size: 'sm', icon: { name: 'times', size: 12 }, color: 'danger', click: function() { cancel() }"
      data-bind="tooltip, tooltipText: 'Отменить'"
    ></fc-button>
    <fc-button
      class="c-collection-category__save"
      params="shape: 'circle', size: 'sm', icon: { name: 'check', size: 14 }, color: 'success', 
              click: function() { update() },
              disabled: disableSaveButton"
      data-bind="tooltip, tooltipText: 'Сохранить'"
    ></fc-button>
  </div>
</div>

<!-- /ko -->

<!-- ko template: {
   foreach: templateIf(!editing(), $data),
   afterAdd: fadeAfterAddFactory(200),
   beforeRemove: fadeBeforeRemoveFactory(200)
} -->
<div class="c-collection-category__view">
  <div class="c-collection-category__name">
    <span data-bind="text: name"></span>
    <span class="c-collection-category__count">(<span data-bind="text: count"></span>)</span>
  </div>
  <div class="c-collection-category__actions">
    <fc-button
      class="c-collection-category__edit"
      params="icon: 'pencil', size: 'auto', mode: 'text', click: edit"
      data-bind="tooltip, tooltipText: 'Редактировать'"
    ></fc-button>
    <div class="d-flex align-items-center"
      data-bind="tooltip, tooltipText: count > 0 ? 'Нельзя удалить категорию, в которую добавлены филиалы' : 'Удалить'"
    >
      <fc-button
        class="c-collection-category__remove"
        params=" icon: 'bin', size: 'auto', mode: 'text', click: remove, 
        disabled: count > 0"
      ></fc-button>
    </div>
  </div>
</div>
<!-- /ko -->
