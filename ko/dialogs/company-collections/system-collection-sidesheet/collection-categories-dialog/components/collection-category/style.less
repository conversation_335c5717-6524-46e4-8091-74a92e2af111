@import "@/less/vars/colors.less";
@import "@/less/mixins/form.less";

.c-collection-category {
  display: block;
  border-bottom: 1px solid @border-color-light;
  min-height: 47px;
  position: relative;

  &:first-child {
    border-top: 1px solid @border-color-light;
  }

  &__name {
    font-size: 14px;
    font-weight: 500;
  }

  &__count {
    color: @text-color-service;
  }

  &__view,
  &__form {
    display: flex;
    min-height: 47px;
    align-items: center;
   
  }

  &__view {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  &__form {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 8px;
    padding-bottom: 8px;
  }

  &__input,
  &__name {
    flex-grow: 1;
  }

  &__actions {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  &__edit,
  &__remove {
    width: 34px;
    text-align: center;
  }

  &__edit,
  &__cancel {
    margin-right: 10px;
  }

  &__input {
    .no-input();

    border-bottom: 1px solid #a6b1bc;
    padding-bottom: 8px;
    align-self: flex-end;
    margin-right: 14px;
    font-weight: 500;
  }
}
