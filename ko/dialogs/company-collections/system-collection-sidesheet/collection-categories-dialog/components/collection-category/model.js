import { elementDispatcher } from "@/utils/dispatch-event";

const { observable, toJS, computed } = ko;

/**
 * CollectionCategory Component
 * @param {CollectionCategoryComponentParams} params
 * @param {HTMLElement} element
 * @returns {CollectionCategoryComponent}
 */
export function ViewModel(params, element) {
  element.classList.add("c-collection-category");
  const dispatch = elementDispatcher(element);

  const { category, editing } = params;

  const unique = category.unique;

  const name = category.name;
  const count = category.count;

  const tmpName = observable(toJS(name));

  const disableSaveButton = computed(() => {
    return !tmpName().trim();
  });

  const nameSb = name.subscribe((v) => {
    if (v !== tmpName()) tmpName(v);
  });

  const editingSb = editing.subscribe((v) => {
    if (v) {
      tmpName(toJS(name));
    }
  });

  return {
    name,
    count,
    tmpName,
    editing,

    disableSaveButton,

    /** @fires CollectionCategory#cancel */
    cancel() {
      dispatch("reset", { unique });
    },

    /** @fires CollectionCategory#edit */
    edit() {
      dispatch("edit", { unique });
    },

    /** @fires CollectionCategory#update */
    update() {
      dispatch("update", { unique, name: tmpName() });
    },

    /** @fires CollectionCategory#remove */
    remove() {
      dispatch("remove", { unique });
    },

    dispose() {
      nameSb.dispose();
      editingSb.dispose();
    },

    onFormRender() {
      setTimeout(() => {
        element.scrollIntoView();
        const input = element.querySelector("input");
        input.focus();
      });
    },
  };
}
