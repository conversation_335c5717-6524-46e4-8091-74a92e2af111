<foquz-dialog
  class="collection-categories-modal"
  params="ref: modal, dialogWrapper: $component"
>
  <foquz-dialog-header>Редактировать категории</foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="collection-categories">
      <!-- ko template: {
         foreach: templateIf(hasCategories(), $data),
         afterAdd: slideAfterAddFactory(400),
         beforeRemove: slideBeforeRemoveFactory(400)
      } -->
      <div class="collection-categories__search">
        <fc-input
          params="value: query, icon: 'search', placeholder: 'Поиск по названию', clearable: true"
        ></fc-input>
      </div>

      <!-- /ko -->

      <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
        <div class="collection-categories__list">
          <!-- ko ifnot: filteredCategories().length -->
          <!-- ko if: query -->
          <div class="collection-categories__empty">
            Ничего не найдено. Измените параметры поиска или добавьте новую
            категорию.
          </div>
          <!-- /ko -->
          <!-- ko ifnot: query -->
          <div class="collection-categories__empty">
            Категорий филиалов пока нет
          </div>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko foreach: { 
            data: filteredCategories, 
            as: 'category', 
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400) 
          } -->

          <c-collection-category
            params="category: category, 
                    editing: category.unique === $parent.editingCategory()"
            data-bind="event: $parent.categoryEvents, attr: {
              'data-category': category.unique
            }"
          ></c-collection-category>

          <!-- /ko -->
        </div>
      </div>

      <div class="collection-categories__add">
        <fc-button
          params="color: 'success', label: 'Добавить категорию', mode: 'text', 
            click: function() {
              addCategory()
            }, disabled: editingCategory"
        ></fc-button>
      </div>
    </div>
  </div>

  <footer class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="click: function() { $dialog.hide('close'); }"
      >
        Закрыть
      </button>
    </div>
  </footer>
</foquz-dialog>
