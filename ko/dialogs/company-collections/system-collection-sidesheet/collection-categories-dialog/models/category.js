import { getUnique } from "@/utils/number/unique";

/**
 * @typedef {Object} CategoryParams
 * @property {String|Number} id
 * @property {String} name
 * @property {Number|String} count
 */

/**
 * @typedef {Object} Category
 * @property {String|null} id
 * @property {Number} unique
 * @property {Observable<String>} name
 * @property {Number} count
 */

const { observable } = ko;

/**
 * @type CategoryParams
 */
const defaultCategory = {
  id: "",
  name: "",
  count: 0,
};

/**
 * @param {CategoryParams} data
 * @returns {Category}
 */
export function CategoryModel(data = defaultCategory) {
  const { id, name, count } = data;

  return {
    id: id.toString(),
    unique: getUnique(),
    name: observable(name),
    count: parseInt(count) || 0,
  };
}

/**
 * @param {Category} category
 * @param {CategoryParams} data
 */
export function updateCategoryModel(category, data) {
  const { id, name, count } = data;

  category.id = id;
  category.name(name);
  category.count = parseInt(count);
}
