<!-- ko let: { $ctx: $component } -->

<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <div class="d-flex justify-content-between">
        <h2 class="foquz-dialog__title"
            data-bind="text: collection.name"></h2>

        <div>
          <!-- ko if: collectionId === 'filials' -->
            <fc-dropdown-button
              class="collection-actions"
              params="
                label: 'Действия',
                color: 'secondary',
                disabled: loading,
              "
            >
              <template data-slot="dropdown">
                <div class="fc-dropdown-list">
                  <a
                    href="javascript:void(0)"
                    data-bind="click: function() { addItem() }"
                    class="fc-dropdown-list__item"
                  >Новый филиал</a>
                  <a
                    href="javascript:void(0)"
                    data-bind="click: function() { openCategoryModal() }"
                    class="fc-dropdown-list__item"
                  >Управление категориями</a>
                  <a
                    href="javascript:void(0)"
                    data-bind="click: function() { addFromCSV() }"
                    class="fc-dropdown-list__item"
                  >Загрузить из файла CSV</a>
                  <a
                    href="/csv/filials.csv"
                    download
                    class="fc-dropdown-list__item"
                  >Скачать пример CSV файла</a>
                </div>
              </template>
            </fc-dropdown-button>
          <!-- /ko -->

          <!-- ko ifnot: collectionId === 'filials' -->
            <fc-button
              params="
                color: 'primary',
                label: collection.texts.newItem,
                click: function() { addItem() }
              "
            ></fc-button>
          <!-- /ko -->
        </div>
      </div>
    </div>
  </div>


  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">

      <!-- ko if: isEmpty -->
      <div class="h-100 d-flex align-items-center justify-content-center">
        <div class="container">
          <div class="text-center"
               data-bind="html: collection.texts.emptyMessage"></div>
        </div>

      </div>
      <!-- /ko -->

      <!-- ko ifnot: isEmpty -->
      <div class="container">
        <div class="py-4">
          <interactive-table params="table: collectionItems">
            <div data-bind="component: {
                name: $ctx.collectionTableComponent,
                params: {
                table: $data,
                onEdit: function(item) { $ctx.editItem(item) },
                onRemove: function(item) { $ctx.removeItem(item) },
                }
                }"></div>
          </interactive-table>
        </div>
      </div>
      <!-- /ko -->

    </div>
  </div>
</sidesheet>

<!-- /ko -->
