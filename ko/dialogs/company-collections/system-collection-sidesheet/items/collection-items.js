import { InteractiveTable } from 'Models/interactive-table';
import { SortModel } from 'Models/sort';
import { ApiUrl } from 'Utils/url/api-url';
import { CollectionItemFactory } from 'Models/collection/item';

export class CollectionItems extends InteractiveTable {
  constructor(collection) {
    super({
      collection
    });

    this.load();
  }

  getSort() {
    return new SortModel('created_at', true);
  }

  getSearch() {
    return this._createSearch('created_at', this.collection.titleField);
  }

  get searchParamName() {
    return 'search';
  }

  get url() {
    return ApiUrl(`dictionaries/${this.collection.serverId}`);
  }

  

  async load() {
    return new Promise((resolve, reject) => {
      if (this.beforeLoad()) {
        let url = this.url;
  
        $.ajax({
          url,
          data: this.getParams(),
          method: 'GET',
          success: (data) => {
            const items = data.items.map((i) => this.createItemModel(i));
            this.afterLoad(items);
            resolve(items);
          },
          error: (response) => {
            console.error(response.responseJSON);
            this.onError();
            reject(response)
          }
        });
      }
    });
  }

  createItemModel(data) {
    return CollectionItemFactory(this.collection, data);
  }

  addFirst(data) {
    this.items.unshift(this.createItemModel(data));
  }

  remove(item) {
    this.items.remove(item);
  }
}
