import { CollectionItems } from './collection-items';
import { FilialsCollectionItems } from './filials-collection-items';
import { FinesCollectionItems } from './fines-collection-items';
import { ThemesCollectionItems } from './themes-collection-items';

const models = {
  fines: FinesCollectionItems,
  filials: FilialsCollectionItems,
  themes: ThemesCollectionItems,
  default: CollectionItems
};

export function CollectionItemsTableFactory(collection) {
  let model = models[collection.serverId] || models.default;
  return new model(collection);
}
