import { CollectionItems } from './collection-items';
import { SortModel } from 'Models/sort';
export class ThemesCollectionItems extends CollectionItems {
  getSearch() {
    return {
      ...super.getSearch(),
      buttonText: ko.observable(''),
      createdAt: ko.observable('')
    };
  }

  getSort() {
    return new SortModel('createdAt', true);
  }

  get url() {
    return ApiUrl(`company-feedback/themes`);
  }

  nextPage() {}
}
