import { declOfNum } from 'Utils/string/decl-of-num';

export function getCsvLoadInfoText(stat) {
  let failed = '';
  
  if (Array.isArray(stat.failed_rows) && stat.failed_rows.length) {
    failed = `
      <div class="mb-15p">
        <span class="bold">${stat.failed_rows.join(', ')}</span>
        <span class="bold">
          ${declOfNum(stat.failed || 0, ['строка', 'строки', 'строк'])}
        </span>
        ${declOfNum(stat.failed || 0, ['пропущена', 'пропущены', 'пропущено'])}
        (ошибка в данных)
      </div>
    `;
  } else if (typeof stat.failed_rows === 'object' && Object.keys(stat.failed_rows).length) {
    const errors = Object.entries(stat.failed_rows)
    const errorsSet = Array.from(new Set(Object.values(stat.failed_rows)));
    errorsSet.forEach(error => {
      const failedRows = [];
      errors.forEach(([row, errorName]) => {
        if (errorName === error) {
          failedRows.push(Number(row) - 1);
        }
      });
      failed += `
        <div class="mb-15p">
          <span class="bold">${failedRows.join(', ')}</span>
          <span class="bold">
            ${declOfNum(failedRows.length || 0, ['строка', 'строки', 'строк'])}
          </span>
          —
          ${error}
        </div>
      `;
    })
  }

  let inserted = `
    <div class="mb-15p">
      <span class="bold">${stat.inserted}</span>
      <span class="bold">
        ${declOfNum(stat.inserted || 0, ['строка', 'строки', 'строк'])}
      </span>
      ${declOfNum(stat.inserted || 0, ['добавлена', 'добавлены', 'добавлено'])}
    </div>
  `;
  let deleted = `
    <div class="mb-15p">
      <span class="bold">${stat.deleted}</span>
      <span class="bold">
        ${declOfNum(stat.deleted || 0, ['строка', 'строки', 'строк'])}
      </span>
      ${declOfNum(stat.deleted || 0, ['удалена', 'удалены', 'удалено'])}
    </div>
  `;
  let update = `
    <div class="mb-15p">
      <span class="bold">${stat.updated}</span>
      <span class="bold">
        ${declOfNum(stat.updated || 0, ['строка', 'строки', 'строк'])}
      </span>
      ${declOfNum(stat.updated || 0, ['обновлена', 'обновлены', 'обновлено'])} 
    </div>
  `;

  return `
    ${failed}
    ${inserted}
    ${deleted}
    ${update}
  `;
}