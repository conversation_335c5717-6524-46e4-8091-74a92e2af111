import { CSVLoader } from "./csv-loader";
import { DialogWrapper } from "@/dialogs/wrapper";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    const {
      collectionId,
      url,
      onChange,
      isFilials,
    } = params;

    this.isSubmitted = ko.observable(false);
    this.isPending = ko.observable(false);
    this.addMode = ko.observable('add');
    this.collectionId = collectionId;
    this.url = url;
    this.isFilials = isFilials;
    this.onChange = onChange;

    this.subtitle = isFilials
      ? 'В каждой строке файла должно быть название филиала. <br/>Формат файла для загрузки описан в разделе <a href="https://foquz.ru/foquz/user-wiki" target="_blank">Помощь</a>.'
      : 'В каждой строке файла должно быть название элемента. <br/>Формат файла для загрузки описан в разделе <a href="https://foquz.ru/foquz/user-wiki" target="_blank">Помощь</a>.';

    this.desc = ko.computed(() => {
      const addMode = this.addMode();
      return (addMode === 'add')
        ? 'Добавятся новые элементы из файла. Одинаковые элементы (у которых имена и названия категорий совпадают с текущими на всех уровнях) — обновятся, при этом все существующие связки с вопросами сохранятся. Остальные данные останутся без изменений.'
        : 'Добавятся новые элементы из файла. Одинаковые элементы (у которых имена и названия категорий совпадают с текущими на всех уровнях) — обновятся, при этом все существующие связки с вопросами сохранятся. Остальные данные из справочника удалятся.';
    });
  }

  submit() {
    const formData = new FormData();
    let url;
    if (this.isFilials) {
      url = `${APIConfig.baseApiUrlPath}dictionaries/filials/import-csv?access-token=${APIConfig.apiKey}`;
    } else {
      formData.append("dictionary_id", this.collectionId);
      url = `${APIConfig.baseApiUrlPath}dictionaries/dictionary/import-csv?access-token=${APIConfig.apiKey}`;
    }
    formData.append("method", this.addMode());

    CSVLoader({
      formData: formData,
      url,
      onSelect: (promis) => this.onChange(promis),
    })();

    this.hide();
  }
}
