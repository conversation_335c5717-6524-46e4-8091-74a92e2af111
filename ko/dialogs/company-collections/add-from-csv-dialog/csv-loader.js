export function CSVLoader(config = {}) {
  let onError = (error, code) => {
    console.error(error, code);

    if (typeof config.onError === "function") config.onError(error);
    else alert(error);
  };
  const loadUrl = config.url;
  const formData = config.formData;
  const onSelect = config.onSelect || (() => {});

  function onSelectFile(file) {
    const dataArray = formData || new FormData();
    dataArray.append("file", file);

    const promis = new Promise((resolve, reject) => {
      $.ajax({
        url: loadUrl,
        type: "POST",
        data: dataArray,
        processData: false,
        contentType: false,
        success: function (data, textStatus, jqXHR) {
          resolve(data, textStatus, jqXHR);
        },
        error: function (jqXHR, textStatus, errorThrown) {
          onError("Ошибка запуска процедуры загрузки: " + errorThrown, 5);
          reject(jqXHR, textStatus, errorThrown);
        },
      });
    })

    onSelect(promis);
  }

  const createInput = () => {
    var input = document.createElement("input");
    input.type = "file";
    input.accept = "text/csv";
    input.onchange = (e) => {
      var file = e.target.files[0];
      onSelectFile(file);
    };
    return input;
  };

  return function () {
    createInput().click();
  };
};