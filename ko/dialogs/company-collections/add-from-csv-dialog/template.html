<!-- ko let: {
  $dialog: $component,
} -->
<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    <div class="">Загрузить из файла CSV</div>
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div
      class="lh-18p mb-30p"
      data-bind="html: subtitle"
    ></div>
    <div class="mb-15p">
      <fc-label
        class="mb-10p"
        params="text: 'Данные в справочнике'"
      ></fc-label>
      <radio-group
        params="
          options: [
            { value: 'add', label: 'Дополнить' },
            { value: 'replace', label: 'Заменить',}
          ],
          value: addMode,
        "
      ></radio-group>
    </div>
    <div
      class="f-color-service f-fs-1"
      data-bind="html: desc"
    ></div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="
          color: 'primary',
          inverse: true,
          label: 'Отменить',
          click: function() { hide() }
        "
      ></fc-button>

      <fc-button
        params="
          color: 'success',
          label: 'Загрузить',
          click: function() { submit() },
        "
      ></fc-button>
    </div>
  </div>
</foquz-dialog>
<!-- /ko -->
