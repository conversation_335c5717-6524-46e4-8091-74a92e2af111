import { get as _get } from "lodash";
import { DialogWrapper } from "@/dialogs/wrapper";
import { DialogsModule } from "@/utils/dialogs-module";
import {
  CompanyCollection,
  CompanyCollectionItemTypes,
} from "@/entities/structures/company-collection/types";
import { CollectionElementsList } from "./models/elements";
import { CollectionElement } from "./models/element";
import { CollectionItemList } from "../types";
import {
  getCollection,
  removeCollection,
  toggleCollectionActivity,
} from "@/api/collections/collection";
import {
  CollectionRemovedEvent,
  CollectionUpdatedEvent,
} from "@/utils/events/collections";
import {
  copyCollectionElement,
  removeCollectionElement,
  reorderCollectionElements,
} from "@/api/collections/collection-elements";
import { getCsvLoadInfoText } from "../getCsvLoadInfoText.js";

interface CustomCollectionSidesheetProps {
  collectionId: string;
  onChange?: (change: Partial<CompanyCollection>) => void;
}

const { observable, computed } = ko;

export class ViewModel extends DialogWrapper {
  collectionId: string;
  loading: KnockoutObservable<boolean>;
  onChange?: (change: Partial<CompanyCollection>) => void;
  elements: CollectionItemList;
  collectionName: KnockoutObservable<string>;
  collectionDescription: KnockoutObservable<string>;
  collectionActive: KnockoutObservable<boolean>;
  collection: CompanyCollection | null;
  canRemove: KnockoutObservable<boolean>;
  query: KnockoutObservable<string>;
  normalizedQuery: KnockoutComputed<string>;
  showOnCopyToast: KnockoutObservable<boolean>;
  showOnCopyErrorToast: KnockoutObservable<boolean>;
  copyToastText: KnockoutObservable<string>;
  copyErrorToastText: KnockoutObservable<string>;

  openDialog: (...params: any[]) => void;
  hide: (...params: any[]) => void;
  info: (...params: any[]) => void;
  confirm: (...params: any[]) => Promise<any>;

  constructor(params: CustomCollectionSidesheetProps, element) {
    super(params, element);

    DialogsModule(this);

    const { collectionId, onChange } = params;

    this.collectionId = collectionId;
    this.collection = null;

    this.loading = observable(false);
    this.onChange = onChange;

    this.collectionName = observable("");
    this.collectionDescription = observable("");
    this.collectionActive = observable(false);
    this.elements = CollectionElementsList();
    this.canRemove = observable(true);

    this.query = observable("");
    this.normalizedQuery = computed(() => {
      return this.query().trim().toLowerCase();
    });

    this.showOnCopyToast = ko.observable(false);
    this.showOnCopyErrorToast = ko.observable(false);
    this.copyToastText = ko.observable('');
    this.copyErrorToastText = ko.observable('');

    this.update();
  }

  async toggleActivity(checked) {
    if (!checked) {
      const res = await fetch(
        // @ts-ignore
        `/foquz/api/dictionaries/dictionary/check-poll-links?id=${this.collection.id}&access-token=${APIConfig.apiKey}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json;charset=utf-8'
          },
        },
      );
      const data = await res.json();
      if (_get(data, 'hasPolls')) {
        this.confirm({
          title: "Отключение справочника",
          text: `
            <p>«${this.collection.name}»</p>
            <p>
              При отключении справочника перестанут работать связки с ним в вопросах
            </p>
            <p>
              Справочник связан с опросом:
            </p>
            <ul class="list list--inner list--left-margin">
              ${data.polls.map(poll => `<li><a href="/foquz/foquz-poll/settings?id=${poll.id}" target="_blank">${poll.name}</a></li>`)}
            </ul>
          `,
          confirm: "Отключить",
          mode: "secondary",
        }).then(() => {
          toggleCollectionActivity(this.collectionId, checked).then((res) => {
            if (res) {
              this.collectionActive(checked);
              CollectionUpdatedEvent.emit({
                id: this.collectionId,
                active: checked,
              });
            }
          });
        });
        return;
      }
    }
    toggleCollectionActivity(this.collectionId, checked).then((res) => {
      if (res) {
        this.collectionActive(checked);
        CollectionUpdatedEvent.emit({
          id: this.collectionId,
          active: checked,
        });
      }
    });
  }

  editCollection() {
    this.openDialog({
      name: "collection-name-dialog",
      params: {
        id: this.collectionId,
        name: this.collectionName(),
        description: this.collectionDescription(),

        onChange: (data) => {
          const { id, name, description } = data;
          this.collectionName(name);
          this.collectionDescription(description);
          CollectionUpdatedEvent.emit(data);
        },
      },
    });
  }

  addElement() {
    this.openDialog({
      name: "custom-collection-element-dialog",
      params: {
        categories: this.elements.getHierarchy(),
        collectionId: this.collectionId,
        isCategory: false,
        onChange: (data) => {
          this.elements.add(data);
          CollectionUpdatedEvent.emit({
            id: this.collectionId,
            count: this.elements.getElementsCount(),
          });
        },
      },
    });
  }

  addCategory() {
    this.openDialog({
      name: "custom-collection-element-dialog",
      params: {
        categories: this.elements.getHierarchy(),
        collectionId: this.collectionId,
        isCategory: true,
        onChange: (data) => {
          this.elements.add(data);
        },
      },
    });
  }

  async update() {
    const data = await getCollection(this.collectionId)
    const { collection, elements } = data;
    this.collection = collection;
    this.collectionName(collection.name);
    this.collectionDescription(collection.description);
    this.collectionActive(collection.active);
    this.elements.setList(elements);
    this.canRemove(collection.canRemove && this.elements.isEmpty());
  }

  addFromCSV() {
    this.openDialog({
      name: "add-from-csv-dialog",
      params: {
        collectionId: this.collectionId,
        onChange: async(promis) => {
          this.loading(true);
          const { stat } = await promis;
          await this.update();
          
          this.info({
            title: 'Загрузка элементов',
            text: getCsvLoadInfoText(stat),
            close: 'Закрыть'
          });
          this.loading(false);
        },
      },
    });
  }

  async copyElement(element) {
    try {
      this.loading(true);
      const data = await copyCollectionElement(element.id, element.parentId() || 0, element.name());
      this.elements.insertAfter(data, element);
      CollectionUpdatedEvent.emit({
        id: this.collectionId,
        count: this.elements.getElementsCount(),
      });
      // this.reorderItems();
      this.copyToastText(
        `${element.category ? 'Категория скопирована' : 'Элемент скопирован'} успешно`,
      );
      this.showOnCopyToast(true);
    } catch (error) {
      this.copyErrorToastText(
        `При копировании ${element.category ? 'категории' : 'элемента'} произошла ошибка`,
      );
      this.showOnCopyErrorToast(true);
    } finally {
      this.loading(false);
    }
  }

  editElement(element) {
    this.openDialog({
      name: "custom-collection-element-dialog",
      params: {
        id: element.id,
        name: element.name(),
        description: element.description(),
        parentId: element.parentId(),
        deep: element.deep(),
        categories: this.elements.getHierarchy(),
        collectionId: this.collectionId,
        isCategory: element.category,
        onChange: (data) => {
          this.elements.update(data);
        },
      },
    });
  }

  async removeElement(element) {
    const res = await fetch(
      // @ts-ignore
      `/foquz/api/dictionaries/elements/check-poll-links?id=${element.id}&access-token=${APIConfig.apiKey}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json;charset=utf-8'
        },
      },
    );
    const data = await res.json();
    if (_get(data, 'hasPolls')) {
      this.confirm({
        title: "Удаление элемента",
        text: `
          При удалении элемента справочника будет удалена связка с ним в опросах:
          <ul>
            ${data.polls.map(poll => `<li><a href="/foquz/foquz-poll/settings?id=${poll.id}" target="_blank">${poll.name}</a></li>`)}
          </ul>
        `,
        confirm: "Удалить",
        mode: "danger",
      }).then(() => {
        removeCollectionElement(element.id);
        this.elements.remove(element.id);
        CollectionUpdatedEvent.emit({
          id: this.collectionId,
          count: this.elements.getElementsCount(),
        });
      });
      return;
    }
    this.confirm({
      title: element.category ? "Удаление категории" : "Удаление элемента",
      text: element.category
        ? "Категория будет удалена вместе с элементами, которые она содержит."
        : "Элемент справочника будет удалён без возможности восстановления.",
      confirm: "Удалить",
      mode: "danger",
    }).then(() => {
      removeCollectionElement(element.id);
      this.elements.remove(element.id);
      CollectionUpdatedEvent.emit({
        id: this.collectionId,
        count: this.elements.getElementsCount(),
      });
    });
  }

  async removeCollection() {
    if (this.canRemove()) {
      const res = await fetch(
        // @ts-ignore
        `/foquz/api/dictionaries/dictionary/check-poll-links?id=${this.collection.id}&access-token=${APIConfig.apiKey}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json;charset=utf-8'
          },
        },
      );
      const data = await res.json();
      if (_get(data, 'hasPolls')) {
        this.confirm({
          title: "Удаление справочника",
          text: `
            <p>«${this.collection.name}»</p>
            <p>
              Справочник используется в опросах:
            </p>
            <ul>
              ${data.polls.map(poll => `<li><a href="/foquz/foquz-poll/settings?id=${poll.id}" target="_blank">${poll.name}</a></li>`)}
            </ul>
          `,
          confirm: "Удалить",
          mode: "danger",
        }).then(() => {
          removeCollection(this.collection.id).then(() => {
            CollectionRemovedEvent.emit(this.collection.id);
            this.hide();
          });
        });
        return;
      }
      this.confirm({
        title: "Удаление справочника",
        text: "Справочник будет удален вместе с элементами, которые он содержит.",
        confirm: "Удалить",
        mode: "danger",
      }).then(() => {
        removeCollection(this.collection.id).then(() => {
          CollectionRemovedEvent.emit(this.collection.id);
          this.hide();
        });
      });
    } else {
      this.info({
        text: "В данный момент удаление невозможно, так как справочник содержит данные.",
      });
    }
  }

  reorderItems() {
    this.elements.sort();

    setTimeout(() => {
      const order = this.elements.reorder();
      reorderCollectionElements(order);
    });
  }
}
