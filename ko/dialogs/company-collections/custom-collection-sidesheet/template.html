<!-- ko let: { $ctx: $component, fixedHeader: ko.observable(false) } -->

<sidesheet params="ref: modal, dialogWrapper: $component">
  <div
    class="foquz-dialog__header pb-20p"
    data-bind="css: {
    fixed: fixedHeader
  }"
  >
    <div class="d-flex justify-content-end">
      <fc-dropdown-button
        class="collection-actions"
        params="
          label: 'Действия',
          color: 'secondary',
          disabled: loading,
        "
      >
        <template data-slot="dropdown">
          <div class="fc-dropdown-list">
            <a
              href="javascript:void(0)"
              data-bind="click: function() { addElement() }"
              class="fc-dropdown-list__item"
            >Добавить элемент</a>
            <a
              href="javascript:void(0)"
              data-bind="click: function() { addCategory() }"
              class="fc-dropdown-list__item"
            >Добавить категорию</a>
            <a
              href="javascript:void(0)"
              data-bind="click: function() { addFromCSV() }"
              class="fc-dropdown-list__item"
            >Загрузить из файла CSV</a>
            <a
              href="/csv/directory.csv"
              download
              class="fc-dropdown-list__item"
            >Скачать пример CSV файла</a>
            <hr class="mr-20p ml-20p mt-10p mb-10p">
            <a
              href="javascript:void(0)"
              data-bind="click: function() { removeCollection() }, style: {
                'opacity': canRemove() ? 1 : 0.5
              }"
              class="fc-dropdown-list__item"
            >Удалить справочник</a>
          </div>
        </template>
      </fc-dropdown-button>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div style="min-height: 100%; display: flex; flex-direction: column">
        <div class="collection-header">
          <div class="container">
            <div class="d-flex">
              <div class="flex-grow-1">
                <div class="d-flex">
                  <h2
                    class="foquz-dialog__title"
                    data-bind="text: collectionName"
                  ></h2>
                  <fc-button
                    class="ml-15p mt-1"
                    params="mode: 'text', size: 'auto', icon:'pencil', click: function() {
                      editCollection();
                    }"
                  ></fc-button>
                </div>

                <!-- ko if: collectionDescription -->
                <div
                  class="f-color-service f-fs-2 mt-10p"
                  data-bind="text: collectionDescription"
                ></div>
                <!-- /ko -->
              </div>

              <div class="flex-shrink-0 ml-25p">
                <fc-switch
                  class="pt-5p"
                  params="
                    checked: collectionActive,
                    onChange: function(value) {
                      toggleActivity(value);
                    },
                    label: 'Активный',
                    disabled: loading,
                  "
                ></fc-switch>
              </div>
            </div>
          </div>
        </div>

        <fc-intersection-watcher
          data-bind="event: {
          'top.hide.intersect': function() {
            fixedHeader(true)
          },
          'top.show.intersect': function() {
            fixedHeader(false)
          }
        }"
        ></fc-intersection-watcher>

        <!-- ko ifnot: elements.loaded -->
        <div
          class="flex-grow-1 d-flex align-items-center justify-content-center"
        >
          <fc-spinner class="f-color-primary"></fc-spinner>
        </div>
        <!-- /ko -->

        <!-- ko if: elements.loaded -->
        <!-- ko if: elements.isEmpty -->
        <div
          class="flex-grow-1 d-flex align-items-center justify-content-center"
        >
          <div class="container">
            <div class="text-center f-fs-2-5">
              Справочник пока пустой.<br />
              Для добавления категории или элемента нажмите кнопку
              <b class="bold">«Действия»</b>.
            </div>
          </div>
        </div>
        <!-- /ko -->

        <!-- ko ifnot: elements.isEmpty -->
        <div class="container">
          <div class="row">
            <div class="col col-6 mb-40p">
              <fc-input
                params="value: elements.query, icon: { name: 'search' }, clearable: true, placeholder: 'Поиск по названию'"
              ></fc-input>
            </div>
          </div>

          <!-- ko if: elements.noResults -->
          <div class="f-color-service">Ничего не найдено</div>
          <!-- /ko -->

          <div style="padding-bottom: 80px">
            <fc-collection-elements
              data-bind="css: { 'collection-elements--loading': loading }"
              params="
                list: elements.list,
                reorderItems: function() {
                  reorderItems();
                },
                isHidden: function(id) {
                  return elements.isHidden(id)
                },
                onCopy: function(element) {
                  copyElement(element)
                },
                onEdit: function(element) {
                  editElement(element)
                }, 
                onRemove: function(element) {
                  removeElement(element)
                }"
            ></fc-collection-elements>
            <!-- ko if: loading -->
            <fc-spinner class="f-color-primary collection-elements-loader"></fc-spinner>
            <!-- /ko -->
          </div>
        </div>
        <!-- /ko -->
        <!-- /ko -->
      </div>
    </div>
  </div>
</sidesheet>

<fc-toast params="show: showOnCopyToast, mode: 'default', text: copyToastText"></fc-toast>
<fc-toast params="show: showOnCopyErrorToast, mode: 'warning', text: copyErrorToastText"></fc-toast>

<!-- /ko -->
