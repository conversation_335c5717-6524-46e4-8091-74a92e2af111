import { MoveEvent } from "sortablejs";
import { MAX_COLLECTION_LEVEL } from "../../constants";

export function checkSort(event: MoveEvent): {
  enabled: boolean;
  message: string;
} {
  const { dragged, to } = event;

  const containerLevel = parseInt(to.dataset.level) || 0;
  const elementDeep = parseInt(dragged.dataset.deep) || 0;

  const enabled = containerLevel + elementDeep <= MAX_COLLECTION_LEVEL;

  console.log("check sort", enabled);

  return {
    enabled,
    message:
      "Категория не может быть перенесена сюда. Максимальная вложенность — 5 уровней.",
  };
}
