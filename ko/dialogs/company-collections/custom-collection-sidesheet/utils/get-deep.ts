import { CollectionItem } from "../../types";

export function getElementDeep(element: {
  category: boolean;
  children: KnockoutObservableArray<CollectionItem>;
}) {
  if (element.category) {
    const children = element.children();
    if (!children.length) return 1;
    const childrenDeeps = children.map((child) => getElementDeep(child));
    return 1 + Math.max(...childrenDeeps);
  }

  return 0;
}
