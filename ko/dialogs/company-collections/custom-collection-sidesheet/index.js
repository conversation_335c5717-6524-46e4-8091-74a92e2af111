import { ViewModel } from "./model.ts";
import html from "./template.html";
import "./style.less";

import "../custom-collection-element-dialog";
import "../add-from-csv-dialog";

import * as CollectionElement from "./components/collection-element";
import * as CollectionElements from "./components/collection-elements";

import { registerComponents } from "@/utils/engine/register-component";

registerComponents({
  "fc-collection-element": CollectionElement,
  "fc-collection-elements": CollectionElements
});

ko.components.register("custom-collection-sidesheet", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("custom-collection-sidesheet");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
