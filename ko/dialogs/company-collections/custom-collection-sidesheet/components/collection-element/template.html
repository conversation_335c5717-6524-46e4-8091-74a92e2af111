<div class="fc-collection-element__wrapper">
  <div class="fc-collection-element__handle">
    <fc-icon params="name: 'drag-arrow'"></fc-icon>
  </div>

  <div class="fc-collection-element__data">
    <div class="fc-collection-element__title">
      <div class="fc-collection-element__name">
        <span data-bind="text: name"></span>
        <!-- ko if: isCategory -->
        <span
          data-bind="text: '(' + elementsCount() + ')'"
          class="fc-collection-element__count"
        ></span>
        <!-- /ko -->
      </div>

      <!-- ko if: isCategory -->
      <!-- ko if: children().length -->

      <fc-expander
        params="open: element.open"
        class="fc-collection-element__toggler"
      ></fc-expander>

      <!-- /ko -->
      <!-- /ko -->
    </div>

    <div
      data-bind="text: description"
      class="fc-collection-element__description"
    ></div>
  </div>

  <div class="fc-collection-element__actions">
    <fc-button
      params="mode: 'text', 
            size: 'auto', 
            icon: {name: 'copy'}, 
            click: function() { onCopy(element) },
            "
      data-bind="fbPopper, title: `Копировать ${isCategory ? 'категорию' : 'элемент'}`"
      class="fc-collection-element__copy"
    ></fc-button>
    <fc-button
      params="mode: 'text', 
            size: 'auto', 
            icon: {name: 'pencil'}, 
            click: function() { onEdit(element) },
            "
      data-bind="fbPopper, title: 'Редактировать'"
      class="fc-collection-element__edit"
    ></fc-button>
    <fc-button
      params="mode: 'text', 
            size: 'auto', 
            icon: {name: 'bin'}, 
            click: function() { onRemove(element) },
            "
          data-bind="fbPopper, title: 'Удалить'"
      class="fc-collection-element__remove"
    ></fc-button>
  </div>
</div>
