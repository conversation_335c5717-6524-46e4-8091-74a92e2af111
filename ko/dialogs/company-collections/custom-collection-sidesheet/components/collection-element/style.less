.fc-collection-element {
  border-top: 1px solid #e7ebed;
  display: block;

  &__wrapper {
    padding: 15px;
    display: flex;
    align-items: center;
  }

  &__handle {
    flex-shrink: 0;
    margin-right: 15px;
    color: var(--f-color-service);
  }
  &__data {
    flex-grow: 1;
  }
  &__actions {
    flex-shrink: 0;
    margin-left: 15px;
  }
  &__copy {
    margin-right: 15px;
  }
  &__edit {
    margin-right: 15px;
  }

  &__title {
    display: flex;
    align-items: center;
  }

  &__name {
    font-size: 15px;
  }

  &__toggler {
    flex-shrink: 0;
    margin-left: 15px;
    cursor: pointer;
  }
  &__description {
    font-size: 12px;
    color: var(--f-color-service);
  }

  &--category {
    & > .fc-collection-element__wrapper {
      background: #f5f6fa;

      .fc-collection-element__name {
        font-size: 19px;
        font-weight: 500;
      }
      .fc-collection-element__count {
        color: var(--f-color-service);
        font-weight: 400;
      }
    }
  }
}
