import { MoveEvent } from "sortablejs";
import { CollectionItem } from "../../../types";
import { checkSort } from "../../utils/check-sort";

interface CollectionElementProps {
  element: CollectionItem;
  
  onCopy: (element: CollectionItem) => void;
  onEdit: (element: CollectionItem) => void;
  onRemove: (element: CollectionItem) => void;
  onSort: () => void;
}


export function ViewModel(
  props: CollectionElementProps,
  container: HTMLElement
) {
  const { element, onCopy, onEdit, onRemove, onSort } = props;

  container.classList.add("fc-collection-element");
  if (element.category) {
    container.classList.add("fc-collection-element--category");
  }



  return {
    element,

    name: element.name,
    description: element.description,
    children: element.children,

    isCategory: element.category,
    elementsCount: element.elementsCount,

    onCopy,
    onEdit,
    onRemove,

    beforeMoved(event: MoveEvent) {
      const enabled = checkSort(event);

      return enabled;
    },
    onSort() {
      if (typeof onSort === 'function') onSort();
    }
  };
}
