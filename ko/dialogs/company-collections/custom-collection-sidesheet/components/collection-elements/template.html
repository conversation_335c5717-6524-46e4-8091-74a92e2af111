<!-- ko let: { $ctx: $component } -->
<div
  class="fc-collection-items__wrapper"
  data-bind="attr: {
    'data-level': level 
  },
  fbSortable: { 
    foreach: list, 
    options: {
      //comparer: comparer,
      group: 'collection',
      handle: '.fc-collection-element__handle',
      beforeMoved: beforeMoved,
      onSort: function() {
        $ctx.reorderItems();
      },
      onAdd: function() {
        $ctx.showItems(true);
      }
    }
  }"
>
  <!-- ko template: {
      foreach: templateIf($ctx.showItems(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400)
  } -->

  <div
    class="fc-collection-item"
    data-bind="attr: {
    'data-deep': $data.deep
  }"
  >
    <!-- ko ifnot: $ctx.isHidden($data.id) -->
    <fc-collection-element
      class="fc-collection-item__data"
      params="element: $data,
              onCopy: function() {
                $ctx.onCopy($data)
              },
              onEdit: function() {
                $ctx.onEdit($data)
              }, 
              onRemove: function(element) {
                $ctx.onRemove($data)
              }"
    ></fc-collection-element>

    <!-- ko if: $data.category -->

    <div
      class="fc-collection-item__children"
      data-bind="let: { $currentElement: $data }"
    >
      <fc-collection-elements
        params="level: $ctx.level + 1,
                showItems: $currentElement.open,
                list: $currentElement.children,
                isHidden: $ctx.isHidden,
                reorderItems: $ctx.reorderItems,
                onCopy: $ctx.onCopy,
                onEdit: $ctx.onEdit,
                onRemove: $ctx.onRemove"
      ></fc-collection-elements>
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>

  <!-- /ko -->
</div>
<!-- /ko -->
