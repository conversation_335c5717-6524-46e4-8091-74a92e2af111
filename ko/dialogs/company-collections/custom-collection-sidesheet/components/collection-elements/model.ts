import { CollectionItem } from "@/dialogs/company-collections/types";
import { MoveEvent } from "sortablejs";
import { checkSort } from "../../utils/check-sort";
import { itemsComparer } from "../../utils/comparer";

interface CollectionElementsProps {
  isHidden: (id: string) => boolean;
  showItems: KnockoutObservable<boolean>;
  level: number;
  list: KnockoutObservableArray<CollectionItem>;
  onCopy: (element: CollectionItem) => void;
  onEdit: (element: CollectionItem) => void;
  onRemove: (element: CollectionItem) => void;
  reorderItems: () => void;
}

export function ViewModel(
  props: CollectionElementsProps,
  element: HTMLElement
) {
  element.classList.add("fc-collection-items");

  const { showItems, isHidden, level, list, onCopy, onEdit, onRemove, reorderItems } = props;

  console.log({ level })

  return {
    showItems: showItems || ko.observable(true),
    isHidden,
    level: level || 0,
    list,

    onCopy,
    onEdit,
    onRemove,
    reorderItems,

    comparer: itemsComparer,

    beforeMoved: (event) => checkSort(event),
  };
}
