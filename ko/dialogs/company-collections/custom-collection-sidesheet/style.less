@import "@/less/vars/breakpoints.less";

.custom-collection-sidesheet {
  .container {
    max-width: 100%;
  }
  
  .foquz-dialog__header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding-left: 30px;
    padding-right: 30px;
    padding-top: 25px;
    padding-bottom: 20px;
    z-index: 10;
    pointer-events: none;

    transition: padding-top 400ms;

    .collection-actions {
      pointer-events: all;
    }

    &.fixed {
      padding-top: 20px;
      background-color: white;
      z-index: 500;

      &:after {
        content: "";
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        height: 22px;
        background: linear-gradient(
          0deg,
          rgba(115, 128, 141, 0) 0%,
          rgba(115, 128, 141, 0.15) 100%
        );
      }
    }
  }

  .collection-items {
    border-bottom: 1px solid #e7ebed;
  }

  .collection-header {
    padding-top: 25px;
    margin-bottom: 30px;

    .container {
      padding-right: 205px;
    }
  }
}

.collection-elements--loading {
  opacity: 0.2;
}

.collection-elements-loader {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
