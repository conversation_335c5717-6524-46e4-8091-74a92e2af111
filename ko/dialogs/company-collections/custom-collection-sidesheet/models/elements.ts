import {
  CollectionItem,
  CollectionItemList,
  CollectionItemsOrder,
} from "../../types";
import { CollectionElement } from "./element";
import { CompanyCollectionItem } from "@/entities/structures/company-collection/types";
import { SelectListItem } from "@/types";
import { useMultilevelList } from "@/utils/list/use-multilevel-list";
import { itemsComparer } from "../utils/comparer";

const { observable, computed } = ko;

function formatCollectionItem(item: CollectionItem): SelectListItem {
  return {
    id: item.id,
    text: item.name(),
    items: item
      .children()
      .filter((el) => el.category)
      .map((el) => formatCollectionItem(el)),
  };
}

function hasMatch(item: CollectionItem, query: string): boolean {
  return item.name().toLowerCase().includes(query);
}

function categoryHasVisibleChildren(
  item: CollectionItem,
  query: string
): boolean {
  const children = item.children();
  return children.some((child) => {
    if (hasMatch(child, query)) return true;
    if (!child.category) return false;
    return categoryHasVisibleChildren(child, query);
  });
}

export function CollectionElementsList(): CollectionItemList {
  const loaded = observable(false);
  const list = useMultilevelList<CollectionItem>([], {
    comparer: itemsComparer,
  });
  const query = observable("");

  const _hackUpdating = observable(false);

  const normalizedQuery = computed(() => query().trim().toLowerCase());

  const hiddenElements = computed(() => {
    const q = normalizedQuery();

    if (!q) return [];

    const items = list.items();

    const hidden = [];

    function checkItems(list) {
      list.forEach((item) => {
        const match = hasMatch(item, q);
        console.log("check", item, match);

        if (!item.category) {
          // элемент
          // скрывать, если нет совпадения
          if (!match) hidden.push(item.id);
          return;
        }

        // категория

        // есть совпадение - показывать всех детей
        if (match) return;

        // нет совпадения, нет совпадения среди детей, не показывать
        if (!categoryHasVisibleChildren(item, q)) {
          hidden.push(item.id);
          return;
        }

        // среди детей есть совпадения, категорию показывать, проверять дальше
        checkItems(item.children());
      });
    }

    checkItems(items);

    return hidden;
  });

  const noResults = computed(() => {
    if (!normalizedQuery) return false;
    const hidden = hiddenElements();
    if (!hidden.length) return false;
    const firstLevel = list.items().map((item) => item.id);
    console.log({
      hidden,
      firstLevel,
      noresults: firstLevel.every((id) => hidden.includes(id)),
    });
    return firstLevel.every((id) => hidden.includes(id));
  });

  function sort() {
    list.sort(itemsComparer);

    // TODO: HACK!
    /**
     * Не обновляется порядок элементов в интерфейсе,
     * если не сбросить весь список сначала
     */

    _hackUpdating(true);
    const items = list.items();

    list.items([]);

    list.items(items);
    _hackUpdating(false);
  }

  return {
    loaded,
    query,
    isHidden: (id: string) => {
      return hiddenElements().includes(id);
    },
    noResults,

    list: list.items,
    isEmpty: computed(() => {
      if (_hackUpdating()) return false;
      return list.items().length === 0;
    }),
    setList(data: CompanyCollectionItem[]) {
      const newList = data.map((element) => {
        const model = CollectionElement(element, normalizedQuery);

        return model;
      });

      list.update(newList);

      sort();

      loaded(true);
    },

    getHierarchy() {
      return list
        .items()
        .filter((el) => el.category)
        .map((el) => formatCollectionItem(el));
    },

    add(element: Partial<CompanyCollectionItem>) {
      const model = CollectionElement(element, normalizedQuery);

      if (element.parentId) {
        list.unshift(model, (item) => item.id === element.parentId);
        const parent = list.find((item) => item.id === element.parentId);
        if (parent) parent.open(true);
      } else {
        list.unshift(model);
      }
    },

    insertAfter: (element: Partial<CompanyCollectionItem>, prev: any): void => {
      const model = CollectionElement(element, normalizedQuery);

      if (element.parentId) {
        list.insertAfter(model, prev, (item) => item.id === element.parentId);
      } else {
        list.insertAfter(model, prev);
      }
    },

    update(element: Partial<CompanyCollectionItem>) {
      const model = list.find((item) => item.id === element.id);
      const oldParent = model.parentId();

      model.update(element);

      const newParent = model.parentId();

      if (oldParent !== newParent) {
        if (newParent) {
          list.move(model, (item) => item.id === newParent);
        } else {
          list.move(model);
        }
      }
    },

    remove(elementId: string) {
      list.remove(list.find((item) => item.id === elementId));
    },

    reorder() {
      let position = 1;

      let order: CollectionItemsOrder = {};

      const newParentIds: {
        [id: string]: string;
      } = {};

      list.iterate((item) => {
        const itemParentId = newParentIds[item.id] || "0";

        item.parentId(itemParentId);

        order[item.id] = {
          parent: itemParentId,
          position: position++,
        };

        if (item.category) {
          item
            .children()
            .forEach((child) => (newParentIds[child.id] = item.id));
        }

        return false;
      });

      return order;
    },

    sort,

    getElementsCount() {
      const count = list.items().reduce((sum, item) => {
        if (item.category) return sum + item.elementsCount();
        return sum + 1;
      }, 0);

      return count;
    },
  };
}
