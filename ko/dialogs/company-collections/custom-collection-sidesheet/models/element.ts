import { CollectionItem } from "../../types";
import { CompanyCollectionItem } from "@/entities/structures/company-collection/types";
import { getElementDeep } from "../utils/get-deep";

const { observable, observableArray, computed } = ko;

function getElementsCount(children: CollectionItem[]): number {
  let counter = 0;
  children.forEach((child) => {
    if (child.category) {
      counter += getElementsCount(child.children());
    } else {
      counter++;
    }
  });
  return counter;
}

export function CollectionElement(
  data: Partial<CompanyCollectionItem>,
  query: KnockoutComputed<string>
): CollectionItem {
  const {
    id,
    name = "",
    description = "",
    children = [],
    category = false,
    parentId = "0",
  } = data;

  const elementName = observable(name);
  const elementDescription = observable(description);
  const elementChildren = observableArray(
    children.map((el) => CollectionElement(el, query))
  );

  const elementParent = observable(parentId);

  const deep = computed(() => {
    return getElementDeep({
      category,
      children: elementChildren,
    });
  });

  const open = observable(false);

  query.subscribe((v) => {
    if (v) open(true);
    else open(false);
  });

  return {
    id: `${id}`,
    name: elementName,
    description: elementDescription,
    parentId: elementParent,
    category,
    children: elementChildren,
    open,
    elementsCount: computed(() => {
      const newCount = getElementsCount(elementChildren());
      return newCount;
    }),
    update: (data: Partial<CompanyCollectionItem>) => {
      if ("name" in data) elementName(data.name);
      if ("description" in data) elementDescription(data.description);
      if ("parentId" in data) elementParent(data.parentId);
    },
    deep,
  };
}
