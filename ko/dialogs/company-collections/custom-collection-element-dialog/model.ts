import { ERROR_TEXT } from "@/constants/validators/errors";
import { DialogWrapper } from "@/dialogs/wrapper";
import { errorMatcher } from "@/utils/validation/state-matchers";
import { StateMatcherFn } from "@/utils/validation/state-matchers.types";
import { SelectListItem } from "@/types";
import { collectionElementAction } from "../../../api/collections/collection-elements";
import { CompanyCollectionItem } from "../../../entities/structures/company-collection/types";
import { CompanyCollectionItemTypes } from "@/entities/structures/company-collection/types";
import { MAX_COLLECTION_LEVEL } from "../constants";

interface CollectionNameDialogProps {
  collectionId: string;
  id: string | null;
  name: string;
  description: string;
  parentId: string;
  isCategory: boolean;
  deep: number;
  categories: SelectListItem[];

  onChange: (element: CompanyCollectionItem) => void;
}

const titles = [
  ["Новая категория", "Редактировать категорию"],
  ["Новый элемент", "Редактировать элемент"],
];

function findCategory(id, categories) {
  const stack = [...categories];
  while (stack.length) {
    const cat = stack.pop();
    if (cat.id === id) return cat;
    stack.push(...cat.items);
  }
  return null;
}

function getCategoryElements(category) {
  const stack = [category];
  const elements = [];

  while (stack.length) {
    const cat = stack.pop();
    elements.push(cat.id);
    stack.push(...cat.items);
  }

  return elements;
}

export class ViewModel extends DialogWrapper {
  collectionId: string;
  elementId: string | null;
  name: KnockoutObservable<string>;
  description: KnockoutObservable<string>;
  parentId: KnockoutObservable<string>;
  isCategory: boolean;
  disabledLevel: number;
  disabledOptions: string[];
  categories: SelectListItem[];
  title: string;
  isSubmitted: KnockoutObservable<boolean>;
  isPending: KnockoutObservable<boolean>;
  matcher: StateMatcherFn;
  hide: () => void;
  onChange: (element: Partial<CompanyCollectionItem>) => void;

  constructor(params: CollectionNameDialogProps, element: HTMLElement) {
    super(params, element);
    element.classList.add("custom-collection-element-dialog");

    const {
      id,
      name,
      isCategory,
      collectionId,
      description,
      parentId,
      deep,
      categories,
      onChange,
    } = params;

    this.isSubmitted = ko.observable(false);
    this.isPending = ko.observable(false);
    this.matcher = errorMatcher(this.isSubmitted);

    this.elementId = id;
    this.isCategory = isCategory;
    this.collectionId = collectionId;
    this.name = ko.observable(name || "").extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });
    this.description = ko.observable(description || "");
    this.parentId = ko.observable(parentId);

    let _deep = deep || 0;
    if (!id && this.isCategory) _deep = 1;

    this.disabledLevel = MAX_COLLECTION_LEVEL - _deep;
    this.disabledOptions = [];

    this.categories = categories;

    if (id && this.isCategory) {
      const cat = findCategory(id, this.categories);
      if (cat) {
        const ids = getCategoryElements(cat);
        this.disabledOptions = ids;
      }
    }

    this.title = titles[this.isCategory ? 0 : 1][id ? 1 : 0];

    this.onChange = onChange;
  }

  isValid() {
    return this.name.isValid();
  }

  getParams() {
    return {
      id: this.elementId,
      name: this.name(),
      type: this.isCategory
        ? CompanyCollectionItemTypes.Category
        : CompanyCollectionItemTypes.Element,
      collectionId: this.collectionId,
      description: this.description(),
      parentId: this.parentId(),
    };
  }

  submit() {
    this.isSubmitted(true);

    if (!this.isValid()) return false;

    this.isSubmitted(false);
    this.isPending(true);

    const params = this.getParams();

    collectionElementAction(params).then((element) => {
      if (typeof this.onChange === "function") {
        this.onChange(element);
      }

      this.hide();
    });
  }
}
