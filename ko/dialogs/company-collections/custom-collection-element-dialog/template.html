<!-- ko let: { $dialog: $component } -->
<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    <div data-bind="text: $parent.title"></div>
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <fc-label params="text: 'Название'"></fc-label>
      <fc-input
        params="placeholder: 'Введите название', value: name, maxlength: 255, counter: true, invalid: matcher(name)"
      ></fc-input>
      <fc-error params="show: matcher(name), text: name.error"></fc-error>
    </div>

    <div class="form-group" data-bind="log">
      <fc-label
        params="text: isCategory ? 'Местоположение категории' : 'Местоположение элемента', hint: isCategory ? 'Местоположение категории' : 'Местоположение элемента'"
      ></fc-label>
      <fc-select
        params="value: parentId, options: categories, clearable: true, placeholder: isCategory ? 'Верхний уровень' : 'Без категории', 
            disabledOptions: disabledOptions,
            disabledLevel: disabledLevel,"
      ></fc-select>
    </div>

    <div class="form-group">
      <fc-label params="text: 'Описание', optional: true"></fc-label>
      <fc-textarea
        params="value: description, maxlength: 500, counter: true"
      ></fc-textarea>
      <div class="form-label__note mt-10p">Описание можно использовать как подсказку для элемента вопроса типа Классификатор в прохождении опроса</div>
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="color: 'primary', inverse: true, label: 'Отменить', click: function() { hide() }"
      ></fc-button>

      <fc-button
        params="color: 'success', label: 'Сохранить', click: function() { submit() },
        pending: isPending, disabled: isPending"
      ></fc-button>
    </div>
  </div>
</foquz-dialog>
<!-- /ko -->
