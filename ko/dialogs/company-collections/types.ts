import { CompanyCollectionItem } from "@/entities/structures/company-collection/types";
import { SelectListItem } from "@/types";

export type CollectionItem = {
  id: string;
  name: KnockoutObservable<string>;
  description: KnockoutObservable<string>;
  category: boolean;
  children: KnockoutObservableArray<CollectionItem>;
  parentId: KnockoutObservable<string>;
  elementsCount: KnockoutComputed<number>;
  deep: KnockoutComputed<number>;
  open: KnockoutObservable<boolean>;

  update: (data: Partial<CompanyCollectionItem>) => void;
};

export type CollectionItemsOrder = {
  [id: string]: {
    position: number;
    parent: string;
  }
}

export type CollectionItemList = {
  loaded: KnockoutObservable<boolean>;
  list: KnockoutObservableArray<CollectionItem>;
  isEmpty: KnockoutComputed<boolean>;
  noResults: KnockoutComputed<boolean>;
  query: KnockoutObservable<string>;

  isHidden: (id: string) => boolean;

  setList: (list: CompanyCollectionItem[]) => void;
  getHierarchy: () => SelectListItem[];

  add: (element: CompanyCollectionItem) => void;
  update: (element: CompanyCollectionItem) => void;
  remove: (elementId: string) => void;

  getElementsCount: () => number;
  reorder: () => CollectionItemsOrder;
  sort: () => void;
  insertAfter: (element: Partial<CompanyCollectionItem>, prev: Partial<CompanyCollectionItem>) => void
};
