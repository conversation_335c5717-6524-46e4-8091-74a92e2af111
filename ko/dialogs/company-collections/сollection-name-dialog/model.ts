import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompanyCollection } from "@/entities/structures/company-collection/types";
import { DialogWrapper } from "@/dialogs/wrapper";
import { errorMatcher } from "@/utils/validation/state-matchers";
import { StateMatcherFn } from "@/utils/validation/state-matchers.types";
import { collectionAction } from "@/api/collections/collection";

interface CollectionNameDialogProps {
  id: string;
  name: string;
  description: string;
  onChange: (data: Partial<CompanyCollection>) => void;
}

export class ViewModel extends DialogWrapper {
  collectionId: string;
  name: KnockoutObservable<string>;
  description: KnockoutObservable<string>;
  title: string;
  isSubmitted: KnockoutObservable<boolean>;
  isPending: KnockoutObservable<boolean>;
  matcher: StateMatcherFn;
  onChange: (data: Partial<CompanyCollection>) => void;

  hide: () => void;

  constructor(params: CollectionNameDialogProps, element: HTMLElement) {
    super(params, element);
    element.classList.add("collection-name-dialog");

    const { id, name = "", description = "", onChange } = params;

    this.collectionId = id;
    this.onChange = onChange;

    this.isSubmitted = ko.observable(false);
    this.isPending = ko.observable(false);
    this.matcher = errorMatcher(this.isSubmitted);

    this.name = ko.observable(name).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });
    this.description = ko.observable(description);

    this.title = this.collectionId
      ? "Редактировать название и описание справочника"
      : "Новый справочник";
  }

  isValid() {
    return this.name.isValid();
  }

  getParams() {
    return {
      id: this.collectionId,
      name: this.name().trim(),
      description: this.description().trim(),
    };
  }

  submit() {
    this.isSubmitted(true);

    if (!this.isValid()) return false;

    this.isSubmitted(false);
    this.isPending(true);

    const params = this.getParams();

    collectionAction(params).then((data) => {
      this.isPending(false);
      this.onChange(data);
      this.hide();
    });
  }
}
