<!-- ko let: { $dialog: $component } -->
<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    <div data-bind="text: $parent.title"></div>
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll">
      <div data-bind="nativeScrollbar">
        <div class="form-group">
          <fc-label params="text: 'Название'"></fc-label>
          <fc-input
            params="placeholder: 'Новый справочник', value: name, maxlength: 150, counter: true, invalid: matcher(name)"
          ></fc-input>
          <fc-error params="show: matcher(name), text: name.error"></fc-error>
        </div>

        <div class="form-group">
          <fc-label params="text: 'Описание', optional: true"></fc-label>
          <fc-textarea
            params="value: description, maxlength: 500, counter: true"
          ></fc-textarea>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="color: 'primary', inverse: true, label: 'Отменить', click: function() { hide() }"
      ></fc-button>

      <fc-button
        params="color: 'success', label: 'Сохранить', click: function() { submit() }"
      ></fc-button>
    </div>
  </div>
</foquz-dialog>
<!-- /ko -->