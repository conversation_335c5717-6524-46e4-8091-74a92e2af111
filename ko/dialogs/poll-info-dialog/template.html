<foquz-dialog params="ref: modal, dialogWrapper: $component" class="mobile-sm">
  <foquz-dialog-header params="empty: true"> </foquz-dialog-header>

  <div class="foquz-dialog__body" data-bind="using: model">
    <div class="f-fs-1">
      <div style="position: relative">
        <div class="f-color-service poll-info-dialog__label" data-bind="text: _t('ID опроса')"></div>
        <div class="d-flex align-items-center">
          <span data-bind="text: id" class="bold"></span>
          <fc-button class="ml-10p" params="size: 'auto', mode: 'text', icon: 'poll-copy'" data-bind="click: function() {
               $parent.copyId();
             }, "></fc-button>
        </div>

        <!-- ko template: {
           foreach: templateIf($parent.copied(), $data),
           afterAdd: fadeAfterAddFactory(400),
           beforeRemove: fadeBeforeRemoveFactory(400)
        } -->
        <div class="copied-message">ID опроса скопирован</div>
        <!-- /ko -->
      </div>

      <div class="mt-20p">
        <div class="f-color-service poll-info-dialog__label" data-bind="text: _t('Создан')"></div>
        <div data-bind="text: createdAt" class="bold"></div>
      </div>
      <!-- ko if: start || finish -->
      <div class="mt-20p">
        <div class="f-color-service poll-info-dialog__label" data-bind="text: _t('Продолжительность')"></div>
        <div>
          <!-- ko if: start -->
          <div data-bind="html: _t('main', 'с {date}', {
              date: '<span class=\'bold\'>' + start + '</span>'
            })"></div>
          <!-- /ko -->
          <!-- ko if: finish -->
          <div data-bind="html: _t('main', 'до {date}', {
              date: '<span class=\'bold\'>' + finish + '</span>'
            })"></div>
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->

      <div class="mt-20p">
        <div class="f-color-service poll-info-dialog__label" data-bind="text: _t('Вопросов / страниц')"></div>
        <div class="bold">
          <span data-bind="text: questions"></span> /
          <span data-bind="text: pages"></span>
        </div>
      </div>
      <!-- ko if: timeToPass -->
      <div class="mt-20p">
        <div class="f-color-service poll-info-dialog__label" data-bind="text: _t('Время прохождения')"></div>
        <div class="bold" data-bind="text: timeToPass"></div>
      </div>
      <!-- /ko -->

      <!-- ko if: !isPublished() || hasPublishedAnswersLimit() -->
      <div class="mt-20p">
        <div class="f-color-service poll-info-dialog__label" data-bind="text: _t('Доступно анкет')"></div>
        <div class="bold" data-bind="text: limitText, css: {
          'f-color-danger': $parent.noAnswersLeft
        }"></div>
        <!-- ko if: !isPublished() -->
        <div class="f-color-danger mt-2"
          data-bind="text: _t('В тестовом режиме доступно только 10 анкет для прохождения')"></div>
        <!-- /ko -->

        <!-- ko if: $parent.noAnswersLeft -->
        <div class="f-color-danger mt-2">
          <div data-bind="text: _t('Достигнут лимит количества ответов.')"></div>
          <div data-bind="text: _t('Опрос недоступен для прохождения.')"></div>
        </div>
        <!-- /ko -->
      </div>
      <!-- /ko -->
    </div>
  </div>
</foquz-dialog>