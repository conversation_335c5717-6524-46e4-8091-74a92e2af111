import { DialogWrapper } from "Dialogs/wrapper";
import { ApiUrl } from "Utils/url/api-url";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.companyId = params.companyId;

    const originalValue = ko.toJS(params.cost) || "";

    this.unlimited = ko.observable(!originalValue);

    this.value = ko.observable(ko.toJS(params.cost) || "").extend({
      required: {
        message: "Обязательное поле",
        onlyIf: () => !this.unlimited(),
      },
      validation: {
        validator: (v) => parseInt(v) > 0,
        message: "Обязательное поле",
        onlyIf: () => !this.unlimited(),
      },
    });
  }

  submit() {
    this.isSubmitted(true);

    this.value(parseInt(this.value()));

    if (!this.value.isValid()) return;

    $.ajax({
      url: ApiUrl("company/change-answer-cost", { companyId: this.companyId }),
      data: {
        answerCost: this.unlimited() ? 0 : this.value(),
      },
      method: "POST",
      success: (response) => {
        this.emitEvent("change-cost", response);
        this.hide();
      },
      error: (response) => {
        console.error(response.responseJSON);
      },
    });
  }
}
