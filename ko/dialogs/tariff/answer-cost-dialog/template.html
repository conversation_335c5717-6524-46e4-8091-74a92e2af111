<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header>
    Изменить стоимость одного ответа сверх лимита
  </foquz-dialog-header>


  <div class="foquz-dialog__body">
    <div class="form-group">
      <label class="form-label">Стоимость, ₽</label>
      <input type="text"
             class="form-control text-center"
             data-bind="onlyNumbers, textInput: value, css: {
          'is-invalid': formControlErrorStateMatcher(value)
        }, event: {
          blur: function() {
            if (value()) { value(parseInt(value())) }
            return true;
          }
        }, disable: unlimited">
      <validation-feedback params="show: formControlErrorStateMatcher(value), text: value.error"></validation-feedback>
    </div>

    <div>
      <fc-switch params="checked: unlimited, label: 'Безлимитный'"></fc-switch>
    </div>
  </div>


  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">

      <button type="button"
              class="f-btn f-btn-link"
              data-bind="
                click: function() {
                  $dialog.hide('close');
                }">Отменить</button>

      <button type="button"
              class="f-btn"
              data-bind="
                  click: function() {
                    submit();
                  }">Сохранить</button>
    </div>
  </div>

</foquz-dialog>
