import { DialogWrapper } from 'Dialogs/wrapper';
import { ApiUrl } from 'Utils/url/api-url';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.companyId = params.companyId;

    this.value = ko.observable(ko.toJS(params.cost) || '').extend({
      required: {
        message: 'Обязательное поле'
      },
    });
  }

  submit() {
    this.isSubmitted(true);

    this.value(parseInt(this.value()));

    if (!this.value.isValid()) return;

    $.ajax({
      url: ApiUrl('company/change-tariff-cost', { companyId: this.companyId }),
      data: {
        tariffCost: this.value() || 0
      },
      method: 'POST',
      success: (response) => {
        this.emitEvent('change-cost', response);
        this.hide();
      },
      error: (response) => {
        console.error(response.responseJSON);
      }
    });
  }
}
