import { DialogWrapper } from 'Dialogs/wrapper';
import { Tariffs, TariffsData } from "@/constants/tariffs"

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.canEdit = params.canEdit;

    this.tariffsDirectory = new Directory('tariffs');

    this.currentTariffId = ko.observable(1);
    this.currentTariff = ko.pureComputed(() => {
      let tariffId = this.currentTariffId();
      if (!tariffId) return null;
      return TariffsData[tariffId];
    });

    this.companyId = params.companyId;
    this.originalTariff = params.tariff;

    this.currentTariffId(this.originalTariff.id);

    this.isBlocked = ko.pureComputed(() => {
      return this.currentTariffId() == this.originalTariff.id;
    });

    this.tariffsDirectory.onLoad(() => {
      this.currentTariffId(this.originalTariff.id);
    });
    this.tariffsDirectory.load();

    this.tariffTemplate = (state) => {
      if (!state.id) {
        return state.text;
      }

      let span = $('<span>').text(state.text);

      if (state.id == this.originalTariff.id) {
        if (this.originalTariff.isBlocked) {
          span.append(
            '<span class="f-color-service f-fs-1"> (текущий, <span class="f-color-danger">заблокирован</span>)</span>'
          );
        } else {
          span.append('<span class="f-color-service f-fs-1"> (текущий)</span>');
        }
      }
      return span;
    };

    this.name = ko.observable('');

    if (CURRENT_USER && CURRENT_USER.name) {
      this.name(CURRENT_USER.name);
    }

    this.phone = ko.observable('').extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => !this.canEdit
      },
      validation: {
        validator: (value) => {
          const pattern = utils.regex.phone;
          const phone = value.trim();
          return pattern.test(phone);
        },
        message: 'Неверный формат',
        onlyIf: () => !this.canEdit
      }
    });

    this.validationObject = ko.validatedObservable(
      {
        phone: this.phone
      },
      { deep: true, liv: true }
    );
  }

  submit() {
    this.isSubmitted(true);

    if (!this.validationObject.isValid()) return;

    let url = this.canEdit
      ? `${APIConfig.baseApiUrlPath}tariffs/change?id=${
          this.companyId
        }&tariffId=${this.currentTariffId()}&access-token=${APIConfig.apiKey}`
      : `${APIConfig.baseApiUrlPath}tariffs/send-request?company_id=${this.companyId}&access-token=${APIConfig.apiKey}`;

    let params = this.canEdit
      ? {}
      : {
          tariff_id: this.currentTariffId(),
          name: this.name(),
          phone: this.phone()
        };
    $.ajax({
      url,
      data: params,
      method: 'POST',
      success: (data) => {
        this.emitEvent('submit', data);
        this.hide();
      }
    });
  }
}
