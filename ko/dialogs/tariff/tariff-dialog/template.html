<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header> Изменить тариф </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <!-- ko ifnot: canEdit || true -->
      <div class="tariff-note">
        <fc-icon params="name: 'exclamation-circle'"></fc-icon>
        Тариф изменится с первого числа следующего месяца
      </div>
      <!-- /ko -->

      <label class="form-label">Выберите тариф</label>

      <!-- ko if: tariffsDirectory.loaded -->
      <div class="select2-wrapper">
        <select
          data-bind="
          value: currentTariffId,
          valueAllowUnset: true,
          lazySelect2: {
            containerCssClass: 'form-control',
            wrapperCssClass: 'select2-container--form-control',
            templateSelection: tariffTemplate,
            templateResult: tariffTemplate,
          }
        "
        >
          <!-- ko foreach: tariffsDirectory.data -->
          <option data-bind="value: id, text: title"></option>
          <!-- /ko -->
        </select>
      </div>

      <div class="mt-3" data-bind="let: { closed: ko.observable(true) }">
        <!-- ko template: {
          foreach: templateIf(closed(), $data),
          afterAdd: slideAfterAddFactory(200),
          beforeRemove: slideBeforeRemoveFactory(200)
        } -->
        <div>
          <div class="d-flex align-items-center justify-content-between">
            <div
              class="f-color-primary cursor-pointer f-fs-1-5"
              data-bind="click: function() {
                closed(false);
              }"
            >
              Условия тарифа
            </div>

            <a href="https://foquz.ru/site/tariffs" target="_blank" class="f-fs-1-5"
              >Подробнее о тарифах</a
            >
          </div>
        </div>
        <!-- /ko -->

        <!-- ko template: {
          foreach: templateIf(!closed(), $data),
          afterAdd: slideAfterAddFactory(200),
          beforeRemove: slideBeforeRemoveFactory(200)
        } -->
        <div>
          <table data-bind="using: currentTariff" class="tariff-data">
            <tbody>
              <tr>
                <td>Стоимость тарифа</td>
                <td data-bind="text: cost"></td>
              </tr>
              <tr>
                <td>Ручные опросы</td>
                <td data-bind="text: manualPolls"></td>
              </tr>
              <tr>
                <td>Автоматические опросы</td>
                <td data-bind="text: autoPolls"></td>
              </tr>
              <tr>
                <td>Лимит на кол-во ответов</td>
                <td data-bind="text: answersLimit"></td>
              </tr>
              <tr>
                <td>Кол-во пользователей</td>
                <td data-bind="text: userLimit"></td>
              </tr>
              <tr>
                <td>Рассылка</td>
                <td data-bind="text: mails"></td>
              </tr>
            </tbody>
          </table>
          <div class="d-flex align-items-center justify-content-between">
            <div
              class="f-color-primary cursor-pointer f-fs-1-5 mt-2"
              data-bind="click: function() {
                closed(true);
              }"
            >
              Свернуть
            </div>

            <a href="/#rates" target="_blank" class="f-fs-1-5"
              >Подробнее о тарифах</a
            >
          </div>
        </div>
        <!-- /ko -->
      </div>
      <!-- /ko -->
    </div>

    <!-- ko ifnot: canEdit -->
    <div class="form-group">
      <div class="d-flex justify-content-between align-items-center">
        <label for="" class="form-label">Ваше имя</label>
        <div class="f-fs-1 f-color-service">необязательное</div>
      </div>

      <input type="text" class="form-control" data-bind="textInput: name" />
    </div>

    <div class="form-group">
      <label for="" class="form-label">Телефон</label>

      <input
        type="text"
        class="form-control"
        data-bind="phoneMask, textInput: phone, css: {
        'is-valid': formControlSuccessStateMatcher(phone),
        'is-invalid': formControlErrorStateMatcher(phone),
      }"
      />

      <!-- ko template: {
        foreach: formControlErrorStateMatcher(phone),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
      <div class="form-error" data-bind="text: $parent.phone.error()"></div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="
                click: function() {
                  $dialog.hide('close');
                }"
      >
        Отменить
      </button>

      <button
        type="button"
        class="f-btn"
        data-bind="
              disable: isBlocked,
                  click: function() {
                    submit();
                  }"
      >
        Сохранить
      </button>
    </div>
  </div>
</foquz-dialog>
