import { DialogWrapper } from 'Dialogs/wrapper';
import OverlayScrollbars from 'overlayscrollbars';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.html = params.html;
    this.frame = ko.observable(null);
  }

  onFrameLoad() {
    let style = {
      'font-family': 'Arial, sans-serif'
    };

    let styleAttr = Object.entries(style)
      .map((e) => `${e[0]}:${e[1]}`)
      .join(';');

    this.frame().contentDocument.body.setAttribute('style', styleAttr);
    OverlayScrollbars(this.frame().contentDocument.body, {});
  }
}
