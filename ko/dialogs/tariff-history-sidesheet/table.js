import { InteractiveTable } from 'Models/interactive-table';
import { SortModel } from 'Models/sort';
import { ApiUrl } from 'Utils/url/api-url';
import { HistoryItem } from './history-item';

export class CompanyTariffHistory extends InteractiveTable {
  constructor(id) {
    super();

    this.id = id;

    this.load();
  }

  getParams() {
    return {
      ...super.getParams(),
      id: this.id
    }
  }

  getSort() {
    return new SortModel('period', true);
  }

  getSearch() {
    return {
      period: ko.observable(''),
      tariff: ko.observable(''),
      have_answers: ko.observable('')
    };
  }

  load() {
    return new Promise((res) => {
      if (this.beforeLoad()) {
        $.ajax({
          method: 'GET',
          url: ApiUrl('affiliate/company', this.getParams()),
          success: (response) => {
            let items = response.items.map((i) => new HistoryItem(i));
            this.afterLoad(items);
            res(items.length > 0);
          },
          error: (response) => {
            // response = response.responseJSON;
            this.onError();
            res(false);
          }
        });
      } else {
        res(false);
      }
    });
  }
}
