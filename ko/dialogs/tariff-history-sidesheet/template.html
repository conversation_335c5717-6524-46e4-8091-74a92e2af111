<sidesheet class="sidesheet--narrow" params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title"
          data-bind="text: company.name">

      </h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container">
        <interactive-table params="table: history">
          <table class="table f-table f-table--searchable f-table-dense">
            <thead>
              <tr>
                <th data-bind="component: {
                name: 'table-head-cell',
                params: {
                  sort: sort,
                  sortName: 'period',
                  withSearch: true,
                  searchValue: search.period,
                  onSearch: function() { reset(); }
                }
              }">Расчетный период</th>
                <th data-bind="component: {
                name: 'table-head-cell',
                params: {
                  sort: sort,
                  sortName: 'tariff',
                  withSearch: true,
                  searchValue: search.tariff,
                  onSearch: function() { reset(); }
                }
              }">Тариф</th>
                <th data-bind="component: {
                name: 'table-head-cell',
                params: {
                  sort: sort,
                  sortName: 'have_answers',
                  withSearch: true,
                  searchValue: search.have_answers,
                  onSearch: function() { reset(); }
                }
              }"
                    align="right">Ответов</th>

              </tr>
            </thead>
            <tbody>
              <!-- ko foreach: { data: items, as: 'item' } -->
              <tr>
                <td data-bind="text: item.period"></td>
                <td data-bind="text: item.title"></td>
                <td align="right">
                  <div data-bind="text: item.answers"></div>
                </td>
              </tr>
              <!-- /ko -->
            </tbody>
          </table>
        </interactive-table>
      </div>
    </div>
  </div>


</sidesheet>
