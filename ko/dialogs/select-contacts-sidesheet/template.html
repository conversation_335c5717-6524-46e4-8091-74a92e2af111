<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">Выбрать контакты</h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container">
        <div class="pb-4">
          <div class="row">
            <div class="col-6">
              <div class="contact-filters">
                <div class="contact-filters__header pb-15p border-bottom">
                  <div class="mb-15p">
                    <h2 class="f-h2 pb-2">
                      Фильтры

                      <!-- ko if: filters.list().length > 0 -->
                      <span
                        class="font-weight-normal f-color-service"
                        data-bind="text: filters.list().length"
                      ></span>
                      <!-- /ko -->
                    </h2>
                    <div class="f-color-service f-fs-1">
                      Добавление контактов по условиям
                    </div>
                  </div>
                  <button
                    class="f-btn f-btn-success f-btn-text flex-shrink-0"
                    type="button"
                    data-bind="click: function() { filters.addCondition() }, disable: blockAddFilterButton"
                  >
                    <span class="f-btn-prepend">
                      <svg-icon params="name: 'plus'"></svg-icon>
                    </span>

                    <span>Добавить условие</span>
                  </button>
                </div>

                <div>
                  
                  <fc-contact-conditions
                    params="conditions: filters.list, showErrors: isSubmitted,
                      onRemove: function(condition) {
                        filters.removeCondition(condition)
                      }, types: filtersTypes"
                  ></fc-contact-conditions>

                  <!-- ko ifnot: filters.list().length -->
                  <div class="f-color-service f-fs-1 mt-25p">Если фильтры не выбраны, будут добавлены все имеющиеся контакты</div>
                  <!-- /ko -->
                </div>

                <!-- ko if: hasFilters -->
                <button
                  class="f-btn f-btn-success f-btn-text mt-25p flex-shrink-0"
                  type="button"
                  data-bind="click: function() { filters.addCondition() }, disable: blockAddFilterButton"
                >
                  <span class="f-btn-prepend">
                    <svg-icon params="name: 'plus'"></svg-icon>
                  </span>

                  <span>Добавить условие</span>
                </button>
                <!-- /ko -->
              </div>
            </div>
            <div class="col-6">
              <div class="contact-answers" data-bind="using: answers">
                <div class="contact-answers__header pb-15p border-bottom">
                  <div class="mb-15p">
                    <h2 class="f-h2 pb-2">
                      Ответы в опросах

                      <!-- ko if: items().length > 0 -->
                      <span
                        class="font-weight-normal f-color-service"
                        data-bind="text: items().length"
                      ></span>
                      <!-- /ko -->
                    </h2>
                    <div class="f-color-service f-fs-1">
                      Добавление контактов по наличию ответов в опросах
                    </div>
                  </div>
                  <button
                    class="f-btn f-btn-success f-btn-text flex-shrink-0"
                    type="button"
                    data-bind="click: addItem"
                  >
                    <span class="f-btn-prepend">
                      <svg-icon params="name: 'plus'"></svg-icon>
                    </span>

                    <span>Добавить условие</span>
                  </button>
                </div>

                <!-- ko foreach: { data: items, afterAdd: slideAfterAddFactory(400), beforeRemove: slideBeforeRemoveFactory(400) } -->

                <contact-answer
                  params="answer: $data, formControlErrorStateMatcher: $parent.formControlErrorStateMatcher, onRemove: function() {
                    $parent.removeItem($data)
                  }"
                ></contact-answer>
                <!-- /ko -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() {
                  $dialog.hide();
                }"
      >
        <foquz-icon params="icon: 'bin'" class="f-btn-prepend"></foquz-icon>
        Отменить
      </button>

      <!-- ko ifnot: isAdding -->
      <button
        type="submit"
        data-bind="click: function() { submit() }"
        class="f-btn f-btn-success"
      >
        <!-- ko text: buttonText -->
        <!-- /ko -->
      </button>
      <!-- /ko -->

      <!-- ko if: isAdding -->
      <div
        class="ml-2"
        data-bind="component: {
            name: 'progress-bar',
            params: { value: addingProgress(), theme: 'success' } }"
      ></div>
      <!-- /ko -->
    </div>
  </div>
</sidesheet>
