
  <button class="contact-answer__remove f-btn f-btn--square f-btn-danger"
          type="button"
          data-bind="click: onRemove, tooltip"
          title="Удалить">
    <svg-icon params="name: 'times'" class="svg-icon--sm"></svg-icon>
  </button>

  <div class="mb-20p">
    <label class="form-label">Опрос</label>

    <div class="select2-wrapper"
        data-bind="css: {
          'is-invalid': formControlErrorStateMatcher(answer.polls) && answer.polls.error
        }">
      <polls-select params="value: answer.polls, multiple: true, placeholder: 'Выберите опрос'"></polls-select>

      <validation-feedback params="show: formControlErrorStateMatcher(answer.polls), text: answer.polls.error"></validation-feedback>
    </div>

  </div>

  <div class="">
    <label class="form-label">
      Условие
      <question-button params="text: 'Условие'"></question-button>
    </label>

    <div class="select2-wrapper">
      <select data-bind="value: answer.condition,
        valueAllowUnset: true,
        select2: {
          containerCssClass: 'form-control',
          wrapperCssClass: 'select2-container--form-control'
        }">
        <option value="1">Есть ответы на любой из перечисленных</option>
        <option value="2">Есть ответы на все перечисленные</option>
        <option value="3">Нет ответов на любой из перечисленных</option>
        <option value="4">Нет ответов на все перечисленные</option>
      </select>
    </div>
  </div>
