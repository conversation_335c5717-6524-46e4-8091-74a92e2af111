import { DialogWrapper } from "Dialogs/wrapper";
import { BirthdayFilterModel } from "Legacy/components/birthday-filter";
import { DatePeriodFilter } from "Components/date-period-filter";
import { getClientFields } from "Legacy/utils/client-fields";
import { Answers } from "./answers";
import "Components/input/select/polls-select";
import "./contact-answer";
import { FilialsDataCollection } from "Models/data-collection/filials";
import { formatClientDateToServer } from "Utils/date/format";
import { formatPeriodToArray } from "Utils/date/period";

const STATUS_STARTING = 1;
const STATUS_PROCESSING = 2;
const STATUS_FAILED = 3;
const STATUS_DONE = 4;

const getContactsCountUrl = `/foquz/foquz-contact/get-contacts-count`;

function dateFilter() {
  let type = ko.observable("");
  let date = ko.observable("");
  let period = ko.observable("");

  let cbs = [];

  [type, date, period].forEach((f) => {
    f.subscribe((_) => {
      cbs.forEach((cb) => cb());
    });
  });

  return {
    type,
    date,
    period,
    onChange: (cb) => {
      cbs.push(cb);
    },
    getParams: () => {
      if (!type()) return;

      if (!date() && !period()) return;

      let [from, to] = formatPeriodToArray(period());

      return {
        type: type(),
        date: formatClientDateToServer(date()),
        period: {
          date1: from,
          date2: to,
        },
      };
    },
  };
}
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.collections = {
      filials: new FilialsDataCollection(),
    };
    Object.values(this.collections).forEach((c) => c.load());

    this.clientFields = getClientFields();
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.initializing = ko.observable(true);
    this.onSubmit = params.data.onSubmit;
    this.formatStats = params.data.formatStats;

    this.isContactsCountLoading = ko.observable(false);
    this.contactsCount = ko.observable("");
    this.totalContactsCount = ko.observable(0);
    this.statId = ko.observable(null);

    this.isAdding = ko.observable(false);
    this.addingProgress = ko.observable(0);
    this.statsRequests = [];

    this.pushTokenOptions = [
      { id: "1", text: "Активный" },
      { id: "0", text: "Отсутствует" },
    ];

    this.filters = {
      query: ko.observable(""),
      genders: ko.observableArray([]),
      tags: ko.observableArray([]),
      exceptTags: ko.observableArray([]),
      filled: ko.observable(1),
      fields: ko.observableArray([]),
      filials: ko.observableArray([]),
      filialsExcept: ko.observable(false),
      pushToken: ko.observable(''),
    };

    this.filters.filials.subscribe((v) => {
      if (!v.length) this.filters.filialsExcept(false);
    });

    this.birthdayFilter = new BirthdayFilterModel({
      format: "YYYY-MM-DD",
    });

    this.createdAtFilter = dateFilter();
    this.updatedAtFilter = dateFilter();

    this.tagsDirectory = params.data.tagsDirectory;

    const getTags = () => {
      const tagsWithConditions = this.tagsDirectory.getTagsWithConditions();
      const tagsWithoutConditions =
        this.tagsDirectory.getTagsWithoutConditions();

      return [
        {
          text: "С условием",
          id: "with-condition",
          category: true,
          items: tagsWithConditions.map((t) => {
            return {
              ...t,
              text: t.name,
            };
          }),
        },
        {
          text: "Без условия",
          id: "without-condition",
          category: true,
          items: tagsWithoutConditions.map((t) => {
            return {
              ...t,
              text: t.name,
            };
          }),
        },
      ];
    };

    this.tagsList = ko.observableArray(getTags());

    this.statsUrl =
      params.data.statsUrl ||
      `${APIConfig.baseApiUrlPath}mailing-lists/get-link-contacts-stat?access-token=${APIConfig.apiKey}`;

    const getCount = utils.debounce(() => {
      this.getCount();
    }, 400);

    this.onFilterChange = () => {
      getCount();
    };

    this.isFiltersEmpty = ko.pureComputed(() => {
      if (this.filters.query().length >= 2) return false;
      if (this.filters.genders().length) return false;
      if (this.filters.tags().length) return false;
      if (this.filters.exceptTags().length) return false;
      if (this.filters.filials().length) return false;
      if (this.birthdayFilter.type()) return false;
      if (this.filters.fields().length) return false;
      if (this.answers.getData().length) return false;
      if (this.createdAtFilter.type()) return false;
      if (this.updatedAtFilter.type()) return false;
      if (this.filters.pushToken()) return false;

      return true;
    });

    this.buttonText = ko.pureComputed(() => {
      if (this.isFiltersEmpty()) {
        if (this.isContactsCountLoading()) return "Добавить все (...)";
        return `Добавить все (${this.totalContactsCount()})`;
      }
      if (this.isContactsCountLoading()) return "Добавить ... контактов";

      return `Добавить ${this.contactsCount()} ${utils.declOfNum(
        this.contactsCount(),
        ["контакт", "контакта", "контактов"]
      )}`;
    });

    this.answers = new Answers();
    this.answers.on("change", () => {
      getCount();
    });

    this.createdAtFilter.onChange(() => getCount());
    this.updatedAtFilter.onChange(() => getCount());

    [
      this.filters.query,
      this.filters.genders,
      this.filters.tags,
      this.filters.exceptTags,
      this.birthdayFilter,
      this.filters.filled,
      this.filters.fields,
      this.filters.filials,
      this.filters.filialsExcept,
      this.filters.pushToken,
    ].forEach((f) => f.subscribe((_) => getCount()));
  }
  getSearchParams() {
    let params = {
      contact: this.filters.query(),
      tagsOperation: 1,
      tags: this.filters.tags(),
      tags_except: this.filters.exceptTags(),
      filials: this.filters.filials(),
      filial_except: this.filters.filialsExcept() ? 1 : 0,
      gender: this.filters.genders(),
      birthday: this.birthdayFilter.getParams(),
      createdAt: this.createdAtFilter.getParams(),
      updatedAt: this.updatedAtFilter.getParams(),
      token: this.filters.pushToken(),
    };

    params.answers = this.answers.getData();

    if (this.filters.fields().length) {
      params.records = {
        filled: this.filters.filled(),
        fields: this.filters.fields(),
      };
    }

    return params;
  }

  getCount() {
    //if (this.isFiltersEmpty()) return;

    this.isContactsCountLoading(true);
    return new Promise((res) => {
      let params = this.getSearchParams();
      $.ajax({
        url: `/foquz/foquz-contact/get-contacts-count`,
        data: {
          page: -1,
          advanced: {
            contact: params.contact,
            tags: params.tags,
            tags_except: params.tags_except,
            filials: params.filials,
            filial_except: params.filial_except,
            birthday: params.birthday,
            createdAt: params.createdAt,
            updatedAt: params.updatedAt,
            gender: params.gender,
            token: params.token,
          },
          answers: params.answers,
          records: params.records,
        },
        method: "GET",
        success: (response) => {
          this.contactsCount(response.data && response.data.total);
          this.totalContactsCount(
            response.data && response.data.totalWithoutFilters
          );
          this.isContactsCountLoading(false);
        },
        error: (response) => {
          this.isContactsCountLoading(false);
          console.error(response.responseJSON);
        },
      });
    });
  }

  abortAllRequests() {
    this.statsRequests.forEach((xhr) => xhr.abort());
    this.statsRequests = [];
  }

  getStatRequest() {
    return new Promise((res, rej) => {
      const xhr = $.ajax({
        method: "GET",
        url: this.statsUrl,
        data: {
          stat_id: this.statId(),
        },
        success: (data) => {
          if (!this.isAdding()) {
            rej();
            return;
          }

          if (typeof this.formatStats == "function") {
            data = this.formatStats(data);
          }

          if (data.status != "ok") {
            rej();
            return;
          }

          const stat = data.stat;
          const status = stat.status;

          if (status == STATUS_FAILED) {
            rej();
            return;
          }

          if (status == STATUS_DONE) {
            res({
              progress: 100,
            });
            return;
          }

          if (!("total" in stat)) {
            rej();
            return;
          }

          // if (stat.total == 0) {
          //   res({
          //     progress: 100,
          //   });
          //   return;
          // }

          // при загрузке ВСЕХ КОНТАКТОВ в поле total несколько первых раз может быть 0
          // поэтому нельзя прерывать опрашивание
          if (stat.total == 0) {
            res({
              progress: 0,
            });
            return;
          }

          let progress = Math.min(
            ((stat.inserted + stat.duplicated) * 100) / stat.total,
            100
          );

          res({
            progress: progress,
          });
        },
        error: (response) => {
          rej();
        },
      });
      this.statsRequests.push(xhr);
    });
  }

  getStat() {
    let intervalId = setInterval(() => {
      this.getStatRequest()
        .then((res) => {
          if (res.progress >= 100) {
            this.addingProgress(100);
            this.isAdding(false);
            clearInterval(intervalId);
            this.hide();
          } else {
            if (this.addingProgress() < res.progress)
              this.addingProgress(res.progress);
          }
        })
        .catch(() => {
          if (this.isAdding() == false) return;

          this.isAdding(false);
          clearInterval(intervalId);
          console.error("Ошибка загрузки данных");

          this.abortAllRequests();
        });
    }, 800);

    // setTimeout(() => {
    //   if (this.isAdding()) {
    //     this.isAdding(false);
    //     this.abortAllRequests();

    //     clearInterval(intervalId);
    //     alert('Превышено время загрузки данных');
    //   }
    // }, 60000);
  }

  submit() {
    this.isSubmitted(true);

    if (!this.birthdayFilter.isValid()) return false;
    if (!this.answers.isValid()) return false;

    this.isAdding(true);
    this.addingProgress(0);

    let params = this.getSearchParams();
    console.log("ON SUBMIT", params);

    this.onSubmit(params).then((statId) => {
      this.statId(statId);
      this.getStat();
    });
  }

  onRender() {
    this.initializing(false);
  }
}
