import { ViewModel } from './model';
import html from './template.html';
import './style.less';


import * as ConditionsList from '@/presentation/views/fc-conditions/contact-conditions-list';

import { registerComponent } from '@/utils/engine/register-component';

registerComponent('fc-contact-conditions', ConditionsList);

ko.components.register('select-contacts-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('select-contacts-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
