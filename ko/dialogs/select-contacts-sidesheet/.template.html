<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">Выбрать контакты</h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container">
        <div class="pb-4">
          <div class="row">
            <div class="col-6">
              <div class="form-group">
                <label class="form-label">Фамилия, телефон или email</label>

                <foquz-chars-counter params="value: filters.query, max: 500">
                  <input
                    class="form-control"
                    data-bind="
                  textInput: $parent.filters.query"
                    maxlength="500"
                  />
                </foquz-chars-counter>
              </div>

              <div class="form-group">
                <label class="form-label">Пол</label>

                <div class="select2-wrapper">
                  <select
                    multiple
                    data-bind="
                    selectedOptions: filters.genders,
                    valueAllowUnset: true,
                    lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      minimumResultsForSearch: 0,
                      placeholder: 'Не выбран'
                    }
                    " data-placeholder="Не выбран"
                  >
                    <option></option>
                    <option value="1">Мужской</option>
                    <option value="2">Женский</option>
                    <option value="3">Не указан</option>
                  </select>
                </div>
              </div>

              <!-- ko component: {
                name: 'birthday-filter',
                params: {
                  isDense: false,
                  model: birthdayFilter,
                  showErrors: isSubmitted
                }
              } -->
              <!-- /ko -->

              <date-period-filter
                params="label: 'По дате добавления', model: createdAtFilter"
              ></date-period-filter>
              <date-period-filter
                params="label: 'По дате обновления', model: updatedAtFilter"
              ></date-period-filter>

              <div class="form-group">
                <label class="form-label"
                  >Филиал контакта
                  <!-- ko if: filters.filialsExcept -->
                  <span class="font-weight-normal">(все, кроме)</span>
                  <!-- /ko -->
                </label>

                <div class="filials-select">
                  <fc-select
                    class="categorized"
                    params="multiple: true,
                      options: collections.filials.list, 
                      value: filters.filials, 
                      placeholder: 'Не выбран',
                      blockSelectedGroup: true,
                      "
                  ></fc-select>

                  <fc-select-except
                    params="value: filters.filialsExcept, selected: filters.filials"
                  ></fc-select-except>
                </div>
              </div>

              <div class="form-group">
                <label class="form-label">Включить контакты с тегами</label>

                <fc-select
                  class="categorized"
                  params="multiple: true,
                    options: tagsList, 
                    value: filters.tags, 
                    placeholder: 'Не выбраны',
                    blockSelectedGroup: true,
                    "
                ></fc-select>
              </div>

              <div class="form-group">
                <label class="form-label">Исключить контакты с тегами</label>

                <fc-select
                  class="categorized"
                  params="multiple: true,
                    options: tagsList, 
                    value: filters.exceptTags, 
                    placeholder: 'Не выбраны',
                    blockSelectedGroup: true,
                    "
                ></fc-select>
              </div>

              <div class="form-group">
                <label class="form-label"
                  >Данные контакта
                  <button
                    class="btn-question"
                    data-bind="tooltip"
                    title="Данные контакта"
                  ></button>
                </label>
                <div class="hat-radio-group form-control">
                  <div class="hat-radio-group__radio">
                    <input
                      type="radio"
                      class="hat-radio-group__radio-input"
                      name="fields"
                      id="filled-in"
                      data-bind="value: 1, checked: filters.filled"
                    />
                    <label for="filled-in" class="hat-radio-group__radio-label">
                      <span class="hat-radio-group__radio-indicator"></span>
                      Заполнены
                    </label>
                  </div>

                  <div class="hat-radio-group__radio">
                    <input
                      type="radio"
                      class="hat-radio-group__radio-input"
                      name="fields"
                      id="not-filled-in"
                      data-bind="value: 0, checked: filters.filled"
                    />
                    <label
                      for="not-filled-in"
                      class="hat-radio-group__radio-label"
                    >
                      <span class="hat-radio-group__radio-indicator"></span>
                      Не заполнены
                    </label>
                  </div>
                </div>

                <!-- ko if: clientFields.loaded() -->
                <div class="select2-wrapper mt-3">
                  <select
                    multiple
                    data-bind="
                    selectedOptions: filters.fields,
                    lazySelect2: {
                      //templateSelection: optionTemplate,
                      //templateResult: optionTemplate,
                      containerCssClass: 'form-control',
                      wrapperCssClass:
                        'select2-container--form-control',
                      minimumResultsForSearch: 0,
                      placeholder: 'Выберите поля'
                    }"
                    data-placeholder="Выберите поля"
                  >
                    <optgroup label="Системные">
                      <!-- ko foreach: {data: clientFields.system, as: 'field'} -->
                      <option
                        data-bind="text: field.text, value: field.id"
                      ></option>
                      <!-- /ko -->
                    </optgroup>
                    <optgroup label="Пользовательские">
                      <!-- ko foreach: {data: clientFields.additional, as: 'field'} -->
                      <option
                        data-bind="text: field.text, value: field.id"
                      ></option>
                      <!-- /ko -->
                    </optgroup>
                  </select>
                </div>
                <!-- /ko -->
              </div>

              <div class="form-group">
                <label class="form-label"
                  >Токен клиента для push-уведомлений</label
                >
                <fc-select
                  params="
                    options: pushTokenOptions, 
                    value: filters.pushToken, 
                    placeholder: 'Не выбран',
                    clearable: true
                    "
                ></fc-select>
              </div>
            </div>
            <div class="col-6">
              <div class="contact-answers" data-bind="using: answers">
                <div class="contact-answers__header pb-15p border-bottom">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h2 class="f-h2 pb-2">
                        Ответы в опросах

                        <!-- ko if: items().length > 0 -->
                        <span
                          class="font-weight-normal f-color-service"
                          data-bind="text: items().length"
                        ></span>
                        <!-- /ko -->
                      </h2>
                      <div class="f-color-service f-fs-1">
                        Добавление контактов по наличию ответов в опросах
                      </div>
                    </div>
                    <button
                      class="f-btn f-btn-success f-btn-text ml-3 flex-shrink-0"
                      type="button"
                      data-bind="click: addItem"
                    >
                      <span class="f-btn-prepend">
                        <svg-icon params="name: 'plus'"></svg-icon>
                      </span>

                      <span>Добавить условие</span>
                    </button>
                  </div>
                </div>

                <!-- ko foreach: { data: items, afterAdd: slideAfterAddFactory(400), beforeRemove: slideBeforeRemoveFactory(400) } -->

                <contact-answer
                  params="answer: $data, formControlErrorStateMatcher: $parent.formControlErrorStateMatcher, onRemove: function() {
                    $parent.removeItem($data)
                  }"
                ></contact-answer>
                <!-- /ko -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() {
                  $dialog.hide();
                }"
      >
        <foquz-icon params="icon: 'bin'" class="f-btn-prepend"></foquz-icon>
        Отменить
      </button>

      <!-- ko ifnot: isAdding -->
      <button
        type="submit"
        data-bind="click: function() { submit() }"
        class="f-btn f-btn-success"
      >
        <!-- ko text: buttonText -->
        <!-- /ko -->
      </button>
      <!-- /ko -->

      <!-- ko if: isAdding -->
      <div
        class="ml-2"
        data-bind="component: {
            name: 'progress-bar',
            params: { value: addingProgress(), theme: 'success' } }"
      ></div>
      <!-- /ko -->
    </div>
  </div>
</sidesheet>
