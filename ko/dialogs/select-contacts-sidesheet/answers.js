import ee from 'event-emitter';

class Answer {
  constructor(ctx) {
    this.polls = ko.observableArray([]).extend({
      required: {
        message: 'Обязательный параметр'
      }
    });
    this.condition = ko.observable('1');

    this.polls.subscribe((v) => ctx.emit('change'));
    this.condition.subscribe((v) => ctx.emit('change'));

    this.isValid = this.polls.isValid;
  }

  getData() {
    return {
      polls: this.polls(),
      condition: this.condition()
    };
  }
}

export class Answers {
  constructor() {
    ee(this);
    this.items = ko.observableArray([]);

    this.items.subscribe(() => this.emit('change'));

    this.isValid = ko.computed(() => {
      return !this.items().some(i => !i.isValid())
    });
  }

  addItem() {
    let answer = new Answer(this);
    this.items.push(answer);
  }

  removeItem(item) {
    this.items.remove(item);
  }

  getData() {
    return this.items().filter((i) => i.isValid()).map((i) => i.getData());
  }
}
