import { PushTokenStates } from "@/constants/client/push-token-state";
import { BirthdayFilterTypes } from "@/entities/structures/birthday-filter/types";
import {
  ContactConditionValue,
  ContactFilterArrayValue,
  ContactFilterBirthdayValue,
  ContactFilterContactFieldsValue,
  ContactFilterDatePeriodValue,
  ContactFilterDishValue,
  ContactFilterOrderValue,
  ContactFilterRangeValue,
  ContactFilterTokenValue,
} from "@/entities/structures/conditions/conditions.types";
import { ContactFilterType } from "@/entities/structures/conditions/contact-filter-types";
import { DatePeriodFilterTypes } from "@/entities/structures/date-period-filter/types";
import { ClientDate } from "@/types";
import {
  ContactFilterOrderDateValue,
  ContactFilterPromocodeValue,
} from "../../../entities/structures/conditions/conditions.types";
import {
  ContactFilterFilialsValue,
  ContactFilterStringValue,
} from "../../../entities/structures/conditions/conditions.types";

type DatePeriodContactFilterVars = {
  type: DatePeriodFilterTypes;
  date?: ClientDate;
  period?: {
    date1: ClientDate;
    date2: ClientDate;
  };
};

type ContactsFiltersVars = {
  advanced: {
    contact?: string;
    gender?: string;
    filials?: Array<string>;
    filial_except?: 0 | 1;
    tags?: Array<string>;
    tags_except?: Array<string>;
    birthday?: {
      type: BirthdayFilterTypes;
      date?: ClientDate;
      period?: {
        date1: ClientDate;
        date2: ClientDate;
      };
      month?: string;
      age?: string;
      day?: string;
    };
    createdAt?: DatePeriodContactFilterVars;
    updatedAt?: DatePeriodContactFilterVars;
    token?: PushTokenStates;
  };
  conditions: Array<{
    id: ContactFilterType;
    [key: string]: any;
  }>;
  records?: {
    fields: Array<string>;
    filled: 0 | 1;
  };
};

function formatCondition(condition: ContactConditionValue) {
  const { type, value } = condition;
  const conditionFilterValue: {
    [key: string]: any;
  } = {};

  switch (type) {
    case ContactFilterType.LastOrderDate:
    case ContactFilterType.Complaint:
      const filterValue = value as ContactFilterOrderDateValue;
      const { criteria, date, period, from, to, complaint } = filterValue;

      conditionFilterValue.criterion = criteria;

      if (date) {
        conditionFilterValue.value = date;
      } else if (period) {
        conditionFilterValue.value = `${period.from}-${period.to}`;
      } else {
        conditionFilterValue.value = [from, to];
      }

      if (type === ContactFilterType.Complaint) {
        conditionFilterValue.complaint = complaint ? 1 : 0;
      }
      break;
    case ContactFilterType.AvgCheck:
    case ContactFilterType.AvgOrdersNumberPerMonth:
    case ContactFilterType.AvgOrdersNumberPerYear:
    case ContactFilterType.RevenueForMonth:
    case ContactFilterType.RevenueForYear:
    case ContactFilterType.PollsParticipation:
    case ContactFilterType.UsedPromocodesPercent:
      conditionFilterValue.value = value as ContactFilterRangeValue;
      break;
    case ContactFilterType.OrderDays:
    case ContactFilterType.OrderTime:
      conditionFilterValue.value = value as ContactFilterArrayValue;
      break;
    case ContactFilterType.FavoriteDish:
      const dishesValue = value as ContactFilterDishValue;
      const dishes = dishesValue.dishes.filter((dish) => dish[0] !== "c");
      const categories = dishesValue.dishes
        .filter((dish) => dish[0] === "c")
        .map((dish) => dish.slice(1));
      conditionFilterValue.dishes = dishes;
      conditionFilterValue.categories = categories;
      conditionFilterValue.minPrice = dishesValue.minPrice;
      break;
    case ContactFilterType.OrderType:
    case ContactFilterType.SourceType:
      const orderValue = value as ContactFilterOrderValue;
      conditionFilterValue.value = orderValue.history;
      if (type === ContactFilterType.OrderType) {
        conditionFilterValue.orderType = orderValue.orderType;
      } else {
        conditionFilterValue.sourceType = orderValue.sourceType;
      }
      break;
    case ContactFilterType.Filial:
      const filialsValue = value as ContactFilterFilialsValue;
      conditionFilterValue.value = filialsValue.filials;
      conditionFilterValue.withoutSelected = filialsValue.except ? 1 : 0;
      conditionFilterValue.type = filialsValue.history;
      break;
    case ContactFilterType.Promocode:
      const promocodeValue = value as ContactFilterPromocodeValue;
      conditionFilterValue.pools = promocodeValue.pools;
      conditionFilterValue.used = promocodeValue.used ? 1 : 0;
      break;
    case ContactFilterType.ContactData:
      const fieldsValue = value as ContactFilterContactFieldsValue;
      conditionFilterValue.values = fieldsValue.fields;
      conditionFilterValue.filled = fieldsValue.filled ? 1 : 0;
      break;
  }

  return {
    id: type,
    ...conditionFilterValue,
  };
}

export function formatConditions(conditions: ContactConditionValue[]) {
  const filters: ContactsFiltersVars = {
    advanced: {},
    conditions: [],
  };

  conditions.forEach((condition) => {
    const { type, value } = condition;

    switch (type) {
      case ContactFilterType.Search:
        filters.advanced.contact = value as ContactFilterStringValue;
        break;
      case ContactFilterType.Gender:
        filters.advanced.gender = value as ContactFilterStringValue;
        break;
      case ContactFilterType.ClientFilial:
        const filialsValue = value as ContactFilterFilialsValue;
        filters.advanced.filials = filialsValue.filials;
        filters.advanced.filial_except = filialsValue.except ? 1 : 0;
        break;
      case ContactFilterType.TagsInclude:
        filters.advanced.tags = value as ContactFilterArrayValue;
        break;
      case ContactFilterType.TagsExclude:
        filters.advanced.tags_except = value as ContactFilterArrayValue;
        break;
      case ContactFilterType.Birthday:
        const birthdayValue = value as ContactFilterBirthdayValue;
        filters.advanced.birthday = {
          type: birthdayValue.type,
          date: birthdayValue.date,
          day: birthdayValue.day,
          month: birthdayValue.month,
          age: birthdayValue.age,
        };
        if (birthdayValue.period) {
          filters.advanced.birthday.period = {
            date1: birthdayValue.period.from,
            date2: birthdayValue.period.to,
          };
        }
        break;
      case ContactFilterType.CreatedAt:
      case ContactFilterType.UpdatedAt:
        const datePeriodValue = value as ContactFilterDatePeriodValue;
        const datePeriodFilterValue: DatePeriodContactFilterVars = {
          type: datePeriodValue.type,
          date: datePeriodValue.date,
        };
        if (datePeriodValue.period) {
          datePeriodFilterValue.period = {
            date1: datePeriodValue.period.from,
            date2: datePeriodValue.period.to,
          };
        }
        if (type === ContactFilterType.CreatedAt) {
          filters.advanced.createdAt = datePeriodFilterValue;
        } else {
          filters.advanced.updatedAt = datePeriodFilterValue;
        }
        break;
      case ContactFilterType.PushToken:
        filters.advanced.token = value as ContactFilterTokenValue;
        break;
      default:
        filters.conditions.push(formatCondition(condition));
        break;
    }
  });

  return filters;
}

export function parseConditions(filters: ContactsFiltersVars): ContactConditionValue[] {
  const conditions: ContactConditionValue[] = [];

  for (const type in filters.advanced) {
    switch (type) {
      case 'contact':
        conditions.push({
          type: ContactFilterType.Search,
          value: filters.advanced.contact as ContactFilterStringValue
        });
        break;
      case 'gender':
        conditions.push({ type: ContactFilterType.Gender, value: filters.advanced.gender as ContactFilterStringValue });
        break;
      case 'filials':
        conditions.push({
          type: ContactFilterType.ClientFilial,
          value: {
            filials: filters.advanced.filials,
            except: filters.advanced.filial_except
          } as ContactFilterFilialsValue
        });
        break;
      case 'tags':
        filters.advanced.tags = [];
        conditions.push({
          type: ContactFilterType.TagsInclude,
          value: filters.advanced.tags as ContactFilterArrayValue
        });
        break;
      case 'tags_except':
        filters.advanced.tags_except = [];
        conditions.push({
          type: ContactFilterType.TagsExclude,
          value: filters.advanced.tags_except as ContactFilterArrayValue
        });
        break;
      case 'birthday':
        conditions.push({
          type: ContactFilterType.Birthday,
          value: {
            type: filters.advanced.birthday.type,
            date: filters.advanced.birthday.date,
            day: filters.advanced.birthday.day,
            month: filters.advanced.birthday.month,
            age: filters.advanced.birthday.age,
            period: filters.advanced.birthday.period
              ? {
                from: filters.advanced.birthday.period.date1,
                to: filters.advanced.birthday.period.date2,
              }
              : undefined,
          } as ContactFilterBirthdayValue
        });
        break;
      case 'createdAt':
      case 'updatedAt':
        conditions.push({
          type: type === 'createdAt' ? ContactFilterType.CreatedAt : ContactFilterType.UpdatedAt,
          value: {
            type: filters.advanced[type].type,
            date: filters.advanced[type].date,
            period: filters.advanced[type].period
              ? {
                from: filters.advanced[type].period.date1,
                to: filters.advanced[type].period.date2,
              }
              : undefined,
          } as ContactFilterDatePeriodValue,
        });
        break;
      case 'token':
        conditions.push({
          type: ContactFilterType.PushToken,
          value: filters.advanced.token as ContactFilterTokenValue
        });
        break;
      default:
        break;
    }
  }
  if (filters.conditions) {

    filters.conditions.forEach(condition => {
      conditions.push({ type: condition.id, value: condition as any });
    });
  }
  console.log('conditions parsed', conditions);
  return conditions;
}
