import { ContactFilterNames } from "@/constants/client/filter-names";
import { ContactFilterType } from "@/entities/structures/conditions/contact-filter-types";

export const ContactFilterTypes = [
  {
    id: "personal",
    text: "Личные данные",
    category: true,
    items: [
      ContactFilterType.Search,
      ContactFilterType.Gender,
      ContactFilterType.Birthday,
      ContactFilterType.CreatedAt,
      ContactFilterType.UpdatedAt,
      ContactFilterType.ClientFilial,
      ContactFilterType.ContactData,
      ContactFilterType.TagsInclude,
      ContactFilterType.TagsExclude,
    ].map((type) => {
      return {
        id: type,
        text: ContactFilterNames[type],
      };
    }),
  },
  {
    id: "order",
    text: "Заказ",
    category: true,
    items: [
      ContactFilterType.LastOrderDate,
      ContactFilterType.Complaint,
      ContactFilterType.AvgOrdersNumberPerYear,
      ContactFilterType.AvgOrdersNumberPer<PERSON><PERSON>h,
      ContactFilterType.RevenueForYear,
      ContactFilterType.RevenueFor<PERSON>onth,
      ContactFilterType.AvgCheck,
      ContactFilterType.OrderDays,
      ContactFilterType.OrderTime,
      ContactFilterType.FavoriteDish,

      ContactFilterType.OrderType,
      ContactFilterType.SourceType,
      ContactFilterType.Filial,
    ].map((type) => {
      return {
        id: type,
        text: ContactFilterNames[type],
      };
    }),
  },
  {
    id: "other",
    text: "Прочее",
    category: true,
    items: [
      ContactFilterType.PollsParticipation,
      ContactFilterType.UsedPromocodesPercent,
      ContactFilterType.Promocode,
      ContactFilterType.PushToken,
    ].map((type) => {
      return {
        id: type,
        text: ContactFilterNames[type],
      };
    }),
  },
];
