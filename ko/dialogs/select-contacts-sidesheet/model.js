import { DialogWrapper } from "Dialogs/wrapper";

import { Answers } from "./answers";
import "Components/input/select/polls-select";
import "./contact-answer";
import { ContactFilterTypes } from "./constants";
import { ContactConditionsModel } from "@/entities/structures/conditions/";
import { isAutoPollsEnabled } from "@/api/settings/check-auto-polls";
import { formatConditions, parseConditions } from "./utils/format-conditions";
import { ContactFilterType } from "../../entities/structures/conditions/contact-filter-types";

const STATUS_STARTING = 1;
const STATUS_PROCESSING = 2;
const STATUS_FAILED = 3;
const STATUS_DONE = 4;

const getContactsCountUrl = `/foquz/foquz-contact/get-contacts-count`;

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    console.log('select-contacts-sidesheet', params);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.onHide = params.onHide;

    this.initialFilterSettings = params.data.filterSetting;
    console.log(this.initialFilterSettings);
   
    this.initializing = ko.observable(true);
    this.onSubmit = params.data.onSubmit;
    this.formatStats = params.data.formatStats;
    this.isContactsCountLoading = ko.observable(false);
    this.contactsCount = ko.observable("");
    this.totalContactsCount = ko.observable(0);
    this.statId = ko.observable(null);
    this.isAdding = ko.observable(false);
    this.addingProgress = ko.observable(0);
    this.statsRequests = [];

    this.statsUrl =
      params.data.statsUrl ||
      `${APIConfig.baseApiUrlPath}mailing-lists/get-link-contacts-stat?access-token=${APIConfig.apiKey}`;

    this.filters = ContactConditionsModel();

    if (this.initialFilterSettings() !== null) {
      console.log('this.initialFilterSettings()', this.initialFilterSettings());
      this.filters.setValue(parseConditions(this.initialFilterSettings()));
    }
    // this.filters.setValue(test);

    this.hasFilters = ko.computed(() => {
      return this.filters.list().some((f) => f.type());
    });

    const _isAutoPollsEnabled = ko.observable(false);
    isAutoPollsEnabled().then((data) => {
      _isAutoPollsEnabled(data);
    });


    this.filtersTypes = ko.computed(() => {
      const filters = ContactFilterTypes;

      if (_isAutoPollsEnabled()) return filters;

      return filters
        .map((category) => {
          if (category.id === "order") return null;
          if (category.id === "personal") return category;

          if (category.id === "other") {
            const { items } = category;
            return {
              ...category,
              items: items.filter((item) => {
                return ![
                  ContactFilterType.Promocode,
                  ContactFilterType.UsedPromocodesPercent,
                ].includes(item.id);
              }),
            };
          }
        })
        .filter(Boolean);
    });

    this.blockAddFilterButton = ko.pureComputed(() => {
      return this.filters.list().some((condition) => !condition.type());
    });

    const getCount = utils.debounce(() => {
      this.getCount();
    }, 400);

    this.getCountDebounced = getCount;

    this.filters.onChange(() => {
      getCount();
    });

    this.buttonText = ko.pureComputed(() => {
      if (this.isFiltersEmpty()) {
        if (this.isContactsCountLoading()) return "Добавить все (...)";
        return `Добавить все (${this.totalContactsCount()})`;
      }
      if (this.isContactsCountLoading()) return "Добавить ... контактов";

      return `Добавить ${this.contactsCount()} ${utils.declOfNum(
        this.contactsCount(),
        ["контакт", "контакта", "контактов"]
      )}`;
    });

    this.answers = new Answers();
    this.answers.on("change", () => {
      getCount();
    });

    this.isFiltersEmpty = ko.computed(() => {
      if (!this.filters.isEmpty()) return false;
      if (this.answers.getData().length) return false;
      return true;
    });


  }

  onModalInit(...params) {
    super.onModalInit(...params);
    this.getCountDebounced();
    this.modal().on('hide', () => {
      if (typeof this.onHide === "function") this.onHide();
    });
  }

  getSearchParams() {
    const conditions = this.filters.getValue();

    const params = formatConditions(conditions);

    params.answers = this.answers.getData();
    return params;
  }

  getCount() {
    this.isContactsCountLoading(true);
    return new Promise((res) => {
      let params = this.getSearchParams();

      $.ajax({
        url: `/foquz/foquz-contact/get-contacts-count`,
        data: {
          page: -1,
          ...params,
        },
        method: "GET",
        success: (response) => {
          this.contactsCount(response.data && response.data.total);
          this.totalContactsCount(
            response.data && response.data.totalWithoutFilters
          );
          this.isContactsCountLoading(false);
        },
        error: (response) => {
          this.isContactsCountLoading(false);
          console.error(response.responseJSON);
        },
      });
    });
  }

  abortAllRequests() {
    this.statsRequests.forEach((xhr) => xhr.abort());
    this.statsRequests = [];
  }

  getStatRequest() {
    return new Promise((res, rej) => {
      const xhr = $.ajax({
        method: "GET",
        url: this.statsUrl,
        data: {
          stat_id: this.statId(),
        },
        success: (data) => {
          if (!this.isAdding()) {
            rej();
            return;
          }

          if (typeof this.formatStats == "function") {
            data = this.formatStats(data);
          }

          if (data.status != "ok") {
            rej();
            return;
          }

          const stat = data.stat;
          const status = stat.status;

          if (status == STATUS_FAILED) {
            rej();
            return;
          }

          if (status == STATUS_DONE) {
            res({
              progress: 100,
            });
            return;
          }

          if (!("total" in stat)) {
            rej();
            return;
          }

          // if (stat.total == 0) {
          //   res({
          //     progress: 100,
          //   });
          //   return;
          // }

          // при загрузке ВСЕХ КОНТАКТОВ в поле total несколько первых раз может быть 0
          // поэтому нельзя прерывать опрашивание
          if (stat.total == 0) {
            res({
              progress: 0,
            });
            return;
          }

          let progress = Math.min(
            ((stat.inserted + stat.duplicated) * 100) / stat.total,
            100
          );

          res({
            progress: progress,
          });
        },
        error: (response) => {
          rej();
        },
      });
      this.statsRequests.push(xhr);
    });
  }

  getStat() {
    let intervalId = setInterval(() => {
      this.getStatRequest()
        .then((res) => {
          if (res.progress >= 100) {
            this.addingProgress(100);
            this.isAdding(false);
            clearInterval(intervalId);
            this.hide();
          } else {
            if (this.addingProgress() < res.progress)
              this.addingProgress(res.progress);
          }
        })
        .catch(() => {
          if (this.isAdding() == false) return;

          this.isAdding(false);
          clearInterval(intervalId);
          console.error("Ошибка загрузки данных");

          this.abortAllRequests();
        });
    }, 800);

    // setTimeout(() => {
    //   if (this.isAdding()) {
    //     this.isAdding(false);
    //     this.abortAllRequests();

    //     clearInterval(intervalId);
    //     alert('Превышено время загрузки данных');
    //   }
    // }, 60000);
  }

  submit() {
    this.isSubmitted(true);


    if (!this.filters.isValid()) return false;
    if (!this.answers.isValid()) return false;

    this.isSubmitted(false);


    this.isAdding(true);
    this.addingProgress(0);

    let params = this.getSearchParams();

    console.log('submit params', params);
    this.onSubmit(params).then((statId) => {
      this.statId(statId);
      this.getStat();
    });
  }

  onRender() {
    this.initializing(false);
  }
}
