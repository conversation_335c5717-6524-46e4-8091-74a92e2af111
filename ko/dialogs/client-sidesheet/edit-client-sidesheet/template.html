
<sidesheet params="ref: modal, dialogWrapper: $component">
  <nav class="nav nav-tabs review-details-modal-nav flex-shrink-0"
       style="height: 45px;">
    <a class="nav-item nav-link active"
       id="client-form-tab"
       data-toggle="tab"
       role="tab"
       href="#client-form"
       aria-controls="client-form"
       data-bind="
        event: {
          'show.bs.tab': function() {
            closeHistory();
            return true;
          }
        }, text: title
        "></a>


    <a class="nav-item nav-link flex-shrink-0"
       id="client-history-tab"
       data-toggle="tab"
       role="tab"
       href="#client-history"
       aria-controls="client-history"
       data-bind="
        event: {
          'show.bs.tab': function() {
            openHistory();
            return true;
          }
        },
        text: _t('История')
        "></a>

  </nav>

  <div class="foquz-dialog__body tab-content">
    <div class="tab-pane show active pt-4 h-100"
         role="tabpanel"
         id="client-form">
      <div class="foquz-dialog__scroll"
           data-bind="nativeScrollbar">
        <div class="container">

          <client-card params="card: card, formControlErrorStateMatcher: formControlErrorStateMatcher, blocked: blocked"></client-card>

        </div>
      </div>
    </div>

    <div class="tab-pane pt-30p h-100"
           role="tabpanel"
           id="client-history">


            <foquz-client-history params="ref: history" data-bind="event: {
              reviewClick: function(_, e, reviewId) { openReview(reviewId) }
            }"></foquz-client-history>

      </div>
  </div>

  <!-- ko ifnot: blocked -->
  <!-- ko ifnot: historyOpened -->
  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn"
              data-bind="click: function() {
                $dialog.hide();
              }">
        <foquz-icon params="icon: 'bin'"
                    class="f-btn-prepend"></foquz-icon>
        <span data-bind="text: _t('Отменить')"></span>
      </button>
      <button type="submit"
              class="f-btn f-btn-success"
              data-bind="click: function() { submit()  }">
        <foquz-icon params="icon: 'save'"
                    class="f-btn-prepend"></foquz-icon>
        <span data-bind="text: _t('Сохранить')"></span>
      </button>
    </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->

</sidesheet>
