import { DialogWrapper } from "Dialogs/wrapper";
import { ClientCard } from "../models/client-card";

import "Dialogs/review-sidesheet";
const loadReviewSidesheet = (function () {
  let promise = null;

  return function () {
    return Promise.resolve();

    if (!promise)
      promise = new Promise((res) => {
        import("Dialogs/review-sidesheet").then((mod) => {});
      });
    return promise;
  };
})();
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.title = _t("Редактировать контакт");

    this.isWatcher = ko.observable(window.CURRENT_USER.watcher ?? 0);

    this.blocked = this.isWatcher ?? !!window.CURRENT_USER.blockActions;

    this.clientId = params.data.clientId;

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.card = new ClientCard(this.clientId);

    this.history = ko.observable(null);
    this.historyOpened = ko.observable(false);

    this.openHistory = () => {
      this.historyOpened(true);
      this.history().load(this.clientId);
    };

    this.closeHistory = () => {
      this.historyOpened(false);
      this.history().reset();
    };

    this._onSubmit = params.onSubmit;
  }

  openReview(reviewId) {
    loadReviewSidesheet().then(() => {
      this.dialogRoot.add({
        name: "review-sidesheet",
        params: { reviewId },
      });
    });
  }

  submit() {
    this.isSubmitted(true);
    if (!this.card.isValid()) return;

    let params = ko.toJSON(this.card.getData());

    $.ajax({
      url: "/foquz/foquz-contact/update?id=" + this.clientId,
      method: "POST",
      data: params,
      success: (response) => {
        if (response.success) {
          this.hide();
          if (typeof this._onSubmit == "function") this._onSubmit();
        } else if (response.errors instanceof Array) {
          this.card.setErrors(response.errors);
        }
      },
    });
  }
}
