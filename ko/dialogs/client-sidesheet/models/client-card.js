import { TagsDirectory } from "Utils/directory/tags";
import { PHONE_REGEX } from "Utils/regex/phone";

import { FilialsDataCollection } from "Models/data-collection/filials";
export class ClientCard {
  constructor(clientId) {
    this.isLoading = ko.observable(true);

    this.directories = {
      users: new Directory("user"),
      processingStatuses: new Directory("answer-processing/statuses"),
      tags: new TagsDirectory(),
    };

    if (!clientId) {
      this.collections = {
        filials: new FilialsDataCollection(),
      };
  
      this.collections.filials.load()
    }

    this.additionalFieldsList = ko.observableArray([]);
    this.additionalFieldsLoaded = ko.observable(false);
    this.additionalFieldsValues = {};
    this.loadAdditionalFields().then((fields) => {
      this.additionalFieldsList(fields);
      fields.forEach((field) => {
        this.additionalFieldsValues[field.id] = ko.observable("");
      });
      this.additionalFieldsLoaded(true);
    });

    Object.keys(this.directories).forEach((d) => this.directories[d].load());

    this.clientId = clientId;
    this.clientData = ko.observable({});

    this.serverErrors = {
      email: ko.observable(""),
      phone: ko.observable(""),
    };

    this.firstName = ko.observable("");
    this.lastName = ko.observable("");
    this.patronymic = ko.observable("");
    this.gender = ko.observable("");
    this.birthday = ko.observable("");
    this.tags = ko.observableArray([]);
    this.filials = ko.observableArray([]);

    this.placeholder = ko.observable("Не выбран");

    this.phone = ko.observable("");
    this.email = ko.observable("");

    this.rus = ko.observable(false);
    this.rus.subscribe((v) => this.phone(""));

    this.phone.extend({
      required: {
        message: _t("Обязательное поле"),
        onlyIf: () => {
          return this.email() === "";
        },
      },
      pattern: {
        params: PHONE_REGEX,
        message: _t("Некорректный формат"),
        onlyIf: () => this.rus(),
      },
      validation: {
        validator: () => false,
        onlyIf: () => !!this.serverErrors.phone(),
        message: () => this.serverErrors.phone(),
      },
    });

    this.email.extend({
      required: {
        message: _t("Обязательное поле"),
        onlyIf: () => {
          return this.phone() === "";
        },
      },
      email: {
        message: _t("main", "Неверный формат параметра «{param}»", {
          param: _t("Email"),
        }),
      },
      validation: {
        validator: () => false,
        onlyIf: () => !!this.serverErrors.email(),
        message: () => this.serverErrors.email(),
      },
    });

    this.phone.subscribe((_) => this.serverErrors.phone(""));
    this.email.subscribe((_) => this.serverErrors.email(""));

    this.formModel = ko.validatedObservable(
      {
        firstName: this.firstName,
        lastName: this.lastName,
        patronymic: this.patronymic,
        phone: this.phone,
        email: this.email,
        gender: this.gender,
        birthday: this.birthday,
        tags: this.tags,
      },
      { deep: true, live: true }
    );

    this.isValid = this.formModel.isValid;

    if (this.clientId) {
      this.getClientData(this.clientId).then((data) => {
        this.setData(data);
        this.isLoading(false);
      });
    } else {
      this.isLoading(false);
    }
  }

  setErrors(errors) {
    errors.forEach((err) => {
      if (err.name in this.serverErrors) {
        this.serverErrors[err.name](err.message);
      }
    });
  }

  loadAdditionalFields() {
    return new Promise((res) => {
      $.ajax({
        url: "/foquz/foquz-question/contact-fields",
        method: "GET",
        success: (response) => {
          let additionalFields = response.additional || [];
          additionalFields.sort((a, b) => {
            return parseInt(a.id) - parseInt(b.id);
          });
          res(additionalFields);
        },
      });
    });
  }

  getAdditionalField(fieldName) {
    return this.clientData()[fieldName] || "";
  }

  setData(data) {
    this.clientData(data);

    this.firstName(data.first_name);
    this.lastName(data.last_name);
    this.patronymic(data.patronymic);

    if (data.phone && data.phone.slice(0, 2) === "+7") {
      this.rus(true);
    }
    this.phone(data.phone);

    this.email(data.email);

    this.gender(data.gender);

    let birthday = moment(data.birthday, "YYYY-MM-DD");
    if (birthday.isValid()) this.birthday(birthday.format("DD.MM.YYYY"));
    else this.birthday("");

    this.tags(data.tags || []);
    this.filials((data.filials || []).map((f) => f.toString()));

    if (this.additionalFieldsLoaded()) this.setAdditionalData(data);
    else {
      this.additionalFieldsLoaded.subscribe((v) => {
        if (v) {
          this.setAdditionalData(data);
        }
      });
    }
  }

  setAdditionalData(data) {
    this.additionalFieldsList().forEach((field) => {
      this.additionalFieldsValues[field.id](data[field.id]);
    });
  }

  getClientData(clientId) {
    this.collections = {
      filials: new FilialsDataCollection({clientId}),
    };

    Object.values(this.collections).forEach((c) => c.load());
    return new Promise((res) => {
      $.ajax({
        url: `${APIConfig.baseApiUrlPath}contact/get?contactId=${clientId}&access-token=${APIConfig.apiKey}`,
        method: "GET",
        success: (response) => {
          res(response.item);
        },
      });
    });

    
  }

  getData() {
    let params = {
      first_name: this.firstName(),
      last_name: this.lastName(),
      patronymic: this.patronymic(),
      phone: this.phone(),
      email: this.email(),
      gender: this.gender(),
      birthday: null,
      tags: this.tags().map((tag) => {
        return {
          ...tag,
          auto_add: tag.isAuto,
        };
      }),
      filials: this.filials(),
    };

    let birthday = moment(this.birthday(), "DD.MM.YYYY");
    if (birthday.isValid()) params.birthday = birthday.format("YYYY-MM-DD");

    this.additionalFieldsList().forEach((field) => {
      params[field.id] = this.additionalFieldsValues[field.id]();
    });

    return params;
  }
}
