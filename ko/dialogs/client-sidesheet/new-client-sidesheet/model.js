import { DialogWrapper } from 'Dialogs/wrapper';
import { ClientCard } from "../models/client-card";


export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted,
    );
    this.card = new ClientCard();
    this._onSubmit = params.onSubmit;
  }

  submit() {
    this.isSubmitted(true);
    if (!this.card.isValid()) return;

    let params = ko.toJSON(this.card.getData());

    $.ajax({
      url: '/foquz/foquz-contact/create',
      method: 'POST',
      data: params,
      success: response => {
        if (response.success) {
          this.hide();
          if (typeof this._onSubmit == 'function') this._onSubmit();
        } else if (response.errors instanceof Array) {
          this.card.setErrors(response.errors)
        }
      }
    })
  }
}
