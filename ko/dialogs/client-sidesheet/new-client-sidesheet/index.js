import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import '../client-card';

ko.components.register('new-client-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('client-sidesheet');
      element.classList.add('new-client-sidesheet');

      return new ViewModel(params, element);
    }
  },
  template: html
});
