@import "Style/breakpoints.less";

.client-card {
  display: block;
  padding-bottom: 24px;

  .check-input__wrapper {
    &:after {
      display: none;
    }
  }

  .client_id__label {
    font-size: 15px;
    line-height: 18px;
    color: #73808d,
  }
  b.client_id__label {
    color: #000;
  }

  .contact-form {
    & > .row {
      & > .col {
        flex-basis: calc(50% - 15px);
        flex-grow: 0;

        &:nth-child(odd) {
          margin-right: 15px;
        }
        &:nth-child(even) {
          margin-left: 15px;
        }
      }

      &.contacts-row {
        & > .col {
          margin: 0;
        }
        .divider {
          width: 30px;
          padding-top: 40px;
          text-align: center;
        }
      }
    }

    .phone-block {
      display: flex;

      .phone-field {
        margin-right: 30px;
      }
      .phone-rus {
        margin-top: 14px;
      }
    }

    .form-hint {
      transform: translateY(-24px);
    }
  }

  @media screen and (max-width: 920px) {
    .contact-form {
      & > .row {
        & > .col {
          flex-basis: 100%;

          &:nth-child(1n) {
            margin-right: 0;
            margin-left: 0;
          }
        }

        &.contacts-row {
          .divider {
            width: 100%;
            padding-top: 0;
            padding-left: 15px;
            padding-bottom: 20px;
            text-align: left;
          }
        }
      }

      .phone-block {
        display: block;

        .phone-field {
          margin-right: 0;
        }
        .phone-rus {
          margin-top: 24px;
        }
      }
    }
  }
}
