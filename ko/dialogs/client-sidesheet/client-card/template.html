<!-- ko if: card.isLoading -->
<spinner></spinner>
<!-- /ko -->

<div class="contact-form">
  <!-- ko ifnot: card.isLoading -->
  <!-- ko if: card.clientData().company_client_id -->
  <div class="row mb-25p">
    <div class="col">
      <span class="client_id__label">Внешний ID: </span>
      <b class="client_id__label" data-bind="text: card.clientData().company_client_id"></b>
    </div>
  </div>
  <!-- /ko -->
  <div class="row">
    <div class="col">
      <div class="form-group">
        <fc-label params="text: _t('Фамилия')"></fc-label>

        <chars-counter params="max: 25, value: card.lastName">
          <input
            class="form-control"
            data-bind="textInput: card.lastName, disable: $parent.blocked"
          />
        </chars-counter>
      </div>
    </div>
    <div class="col">
      <div class="form-group">
        <fc-label params="text: _t('Имя')"></fc-label>

        <chars-counter params="max: 25, value: card.firstName">
          <input
            class="form-control"
            data-bind="textInput: card.firstName, disable: $parent.blocked"
          />
        </chars-counter>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col">
      <div class="form-group">
        <fc-label params="text: _t('Отчество')"></fc-label>

        <chars-counter params="max: 25, value: card.patronymic">
          <input
            class="form-control"
            data-bind="textInput: card.patronymic, disable: $parent.blocked"
          />
        </chars-counter>
      </div>
    </div>
  </div>

  <div class="row contacts-row">
    <div class="col">
      <div class="form-group">
        <fc-label params="text: _t('Телефон'), required: true"></fc-label>

        <div class="phone-block">
          <div class="phone-field">
            <fc-input
              params="value: card.phone,
                mask: card.rus() ? 'phoneRu' : 'phone',
                disabled: blocked,
                placeholder: card.rus() ? '+7 (___) ___ ____' : '',
                invalid: formControlErrorStateMatcher(card.phone)"
            ></fc-input>
            <fc-error
              params="show: formControlErrorStateMatcher(card.phone), text: card.phone.error"
            ></fc-error>
          </div>

          <input-checkbox
            params="checked: card.rus, disabled: blocked,"
            class="phone-rus"
          >
            <div class="d-flex align-items-center mt-n1">
              <svg
                width="24"
                class="mr-2"
                height="18"
                viewBox="0 0 24 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g opacity="0.5">
                  <rect
                    x="0.5"
                    y="0.5"
                    width="23"
                    height="17"
                    fill="white"
                    stroke="#CFD8DC"
                  />
                  <rect y="12" width="24" height="6" fill="#FF0200" />
                  <rect y="6" width="24" height="6" fill="#2D99FF" />
                </g>
              </svg>
              Россия (+7)
            </div>
          </input-checkbox>
        </div>
      </div>
    </div>

    <div class="divider">
      <span data-bind="text: _t('или')"></span>
    </div>

    <div class="col">
      <div class="form-group">
        <fc-label params="text: _t('Email'), required: true"></fc-label>

        <input
          class="form-control"
          data-bind="textInput: card.email, css: {
                  'is-invalid': formControlErrorStateMatcher(card.email)
                }, disable: blocked"
        />

        <validation-feedback
          params="show: formControlErrorStateMatcher(card.email), text: card.email.error"
        >
        </validation-feedback>
      </div>
    </div>
  </div>

  <div
    class="form-hint d-block"
    data-bind="text: _t('Должно быть заполнено хотя бы одно из этих полей')"
  ></div>

  <div class="row">
    <div class="col">
      <div class="row">
        <div class="col-12 col-sm-6">
          <div class="form-group">
            <fc-label params="text: _t('Пол')"></fc-label>

            <select
              data-bind="
                  value: card.gender,
                  lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      allowClear: true,
                      placeholder: _t('Не выбран'),
                  }, disable: blocked
              "
            >
              <option></option>
              <option value="1" data-bind="text: _t('Мужской')"></option>
              <option value="2" data-bind="text: _t('Женский')"></option>
            </select>
          </div>
        </div>

        <div class="col-12 col-sm-6">
          <div class="form-group">
            <fc-label params="text: _t('Дата рождения')"></fc-label>

            <date-picker
              params="value: card.birthday, disabled: blocked"
            ></date-picker>
          </div>
        </div>
      </div>
    </div>

    <div class="col">
      <div class="form-group">
        <fc-label
          params="text: _t('Филиал контакта')"
          data-bind="style: {
          opacity: !card.collections.filials.loading() && card.collections.filials.list().length === 0 ? 0.5 : 1
        }"
        ></fc-label>

        <fc-select
          class="categorized"
          params="multiple: true,
          options: card.collections.filials.list, 
          value: card.filials, 
          placeholder: card.placeholder,
          disabled: card.collections.filials.list().length == 0 || $parent.blocked
          "
          data-bind="style: {
            opacity: !card.collections.filials.loading() && card.collections.filials.list().length === 0 ? 0.5 : 1
          }"
        ></fc-select>

        <!-- ko if: !card.collections.filials.loading() && card.collections.filials.list().length === 0-->
        <div
          class="f-color-service f-fs-1 mt-2"
          data-bind="text: _t('В настройках компании нет добавленных филиалов.')"
        >
          <a
            href="/foquz/settings?tab=settings&channel=email&setting=collections"
            target="_blank"
            data-bind="text: _t('Добавить')"
          ></a>
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="row">
    <!-- ko if: !blocked || card.tags().length > 0 -->
    <div class="col">
      <div class="form-group">
        <fc-label params="text: _t('Теги')"></fc-label>

        <!-- ko if: card.directories.tags.loaded -->
        <div
          data-bind="component: {
              name: 'foquz-tags-select',
              params: {
                  value: card.tags,
                  list: card.directories.tags.data,
                  addButton: {
                      label: _t('Добавить тег')
                  },
                  clientId: card.clientId,
                  disabled: blocked
              }
          }"
        ></div>
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->
  </div>

  <!-- ko if: card.additionalFieldsLoaded() && card.additionalFieldsList().length -->
  <div
    class="row"
    data-bind="let: {isAdditionalSectionExpanded: ko.observable(false)}"
  >
    <div class="col">
      <div class="clients__create-modal-dialog-additional-section">
        <div
          class="clients__create-modal-dialog-additional-section-header"
          data-bind="click: function () {
              isAdditionalSectionExpanded(!isAdditionalSectionExpanded())
              $(window).resize();
            }"
        >
          <span data-bind="text: _t('Пользовательские поля')"></span>

          <i
            class="clients__create-modal-dialog-additional-section-header-icon"
            data-bind="style: {
                  transform: isAdditionalSectionExpanded() ? 'rotate(-180deg)' : ''
                }"
          ></i>
        </div>

        <div
          class="clients__create-modal-dialog-additional-list"
          data-bind="slide: isAdditionalSectionExpanded"
        >
          <!-- ko foreach: card.additionalFieldsList -->
          <div class="clients__create-modal-dialog-additional-list-item">
            <div
              class="form-group clients__create-modal-dialog-additional-list-item-form-group"
            >
              <div class="form-group-header">
                <label class="form-label" data-bind="text: $data.text"></label>

                <span class="spacer"></span>

                <span
                  class="form-group-note"
                  data-bind="text: _t('необязательное')"
                ></span>
              </div>

              <div class="chars-counter chars-counter--type_input">
                <input
                  class="form-control"
                  data-bind="textInput: $parent.card.additionalFieldsValues[$data.id], disable: $parent.blocked"
                />
              </div>
            </div>
          </div>
          <!-- /ko -->
        </div>
      </div>
    </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->
</div>
