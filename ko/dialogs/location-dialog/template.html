<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    <span data-bind="text: $parent.title"></span>
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <fc-label params="text: nameLabel"></fc-label>

      <fc-input
        params="value: name, maxlength: 150, counter: true, placeholder: namePlaceholder, valid: formControlSuccessStateMatcher(name), invalid: formControlErrorStateMatcher(name)"
      ></fc-input>

      <fc-error
        params="show: formControlErrorStateMatcher(name), text: name.error"
      ></fc-error>
    </div>

    <div class="form-group mb-0 mb-md-4">
      <fc-label params="text: locationLabel"></fc-label>

      <fc-select
        params="value: location, options: locations, disabledLevel: disabledLevel"
      >
        <template data-slot="result">
          <span class="d-inline-flex">
            <!-- ko if: $data.parents -->
            <!-- ko foreach: parents -->
            <span class="f-color-service" data-bind="text: text + '/'"></span>
            <!-- /ko -->
            <!-- /ko -->
            <span data-bind="text: text"></span>
          </span>
        </template>
      </fc-select>
    </div>

    <fc-error params="show: error, text: error"></fc-error>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="color: 'primary', inverse: true, click: function() { $dialog.hide('close') }, label: 'Отменить'"
      ></fc-button>
      <fc-button
        params="color: 'success', click: function() { submit() }, label: 'Сохранить', pending: pending"
      ></fc-button>
    </div>
  </div>
</foquz-dialog>
