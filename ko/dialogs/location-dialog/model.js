import { DialogWrapper } from 'Dialogs/wrapper';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.disabledLevel = params.disabledLevel;
    this.error = ko.observable(null);

    this.pending = ko.observable(false);

    this.title =
      params.title || params.name ? 'Редактировать папку' : 'Добавить папку';
    this.locations = params.locations;
    this.nameLabel = params.nameLabel || 'Название';
    this.namePlaceholder = params.namePlaceholder || 'Новая папка';
    this.locationLabel = params.locationLabel || 'Местоположение папки';

    this.name = ko.observable(params.name || '').extend({
      required: {
        message: _t('Обязательное поле')
      },
      minLength: {
        params: 2,
        message: _t('main', 'Не менее {characters}', {
          characters: _t('main', '{count} символов', {
            count: 2
          })
        })
      }
    });

    this.location = ko.observable(params.location || 0);

    [(this.name, this.location)].forEach((f) => {
      this.subscriptions.push(
        f.subscribe(() => {
          this.error(null);
        })
      );
    });
  }

  submit() {
    if (this.error()) return;
    this.isSubmitted(true);
    if (!this.name.isValid()) return;
    this.pending(true);
    let data = {
      name: this.name(),
      locationId: this.location(),
      onError: (error) => {
        this.pending(false);
        this.error(error);
        console.error(error);
      }
    };
    this.emitEvent('submit', data);
  }
}
