import { DialogWrapper } from "@/dialogs/wrapper";
import { listToTree } from "@/utils/list/list-to-tree";
import QuestionFormController from "@/components/question-form";
import { cPointServerFormatter } from "@/components/question-form/utils/cpoint-server-formatter";
import { questionClientFormatter } from "@/components/question-form/utils/client-formatter";

import { INTER_BLOCK } from "@/data/question-types";
import { ApiUrl } from "@/utils/url/api-url";
import { declOfNum } from "@/utils/string/decl-of-num";
import { DialogsModule } from "@/utils/dialogs-module";
import { request } from "../../utils/api/request";
import { addFilesToFormData, toFormData } from "../../utils/api/form-data";

// import '@/presentation/views/fc-questions/fc-cp-form';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);

    this.HIDE_FEATURES = true;

    this.scrollbar = ko.observable(null);

    this.rendered = ko.observable(false);
    this.prepared = ko.observable(false);
    this.inited = ko.computed(() => this.rendered() && this.prepared());

    this.mode = params.mode;
    this.isAutoPollsEnabled = params.isAutoPollsEnabled;
    this.addMethod = ko.observable("new");

    this.pointBusinessTypes = ko.observableArray([]);
    this.pointFolder = ko.observable(null);

    this.businessTypes = ko.observableArray([]).extend({
      required: {
        message: "Обязательное поле",
      },
    });

    this.readySubmitted = ko.observable(false);
    this.readyPointsPending = ko.observable(false);
    this.isResetting = ko.observable(false);
    this.isLoading = ko.observable(this.mode === "edit");
    this.isSubmitted = ko.observable(false);

    this.formModel = ko.validatedObservable(null, {
      deep: true,
      live: true,
    });

    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.controller = new QuestionFormController({
      mode: "cpoint",
      isAuto: true,
      formControlErrorStateMatcher:
        this.formControlErrorStateMatcher.bind(this),
      formControlSuccessStateMatcher:
        this.formControlSuccessStateMatcher.bind(this),
      tryChangeBlockedParam: () => {
        //$('#launched-survey-alert-modal').modal();
      },
      api: {
        loadImageByFileUrl: (id) => {
          return Promise.resolve(
            id
              ? `/foquz/api/questions/image-upload?access-token=${APIConfig.apiKey}&id=${this.sourceQuestionId}`
              : `/foquz/api/questions/image-upload?access-token=${APIConfig.apiKey}`
          );
        },
        loadYoutubeVideoUrl: (id) => {
          return Promise.resolve(
            id
              ? `/foquz/api/questions/upload-youtube?access-token=${APIConfig.apiKey}&id=${this.sourceQuestionId}`
              : `/foquz/api/questions/upload-youtube?access-token=${APIConfig.apiKey}`
          );
        },
        loadVideoByFileUrl: (id) => {
          return Promise.resolve(
            id
              ? `/foquz/api/questions/video-upload?access-token=${APIConfig.apiKey}&id=${this.sourceQuestionId}`
              : `/foquz/api/questions/video-upload?access-token=${APIConfig.apiKey}`
          );
        },
        loadMediaByUrl: (id) => {
          return Promise.resolve(
            id
              ? `/foquz/api/questions/upload-by-link?access-token=${APIConfig.apiKey}&id=${this.sourceQuestionId}`
              : `/foquz/api/questions/upload-by-link?access-token=${APIConfig.apiKey}`
          );
        },
        changeMediaLabelUrl: (mediaId) => {
          return Promise.resolve(
            "/foquz/api/questions/change-label?access-token=" +
              APIConfig.apiKey +
              "&id=" +
              mediaId
          );
        },

        // changeMediaLabelUrl: (id) => {
        //   return Promise.resolve(`/foquz/api/questions/change-label?access-token=${APIConfig.apiKey}&id=${id}`);
        // }
      },
    });

    this.question = ko.pureComputed(() => {
      let q = this.controller.question();
      q.on("question.change:ui", (event) => {
        if ((event.field = "screenType")) this.formModel().conditions([]);
      });
      return q;
    });

    this.isStartScreen = ko.pureComputed(() => {
      return (
        this.question().type == INTER_BLOCK &&
        this.question().blockType() == "start"
      );
    });

    this.hasConditions = ko.pureComputed(() => {
      if (this.addMethod() !== "new") return false;
      if (!this.question()) return true;
      return !this.question().isItemsQuestion;
    });

    let fetchPromise = Promise.resolve();
    if (params.id && this.mode === "edit") {
      fetchPromise = fetch(ApiUrl("contact-points/view", { id: params.id }))
        .then((res) => res.json())
        .then((json) => {
          this.data = json;
          this.sourceQuestionId = params.data
            ? params.data.sourceQuestionId
            : "";

          this.pointBusinessTypes(json.businessTypes.map((type) => type.id));
          this.pointFolder(json.folder && json.folder.id);
        });
    }

    fetchPromise.then(() => {
      this.isSystem = this.mode === "edit" ? this.data.isSystem : false;
      this.systemType = this.mode === "edit" ? this.data.systemType : null;
      this.surveys = this.mode === "edit" ? this.data.surveys : [];

      if (this.mode === "edit") {
        const data = questionClientFormatter(this.data, "cpoint");
        this.controller.setQuestion(data);
      } else {
        this.controller.setQuestion(null);
      }

      this.formModel({
        sourceQuestionId: ko.observable(
          this.mode === "edit" ? this.data.sourceQuestionId : 0
        ),
        conditions: ko.observableArray(
          (this.mode === "edit" ? this.data.conditions : []).map(
            (condition) => {
              return this.createCondition(
                condition.orderType !== null
                  ? condition.orderType.toString()
                  : "",
                condition.orderSource !== null
                  ? condition.orderSource.toString()
                  : ""
              );
            }
          )
        ),
      });

      this.formModelValue = ko.pureComputed(() => {
        let contactPointData = cPointServerFormatter(this.controller.getData());
        const data = {
          ...contactPointData,
          businessTypes: this.pointBusinessTypes(),
          folder_id: this.pointFolder() || '',

          systemType: false,
          sourceQuestionId: this.formModel().sourceQuestionId(),
          conditions: this.formModel()
            .conditions()
            .map((c) => ({
              orderType: c.orderType() !== "" ? +c.orderType() : null,
              orderSource: c.orderSource() !== "" ? +c.orderSource() : null,
            })),
        };

        data.conditions.forEach((c) => {
          if (c.orderType === 3) {
            c.orderSource = null;
          }
        });

        return data;
      });

      if (this.mode === "edit") {
        setTimeout(() => {
          this.isLoading(false);
        }, 1000);
      }

      this.prepared(true);
    });
  }

  getFolders(cb) {
    fetch(ApiUrl("contact-points/folders"))
      .then((res) => res.json())
      .then((json) => {
        let items = json.items;
        items = items.map((i) => ({ ...i, text: i.name }));
        items = listToTree(items, { parent: "folder_id", children: "items" });
        return cb(items);
      });
  }

  getBusinessTypes(cb) {
    fetch(ApiUrl("business-types"))
      .then((res) => res.json())
      .then((list) => {
        cb(
          list.map((i) => {
            return {
              id: i.id,
              text: i.name,
            };
          })
        );
      });
  }

  createCondition(orderType, orderSource) {
    return {
      orderType: ko.observable(orderType),
      orderSource: ko.observable(orderSource),
    };
  }

  addCondition() {
    return this.formModel().conditions.push(this.createCondition("", ""));
  }

  removeCondition(condition) {
    return this.formModel().conditions.remove(condition);
  }

  conditionBeforeRemove(element) {
    $(element).slideUp(200, () => $(element).remove());
  }

  conditionAfterAdd(element) {
    $(element).hide().slideDown(200);
  }

  onDeleteButtonClick() {
    this.hide();
    console.log("delete button click");
    this.emitEvent("delete");
  }

  initializeSource(questionId) {
    if (questionId && this.formModel().sourceQuestionId()) {
      return Promise.resolve(this.formModel().sourceQuestionId());
    }

    return new Promise((res) => {
      $.post(
        "/foquz/contact-points/initialize-source-question",
        this.formModelValue(),
        (response) => {
          this.question().id(response.id);
          this.formModel().sourceQuestionId(response.question_id);

          res(response.question_id);
        }
      );
    });
  }

  cancel() {
    this.isSubmitted(false);
    this.isResetting(true);

    if (this.mode === "edit") {
      this.controller.updateData(questionClientFormatter(this.data, "cpoint"));
    } else {
      this.controller.setQuestion(null);
    }

    this.formModel().conditions(
      (this.mode === "edit" ? this.data.conditions : []).map((condition) => {
        return this.createCondition(
          condition.orderType !== null ? condition.orderType.toString() : "",
          condition.orderSource !== null ? condition.orderSource.toString() : ""
        );
      })
    );

    this.isResetting(false);
  }

  submit() {
    this.isSubmitted(true);

    if (this.formModel.isValid() && this.controller.isValid()) {
      var t = this;

      const params = this.formModelValue();
      console.log({ params });

      const editMode = params.id !== 0;
      const url = editMode
        ? "/foquz/api/contact-points/update"
        : "/foquz/api/contact-points/create";

      const { additionalFiles, ...pointParams } = params;

      const fd = toFormData(pointParams);
      addFilesToFormData(fd, additionalFiles);

      request(url, {
        method: "post",
        params: editMode && {
          pointId: params.id,
          questionId: params.sourceQuestionId,
        },
        body: fd,
      }).then((response) => {
        const { data, error } = response;
        if (error) {
          const { message } = data;
          this.controller.setErrors({
            alias: message
          });
          return;
        }

        this.emitEvent("submit", data);
        this.hide();
      });
    }
  }

  addReadyPoints() {
    this.readySubmitted(true);
    if (!this.businessTypes.isValid()) return;
    this.readyPointsPending(true);

    $.ajax({
      method: "POST",
      url: ApiUrl("contact-points/create-by-business-type", {
        typeIds: this.businessTypes(),
      }),
      data: {
        typeIds: this.businessTypes(),
      },
      success: (res) => {
        this.emitEvent("update");
        this.readySubmitted(false);
        this.readyPointsPending(false);
        let pointsCount = res.addedPointItemsCount || 0;

        let m = this.info({
          text: `Добавлено <b class="bold">${pointsCount} ${declOfNum(
            pointsCount,
            ["точка контакта", "точки контакта", "точек контакта"]
          )}</b>`,
        }).then(() => {
          this.hide();
        });
      },
      error: (res) => {
        console.error(res.responseText);
        this.readyPointsPending(false);
      },
    });
  }

  onRender() {
    this.rendered(true);
  }
}
