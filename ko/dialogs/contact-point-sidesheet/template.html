<!-- ko let: { $cpModal: $component} -->
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div
    class="foquz-dialog__body"
    data-bind="descendantsComplete: $cpModal.onRender.bind($cpModal)"
  >
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar, ref: scrollbar">
      <div class="container">
        <div class="py-30p">
          <!-- ko if: inited() && !isLoading() && !isResetting() -->

          <div class="row">
            <div class="col-6">
              <!-- ko if: !isLoading() -->

              <!-- ko ifnot: HIDE_FEATURES -->
              <!-- ko ifnot: mode === 'edit' -->
              <div class="form-group">
                <fc-label
                  params="text: 'Способ добавления', hint: 'Способ добавления'"
                ></fc-label>
                <radio-group
                  params="value: addMethod, options: [
                  {value: 'new', label: 'Добавить новую'},
                  {value: 'ready', label: 'Выбрать готовые'},
                ], disabled: readyPointsPending"
                ></radio-group>

                <!-- ko ifnot: addMethod() === 'new' -->
                <div class="mt-10p f-fs-1 f-color-service">
                  Вы можете добавить уже созданные точки контакта, выбрав вид
                  бизнеса вашей компании. Для каждого вида бизнеса составлены
                  примеры шаблонов вопросов, которые также упорядочены по
                  категориям (папкам). Добавленные точки контакта можно
                  редактировать.
                </div>
                <!-- /ko -->
              </div>
              <!-- /ko -->
              <!-- /ko -->

              <!-- ko if: addMethod() === 'new' -->

              <div class="form-group">
                <fc-label params="text: 'Папка', hint: 'Папка', optional: true"></fc-label>
                <fc-select
                  params="value: pointFolder, options: getFolders, placeholder: 'Главная страница'"
                >
                  <template data-slot="result">
                    <span class="d-inline-flex" data-bind="log">
                      <!-- ko if: $data.parents -->
                      <!-- ko foreach: parents -->
                      <span
                        class="f-color-service"
                        data-bind="text: text + '/'"
                      ></span>
                      <!-- /ko -->
                      <!-- /ko -->
                      <span data-bind="text: text"></span>
                    </span>
                  </template>
                </fc-select>
              </div>

              <!-- ko ifnot: HIDE_FEATURES -->
              <div class="form-group">
                <fc-label
                  params="text: 'Виды бизнеса', hint: 'Виды бизнеса', optional: true"
                ></fc-label>

                <fc-select
                  params="multiple: true, value: pointBusinessTypes, options: getBusinessTypes, placeholder: 'Выберите виды бизнеса'"
                ></fc-select>
              </div>
              <!-- /ko -->

              <!-- ko component: {
                    name: 'question-form',
                    params: {
                        controller: controller,
                        isAuto: true,
                    }
                } -->
              <!-- /ko -->

              <!-- /ko -->

              <!-- ko ifnot: addMethod() === 'new' -->
              <div class="form-group">
                <fc-label
                  params="text: 'Вид бизнеса', hint: 'Вид бизнеса'"
                ></fc-label>
                <fc-select
                  params="multiple: true, value: businessTypes, options: getBusinessTypes, placeholder: 'Выберите вид бизнеса', invalid: readySubmitted() && !businessTypes.isValid()"
                ></fc-select>
                <fc-error
                  params="show: readySubmitted() && !businessTypes.isValid(), text: businessTypes.error"
                ></fc-error>
              </div>
              <!-- /ko -->

              <!-- /ko -->

              <!-- ko if: isLoading() -->
              <div
                class="pages-loader contact-points__details-modal-dialog-question-loader"
                title="Пожалуйста, подождите ..."
              >
                <i class="fa fa-spinner fa-pulse fa-2x fa-fw"></i>
              </div>
              <!-- /ko -->
            </div>

            <!-- ko if: hasConditions -->
            <div class="col-6">
              <!-- ko ifnot: isAutoPollsEnabled -->
              <div class="contact-points__details-modal-dialog-conditions">
                <div
                  class="contact-points__details-modal-dialog-conditions-header"
                >
                  <div
                    class="contact-points__details-modal-dialog-conditions-leader"
                  >
                    <div
                      class="contact-points__details-modal-dialog-conditions-title"
                    >
                      Условия
                    </div>
                  </div>

                  <div class="spacer"></div>

                  <!-- ko if: surveys.length === 0 && !isSystem  -->
                  <button
                    type="button"
                    disabled
                    class="f-btn f-btn-text f-btn-success"
                  >
                    <span class="f-btn-prepend">
                      <svg-icon params="name: 'plus'"></svg-icon>
                    </span>
                    Добавить условие
                  </button>
                  <!-- /ko -->
                </div>

                <div class="d-flex mt-20p">
                  <svg-icon
                    params="name: 'exclamation-circle'"
                    class="f-color-danger mr-3 flex-shrink-0"
                  ></svg-icon>
                  <div class="f-fs-1 f-color-service">
                    Условия доступны только для автоматических опросов. Для
                    вашей компании
                    <a href="/foquz/foquz-poll/create?isAuto=1" target="_blank">
                      не подключены автоматические опросы </a
                    >.
                  </div>
                </div>
              </div>
              <!-- /ko -->

              <!-- ko if: isAutoPollsEnabled -->

              <div
                class="contact-points__details-modal-dialog-conditions"
                data-bind="css: {
                  'blocked': !(!isStartScreen() && surveys.length === 0 && !isSystem)
                }"
              >
                <div
                  class="contact-points__details-modal-dialog-conditions-header"
                >
                  <div
                    class="contact-points__details-modal-dialog-conditions-leader"
                  >
                    <div
                      class="contact-points__details-modal-dialog-conditions-title"
                    >
                      Условия
                      <!-- ko if: formModel().conditions().length > 0 -->
                      <span
                        class="contact-points__details-modal-dialog-conditions-count"
                        data-bind="text: formModel().conditions().length"
                      >
                      </span>
                      <!-- /ko -->
                    </div>

                    <div
                      class="contact-points__details-modal-dialog-conditions-sub-title"
                    >
                      Добавление условий для отображения точки контакта
                    </div>
                  </div>

                  <div class="spacer"></div>

                  <!-- ko if: !isStartScreen() && surveys.length === 0 && !isSystem  -->
                  <button
                    type="submit"
                    class="btn btn-success btn-with-icon btn-add settings__save-button"
                    data-bind="click: function() { addCondition(); }"
                  >
                    Добавить
                  </button>
                  <!-- /ko -->
                </div>

                <!-- ko foreach: { data: formModel().conditions, afterAdd: conditionAfterAdd, beforeRemove: conditionBeforeRemove } -->
                <div
                  class="contact-points__details-modal-dialog-conditions-item"
                >
                  <div
                    class="form-group contact-points__details-modal-dialog-conditions-item-form-group"
                  >
                    <label class="form-label">Тип заказа</label>

                    <button
                      class="btn-question"
                      data-bind="tooltip, tooltipPlacement: 'top'"
                      type="button"
                      title="Тип заказа"
                    ></button>

                    <select
                      data-bind="
                            value: orderType,
                            select2: {
                                containerCssClass: 'form-control',
                                wrapperCssClass: 'select2-container--form-control',
                                allowClear: true
                            },
                            attr: { disabled: $cpModal.surveys.length > 0 || $cpModal.isSystem }
                        "
                      data-placeholder=""
                    >
                      <option></option>
                      <option value="1">Доставка</option>
                      <option value="2">Самовывоз</option>
                      <option value="3">Зал</option>
                    </select>
                  </div>

                  <!-- ko template: {
                      foreach: templateIf(orderType() !== '3', $data),
                      afterAdd: fadeAfterAddFactory(200, 200),
                      beforeRemove: fadeBeforeRemoveFactory(200)
                  } -->
                  <div
                    class="form-group contact-points__details-modal-dialog-conditions-item-form-group"
                  >
                    <label class="form-label">Способ оформления</label>

                    <button
                      class="btn-question"
                      data-bind="tooltip, tooltipPlacement: 'top'"
                      type="button"
                      title="Способ оформления"
                    ></button>

                    <select
                      data-bind="
                                                          value: orderSource,
                                                          select2: {
                                                              containerCssClass: 'form-control',
                                                              wrapperCssClass: 'select2-container--form-control',
                                                              allowClear: true
                                                          },
                                                          attr: { disabled: $cpModal.surveys.length > 0 || $cpModal.isSystem }
                                                      "
                      data-placeholder=""
                    >
                      <option></option>
                      <option value="1">Сайт</option>
                      <option value="2">Приложение</option>
                      <option value="3">Телефон</option>
                    </select>
                  </div>
                  <!-- /ko -->

                  <!-- ko if: $cpModal.surveys.length === 0 && !$cpModal.isSystem  -->
                  <button
                    type="submit"
                    class="btn btn-danger contact-points__details-modal-dialog-conditions-item-remove-button"
                    title="Удалить"
                    data-bind="click: function() { $cpModal.removeCondition($data); }"
                  ></button>
                  <!-- /ko -->
                </div>
                <!-- /ko -->
              </div>

              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>

          <!-- /ko -->

          <!-- ko if: isLoading() -->
          <div class="pages-loader" title="Пожалуйста, подождите ...">
            <i class="fa fa-spinner fa-pulse fa-2x fa-fw"></i>
          </div>
          <!-- /ko -->
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="container">
      <!-- ko if: inited() -->
      <div class="f-card-row align-items-center">
        <div class="f-card-row__block f-card-row__block--left">
          <!-- ko if: mode === 'edit' && surveys.length > 0 -->
          <span class="contact-points__details-modal-dialog-disclaimer">
            Изменённые настройки точки контакта будут применены только для новых
            опросов
          </span>
          <!-- /ko -->
        </div>
        <div class="f-card-row__block f-card-row__block--right">
          <div class="contact-points__details-modal-dialog-actions">
            <!-- ko if: addMethod() === 'new' -->
            <!-- ko if: mode === 'edit' && !isSystem && surveys.length === 0 -->
            <button
              type="button"
              class="f-btn f-btn-danger"
              data-bind="click: function() { onDeleteButtonClick(); }"
            >
              <span class="f-btn-prepend">
                <svg-icon
                  params="name: 'times'"
                  class="svg-icon--sm"
                ></svg-icon>
              </span>
              Удалить
            </button>
            <!-- /ko -->
            <button
              type="button"
              class="f-btn"
              data-bind="click: function () { cancel(); }"
            >
              <span class="f-btn-prepend">
                <svg-icon params="name: 'bin'"></svg-icon>
              </span>
              Отменить
            </button>
            <button
              type="button"
              class="f-btn f-btn-success"
              data-bind="click: function () { submit(); }"
            >
              <span class="f-btn-prepend">
                <svg-icon params="name: 'save'"></svg-icon>
              </span>
              Сохранить
            </button>
            <!-- /ko -->

            <!-- ko ifnot: addMethod() === 'new' -->
            <fc-button
              params="color: 'primary', inverse: true, label: 'Отменить', click: function() {
              cancel()
            }"
            ></fc-button>
            <fc-button
              params="color: 'success', label: 'Добавить точки контакта', click: function() {
              addReadyPoints();
            }, pending: readyPointsPending"
            ></fc-button>
            <!-- /ko -->
          </div>
        </div>
      </div>
      <!-- /ko -->
    </div>
  </div>
</sidesheet>
<!-- /ko -->
