import { ViewModel } from './model';
import html from './template.html';
import './style.less';





ko.bindingHandlers.contactPointsDetailsModalDialogCallback = {
  init: function (element, valueAccessor) {
    valueAccessor()(element);
  }
};

ko.bindingHandlers.contactPointsDetailsModalDialogImageFormControlListItem = {
  init: function (element, valueAccessor, allBindings) {
    const $element = $(element);
    $element.addClass(
      'contact-points__details-modal-dialog-media-form-control-list-item'
    );

    const urls = allBindings.get(
      'contactPointsDetailsModalDialogImageFormControlListItemUrls'
    );
    const index = allBindings.get(
      'contactPointsDetailsModalDialogImageFormControlListItemIndex'
    );

    $(element).on('click', (event) => {
      if (
        !$(event.target).closest(
          '.contact-points__details-modal-dialog-media-form-control-list-item-remove-button'
        ).length
      ) {
        $.fancybox.open(
          ko.utils.unwrapObservable(urls).map((url) => ({
            src: url
          })),
          {
            index: ko.utils.unwrapObservable(index),
            loop: false,
            buttons: ['rotate', 'zoom', 'close']
          }
        );
      }
    });
  }
};

ko.bindingHandlers.contactPointsDetailsModalDialogVideoFormControlListItem = {
  init: function (element, valueAccessor, allBindings) {
    const $element = $(element);
    $element.addClass(
      'contact-points__details-modal-dialog-media-form-control-list-item'
    );

    const urls = allBindings.get(
      'contactPointsDetailsModalDialogVideoFormControlListItemUrls'
    );
    const index = allBindings.get(
      'contactPointsDetailsModalDialogVideoFormControlListItemIndex'
    );

    $(element).on('click', () => {
      $.fancybox.open(
        ko.utils.unwrapObservable(urls).map((url) => ({
          src: url
        })),
        {
          index: ko.utils.unwrapObservable(index),
          loop: false,
          buttons: ['zoom', 'close']
        }
      );
    });
  }
};

ko.components.register('contact-point-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('contact-point-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
