import { DialogWrapper } from 'Dialogs/wrapper';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.name = ko.observable(ko.toJS(params.name)).extend({
      required: {
        message: 'Обязательное поле'
      }
    });
  }

  onSubmit() {
    this.isSubmitted(true);

    if (this.name.isValid()) {
      this.emitEvent('changeName', this.name());
      this.hide();
    }
  }
}
