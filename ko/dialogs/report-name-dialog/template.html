<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header>
    Сохранение отчёта
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <label class="form-label">Введите название отчёта</label>

      <foquz-chars-counter params="value: name, max: 150">
        <input class="form-control"
               data-bind="textInput: $parent.name, css: { 'is-invalid': $parent.formControlErrorStateMatcher($parent.name) }">
      </foquz-chars-counter>

      <validation-feedback params="text: name.error, show: formControlErrorStateMatcher(name)"></validation-feedback>

    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link px-2"
              data-bind="
                click: function() {
                  hide();
                }">
        Отменить
      </button>
      <button type="button"
              class="f-btn f-btn-success"
              data-bind="
                click: function() {
                  onSubmit();
                }">
        Сохранить
      </button>
    </div>
  </div>

</foquz-dialog>
