<sidesheet class="poll-widget-sidesheet" params="ref: modal, dialogWrapper: $component">
  <div style="display: none">    
    <svg>
      <symbol id="foquz-logo-xs-icon" class="quiz" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5.31852 17.9999H2.72741C2.26927 17.9999 1.8299 17.8208 1.50595 17.502C1.18199 17.1832 1 16.7508 1 16.2999V10.3499C1 9.89907 1.18199 9.46668 1.50595 9.14787C1.8299 8.82906 2.26927 8.64995 2.72741 8.64995H5.31852M11.3645 6.94996V3.54998C11.3645 2.87369 11.0915 2.22509 10.6055 1.74687C10.1196 1.26866 9.46055 1 8.77334 1L5.31852 8.64995V17.9999H15.0611C15.4777 18.0045 15.882 17.8608 16.1994 17.5953C16.5168 17.3298 16.7261 16.9603 16.7885 16.5549L17.9804 8.90495C18.018 8.6613 18.0013 8.41253 17.9315 8.17587C17.8617 7.9392 17.7404 7.72031 17.5761 7.53435C17.4118 7.34838 17.2084 7.1998 16.9799 7.0989C16.7514 6.99799 16.5034 6.94717 16.253 6.94996H11.3645Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
      </symbol>
      <symbol id="foquz-logo-xs" width="43" height="9" viewBox="0 0 43 9" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.12512 1.78826C5.0963 1.90718 4.98932 1.99098 4.86632 1.99098H1.99566V3.75228H4.66213L4.23531 5.45327H1.99566V8.77079H0V0.229676H5.16483C5.33709 0.229676 5.46399 0.389902 5.42363 0.556438L5.12512 1.78826ZM14.9658 4.46358C14.9658 7.16585 13.0292 8.99953 10.4077 8.99953C7.798 8.99953 5.86139 7.16585 5.86139 4.46358C5.86139 1.72511 7.798 0 10.4077 0C13.0292 0 14.9658 1.72511 14.9658 4.46358ZM12.8049 4.46368C12.8049 2.96777 11.8248 1.85791 10.4078 1.85791C8.99072 1.85791 8.02242 2.96777 8.02242 4.46368C8.02242 6.00783 9.00253 7.11769 10.4078 7.11769C11.813 7.11769 12.8049 6.00783 12.8049 4.46368ZM25.7192 8.50559C25.7192 8.6518 25.6 8.77032 25.4529 8.77032H20.6296C17.9727 8.77032 16.166 7.18998 16.166 4.39119C16.166 1.79749 18.1144 0 20.7005 0C23.2984 0 25.1996 1.78543 25.1996 4.355C25.1996 5.65788 24.5619 6.65917 23.9597 7.12966V7.16585L25.4474 7.13525C25.5966 7.13218 25.7192 7.25158 25.7192 7.39993V8.50559ZM23.0623 4.41566C23.0623 2.93182 22.094 1.87022 20.7006 1.87022C19.3544 1.87022 18.3389 2.93182 18.3389 4.41566C18.3389 5.94775 19.3072 7.00936 20.6888 7.00936C22.0586 7.00936 23.0623 5.94775 23.0623 4.41566ZM34.1106 5.54978C34.1106 7.60061 32.6936 9 30.509 9C28.3126 9 26.9428 7.60061 26.9428 5.54978V0.494414C26.9428 0.348203 27.062 0.229676 27.209 0.229676H28.684C28.8311 0.229676 28.9503 0.348203 28.9503 0.494414V5.38088C28.9503 6.29773 29.3872 7.14219 30.5208 7.14219C31.6662 7.14219 32.0913 6.29773 32.0913 5.38088V0.494414C32.0913 0.348203 32.2105 0.229676 32.3576 0.229676H33.8444C33.9914 0.229676 34.1106 0.348203 34.1106 0.494414V5.54978ZM42.2308 8.50605C42.2308 8.65226 42.1116 8.77079 41.9646 8.77079H35.9078C35.7607 8.77079 35.6416 8.65226 35.6416 8.50605V7.07736C35.6416 7.01777 35.6618 6.95993 35.6989 6.91318L39.2801 2.40783C39.4181 2.23424 39.2938 1.97891 39.0713 1.97891H36.0022C35.8552 1.97891 35.736 1.86039 35.736 1.71418V0.494414C35.736 0.348203 35.8552 0.229676 36.0022 0.229676H41.8819C42.0289 0.229676 42.1481 0.348203 42.1481 0.494414V1.79009C42.1481 1.84987 42.1278 1.90789 42.0904 1.9547L38.4169 6.55601C38.2784 6.7295 38.4026 6.98536 38.6254 6.98536H41.9646C42.1116 6.98536 42.2308 7.10389 42.2308 7.2501V8.50605Z" fill="currentColor" />
      </symbol>
      <symbol id="foquz-logo-sm" width="12" height="19" viewBox="0 0 12 19" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.3238 3.44366C11.2601 3.7064 11.0238 3.89155 10.752 3.89155H4.40935V7.7831H10.3008L9.3578 11.5414H4.40935V18.8713H0V0H11.4115C11.7921 0 12.0725 0.354016 11.9834 0.721973L11.3238 3.44366Z" fill="currentColor"></path>
      </symbol>
    </svg>
  </div>
  <div class="poll-widget-sidesheet-wrapper">
    <div class="d-flex align-items-center poll-widget-sidesheet__header poll-widget-sidesheet__header_name mb-35p">
      <!-- ko if: !editingName() -->
        <div class="poll-widget-sidesheet__widget-name" data-bind="text: widgetName"></div>
        <button class="f-btn f-btn--round f-btn-white poll-name-form__edit"
            type="button"
            title="Редактировать название"
            data-bind="click: function() { editingName(true); }, tooltip,
              disable: false">
          <svg-icon params="name: 'pencil'"
                    class="f-color-service"></svg-icon>
        </button>
      <!-- /ko -->
      <!-- ko if: editingName() -->
      <edit-form
        params="
          value: widgetName,
          observableValue: editFormWidgetName,
          maxLength: 255,
          onSave: function(v) { 
            widgetName(v)
            editingName(false)
          },
          onCancel: function() { editingName(false) }
        "
      ></edit-form>
      <!-- /ko -->
      <div class="ml-auto">
        <button class="f-btn f-btn-link widget__examples cursor-zoom-in" type="button" data-bind="fancyboxGalleryItem: {
          gallery: [
            { src: '/img/widget/1_.png', },
            { src: '/img/widget/2_.png', },
            { src: '/img/widget/3_.png', },
            { src: '/img/widget/4_.png', },
            { src: '/img/widget/5_.png', },
          ]
        }">Примеры виджета</button>
      </div>
    </div>
    <div class="poll-widget-sidesheet__header mb-35p">
      <!-- ko if: pollSelect -->
        <div class="poll-widget-sidesheet__coll-title mb-10p">Опрос</div>
      <!-- /ko -->
      <div class="row align-items-center">
        <!-- ko if: pollSelect -->
          <div class="col-lg-8 pt-1">
            <fc-select
              params="
                options: polls.data,
                value: poll,
                fields: { text: 'name', value: 'id' }
                placeholder: 'Опрос',
              "
            ></fc-select>
          </div>
        <!-- /ko -->
        <div class="col-lg-4">
          <switch class="mb-0" params="checked: isActive">Активен</switch>
        </div>
      </div>
    </div>
    
    <div class="poll-widget-sidesheet__tabs">
      <div class="poll-widget-sidesheet__tabs-item" data-bind="css: {active: activeTab() === 'visual'}, click: function () {activeTab('visual')}">Визуализация</div>
      <div class="poll-widget-sidesheet__tabs-item" data-bind="css: {active: activeTab() === 'target'},  click: function () {activeTab('target')}">Таргетинг <span class="ml-10p" data-bind="text: targetCounter"></span></div>
      <div class="poll-widget-sidesheet__tabs-item" data-bind="css: {active: activeTab() === 'settings'}, click: function () {activeTab('settings')}">Настройки</div>
    </div>
    <!-- ko if: activeTab() === 'settings' -->
    <div class="row mt-15p">
      <div class="col-12 col-md-8">
        <div class="widget-option-name mb-10p">
          До какого момента отображать виджет/кнопку
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Позволяет настроить отображение виджета в зависимости от его показа или полученных ответов (записываются при клике на кнопку &quot;Далее&quot;)'"></button>
        </div>
        <div class="form-group">
          <fc-select
              params="
                  options: showActions,
                  value: showAction,
                  disabled: false
              "
          ></fc-select>
        </div>
      </div>
        
      <div class="col-12 col-md-8">
        <div class="form-group">
          <fc-check params="
            checked: closeByFinishButton, 
            disabled: false, 
            label: 'Закрыть виджет по кнопке «Завершить»',
            hint: 'Виджет будет автоматически закрываться при клике на кнопку «Завершить», которая отображается под последним вопросом',
          ">
          </fc-check>
        </div>
      </div>

      <div class="col-12">
        <div class="row">
          <div class="col-12 col-md-4">
            <fc-label
              params="
                text: 'Приоритет отображения виджета',
                hint: 'Приоритет отображения виджетов устанавливается в рамках опросов всей компании, а не одного опроса'
              "
            ></fc-label>
            <div class="form-group ">
              <foquz-chars-counter
                params="value: formattedPriority, max: 6"
              >
                <input
                  type="text"
                  class="form-control text-center"
                  data-bind="
                    textInput: $parent.formattedPriority,
                    css: {'is-invalid': $parent.priorityError}
                  "
                  maxlength="6"
                >
              </foquz-chars-counter>
              <!-- ko if: priorityError() -->
                <div
                  class="form-error"
                  data-bind="text: priorityError()"
                ></div>
              <!-- /ko -->
              <div class="f-color-service f-fs-1 mt-5p">
                Приоритет для виджетов устанавливается в рамках опросов всей компании, а не одного опроса
              </div>
              <!-- ko if: !!window.isPaidRate && priorityCheckLink -->
              <div class="mt-5p">
                <a
                  class="semibold f-fs-1"
                  href="/foquz/widgets"
                  target="_blank"
                >Посмотреть приоритет всех виджетов</a>
              </div>
              <!-- /ko -->
            </div>
          </div>
        </div>
        
      </div>
    </div>
    <!-- /ko -->
    <!-- ko if: activeTab() === 'visual' -->
    <div class="poll-widget-sidesheet__row">
      <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
        <div class="poll-widget-sidesheet__coll-title">
          Появление виджета
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Появление виджета'"></button>
          <div class="ghost-label">
            <radio-group params="options: [
              { value: 'click', label: 'По клику' },
              { value: 'no-click', label: 'Без клика',}
            ], value: showMode"></radio-group>
          </div>
        </div>
      </div>
      <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
        <div class="poll-widget-sidesheet__coll-title">
          Филиал
          <div class="form-group form-group_filials">
            <div class="filials-select">
              <fc-select
                class="categorized"
                params="
                  options: collections.filials.list,
                  value: filialId,
                  placeholder: 'Без филиала',
                  clearable: true,
                "
              ></fc-select>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="poll-widget-sidesheet__row mt-5p mb-15p">
      
      <div class="poll-widget-sidesheet__coll">
        <!-- ko if: showMode() == 'click' -->
        <span class="poll-widget-sidesheet__info-text">Виджет с анкетой опроса будет отображаться на странице при клике на кнопку или шкалу оценок</span>
        <!-- /ko -->
        <!-- ko if: showMode() == 'no-click' -->
        <span class="poll-widget-sidesheet__info-text">Виджет с анкетой опроса будет всплывать на странице автоматически</span>
        <!-- /ko -->
      </div>

      <div class="poll-widget-sidesheet__coll">
        <!-- ko if: !collections.filials.list().length || (collections.filials.list().length == 1 && collections.filials.list()[0].id == '0') -->
        <span class="poll-widget-sidesheet__info-text">В настройках компании нет добавленных филиалов. </span><fc-button params="
        label: 'Добавить',
        color: 'primary',
        mode: 'text'" data-bind="click: function() {
          window.open(window.location.origin + '/foquz/settings?tab=settings&channel=email&setting=collections', '_blank');
        }, title: 'Добавить'">
      </fc-button>
      <!-- /ko -->
    </div>
    </div>
    <!-- ko if: showMode() === 'click' -->
    <div class="poll-widget-sidesheet__row mb-25p">
      <div class="ghost-label ghost-label_button-option">
        <radio-group params="options: [
          { value: 'text', label: 'Текстовая кнопка' },
          { value: 'icon', label: 'Кнопка-иконка',},
          { value: 'scale', label: 'Шкала оценок',}
        ], value: buttonMode"></radio-group>
        <!-- ko if: buttonMode() === 'scale' && !pollScale() -->
        <div class="mt-10p d-flex" data-bind="">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#F96261" stroke-width="2"/>
            <path d="M7 5C7 4.44772 7.44772 4 8 4C8.55228 4 9 4.44772 9 5V8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8V5Z" fill="#F96261"/>
            <path d="M7 11C7 10.4477 7.44772 10 8 10C8.55228 10 9 10.4477 9 11C9 11.5523 8.55228 12 8 12C7.44772 12 7 11.5523 7 11Z" fill="#F96261"/>
          </svg>            
          <span class="poll-widget-sidesheet__info-text ml-10p">Чтобы виджет отображался, первый вопрос в этом опросе должен быть одним из перечисленных типов: Звёздный рейтинг, Рейтинг NPS (стандартный), Смайл-рейтинг,  Рейтинг.</span>
        </div>
        <!-- /ko -->
      </div>
    </div>
    <div class="poll-widget-sidesheet__row mb-15p" data-bind="css: {'d-none': buttonMode() === 'scale' && !pollScale()}">
      <!-- ko if:  buttonMode() === 'text' || buttonMode() === 'icon' -->
      <div class="poll-widget-sidesheet__button-wrapper">
        <div class="poll-widget-sidesheet__button-wrapper-inner" data-bind="css: {'position-left': buttonPlacement() == 'left', 'position-right': buttonPlacement() == 'right'}">
          <button type="button" class="fc-widget-preview-button" data-bind="style: {
            fontFamily: font(),
            fontSize: fontSize() + 'px',
            color: color(),
            backgroundColor: background(),
            borderWidth: border() ? '1px' : '0',
            borderStyle: 'solid',
            borderColor: 'currentColor',
            fontWeight: bold() ? 'bold' : 'normal',
            fontStyle: italic() ? 'italic' : 'normal',
            transform: buttonPlacement() == 'left' ? 'rotate(-90deg) translate(-50%)' : buttonPlacement() == 'right' ? 'rotate(90deg) translate(-50%, -100%)' : '',
            transformOrigin: 'left top',
            borderRadius: buttonPlacement() == 'left' || buttonPlacement() == 'right' ? '0px 0px 6px 6px' : '',
            boxShadow: buttonPlacement() == 'left' || buttonPlacement() == 'right' ? '0px 0px 6px rgba(0, 0, 0, 0.25)' : 'none',
            padding: buttonMode() === 'icon' ? '4px 7px': buttonPlacement() == 'left' || buttonPlacement() == 'right' ? '4px 15px' : '4px 25px'
          }, 
          click: function() { openNoClickWidget() }">  
          <div class="fc-widget-preview-text">
            <!-- ko if:  buttonMode() === 'text' -->
            <!-- ko if:  buttonPlacement() == 'left' -->
            <svg class="fc-widget-preview-logo fc-widget-preview-logo--sm" width="12" height="19">
              <use xlink:href="#foquz-logo-sm" href="#foquz-logo-sm"></use>
            </svg>
            <!-- /ko -->
            <span data-bind="text: buttonText().length ? buttonText() : 'Пройти опрос'"></span>
            <!-- ko if:  buttonPlacement() == 'fix' -->
            <svg class="fc-widget-preview-logo fc-widget-preview-logo--lg" width="43" height="9">
              <use xlink:href="#foquz-logo-xs" href="#foquz-logo-xs"></use>
            </svg>
            <!-- /ko -->
            <!-- ko if:  buttonPlacement() == 'right' -->
            <svg class="fc-widget-preview-logo fc-widget-preview-logo--sm" width="12" height="19">
              <use xlink:href="#foquz-logo-sm" href="#foquz-logo-sm"></use>
            </svg>
            <!-- /ko -->
            <!-- /ko -->
            <!-- ko if:  buttonMode() === 'icon' -->
            <svg class="fc-widget-preview-logo fc-widget-preview-logo--lg-icon" width="19" height="19" data-bind="style: {             
              transform: buttonPlacement() == 'left' ? 'rotate(90deg)' : buttonPlacement() == 'right' ? 'rotate(-90deg)' : '',
            }">
              <use xlink:href="#foquz-logo-xs-icon" href="#foquz-logo-xs-icon"></use>
            </svg>
            <!-- /ko -->
          </div>
          </button>
        </div>
      </div>  
      <!-- /ko -->
      <!-- ko if: buttonMode() === 'scale' && pollScale() -->
        <div class="widget-sticky-preview__wrapper" data-bind="">
          <div class="preview-scale-wrapper" data-bind="style: {
              color: color,
              fontFamily: font,
              fontSize: fontSize() + 'px',
              fontWeight: bold() ? 'bold' : 'normal',
              fontStyle: italic() ? 'italic' : 'normal'
            }, click: function(_, e) { onRatingClick(e); }">
            <div class="preview-scale" data-bind="html: pollScale"></div>
          </div>
        </div>
      <!-- /ko -->
    </div>
      <!-- ko if:  buttonMode() === 'text' || buttonMode() === 'icon' -->
        <div class="poll-widget-sidesheet__row">
          <div class="ghost-label ghost-label_button-option">
            <radio-group params="options: [
              { value: 'fix', label: 'Фиксированное положение' },
              { value: 'left', label: 'Плавающая слева',},
              { value: 'right', label: 'Плавающая справа',}
            ], value: buttonPlacement"></radio-group>
          </div>
        </div>
      <!-- /ko -->
      <!-- ko if:  buttonMode() === 'text' || buttonMode() === 'scale' -->
        <div class="row mt-4">
          <div class="col-4">
            <!-- ko if: buttonMode() !== 'scale' -->
              <div class="form-group">
                <label for="" class="form-label">Текст кнопки</label>
                <input type="text" class="form-control" placeholder="Пройти опрос" data-bind="textInput: buttonText" maxlength="30">
                <div class="chars-counter__value">30</div>
              </div>
            <!-- /ko -->
            <!-- ko if: buttonMode() == 'scale' -->
              <div class="">
                <label for="" class="form-label" data-bind="text: 'Цвет текста' ">
                </label>
                <color-picker params="value: color"></color-picker>
              </div>
            <!-- /ko -->
          </div>
          <div class="col-4">
            <div class="form-group">
              <label for="" class="form-label">Наименование шрифта</label>
              <collection-select params="collection: collections.fonts, value: font, search: true"></collection-select>
            </div>
          </div>
          <div class="col-4">
            <div class="form-group">
              <label for="" class="form-label">Размер текста</label>
              <div class="d-flex align-items-center">
                <collection-select class="select-font" params="collection: collections.fontSizes, value: fontSize, search: true, selectConfig: {
                  dropdownAutoWidth: false
                }"></collection-select>
                <button type="button" class="button-ghost button-font" data-bind="css: {
                      active: bold
                    }, click: function() {
                      console.log(bold())
                      bold(!bold())
                    }, tooltip, tooltipText: 'Жирное начертание'">
                  <svg-icon params="name: 'text-bold'"></svg-icon>
                </button>
                <button type="button" class="button-ghost button-font" data-bind="css: {
                      active: italic
                    }, click: function() {
                      italic(!italic())
                    }, tooltip, tooltipText: 'Курсивное начертание'">
                  <svg-icon params="name: 'text-italic'"></svg-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      <!-- /ko -->
      <!-- ko if: buttonMode() !== 'scale' -->
        <div class="row" data-bind="css: {'mt-4': buttonMode() == 'icon' }">
          <div class="col-4">
            <div class="">
              <label for="" class="form-label" data-bind="text: buttonMode() == 'text' ? 'Цвет текста кнопки' : buttonMode() == 'icon' ? 'Цвет иконки' : 'Цвет текста' ">
              </label>
              <color-picker params="value: color"></color-picker>
            </div>
          </div>
          <div class="col-4">
            <div class="">
              <label for="" class="form-label">
                Цвет фона кнопки
              </label>
              <color-picker params="value: background"></color-picker>
            </div>
          </div>
          <div class="col-4 pt-20p">
            <div class="ghost-label h-100 d-flex flex-column justify-content-center">
              <switch class="mb-0" params="checked: border">Обводка кнопки</switch>
            </div>
          </div>
        </div>
      <!-- /ko -->
      <hr data-bind="css: {'mt-0': buttonMode() == 'scale'}"/>
      <div class="simple-view">
        <fc-switch params="checked: simpleView, label: 'Упрощенный вид опроса'"></fc-switch>
        <div class="mt-15p">
          <div class="f-color-service f-fs-1 lh-16p">
            В упрощенном виде опрос открывается в окне шириной 680 px с настройками дизайна по умолчанию.
          </div>

          <div class="mt-5p">
            <fc-button params="mode: 'text', color: 'primary', label: 'Пример упрощенного вида'" data-bind="fancyboxGalleryItem: {
              gallery: [
                { src: '/img/widget/simple-1.png', },
                { src: '/img/widget/simple-2.png', },
                { src: '/img/widget/simple-3.png', },
              ]
            }"></fc-button>
          </div>
        </div>
      </div>    
    <!-- /ko -->
    <!-- ko if: showMode() === 'no-click' -->
      <div class="poll-widget-sidesheet__row mb-4">
        <div class="ghost-label ghost-label_button-option">
          <radio-group params="options: [
            { value: 'page-stop', label: 'Page-stop' },
            { value: 'lite-page-stop', label: 'Упрощенный Page-stop',},
            { value: 'hello', label: 'Hello-board',}
          ], value: noClickMode"></radio-group>
        </div>
      </div>
      <div class="poll-widget-sidesheet__row poll-widget-sidesheet__row_no-click lh-16p">
        <!-- ko if: noClickMode() === 'page-stop' -->
        Опрос открывается в полную ширину окна поверх контента страницы
        <!-- /ko -->
        <!-- ko if: noClickMode() === 'lite-page-stop' -->
        Опрос открывается в окне шириной 680 px с настройками дизайна по умолчанию поверх контента страницы
        <!-- /ko -->
        <!-- ko if: noClickMode() === 'hello' -->
        Опрос открывается в окне шириной 330 px с настройками дизайна по умолчанию, не блокируя контента страницы
        <!-- /ko -->
      </div>
      <fc-button
        class="mt-10p"
        params="
          mode: 'text',
          color: 'primary',
          label: 'Предпросмотр виджета',
          click: (event) => openNoClickWidget(),
        "
      ></fc-button>
    <!-- /ko -->
    <hr/>

    <div class="f-h2 mb-25p">Окно виджета</div>
    <div class="row">
      <div class="col-4">
        <div class="form-group ">
          <fc-label
            params="
              text: 'Ширина окна',
              hint: 'Если размеры окна виджета не указаны, то используется стандартное отображение. ' + minWindowWidth().text
            "
          ></fc-label>
          <variant-text-field
            data-bind="css: {'is-invalid': formControlErrorStateMatcher(windowWidth.pureValue)}"
            params="
              options: windowUnitVariants,
              variantValue: windowWidthUnit,
              value: windowWidth.value,
              placeholder: placeholderWindowSettings.windowWidth(),
            "
          ></variant-text-field>
          <fc-error
            params="
              show: formControlErrorStateMatcher(windowWidth.pureValue),
              text: windowWidth.pureValue.error
            "
          ></fc-error>
        </div>
      </div>
      <div class="col-4">
        <div class="form-group ">
          <fc-label params="text: 'Высота окна, %'"></fc-label>
          <input
            type="text"
            class="form-control variant-text-field__form-control"
            data-bind="
              textInput: windowHeight.value,
              css: {
                'is-invalid': formControlErrorStateMatcher(windowHeight.pureValue),
              },
              attr: {
                placeholder: placeholderWindowSettings.windowHeight(),
              }
            "
          >
          <div class="f-color-service f-fs-1 mt-5p">
            Чтобы высота виджета подстраивалась под его содержимое, оставьте поле пустым
          </div>
          <fc-error
            params="
              show: formControlErrorStateMatcher(windowHeight.pureValue),
              text: windowHeight.pureValue.error
            "
          ></fc-error>
        </div>
      </div>
      <div class="col-4">
        <div class="form-group ">
          <fc-label params="text: 'Радиус скругления углов'"></fc-label>
          <variant-text-field
            data-bind="css: {'is-invalid': formControlErrorStateMatcher(windowBorderRadius.pureValue)}"
            params="
              options: windowUnitVariants,
              variantValue: windowBorderRadiusUnit,
              value: windowBorderRadius.value,
              placeholder: placeholderWindowSettings.windowBorderRadius(),
            "
          ></variant-text-field>
          <fc-error
            params="
              show: formControlErrorStateMatcher(windowBorderRadius.pureValue),
              text: windowBorderRadius.pureValue.error
            "
          ></fc-error>
        </div>
      </div>
      <div class="col-6">
        <div class="form-group ">
          <fc-label params="text: 'Расположение окна'"></fc-label>
          <select
            data-bind="
              value: windowPosition,
              options: windowPositionOtions,
              optionsText: 'name',
              optionsValue: 'id',
              select2: {
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                dropdownAutoWidth: false,
              },
            "
          ></select>
        </div>
      </div>
    </div>

    <div>
      <div class="f-color-service f-fs-1 lh-16p">
        <div>
          Указанные размеры применяются только к десктопной и планшетной версиям виджета.
        </div>
        <div>
          Для мобильной версии ширина виджета фиксированная — 90% от размера экрана.
        </div>
        <div>
          <!-- ko text: minWindowWidth().text -->
          <!-- /ko -->
        </div>
        <div>
          Если размеры окна виджета не указаны, то используется стандартное отображение.
        </div>
      </div>
  
      <fc-button
        class="mt-5p"
        params="
          mode: 'text',
          color: 'primary',
          label: 'Сбросить значения',
          click: () => resetWindowSettings(),
        "
      ></fc-button>
    </div>

    <hr/>
    <section class="widget-code mt-2">
      <label class="form-label">
        Код для вставки виджета на сайт
        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Код для вставки виджета на сайт'"></button>
      </label>
      <!-- ko if: code -->
      <div class="form-control sender-page__html-wrapper" id="html-code-input">

        <fc-button class="copy-btn"
                params="mode: 'text', size: 'auto', icon: {name: 'copy', size: 20}, disabled: !code(), click: function() {
                  if (code()) {
                    copyHTML()
                  }
              }"
                data-bind="fbPopper, title: 'Скопировать в буфер'"
              ></fc-button>
        
        <pre class="sender-page__html-code"
             data-bind="text: code() ?  widgetCode() : ''"></pre>
        
        
      </div>
      <!-- /ko -->
      <!-- ko if: !code() -->
      <copy-input class="" params="value: 'Чтобы получить код для вставки на сайт, начните настраивать виджет', disabled: true"></copy-input>
      <!-- /ko -->
      <div class="f-fs-1 widget-code__info mt-10p">
        Скопируйте код для виджета и вставьте его в нужное место на Вашем сайте. В разделе <a href='https://foquz.ru/foquz/user-wiki/vidgety-oprosa' target="_blank" class="font-weight-500">Помощь</a> можно получить подробную информацию о виджете.
      </div>
      <fc-status-popper params="target: 'html-code-input', show: codeCopied, mode: 'success'">
        Код скопирован в буфер
      </fc-status-popper>
    </section>
    
    <!-- /ko -->
    <!-- ko if: activeTab() === 'target' -->
    <div class="poll-widget-sidesheet__row">
      <fc-switch params="checked: enableTarget, label: 'Настроить таргетинг', hint: 'Настроить таргетинг',"></fc-switch>
    </div>
    <!-- ko template: {
                  foreach: templateIf(disabled(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
    <div class="row">
      <div class="col-12 mt-15p">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-10p">
          <path d="M8 5V8M8 11V11.0234M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#F96261" stroke-width="2" stroke-linecap="round"/>
        </svg>
        <span>Сейчас виджет показывается всем посетителям страницы. Настройте таргетинг, чтобы показывать его по условиям.</span>
      </div>
      
    </div>
    <!-- /ko -->

    <!-- ko template: {
                  foreach: templateIf(enableTarget(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="mt-15p condition-info">Все триггеры работают через «И»</div>
      <!-- /ko -->
    <div class="row mt-40p">
      <div class="col-12">
        <span class="widget-target-title" data-bind="css: {'enabled': disabled}">Таргетинги на пользователей</span>
      </div>
    </div>
    <hr>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <fc-switch class="widget-target-option-label" data-bind="css: {'enabled': disabled, 'enable-option': !enableTag()}, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            enableTag(true)
          }
          
        }" params="checked: enableTag, disabled: disabled, label: 'По тегу контакта'"></fc-switch>
        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Позволяет настроить показ виджета по тегу тех пользователей сайта, которые добавлены в раздел &quot;Контакты&quot;'"></button>
        <toggler-icon class="ml-15p widget-target-option-label" data-bind="click: function () {showTag(!showTag())},css: {'enabled': disabled, 'enable-option': !enableTag()}" params="isOpen: showTag, color: '#73808D', width: 14, height: 8"></toggler-icon>
        
      </div>
      <!-- ko template: {
                  foreach: templateIf(enableTag(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
      <div class="col-12 mt-15p condition-info">Условия между собой работают через «И», выбранные значения в поле Теги контакта — через «ИЛИ»</div>
      <!-- /ko -->
    </div>
    <!-- ko template: {
                  foreach: templateIf(showTag(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="row">
                <div class="col-12">
                  <div class="widget-option-wrapper">
                    <hr>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12">
                  <div class="widget-option-wrapper" data-bind="css: {disabled: disabled }, click: function () {
                    if (!enableTarget()) {
                      enableTarget(true)
                      
                    }
                    enableTag(true)
                    return true
                  }">
                    <!-- ko foreach: tags -->
                    <div class="widget-option-row">
                      <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
                        <div class="widget-option-name mb-10p">
                          Условие для тега
                        </div>
                          <div class="form-group">
                            <fc-select
                                params="
                                    options: $parent.tagActions,
                                    value: $data.value,
                                    disabled: !$parent.enableTarget()
                                "
                            ></fc-select>
                        </div>
                      </div>
                      <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
                        <div class="widget-option-name mb-10p">
                          Теги контакта
                        </div>
                        <div class="form-group">
                          <fc-select params="
                          options: $parent.tagOptions,
                          value: $data.tags,
                          disabled: !$parent.enableTarget(),
                          multiple: true,
                          invalid: $data.tagError,
                          placeholder: 'Выберите теги'
                      "></fc-select>
                      <!-- ko if: $data.tagError -->
                        <div class="form-error">Обязательное поле</div>
                      <!-- /ko -->
                        </div>
                      </div>
                      <!-- ko if: $parent.tags().length > 1 -->
                      <fc-button
                          class="poll-widget-sidesheet__remove-button" params="color: 'danger', icon: 'times', dasable: !$parent.enableTarget(), shape: 'square', click: function() {$parent.removeTag($data)}"
                      ></fc-button>
                      <!-- /ko -->
                    </div>
                    <div><hr></div>
                    <!-- /ko -->
                    
                    <fc-button
                      class="mb-4 add-option-button"
                      data-bind="css: {'disabled-button': (tags().length && tags().some(i => !i.tags().length)) || !$parent.enableTarget()}"
                      params="
                        icon: 'plus',
                        color: 'success',
                        mode: 'text',
                        label: 'Добавить условие',
                        click: function() { 
                          if (tags().length && tags().some(i => !i.tags().length) || !$parent.enableTarget()) return
                          addTag()
                        }
                      "
                    ></fc-button>
                    
                  
                  </div>
                </div>
              </div>
    <!-- /ko -->
    <hr>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <fc-switch class="widget-target-option-label" data-bind="css: {'enabled': disabled, 'enable-option': !enableCookie()}, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            enableCookie(true)
          }
          
        }" params="checked: enableCookie, disabled: disabled, label: 'Таргетинг на cookie'"></fc-switch>
        <toggler-icon class="ml-15p widget-target-option-label" data-bind="click: function () {showCookie(!showCookie())},css: {'enabled': disabled, 'enable-option': !enableCookie()}" params="isOpen: showCookie, color: '#73808D', width: 14, height: 8"></toggler-icon>
      </div>
      <!-- ko template: {
                  foreach: templateIf(enableCookie(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="col-12 mt-15p condition-info">Условия между собой работают через «ИЛИ»</div>
      <!-- /ko -->
    </div>

    <!-- ko template: {
                  foreach: templateIf(showCookie(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
    <div class="row">
      <div class="col-12">
        <div class="widget-option-wrapper">
          <hr>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-12">
        <div class="widget-option-wrapper" data-bind="css: {disabled: disabled }, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            
          }
          enableCookie(true)
          return true
        }">
          <!-- ko foreach: cookies -->
          <div class="widget-option-row">
            <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
              <div class="widget-option-name mb-10p">
                Наименование cookie <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Наименование cookie'"></button>
              </div>
                <div class="form-group">
                  <fc-select
                      params="
                          options: $parent.cookiesActions,
                          value: $data.cookieValue,
                          disabled: !$parent.enableTarget()
                      "
                  ></fc-select>
              </div>
            </div>
            <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
              <div class="widget-option-name mb-10p">
                &nbsp;
              </div>
              <div class="form-group">
                <input type="text" class="form-control" placeholder="Введите значение" data-bind="textInput: $data.cookieText,disable: !$parent.enableTarget(), css: {'is-invalid': $data.textError}">
                <!-- ko if: $data.textError -->
                <div class="form-error">Обязательное поле</div>
                <!-- /ko -->
              </div>
            </div>
            <!-- ko if: $parent.cookies().length > 1 -->
            <fc-button
                class="poll-widget-sidesheet__remove-button" params="color: 'danger', icon: 'times', dasable: !$parent.enableTarget(), shape: 'square', click: function() {$parent.removeCookie($data)}"
            ></fc-button>
            <!-- /ko -->
          </div>
          <div class="widget-option-row mt-25p mb-10p">
            <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
              <div class="widget-option-name mb-10p">
                Значение <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Значение'"></button>
              </div>
              <div class="ghost-label">
                <radio-group params="options: [
                  { value: 0, label: 'Без значения' },
                  { value: 1, label: 'Со значением',}
                ], disabled: !$parent.enableTarget(), value: $data.cookieValueMode"></radio-group>
              </div>
            </div>
            <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
              <div class="widget-option-name mb-20p">
                &nbsp;
              </div>
              <!-- ko if: $data.cookieValueMode() -->
              <div class="form-group">
                <fc-select
                    params="
                        options: $parent.cookiesActions,
                        value: $data.cookieValueModeAction,
                        disabled: !$parent.enableTarget()
                    "
                ></fc-select>
            </div>
              <!-- /ko -->
            </div>
          </div>
          <!-- ko if: $data.cookieValueMode() -->
          <div class="widget-option-row mt-40p">
            <div class="form-group widget-option-text-value">
              <input type="text" class="form-control" placeholder="Введите значение" data-bind="textInput: $data.cookieValueModeText, disable: !$parent.enableTarget(), css: {'is-invalid': $data.modeTextError}">
              <!-- ko if: $data.modeTextError -->
              <div class="form-error">Обязательное поле</div>
              <!-- /ko -->
            </div>
          </div>
          <!-- /ko -->
          <div data-bind="css: {'mt-35p': !$data.cookieValueMode() }"><hr></div>
          <!-- /ko -->
          <fc-button
            class="mb-4 add-option-button"
            data-bind="css: {'disabled-button': (cookies().length && cookies().some(i => !i.cookieText().length || (i.cookieValueMode() && !i.cookieValueModeText().length))) || !$parent.enableTarget()}"
            params="
              icon: 'plus',
              color: 'success',
              mode: 'text',
              label: 'Добавить условие',
              click: function() { 
                if (cookies().length && cookies().some(i => !i.cookieText().length || (i.cookieValueMode() && !i.cookieValueModeText().length)) || !$parent.enableTarget()) return
                addCookieValue() 
              }
            "
          ></fc-button>
          
        
        </div>
      </div>
    </div>
    <!-- /ko -->
    <hr>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <fc-switch class="widget-target-option-label" data-bind="css: {'enabled': disabled, 'enable-option': !enableType()}, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            enableType(true)
          }
          
        }" params="checked: enableType, disabled: disabled,  label: 'Тип устройства'"></fc-switch>
        <toggler-icon class="ml-15p widget-target-option-label" data-bind="click: function () {showType(!showType())},css: {'enabled': disabled, 'enable-option': !enableType()}" params="isOpen: showType, color: '#73808D', width: 14, height: 8"></toggler-icon>
      </div>
    </div>
    <!-- ko template: {
                  foreach: templateIf(showType(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="row mt-25p">
                <div class="col-12">
                  <div class="widget-option-wrapper d-flex widget-option-wrapper-depth" data-bind="click: function () {
                    if (!enableTarget()) {
                      enableTarget(true)
                      
                    }
                    enableType(true)
                    return true
                    
                  }">
                    <foquz-checkbox class="mr-30p" params="name: 'Компьютеры',
                      checked: cookiePc,
                      disabled: disabled
                      ">Компьютеры</foquz-checkbox>
                    <foquz-checkbox class="mr-30p" params="name: 'Планшеты',
                    checked: cookiePad,
                    disabled: disabled
                    ">Планшеты</foquz-checkbox>
                    <foquz-checkbox params="name: 'Смартфоны',
                    checked: cookiePhone,
                    disabled: disabled
                    ">Смартфоны</foquz-checkbox>
                  </div>
                  <!-- ko if: typesError -->
                  <div class="widget-option-wrapper form-error">Выберите хотя бы один тип устройства</div>
                  <!-- /ko -->
                </div>
              </div>
    <!-- /ko -->
    <hr>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <fc-switch class="widget-target-option-label" data-bind="css: {'enabled': disabled, 'enable-option': !enableUser()}, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            enableUser(true)
          }
          
        }" params="checked: enableUser, disabled: disabled, label: 'Доля пользователей'"></fc-switch>
        <toggler-icon class="ml-15p widget-target-option-label" data-bind="click: function () {showUser(!showUser())},css: {'enabled': disabled, 'enable-option': !enableUser()}" params="isOpen: showUser, color: '#73808D', width: 14, height: 8"></toggler-icon>
      </div>
    </div>
    <!-- ko template: {
                  foreach: templateIf(showUser(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="row mt-25p">
                <div class="col-12">
                  <div class="widget-option-wrapper d-flex widget-option-row" data-bind="click: function () {
                    if (!enableTarget()) {
                      enableTarget(true)
                      
                    }
                    enableUser(true)
                    return true
                  }">
                    <div class="form-group widget-slider-group mb-0" data-bind="css: {'disabled': disabled}">
                      <label class="form-label">Процент охвата пользователей</label>

                      <div class="input-group slider-input-group">
                          <div class="form-control" data-bind="slider, sliderMin: 0, sliderMax: 100, sliderValue: userPercent, disabled: disabled"></div>

                          <div class="">
                            <input type="text" class="form-control input-group-append_" placeholder="Введите значение" data-bind="onlyNumbers, textInput: userPercent, disable: !$parent.enableTarget(),foquzMask, maskPattern: '000',">
                          </div>
                      </div>
                      <div class="d-flex slider-marker-wrapper">
                        <div class="slider-marker">0</div>
                        <div class="slider-marker ml-auto">100</div>
                      </div>
                      
                  </div>
                  </div>
                </div>
              </div>
    <!-- /ko -->
    <hr>
    <div class="row mt-40p">
      <div class="col-12">
        <span class="widget-target-title" data-bind="css: {'enabled': disabled}">Таргетинги по действиям</span>
      </div>
    </div>
    <hr>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <fc-switch class="widget-target-option-label" data-bind="css: {'enabled': disabled, 'enable-option': !enablePages()}, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            enablePages(true)
          }
        }" params="checked: enablePages, disabled: disabled, label: 'Пользователи на конкретных страницах'"></fc-switch>
        <toggler-icon class="ml-15p widget-target-option-label" data-bind="click: function () {showPages(!showPages())},css: {'enabled': disabled, 'enable-option': !enablePages()}" params="isOpen: showPages, color: '#73808D', width: 14, height: 8"></toggler-icon>
      </div>
      <!-- ko template: {
                  foreach: templateIf(enablePages(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="col-12 mt-15p condition-info">Условия между собой работают через «ИЛИ». Дополнительные условия всегда работают через «И»</div>
      <!-- /ko -->
    </div>
    <!-- ko template: {
                  foreach: templateIf(showPages(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="row">
                <div class="col-12">
                  <div class="widget-option-wrapper">
                    <hr>
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-12">
                  <div class="widget-option-wrapper" data-bind="click: function () {
                    if (!enableTarget()) {
                      enableTarget(true)
                      
                    }
                    enablePages(true)
                    return true
                  }">
                    <!-- ko foreach: pages -->
                    <div class="widget-option-row" data-bind="css: {'disabled': $parent.disabled()}">
                      <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
                        <div class="widget-option-name mb-10p">
                          Условие для URL
                        </div>
                          <div class="form-group">
                            <fc-select
                                params="
                                    options: $parent.pagesActions,
                                    value: $data.rool,
                                    disabled: !$parent.enableTarget(),
                                "
                            ></fc-select>
                        </div>
                      </div>
                      <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
                        <div class="widget-option-name mb-10p">
                          Значение
                        </div>
                        <div class="form-group">
                          <input type="text" class="form-control" placeholder="Введите значение" data-bind="textInput: $data.adress, disable: !$parent.enableTarget(), css: {'is-invalid': $data.adressError}">
                          <!-- ko if: $data.adressError -->
                          <div class="form-error">Обязательное поле</div>
                          <!-- /ko -->
                        </div>
                      </div>
                      <!-- ko if: $parent.pages().length > 1 -->
                        <fc-button
                          class="poll-widget-sidesheet__remove-button" params="color: 'danger', icon: 'times', shape: 'square', click: function() {$parent.removePage($data)}"
                      ></fc-button>
                      <!-- /ko -->
                    </div>
                    <div><hr></div>
                    <!-- /ko -->
                    <fc-button
                      class="add-option-button"
                      data-bind="css: {'disabled-button': (pages().length && pages().some(i => !i.adress().length)) || disabled}"
                      params="
                        icon: 'plus',
                        color: 'success',
                        mode: 'text',
                        label: 'Добавить условие',
                        click: function() { 
                          if ((pages().length && pages().some(i => !i.adress().length)) || disabled()) return
                          addPage() 
                        }
                      "
                    ></fc-button>

                    <div class="row mt-40p">
                      <div class="col-12">
                        <span class="widget-target-title" data-bind="css: {'enabled': disabled}">Дополнительные условия</span>
                      </div>
                    </div>
                    <foquz-checkbox class="mt-25p" params="name: '',
                    checked: enableVisitCounter,
                    disabled: disabled
                    ">Количество переходов на страницу, после которого вызывать виджет</foquz-checkbox>
                    <!-- ko template: {
                        foreach: templateIf(enableVisitCounter(), $data),
                        afterAdd: slideAfterAddFactory(400),
                        beforeRemove: slideBeforeRemoveFactory(400)
                    } -->
                    <div class="row mt-15p ml-35p" data-bind="css: {'enabled': disabled}">
                      <div class="extra-users-first-col mr-30p">
                        <div class="widget-option-name mb-10p">
                          Переходы
                        </div>
                        <div class="form-group mb-0">
                          <input type="text" class="form-control" placeholder="0" data-bind="textInput: visitCounter, foquzMask, maskPattern: '00000', numericIntervalField: { min: 1, max: 99999 }, disable: !$parent.enableTarget(), css: {'is-invalid': visitCounterError}" maxlength="5">
                          <!-- ko if: visitCounterError -->
                          <div class="form-error">Обязательное поле</div>
                          <!-- /ko -->
                        </div>
                      </div>
                      <div class="extra-users-second-col">
                        <div class="widget-option-name mb-10p">
                          Счетчик
                        </div>
                        <div class="form-group mb-0">
                          <fc-select
                                params="
                                    options: visitOptions,
                                    value: visitOptionValue,
                                    disabled: !$parent.enableTarget(),
                                "
                            ></fc-select>
                        </div>
                      </div>
                      
                    </div>
                    <!-- /ko -->
                    <foquz-checkbox class="mt-25p" params="name: '',
                    checked: enableVisitTimer,
                    disabled: disabled
                    ">Время после последнего перехода на страницу</foquz-checkbox>
                    <!-- ko template: {
                        foreach: templateIf(enableVisitTimer(), $data),
                        afterAdd: slideAfterAddFactory(400),
                        beforeRemove: slideBeforeRemoveFactory(400)
                    } -->
                    <div class="row mt-15p ml-35p" data-bind="css: {'enabled': disabled}">
                      <div class="extra-users-first-col mr-30p">
                        <div class="widget-option-name mb-10p">
                          Секунд
                        </div>
                        <div class="form-group mb-0">
                          <input type="text" class="form-control" placeholder="0" data-bind="textInput: visitTimer, foquzMask, maskPattern: '00000000000', numericIntervalField: { min: 1, max: 60000000 }, disable: !$parent.enableTarget(), css: {'is-invalid': visitTimerError}" maxlength="5">
                          <!-- ko if: visitTimerError -->
                          <div class="form-error">Обязательное поле</div>
                          <!-- /ko -->
                        </div>
                      </div>
                      <div class="extra-users-second-col">
                        <div class="widget-option-name mb-10p">
                          &nbsp;
                        </div>
                        <div class="form-group mb-0">
                          <fc-select
                                params="
                                    options: visitTimerOptions,
                                    value: visitTimerOptionValue,
                                    disabled: !$parent.enableTarget(),
                                "
                            ></fc-select>
                        </div>
                      </div>
                      
                    </div>
                    <!-- /ko -->
                    <div><hr></div>
                    <foquz-checkbox params="name: '',
                    checked: enableHash,
                    disabled: disabled
                    ">Учитывать хэш (#) в url</foquz-checkbox>
                  </div>
                </div>
              </div>
              
              <!-- /ko -->
              <hr>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <fc-switch class="widget-target-option-label" data-bind="css: {'enabled': disabled, 'enable-option': !enableEvents()}, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            enableEvents(true)
          }
        }" params="checked: enableEvents, disabled: disabled, label: 'Триггер по событию'"></fc-switch>
        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Позволяет настроить показ виджета по событиям, которые добавлены на сайт'"></button>
        <toggler-icon class="ml-15p widget-target-option-label" data-bind="click: function () {showEvents(!showEvents())},css: {'enabled': disabled, 'enable-option': !enableEvents()}" params="isOpen: showEvents, color: '#73808D', width: 14, height: 8"></toggler-icon>
      </div>
      <!-- ko template: {
                  foreach: templateIf(enableEvents(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="col-12 mt-15p condition-info">Условия между собой работают через «ИЛИ». Дополнительные условия всегда работают через «И»</div>
      <!-- /ko -->
    </div>
    <!-- ko template: {
                  foreach: templateIf(showEvents(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="row">
                <div class="col-12">
                  <div class="widget-option-wrapper">
                    <hr>
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-12">
                  <div class="widget-option-wrapper" data-bind="click: function () {
                    if (!enableTarget()) {
                      enableTarget(true)
                    }
                    enableEvents(true)
                    return true
                  }">
                    <!-- ko foreach: events_ -->
                    <div class="widget-option-row" data-bind="css: {'disabled': $parent.disabled()}">
                      <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
                        <div class="widget-option-name mb-10p">
                          Условие для наименования события
                        </div>
                          <div class="form-group">
                            <fc-select
                                params="
                                    options: $parent.pagesActions,
                                    value: $data.rool,
                                    disabled: !$parent.enableTarget(),
                                "
                            ></fc-select>
                        </div>
                      </div>
                      <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
                        <div class="widget-option-name mb-10p">
                          Наименование события
                        </div>
                        <div class="form-group">
                          <input type="text" class="form-control" placeholder="Введите значение" data-bind="textInput: $data.adress, disable: !$parent.enableTarget(), css: {'is-invalid': $data.adressError}">
                          <!-- ko if: $data.adressError -->
                          <div class="form-error">Обязательное поле</div>
                          <!-- /ko -->
                        </div>
                      </div>
                      <!-- ko if: $parent.events_().length > 1 -->
                        <fc-button
                          class="poll-widget-sidesheet__remove-button" params="color: 'danger', icon: 'times', shape: 'square', click: function() {$parent.removeEvent($data)}"
                      ></fc-button>
                      <!-- /ko -->
                    </div>
                    <div><hr></div>
                    <!-- /ko -->
                    <fc-button
                      class="add-option-button"
                      data-bind="css: {'disabled-button': (events_().length && events_().some(i => !i.adress().length)) || disabled}"
                      params="
                        icon: 'plus',
                        color: 'success',
                        mode: 'text',
                        label: 'Добавить условие',
                        click: function() { 
                          if ((events_().length && events_().some(i => !i.adress().length)) || disabled()) return
                          addEvent() 
                        }
                      "
                    ></fc-button>

                    <div class="row mt-40p">
                      <div class="col-12">
                        <span class="widget-target-title" data-bind="css: {'enabled': disabled}">Дополнительные условия</span>
                      </div>
                    </div>
                    <foquz-checkbox class="mt-25p" params="name: '',
                    checked: enableEventCounter,
                    disabled: disabled
                    ">Количество срабатываний события, после которого показать виджет</foquz-checkbox>
                    <!-- ko template: {
                        foreach: templateIf(enableEventCounter(), $data),
                        afterAdd: slideAfterAddFactory(400),
                        beforeRemove: slideBeforeRemoveFactory(400)
                    } -->
                    <div class="row mt-15p ml-35p" data-bind="css: {'enabled': disabled}">
                      <div class="extra-users-first-col mr-30p">
                        <div class="widget-option-name mb-10p">
                          Количество срабатываний
                        </div>
                        <div class="form-group mb-0">
                          <input type="text" class="form-control" placeholder="0" data-bind="textInput: eventCounter, foquzMask, maskPattern: '00000', numericIntervalField: { min: 1, max: 99999 }, disable: !$parent.enableTarget(), css: {'is-invalid': eventCounterError}" maxlength="5">
                          <!-- ko if: eventCounterError -->
                          <div class="form-error">Обязательное поле</div>
                          <!-- /ko -->
                        </div>
                      </div>
                      <div class="extra-users-second-col">
                        <div class="widget-option-name mb-10p">
                          Счетчик
                        </div>
                        <div class="form-group mb-0">
                          <fc-select
                                params="
                                    options: visitOptions,
                                    value: eventOptionValue,
                                    disabled: !$parent.enableTarget(),
                                "
                            ></fc-select>
                        </div>
                      </div>
                      
                    </div>
                    <!-- /ko -->
                    <foquz-checkbox class="mt-25p" params="name: '',
                    checked: enableEventTimer,
                    disabled: disabled
                    ">Через N секунд после срабатывания события</foquz-checkbox>
                    <!-- ko template: {
                        foreach: templateIf(enableEventTimer(), $data),
                        afterAdd: slideAfterAddFactory(400),
                        beforeRemove: slideBeforeRemoveFactory(400)
                    } -->
                    <div class="row mt-15p ml-35p" data-bind="css: {'enabled': disabled}">
                      <div class="extra-users-first-col mr-30p">
                        <div class="widget-option-name mb-10p">
                          Секунд
                        </div>
                        <div class="form-group mb-0">
                          <input type="text" class="form-control" placeholder="0" data-bind="textInput: eventTimer, foquzMask, maskPattern: '00000000000', numericIntervalField: { min: 1, max: 60000000 }, disable: !$parent.enableTarget(), css: {'is-invalid': eventTimerError}" maxlength="5">
                          <!-- ko if: eventTimerError -->
                          <div class="form-error">Обязательное поле</div>
                          <!-- /ko -->
                        </div>
                      </div>
                      <div class="extra-users-second-col">
                        <div class="widget-option-name mb-10p">
                          &nbsp;
                        </div>
                        <div class="form-group mb-0">
                          <fc-select
                                params="
                                    options: eventTimerOptions,
                                    value: eventTimerOptionValue,
                                    disabled: !$parent.enableTarget(),
                                "
                            ></fc-select>
                        </div>
                      </div>
                      
                    </div>
                    <!-- /ko -->
                </div>
              </div>
              
    </div>
    <!-- /ko -->

    <hr>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <fc-switch class="widget-target-option-label" data-bind="css: {'enabled': disabled, 'enable-option': !enableVariable()}, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            enableVariable(true)
          }
          
        }" params="checked: enableVariable, disabled: disabled, label: 'Триггер по переменным'"></fc-switch>
        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Позволяет настроить отображение виджета, исходя из значений параметров, переданных при инициализации виджета или при вызове событий'"></button>
        <toggler-icon class="ml-15p widget-target-option-label" data-bind="click: function () {showVariable(!showVariable())},css: {'enabled': disabled, 'enable-option': !enableVariable()}" params="isOpen: showVariable, color: '#73808D', width: 14, height: 8"></toggler-icon>
      </div>
      <!-- ko template: {
                  foreach: templateIf(enableVariable(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="col-12 mt-15p condition-info">Условия между собой работают через «ИЛИ»</div>
      <!-- /ko -->
    </div>

    <!-- ko template: {
                  foreach: templateIf(showVariable(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
    <div class="row">
      <div class="col-12">
        <div class="widget-option-wrapper">
          <hr>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-12">
        <div class="widget-option-wrapper" data-bind="css: {disabled: disabled }, click: function () {
          if (!enableTarget()) {
            enableTarget(true)
            
          }
          enableVariable(true)
          return true
        }">
          <!-- ko foreach: variables -->
          <div class="widget-option-row">
            <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
              <div class="widget-option-name mb-10p">
                Условие для наименования переменной <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Условие для наименования переменной'"></button>
              </div>
                <div class="form-group">
                  <fc-select
                      params="
                          options: $parent.cookiesActions,
                          value: $data.cookieValue,
                          disabled: !$parent.enableTarget()
                      "
                  ></fc-select>
              </div>
            </div>
            <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
              <div class="widget-option-name mb-10p">
                Наименование переменной
              </div>
              <div class="form-group">
                <input type="text" class="form-control" placeholder="Введите значение" data-bind="textInput: $data.cookieText,disable: !$parent.enableTarget(), css: {'is-invalid': $data.textError}">
                <!-- ko if: $data.textError -->
                <div class="form-error">Обязательное поле</div>
                <!-- /ko -->
              </div>
            </div>
            <!-- ko if: $parent.variables().length > 1 -->
            <fc-button
                class="poll-widget-sidesheet__remove-button" params="color: 'danger', icon: 'times', dasable: !$parent.enableTarget(), shape: 'square', click: function() {$parent.removeVariable($data)}"
            ></fc-button>
            <!-- /ko -->
          </div>
          <div class="widget-option-row mt-25p mb-10p">
            <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
              <div class="widget-option-name mb-10p">
                Значение <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Значение'"></button>
              </div>
              <div class="ghost-label">
                <radio-group params="options: [
                  { value: 0, label: 'Без значения' },
                  { value: 1, label: 'Со значением',}
                ], disabled: !$parent.enableTarget(), value: $data.cookieValueMode"></radio-group>
              </div>
            </div>
            <div class="poll-widget-sidesheet__coll poll-widget-sidesheet__coll_first">
              <div class="widget-option-name mb-20p">
                &nbsp;
              </div>
              <!-- ko if: $data.cookieValueMode() -->
              <div class="form-group">
                <fc-select
                    params="
                        options: $parent.cookiesActions,
                        value: $data.cookieValueModeAction,
                        disabled: !$parent.enableTarget()
                    "
                ></fc-select>
            </div>
              <!-- /ko -->
            </div>
          </div>
          <!-- ko if: $data.cookieValueMode() -->
          <div class="widget-option-row mt-40p">
            <div class="form-group widget-option-text-value">
              <input type="text" class="form-control" placeholder="Введите значение" data-bind="textInput: $data.cookieValueModeText, disable: !$parent.enableTarget(), css: {'is-invalid': $data.modeTextError}">
              <!-- ko if: $data.modeTextError -->
              <div class="form-error">Обязательное поле</div>
              <!-- /ko -->
            </div>
          </div>
          <!-- /ko -->
          <div data-bind="css: {'mt-35p': !$data.cookieValueMode() }"><hr></div>
          <!-- /ko -->
          <fc-button
            class="mb-4 add-option-button"
            data-bind="css: {'disabled-button': (variables().length && variables().some(i => !i.cookieText().length || (i.cookieValueMode() && !i.cookieValueModeText().length))) || !$parent.enableTarget()}"
            params="
              icon: 'plus',
              color: 'success',
              mode: 'text',
              label: 'Добавить условие',
              click: function() { 
                if (variables().length && variables().some(i => !i.cookieText().length || (i.cookieValueMode() && !i.cookieValueModeText().length)) || !$parent.enableTarget()) return
                addVariableValue() 
              }
            "
          ></fc-button>
          
        
        </div>
      </div>
    </div>
    <!-- /ko -->

    <hr>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
          <fc-switch class="widget-target-option-label" data-bind="css: {'enabled': disabled, 'enable-option': !enableDepth()}, click: function () {
            if (!enableTarget()) {
              enableTarget(true)
              enableDepth(true)
            }
            
          }" params="checked: enableDepth, disabled: disabled, label: 'Глубина прокрутки'"></fc-switch>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: 'Триггер глубины прокрутки активирует показ виджета в зависимости от того, как далеко пользователь прокрутил страницу'"></button>
          <toggler-icon class="ml-15p widget-target-option-label" data-bind="click: function () {showDepth(!showDepth())},css: {'enabled': disabled, 'enable-option': !enableDepth()}" params="isOpen: showDepth, color: '#73808D', width: 14, height: 8"></toggler-icon>
        </div>
        <!-- ko template: {
                    foreach: templateIf(enableDepth(), $data),
                    afterAdd: slideAfterAddFactory(400),
                    beforeRemove: slideBeforeRemoveFactory(400)
                } -->
                <div class="col-12 mt-15p condition-info">Условия между собой работают через «И»</div>
        <!-- /ko -->
      </div>
    <!-- ko template: {
                  foreach: templateIf(showDepth(), $data),
                  afterAdd: slideAfterAddFactory(400),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="row mt-25p">
                <div class="col-12">
                  <div class="row widget-option-wrapper" data-bind="click: function () {
                    if (!enableTarget()) {
                      enableTarget(true)
                    }
                    enableDepth(true)
                    return true
                  }">
                    <div class="col-6">
                      <div class="d-flex align-items-center widget-option-wrapper-depth">
                        <foquz-checkbox params="name: 'Глубина вертикальной прокрутки',
                          checked: enableVerticalScroll,
                          disabled: disabled
                        ">Глубина вертикальной прокрутки</foquz-checkbox>
                        <button class="btn-question ml-2" tabindex="10" data-bind="tooltip, tooltipText: 'Показать виджет при достижении определенной глубины прокрутки по вертикали'"></button>
                      </div>
                      <!-- ko if: enableVerticalScroll -->
                      <div class="row">
                        <div class="col-12">
                          <div class="form-group ">
                            <variant-text-field
                              data-bind="css: {'is-invalid': (verticalScrollError() || formControlErrorStateMatcher(verticalScrollValue.pureValue))},
                              event: { 
                                keydown: restrictScrollInput, 
                                input: function(data, event) { 
                                  $parent.formatScrollInput(event.target.value, true); 
                                },
                                blur: function(data, event) { 
                                  $parent.formatScrollInput(event.target.value, true); 
                                } 
                              }"
                              params="
                                options: windowUnitVariants,
                                variantValue: verticalScrollUnit,
                                value: verticalScrollValue.value,
                                placeholder: 'Например: 10, 50, 100',
                              "
                            ></variant-text-field>
                            <!-- ko if: verticalScrollError() -->
                            <div class="form-error" data-bind="text: verticalScrollError"></div>
                            <!-- /ko -->
                            <!-- ko if: !verticalScrollError() && formControlErrorStateMatcher(verticalScrollValue.pureValue) -->
                            <div class="form-error">Обязательное поле</div>
                            <!-- /ko -->
                          </div>
                        </div>
                      </div>
                      <!-- /ko -->
                    </div>
                    
                    <div class="col-6">
                      <div class="d-flex align-items-center widget-option-wrapper-depth">
                        <foquz-checkbox params="name: 'Глубина горизонтальной прокрутки',
                          checked: enableHorizontalScroll,
                          disabled: disabled
                        ">Глубина горизонтальной прокрутки</foquz-checkbox>
                        <button class="btn-question ml-2" tabindex="10" data-bind="tooltip, tooltipText: 'Показать виджет при достижении определенной глубины прокрутки по горизонтали'"></button>
                      </div>
                      <!-- ko if: enableHorizontalScroll -->
                      <div class="row">
                        <div class="col-12">
                          <div class="form-group ">
                            <variant-text-field
                              data-bind="css: {'is-invalid': (horizontalScrollError() || formControlErrorStateMatcher(horizontalScrollValue.pureValue))},
                              event: { 
                                keydown: restrictScrollInput, 
                                input: function(data, event) { 
                                  $parent.formatScrollInput(event.target.value, false); 
                                },
                                blur: function(data, event) { 
                                  $parent.formatScrollInput(event.target.value, false); 
                                } 
                              }"
                              params="
                                options: windowUnitVariants,
                                variantValue: horizontalScrollUnit,
                                value: horizontalScrollValue.value,
                                placeholder: 'Например: 10, 50, 100',
                              "
                            ></variant-text-field>
                            <!-- ko if: horizontalScrollError() -->
                            <div class="form-error" data-bind="text: horizontalScrollError"></div>
                            <!-- /ko -->
                            <!-- ko if: !horizontalScrollError() && formControlErrorStateMatcher(horizontalScrollValue.pureValue) -->
                            <div class="form-error">Обязательное поле</div>
                            <!-- /ko -->
                          </div>
                        </div>
                      </div>
                      <!-- /ko -->
                    </div>
                    
                  </div>
                  <!-- ko if: depthError -->
                  <div class="widget-option-wrapper form-error">Установите хотя бы одно условие</div>
                  <!-- /ko -->
                </div>
              </div>
    <!-- /ko -->
    
    <!-- /ko -->
    
    </div>

    
  
  
  <div class="foquz-dialog__footer fixed-footer fixed poll-widget-sidesheet-footer">
    <div class="foquz-dialog__actions">
      <fc-button
        class="ml-2"
        params="label: 'Отменить', icon: 'bin', color: 'secondary', click: function() { reset() }"
      ></fc-button>

      <fc-button
        class="ml-2"
        params="label: 'Сохранить', icon: 'save', color: 'success', disabled: false, click: function() { submit() }"
      ></fc-button>
    </div>
    <fc-success params="show: showSuccessMessage" class="fc-success"><!-- ko text: text -->Сохранено успешно<!-- /ko -->
    </fc-success>
  </div>
  
</sidesheet>
