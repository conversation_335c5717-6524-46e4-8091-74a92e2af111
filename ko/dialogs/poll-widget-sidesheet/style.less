.poll-widget-sidesheet {
    overflow-y: hidden !important;
    .sender-page__html-code {
        min-height: 100px;
        white-space: pre-wrap;
        word-break: break-word;
        color: #73808d;
    }
    .sender-page__html-wrapper {
        height: auto;
        flex-grow: 1;
        position: relative;
        padding-right: 30px;

        .fc-btn {
            position: absolute;
            top: 15px;
            right: 15px;
        }
    }
    .condition-info {
        color: #73808D;
        font-size: 12px;
        font-weight: 400;
        line-height: 15.6px;
    }
    .widget-sticky-preview__wrapper {
        margin: 15px auto;
    }
    &__button-wrapper {
        display: table;
        .poll-widget-sidesheet__button-wrapper-inner {
            &.position-left {
                height: 0;
                padding: 50% 0;
                .fc-widget-preview-logo--sm {
                    transform: rotate(90deg);
                    margin-left: 0;
                    margin-right: 12px;
                }
            }
            &.position-right {
                height: 0;
                padding: 50% 0;
                .fc-widget-preview-logo--sm {
                    transform: rotate(-90deg);
                    margin-right: 0;
                    margin-left: 12px;
                }
            }
            
        }
        .fc-widget-preview-logo--lg-icon {
            margin-left: 0 !important;
        }
    }
    .extra-users-second-col {
        min-width: 311px;
    }
    .row {
        &.enabled {
            opacity: .5;
            .fc-switch__label {
                color: #73808D;
            }
        }
    }
    .slider-marker {
        color: #A6B1BC;
        text-align: center;
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 19px; /* 135.714% */
        &-wrapper {
            width: calc(100% - 275px);
            margin-top: -13px;
        }
    }
    .extra-users-first-col {
        max-width: 212px;
    }
    .fc-select-result {
        font-weight: normal;
    }
    .add-option-button {
        &.disabled-button {
            opacity: .5;
            cursor: default;
        }
    }
    .poll-widget-sidesheet__remove-button {
        position: absolute;
        right: 0;
    }
    .widget-slider-group {
        width: 100%;
        &.disabled {
            opacity: .5;
        }
        .input-group-append_ {
            min-width: 245px;
            justify-content: center;
            margin-left: 30px;
            text-align: center;
            &.dasabled {
                opacity: .5;
            }
            .input-group-text {
                font-weight: normal;
            }
        }
    }
    .ui-slider {
        margin-left: 10px;
    }
    .widget-option-text-value {
        width: 100%;
        margin-bottom: 0;
    }
    .widget-option-coll {
        max-width: 50%;
    }
    .widget-option-row {
        display: flex;
        gap: 30px;
        padding-right: 53px;
        position: relative;
        &.disabled {
            opacity: .5;
        }
    }
    .widget-option-name {
        color: #2E2F31;
        /* input label */
        font-family: Roboto;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 110%; /* 17.6px */
    }
    .widget-option-wrapper {
        padding-left: 65px;
        &.disabled {
            opacity: .5;
        }

        &-depth {
            margin-bottom: 10px;
        }
    }
    .widget-target-option-label {
        .fc-switch__label {
            color: #2E2F31;
            font-family: Roboto;
            font-size: 19px;
            font-style: normal;
            font-weight: 700;
            line-height: 13px; /* 68.421% */
        }
        &.enabled {
            opacity: .5;
            .fc-switch__label {
                color: #73808D;
            }
        }
        &.enable-option {
            .fc-switch__label {
                color: #73808D;
            }
        }
    }
    .widget-target-title {
        font-size: 19px;
        color: #2E2F31;
        font-weight: 700;
        &.enabled {
            opacity: .5;
        }
    }
    .foquz-dialog__actions .fc-btn-b {
        height: 38px;
    }
    .collection-select  {
        min-width: 141px;
    }
    .widget-code__info {
        color: #73808D;
    }
    .form-group {
        position: relative;
    }
    .chars-counter__value {
        bottom: 18px;
        right: 10px;
    }
    .fc-select-field__placeholder {
        font-weight: 400;
    }
    .ghost-label {
        padding-top: 10px;
    }
    .fc-widget-preview-logo {
        margin-left: 12px;
    }
    .button-font {
        padding-left: 12px;
        padding-right: 12px;
        color: #ccd3d9;
        margin-left: 18px;
        &:last-child {
            margin-left: 1px;
        }
        &.active {
            color: #3a5cdc;
        }
    }
    .fc-widget-preview-button {
        border-radius: 4px;
        display: flex;
        align-items: center;
        line-height: 1;
        min-height: 35px;
        padding: 4px 25px;
        white-space: nowrap;
    }
    .ghost-label_button-option {
        width: 100%;
        .hat-radio-group__radio-label {
            justify-content: flex-start;
        }
        .hat-radio-group__radio {
            flex: 1;
        }
    }
    &__info-text {
        color:#73808D;
        font-feature-settings: 'clig' off, 'liga' off;
        /* hint text */
        font-family: Roboto;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 130%; /* 15.6px */
    }
    &-wrapper {
        padding: 33px 30px;
        overflow-y: scroll;
        flex: 1;
        &::-webkit-scrollbar {
            width: 4px;
            height: 4px;
          }
          
          &::-webkit-scrollbar-track {
            background: #E8EDEE;
            width: 8px;      /* цвет зоны отслеживания */
          }
          
          &::-webkit-scrollbar-thumb {
            background-color: #8E99A3;    /* цвет бегунка */
            border-radius: 20px;       /* округлось бегунка */
          }
    }
    &-title {
        color: var(--text, #2E2F31);
        font-feature-settings: 'clig' off, 'liga' off;
        /* h1 */
        font-family: Roboto;
        font-size: 22px;
        font-style: normal;
        font-weight: 900;
        line-height: 110%; /* 24.2px */
        .counter {
            color: #73808D;
            font-weight: 400;
        }
    }
    &-subtitle {
        color: var(--icons-hints-inactive-text, #73808D);
        font-feature-settings: 'clig' off, 'liga' off;
        /* hint text */
        font-family: Roboto;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 130%; /* 15.6px */
        margin-bottom: 30px;
    }
    &-add-button-lable {
        margin-left: 10px;
    }
    &-filter-item {
        border-top: 1px solid #E7EBED;
        padding-top: 15px;
    }
    &-footer {
        margin-top: auto;
    }
    &__widget-name {
        color: #2E2F31;
        font-feature-settings: 'clig' off, 'liga' off;
        /* h1 */
        font-family: Roboto;
        font-size: 22px;
        font-style: normal;
        font-weight: 900;
        line-height: 110%; /* 24.2px */
        margin-right: 15px;
        max-width: 712px;
    }
    &__tabs {
        display: flex;
        height: 48px;
        align-items: center;
        flex: 1 0 0;
        border-radius: 4px;
        border: 1px solid #DADFE3;
        margin-bottom: 25px;
        &-item {
            flex: 1;
            display: flex;
            height: 100%;
            padding: 0px 15px;
            align-items: center;
            /* input label */
            font-family: Roboto;
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: 110%; /* 17.6px */
            color: #73808D;
            background:#F2F5F6;
            border-right: 1px solid #DADFE3;
            cursor: pointer;
            &:last-child {
                border-right: none;
            }
            &.active {
                color: #2E2F31;
                background-color: #fff;
            }
            span {
                font-weight: normal;
            }
        }
    }
    &__row {
        display: flex;
        gap: 30px;
        &_no-click {
            color: #2E2F31;
            font-size: 15px;
        }
    }
    &__coll {
        width: 50%;
    }
    &__coll-title {
        color:#2E2F31;
        /* input label */
        font-family: Roboto;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 110%; /* 17.6px */
    }
    .form-group_filials {
        padding-top: 10px;
    }
    .f-radio-group .hat-radio-group__radio-label {
        justify-content: flex-start;
    }
    .poll-widget-sidesheet__coll_first {
        height: 75px;
    }
    hr {
        margin-top: 25px;
        margin-bottom: 25px;
    }

    .form-error {
        margin-top: 5px;
    }
}
