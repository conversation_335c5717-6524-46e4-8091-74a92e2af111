import { DialogWrapper } from "Dialogs/wrapper";
import { handleFolders } from "Legacy/utils/handle-folders";
const { PHONE_REGEX } = require("Utils/regex/phone");
import { FoquzLoaderWithFile } from "Models/file-loader/loader-with-file";
import { FilialsDataCollection } from "Models/data-collection/filials";
import { User } from "Models/user";
import { DialogsModule } from "Utils/dialogs-module";
import { getFolders, getPoll } from "./api";
import { getCompanyFilials } from "@/api/company/get-filials";

function formatFilials(list) {
  let result = [];

  list.forEach((item) => {
    const { category, items } = item;

    if (!items.length) return;

    result.push({
      id: category.id,
      text: category.name,
      category: true,
      text: category.name,
      itemText: `<span class="category-name">${category.name}</span>`,
      disabled: true,
      items: items.map((filial) => {
        const resultText =
          category.id == 0
            ? filial.name
            : `<span class="f-color-service">${category.name}</span>/<span class="">${filial.name}</span>`;

        return {
          id: filial.id,
          type: "filial",
          text: filial.name,
          resultText,
        };
      }),
    });
  });

  return result;
}

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);

    this.roles = params.roles || [];
    this.companies = params.companies || [];

    this.superUser = params.superUser;
    this.mode = params.mode;

    this.users = params.users;
    this.currentUserId = params.currentUserId;

    this.data = params.user;

    this.id =
      this.mode === "edit" && this.data.id
        ? this.data.id
        : this.users().length + 1;
    this.createdAt =
      this.mode === "edit" && this.data.createdAt ? this.data.createdAt : "";

    if (!this.createdAt) {
      let currentDate = new Date();
      this.createdAt =
        (currentDate.getDay() > 10
          ? currentDate.getDay()
          : "0" + currentDate.getDay()) +
        "." +
        (currentDate.getMonth() + 1 > 10
          ? currentDate.getMonth() + 1
          : "0" + (currentDate.getMonth() + 1)) +
        "." +
        currentDate.getFullYear();
    }

    this.isSubmitted = ko.observable(false);
    this.pending = ko.observable(false);
    this.isBlocked = ko.observable(false);

    this.name = ko.observable(
      this.mode === "edit" && this.data.name() ? this.data.name() : ""
    );
    this.isActive = ko.observable(
      this.mode === "edit" ? this.data.activity() : ""
    );

    let role = "";
    if (this.mode === "edit") role = this.data.roleId();
    if (!role) role = this.roles[0].name;

    this.role = ko.observable(role);
    this.clientsSection = ko.observable(
      this.mode === "edit" ? this.data.clientsSection() : ""
    );
    this.email = ko.observable(this.mode === "edit" ? this.data.email() : "");
    this.phone = ko.observable(
      this.mode === "edit" ? this.data.phone() || "" : ""
    );

    let photo =
      this.mode === "edit" && this.data.photo ? this.data.photo().detail : "";

    this.userpicFile = ko.observable(undefined);
    this.userpic = new FoquzLoaderWithFile((newFile) => {
      return new Promise((res) => {
        if (!newFile) {
          this.userpicFile(null);
        } else {
          this.userpicFile(newFile);
        }
      });
    }, photo);

    this.isChecked = ko.observable(
      this.mode === "edit" ? this.data.email_confirmed() : ""
    );
    this.password = ko.observable("");
    this.passwordApiError = ko.observable("");
    this.retypePassword = ko.observable("");
    this.login = ko.observable(this.mode === "edit" ? this.data.login() : "");
    this.showPassword = ko.observable(false);
    this.showRetypePassword = ko.observable(false);
    this.checkPasswords = () => {
      if (this.password.length && this.retypePassword.length) {
        if (
          this.password.length >= 6 &&
          this.password === this.retypePassword
        ) {
          return true;
        } else {
          return false;
        }
      }
    };

    this.company = ko.observable(
      this.mode === "edit" ? this.data.company_id : ""
    );

    this.polls = ko.observableArray(
      this.mode === "edit" ? this.data.respondent_polls() : []
    );

    this.folders = ko.observableArray(
      this.mode === "edit" ? this.data.folders() : []
    );
    this.filials = ko.observableArray(
      this.mode === "edit" ? this.data.filials().map((id) => id.toString()) : []
    );

    this.googleReviewAnswer = ko.observable(
      this.mode === "edit" ? this.data.googleReviewAnswer() : false
    );
    this.answerProcessing = ko.observable(
      this.mode === "edit" ? this.data.answerProcessing() : false
    );

    this.hidePersonalData = ko.observable(
      this.mode === "edit" ? this.data.hidePersonalData() : false
    );

    this.canEditAnswers = ko.observable(
      this.mode === "edit" ? this.data.canEditAnswers() : false
    );

    this.filialsList = ko.observableArray([]);

    getCompanyFilials(this.superUser && this.company(), this.mode === "edit" && this.data.id
    ? this.data.id : null).then((list) => {
      this.filialsList(formatFilials(list));
    });

    this.collections = {
      folders: ko.observableArray([]),
    };

    this.folderResultTemplate = select2templates.folder.result;
    this.folderSelectionTemplate = select2templates.folder.selection;

    this.foldersLoaded = ko.observable(false);
    this.foldersDisabled = ko.observable(false);
    this.loadFolders();
    this.hidePolls = ko.observable(false);

    this.hasFilials = ko.pureComputed(() => {
      return this.filialsList().length > 0;
    });

    if (this.superUser) {
      this.company.subscribe((v) => {
        if (this.superUser && this.role() === "foquz_respondent") this.hidePolls(true)
        getCompanyFilials(v).then((list) => {
          this.filials([]);
          this.filialsList(formatFilials(list));
        });
        this.folders([]);
        this.loadFolders();
        if (this.superUser && this.role() === "foquz_respondent") this.hidePolls(false)
      });
    }

    this.role.subscribe((v) => {
      if (v !== "foquz_executor") {
        this.hidePersonalData(false);
        this.canEditAnswers(false)
      }
    });

    this.warning = ko.pureComputed(() => {
      if (this.superUser && this.role() === "foquz_respondent" && !this.company()) return "foquz_respondent-no-company";
      if (this.role() === "editor") {
        if (this.superUser && !this.company()) return "editor-no-company";
        return "";
      }
      if (this.role() != "filial_employee") return "";
      if (this.superUser && !this.company()) return "no-company";
      
      //if (this.collections.filials.loading()) return "loading";
      if (!this.filialsList().length) {
        if (this.superUser) return "no-company-filials";
        else return "no-filials";
      }
      return "has-filials";
    });

    this.type = 0;
    this.mediaType = 2;

    this.serverErrors = {
      username: ko.observable(""),
      email: ko.observable(""),
      phone: ko.observable(""),
    };

    this.currentUserInBase = ko.observable(false);

    this.formModel = (() => {
      const formModel = ko.validatedObservable(null, {
        deep: true,
        live: true,
      });

      this.role.extend({
        requiered: {
          message: "Обязательное поле",
        },
        validation: {
          validator: (v) => {
            if (v === "editor") {
              if (this.superUser && !this.company()) return false;
            }

            if (v === "filial_employee") {
              if (!this.hasFilials()) return false;
              if (this.superUser && !this.company()) return false;
            }

            return true;
          },
          message: "Укажите компанию",
        },
      });

      let login = ko
        .observable(this.mode === "edit" ? this.data.login() : "")
        .extend({
          required: {
            message: "Обязательное поле",
          },
          minLength: {
            params: 2,
            message: "Должно быть введено хотя бы 2 символа",
          },
          pattern: {
            params: "^\\S*$",
            message: "Неверный формат параметра «Логин»",
          },
          validation: {
            validator: () => false,
            onlyIf: () => !!this.serverErrors.username(),
            message: () => this.serverErrors.username(),
          },
        });

      login.subscribe((v) => {
        this.serverErrors.username("");
      });

      let password = ko
        .observable(this.mode === "edit" ? this.data.password : "")
        .extend({
          required: {
            message: "Обязательное поле",
            onlyIf: () => {
              return (
                (formModel() !== null && formModel().password()) ||
                this.mode === "create"
              );
            },
          },
          validation: {
            validator: (value) => {
              if (this.mode == "create") {
                return value?.length && value.length > 7 && !this.passwordApiError()
              } else {
                return !this.passwordApiError()
              }
            },
            message: () => {
              if (!formModel()?.password()?.length && this.mode === "create") {
                return "Обязательное поле"
              }
              if (formModel()?.password()?.length && formModel().password().length < 8) {
                return "Длина параметра «Пароль» должна быть не менее 8 символов"
              }
              if (this.passwordApiError()) {
                return this.passwordApiError()
              }
            },
          },
          minLength: {
            params: 8,
            message: "Длина параметра «Пароль» должна быть не менее 8 символов",
          },
        });
        password.subscribe((v) => {
          this.passwordApiError('');
        });
      let retypePassword = ko
        .observable(this.mode === "edit" ? this.data.retypePassword : "")
        .extend({
          /* required: {
            message: "Обязательное поле",
            onlyIf: () => {
              return (
                (formModel() !== null && formModel().password()) ||
                this.mode === "create"
              );
            },
          }, */
          validation: {
            validator: (value) => {
              return formModel() === null || value === formModel().password();
            },
            message:
              "Значение должно совпадать со значением параметра «Пароль»",
          },
        });

      let email = ko
        .observable(this.mode === "edit" ? this.data.email() || "" : "")
        .extend({
          email: {
            message: "Неверный формат параметра «Email»",
          },
          validation: {
            validator: () => false,
            onlyIf: () => !!this.serverErrors.email(),
            message: () => this.serverErrors.email(),
          },
        });

      email.subscribe((v) => {
        this.serverErrors.email("");
      });

      let phone = ko
        .observable(this.mode === "edit" ? this.data.phone() || "" : "")
        .extend({
          pattern: {
            params: PHONE_REGEX,
            message: "Неверный формат параметра «Телефон»",
          },
          validation: {
            validator: () => false,
            onlyIf: () => !!this.serverErrors.phone(),
            message: () => this.serverErrors.phone(),
          },
        });

      phone.subscribe((v) => {
        this.serverErrors.phone("");
      });

      formModel({
        login,
        password,
        retypePassword,
        email,
        phone,
        role: this.role,
        clientsSection: this.clientsSection,
      });

      return formModel;
    })();

    this.formModel.isValid.subscribe((v) => {
      if (v) this.isBlocked(false);
    });

    this.password.subscribe((v) => {
      this.passwordApiError('')
    });

    this.notify = ko.observable(false);
    this.notify.subscribe((v) => {
      if (!v) return;

      let isActive = this.isActive();
      let hasEmail =
        this.formModel().email() && this.formModel().email.isValid();

      if (isActive && hasEmail) return;

      let text =
        "Для отправки письма пользователю о добавлении его учётной записи необходимо ";

      if (!isActive && !hasEmail) {
        text +=
          'заполнить поле <b class="bold">«Email»</b> и включить опцию <b class="bold">«Активен»</b>.';
      } else if (!isActive) {
        text += 'включить опцию <b class="bold">«Активен»</b>.';
      } else {
        text += 'заполнить поле <b class="bold">«Email»</b>.';
      }

      this.info({
        text,
      });
    });

    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );
  }

  loadFolders() {
    this.foldersLoaded(false);
    this.collections.folders([]);
    if (this.superUser && !this.company()) {
      this.foldersDisabled(true);
      this.foldersLoaded(true);
      return;
    }
    this.foldersDisabled(false);
    return getFolders(this.superUser ? this.company() : null).then((list) => {
      this.collections.folders(handleFolders(list || []));
      this.foldersLoaded(true);
    });
  }

  cancel() {
    this.notify(false);
    if (this.mode === "create") {
      this.isSubmitted(false);
      this.formModel().login("");
      this.formModel().password("");
      this.formModel().retypePassword("");
      this.formModel().email("");
      this.formModel().phone("");
      this.formModel().clientsSection(false);
      this.hidePersonalData(false);
      this.canEditAnswers(false);
    } else {
      this.isSubmitted(false);
      this.formModel().login(this.data.login());
      this.formModel().password("");
      this.formModel().retypePassword("");
      this.formModel().email(this.data.email());
      this.formModel().phone(this.data.phone());
      this.formModel().clientsSection(this.data.clientsSection());
      this.hidePersonalData(this.data.hidePersonalData());
      this.canEditAnswers(this.data.canEditAnswers())
    }
  }

  setErrors(errors) {
    if (!errors) return;
    if (typeof errors !== "object") return;

    ["username", "email", "phone"].forEach((key) => {
      if (key in errors) {
        this.serverErrors[key](errors[key]);
      }
    });
  }

  submit() {
    if (this.pending()) return;
    this.isSubmitted(true);

    var t = this;

    if (!this.formModel.isValid()) {
      this.isBlocked(true);
      return;
    }
    this.pending(true);

    if (this.isActive() && !this.superUser) {
      const activeCount = this.users().filter(el => el.activity() === 1).length;
      const { userLimit } = window.PAGE_DATA.company;
    }

    let form_data = new FormData();
    form_data.append("username", this.formModel().login());
    form_data.append("status", this.isActive() ? 1 : 0);
    form_data.append("role", this.role());
    form_data.append("email", this.formModel().email());
    form_data.append("phone", this.formModel().phone());
    if (this.superUser) {
      form_data.append("email_confirmed", this.isChecked() ? 1 : 0);
    }
    form_data.append("name", this.name());
    form_data.append(
      "clients_section",
      this.formModel().clientsSection() ? 1 : 0
    );

    let filials = this.filials();
    filials.forEach((f) => form_data.append("filials[]", f));

    form_data.append("googleReviewAnswer", this.googleReviewAnswer() ? 1 : 0);
    form_data.append("answerProcessing", this.answerProcessing() ? 1 : 0);

    if (this.superUser) {
      form_data.append(
        "superadmin",
        this.roles.find(el => el.name === "foquz_superadmin") ? 1 : 0
      );
      form_data.append("company", this.company());
    } else {
      form_data.append("company", COMPANY_ID);
    }

    form_data.append("hide_personal_data", this.hidePersonalData() ? 1 : 0);
    form_data.append("can_edit_answers", this.canEditAnswers() ? 1 : 0);

    form_data.append("avatar", this.userpicFile());

    let folders = this.folders();
    folders.forEach((f) => form_data.append("folders[]", f));

    let polls = this.polls()
    if (polls.length) {
      polls.forEach((p) => form_data.append("polls[]", p));
    } else {
      form_data.append("polls", [])
    }
    

    const self = this

    if (this.mode === "create") {
      form_data.append("password", this.formModel().password());
      form_data.append("repeat_password", this.formModel().retypePassword());
      form_data.append("notify", this.notify() ? 1 : 0);

      

      $.ajax({
        url: this.superUser
          ? "/foquz/users/create"
          : "/foquz/api/user/create?access-token=" + APIConfig.apiKey,
        cache: false,
        contentType: false,
        processData: false,
        data: form_data,
        type: "post",
        success: function (data) {
          self.pending(false);
          if (data.errors) {
            t.setErrors(data.errors);
            return;
          }

          let newUser = new User(data);
          t.users.splice(0, 0, newUser);
          t.hide();
          t.emitEvent('submit', newUser);
        },
        error: function (response) {
          self.pending(false);
          if (response.responseJSON?.errors?.password) {
            self.passwordApiError(response.responseJSON.errors.password[0])
          }
          let errors = response.responseJSON?.errors;
          if (response.responseJSON?.limit) {
            self.confirm({
              title: "",
              text: response.responseJSON.error,
              cancel: "Закрыть",
              confirm: "Сменить тариф",
            }).then(() => {
              window.open('/foquz/payments?tab=tariff', '_blank').focus();
            });
            return;
          }
          
          t.setErrors(errors);
        },
      });
    } else if (this.mode === "edit") {
      let user = this.users().find((r) => r.id === this.id);

      if (this.formModel().password()) {
        form_data.append("password", this.formModel().password());
      }

      $.ajax({
        url: this.superUser
          ? "/foquz/users/update?id=" + this.id
          : "/foquz/api/user/update?access-token=" +
            APIConfig.apiKey +
            "&id=" +
            this.id,
        cache: false,
        contentType: false,
        processData: false,
        data: form_data,
        type: "post",
        success: function (data) {
          self.pending(false);
          if (data.errors !== undefined) {
            t.setErrors(data.errors);
            return;
          }

          user.update(data);

          if (t.currentUserId() === data.id) {
            if (data.photo === false) {
              $(".sidebar__account-picture").attr("src", "/avatars/dummy.png");
            } else {
              $(".sidebar__account-picture").attr("src", data.photo.preview);
            }
          }
          t.hide();
          t.emitEvent('submit', data);
        },
        error: function (response) {
          self.pending(false);
          if (response.responseJSON?.errors?.password) {
            self.passwordApiError(response.responseJSON.errors.password[0])
          }
          let errors = response.responseJSON?.errors;
          if (response.responseJSON?.limit) {
            self.confirm({
              title: "",
              text: response.responseJSON.error,
              cancel: "Закрыть",
              confirm: "Сменить тариф",
            }).then(() => {
              window.open('/foquz/payments?tab=tariff', '_blank').focus();
            });
            return;
          }
          t.setErrors(errors);
        },
      });
    }
  }
}
