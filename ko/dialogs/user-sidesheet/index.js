import { ViewModel } from './model';
import html from './template.html';
import './style.less';
import 'Components/input/select/polls-select';

ko.components.register('user-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('user-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});