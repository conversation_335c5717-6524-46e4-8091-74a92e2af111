import { ApiUrl } from "@/utils/url/api-url";

export function getFolders(company) {
  const params = {};
  if (company) {
    params.companyId = company
  }
  return fetch(ApiUrl("poll/folders", params))
    .then((res) => res.json())
}

export function getPoll(company) {
  const params = {};
  if (company) {
    params.companyId = company
  }
  return fetch(ApiUrl("poll", params))
    .then((res) => res.json())
}
