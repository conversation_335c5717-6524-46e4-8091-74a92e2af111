<!-- ko let: { $userDialog: $component } -->
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div
    class="d-flex flex-column flex-grow-1 overflow-hidden"
    data-bind="dnd: function(files) {
          userpic.loadFile(files[0]);
        }, dndDisabled: userpic.preview"
  >
    <div class="foquz-dialog__header">
      <div class="container">
        <h2
          class="foquz-dialog__title"
          data-bind="text: mode === 'edit'?'Редактировать пользователя':'Новый пользователь'"
        ></h2>
      </div>
    </div>

    <div class="foquz-dialog__body">
      <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
        <div class="container">
          <div class="pb-4 pt-4" data-bind=" using: formModel">
            <dnd-cover
              params="type: 'image'"
              style="margin-bottom: -50px"
            ></dnd-cover>

            <div class="d-flex justify-content-between align-items-start">
              <div class="users__details-modal-dialog-media-form-control">
                <media-load-button params="loader: $userDialog.userpic">
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 40 40"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <mask
                      id="mask0"
                      mask-type="alpha"
                      maskUnits="userSpaceOnUse"
                      x="0"
                      y="0"
                      width="40"
                      height="40"
                    >
                      <path
                        d="M0 8C0 3.58172 3.58172 0 8 0H32C36.4183 0 40 3.58172 40 8V32C40 36.4183 36.4183 40 32 40H8C3.58172 40 0 36.4183 0 32V8Z"
                        fill="white"
                      />
                    </mask>
                    <g mask="url(#mask0)">
                      <path d="M-11 -5H51V60.7959H-11V-5Z" />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M20 25C23.866 25 27 21.866 27 18C27 14.134 23.866 11 20 11C16.134 11 13 14.134 13 18C13 21.866 16.134 25 20 25ZM20 23C22.7614 23 25 20.7614 25 18C25 15.2386 22.7614 13 20 13C17.2386 13 15 15.2386 15 18C15 20.7614 17.2386 23 20 23Z"
                        fill="#73808D"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M20 62C29.3888 62 37 54.3888 37 45C37 35.6112 29.3888 28 20 28C10.6112 28 3 35.6112 3 45C3 54.3888 10.6112 62 20 62ZM20 60C28.2843 60 35 53.2843 35 45C35 36.7157 28.2843 30 20 30C11.7157 30 5 36.7157 5 45C5 53.2843 11.7157 60 20 60Z"
                        fill="#73808D"
                      />
                    </g>
                  </svg>
                  <div class="mt-2">добавить фото</div>
                </media-load-button>

                <file-loader-error
                  params="error: $userDialog.userpic.error"
                ></file-loader-error>
              </div>

              <form
                autocomplete="nope1"
                onsubmit="return false"
                class="users__details-modal-dialog__form"
              >
                <div class="row">
                  <div class="col-8">
                    <div class="form-group">
                      <label
                        class="d-flex align-items-center justify-content-between form-label"
                        for="name"
                      >
                        <span>
                          ФИО
                          <button
                            class="btn-question"
                            data-bind="tooltip, tooltipPlacement: 'top'"
                            type="button"
                            title="Имя пользователя"
                          ></button>
                        </span>

                        <span class="form-label_not-required"
                          >необязательное</span
                        >
                      </label>

                      <div
                        class="chars-counter chars-counter--type_input"
                        data-bind="charsCounter, charsCounterCount: $userDialog.name().length"
                      >
                        <input
                          class="form-control"
                          data-bind="textInput: $userDialog.name"
                          id="name"
                          maxlength="140"
                        />

                        <div class="chars-counter__value"></div>
                      </div>
                    </div>
                  </div>
                  <div class="col-4 align-self-center">
                    <div class="form-group ghost-label">
                      <div
                        class="form-check users__details-modal-dialog-dish-rating-settings-selector-checkbox"
                      >
                        <input
                          class="form-check-input"
                          data-bind="checked: $userDialog.isActive"
                          id="is-active"
                          type="checkbox"
                        />
                        <label class="form-check-label" for="is-active"
                          >Активен</label
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <!-- ko if: $userDialog.superUser -->
                <div class="row">
                  <div class="col-8">
                    <div class="form-group">
                      <label
                        class="d-flex align-items-center justify-content-between form-label"
                        for="company"
                      >
                        <span>
                          Компания
                          <button
                            class="btn-question"
                            data-bind="tooltip, tooltipPlacement: 'top'"
                            type="button"
                            title="Имя пользователя"
                          ></button>
                        </span>

                        <span class="form-label_not-required"
                          >необязательное</span
                        >
                      </label>

                      <select2
                        params="value: $userDialog.company, placeholder: 'Выберите компанию', search: 0, allowUnset: true"
                      >
                        <!-- ko foreach: $userDialog.companies -->
                        <option data-bind="value: id, html: name"></option>
                        <!-- /ko -->
                      </select2>
                    </div>
                  </div>
                </div>
                <!-- /ko -->

                <div class="row" data-bind="css: {'align-items-center': $userDialog.role() === 'foquz_executor'}">
                  <div class="col-4">
                    <div class="form-group">
                      <label
                        class="d-flex align-items-center justify-content-between form-label"
                        for="role"
                      >
                        <span>
                          Роль
                          <question-button
                            params="text: 'Роль'"
                          ></question-button>
                        </span>
                      </label>

                      <select2
                        class="select2-wrapper"
                        params="value: $userDialog.role, allowUnset: true"
                        data-bind="css: {
                        'is-invalid': $userDialog.formControlErrorStateMatcher($userDialog.role)
                      }"
                      >
                        <!-- ko foreach: $userDialog.roles -->
                        <option
                          data-bind="value: name, text: description"
                        ></option>
                        <!-- /ko -->
                      </select2>

                      <!-- ko if: $userDialog.formControlErrorStateMatcher($userDialog.role) -->
                      <div
                        class="form-error"
                        data-bind="text: $userDialog.role.error()"
                      ></div>
                      <!-- /ko -->
                    </div>
                  </div>

                  <div class="col-8" data-bind="css: {'d-flex align-items-center': $userDialog.role() === 'foquz_executor'}">
                    <!-- ko if: $userDialog.role() === 'foquz_executor' -->
                    <div class="form-group form-group_executor" style="padding-top: 40px; flex: 1">
                      <fc-check
                        params="checked: $userDialog.hidePersonalData, label: 'Скрыть персональные данные контакта', hint: 'Скрыть персональные данные контакта'"
                      ></fc-check>
                    </div>
                    <div class="form-group form-group_executor" style="padding-top: 40px; flex: 1; margin-left: 30px">
                      <fc-check
                        params="checked: $userDialog.canEditAnswers, label: 'Разрешить редактировать анкету опроса в разделе «Ответы»', hint: 'Разрешить редактировать анкету опроса в разделе «Ответы»'"
                      ></fc-check>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: $userDialog.role() === 'editor' || $userDialog.role() === 'foquz_watcher' -->
                    <div class="form-group" style="padding-top: 40px">
                      <fc-check
                        params="checked: $userDialog.clientsSection, label: 'Доступ к разделу «Контакты»'"
                      ></fc-check>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: $userDialog.warning() == 'no-company' -->

                    <div class="filial-employee-note">
                      <span>
                        Для создания пользователя с ролью
                        <b class="bold">Сотрудник филиала</b><br />необходимо
                        указать компанию, к которой он относится
                      </span>
                    </div>

                    <!-- /ko -->

                    <!-- ko if: $userDialog.warning() == 'loading' -->
                    <spinner class="filial-employee-spinner"></spinner>
                    <!-- /ko -->

                    <!-- ko if: $userDialog.warning() == 'no-company-filials' -->
                    <div class="filial-employee-note">
                      <span>
                        Для создания пользователя с ролью
                        <span class="bold">Сотрудник филиала</span
                        ><br />необходимо
                        <b class="bold">добавить хотя бы один филиал</b> в
                        выбранную компанию
                      </span>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: $userDialog.warning() == 'no-filials' -->
                    <div class="filial-employee-note">
                      <span>
                        Для создания пользователя с ролью
                        <span class="bold">Сотрудник филиала</span
                        ><br />необходимо
                        <a
                          href="/foquz/settings?tab=settings&channel=email&setting=collections"
                          >добавить хотя бы один филиал</a
                        >
                      </span>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: $userDialog.warning() == 'has-filials' -->
                    <div class="form-group">
                      <label class="form-label">
                        Филиал
                        <question-button
                          params="text: 'Филиал'"
                        ></question-button>
                      </label>
                      <fc-select
                        class="categorized"
                        params="options: $userDialog.filialsList,
                        value: $userDialog.filials, multiple: true, placeholder: 'Все филиалы',
                        searchable: true"
                      ></fc-select>
                    </div>
                    <!-- /ko -->
                  </div>
                </div>

                <!-- ko if: $userDialog.warning() == 'editor-no-company' -->
                <div class="row">
                  <div class="col-12">
                    <div class="service-text mt-n15p mb-25p">
                      <span>
                        Для создания пользователя с ролью
                        <b class="bold">Редактор</b> необходимо указать
                        компанию, к которой он относится
                      </span>
                    </div>
                  </div>
                </div>
                <!-- /ko -->
                <!-- ko if: $userDialog.warning() == 'foquz_respondent-no-company' -->
                <div class="row">
                  <div class="col-12">
                    <div class="service-text mt-n15p mb-25p">
                      <span>
                        Для создания пользователя с ролью
                        <b class="bold">Респондент</b> необходимо указать
                        компанию, к которой он относится
                      </span>
                    </div>
                  </div>
                </div>
                <!-- /ko -->

                <!-- ko template: {
                   foreach: templateIf($userDialog.role() === 'foquz_respondent', $data),
                   afterAdd: slideAfterAddFactory(400),
                   beforeRemove: slideBeforeRemoveFactory(400)
                } -->
                <!-- ko if: !$userDialog.hidePolls() -->
                <div class="mb-20p">
                  <label class="form-label">Опрос</label>
              
                  <div class="select2-wrapper">
                    <polls-select params="companyId: $userDialog.superUser ? $userDialog.company() : undefined ,value: $userDialog.polls, multiple: true, placeholder: 'Все опросы',disabled: $userDialog.warning() == 'foquz_respondent-no-company'"></polls-select>
                  </div>
                </div>

                <!-- /ko -->
                <!-- /ko -->

                <!-- ko template: {
                   foreach: templateIf($userDialog.role() === 'editor' || $userDialog.role() === 'foquz_watcher', $data),
                   afterAdd: slideAfterAddFactory(400),
                   beforeRemove: slideBeforeRemoveFactory(400)
                } -->

                <div class="row">
                  <div class="col-12">
                    <div class="form-group">
                      <fc-label
                        params="text: 'Папки, доступные пользователю', hint: 'Папки, доступные пользователю'"
                      ></fc-label>
                      <!-- ko if: $userDialog.foldersLoaded() -->
                      <div class="select2-wrapper">
                        <select
                          multiple
                          data-bind="
                                      selectedOptions:  $userDialog.folders,
                                      disable: $userDialog.foldersDisabled,
                                      lazySelect2: {
                                        containerCssClass: 'form-control',
                                        wrapperCssClass: 'select2-container--form-control',
                                          templateResult:  $userDialog.folderResultTemplate,
                                          templateSelection:  $userDialog.folderSelectionTemplate,
                                          placeholder: _t('Папки не выбраны')
                                      }"
                        >
                          <!-- ko foreach: { data:  $userDialog.collections.folders, as: 'folder' } -->
                          <!-- ko template: {
                                              name: 'folder-template',
                                              data: {
                                              folder: folder,
                                              level: 0
                                              }
                                          } -->
                          <!-- /ko -->
                          <!-- /ko -->
                        </select>
                      </div>
                      <!-- /ko -->

                      <!-- ko ifnot:  $userDialog.foldersLoaded() -->
                      <fc-spinner class="f-color-primary"></fc-spinner>
                      <!-- /ko -->
                    </div>
                  </div>
                </div>

                <!-- /ko -->

                <!-- ko if: $userDialog.role() == 'filial_employee' && $userDialog.hasFilials() -->
                <div class="row">
                  <div class="col-4">
                    <div class="form-group">
                      <foquz-checkbox
                        params="checked: $userDialog.googleReviewAnswer"
                        >Отвечать на отзывы с Google&#8209;карт
                      </foquz-checkbox>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="form-group">
                      <foquz-checkbox
                        params="checked: $userDialog. answerProcessing"
                        >Обработка анкет
                      </foquz-checkbox>
                    </div>
                  </div>
                </div>
                <!-- /ko -->

                <div class="row">
                  <div class="col-4">
                    <div class="form-group">
                      <label
                        class="d-flex align-items-center justify-content-between form-label"
                        for="login"
                      >
                        <span>
                          Логин
                          <button
                            class="btn-question"
                            data-bind="fbPopper, title: 'Логин может содержать только латинские буквы, цифры, тире и знак подчеркивания. Логин может быть почтой'"
                            type="button"
                          ></button>
                        </span>
                      </label>
                      <div
                        class="chars-counter chars-counter--type_input"
                        data-bind="charsCounter, charsCounterCount: login().length"
                      >
                        <input
                          required
                          class="form-control"
                          data-bind="textInput: login,css: {
                           'is-invalid': $userDialog.formControlErrorStateMatcher(login),
                           'is-valid': $userDialog.formControlSuccessStateMatcher(login)
                       }"
                          id="login"
                          maxlength="140"
                          autocomplete="nope2"
                        />

                        <div class="chars-counter__value"></div>
                      </div>

                      <!-- ko if: $userDialog.formControlErrorStateMatcher(login) -->
                      <div
                        class="form-error"
                        data-bind="text: login.error()"
                      ></div>
                      <!-- /ko -->
                    </div>
                  </div>

                  <div class="col-4">
                    <div class="form-group">
                      <label
                        class="d-flex align-items-center justify-content-between form-label"
                        for="password"
                      >
                        <span>
                          Пароль
                          <button
                            class="btn-question"
                            data-bind="fbPopper, title: 'Пароль должен содержать:<br/>* не менее 8 символов,<br/>* заглавные и строчные буквы (A-Z, a-z),<br/>* не менее одной цифры (0-9),<br/>* не менее одного спецсимвола (!, #, +, % и т.п.).<br/>* Пробелы не допускаются.'"
                            type="button"
                            title="Пароль"
                          ></button>
                        </span>
                      </label>

                      <password-field
                        params="value: password"
                        data-bind="css: {
                        'is-invalid': $userDialog.formControlErrorStateMatcher(password),
                        'is-valid': $userDialog.formControlSuccessStateMatcher(password)
                      }"
                        autocomplete="new-password"
                        id="password"
                      >
                      </password-field>

                      <validation-feedback
                        params="show: $userDialog.formControlErrorStateMatcher(password), html: password.error"
                      ></validation-feedback>
                    </div>
                  </div>

                  <div class="col-4">
                    <div class="form-group">
                      <label
                        class="d-flex align-items-center justify-content-between form-label"
                        for="passwordRetype"
                      >
                        <span>
                          Повторите пароль
                          <button
                            class="btn-question"
                            data-bind="tooltip, tooltipPlacement: 'top'"
                            type="button"
                            title="Пароль"
                          ></button>
                        </span>
                      </label>

                      <password-field
                        params="value: retypePassword"
                        data-bind="css: {
                        'is-invalid': $userDialog.formControlErrorStateMatcher(retypePassword),
                        'is-valid': $userDialog.formControlSuccessStateMatcher(retypePassword)
                      }"
                        autocomplete="new-password"
                        id="passwordRetype"
                      >
                      </password-field>

                      <validation-feedback
                        params="show: $userDialog.formControlErrorStateMatcher(retypePassword), text: retypePassword.error"
                      ></validation-feedback>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-4">
                    <div class="form-group">
                      <label
                        class="d-flex align-items-center justify-content-between form-label"
                        for="phone"
                      >
                        <span>
                          Телефон
                          <button
                            class="btn-question"
                            data-bind="tooltip, tooltipPlacement: 'top'"
                            type="button"
                            title="Телефон"
                          ></button>
                        </span>

                        <span class="form-label_not-required"
                          >необязательное</span
                        >
                      </label>
                      <input
                        type="text"
                        class="form-control"
                        data-bind="phoneMask, textInput: phone, css: {
                           'is-valid': $userDialog.formControlSuccessStateMatcher(phone),
                           'is-invalid': $userDialog.formControlErrorStateMatcher(phone),
                         }"
                      />

                      <!-- ko if: $userDialog.formControlErrorStateMatcher(phone) -->
                      <div
                        class="form-error"
                        data-bind="text: phone.error()"
                      ></div>
                      <!-- /ko -->
                    </div>
                  </div>

                  <div class="col-4">
                    <div class="form-group">
                      <label
                        class="d-flex align-items-center justify-content-between form-label"
                        for="email"
                      >
                        <span>
                          Email
                          <button
                            class="btn-question"
                            data-bind="tooltip, tooltipPlacement: 'top'"
                            type="button"
                            title="Email"
                          ></button>
                        </span>

                        <span class="form-label_not-required"
                          >необязательное</span
                        >
                      </label>
                      <div
                        class="chars-counter chars-counter--type_input"
                        data-bind="charsCounter, charsCounterCount: email().length"
                      >
                        <input
                          class="form-control"
                          data-bind="
                           textInput: email,
                           css: {
                               'is-invalid': $userDialog.formControlErrorStateMatcher(email),
                               'is-valid': $userDialog.formControlSuccessStateMatcher(email)
                           }
                       "
                          id="email"
                          maxlength="50"
                        />

                        <div class="chars-counter__value"></div>
                      </div>

                      <!-- ko if: $userDialog.formControlErrorStateMatcher(email) -->
                      <div
                        class="form-error"
                        data-bind="text: email.error()"
                      ></div>
                      <!-- /ko -->
                    </div>
                  </div>
                  <!-- ko if: $userDialog.superUser -->
                  <div class="col-4 align-self-center">
                    <div class="form-group ghost-label">
                      <div
                        class="form-check users__details-modal-dialog-dish-rating-settings-selector-checkbox"
                      >
                        <input
                          class="form-check-input"
                          data-bind="checked: $userDialog.isChecked"
                          id="is-checked"
                          type="checkbox"
                        />
                        <label class="form-check-label" for="is-checked"
                          >Email подтвержден</label
                        >
                      </div>
                    </div>
                  </div>
                  <!-- /ko -->
                </div>

                <!-- ko if: $userDialog.mode == 'create' -->
                <div class="row">
                  <div class="col-6">
                    <input-checkbox params="checked: $userDialog.notify">
                      Уведомить пользователя
                      <question-button
                        params="text: 'Уведомить пользователя'"
                      ></question-button>
                    </input-checkbox>
                  </div>
                </div>
                <!-- /ko -->
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() { cancel() }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Отменить
      </button>

      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="click: function() { submit() }, disable: isBlocked() || pending()"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        Сохранить
      </button>
    </div>
  </div>
</sidesheet>
<!-- /ko -->
