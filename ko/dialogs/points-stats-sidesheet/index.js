import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/chart/column-chart';
import 'Components/chart/pie-chart';
import 'Components/stats-filters-value';

ko.components.register('points-stats-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('points-stats-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
