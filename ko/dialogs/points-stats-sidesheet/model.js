import { DialogWrapper } from 'Dialogs/wrapper';
import { toPercent } from 'Utils/number/to-percent';
import { Printer } from 'Utils/print';
import { debounce } from 'Utils/timer/debounce';
import { PointsModel } from 'Models/points';
import { mediaWatcher } from 'Utils/media/watcher';
import { Range } from './range';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.poll = params.poll;
    this.pointsModel = new PointsModel(params.poll);

    this.rangesCount = ko.observable(5);
    this.noRanges = ko.observable(false);

    this.load = debounce(() => this.loadRanges(), 400);

    [this.rangesCount, this.noRanges].forEach((f) => {
      this.subscriptions.push(f.subscribe(() => this.load()));
    });

    this.loading = ko.observable(false);
    this.ranges = ko.observableArray([]);

    this.columnChartData = ko.pureComputed(() => {
      return this.getColumnChartData(this.ranges());
    });

    this.pieChartData = ko.pureComputed(() => {
      return this.getPieChartData(this.ranges());
    });

    this.filters = params.filters;

    this.loadRanges();

    this.printer = Printer();

    this.modalScrollDisabled = ko.observable(true);
    this.tableScrollDisabled = ko.observable(true);

    let watcher = mediaWatcher((state) => {
      console.log('change', state);

      if (state == 'desktop') {
        this.modalScrollDisabled(true);
        this.tableScrollDisabled(false);
      } else {
        this.modalScrollDisabled(false);
        this.tableScrollDisabled(true);
      }
    });
  }

  getColumnChartData(ranges) {
    let max = ranges.reduce((acc, r) => acc + r.count, 0);
    return {
      series: [
        {
          data: ranges.map((range) => {
            return {
              y: range.count,
              percents: range.percents,
              name: range.range
            };
          })
        }
      ],
      yAxis: [
        {
          title: { enabled: false },
          max
        },
        {
          opposite: true,
          linkedTo: 0,
          title: { enabled: false },
          max: 100,
          endToTick: false,
          labels: {
            formatter: function () {
              let value = toPercent(this.value, max, 0);

              return value > 100 ? '' : value + '%';
            }
          }
        }
      ],
      xAxis: {
        categories: ranges.map((range) => range.range)
      },
      tooltip: {
        formatter: function () {
          return `<span class="font-weight-normal">${this.point.name}</span><br><span class="bold">${this.y}/${this.point.percents}%</span>`;
        }
      }
    };
  }

  getPieChartData(ranges) {
    return {
      series: [
        {
          data: ranges
            .filter((range) => range.count > 0)
            .map((range) => {
              return {
                y: range.count,
                name: range.range,
                percents: range.percents
              };
            }),
          dataLabels: {
            connectorWidth: 0
          },
          tooltip: {
            pointFormat:
              '<span style="color:{point.color}">●</span> Кол-во ответов: <b>{point.y}</b><br/>'
          }
        }
      ],
      plotOptions: {
        innerSize: '60%',
        center: ['40%', 100],
        dataLabels: {
          overflow: 'allow',
          clip: false,
          crop: false,
          distance: 20,
          enabled: true,
          useHTML: true,
          format:
            '<span class="font-weight-normal">{point.name}</span><br><span class="bold">{point.y}/{point.percentage:.1f}%</span>'
        },
        size: 250
      },
      responsive: {
        rules: [
          {
            condition: {
              maxWidth: 768
            },
            chartOptions: {
              plotOptions: {
                pie: {
                  size: 175,
                  center: ['50%', 100],
                  dataLabels: {
                    distance: 10,
                    padding: 0,
                    alignTo: 'toPlotEdges'
                  }
                }
              }
            }
          }
        ]
      }
      // tooltip: {
      //   formatter: function (point) {
      //     console.log(point, this);
      //   }
      // }
    };
  }

  loadRanges() {
    this.loading(true);

    $.ajax({
      method: 'GET',
      url: `${APIConfig.baseApiUrlPath}poll/points-distribution?id=${this.poll.id}&access-token=${APIConfig.apiKey}`,
      data: {
        ranges: this.rangesCount(),
        noRanges: this.noRanges() ? 1 : 0
      },
      success: (response) => {
        this.ranges(response.items.map((i) => new Range(i)));
        this.loading(false);
      },
      error: (response) => {
        console.error(response.responseJSON);
        this.loading(false);
      }
    });
  }

  print() {
    let header = $('<div></div>').addClass('page-header');
    let title = $('.s-app__header').find('h1').clone(true);
    header.append(title);
    let bodyHtml = this.element.querySelector('.foquz-dialog__content').cloneNode(true); // клонируем весь контент
    let tableHtml = bodyHtml.querySelector('.native-scrollbar table').cloneNode(true); // клонируем таблицу
    let tableContainer = bodyHtml.querySelector('.native-scrollbar'); // находим родительский контейнер таблицы
    tableContainer.innerHTML = ''; // очищаем его содержимое
    tableContainer.appendChild(tableHtml); // добавляем в него полную версию таблицы

    this.printer.print([header.get(0), bodyHtml]);
  }


}
