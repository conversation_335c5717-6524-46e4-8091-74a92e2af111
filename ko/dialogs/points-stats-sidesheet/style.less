@import 'Style/breakpoints';
@import 'Style/colors';

.points-stats-sidesheet {
  .only-mobile({
    .print-button {
      display: none;
    }
    .pie-chart {
      [data-highcharts-chart] {
        overflow: visible!important;
      }
    }
  });

  .header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .mobile-and-tablet({
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;

      .foquz-dialog__title {
        margin-bottom: 25px;
      }

      .print-button {
        margin-bottom: 22px;
      }
    });

    .only-mobile({
      .foquz-dialog__title {
        margin-bottom: 4px;
      }
    });
  }

  .points-stats-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid @f-color-border;
    border-bottom: 1px solid @f-color-border;

    &__wrapper {
      display: flex;
      padding-left: 10px;
      padding-right: 10px;
    }

    .mobile-and-tablet({
      margin-left: calc(-1 * var(--foquz-side-offset));
      margin-right: calc(-1 * var(--foquz-side-offset));

      &__wrapper {
        padding: 0;
      }
    });

    .only-mobile({
      margin-bottom: 24px;
      border-bottom: 0;

      &__wrapper {
        flex-direction: column;
        width: 100%;
      }

    });
  }

  &__content {
    padding-left: 30px;
    padding-right: 30px;
    margin-top: 35px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;

    .mobile-and-tablet({
      padding-left: 0px;
      padding-right: 0px;
    });

    .only-mobile({
      margin-top: 24px;
      .foquz-slider {
        width: 100%;
      }
    });
  }

  &__data {
    flex-grow: 1;

    .only-desktop({
      .row {
        flex-wrap: nowrap;
      }
    });

    .mobile-and-tablet({
      .row {
        flex-direction: column;
      }
    });


  }

  &__table {
    .only-mobile({
      padding-left: 0;
      padding-right: 0;

      .f-table {
        td {
          padding-top: 10px;
          padding-bottom: 10px;
        }
        th, td {
          &:first-child {
            padding-left: var(--foquz-side-offset);
          }
          &:last-child {
            padding-right: var(--foquz-side-offset);
          }
        }
      }
    });
  }


}
