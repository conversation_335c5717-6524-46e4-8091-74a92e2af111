<sidesheet params="ref: modal, dialogWrapper: $component">

    <div class="foquz-dialog__header">
        <div class="container">
            <div class="header-wrapper">
                <h2 class="foquz-dialog__title">
                    Распределение баллов по диапазонам
                </h2>
                <button class="print-button no-print f-btn"
                        data-bind="click: function() { print() }">
                    <foquz-icon class="f-btn-prepend"
                                params="icon: 'print'"></foquz-icon>
                    Распечатать
                </button>
            </div>
        </div>
    </div>


    <div class="foquz-dialog__body">
        <div class="foquz-dialog__scroll"
             data-bind="nativeScrollbar, nativeScrollbarDisabled: modalScrollDisabled">
            <div class="container d-flex flex-column h-100">
                <div class="points-stats-meta">
                    <div class="points-stats-meta__wrapper"
                         data-bind="using: pointsModel">
                        <foquz-stats-item class="adaptive">
                            <div class="value"
                                 data-bind="text: $parent.maxPoints"></div>
                            <div class="label">Max кол-во баллов</div>
                        </foquz-stats-item>

                        <foquz-stats-item class="adaptive">
                            <div class="value"
                                 data-bind="text: $parent.minPoints"></div>
                            <div class="label">Min кол-во баллов</div>
                        </foquz-stats-item>

                        <foquz-stats-item class="adaptive">
                            <div class="value">
                                <span data-bind="text: $parent.avgPoints"></span>
                                /
                                <span data-bind="text: $parent.avgPointsPercent + '%'"></span>
                            </div>
                            <div class="label">Среднее кол-во баллов</div>
                        </foquz-stats-item>
                    </div>
                </div>


                <!-- ko if: filters -->
                <div style="display: none"
                     class="only-print">
                    <stats-filters-value params="filters: filters"
                                         class="border-bottom points-stats-modal-page__stats px-10p py-15p">
                    </stats-filters-value>
                </div>
                <!-- /ko -->


                <div class="points-stats-sidesheet__content h-100">
                    <div class="row no-print">
                        <div class="col-12 col-lg-6">
                            <h3 class="f-fs-3 bold">Количество диапазонов</h3>
                            <div class="row mt-4">
                                <div class="col-12 col-md-auto d-flex align-items-center mb-4">
                                    <foquz-slider
                                            params="value: rangesCount, min: 2, max: 20, withValue: true, disabled: noRanges">
                                    </foquz-slider>
                                </div>
                                <div class="col-12 col-md-auto">
                                    <switch params="checked: noRanges">Показать без диапазонов</switch>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="points-stats-sidesheet__data">
                        <div class="row py-md-4 h-100">
                            <div class="col points-stats-modal-page__table points-stats-sidesheet__table">

                                <div class="h-100"
                                     data-bind="nativeScrollbar, nativeScrollbarDisabled: tableScrollDisabled">
                                    <table class="table f-table">
                                        <thead>
                                        <tr>
                                            <th>Диапазон</th>
                                            <th>Кол-во ответов</th>
                                            <th width="85">Проценты</th>
                                        </tr>
                                        </thead>
                                        <!-- ko ifnot: loading -->
                                        <tbody>
                                        <!-- ko foreach: ranges -->
                                        <tr>
                                            <td data-bind="text: range"></td>
                                            <td data-bind="text: count"></td>
                                            <td data-bind="text: percents + '%'"></td>
                                        </tr>
                                        <!-- /ko -->
                                        </tbody>
                                        <!-- /ko -->
                                    </table>
                                    <!-- ko if: loading -->
                                    <spinner></spinner>
                                    <!-- /ko -->
                                </div>

                            </div>
                            <div class="col points-stats-modal-page__chart">
                                <!-- ko template: {
                               foreach: templateIf(!noRanges(), $data),
                               afterAdd: fadeAfterAddFactory(400),
                               beforeRemove: fadeBeforeRemoveFactory(400)
                             } -->
                                <div class="charts-block"
                                     data-bind="let: { chartMode: ko.observable('pie') }">

                                    <div class="charts-block__toggler">
                                        <button class="f-icon-button f-icon f-icon-chart f-icon-chart--column"
                                                title="Столбчатая диаграмма"
                                                data-bind="tooltip,
                     click: function() {
                       chartMode('column')
                     },css: {
                       'active': chartMode() == 'column'
                     }, component: {
                       name: 'foquz-icon',
                       params: { prefix: 'chart', icon: 'column' }
                     }">
                                        </button>

                                        <button class="f-icon-button f-icon f-icon-chart f-icon-chart--pie"
                                                title="Круговая диаграмма"
                                                data-bind="tooltip,
                     click: function() {
                       chartMode('pie')
                     },css: {
                       'active': chartMode() == 'pie'
                     }, component: {
                       name: 'foquz-icon',
                       params: { prefix: 'chart', icon: 'pie' }
                     }"></button>
                                    </div>

                                    <div data-bind="slide: chartMode() == 'column'">
                                        <column-chart params="config: columnChartData"></column-chart>
                                    </div>

                                    <div data-bind="slide: chartMode() == 'pie'">
                                        <pie-chart params="config: pieChartData"></pie-chart>
                                    </div>
                                </div>
                                <!-- /ko -->
                            </div>
                        </div>
                    </div>

                    <print-footer></print-footer>
                </div>
            </div>
        </div>
    </div>


</sidesheet>
