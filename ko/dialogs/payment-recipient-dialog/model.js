import { DialogWrapper } from 'Dialogs/wrapper';
import { ApiUrl } from 'Utils/url/api-url';
import { LegalEntitiesCollection } from 'Models/data-collection/legal-entities';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.companyId = params.companyId;
    this.legalsCollection = new LegalEntitiesCollection();
    this.legalsCollection.load();
    this.recipient = ko.observable(params.recipientId);
  }

  submit() {

    $.ajax({
      url: ApiUrl('company/change-legal-entity', {
        companyId: this.companyId,
      }),
      data: {
        legalEntityId: this.recipient()
      },
      method: 'POST',
      success: (response) => {
        this.emitEvent('change-recipient', response);
        this.hide();
      },
      error: (response) => {
        console.error(response.responseJSON);
      }
    });
  }
}
