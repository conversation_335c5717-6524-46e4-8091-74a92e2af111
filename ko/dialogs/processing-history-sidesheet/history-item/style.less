@import "@/less/vars/colors";

.fc-history-item {
  margin-bottom: 25px;
  display: block;

  &__header {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    font-size: 12px;
  }

  &-user {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-right: 15px;

    &__pic {
      width: 30px;
      height: 30px;
      margin-right: 10px;
      flex-shrink: 0;
      border-radius: 8px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }

    &__name {
      color: var(--f-color-text);
    }

    &__label {
      color: var(--f-color-service);
    }
  }

  &__date {
    color: var(--f-color-service);
  }

  &__data {
    padding-left: 40px;
  }

  &-separator {
    color: #cfd8dc;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: -2px;
  }

  &-status {
    &[data-status="0"] {
      color: @processing-created;
    }
    &[data-status="1"] {
      color: @processing-in-process;
    }
    &[data-status="2"] {
      color: @processing-delayed;
    }
    &[data-status="3"] {
      color: @processing-completed;
    }
    &[data-status="4"] {
      color: @processing-execution;
    }
  }

  &-change {
    margin-bottom: 10px;
    font-size: 15px;
  }

  &-files {
    display: flex;
    flex-wrap: wrap;

    .fc-history-item-separator {
      margin-top: 12px;
    }
  }

  &__comment {
  }
}
