<div class="fc-history-item__wrapper">
  <header class="fc-history-item__header">
    <div class="fc-history-item-user">
      <div class="fc-history-item-user__pic">
        <img
          data-bind="attr: {
                src: item.user && item.user.avatar || '/assets/1ff3c60d/preview-dummy.png'
            }"
          alt=""
        />
      </div>
      <!-- ko if: item.user -->
      <a
        class="fc-history-item-user__name"
        data-bind="attr: {
            href: '/foquz/settings?tab=user&loginSearch=' + item.user.login
        }"
        target="_blank"
      >
        <div class="font-weight-500" data-bind="text: item.user.name || item.user.login, "></div>
        <!-- ko if: item.isModerator -->
        <div class="fc-history-item-user__label">Модератор</div>
        <!-- /ko -->
        <!-- ko if: item.isExecutor -->
        <div class="fc-history-item-user__label">Исполнитель</div>
        <!-- /ko -->
        <div class="fc-history-item__date d-md-none" data-bind="text: item.createdAt"></div>
      </a>
      <!-- /ko -->
    </div>
    <div class="fc-history-item__date d-none d-md-block" data-bind="text: item.createdAt"></div>
  </header>

  <div class="fc-history-item__data">
    <!-- ko if: item.status -->
    <div class="fc-history-item-change">
      <span
        class="fc-history-item-status"
        data-bind="text: item.status.from.text, attr: {
            'data-status': item.status.from.id
        }"
      ></span>
      <fc-icon
        class="fc-history-item-separator"
        params="name: 'long-arrow-right-bold', width: 12, height: 8"
      ></fc-icon>
      <span
        class="fc-history-item-status"
        data-bind="text: item.status.to.text, attr: {
            'data-status': item.status.to.id
        }"
      ></span>
    </div>
    <!-- /ko -->

    <!-- ko foreach: item.changes -->
    <div class="fc-history-item-change">
      <span class="f-color-service" data-bind="text: name + ':'"></span>
      <span data-bind="text: from"></span>
      <fc-icon
        class="fc-history-item-separator"
        params="name: 'long-arrow-right-bold', width: 12, height: 8"
      ></fc-icon>
      <span data-bind="text: to"></span>
    </div>
    <!-- /ko -->

    <!-- ko if: item.filesChanges -->
    <div class="fc-history-item-change">
      <div class="f-color-service">Файлы:</div>

      <div class="fc-history-item-files">
        <!-- ko ifnot: item.filesChanges.to.length -->
        <div>Не выбраны</div>
        <!-- /ko -->

        <!-- ko foreach: item.filesChanges.to -->
        <div class="media-wrapper media-wrapper--sm mr-3 mb-3">
          <img
            data-bind="attr: {
              src: $data.poster
            }, fancyboxGalleryItem: {
            gallery: $parent.item.filesChanges.to.map(function(file) {
              return {
                src: file.url
              }
            }),
            index: $index(),
          }"
            alt=""
          />
        </div>
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: item.status -->
    <!-- ko if: item.comment -->
    <div class="fc-history-item__comment" data-bind="text: item.comment"></div>
    <!-- /ko -->

    <!-- ko if: item.files -->
    <div class="fc-history-item-files mt-10p">
      <!-- ko foreach: item.files -->
      <div class="media-wrapper media-wrapper--sm mr-3 mb-3">
        <img
          data-bind="attr: {
            src: $data.poster
          }, fancyboxGalleryItem: {
          gallery: $parent.item.files.map(function(file) {
            return {
              src: file.url
            }
          }),
          index: $index(),
        }"
          alt=""
        />
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>
</div>
