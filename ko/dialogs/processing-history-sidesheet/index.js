import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";
import { registerComponent } from "@/utils/engine/register-component";
import * as HistoryItem from "./history-item";

registerComponent("fc-processing-history-item", HistoryItem);

ko.components.register("processing-history-sidesheet", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("processing-history-sidesheet");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
