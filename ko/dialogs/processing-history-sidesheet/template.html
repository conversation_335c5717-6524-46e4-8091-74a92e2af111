<!-- ko let: { $dialogWrapper: $component } -->
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2
        class="foquz-dialog__title"
        data-bind="text: 'История изменений'"
      ></h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <!-- ko ifnot: inited -->
    <div class="h-100 d-flex align-items-center justify-content-center">
      <fc-spinner class="f-color-primary"></fc-spinner>
    </div>
    <!-- /ko -->

    <!-- ko if: inited -->

    <!-- ko if: empty -->
    <div class="h-100 d-flex align-items-center justify-content-center">Изменений пока не было</div>
    <!-- /ko -->

    <!-- ko ifnot: empty -->
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container pb-4">
        <!-- ko foreach: { data: items } -->
        <fc-processing-history-item
          params="item: $data"
        ></fc-processing-history-item>
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>
</sidesheet>
<!-- /ko -->
