import { DialogWrapper } from "@/dialogs/wrapper";
import { getProcessingHistory } from "@/api/answers/history";
import {
  FeedbackHistoryItem,
  HistoryItem,
  PollHistoryItem,
  RequestHistoryItem,
} from "./models/HistoryItem";

const { observable, observableArray } = ko;

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    const { answerId, mode } = params;

    this.mode = mode;

    this.inited = observable(false);
    this.empty = observable(false);
    this.items = observableArray([]);

    this.answerId = answerId;
    this.page = observable(1);

    this.load()
      .then((items) => {
        if (!items.length) {
          this.empty(true);
          return;
        }
        this.items(items);
      })
      .finally(() => {
        this.inited(true);
      });
  }

  load() {
    return getProcessingHistory(this.answerId, this.mode).then((items) => {
      const newItems = [];
      items.forEach((state, index) => {
        const prevState = items[index + 1];

        const diff = HistoryItem(this.mode, state, prevState,)

        if (diff)
          newItems.push(diff);
      });
      return newItems;
    });
  }
}
