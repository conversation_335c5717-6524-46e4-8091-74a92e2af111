import { Processing } from "@/entities/models/processing/types";

import { ClientDateTime } from "@/types";
import { ProcessingStatuses, ProcessingStatusNames } from '../../../constants/processing/processingStatuses';
import { FineTypes } from "@/constants/fine";
import { FileData } from "@/entities/models/file/types";

interface StateView {
  user?: {
    name: string;
    avatar: string;
    login: string;
  };
  isModerator: boolean;
  isExecutor: boolean;
  createdAt: ClientDateTime;
  status?: {
    from: {
      text: string;
      id: ProcessingStatuses;
    };
    to: {
      text: string;
      id: ProcessingStatuses;
    };
  };
  changes?: Array<{
    name: string;
    from: string;
    to: string;
  }>;
  filesChanges?: {
    from: Array<FileData>;
    to: Array<FileData>;
  };
  files?: Array<FileData>;
  comment?: string;
}

export function HistoryItem(
  mode: string,
  state: Processing,
  prevState: Processing
): StateView {
  const {
    authorName,
    authorId,
    authorLogin,
    authorAvatar,
    createdAt,
    status,
    moderatorId,
    moderatorName,
    executorId,
    executorName,
    employeeName,
    processUp,
    reasonName,
    fineName,
    fineType,
    fineAmount,
    compensationName,
    notificationScript,
    comment,
    executorComment,
    files
  } = state;

  const {
    status: prevStatus = ProcessingStatuses.Created,
    moderatorName: prevModeratorName = "",
    executorName: prevExecutorName = "",
    employeeName: prevEmployeeName = "",
    processUp: prevProcessUp = "",
    reasonName: prevReasonName = "",
    fineName: prevFineName = "",
    fineAmount: prevFineAmount = "",
    compensationName: prevCompensationName = "",
    notificationScript: prevNotificationScript = null,
    comment: prevComment = "",
    executorComment: prevExecutorComment = "",
    files: prevFiles = []
  } = prevState || {};

  

  const view: StateView = {
    createdAt,
    isModerator: authorId === moderatorId,
    isExecutor: authorId === executorId
  };

  if (authorId) {
    view.user = {
      name: authorName,
      avatar: authorAvatar,
      login: authorLogin
    };
  }

  let statusChanged = status !== prevStatus;

  if (statusChanged) {
    view.status = {
      from: {
        id: prevStatus,
        text: ProcessingStatusNames[prevStatus],
      },
      to: {
        id: status,
        text: ProcessingStatusNames[status],
      },
    };

    view.comment = comment || executorComment;
  }

  const changes = [];

  if (moderatorName !== prevModeratorName) {
    changes.push({
      name: "Модератор",
      from: prevModeratorName || 'не назначен',
      to: moderatorName || 'не назначен',
    });
  }

  if (processUp !== prevProcessUp) {
    changes.push({
      name: "Обработать до",
      from: prevProcessUp || 'не назначено',
      to: processUp  || 'не назначено',
    });
  }

  if (executorName !== prevExecutorName) {
    changes.push({
      name: "Исполнитель",
      from: prevExecutorName || 'не назначен',
      to: executorName || 'не назначен',
    });
  }

  if (reasonName !== prevReasonName) {
    changes.push({
      name: "Причина плохого отзыва",
      from: prevReasonName || 'не назначена',
      to: reasonName || 'не назначена',
    });
  }

  if (employeeName !== prevEmployeeName) {
    changes.push({
      name: "Сотрудник",
      from: prevEmployeeName || 'не назначен',
      to: employeeName || 'не назначен',
    });
  }


  if (fineName !== prevFineName) {
    changes.push({
      name: "Взыскание или нарушение",
      from: prevFineName  || 'не назначено',
      to: fineName  || 'не назначено',
    });
  }

  if (fineName) {
    if (fineAmount !== prevFineAmount) {
      changes.push({
        name:
          fineType === FineTypes.Fine
            ? "Сумма взыскания, ₽"
            : "Баллы за нарушение",
        from: prevFineAmount  || 'не назначено',
        to: fineAmount  || 'не назначено',
      });
    }
  }

  if (compensationName !== prevCompensationName) {
    changes.push({
      name: "Компенсация",
      from: prevCompensationName || 'не назначена',
      to: compensationName || 'не назначена',
    });
  }

  if (notificationScript?.name !== prevNotificationScript?.name) {
    changes.push({
      name: "Сценарий уведомления",
      from: prevNotificationScript?.name || 'не назначен',
      to: notificationScript?.name || 'не назначен',
    });
  }

  if (!statusChanged) {
    if (comment !== prevComment) {
      changes.push({
        name: "Комментарий модератора",
        from: prevComment || 'не назначен',
        to: comment || 'не назначен',
      });
    }
    if (executorComment !== prevExecutorComment) {
      changes.push({
        name: "Комментарий исполнителя",
        from: prevExecutorComment || 'не назначен',
        to: executorComment || 'не назначен',
      });
    }

    const filesIds = files.map(file => file.id);
    const prevFilesIds = prevFiles.map(file => file.id);

    if (filesIds.join('') !== prevFilesIds.join('')) {
      view.filesChanges = {
        from: prevFiles,
        to: files
      }
    }
  } else {
    view.files = files;
  }

  view.changes = changes;

  console.log({ state, view })

  if (view.status || view.filesChanges || view.changes.length) return view;

  return null;
}
