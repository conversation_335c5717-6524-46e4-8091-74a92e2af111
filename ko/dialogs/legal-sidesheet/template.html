<!-- ko let: { $legalSidesheet: $component } -->
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">
        <!-- ko ifnot: company -->
        <span
          data-bind="text: legalId ? 'Редактировать юр. лицо' : 'Новое юр. лицо'"
        ></span>
        <!-- /ko -->

        <!-- ko if: company -->
        Реквизиты <span data-bind="text: company.name"></span>
        <!-- /ko -->
      </h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container pt-3" data-bind="using: formData">
        <!-- ko if: $legalSidesheet.loading -->
        <spinner></spinner>
        <!-- /ko -->

        <!-- ko ifnot: $legalSidesheet.loading -->
        <div class="row">
          <div class="col-3">
            <div class="form-group">
              <label class="form-label"
                >ИНН<span class="f-color-danger">*</span></label
              >
              <foquz-chars-counter params="value: inn, max: 10">
                <input
                  type="text"
                  class="form-control"
                  data-bind="textInput: $parent.inn,
                  css: {
                    'is-invalid': $legalSidesheet.formControlErrorStateMatcher($parent.inn)
                  }"
                />
              </foquz-chars-counter>

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(inn), text: inn.error"
              >
              </validation-feedback>
            </div>
          </div>
          <!-- ko if: $legalSidesheet.innSuggestion -->
          <div class="col-9" data-bind="using: $legalSidesheet.innSuggestion">
            <div class="form-group ghost-label mt-2">
              <div class="f-color-service">
                <span data-bind="text: name"></span>,
                <span data-bind="text: inn"></span>
              </div>
              <div>
                <a
                  href="javascript:void(0)"
                  data-bind="click: function() {
                  $legalSidesheet.setInnSuggestion();
                }"
                  >Добавить</a
                >
              </div>
            </div>
          </div>
          <!-- /ko -->
        </div>
        <div class="row">
          <div class="col-9">
            <div class="form-group">
              <label class="form-label"
                >Наименование<span class="f-color-danger">*</span></label
              >

              <input
                type="text"
                class="form-control"
                maxlength="500"
                data-bind="textInput: name,
                  css: {
                    'is-invalid': $legalSidesheet.formControlErrorStateMatcher(name)
                  }"
              />

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(name), text: name.error"
              >
              </validation-feedback>
            </div>
          </div>
          <!-- ko ifnot: $legalSidesheet.company -->
          <div class="col-3">
            <div class="form-group ghost-label">
              <switch params="checked: isDefault" class="mt-10p"
                >Юр.лицо по умолчанию</switch
              >
            </div>
          </div>
          <!-- /ko -->
        </div>
        <div class="row">
          <div class="col-3">
            <div class="form-group">
              <label class="form-label">КПП</label>
              <foquz-chars-counter params="value: kpp, max: 9">
                <input
                  type="text"
                  class="form-control"
                  data-bind="textInput: $parent.kpp,
                        css: {
                          'is-invalid': $legalSidesheet.formControlErrorStateMatcher($parent.kpp)
                        }"
                />
              </foquz-chars-counter>

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(kpp), text: kpp.error"
              >
              </validation-feedback>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <label class="form-label"
                >Юридический адрес и почтовый индекс<span class="f-color-danger"
                  >*</span
                ></label
              >

              <input
                type="text"
                class="form-control"
                maxlength="500"
                data-bind="textInput: address,
                css: {
                  'is-invalid': $legalSidesheet.formControlErrorStateMatcher(address)
                }"
              />

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(address), text: address.error"
              >
              </validation-feedback>
            </div>
          </div>
        </div>

        <hr class="mt-0" />

        <div class="row">
          <div class="col-3">
            <div class="form-group">
              <label class="form-label">БИК</label>
              <foquz-chars-counter params="value: bik, max: 9">
                <input
                  type="text"
                  class="form-control"
                  data-bind="textInput: $parent.bik,
                  css: {
                    'is-invalid': $legalSidesheet.formControlErrorStateMatcher($parent.bik)
                  }"
                />
              </foquz-chars-counter>

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(bik), text: bik.error"
              >
              </validation-feedback>
            </div>
          </div>
          <!-- ko if: $legalSidesheet.bikSuggestion -->
          <div class="col-9" data-bind="using: $legalSidesheet.bikSuggestion">
            <div class="form-group ghost-label mt-2">
              <div class="f-color-service">
                <span data-bind="text: name"></span>, к/с
                <span data-bind="text: kpp"></span>
              </div>
              <div>
                <a
                  href="javascript:void(0)"
                  data-bind="click: function() {
                  $legalSidesheet.setBikSuggestion()
                }"
                  >Добавить</a
                >
              </div>
            </div>
          </div>
          <!-- /ko -->
        </div>
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <label class="form-label">Название банка</label>

              <input
                type="text"
                class="form-control"
                maxlength="500"
                data-bind="textInput: bankName,
                  css: {
                      'is-invalid': $legalSidesheet.formControlErrorStateMatcher(bankName)
                  }"
              />

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(bankName), text: bankName.error"
              >
              </validation-feedback>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">Корреспондентский счет</label>
              <foquz-chars-counter params="value: corAccount, max: 20">
                <input
                  type="text"
                  class="form-control"
                  data-bind="textInput: $parent.corAccount,
                  css: {
                    'is-invalid': $legalSidesheet.formControlErrorStateMatcher($parent.corAccount)
                  }"
                />
              </foquz-chars-counter>

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(corAccount), text: corAccount.error"
              >
              </validation-feedback>
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">Расчетный счет</label>
              <foquz-chars-counter params="value: paymentAccount, max: 20">
                <input
                  type="text"
                  class="form-control"
                  data-bind="textInput: $parent.paymentAccount,
                  css: {
                    'is-invalid': $legalSidesheet.formControlErrorStateMatcher($parent.paymentAccount)
                  }"
                />
              </foquz-chars-counter>

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(paymentAccount), text: paymentAccount.error"
              >
              </validation-feedback>
            </div>
          </div>
        </div>

        <hr class="mt-0" />

        <div class="row">
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">ФИО подписанта</label>

              <input
                type="text"
                class="form-control"
                maxlength="250"
                data-bind="textInput: signerFullname,
                  css: {
                    'is-invalid': $legalSidesheet.formControlErrorStateMatcher(signerFullname)
                  }"
              />

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(signerFullname), text: signerFullname.error"
              >
              </validation-feedback>
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">Должность подписанта</label>

              <input
                type="text"
                class="form-control"
                maxlength="250"
                data-bind="textInput: signerPosition,
                  css: {
                    'is-invalid': $legalSidesheet.formControlErrorStateMatcher(signerPosition)
                  }"
              />

              <validation-feedback
                params="show: $legalSidesheet.formControlErrorStateMatcher(signerPosition), text: signerPosition.error"
              >
              </validation-feedback>
            </div>
          </div>
        </div>

        <fc-document-file class="form-group" params="name: 'Политика конфиденциальности', document:  $legalSidesheet.privacyPolicy"></fc-document-file>
        <fc-document-file class="pb-25p" params="name: 'Пользовательское соглашение', document:  $legalSidesheet.termsOfUse"></fc-document-file>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() {
                $dialog.hide();
              }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Отменить
      </button>
      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="click: function() { submit(); }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        Сохранить
      </button>
    </div>
  </div>
</sidesheet>
<!-- /ko -->
