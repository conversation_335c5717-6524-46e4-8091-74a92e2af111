import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './components/document-file'

ko.components.register('legal-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('legal-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});