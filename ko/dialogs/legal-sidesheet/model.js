import { DialogWrapper } from "Dialogs/wrapper";
import { ApiUrl } from "Utils/url/api-url";
import { camelToSnake, snakeToCamel } from "Utils/string/snake-to-camel";
import { createFileInput } from "../../utils/file-loader/create-file-input";
import serialize from "json-form-data";
import { addFilesToFormData, toFormData } from "../../utils/api/form-data";

const innSuggestUrl =
  "https://suggestions.dadata.ru/suggestions/api/4_1/rs/findById/party";
const bikSuggestUrl =
  "https://suggestions.dadata.ru/suggestions/api/4_1/rs/suggest/bank";

let unique = 1;

function DocumentFile() {
  const url = ko.observable(null);
  const file = ko.observable(null);
  const name = ko.observable("");

  return {
    url,
    file,
    name,

    remove() {
      file(null);
      url(null);
      name(null);
    },

    load() {
      const input = createFileInput(
        (data) => {
          input.remove();

          if (!data.file) return;
          file(data.file);
          name(data.file.name);

          const fileUrl = URL.createObjectURL(data.file);
          url(fileUrl);
        },
        {
          open: true,
          accept: ".pdf",
        }
      );
    },

    update(newUrl) {
      if (!newUrl) {
        url(null);
        file(null);
        name(null);
      } else {
        url(`/${newUrl}`);
        const fileName = newUrl.split("/").pop();
        name(fileName);
      }
    },
  };
}
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.token = params.token;

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.loading = ko.observable(true);

    this.company = params.company;
    this.legalId = params.legalId;
    this.isAdmin = params.isAdmin;

    this.termsOfUse = DocumentFile();
    this.privacyPolicy = DocumentFile();

    this.formData = ko.validatedObservable(
      {
        inn: ko.observable("").extend({
          required: {
            message: "Обязательное поле",
          },
        }),
        name: ko.observable("").extend({
          required: {
            message: "Обязательное поле",
          },
        }),
        isDefault: ko.observable(false),
        kpp: ko.observable(""),
        address: ko.observable("").extend({
          required: {
            message: "Обязательное поле",
          },
        }),
        bik: ko.observable(""),
        bankName: ko.observable(""),
        corAccount: ko.observable(""),
        paymentAccount: ko.observable(""),
        signerFullname: ko.observable(""),
        signerPosition: ko.observable(""),
      },
      { live: true, deep: true }
    );

    this.innQuery = null;
    this.innSuggestion = ko.observable(null);
    this.innUpdating = ko.observable(false);
    this.bikQuery = null;
    this.bikSuggestion = ko.observable(null);
    this.bikUpdating = ko.observable(false);

    if (this.legalId || this.company) {
      this.loadData().then(() => {
        this.loading(false);
      });
    } else {
      this.loading(false);
      this.subscribe();
    }
  }

  get url() {
    if (this.company) {
      if (this.isAdmin)
        return ApiUrl("company/save-requisites", {
          companyId: this.company.id,
        });
      return ApiUrl("company/save-requisites");
    }

    if (this.legalId)
      return ApiUrl("legal-entities/update", { id: this.legalId });

    return ApiUrl("legal-entities/create");
  }

  suggestInn(v) {
    let index = unique++;

    var options = {
      method: "POST",
      mode: "cors",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: "Token " + this.token,
      },
      body: JSON.stringify({
        query: v,
        count: 1,
        branch_type: "MAIN",
      }),
    };

    fetch(innSuggestUrl, options)
      .then((response) => response.json())
      .then((result) => {
        if (result.suggestions && result.suggestions.length) {
          if (this.innQuery > index) return;
          this.innQuery = index;
          let data = result.suggestions[0];
          this.innSuggestion({
            name: data.value,
            inn: data.data.inn,
            kpp: data.data.kpp,
            address: data.data.address.unrestricted_value,
          });
        }
      })
      .catch((error) => console.log("error", error));
  }

  setInnSuggestion() {
    this.innUpdating(true);
    let data = this.innSuggestion();
    let formData = this.formData();
    formData.inn(data.inn);
    formData.name(data.name);
    formData.kpp(data.kpp);
    formData.address(data.address);
    this.innSuggestion(null);
    this.innUpdating(false);
  }

  suggestBik(v) {
    let index = unique++;

    var options = {
      method: "POST",
      mode: "cors",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: "Token " + this.token,
      },
      body: JSON.stringify({
        query: v,
        count: 1,
      }),
    };

    fetch(bikSuggestUrl, options)
      .then((response) => response.json())
      .then((result) => {
        if (this.bikQuery > index) return;
        this.bikQuery = index;
        if (result.suggestions && result.suggestions.length) {
          let data = result.suggestions[0];
          this.bikSuggestion({
            name: data.value,
            bik: data.data.bic,
            inn: data.data.inn,
            kpp: data.data.kpp,
            corAccount: data.data.correspondent_account,
          });
        }
      })
      .catch((error) => console.log("error", error));
  }

  setBikSuggestion() {
    this.bikUpdating(true);
    let data = this.bikSuggestion();
    let formData = this.formData();
    formData.bankName(data.name);
    formData.bik(data.bik);
    formData.corAccount(data.corAccount);
    this.bikSuggestion(null);
    this.bikUpdating(false);
  }

  loadData() {
    let url;

    if (this.company) {
      if (this.isAdmin) {
        url = ApiUrl("company/requisites", { companyId: this.company.id });
      } else {
        url = ApiUrl("company/requisites");
      }
    } else {
      url = ApiUrl("legal-entities/view", { id: this.legalId });
    }

    return $.ajax({
      url,
      success: (response) => {
        if (response) {
          let data = this.formData();
          Object.keys(data).forEach((key) => {
            data[key](response[camelToSnake(key)]);
          });
          this.privacyPolicy.update(response.privacy_policy);
          this.termsOfUse.update(response.terms_of_use);
        }

        this.subscribe();
      },
      error: (response) => {
        console.error(response.responseJSON);
      },
    });
  }

  getData() {
    let data = ko.toJS(this.formData);
    let result = {};
    Object.keys(data).forEach((key) => {
      result[camelToSnake(key)] = data[key];
    });
    if (this.company) {
      delete result.is_default;
    } else {
      result.is_default = result.is_default ? 1 : 0;
    }

    return result;
  }

  submit() {
    this.isSubmitted(true);
    if (!this.formData.isValid()) return;

    const data = this.getData();
    const fd = serialize(data);

    if (this.privacyPolicy.file()) {
      fd.append("privacy_policy", this.privacyPolicy.file());
    } else if (!this.privacyPolicy.url()) {
      fd.append("privacy_policy", "");
    }

    if (this.termsOfUse.file()) {
      fd.append("terms_of_use", this.termsOfUse.file());
    } else if (!this.termsOfUse.url()) {
      fd.append("terms_of_use", "");
    }

    $.ajax({
      url: this.url,
      data: fd,
      processData: false,
      contentType: false,
      method: "POST",
      success: (response) => {
        this.emitEvent("submit", response);
        this.hide();
      },
      error: (response) => {
        console.error(response.responseJSON);
      },
    });
  }

  subscribe() {
    let suggestInn = _.debounce((v) => {
      this.suggestInn(v);
    }, 400);

    this.subscriptions.push(
      this.formData().inn.subscribe((v) => {
        if (this.innUpdating()) return;
        if (v.length < 2) {
          this.innSuggestion(null);
          return;
        }
        suggestInn(v);
      })
    );

    let suggestBik = _.debounce((v) => {
      this.suggestBik(v);
    }, 400);
    this.subscriptions.push(
      this.formData().bik.subscribe((v) => {
        if (this.bikUpdating()) return;
        if (v.length < 2) {
          this.bikSuggestion(null);
          return;
        }
        suggestBik(v);
      })
    );
  }
}
