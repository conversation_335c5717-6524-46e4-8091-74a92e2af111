import { DialogWrapper } from 'Dialogs/wrapper';

const LAUNCH_NOW = '1';
const DEFER_LAUNCH = '2';
const CANCEL_LAUNCH = '3';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    let data = params.data;

    this.initializing = ko.observable(true);
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted,
    );

    this.mailing = data.mailing;

    this.launchType = ko.observable(LAUNCH_NOW);
    this.date = ko.observable(moment().format('DD.MM.YYYY')).extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () =>this.launchType() == '2'
      },
      validation: {
        validator: v => {
          let date = moment(v, 'DD.MM.YYYY');
          return date.isValid();
        },
        message: 'Некорректный формат',
        onlyIf: () => this.launchType() == '2'
      }
    });
    this.time = ko.observable('').extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () =>this.launchType() == '2',
      },
      validation: {
        validator: v => {
          let reg = /^\d\d:\d\d$/;
          if (!reg.test(v)) return false;
          let time = moment(v, 'HH:mm');
          return time.isValid();
        },
        message: 'Некорректный формат',
        onlyIf: () => this.launchType() == '2',
      }
    });

    this.canStop = ko.observable(false);

    if (this.mailing.launchedAt()) {
      this.canStop(true);
      this.launchType(DEFER_LAUNCH);
      let date = moment(this.mailing.launchedAt(), 'DD.MM.YYYY HH:mm');
      this.date(date.format('DD.MM.YYYY'));
      this.time(date.format('HH:mm'));
    }
  }

  submit() {
    this.isSubmitted(true);
    if (!this.date.isValid() || !this.time.isValid()) return;

    let date = this.date();
    let time = moment(this.time(), 'HH:mm').format('HH:mm:ss');

    this.emitEvent('launch', {
      type: this.launchType(),
      date: `${date} ${time}`
    });

    this.hide();
  }

  onRender() {
    this.initializing(false);
  }

}
