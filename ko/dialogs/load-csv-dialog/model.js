import { DialogWrapper } from 'Dialogs/wrapper';
import { declOfNum } from 'Utils/string/decl-of-num';
import { ApiUrl } from 'Utils/url/api-url';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    console.log('LOAD CSV', params)

    let stats = params.stats;
    this.loading = ko.observable(false);

    this.stats = null;
    this.insertedCount = ko.observable(0);
    this.insertedHtml = ko.pureComputed(() => {
      let count = this.insertedCount();
      if (!count) return '';
      return `<div>
        <b>${count} ${declOfNum(count, [
        'строка',
        'строки',
        'строк'
      ])}</b> ${declOfNum(count, ['добавлена', 'добавлены', 'добавлены'])}
      </div>`;
    });

    this.updatedCount = ko.observable(0);
    this.updatedRows = ko.observableArray([]);
    this.updatedHtml = ko.pureComputed(() => {
      let count = this.updatedCount();
      let rows = this.updatedRows();
      if (!count) return '';
      return `<b>${declOfNum(count, [
        'Строка',
        'Строки',
        'Строки'
      ])} ${rows.join(
        ', '
      )}</b> — данные обновлены (контакты совпадают с существующими)`;
    });

    this.failedCount = ko.observable(0);
    this.failedRows = ko.observableArray([]);
    this.failedHtml = ko.pureComputed(() => {
      let count = this.failedCount();
      let rows = this.failedRows();
      if (!count) return '';
      return `<b>${declOfNum(count, [
        'Строка',
        'Строки',
        'Строки'
      ])} ${rows.join(', ')}</b> пропущены (ошибки в данных)`;
    });

    this.conflictedCount = ko.observable(0);
    this.conflictedRows = ko.observableArray([]);
    this.conflictedHtml = ko.pureComputed(() => {
      let count = this.conflictedCount();
      let rows = this.conflictedRows();
      if (!count) return '';
      return `<b>${declOfNum(count, [
        'Строка',
        'Строки',
        'Строки'
      ])} ${rows.join(
        ', '
      )}</b> — клиенты с такими почтами, но с другими номерами телефонов (внешними ID) уже есть в системе.`;
    });

    this.action = ko.observable('update');

    this.errors = ko.observableArray([]);

    this.setStats(stats);
  }

  setStats(stats) {
    this.stats = stats;

    this.insertedCount(stats.inserted);

    this.updatedCount(stats.updated);
    this.updatedRows(this._getRows(stats.updated, stats.updated_rows));

    this.failedCount(stats.failed);
    this.failedRows(this._getRows(stats.failed, stats.failed_rows));

    this.conflictedCount(stats.conflicted);
    this.conflictedRows(this._getRows(stats.conflicted, stats.conflicted_rows));

    this.action('update');

    this.errors.removeAll();
  }

  _getRows(count, rowsString) {
    if (count && rowsString) {
      try {
        let rows = JSON.parse(rowsString);
        return rows;
      } catch (e) {
        console.error('Не удалось извлечь обновленные строки');
      }
    }
    return [];
  }

  continueLoading() {
    if (this.action() == 'pass') {
      this.hide();
      return;
    }

    this.loading(true);

    let params = {
      statId: this.stats.id
    };

    if (this.action() == 'update-mail') {
      params.withEmail = 1;
    }

    let url = ApiUrl('contact/resolve-conflict', params);

    $.ajax({
      method: 'POST',
      url,
      success: (response) => {
        this.setStats(response.stat);
        this.loading(false);
      },
      error: (response) => {
        let errors = response.responseJSON;
        console.error(errors);
        this.errors(Object.values(errors));
        this.loading(false);
      }
    });
  }
}
