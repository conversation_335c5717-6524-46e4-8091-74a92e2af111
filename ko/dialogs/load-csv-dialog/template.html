<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header>
    Загрузка контактов
  </foquz-dialog-header>



  <div class="foquz-dialog__body">

    <!-- ko if: loading -->
    <spinner></spinner>
    <!-- /ko -->

    <!-- ko ifnot: loading -->

    <!-- ko if: errors().length -->
    <!-- ko foreach: errors -->
    <div class="mb-3 f-color-danger"
         data-bind="text: $data"></div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: errors.length -->

    <!-- ko if: failedCount -->
    <div data-bind="html: failedHtml"
         class="mb-3"></div>
    <!-- /ko -->

    <!-- ko if: updatedCount -->
    <div data-bind="html: updatedHtml"
         class="mb-3"></div>
    <!-- /ko -->

    <!-- ko if: insertedCount -->
    <div data-bind="html: insertedHtml"
         class="mb-3"></div>
    <!-- /ko -->

    <!-- ko if: conflictedCount -->
    <div class="conflict full-width px-30p py-4">

      <div data-bind="html: conflictedHtml"
           class="mb-2"></div>
      <div class="mb-15p">Выберите дальнейшее действие и нажмите кнопку «Продолжить»:</div>

      <div>
        <foquz-radio params="checked: action, value: 'pass'"
                     class="mb-15p">Пропустить</foquz-radio>

        <foquz-radio params="checked: action, value: 'update'"
                     class="mb-15p">Обновить данные существующих клиентов без учёта почты
        </foquz-radio>

        <foquz-radio params="checked: action, value: 'update-mail'"
                     class="mb-15p">Обновить данные существующих клиентов с учётом почты</foquz-radio>
      </div>

    </div>
    <!-- /ko -->

    <!-- /ko -->
    <!-- /ko -->

  </div>

  <!-- ko if: conflictedCount -->
  <footer class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button class="f-btn"
              data-bind="click: function() {
        continueLoading();
      }">Продолжить</button>
    </div>
  </footer>
  <!-- /ko -->

</foquz-dialog>
