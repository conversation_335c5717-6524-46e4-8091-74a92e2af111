import { DialogWrapper } from '@/dialogs/wrapper';
import { ApiUrl } from '@/utils/url/api-url';
import { Range } from './range';

import { Translator } from '@/utils/translate';
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.translator = Translator('questions')

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.loading = ko.observable(true);
    this.pollId = params.id;
    this.max = ko.observable(null);

    this.ranges = ko.observableArray([]);
    this.tmpRanges = ko.computed(() => {
      return this.ranges().filter((r) => r.isTmp());
    });
    this.hasTmpRanges = ko.pureComputed(() => {
      return this.tmpRanges().length > 0;
    });
    this.rangesCount = ko.pureComputed(() => {
      return this.ranges().filter((r) => !r.isTmp()).length;
    });

    this.getData().then((data) => {
      this.max(data.max_points || 0);
      let ranges = data.scoresInterpretationRanges.map((r) => new Range(r));
      if (ranges.length) this.ranges(ranges);
      else this.addRange();

      this.loading(false);
    });
  }

  getData() {
    return new Promise((res) => {
      $.ajax({
        url: ApiUrl('poll/view', {
          id: this.pollId,
          fields: 'max_points,scoresInterpretationRanges'
        }),
        success: (response) => {
          res(response.model);
        },
        error: (response) => {
          console.error(response.responseJSON);
        }
      });
    });
  }

  addRange() {
    this.ranges.push(new Range());
  }

  removeRange(range) {
    this.ranges.remove(range);
  }

  submit() {
    this.isSubmitted(true);
    if (this.ranges().some((r) => !r.isValid())) return;

    let ranges = this.ranges().map((r, i) => {
      return {
        ...r.getData(),
        position: i
      };
    });

    $.ajax({
      url: ApiUrl('poll/save-scores-interpretation-ranges', {
        pollId: this.pollId
      }),
      method: 'POST',
      data: { ranges },
      success: (response) => {
        this.hide();
      },
      error: (response) => {
        console.error(response.responseJSON);
      }
    });
  }
}
