<!-- ko let: { $translator: $component.translator }-->
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header pb-20p">
    <div class="container">
      <h2 class="foquz-dialog__title mb-3">
        <span data-bind="text: $translator.t('Интерпретация баллов')"></span>
      </h2>

      <div
        class="f-color-service f-fs-1 mb-20p"
        data-bind="let: { opened: ko.observable(false) }"
      >
        <span
          data-bind="text: $translator.t('Для опросов с включенной системой баллов за ответ есть возможность настроить текст результата в зависимости от количества набранных баллов.')"
        ></span>
        <!-- ko ifnot: opened -->
        <fc-button
          params="size: 'auto', color: 'primary', mode: 'text', label: $translator.t('Подробнее'),
          click: function() { opened(true) }"
          class="ml-2"
        ></fc-button>
        <!-- /ko -->
        <!-- ko if: opened -->
        <span
          data-bind="text: $translator.t('Для этого укажите диапазоны и введите текст для краткого результата и подробного описания. А на конечном экране выберите соответствующие переменные: «Интерпретация баллов: Название» и «Интерпретация баллов: Описание». После прохождения опроса респондент увидит текст результата в зависимости от набранных им баллов.')"
        >
        </span>
        <!-- /ko -->
      </div>

      <!-- ko ifnot: loading -->
      <div class="d-flex align-items-center max-points">
        <div class="max-points__value" data-bind="text: max"></div>
        <div class="max-points__text">
          <span
            data-bind="html: $translator.t('Максимальное количество<br>баллов за опрос')"
          ></span>
        </div>
      </div>
      <!-- /ko -->
    </div>
  </div>

  <div class="foquz-dialog__body" data-drag-containment>
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container">
        <!-- ko if: loading -->
        <fc-spinner class="f-color-primary"></fc-spinner>
        <!-- /ko -->

        <!-- ko ifnot: loading -->

        <div>
          <h2 class="f-h2 pb-0 mb-10p">
            <span data-bind="text: $translator.t('Диапазоны баллов')"></span>
            <!-- ko if: rangesCount -->
            <span
              class="font-weight-normal f-color-service"
              data-bind="text: rangesCount"
            ></span>
            <!-- /ko -->
          </h2>

          <div class="f-fs-1 f-color-service mb-10p">
            <span
              data-bind="text: $translator.t('Добавление диапазонов для вывода результата тестирования')"
            ></span>
          </div>

          <div class="mb-30p">
            <fc-button
              params="mode: 'text',
                          size: 'auto',
                          color: 'success',
                          icon: 'plus',
                          label: $translator.t('Добавить диапазон'),
                          click: function() { addRange() },
                          disabled: hasTmpRanges"
            >
            </fc-button>
          </div>

          <div
            class="points-ranges mb-30p styled"
            data-bind="foquzSortable: {
            data: ranges,
            as: 'range',
            connectClass: false,
            options: {
                'handle': '.drag-handle',
            }
          }"
          >
            <fc-points-range
              class="points-ranges__item"
              params="range: range,
                    move: $parent.ranges().length > 1,
                    remove: $parent.ranges().length > 1,
                    formControlErrorStateMatcher: $parent.formControlErrorStateMatcher"
              data-bind="event: {
                'removeRange': function () { $parent.removeRange(range) }
              }"
            ></fc-points-range>
          </div>

          <div>
            <fc-button
              params="mode: 'text', color: 'success', icon: 'plus', label: $translator.t('Добавить диапазон'), click: function() { addRange() }, disabled: hasTmpRanges"
            >
            </fc-button>
          </div>
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() {
                $dialog.hide();
              }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        <span data-bind="text: $translator.t('Отменить')"></span>
      </button>
      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="click: function() { submit()  }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        <span data-bind="text: $translator.t('Сохранить')"></span>
      </button>
    </div>
  </div>
</sidesheet>
<!-- /ko -->
