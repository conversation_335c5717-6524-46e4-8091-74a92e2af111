import { Translator } from '@/utils/translate';

export class Range {
  constructor(data = {}) {
    this.validationTranslator = Translator('validation');
    this.translator = Translator('questions');
    this.from = ko.observable(data.min);
    this.to = ko.observable(data.max);
    this.title = ko.observable(data.result || '');
    this.description = ko.observable(data.description || '');

    this.isTmp = ko.computed(() => {
      return !this.from() && !this.to();
    });

    this.points = ko.observable().extend({
      validation: [
        {
          validator: () => {
            return this.from() || this.to();
          },
          message: () => this.validationTranslator.t('Должно быть заполнено хотя бы одно поле')()
        },
        {
          validator: () => {
            let from = parseInt(this.from());
            let to = parseInt(this.to());
            return from <= to;
          },
          onlyIf: () => this.from() && this.to(),
          message: () => this.validationTranslator.t('Некорректный диапазон')()
        }
      ]
    });
    this.texts = ko.observable().extend({
      validation: {
        validator: () => this.title().trim() || this.description().trim(),
        message:
          () => this.translator.t('Должно быть заполнено хотя бы одно из полей: результат или описание результата')()
      }
    });

    this.isValid = ko.computed(() => {
      return this.points.isValid() && this.texts.isValid();
    });
  }

  getData() {
    return {
      min: this.from(),
      max: this.to(),
      result: this.title(),
      description: this.description()
    };
  }
}
