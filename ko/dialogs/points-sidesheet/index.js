import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './points-range/index.js';
import 'Bindings/sortable';

ko.components.register('points-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('points-sidesheet');

      return new ViewModel(params, element);
    }
  },
  template: html
});
