<div class="fc-points-range__wrapper" data-bind="let: {
  $translator: $component.translator
}">
  <!-- ko if: move -->
  <fc-icon params="name: 'drag-arrow'"
           class="drag-handle"></fc-icon>
  <!-- /ko -->

  <div class="fc-points-range__data">
    <div class="d-flex">
      <div class="fc-points-range__range mb-4 mr-30p">
        <fc-label params="hint: $translator.t('Баллы, от-до')">
          <template data-slot="text">
            <span class="bold" data-bind="text: $translator.t('Баллы')() + ','"></span> <span class="font-weight-normal" data-bind="text: $translator.t('от–до')"></span>
          </template>
        </fc-label>
        <div>
          <div class="d-flex align-items-center">
            <fc-input params="mask: 'numbers', value: range.from, invalid: formControlErrorStateMatcher(range.points)" class="range-input">
            </fc-input>
            <span class="mx-1">–</span>
            <fc-input params="mask: 'numbers', value: range.to, invalid: formControlErrorStateMatcher(range.points)"  class="range-input">
            </fc-input>
          </div>

          <fc-error params="show: formControlErrorStateMatcher(range.points), text: range.points.error"></fc-error>
        </div>
      </div>
      <div class="fc-points-range__title mb-4">
        <fc-label params="text: $translator.t('Результат'), hint: $translator.t('Результат')"></fc-label>
        <fc-input
                  params="value: range.title, counter: true, maxlength: 250, invalid: formControlErrorStateMatcher(range.texts)">
        </fc-input>
      </div>
    </div>
    <div class="fc-points-range__description">
      <fc-label params="text: $translator.t('Описание результата'), hint: $translator.t('Описание результата')"></fc-label>
      <fc-textarea
                   params="value: range.description, counter: true, maxlength: 500, invalid: formControlErrorStateMatcher(range.texts)">
      </fc-textarea>

      <fc-error params="show: formControlErrorStateMatcher(range.texts), text: range.texts.error"></fc-error>
    </div>
  </div>

  <!-- ko if: remove -->
  <div class="fc-points-range__remove">
    <fc-button params="color: 'danger', icon: 'times', shape: 'square', click: function() { onRemove() }"></fc-button>
  </div>
  <!-- /ko -->
</div>
