import { FComponent } from 'Components/f-component';
import { Translator } from '@/utils/translate';
export class ViewModel extends FComponent {
  constructor(params, element) {
    super(params, element);

    this.translator = Translator('questions')

    element.classList.add('fc-points-range');

    this.range = params.range;
    this.move = params.move;
    this.remove = params.remove;
    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher;
  }

  onRemove() {
    this.emitEvent('removeRange');
  }
}
