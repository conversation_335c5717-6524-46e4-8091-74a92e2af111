import { DialogWrapper } from 'Dialogs/wrapper';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.error = ko.observable('');

    this.link = ko.observable('').extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: [
        {
          validator: () => false,
          onlyIf: () => this.error(),
          message: () => this.error()
        },
        {
          validator: (v) => {
            return validator.isURL(v);
          },
          message: 'Некорректный адрес'
        }
      ]
    });

    this.link.subscribe(() => {
      this.error('');
    });
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.submitMethod = params.submit;
  }

  submit() {
    this.isSubmitted(true);
    if (!this.link.isValid()) return;

    let data = { link: this.link(), hide: () => this.hide() };

    if (typeof this.submitMethod === 'function') {
      this.submitMethod(data)
        .then(() => {
          this.emitEvent('submit', data);
          this.hide();
        })
        .catch((err) => {
          this.error(err);
        });
    } else {
      this.emitEvent('submit', data);
      this.hide();
    }
  }
}
