<foquz-dialog params="ref: modal, dialogWrapper: $component">

<foquz-dialog-header>

</foquz-dialog-header>

<div class="foquz-dialog__body">
  <div>
    <label class="form-label">Ссылка</label>

    <input type="text" class="form-control" data-bind="textInput: link, css: {
      'is-invalid': formControlErrorStateMatcher(link)
    }">

    <validation-feedback params="show: formControlErrorStateMatcher(link), text: link.error"></validation-feedback>

  </div>
</div>


<div class="foquz-dialog__footer">
  <div class="foquz-dialog__actions">
    <button type="button"
            class="f-btn f-btn-link px-2"
            data-bind="
              click: function() {
                hide();
              }">
      Отменить
    </button>
    <button type="button"
            class="f-btn f-btn-success"
            data-bind="
              click: function() {
                submit()
              }">
      Добавить
    </button>
  </div>
</div>


</foquz-dialog>
