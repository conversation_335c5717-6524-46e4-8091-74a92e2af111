<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header>

    <!-- ko text: _t('Настроить столбцы') -->
    <!-- /ko -->
  </foquz-dialog-header>


  <div class="foquz-dialog__body">
    <div class="table-columns-dialog__column">
      <div>
        <search-field params="placeholder: _t('Поиск по наименованию столбца'), value: query"></search-field>
      </div>
      <div class="table-columns-dialog__wrapper">
        <div class="table-columns"
             data-bind="nativeScrollbar">
          <!-- ko if: groups.length -->
          <!-- ko foreach: { data: groups, as: 'group' } -->
          <columns-group params="name: group.name, columns: group.columns"></columns-group>
          <!-- /ko -->
          <!-- ko ifnot: filteredColumns().length -->
          <div class="text-center f-color-text f-fs-2 py-3"
               data-bind="text: _t('Столбец с указанным наименованием не найден')">

          </div>
          <!-- /ko -->
          <!-- /ko -->
          <!-- ko ifnot: groups.length -->
          <div class="table-columns__list">
            <!-- ko ifnot: filteredColumns().length -->
            <div class="text-center f-color-text f-fs-2 py-3"
                 data-bind="text: _t('Столбец с указанным наименованием не найден')">
            </div>
            <!-- /ko -->
            <!-- ko foreach: { data: filteredColumns, as: 'column'} -->
            <div class="table-column">
              <foquz-checkbox params="checked: column.checked">
                <!-- ko text: column.name -->
              </foquz-checkbox>
            </div>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>
      </div>
    </div>
  </div>



  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link"
              data-bind="
                click: function() {
                  $dialog.hide();
                }, text: _t('Отменить')"></button>
      <button type="button"
              class="f-btn f-btn-success"
              data-bind="
                click: function() {
                  apply();
                }, text: _t('Сохранить')"></button>
    </div>
  </div>

</foquz-dialog>
