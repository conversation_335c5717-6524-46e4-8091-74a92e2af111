import { ViewModel } from './model';
import html from './template.sidesheet.html';
import './style.sidesheet.less';

import './columns-group';

ko.components.register('table-columns-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('table-columns-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
