import { DialogWrapper } from "Dialogs/wrapper";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.query = ko.observable("");
    this.model = params.model;

    this.clientFields = params.clientFields || [];
    this.columns = this.model.columns.map((c) => {
      return {
        id: c.id,
        name: c.name,
        checked: ko.observable(false),
        isClientField: this.clientFields.indexOf(c.id) > -1,
      };
    });
    this.filteredColumns = ko.pureComputed(() => {
      let query = this.query().trim();
      if (!query) return this.columns;
      query = query.toLowerCase();
      return this.columns.filter((c) => {
        return c.name.toLowerCase().includes(query);
      });
    });

    this.groups = (params.groups || []).map((group) => {
      let columns = group.columns
        .map((cId) => this.columns.find((c) => c.id == cId))
        .filter(Boolean);
      let filteredColumns = ko.pureComputed(() => {
        let query = this.query().trim();
        if (!query) return columns;
        query = query.toLowerCase();
        return columns.filter((c) => {
          return c.name.toLowerCase().includes(query);
        });
      });
      return {
        name: group.name,
        columns: filteredColumns,
      };
    });

    this._updateState();
  }

  _updateState() {
    let state = this.model.getState();
    console.log({ state });
    this.columns.forEach((c) => {
      c.checked(state[c.id]);
    });
  }

  apply() {
    let state = {};
    this.columns.forEach((c) => {
      state[c.id] = c.checked();
    });

    this.model.update(state);
    this.hide();
  }
}
