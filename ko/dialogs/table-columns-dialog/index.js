import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './columns-group';

ko.components.register('table-columns-dialog', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('table-columns-dialog');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
