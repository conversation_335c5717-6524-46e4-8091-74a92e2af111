@import 'Style/breakpoints';

.table-columns-dialog {
  .table-columns {
    max-height: 252px;
    overflow: hidden;

    &__list {
      display: flex;
      flex-wrap: wrap;
      margin-top: 10px;
    }

    .table-column {
      width: 50%;
      min-height: 24px;
      margin-top: 16px;
    }

  }

  .native-scrollbar {
    .os-scrollbar.os-scrollbar-vertical {
      top: 24px;
    }
  }

  .only-mobile({
    .foquz-dialog__body {
      overflow: hidden;
    }

    .table-columns-dialog__column {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      max-height: 100%;
    }

    .table-columns-dialog__wrapper {
      flex-grow: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .table-columns {
      flex-grow: 1;

      max-height: 100%;
      overflow: hidden!important;
    }
  });
}
