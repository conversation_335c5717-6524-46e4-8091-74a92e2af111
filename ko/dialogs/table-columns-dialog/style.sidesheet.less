@import 'Style/breakpoints';
@import 'Style/colors';

.table-columns-sidesheet {
  .foquz-dialog__title {
    margin-bottom: 25px;

    .only-mobile({
      margin-bottom: 8px;
    })
  }
  .table-columns {
    overflow: hidden;

    &__list {
      margin-top: 10px;
      column-count: 3;
      vertical-align: top;

      .only-mobile({
        column-count: 1;
      });

      .foquz-checkbox {
        padding-top: 8px;
        padding-bottom: 8px;
      }
    }

    .table-column {
      min-height: 24px;
      width: 100%;
      break-inside: avoid;
    }

    .columns-group {
      padding-bottom: 18px;

      &:not(:last-child) {
        border-bottom: 1px solid @f-color-border;
      }

      &__header {
        display: flex;
        margin-bottom: 8px;

        .f-check-label {
          font-size: 19px;
          padding-top: 2px;
        }
      }

      .only-mobile({
        &__header {
          .f-check-label {
            font-size: 16px;
          }
        }
      });
    }
  }

  .table-columns-dialog__wrapper {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin-right: -30px;
    padding-bottom: 40px;

    .os-content {
      padding-right: 30px!important;
    }
  }

  .mobile-and-tablet({
    .table-columns-dialog__wrapper {
      margin-right: 0;

      .os-content {
        padding-right: 0px!important;
      }
    }
  });

  .only-mobile({
    .foquz-dialog__body {
      overflow: hidden;
    }

    .table-columns-dialog__column {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      max-height: 100%;
    }


    .table-columns {
      flex-grow: 1;

      max-height: 100%;
      overflow: hidden!important;

    }
  });


}
