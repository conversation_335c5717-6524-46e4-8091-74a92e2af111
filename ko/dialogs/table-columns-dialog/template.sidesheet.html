<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title" data-bind="text: _t('Настроить столбцы')">

      </h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll">
      <div class="container h-100">
        <div class="table-columns-dialog__column h-100 d-flex flex-column">
          <div class="border-bottom pb-30p">
            <search-field params="placeholder: _t('Поиск по наименованию столбца'), value: query"></search-field>
          </div>
          <div class="table-columns-dialog__wrapper">
            <div class="table-columns h-100"
                 data-bind="nativeScrollbar">
              <!-- ko if: groups.length -->
              <!-- ko foreach: { data: groups, as: 'group' } -->
              <columns-group params="name: group.name, columns: group.columns, toggle: true"></columns-group>
              <!-- /ko -->
              <!-- ko ifnot: filteredColumns().length -->
              <div class="text-center f-color-text f-fs-2 py-3" data-bind="text: _t('Столбец с указанным наименованием не найден')">

              </div>
              <!-- /ko -->
              <!-- /ko -->
              <!-- ko ifnot: groups.length -->
              <div class="table-columns__list">

                  <!-- ko ifnot: filteredColumns().length -->
                  <div class="text-center f-color-text f-fs-2 py-3" data-bind="text: _t('Столбец с указанным наименованием не найден')">

                  </div>
                  <!-- /ko -->
                  <!-- ko foreach: { data: filteredColumns, as: 'column'} -->
                  <div class="table-column">
                    <foquz-checkbox params="checked: column.checked">
                      <!-- ko text: column.name -->
                    </foquz-checkbox>
                  </div>
                  <!-- /ko -->

              </div>
              <!-- /ko -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn"
              data-bind="click: function() {
                $dialog.hide();
              }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        <!-- ko text: _t('Отменить') -->
        <!-- /ko -->
      </button>
      <button type="button"
              class="f-btn f-btn-success"
              data-bind="click: function() { apply()  }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        <!-- ko text: _t('Сохранить') -->
        <!-- /ko -->
      </button>
    </div>
  </div>

</sidesheet>
