<!-- ko if: columns().length -->
<div class="columns-group__header">
  <foquz-checkbox params="checked: hasChecked, partially: partiallyChecked,
    event: {
        change: function() {
          console.log('change', $data)
          toggle();
        }
    }">
    <!-- ko text: $parent.name -->
    <!-- /ko -->
  </foquz-checkbox>

  <!-- ko if: canToggle -->
  <button class="button-ghost ml-10p mt-5p"
          data-bind="click: function() {
    isOpen(!isOpen())
  }">
    <toggler-icon params="isOpen: isOpen"
                  class="f-color-service"></toggler-icon>
  </button>
  <!-- /ko -->
</div>

<!-- ko template: {
  foreach: templateIf(isOpen(), $data),
  afterAdd: slideAfterAddFactory(400),
  beforeRemove: slideBeforeRemoveFactory(400)
} -->
<div class="table-columns__list">
  <!-- ko foreach: { data: columns, as: 'column'} -->
  <div class="table-column">
    <foquz-checkbox params="checked: column.checked">
      <!-- ko text: column.name -->
      <!-- /ko -->

      <!-- ko if: column.isClientField -->
      <svg-icon  params="name: 'user'" style="color: #8EA6FF" class="ml-5p  client-field"></svg-icon>
      <!-- /ko -->
    </foquz-checkbox>
  </div>
  <!-- /ko -->
</div>
<!-- /ko -->

<!-- /ko -->
