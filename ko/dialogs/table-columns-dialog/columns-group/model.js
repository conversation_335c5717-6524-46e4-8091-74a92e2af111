import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.name = params.name;
    this.columns = params.columns;
    this.canToggle = params.toggle;
    this.isOpen = ko.observable(true);

    element.style.display = params.columns().length ? '' : 'none';
    this.subscriptions.push(this.columns.subscribe(v => {
      element.style.display = v.length ? '' : 'none';
    }));

    this.checkedColumns = ko.pureComputed(() => {
      return this.columns().filter(c => c.checked());
    });

    this.allChecked = ko.pureComputed(() => {
      return this.checkedColumns().length == this.columns().length;
    });



    this.hasChecked = ko.pureComputed(() => {
      return this.checkedColumns().length > 0;
    });

    this.partiallyChecked = ko.pureComputed(() => {
      return this.hasChecked() && !this.allChecked();
    })

    this.noChecked = ko.pureComputed(() => {
      return this.checkedColumns().length == 0
    });
  }

  toggle() {
    if (this.hasChecked()) {
      this.columns().forEach(c => c.checked(false));
    } else {
      this.columns().forEach(c => c.checked(true));
    }

  }
}
