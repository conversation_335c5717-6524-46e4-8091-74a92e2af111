@import "Style/breakpoints";
@import "Style/colors";

.stats-rating-sidesheet {
  .only-mobile({
    .rating-scale {
      transform: translateY(-75px);
      h3 {
        font-size: 16px!important;
      }
    }
    .foquz-search-field {
      transform: translateY(110px);
    }
  });
  .rating-value {
    width: 28px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 13px;
    border-width: 2px;
    border-style: solid;
    border-radius: 4px;
  }
}
