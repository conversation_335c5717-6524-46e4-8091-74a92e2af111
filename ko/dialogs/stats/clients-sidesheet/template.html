<stats-sidesheet class="clients-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  deleted: deleted,
                  question: question,
                  withPoints: withPoints">

    

  <!-- ko let: { $table: table, $modal: $data } -->

  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table class="table foq-table question-statistics__clients-modal-dialog-table m-0">
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <th>Филиал</th>

            <!-- ko if: $modal.hasOrder -->
            <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <!-- ko if: $modal.showSelectedVariants -->
            <th>Вариант оценки</th>
            <!-- /ko -->
          </tr>
        </thead>

        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)">
            </td>
            <td class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)">
            </td>
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <td data-bind="html: $modal.getText(filialName)"></td>

            <!-- ko if: $modal.hasOrder -->
            <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
              <a class="question-statistics__clients-modal-dialog-table-link"
                 href="#"
                 data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <!-- ko if: variantText -->
            <td data-bind="html: $modal.getText(variantText)"></td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table" data-bind="nativeScrollbar">
      <interactive-table params="table: $table, horizontal: true">
        <table class="table foq-table fixed-table">
          <tbody>
            <tr class="">
              <th>ФИО контакта</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(name)"></td>
              <!-- /ko -->
            </tr>
            <tr class="">
              <th>Телефон</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                  data-bind="html: $modal.getText(phone)">
              </td>
              <!-- /ko -->
            </tr>
            <tr class="">
              <th>Email</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-email-cell"
                  data-bind="html: $modal.getText(email)">
              </td>
              <!-- /ko -->
            </tr>
            <tr class="">
              <th>Пройден</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(passedAt)"></td>
              <!-- /ko -->
            </tr>
            <tr class="">
              <th>Филиал</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(filialName)"></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.hasOrder -->
            <tr class="">
              <th>№ заказа</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
                <a class="question-statistics__clients-modal-dialog-table-link"
                   href="#"
                   data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                </a>
              </td>
              <!-- /ko -->
            </tr>
            <tr class="">
              <th>Дата заказа</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
          </tbody>
        </table>
      </interactive-table>
    </div>
  </media-query>


  <!-- /ko -->
</stats-sidesheet>
