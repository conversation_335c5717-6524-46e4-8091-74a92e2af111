import { map as _map, reduce as _reduce } from 'lodash';

import { Client } from "../client";

export class MatrixClient extends Client {
  constructor(data, params) {
    super(data);

    this.answer = data.answer;
    this.selfVariantValue = data.selfAnswerValue || "";
    this.skipped = data.skipped;
    this.questionId = params.id;
    this.extraComments = _reduce(data.answer.extra || [], (acc, el, key) => {
      return {
        ...acc,
        ...({
          [key]: _reduce(el, (sAcc, sEl, sKey) => {
            let entry = sEl;
            if (sKey !== 'self_variant' && sKey !== 'answer') {
              if (window.IS_EXTERNAL) {
                entry = QUESTIONS.find(el => el.id === this.questionId || el.question_id === this.questionId).clarifyingQuestionVariants.find(el => el.id === +sEl);
              } else {
                entry = QUESTIONS.find(el => el.id === this.questionId).detail_answers.find(el => el.id === +sEl);
              }
              entry = entry.variant || entry.question;
            }
            return `${sAcc}${sAcc === '' ? '' : ', '}${entry}`;
          }, ''),
        }),
      };
    }, []);
  }
}
