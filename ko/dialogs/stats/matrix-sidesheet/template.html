<stats-sidesheet class="matrix-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints">

  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table class="table matrix-stats-table foq-table question-statistics__clients-modal-dialog-table">
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <!-- ko if: $modal.withPoints -->
            <th>Баллы</th>
            <!-- /ko -->
            <th>Пройден</th>
            <!-- ko if: $modal.question.clarifyingQuestion -->
            <th>
              Ответ на доп. вопрос
            </th>
            <!-- /ko -->
            <!-- ko if: $modal.hasOrder -->
            <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
              № заказа
            </th>
            <th>Дата заказа</th>

            <!-- /ko -->
            <!-- ko ifnot: $modal.question.clarifyingQuestion -->
            <th>Комментарий</th>
            <!-- /ko -->
            <!-- ko if: $modal.withPoints -->
            <th>Ответы</th>
            <!-- /ko -->
          </tr>
        </thead>

        <tbody>
          <!-- ko foreach: items -->
          <tr class="matrix-stats__client-data question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)">
            </td>
            <td class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)">
            </td>
            <!-- ko if: $modal.withPoints -->
            <td>
              <div class="mb-5p">
                <span class="bold"
                      data-bind="text: $data.points"></span>
                из
                <span class="bold"
                      data-bind="text: $modal.question.maxPoints"></span>,
                <span data-bind="text: $modal.getPercent($data.points, $modal.question.maxPoints) + '%'"></span>
              </div>
              <progress-line style="width: 100px"
                             params="progress: $modal.getPercent($data.points, $modal.question.maxPoints)">
              </progress-line>
            </td>
            <!-- /ko -->
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
              <a class="question-statistics__clients-modal-dialog-table-link"
                 href="#"
                 data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order) }">
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>

            <!-- /ko -->

            <td data-bind="html: $modal.getText(comment)"></td>
            <!-- ko if: $modal.withPoints -->
            <td>
              <!-- ko if: email -->
              <a class="d-flex align-items-center"
                 data-bind="attr: {
              href: $modal.getAnswersLink($data)
            }">
                <foquz-icon params="icon: 'message'"
                            class="f-icon-primary"></foquz-icon>
                <span class="f-color-text">Ответы</span>
              </a>
              <!-- /ko -->
            </td>
            <!-- /ko -->
          </tr>
          <!-- ko if: $modal.question.skipped -->
          <tr>
            <td class="matrix-stats__client-answer" colspan="5">
              <div class="py-2 matrix-answer--no-value">Пропуск оценки</div>
            </td>
          </tr>
          <!-- /ko -->
          <!-- ko ifnot: $modal.question.skipped -->
          <!-- ko foreach: $modal.question.matrix.rows -->
          <tr>
            <td class="matrix-stats__client-answer" colspan="4">
             <!-- ko if: $data.skipped -->
             <div class="py-2">Отказался от оценки</div>
             <!-- /ko -->
             <!-- ko ifnot: $data.skipped -->
              <div class="matrix-answer-values">
                <!-- ko let: {
                  donorAnswer: $modal.question.donorId ?
                    $parent.answer[$data == 'Свой вариант' ? -1 : $modal.question.matrix.donorRows[$index()]] :
                    null,
                } -->
                <!-- ko if: !$modal.question.donorId || donorAnswer -->
                <div class="matrix-answer-values__item">
                  <div class="f-color-service matrix-answer-value__name">
                    <!-- ko if: $data === $modal.question.selfVariantText -->
                    <span class="bold" data-bind="text: $data + ': ' + $parent.selfVariantValue"></span>
                    <!-- /ko -->
                    <!-- ko ifnot: $data === $modal.question.selfVariantText -->
                    <span data-bind="text: $data.replace('__foquz_dictionary_element', '')"></span>
                    <!-- /ko -->
                  </div>
                  <div
                    class="matrix-answer-value__value"
                    data-bind="
                      let: {
                        answerValue: $modal.question.donorId ? donorAnswer : $parent.answer[$data],
                      },
                    "
                  >
                    <!-- ko if: answerValue == 'Респондент отказался от оценки' -->
                    <span class="matrix-answer--no-value">
                      Пропуск ответа
                    </span>
                    <!-- /ko -->
                    <div class="matrix-answer-value__wrapper">
                      <!-- ko ifnot: answerValue == 'Респондент отказался от оценки' -->
                      <div class="matrix-answer-value__value-wrapper">
                        <!-- ko foreach: answerValue -->
                        <span class="bold" data-bind="text: $data"></span>
                        <!-- /ko -->
                      </div>
                      <!-- /ko -->
                      <!-- ko if: $modal.withPoints -->
                      <!-- ko if: $modal.question.correctVariants[$data].col == answerValue -->
                      <svg-icon params="name: 'check'" class="svg-icon--sm ml-2"></svg-icon>
                      <!-- /ko -->
                      <!-- /ko -->
                    </div>
                  </div>
                </div>
                <!-- /ko -->
                <!-- /ko -->
              </div>
              <!-- /ko -->
            </td>
            <!-- ko let: {
              commentId: $data === "Свой вариант" ? -1 : $data,
            } -->
            <!-- ko if: $parent.extraComments[commentId] -->
            <td data-bind="text: $parent.extraComments[commentId]"></td>
            <!-- /ko -->
            <!-- /ko -->
          </tr>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /ko -->
          <tr class="matrix-stats__client-data">
            <td colspan="100%"></td>
          </tr>
        </tbody>
      </table>

    </interactive-table>
  </media-query>


  <media-query params="query: 'mobile'">
    <interactive-table params="table: $table">
      <!-- ko foreach: $table.items -->
      <div class="mobile-item border-bottom">
        <div class="mobile-item__client">
          <table>
            <tbody>

              <tr>
                <th class="question-statistics__profiles-modal-dialog-table-name-cell">
                  ФИО контакта
                </th>
                <td data-bind="html: $modal.getText(name)"></td>
              </tr>
              <tr>
                <th>Телефон</th>
                <td class="question-statistics__profiles-modal-dialog-table-phone-cell"
                    data-bind="html: $modal.getText(phone)">
                </td>
              </tr>
              <tr>
                <th>Email</th>
                <td class="question-statistics__profiles-modal-dialog-table-email-cell"
                    data-bind="html: $modal.getText(email)">
                </td>
              </tr>
              <!-- ko if: $modal.withPoints -->
              <tr>
                <th>Баллы</th>
                <td>
                <td>
                  <div class="mb-5p">
                    <span class="bold"
                          data-bind="text: $data.points"></span>
                    из
                    <span class="bold"
                          data-bind="text: $modal.question.maxPoints"></span>,
                    <span data-bind="text: $modal.getPercent($data.points, $modal.question.maxPoints) + '%'"></span>
                  </div>
                  <progress-line style="width: 100px"
                                 params="progress: $modal.getPercent($data.points, $modal.question.maxPoints)">
                  </progress-line>
                </td>
                </td>
              </tr>
              <!-- /ko -->
              <tr>
                <th>Пройден</th>
                <td data-bind="html: $modal.getText(passedAt)"></td>
              </tr>
              <!-- ko if: $modal.question.clarifyingQuestion -->
              <tr>
                <th>
                  Ответ на доп. вопрос
                </th>
                <!-- ko foreach: items -->
                <td data-bind="html: $modal.getText(answer)"></td>
                <!-- /ko -->
              </tr>
              <!-- /ko -->
              <!-- ko if: $modal.hasOrder -->
              <tr>
                <th class="question-statistics__profiles-modal-dialog-table-order-id-head-cell">
                  № заказа
                </th>
                <td class="question-statistics__profiles-modal-dialog-table-order-id-cell">
                  <a class="question-statistics__profiles-modal-dialog-table-link"
                     href="#"
                     data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                  </a>
                </td>
              </tr>
              <tr>
                <th>Дата заказа</th>
                <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              </tr>
              <!-- /ko -->
              <!-- ko ifnot: $modal.question.clarifyingQuestion -->
              <tr>
                <th>Комментарий</th>
                <td data-bind="html: $modal.getText(comment)"></td>
              </tr>
              <!-- /ko -->
              <!-- ko if: $modal.withPoints -->
              <tr>
                <th>Ответы</th>

                <td>
                  <!-- ko if: email -->
                  <a class="d-flex align-items-center"
                     data-bind="attr: {
                      href: $modal.getAnswersLink($data)
                    }">
                    <foquz-icon params="icon: 'message'"
                                class="f-icon-primary"></foquz-icon>
                    <span class="f-color-text">Ответы</span>
                  </a>
                  <!-- /ko -->
                </td>
              </tr>
              <!-- /ko -->
            </tbody>
          </table>
        </div>
        <div class="mobile-item__answer">
          <!-- ko if: $data.skipped -->
          <div class="matrix-stats__client-answer py-2 matrix-answer--no-value">
            Пропуск оценки
          </div>
          <!-- /ko -->
          <!-- ko ifnot: $data.skipped -->
          <div class="matrix-answer-values">
            <!-- ko foreach: $modal.question.matrix.rows -->
            <!-- ko if: !$modal.question.donorId || $parent.answer[$modal.question.matrix.donorRows[$index()]] -->
            <div class="matrix-answer-values__item">
              <div
                class="f-color-service matrix-answer-value__name"
                data-bind="text: $data"
              >
                <!-- ko if: $data === $modal.question.selfVariantText -->
                <span class="bold"
                data-bind="text: $data + ': ' + $parent.selfVariantValue"></span>
                <!-- /ko -->
                <!-- ko ifnot: $data === $modal.question.selfVariantText -->
                <span data-bind="text: $data"></span>
                <!-- /ko -->
              </div>
              <div
                class="matrix-answer-value__value"
                data-bind="
                  let: {
                    answerValue: $modal.question.donorId ? $parent.answer[$modal.question.matrix.donorRows[$index()]] : $parent.answer[$data],
                  },
                "
              >
                <!-- ko if: answerValue == 'Респондент отказался от оценки' -->
                <span class="matrix-answer--no-value">
                  Пропуск ответа
                </span>
                <!-- /ko -->
                <!-- ko ifnot: answerValue == 'Респондент отказался от оценки' -->
                <span class="bold" data-bind="text: answerValue"></span>
                <!-- /ko -->
                <!-- ko if: $modal.withPoints -->
                <!-- ko if: $modal.question.correctVariants[$data].col == answerValue -->
                <svg-icon params="name: 'check'" class="svg-icon--sm ml-2"></svg-icon>
                <!-- /ko -->
                <!-- /ko -->
              </div>
            </div>
            <!-- /ko -->
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->
    </interactive-table>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
