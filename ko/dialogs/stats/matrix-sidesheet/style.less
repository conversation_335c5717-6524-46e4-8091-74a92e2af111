.stats-sidesheet.matrix-stats {
  .matrix-stats-table {
    .matrix-stats__client-data {
      td {
        border-top: 2px solid #E7EBED;
        border-bottom: none;
      }
    }
    .matrix-stats__client-answer {
      border-bottom-width: 2px;
      border-top: none;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        left: 10px;
        right: 10px;
        top: 0;
        border-bottom: 1px solid #E7EBED;
      }

      table {
        margin-bottom: 0;
      }
    }
  }
}

.matrix-answer-values {
  display: flex;
  flex-direction: column;
}

.matrix-answer-values__item {
  width: 100%;
  display: flex;
  padding-top: 8px;
  padding-bottom: 8px;

  &:last-child {
    border-bottom: unset;
  }
}

.matrix-answer-value__name {
  flex-grow: 1;
}

.matrix-answer-value__value {
  min-width: 200px;
  text-align: right;
}

.matrix-answer--no-value {
  font-size: 12px;
  font-weight: 400;
  line-height: 13px;
  color: #73808D;
}

.matrix-answer-value__wrapper {
  display: flex;
  justify-content: flex-end;

  .matrix-answer-value__value-wrapper {

  }
}
