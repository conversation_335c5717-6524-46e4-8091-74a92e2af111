<stats-sidesheet
  class="priority-stats"
  params="
    ref: modal,
    dialogWrapper: $component,
    title: title,
    question: question,
    withPoints: withPoints
  "
>
  <!-- ko let: { $table: table, $modal: $data } -->

  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__priority-modal-dialog-table mr-0"
        data-bind="let: { colsCount: 4 + ($modal.withPoints ? 2 : 0) + ($modal.hasOrder ? 2 : 0) + ($modal.question.commentEnabled ? 1 : 0) }"
      >
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <!-- ko if: $modal.withPoints -->
            <th>Баллы</th>
            <!-- /ko -->
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th
              class="question-statistics__priority-modal-dialog-table-order-id-head-cell"
            >
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <!-- ko if: $modal.withPoints -->
            <th>Ответы</th>
            <!-- /ko -->
            <!-- ko if: $modal.question.commentEnabled -->
            <th>Комментарий</th>
            <!-- /ko -->
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__priority-modal-dialog-table-row-data">
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__priority-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__priority-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)"
            ></td>
            <!-- ko if: $modal.withPoints -->
            <td>
              <div class="mb-5p">
                <span class="bold" data-bind="text: points"></span>
                из
                <span
                  class="bold"
                  data-bind="text: $modal.question.maxPoints"
                ></span
                >,
                <span
                  data-bind="text: $modal.getPercent(points, $modal.question.maxPoints) + '%'"
                ></span>
              </div>
              <progress-line
                style="width: 100px"
                params="progress: $modal.getPercent(points, $modal.question.maxPoints)"
              >
              </progress-line>
            </td>
            <!-- /ko -->
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td
              class="question-statistics__priority-modal-dialog-table-order-id-cell"
            >
              <a
                class="question-statistics__priority-modal-dialog-table-link"
                href="#"
                data-bind="html: '#' + orderId, attr: {href: '/foquz/foquz-poll/answer?id='+$parent.pollId+'&search[orderNumber]='+simpleOrderId'}"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <!-- ko if: $modal.withPoints -->
            <td>
              <!-- ko if: email -->
              <a
                class="d-flex align-items-center"
                data-bind="attr: {
                href: $modal.getAnswersLink($data)
                }"
              >
                <foquz-icon
                  params="icon: 'message'"
                  class="f-icon-primary"
                ></foquz-icon>
                <span class="f-color-text">Ответы</span>
              </a>
              <!-- /ko -->
            </td>
            <!-- /ko -->
            <!-- ko if: $modal.question.commentEnabled -->
            <td data-bind="html: $modal.getText(comment)"></td>
            <!-- /ko -->
          </tr>
          <tr
            class="question-statistics__priority-modal-dialog-table-row-answer"
          >
            <td data-bind="attr: { colspan: colsCount }">
              <div class="d-flex">
                <!-- ko if: $modal.withPoints -->
                <div class="pt-2 px-3">
                  <!-- ko if: $modal.question.correctOrder.toString() == answer.toString() -->
                  <svg-icon
                    params="name: 'check'"
                    class="svg-icon--sm"
                  ></svg-icon>
                  <!-- /ko -->
                </div>
                <!-- /ko -->
                <div
                  class="question-statistics__priority-modal-dialog-variants flex-grow-1"
                >
                  <!-- ko foreach: answer -->
                  <div
                    class="question-statistics__priority-modal-dialog-variant"
                    data-bind="style: {
                                        'background-color': $modal.getGradientPoint($index(), $parent.answer.length)
                                    }"
                  >
                    <span
                      class="question-statistics__priority-modal-dialog-variant-index"
                      data-bind="text: $index() + 1 + '.'"
                    ></span>

                    <!-- ko if: id == 'self' || id == -1 -->
                    <span
                      data-bind="text: `${$modal.question.selfVariantText}: ${$data.variant}`"
                    ></span>
                    <!-- /ko -->
                    <!-- ko ifnot: id == 'self' || id == -1 -->
                    <span
                      class="question-statistics__priority-modal-dialog-variant-text"
                      data-bind="text: $data.variant"
                    ></span>
                    <!-- /ko -->
                  </div>
                  <!-- /ko -->
                </div>
              </div>
            </td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <interactive-table params="table: $table">
      <!-- ko foreach: $table.items -->
      <div class="mobile-item border-bottom">
        <div class="mobile-item__client">
          <table>
            <tbody>
              <tr>
                <th
                  class="question-statistics__profiles-modal-dialog-table-name-cell"
                >
                ФИО контакта
                </th>
                <td data-bind="html: $modal.getText(name)"></td>
              </tr>
              <tr>
                <th>Телефон</th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-phone-cell"
                  data-bind="html: $modal.getText(phone)"
                ></td>
              </tr>
              <tr>
                <th>Email</th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-email-cell"
                  data-bind="html: $modal.getText(email)"
                ></td>
              </tr>
              <!-- ko if: $modal.withPoints -->
              <tr>
                <th>Баллы</th>
                <td>
                  <div class="mb-5p">
                    <span class="bold" data-bind="text: points"></span>
                    из
                    <span
                      class="bold"
                      data-bind="text: $modal.question.maxPoints"
                    ></span
                    >,
                    <span
                      data-bind="text: $modal.getPercent(points, $modal.question.maxPoints) + '%'"
                    ></span>
                  </div>
                  <progress-line
                    style="width: 100px"
                    params="progress: $modal.getPercent(points, $modal.question.maxPoints)"
                  >
                  </progress-line>
                </td>
              </tr>
              <!-- /ko -->
              <tr>
                <th>Пройден</th>
                <td data-bind="html: $modal.getText(passedAt)"></td>
              </tr>
              <!-- ko if: $modal.hasOrder -->
              <tr>
                <th
                  class="question-statistics__profiles-modal-dialog-table-order-id-head-cell"
                >
                  № заказа
                </th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-order-id-cell"
                >
                  <a
                    class="question-statistics__profiles-modal-dialog-table-link"
                    href="#"
                    data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }"
                  >
                  </a>
                </td>
              </tr>
              <tr>
                <th>Дата заказа</th>
                <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              </tr>
              <!-- /ko -->

              <!-- ko if: $modal.withPoints -->
              <tr>
                <th>Ответы</th>
                <td>
                  <!-- ko if: email -->
                  <a
                    class="d-flex align-items-center"
                    data-bind="attr: {
                      href: $modal.getAnswersLink($data)
                      }"
                  >
                    <foquz-icon
                      params="icon: 'message'"
                      class="f-icon-primary"
                    ></foquz-icon>
                    <span class="f-color-text">Ответы</span>
                  </a>
                  <!-- /ko -->
                </td>
              </tr>
              <!-- /ko -->

              <!-- ko if: $modal.question.commentEnabled -->
              <tr>
                <th>Комментарий</th>
                <!-- ko foreach: items -->
                <td data-bind="html: $modal.getText(comment)"></td>
                <!-- /ko -->
              </tr>
              <!-- /ko -->
            </tbody>
          </table>
        </div>
        <div class="mobile-item__answer">
          <div class="d-flex">
            <!-- ko if: $modal.withPoints -->
            <div class="pt-2 px-3">
              <!-- ko if: $modal.question.correctOrder.toString() == answer.toString() -->
              <svg-icon params="name: 'check'" class="svg-icon--sm"></svg-icon>
              <!-- /ko -->
            </div>
            <!-- /ko -->
            <div
              class="question-statistics__priority-modal-dialog-variants flex-grow-1"
            >
              <!-- ko foreach: answer -->
              <div
                class="question-statistics__priority-modal-dialog-variant"
                data-bind="style: {
                                    'background-color': $modal.getGradientPoint($index(), $parent.answer.length)
                                }"
              >
                <span
                  class="question-statistics__priority-modal-dialog-variant-index"
                  data-bind="text: $index() + 1 + '.'"
                ></span>

                <!-- ko if: id == 'self' || id == -1 -->
                <span
                data-bind="text: $modal.question.selfVariantText + ': '"
              ></span>
              <!-- /ko -->

                <span
                  class="question-statistics__priority-modal-dialog-variant-text"
                  data-bind="text: $data.variant"
                ></span>
              </div>
              <!-- /ko -->
            </div>
          </div>
        </div>
      </div>
      <!-- /ko -->
    </interactive-table>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
