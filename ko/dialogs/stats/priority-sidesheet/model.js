import { StatsDialog } from '../dialog';
import { Table } from './table';

export class ViewModel extends StatsDialog {
  createTable() {
    return new Table();
  }


  getGradientPoint(point, count) {
    const start = [236, 239, 241];
    const finish = [249, 250, 250];

    let x = start[0],
      y = start[1],
      z = start[2];

    count = count - 1;
    if (point === count) {
      x = finish[0];
      y = finish[1];
      z = finish[2];
    } else if (point > 0) {
      const k = point / count / ((count - point) / point);
      x = (start[0] + k * finish[0]) / (1 + k);
      y = (start[1] + k * finish[1]) / (1 + k);
      z = (start[2] + k * finish[2]) / (1 + k);
    }

    return `rgb(${x}, ${y}, ${z})`;
  }
}
