import { StatsDialog } from '../dialog';
import { Table } from './table';

export class ViewModel extends StatsDialog {
  constructor(params, element) {
    super(params, element);
    this.starsCount = this.question.starsCount;

    this.ratingItems = Array(this.starsCount)
      .fill()
      .map((_, i) => {
        let checked = ko.observable(false);
        checked.subscribe((v) => {
          this.table.setParams(this.getParams());
          this.table.reset();
        });
        return {
          rating: i + 1,
          checked
        };
      });
  }

  getParams() {
    return {
      ...super.getParams(),
      rating: this.ratingItems.filter((i) => i.checked()).map((i) => i.rating)
    };
  }

  createTable() {
    return new Table();
  }
}
