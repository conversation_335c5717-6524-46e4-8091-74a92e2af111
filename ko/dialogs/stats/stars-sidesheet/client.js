import { Client } from '../client';

export class StarsClient extends Client {
  constructor(data) {
    super(data);

    this.rating = data.rating;
    this.skipped = data.skipped;

    this.answer = '';
    if (data.answer) {
      if (typeof data.answer == 'string') this.answer = data.answer;
      if (Array.isArray(data.answer)) {
        this.answer = data.answer.join(', ');
      }
    }
  }
}
