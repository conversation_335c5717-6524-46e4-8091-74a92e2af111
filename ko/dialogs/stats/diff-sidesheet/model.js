import { StatsDialog } from '../dialog';
import { Table } from './table';

export class ViewModel extends StatsDialog {

  createTable() {
    return new Table();
  }

  getRow(rowId) {
    return this.question.diffRows.find(row => row.id == rowId)
  }

  getRowStartLabel(rowId) {
    let row = this.getRow(rowId);
    if (row) return row.start_label;
  }
  getRowEndLabel(rowId) {
    let row = this.getRow(rowId);
    if (row) return row.end_label;
  }
}
