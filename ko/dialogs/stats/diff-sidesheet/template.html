<stats-sidesheet
  class="diff-stats"
  class="diff-stats"
  params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints"
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__clients-modal-dialog-table mr-0"
      >
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th
              class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
            >
              № заказа
            </th>
            <th>Дата заказа</th>

            <!-- /ko -->

            <th>Комментарий</th>
          </tr>
        </thead>

        <tbody>
          <!-- ko foreach: items -->
          <tr
            class="question-statistics__clients-modal-dialog-table-row diff-stats-client"
          >
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__clients-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__clients-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)"
            ></td>
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td
              class="question-statistics__clients-modal-dialog-table-order-id-cell"
            >
              <a
                class="question-statistics__clients-modal-dialog-table-link"
                href="#"
                data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order)}"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>

            <!-- /ko -->

            <td data-bind="html: comment"></td>
          </tr>

          <tr>
            <td
              class="px-0 diff-stats-answer"
              style=""
              data-bind="attr: {
            colspan: $modal.hasOrder ? 7 : 5
          }"
            >
              <!-- ko if: skipped -->
              <div class="py-2 text-center">Отказался от оценки</div>
              <!-- /ko -->

              <!-- ko ifnot: skipped -->

              <table
                class="question-statistics__clients-modal-dialog-inner-table"
              >
                <tbody>
                  <!-- ko foreach: Object.keys(answer) -->
                  <tr>
                    <td
                      align="right"
                      style="min-width: 60px"
                      width="50%"
                      class="f-color-service"
                      data-bind="text: $modal.getRowStartLabel($data)"
                    ></td>
                    <td
                      class="bold"
                      align="center"
                      style="width: 10px"
                      data-bind="text: $parent.answer[$data]"
                    ></td>
                    <td
                      style="min-width: 60px"
                      width="50%"
                      class="f-color-service"
                      data-bind="text: $modal.getRowEndLabel($data)"
                    ></td>
                  </tr>
                  <!-- /ko -->
                </tbody>
              </table>

              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <interactive-table params="table: $table">
      <!-- ko foreach: $table.items -->
      <div class="mobile-item border-bottom">
        <div class="mobile-item__client">
          <table>
            <tbody>
              <tr>
                <th
                  class="question-statistics__profiles-modal-dialog-table-name-cell"
                >
                ФИО контакта
                </th>
                <td data-bind="html: $modal.getText(name)"></td>
              </tr>
              <tr>
                <th>Телефон</th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-phone-cell"
                  data-bind="html: $modal.getText(phone)"
                ></td>
              </tr>
              <tr>
                <th>Email</th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-email-cell"
                  data-bind="html: $modal.getText(email)"
                ></td>
              </tr>
              <tr>
                <th>Пройден</th>
                <td data-bind="html: $modal.getText(passedAt)"></td>
              </tr>
              <!-- ko if: $modal.hasOrder -->
              <tr>
                <th
                  class="question-statistics__profiles-modal-dialog-table-order-id-head-cell"
                >
                  № заказа
                </th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-order-id-cell"
                >
                  <a
                    class="question-statistics__profiles-modal-dialog-table-link"
                    href="#"
                    data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }"
                  >
                  </a>
                </td>
              </tr>
              <tr>
                <th>Дата заказа</th>
                <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              </tr>
              <!-- /ko -->
              <tr>
                <th>Комментарий</th>
                <td data-bind="html: comment"></td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="mobile-item__answer">
          <!-- ko if: skipped -->
          <div class="py-2 text-center">Отказался от оценки</div>
          <!-- /ko -->

          <!-- ko ifnot: skipped -->

          <table class="question-statistics__clients-modal-dialog-inner-table">
            <tbody>
              <!-- ko foreach: Object.keys(answer) -->
              <tr>
                <td
                  align="right"
                  style="min-width: 60px; max-width: 320px"
                  class="f-color-service"
                  data-bind="text: $modal.getRowStartLabel($data)"
                ></td>
                <td
                  class="bold px-2"
                  align="center"
                  style="width: 20px"
                  data-bind="text: $parent.answer[$data]"
                ></td>
                <td
                  style="min-width: 60px; max-width: 320px"
                  class="f-color-service"
                  data-bind="text: $modal.getRowEndLabel($data)"
                ></td>
              </tr>
              <!-- /ko -->
            </tbody>
          </table>
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->
    </interactive-table>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
