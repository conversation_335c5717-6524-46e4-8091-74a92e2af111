<stats-sidesheet
  class="smiles-stats"
  params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints"
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__clients-modal-dialog-table"
      >
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th
              class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
            >
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <th width="30">Оценка</th>
            <th>
              <!-- ko if: $modal.question.clarifyingQuestion -->
              Ответ на доп. вопрос
              <!-- /ko -->
              <!-- ko ifnot: $modal.question.clarifyingQuestion -->
              Комментарий
              <!-- /ko -->
            </th>
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__clients-modal-dialog-table-phone-cell"
              data-bind="html:  $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__clients-modal-dialog-table-email-cell"
              data-bind="html:  $modal.getText(email)"
            ></td>
            <td data-bind="html:  $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td
              class="question-statistics__clients-modal-dialog-table-order-id-cell"
            >
              <a
                class="question-statistics__clients-modal-dialog-table-link"
                href="#"
                data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order) }"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <td align="center">
              <!-- ko if: $data.skipped -->
              Отказался от оценки
              <!-- /ko -->
              <!-- ko ifnot: $data.skipped -->
              <div class="img-rating img-rating--xs d-inline-flex">
                <div class="img-rating__icon m-auto">
                  <img data-bind="attr: { src: smile.smile_url }" />
                </div>
              </div>
              <!-- /ko -->
            </td>
            <td>
              <!-- ko if: $modal.question.clarifyingQuestion && Array.isArray($data.selectedIds) -->
                <!-- ko foreach: $data.selectedIds -->
                  <div class="d-flex align-items-center mb-5p">
                    <!-- ko if: !!$data.file_url -->
                      <file-loader-preview
                        class="file-loader-preview file-loader-preview_review mr-15p"
                        style="width: 35px; height: 35px"
                        params="
                            loading: false,
                            file: $data.file_url,
                            disabled: true,
                            preview: $data.file_url,
                        "
                      ></file-loader-preview>
                    <!-- /ko -->
                    <!-- ko if: 'selfVariant' in $data -->
                      <!-- ko text: $modal.question.selfVariantText || 'Свой вариант' -->
                      <!-- /ko -->:
                    <!-- /ko -->
                    <!-- ko text: $data.question || $data.selfVariant -->
                    <!-- /ko -->
                  </div>
                <!-- /ko -->
              <!-- /ko -->
              <!-- ko if: $data.selectedIds?.comment -->
                <!-- ko text: $data.selectedIds?.comment -->
                <!-- /ko -->
              <!-- /ko -->
              <!-- ko ifnot: $modal.question.clarifyingQuestion -->
                <!-- ko html: $modal.getText(comment) -->
                <!-- /ko -->
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table" data-bind="nativeScrollbar">
      <interactive-table params="table: $table, horizontal: true">
        <table class="table foq-table fixed-table">
          <tbody>
            <tr>
              <th>ФИО контакта</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(name)"></td>

              <!-- /ko -->
            </tr>
            <tr>
              <th>Телефон</th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html:  $modal.getText(phone)"
              ></td>

              <!-- /ko -->
            </tr>
            <tr>
              <th>Email</th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html:  $modal.getText(email)"
              ></td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Пройден</th>
              <!-- ko foreach: items -->
              <td data-bind="html:  $modal.getText(passedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.hasOrder -->
            <tr>
              <th
                class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
              >
                № заказа
              </th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__clients-modal-dialog-table-order-id-cell"
              >
                <a
                  class="question-statistics__clients-modal-dialog-table-link"
                  href="#"
                  data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order) }"
                >
                </a>
              </td>

              <!-- /ko -->
            </tr>
            <tr>
              <th>Дата заказа</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th width="30">Оценка</th>
              <!-- ko foreach: items -->
              <td align="center">
                <!-- ko if: $data.skipped -->
                Отказался от оценки
                <!-- /ko -->
                <!-- ko ifnot: $data.skipped -->
                <div class="img-rating img-rating--xs d-inline-flex">
                  <div class="img-rating__icon m-auto">
                    <img data-bind="attr: { src: smile.smile_url }" />
                  </div>
                </div>
                <!-- /ko -->
              </td>

              <!-- /ko -->
            </tr>
            <tr>
              <th>
              <!-- ko if: $modal.question.clarifyingQuestion -->
              Ответ на доп. вопрос
              <!-- /ko -->
              <!-- ko ifnot: $modal.question.clarifyingQuestion -->
              Комментарий
              <!-- /ko -->
              </th>
              <!-- ko foreach: items -->
              <td>  
                <!-- ko if: $modal.question.clarifyingQuestion && Array.isArray($data.selectedIds) -->
                  <!-- ko foreach: $data.selectedIds -->
                    <div class="d-flex align-items-center mb-5p">
                      <!-- ko if: !!$data.file_url -->
                        <file-loader-preview
                          class="file-loader-preview file-loader-preview_review mr-15p"
                          style="width: 35px; height: 35px"
                          params="
                              loading: false,
                              file: $data.file_url,
                              disabled: true,
                              preview: $data.file_url,
                          "
                        ></file-loader-preview>
                      <!-- /ko -->
                      <!-- ko if: 'selfVariant' in $data -->
                        <!-- ko text: $modal.question.selfVariantText || 'Свой вариант' -->
                        <!-- /ko -->:
                      <!-- /ko -->
                      <!-- ko text: $data.question || $data.selfVariant -->
                      <!-- /ko -->
                    </div>
                  <!-- /ko -->
                <!-- /ko -->
                <!-- ko if: $data.selectedIds?.comment -->
                  <!-- ko text: $data.selectedIds?.comment -->
                  <!-- /ko -->
                <!-- /ko -->
                <!-- ko ifnot: $modal.question.clarifyingQuestion -->
                  <!-- ko html: $modal.getText(comment) -->
                  <!-- /ko -->
                <!-- /ko -->
              </td>
              <!-- /ko -->
            </tr>
          </tbody>
        </table>
      </interactive-table>
    </div>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
