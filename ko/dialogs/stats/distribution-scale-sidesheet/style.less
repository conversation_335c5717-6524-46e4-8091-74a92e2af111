.scale-stats {
  .question-statistics__clients-modal-dialog-table td {
    border-bottom: none;
  }

  .scale-scale_mobile {
    .foquz-slider__slider {
      min-width: 217px;
    }
  }

  @media screen and (max-width: 1199px) {
    .complex-table table {
      table-layout: auto;
    }

    .foquz-slider__slider {
      min-width: 100px;
    }

    .scale-scale {
      flex-grow: 1;

      .foquz-slider {
        flex-grow: 1;
      }
    }

    .scale-scale__item {
      display: flex;
      align-items: center;
    }

    .scale-variant_mobile {
      display: flex;
      align-items: center;
      padding: 6px 0;

      .scale-variant__value {
        flex-grow: 1;
      }

      & + .scale-variant_mobile {
        border-top: 1px solid #E7EBED;
      }
    }
  }

  .scale-variant__name {
    color: #73808D;
  }


  .ui-state-disabled {
    opacity: 1;
  }

  .ui-slider {
    background-color: #D9D9D9;

    .ui-slider-range {
      background-color: #3F65F1;

    }
  }

  .deleted {
    color: var(--f-color-service);

    &:after {
      content: "(удален)";
      color: var(--f-color-danger);
      font-style: italic;
      margin-left: 4px;
    }
  }
}

.scale-stats__rating-td {
  max-width: 135px;

  .foquz-slider__slider {
    min-width: unset;
    width: 100px;
  }

  .scale-scale__item {
    display: flex;
    align-items: center;

    >:first-child {
      font-size: 14px;
      font-weight: 700;
      line-height: 15px;
    }
  }
}
