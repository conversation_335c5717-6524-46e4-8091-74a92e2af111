import { Client } from '../client';

export class DistributionScaleClient extends Client {
  constructor(data) {
    super(data);

    this.rating = data.rating;
    this.end = data.end;
    this.start = data.start;

    const answer = (Array.isArray(data.answer) ? data.answer : []);
    
    this.total = 0;

    this.variants = answer.map((v) => {
      const value = parseInt(v.value)
      this.total += value;
      return {
        name: v.id === -1 ? `${v.name}: ${data.selfAnswerValue}` : v.name,
        rating: value,
        deleted: v.is_deleted,
        end: data.end,
        start: data.start
      };
    });

    this.skipped = data.skipped || !answer.some(v => (v.value && v.value !== '0'));
  }
}
