<stats-sidesheet
        class="scale-stats"
        params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints"
>
    <!-- ko let: { $table: table, $modal: $data } -->
    <media-query params="query: 'tablet+'">
        <interactive-table params="table: $table, fixedHeader: true">
            <table
                    class="table foq-table question-statistics__clients-modal-dialog-table"
                    data-bind="log"
            >
                <thead>
                <tr>
                    <th>ФИО контакта</th>
                    <th>Телефон</th>
                    <th>Email</th>
                    <th style="max-width: 40px">Пройден</th>
                    <th>Филиал</th>
                    <!-- ko if: $modal.hasOrder -->
                    <th
                            class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
                    >
                        № заказа
                    </th>
                    <th>Дата заказа</th>
                    <!-- /ko -->

                    <th>Комментарий</th>
                </tr>
                </thead>
                <tbody>
                <!-- ko foreach: items -->
                <!-- ko if: $data.passedAt --><!-- ToDo << убрать после исправлений по постраничной загрузке-->
                <tr class="question-statistics__clients-modal-dialog-table-row">
                    <td data-bind="html: $modal.getText(name)"></td>
                    <td
                            class="question-statistics__clients-modal-dialog-table-phone-cell"
                            data-bind="html: $modal.getText(phone)"
                    ></td>
                    <td
                            class="question-statistics__clients-modal-dialog-table-email-cell"
                            data-bind="html: $modal.getText(email)"
                    ></td>
                    <td data-bind="html: $modal.getText(passedAt)" style="max-width: 40px"></td>
                    <td data-bind="html: $modal.getText(filialName)"></td>
                    <!-- ko if: $modal.hasOrder -->
                    <td
                            class="question-statistics__clients-modal-dialog-table-order-id-cell"
                    >
                        <a
                                class="question-statistics__clients-modal-dialog-table-link"
                                href="#"
                                data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order) }"
                        >
                        </a>
                    </td>
                    <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
                    <!-- /ko -->

                    <td data-bind="html: $modal.getText(comment)"></td>
                </tr>
                <!-- ko if: $data.skipped -->
                    <tr>
                        <td colspan="2" class="p-0">
                            <div class="py-10p px-15p skipped">
                                Отказался от оценки
                            </div>
                        </td>
                    </tr>
                <!-- /ko -->
                <!-- ko ifnot: $data.skipped -->
                    <tr>
                        <td
                            class="p-0"
                            data-bind="
                                attr: {
                                    colspan: 4,
                                },
                            "
                        >
                            <div class="scale-variants-item">
                                <div class="scale-variants-item__text">
                                    <div class="scale-variant px-15p">
                                        <div
                                            class="scale-variant__name"
                                            data-bind="
                                                text: $modal.question.questionData.self_variant_text,
                                            "
                                        ></div>
                                    </div>
                                </div>
                                <div class="nowrap">
                                    <span data-bind="text: total"></span>
                                    /
                                    <span data-bind="text: $modal.question.scaleMax"></span>
                                </div>
                            </div>
                        </td>
                        <!-- ko if: $table.screenSize().width >= 1200 -->
                        <td colspan="2" class="p-0"></td>
                        <!-- /ko -->
                    </tr>
                    <!-- ko foreach: { data: variants, as: 'field' } -->
                        <tr>
                            <td
                                class="p-0"
                                data-bind="
                                    attr: {
                                        colspan: 4,
                                    },
                                "
                            >
                                <div class="scale-variants-item">
                                    <div class="scale-variants-item__text">
                                        <div class="scale-variant px-15p">
                                            <div
                                                class="scale-variant__name"
                                                data-bind="
                                                    text: field.name,
                                                    css: {
                                                        deleted: field.deleted
                                                    },
                                                "
                                            ></div>
                                        </div>
                                    </div>
                                    <div class="scale-variants-item__value">
                                        <!-- ko if: isNaN(field.rating) -->
                                        <div class="py-10p px-15p skipped">Пропуск оценки</div>
                                        <!-- /ko -->
                                        <!-- ko ifnot: isNaN(field.rating) -->
                                        <span data-bind="text: field.rating"></span>
                                        <!-- ko if: field.rating > -1 -->
                                        <foquz-slider
                                            params="
                                                value: parseInt(field.rating),
                                                min: start,
                                                max: end,
                                                withIndicator: false,
                                                withHandle: false,
                                                disabled: true,
                                            "
                                        ></foquz-slider>
                                        <!-- /ko -->
                                        <!-- /ko -->
                                    </div>
                                </div>
                            </td>
<!--                            &lt;!&ndash; ko if: $table.screenSize().width >= 1200 &ndash;&gt;-->
<!--                            <td colspan="2" class="p-0"></td>-->
<!--                            &lt;!&ndash; /ko &ndash;&gt;-->
                        </tr>
                    <!-- /ko -->
                <!-- /ko -->
                <!-- /ko -->
                <!-- /ko -->
                </tbody>
            </table>
        </interactive-table>
    </media-query>

    <media-query params="query: 'mobile'">
        <div class="mobile-table" data-bind="nativeScrollbar">
            <interactive-table params="table: $table">
                <table class="table foq-table fixed-table">
                    <!-- ko foreach: items -->
                    <tbody>
                    <tr>
                        <th>ФИО контакта</th>
                        <td data-bind="html: $modal.getText(name)"></td>
                    </tr>
                    <tr>
                        <th>Телефон</th>
                        <td
                                class="question-statistics__clients-modal-dialog-table-phone-cell"
                                data-bind="html: $modal.getText(phone)"
                        ></td>
                    </tr>

                    <tr>
                        <th>Email</th>

                        <td
                                class="question-statistics__clients-modal-dialog-table-email-cell"
                                data-bind="html: $modal.getText(email)"
                        ></td>
                    </tr>

                    <tr>
                        <th>Пройден</th>

                        <td data-bind="html: $modal.getText(passedAt)"></td>
                    </tr>
                    <tr>
                        <th>Филиал</th>

                        <td data-bind="html: $modal.getText(filialName)"></td>
                    </tr>


                    <!-- ko if: $modal.hasOrder -->
                    <tr>
                        <th
                                class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
                        >
                            № заказа
                        </th>

                        <td
                                class="question-statistics__clients-modal-dialog-table-order-id-cell"
                        >
                            <a
                                    class="question-statistics__clients-modal-dialog-table-link"
                                    href="#"
                                    data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order) }"
                            >
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>Дата заказа</th>

                        <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
                    </tr>
                    <!--/ko -->

                    <tr>
                        <th>Комментарий</th>

                        <td style="color:#73808D;" data-bind="html: $modal.getText(comment)"></td>
                    </tr>

                    <tr>
                        <td colspan="2" class="py-0">
                            <!-- ko if: $data.skipped -->
                            <div class="py-10p skipped">Респондент отказался от оценки</div>
                            <!-- /ko -->

                            <!-- ko ifnot: $data.skipped -->
                            <div class="scale-variants-list">
                                <div class="scale-variant_mobile">
                                    <div
                                        class="scale-variant__name"
                                        data-bind="text: $modal.question.questionData.self_variant_text"
                                    ></div>
                                    <div class="nowrap">
                                        <span data-bind="text: total"></span>
                                        /
                                        <span data-bind="text: $modal.question.scaleMax"></span>
                                    </div>
                                </div>
                                <!-- ko foreach: { data: variants, as: 'field' } -->
                                <div class="scale-variant_mobile">
                                    <div
                                        class="scale-variant__name"
                                        data-bind="
                                            text: field.name,
                                            css: {
                                                deleted: field.deleted
                                            }
                                        "
                                    ></div>
                                    <div class="scale-variant__value">
                                        <!-- ko if: field.rating > -1 -->
                                        <foquz-slider
                                                params="value: parseInt(field.rating), min: start, max: end, withIndicator: false, withHandle: false, disabled: true"></foquz-slider>
                                        <!-- /ko -->
                                        <!-- ko ifnot: field.rating > -1 -->
                                        -
                                        <!-- /ko -->
                                    </div>
                                </div>
                                <!-- /ko -->
                            </div>
                            <!-- /ko -->
                        </td>
                    </tr>

                    </tbody>
                    <!-- /ko -->
                </table>
            </interactive-table>
        </div>
    </media-query>
    <!-- /ko -->
</stats-sidesheet>
