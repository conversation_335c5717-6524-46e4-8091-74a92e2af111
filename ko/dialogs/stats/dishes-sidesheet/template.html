<stats-sidesheet class="dishes-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints">

  <!-- ko let: { $table: table, $modal: $data } -->
  <interactive-table params="table: table, fixedHeader: true">
    <table class="table foq-table question-statistics__clients-modal-dialog-table m-0">
      <thead>
        <tr>
          <th>ФИО контакта</th>
          <th>Телефон</th>
          <th>Email</th>
          <th>Пройден</th>

          <!-- ko if: $modal.hasOrder -->
          <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
            № заказа
          </th>
          <th>Дата заказа</th>
          <!-- /ko -->
        </tr>
      </thead>

      <tbody>
        <!-- ko foreach: items -->
        <tr class="question-statistics__clients-modal-dialog-table-row">
          <td data-bind="html: $modal.getText(name)"></td>
          <td class="question-statistics__clients-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)">
          </td>
          <td class="question-statistics__clients-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)">
          </td>
          <td data-bind="html: $modal.getText(passedAt)"></td>

          <!-- ko if: $modal.hasOrder -->
          <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
            <a class="question-statistics__clients-modal-dialog-table-link"
               href="#"
               data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
            </a>
          </td>
          <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
          <!-- /ko -->
        </tr>
        <!-- /ko -->
      </tbody>
    </table>
  </interactive-table>
  <!-- /ko -->
</stats-sidesheet>
