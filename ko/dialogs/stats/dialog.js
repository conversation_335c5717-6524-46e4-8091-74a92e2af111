import { DialogWrapper } from 'Dialogs/wrapper';
import { toPercent } from 'Utils/number/to-percent';

export class StatsDialog extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.title = params.title;
    this.withPoints = params.withPoints;

    this.poll = params.poll;
    this.hasOrder = this.poll.hasOrder;

    this.deleted = params.deleted == 1;

    this.searchQuery = ko.observable('');
    this.searchPoints = ko.observable([]);

    this.question = params.question;
    this.showSelectedVariants = params.showSelectedVariants;

    this._urlParams = params.urlParams || {};
    this._searchParams = params.searchParams || {};

    this.table = this.createTable(params);

    this.on('search.query', (query) => {
      this.searchQuery(query);

      this.table.setParams(this.getParams());
      this.table.reset();
    });

    this.on('search.points', (points) => {
      this.searchPoints(points);

      this.table.setParams(this.getParams());
      this.table.reset();
    });
  }

  get questionId() {
    return this.question.question_id;
  }

  onModalInit(...params) {
    super.onModalInit(...params);
    this.table.setParams(this.getParams());
    this.table.load();
  }

  getParams() {
    return {
      id: this.questionId,
      ...this._urlParams,
      ...this._searchParams,
      ...this.modal().getParams()
    };
  }

  getPercent(value, total) {
    return toPercent(value, total);
  }

  getOrderLink(orderId) {
    return (
      '/foquz/foquz-poll/answer?id=' +
      this.poll.id +
      '&search[orderNumber]=' +
      orderId
    );
  }

  getAnswersLink(client) {
    if (!client.email) return `/foquz/foquz-poll/answer?id=${this.poll.id}`;
    return `/foquz/foquz-poll/answer?id=${this.poll.id}&search%5BclientEmail%5D=${client.email}`;
  }

  getText(str) {
    if (!str) return '';

    str = str.toString();

    let q = this.searchQuery();
    if (!q) return str;

    const reg = new RegExp(q, 'i');

    return str.replace(
      reg,
      '<span class="question-statistics__comments-modal-dialog-table-highlight">$&</span>'
    );
  }
}
