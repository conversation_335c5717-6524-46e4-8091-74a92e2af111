import { StatsDialog } from '../dialog';
import { Table } from './table';

export class ViewModel extends StatsDialog {

  createTable() {
    return new Table();
  }

  getVariants(ids) {
    let type = this.question.chooseType;
    const _ids = ids || [];

    let variants = this.question.chooseMedia.map((m, i)=> {
      return {
        ...m,
        name: (type == 'image' ? 'Изображение ' : 'Видео ') + (i + 1),
      }
    }).filter(m => {

      return _ids.includes('' + m.id);
    });
    return variants;
  }
}
