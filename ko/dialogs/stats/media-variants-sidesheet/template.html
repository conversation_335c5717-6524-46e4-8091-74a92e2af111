<stats-sidesheet
  class="media-variants-stats"
  params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints"
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__priority-modal-dialog-table mr-0"
      >
        <thead>
          <tr>
            <th>Клиент</th>
            <th>Телефон</th>
            <th>Email</th>
            <!-- ko if: $modal.withPoints -->
            <th>Баллы</th>
            <!-- /ko -->
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th
              class="question-statistics__priority-modal-dialog-table-order-id-head-cell"
            >
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <th>Вариант</th>
            <th>Комментарий</th>
            <!-- ko if: $modal.withPoints -->
            <th>Ответы</th>
            <!-- /ko -->
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__priority-modal-dialog-table-row-data">
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__priority-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__priority-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)"
            ></td>
            <!-- ko if: $modal.withPoints -->
            <td>
              <div class="mb-5p">
                <span class="bold" data-bind="text: $data.points"></span>
                из
                <span
                  class="bold"
                  data-bind="text: $modal.question.maxPoints"
                ></span
                >,
                <span
                  data-bind="text: $modal.getPercent($data.points, $modal.question.maxPoints) + '%'"
                ></span>
              </div>
              <progress-line
                style="width: 100px"
                params="progress: $modal.getPercent($data.points, $modal.question.maxPoints)"
              >
              </progress-line>
            </td>
            <!-- /ko -->
            <td data-bind="html: passedAt"></td>
            <!-- ko if: $modal.hasOrder -->
            <td
              class="question-statistics__priority-modal-dialog-table-order-id-cell"
            >
              <a
                class="question-statistics__priority-modal-dialog-table-link"
                href="#"
                data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order)}"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <td data-bind="let: { variants: $modal.getVariants($data.ids)}">
              <!-- ko if: skipped -->
              Отказался от ответа
              <!-- /ko -->
              <!-- ko ifnot: skipped -->
              <!-- ko foreach: variants -->
              <a
                style="white-space: nowrap"
                href="javascript:void(0)"
                data-bind="text: name + ($index() == variants.length - 1 ? '' : ', '), fancyboxGalleryItem: {
                                    index: $index(),
                                    gallery: variants.map(function(v) { return {
                                        src: v.url,
                                        opts: { caption: v.description || '' }
                                   } })
                                }"
              ></a>
              <!-- ko if: $modal.question.correctVariantsIds.includes($data.id) -->
              <svg-icon
                params="name: 'check'"
                class="svg-icon--sm ml-1"
              ></svg-icon>
              <!-- /ko -->
              <!-- /ko -->
              <!-- /ko -->
            </td>
            <td data-bind="html: $modal.getText(comment)"></td>
            <!-- ko if: $modal.withPoints -->
            <td>
              <!-- ko if: email -->
              <a
                class="d-flex align-items-center"
                data-bind="attr: {
                href: $modal.getAnswersLink($data)
              }"
              >
                <foquz-icon
                  params="icon: 'message'"
                  class="f-icon-primary"
                ></foquz-icon>
                <span class="f-color-text">Ответы</span>
              </a>
              <!-- /ko -->
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table" data-bind="nativeScrollbar">
      <interactive-table params="table: $table, horizontal: true">
        <table class="table foq-table fixed-table">
          <tbody>
            <tr>
              <th>Клиент</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(name)"></td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Телефон</th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__priority-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)"
              ></td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Email</th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__priority-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)"
              ></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.withPoints -->
            <tr>
              <th>Баллы</th>
              <!-- ko foreach: items -->
              <td>
                <div class="mb-5p">
                  <span class="bold" data-bind="text: $data.points"></span>
                  из
                  <span
                    class="bold"
                    data-bind="text: $modal.question.maxPoints"
                  ></span
                  >,
                  <span
                    data-bind="text: $modal.getPercent($data.points, $modal.question.maxPoints) + '%'"
                  ></span>
                </div>
                <progress-line
                  style="width: 100px"
                  params="progress: $modal.getPercent($data.points, $modal.question.maxPoints)"
                >
                </progress-line>
              </td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th>Пройден</th>
              <!-- ko foreach: items -->
              <td data-bind="html: passedAt"></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.hasOrder -->
            <tr>
              <th
                class="question-statistics__priority-modal-dialog-table-order-id-head-cell"
              >
                № заказа
              </th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__priority-modal-dialog-table-order-id-cell"
              >
                <a
                  class="question-statistics__priority-modal-dialog-table-link"
                  href="#"
                  data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order)}"
                >
                </a>
              </td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Дата заказа</th>
              <!-- ko foreach: items -->

              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th>Вариант</th>
              <!-- ko foreach: items -->
              <td data-bind="let: { variants: $modal.getVariants($data.ids)}">
                <!-- ko if: skipped -->
                Отказался от ответа
                <!-- /ko -->
                <!-- ko ifnot: skipped -->
                <!-- ko foreach: variants -->
                <a
                  style="white-space: nowrap"
                  href="javascript:void(0)"
                  data-bind="text: name + ($index() == variants.length - 1 ? '' : ', '), fancyboxGalleryItem: {
                                        index: $index(),
                                        gallery: variants.map(function(v) { return {
                                            src: v.url,
                                            opts: { caption: v.description || '' }
                                       } })
                                    }"
                ></a>
                <!-- ko if: $modal.question.correctVariantsIds.includes($data.id) -->
                <svg-icon
                  params="name: 'check'"
                  class="svg-icon--sm ml-1"
                ></svg-icon>
                <!-- /ko -->
                <!-- /ko -->
                <!-- /ko -->
              </td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Комментарий</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(comment)"></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.withPoints -->
            <tr>
              <th>Ответы</th>
              <!-- ko foreach: items -->
              <td>
                <!-- ko if: email -->
                <a
                  class="d-flex align-items-center"
                  data-bind="attr: {
                    href: $modal.getAnswersLink($data)
                  }"
                >
                  <foquz-icon
                    params="icon: 'message'"
                    class="f-icon-primary"
                  ></foquz-icon>
                  <span class="f-color-text">Ответы</span>
                </a>
                <!-- /ko -->
              </td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
          </tbody>
        </table>
      </interactive-table>
    </div>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
