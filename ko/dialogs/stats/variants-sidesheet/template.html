<stats-sidesheet
  class="variants-stats"
  params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints"
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__clients-modal-dialog-table"
      >
        <thead>
          <tr>
            <th width="120">ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <!-- ko if: $modal.withPoints -->
            <th>Баллы</th>
            <!-- /ko -->
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th
              class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
            >
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <th>Филиал</th>
            <th>
              Комментарий
            </th>
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__clients-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__clients-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)"
            ></td>
            <!-- ko if: $modal.withPoints && !without_points -->
            <td>
              <div class="mb-5p">
                <span class="bold" data-bind="text: points"></span>
                из
                <span
                  class="bold"
                  data-bind="text: $modal.question.maxPoints"
                ></span
                >,
                <span
                  data-bind="text: $modal.getPercent(points, $modal.question.maxPoints) + '%'"
                ></span>
              </div>
              <progress-line
                style="width: 100px"
                params="progress: $modal.getPercent(points, $modal.question.maxPoints)"
              >
              </progress-line>
            </td>
            <!-- /ko -->
            <!-- ko if: $modal.withPoints && without_points -->
            <td>
              <div class="mb-5p">
                <span>Выбранные<br/>варианты<br/>не участвуют<br/>в подсчёте</span>
              </div>
            </td>
            <!-- /ko -->
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td
              class="question-statistics__clients-modal-dialog-table-order-id-cell"
            >
              <a
                class="question-statistics__clients-modal-dialog-table-link"
                data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <!-- ko if: $modal.withPoints -->
            <td>
              <!-- ko if: email -->
              <a
                class="d-flex align-items-center"
                data-bind="attr: {
                href: $modal.getAnswersLink($data)
              }"
              >
                <foquz-icon
                  params="icon: 'message'"
                  class="f-icon-primary"
                ></foquz-icon>
                <span class="f-color-text">Ответы</span>
              </a>
              <!-- /ko -->
            </td>
            <!-- /ko -->
            <td data-bind="html: $modal.getText(filialName)"></td>
            <td data-bind="html: $modal.getText(comment)"></td>
          </tr>
          <tr class="question-statistics__clients-modal-dialog-table-row question-statistics__clients-modal-dialog-table-row_variants border-top" data-bind="click: function () {console.log($data)}">
            <td class="border-top-0" colspan="88">
              <!-- ko if: $data.skipped -->
              Отказался от ответа
              <!-- /ko -->

              <!-- ko ifnot: $data.skipped -->
              <!-- ko foreach: answers -->
              <div class="d-flex align-items-center" data-bind="css: {'with-file': $data.file_id}">
                <!-- ko if: $data.file_id -->
                <file-loader-preview class="file-loader-preview file-loader-preview_stats mr-10p" data-bind="click: function (_, event) {
                  event.stopPropagation();
                  }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url,
                  onRemove: function() { 
                      variant.file(null)
                      variant.value('')
                  }">

                </file-loader-preview>
                <!-- /ko -->
                <!-- ko if: id == 'self' || id == -1 -->
                <span
                  class="mr-5p align-self-start stats-custom-variant"
                  data-bind="text: $modal.question.selfVariantText + ':'"
                ></span>
                <!-- /ko -->
                <span data-bind="text: variant, css: {
                  'deleted': $data.deleted
                }"></span>
                <!-- ko if: $modal.question.correctVariantsIds.includes(parseInt(id)) -->
                <svg-icon
                  params="name: 'check'"
                  class="svg-icon--sm ml-1"
                ></svg-icon>
                <!-- /ko -->
              </div>
              <!-- /ko -->
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table" data-bind="nativeScrollbar">
      <interactive-table params="table: $table, horizontal: true">
        <table class="table foq-table fixed-table">
          <tbody>
            <tr>
              <th>ФИО контакта</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(name)"></td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Телефон</th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)"
              ></td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Email</th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)"
              ></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.withPoints -->
            <tr>
              <th>Баллы</th>
              <!-- ko foreach: items -->
              <td>
                <div class="mb-5p">
                  <span class="bold" data-bind="text: points"></span>
                  из
                  <span
                    class="bold"
                    data-bind="text: $modal.question.maxPoints"
                  ></span
                  >,
                  <span
                    data-bind="text: $modal.getPercent(points, $modal.question.maxPoints) + '%'"
                  ></span>
                </div>
                <progress-line
                  style="width: 100px"
                  params="progress: $modal.getPercent(points, $modal.question.maxPoints)"
                >
                </progress-line>
              </td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th>Пройден</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(passedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.hasOrder -->
            <tr>
              <th
                class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
              >
                № заказа
              </th>
              <!-- ko foreach: items -->
              <td
                class="question-statistics__clients-modal-dialog-table-order-id-cell"
              >
                <a
                  class="question-statistics__clients-modal-dialog-table-link"
                  data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }"
                >
                </a>
              </td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Дата заказа</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th>Вариант</th>
              <!-- ko foreach: items -->
              <td>
                <!-- ko if: $data.skipped -->
                Отказался от ответа
                <!-- /ko -->
                <!-- ko ifno: $data.skipped -->
                <!-- ko foreach: answers -->
                <div>
                  <!-- ko if: id == 'self' || id == -1 -->
                  <span
                    data-bind="text: $modal.question.selfVariantText + ': '"
                  ></span>
                  <!-- /ko -->
                  <span data-bind="text: variant"></span>
                  <!-- ko if: $modal.question.correctVariantsIds.includes(parseInt(id)) -->
                  <svg-icon
                    params="name: 'check'"
                    class="svg-icon--sm ml-1"
                  ></svg-icon>
                  <!-- /ko -->
                </div>
                <!-- /ko -->
                <!-- /ko -->
              </td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.withPoints -->
            <tr>
              <th>Ответы</th>
              <!-- ko foreach: items -->
              <td>
                <!-- ko if: email -->
                <a
                  class="d-flex align-items-center"
                  data-bind="attr: {
                  href: $modal.getAnswersLink($data)
                }"
                >
                  <foquz-icon
                    params="icon: 'message'"
                    class="f-icon-primary"
                  ></foquz-icon>
                  <span class="f-color-text">Ответы</span>
                </a>
                <!-- /ko -->
              </td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th>Филиал</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(filialName)"></td>
              <!-- /ko -->
            </tr>
            <tr>
              <!-- ko foreach: items -->
              <td data-bind="html: 11111"></td>
              <!-- /ko -->
            </tr>
          </tbody>
        </table>
      </interactive-table>
    </div>
  </media-query>

  <!-- /ko -->
</stats-sidesheet>
