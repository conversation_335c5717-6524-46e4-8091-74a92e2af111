import { Client } from '../client';

export class VariantsClient extends Client {
  constructor(data) {
    super(data);
    this.filialName = data.filialName;
    this.comment = data.comment
    this.skipped = data.skipped;
    this.without_points = data.without_points
    this.answers = data.answer.map(a => {
      return {
        id: a.id,
        variant: a.variant,
        deleted: a.is_deleted,
        file_id: a.file_id,
        file_url: a.file_url,
        preview_url: a.preview_url
      }
    })

  }
}
