import { InteractiveTable } from 'Models/interactive-table';

export class StatsDataTable extends InteractiveTable {
  constructor() {
    super();
    this._additionalParams = {};
  }

  getParams() {
    return {
      ...super.getParams(),
      ...this._additionalParams
    };
  }

  setParams(params) {
    this._additionalParams = params;
  }

  handleResponse(response) {
    return response;
  }

  load() {
    if (this.beforeLoad()) {
      let ajaxParams = this.getParams();
      $.ajax({
        url: this.apiUrl,
        data: ajaxParams,
        success: (response) => {
          let items = this.handleResponse(response, ajaxParams);
          this.afterLoad(items);
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.onError();
        }
      });
    }
  }

}
