import { DialogWrapper } from 'Dialogs/wrapper';
import { ApiUrl } from 'Utils/url/api-url';

import { months } from 'Utils/date/months';

function formatPeriod([from, to], type) {
  let fromDate = moment(from, 'YYYY-MM-DD');
  let toDate = moment(to, 'YYYY-MM-DD');

  if (type == 'year') {
    return fromDate.format('YYYY');
  }

  if (type == 'month') {
    let month = fromDate.format('M');
    return months[month - 1];
  }

  if (type == 'day') {
    return fromDate.format('DD.MM.YYYY');
  }

  return `${fromDate.format('DD.MM')} — ${toDate.format('DD.MM')}`;
}
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    let that = this;

    this.question = params.question;
    this.type = this.question.type;
    this._searchParams = params.searchParams || {};

    this.groupBy = ko.observable('week');

    this.loading = ko.observable(true);
    this.pending = ko.observable(true);
    this.noData = ko.observable(false);

    this.chart = ko.observable(null);
    this.chartConfig = ko.observable({
      series: [],
      yAxis: {
        ceiling: 100,
        floor: -100
      },
      chart: {
        height: 400,
        spacingTop: 15
      },
      legend: {
        align: 'left',
        itemMarginBottom: 5,
      },
      tooltip: {
        useHTML: true,

        formatter: function () {
          return `
          <div style="font-size: 10px; color: #666666; margin-bottom: 4px;">${formatPeriod(
            this.key,
            that.groupBy()
          )}</div>
          <div style="display: flex;">
            <span style="background-color: ${
              this.color
            }; width: 8px; height: 8px; border-radius: 50%; display: inline-block; margin-right: 4px; margin-top: 2px;"></span>
            ${
              this.series.name
            }: <span style="font-weight: bold; margin-left: 4px;">${
            Math.floor(this.y * 100) / 100
          }</span>
          </div>
        `;
        }
      },
      xAxis: {
        type: 'category',
        labels: {
          rotation: 0,
          useHTML: true,
          formatter: function () {
            return formatPeriod(this.value, that.groupBy());
          }
        }
      }
    });

    this.load();

    this.groupBy.subscribe((v) => {
      this.load();
    });
  }

  getYConfig(min, max) {
    let that = this;
    return {
      ceiling: parseInt(max),
      max: parseInt(max),
      floor: parseInt(min),
      min: parseInt(min),
      tickInterval: this.question.type == 'nps' ? 10 : 1,
      labels: {
        useHTML: true,
        formatter: function () {
          if (that.question.type == 'smile') {
            let smile = that.question.smiles[this.value - 1];
            return `<img width="22" height="22" src="${smile.url}">`;
          }

          return `<span>${this.value}</span>`;
        }
      }
    };
  }

  handleData(response) {
    let periods = response.periods;

    if (!periods.length) {
      this.noData(true);
      return;
    }

    let series = [
      {
        name: this.type == 'nps' ? 'Общий рейтинг' : 'Общая оценка',
        color: '#00C968',
        lineWidth: 4,
        data: response.total.map((p, i) => {
          return {
            name: periods[i],
            y: p,
            marker: {
              radius: 1,
              symbol: 'circle'
            },
            color: '#00C968'
          };
        })
      }
    ];

    response.items.forEach((item) => {
      let seria = {
        name: item.filial,
        data: item.data.map((p, i) => {
          return {
            name: periods[i],
            y: p
          };
        })
      };
      series.push(seria);
    });

    let legendOffset = 12;
    if (this.type == 'smile') legendOffset = 27;
    else if (this.type == 'nps') legendOffset = 27;
    else if (response.maxRate > 9) legendOffset = 20;

    let data = {
      xAxis: {
        categories: periods
      },

      yAxis: this.getYConfig(response.minRate, response.maxRate),

      series,

      legend: {
        x: legendOffset,
      },

      chart: {
        scrollablePlotArea: {
          minWidth: 100 * periods.length
        }
      }
    };
    this.chart().update(data);
  }

  load() {
    this.pending(true);

    $.ajax({
      url: ApiUrl('rating/dynamics'),
      data: {
        ...this._searchParams,
        questionId: this.question.id,
        groupBy: this.groupBy()
      },
      success: (response) => {
        this.handleData(response);
        this.loading(false);
        this.pending(false);
      },
      error: (response) => {
        console.error(response.responseJSON);
        this.loading(false);
        this.pending(false);
      }
    });
  }
}
