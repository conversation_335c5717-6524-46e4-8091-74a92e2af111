import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/highchart/line';

ko.components.register('stats-dynamics-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('stats-dynamics-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
