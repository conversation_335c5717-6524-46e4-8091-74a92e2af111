<!-- ko let: { $statsDialog: $component } -->
<sidesheet class="dynamics-stats" params="ref: modal, dialogWrapper: $component">

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll pt-4"
         data-bind="nativeScrollbar">

      <div class="container mb-3">
        <div class="stats-sidesheet__header">
          <div class="px-2">
            <h2 class="foquz-dialog__title">
              Динамика изменений
            </h2>
          </div>
        </div>

        <!-- ko if: loading -->
        <div class="flex-grow-1">
          <spinner></spinner>
        </div>
        <!-- /ko -->


        <!-- ko if: !loading() && noData() -->
        <div class="flex-grow-1 flex-grow-1 d-flex align-items-center justify-content-center">
          <div>Нет данных для построения графика</div>
        </div>
        <!-- /ko -->

        <div data-bind="fade: !loading() && !noData()">
          <div class="mt-2 mt-md-4 mb-20p details-filter">
            <label class="details-filter__label f-color-service">Группировать по</label>
            <div class="d-flex flex-wrap mx-n10p">
              <input-radio class="sm mx-10p mb-1"
                           params="checked: groupBy, name: 'group-by', value: 'year'">Годам</input-radio>
              <input-radio class="sm mx-10p mb-1"
                           params="checked: groupBy, name: 'group-by', value: 'month'">Месяцам</input-radio>
              <input-radio class="sm mx-10p mb-1"
                           params="checked: groupBy, name: 'group-by', value: 'week'">Неделям</input-radio>
              <input-radio class="sm mx-10p mb-1"
                           params="checked: groupBy, name: 'group-by', value: 'day'">Дням</input-radio>
            </div>
          </div>
          <line-highchart params="ref: chart, config: chartConfig, loading: pending"></line-highchart>
        </div>


      </div>
    </div>
  </div>
</sidesheet>
<!-- /ko -->
