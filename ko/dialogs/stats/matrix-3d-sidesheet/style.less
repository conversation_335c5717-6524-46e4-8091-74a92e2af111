.stats-sidesheet.matrix-stats {
  .matrix-stats-table {
    .matrix-stats__client-data {
      td {
        border-top: 2px solid #E7EBED;
        border-bottom: none;
      }
    }
    .matrix-stats__client-answer {
      border-bottom-width: 2px;
      border-top: none;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        left: 10px;
        right: 10px;
        top: 0;
        border-bottom: 1px solid #E7EBED;
      }

      table {
        margin-bottom: 0;
      }
    }
  }
}

.matrix-answer-values {
  display: flex;
  flex-direction: column;
}

.matrix-answer-values__item {
  width: 100%;
  display: flex;
  padding-top: 8px;
  padding-bottom: 8px;

  &:last-child {
    border-bottom: unset;
  }
}

.matrix-answer-value__name {
  flex-grow: 1;
}

.matrix-answer-value__value {
  min-width: 200px;
  text-align: right;
}

.matrix-answer--no-value {
  font-size: 12px;
  font-weight: 400;
  line-height: 13px;
  color: #73808D;
  min-width: 200px;
}

.matrix-3d-table__desktop-header {
  display: flex;
}
.matrix-3d-table__row {
  display: flex;
}
.matrix-3d-table__row-data {
  display: flex;
}
.matrix-3d-table__row-item {
  display: flex;
}
.matrix-3d-table__col-header {
  display: none;
}

.matrix-3d-table__header-item {
  min-width: 140px;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  color: #73808D;
  flex-grow: 1;
}

.matrix-3d-table__desktop-header {
  justify-content: space-between;
  align-items: flex-end;
  border-bottom: 1px solid #E1E8EB;
  padding-bottom: 10px;
}

.matrix-3d-table__row {
  justify-content: space-between;
  padding-top: 8px;
  padding-bottom: 8px;
  width: fit-content;

  &:not(:last-child) {
    border-bottom: 1px solid #E1E8EB;
  }
}

.matrix-3d-table__row-header {
  width: 100%;
  display: flex;
  align-items: center;
  padding-top: 7px;
  padding-bottom: 7px;

  &:not(:last-child) {
    border-bottom: 1px solid #E1E8EB;
  }
}

.matrix-3d-table__row-data {
  flex-grow: 1;
}

.matrix-3d-table__row-item {
  min-width: 120px;
  font-family: Roboto;
  font-size: 12px;
  font-weight: 500;
  line-height: 12px;

  .matrix-3d-table__row-item--skipped {
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    color: #73808D;
  }

  .foquz-combo {
    width: 100%;
  }
}

.matrix-3d-table {
  display: flex;
  margin-top: 8px;
  margin-bottom: 25px;
}

.matrix-3d-table__desktop-row-headers {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  color: #73808D;

  >* {
    padding-right: 10px;
  }

  @media screen and (max-width: 768px) {
    display: none;
  }
}

.matrix-3d-table__body {
  width: 75%;

  @media screen and (max-width: 768px) {
    width: 100%;
  }

  &.matrix-3d-table__body--rows-above {
    width: 100%;
  }
}

.matrix-3d-table__above-header {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-top: 20px;
  position: sticky;
  left: 1px;
  width: fit-content;

  @media screen and (max-width: 768px) {
    display: block;
    width: 100%;
  }

  &.matrix-3d-table__above-header--hidden {
    display: none;
    @media screen and (max-width: 768px) {
      display: block;
    }
  }
}

.matrix-3d-table {
  .foquz-combo__wrapper--valid {
    position: relative;

    .foquz-combo__select {
      border-color: #00C968;
    }

    &::after {
      content: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M7 14C10.866 14 14 10.866 14 7C14 3.13401 10.866 0 7 0C3.13401 0 0 3.13401 0 7C0 10.866 3.13401 14 7 14Z' fill='%2300C968'/%3E%3Cpath d='M4 6.7077L6.32424 9L9.93806 5' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
      position: absolute;
      top: -7px;
      right: -7px;
    }
  }
}
