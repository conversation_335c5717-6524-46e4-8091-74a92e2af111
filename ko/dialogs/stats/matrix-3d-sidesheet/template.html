<stats-sidesheet
  class="matrix-stats"
  params="
    ref: modal,
    dialogWrapper: $component,
    title: title,
    question: question,
    withPoints: withPoints,
  "
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table class="table matrix-stats-table foq-table question-statistics__clients-modal-dialog-table">
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <!-- ko if: $modal.question.clarifyingQuestion -->
            <th>
              Ответ на доп. вопрос
            </th>
            <!-- /ko -->
            <!-- ko if: $modal.hasOrder -->
            <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
              № заказа
            </th>
            <th>Дата заказа</th>

            <!-- /ko -->
            <!-- ko ifnot: $modal.question.clarifyingQuestion -->
            <th>Комментарий</th>
            <!-- /ko -->
          </tr>
        </thead>

        <tbody>
          <!-- ko foreach: items -->
          <!-- ko let: { $itemIndex: $index() } -->
          <tr class="matrix-stats__client-data question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)">
            </td>
            <td class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)">
            </td>
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
              <a class="question-statistics__clients-modal-dialog-table-link"
                 href="#"
                 data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order) }">
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <td data-bind="html: $modal.getText(comment)"></td>
          </tr>
          <!-- ko if: $modal.question.skipped -->
          <tr>
            <td class="matrix-stats__client-answer" colspan="5">
              <div class="py-2 matrix-answer--no-value">Пропуск ответа</div>
            </td>
          </tr>
          <!-- /ko -->
          <!-- ko ifnot: $modal.question.skipped -->
          <tr>
            <td class="matrix-stats__client-answer" colspan="5">
              <div class="matrix-3d-table" data-bind="matrix3DTable">
                <div class="matrix-3d-table__desktop-row-headers">
                  <div class="matrix-3d-table__row-header"></div>
                  <!-- ko foreach: $modal.question.matrix.rows -->
                  <!-- ko let: {
                    text: $data.donor_variant_id === null ? `${$data.name}` : $data.name,
                  } -->
                  <!-- ko if: ($table.items()[$itemIndex].recipientRows && $table.items()[$itemIndex].recipientRows.includes($data.id)) || !$table.items()[$itemIndex].recipientRows -->
                  <div class="matrix-3d-table__row-header" data-bind="text: text"></div>
                  <!-- /ko -->
                  <!-- /ko -->
                  <!-- /ko -->
                </div>
                <div class="matrix-3d-table__body">
                  <div class="" data-bind="nativeScrollbar">
                    <div class="matrix-3d-table__wrapper">
                      <div class="matrix-3d-table__desktop-header">
                        <!-- ko foreach: $modal.question.matrix.columns -->
                        <!-- ko if: ($table.items()[$itemIndex].recipientColumns && $table.items()[$itemIndex].recipientColumns.includes($data.id)) || !$table.items()[$itemIndex].recipientColumns -->
                        <div class="matrix-3d-table__header-item" data-bind="text: name"></div>
                        <!-- /ko -->
                        <!-- /ko -->
                      </div>
                      <!-- ko foreach: $modal.question.matrix.rows -->
                      <!-- ko if: ($table.items()[$itemIndex].recipientRows && $table.items()[$itemIndex].recipientRows.includes($data.id)) || !$table.items()[$itemIndex].recipientRows -->
                      <div class="matrix-3d-table__row">
                        <!-- ko foreach: $modal.question.matrix.columns -->
                        <!-- ko if: ($table.items()[$itemIndex].recipientColumns && $table.items()[$itemIndex].recipientColumns.includes($data.id)) || !$table.items()[$itemIndex].recipientColumns -->
                        <div class="matrix-3d-table__row-item">
                          <div class="matrix-3d-table__col-header" data-bind="text: name"></div>
                          <!-- ko ifnot: $parents[1].answer[$parent.id] && $parents[1].answer[$parent.id][id] -->
                          <span class="matrix-3d-table__row-item--skipped"></span>
                          <!-- /ko -->
                          <!-- ko if: $parents[1].answer[$parent.id] && $parents[1].answer[$parent.id][id] -->
                          <!-- ko foreach: $parents[1].answer[$parent.id][id] -->
                          <!-- ko let: {
                            cellAnswer: $parent.variants.find(el => el.id == $data),
                          }-->
                          <!-- ko if: cellAnswer -->
                          <span data-bind="text: cellAnswer.name"></span>
                          <!-- /ko -->
                          <!-- ko ifnot: cellAnswer -->
                          <span class="matrix-3d-table__row-item--skipped">
                            Пропуск ответа
                          </span>
                          <!-- /ko -->
                          <!-- /ko -->
                          <!-- /ko -->
                          <!-- /ko -->
                          
                        </div>
                        <!-- /ko -->
                        <!-- /ko -->
                      </div>
                      <!-- /ko -->
                      <!-- /ko -->
                    </div>
                  </div>
                </div>
              </div>
            </td>
          </tr>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /ko -->
        </tbody>
      </table>

    </interactive-table>
  </media-query>


  <media-query params="query: 'mobile'">
    <interactive-table params="table: $table">
      <!-- ko foreach: $table.items -->
      <div class="mobile-item border-bottom">
        <div class="mobile-item__client">
          <table>
            <tbody>

              <tr>
                <th class="question-statistics__profiles-modal-dialog-table-name-cell">
                  ФИО контакта
                </th>
                <td data-bind="html: $modal.getText(name)"></td>
              </tr>
              <tr>
                <th>Телефон</th>
                <td class="question-statistics__profiles-modal-dialog-table-phone-cell"
                    data-bind="html: $modal.getText(phone)">
                </td>
              </tr>
              <tr>
                <th>Email</th>
                <td class="question-statistics__profiles-modal-dialog-table-email-cell"
                    data-bind="html: $modal.getText(email)">
                </td>
              </tr>
              <!-- ko if: $modal.withPoints -->
              <tr>
                <th>Баллы</th>
                <td>
                <td>
                  <div class="mb-5p">
                    <span class="bold"
                          data-bind="text: $data.points"></span>
                    из
                    <span class="bold"
                          data-bind="text: $modal.question.maxPoints"></span>,
                    <span data-bind="text: $modal.getPercent($data.points, $modal.question.maxPoints) + '%'"></span>
                  </div>
                  <progress-line style="width: 100px"
                                 params="progress: $modal.getPercent($data.points, $modal.question.maxPoints)">
                  </progress-line>
                </td>
                </td>
              </tr>
              <!-- /ko -->
              <tr>
                <th>Пройден</th>
                <td data-bind="html: $modal.getText(passedAt)"></td>
              </tr>
              <!-- ko if: $modal.question.clarifyingQuestion -->
              <tr>
                <th>
                  Ответ на доп. вопрос
                </th>
                <!-- ko foreach: items -->
                <td data-bind="html: $modal.getText(answer)"></td>
                <!-- /ko -->
              </tr>
              <!-- /ko -->
              <!-- ko if: $modal.hasOrder -->
              <tr>
                <th class="question-statistics__profiles-modal-dialog-table-order-id-head-cell">
                  № заказа
                </th>
                <td class="question-statistics__profiles-modal-dialog-table-order-id-cell">
                  <a class="question-statistics__profiles-modal-dialog-table-link"
                     href="#"
                     data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                  </a>
                </td>
              </tr>
              <tr>
                <th>Дата заказа</th>
                <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              </tr>
              <!-- /ko -->
              <!-- ko ifnot: $modal.question.clarifyingQuestion -->
              <tr>
                <th>Комментарий</th>
                <td data-bind="html: $modal.getText(comment)"></td>
              </tr>
              <!-- /ko -->
              <!-- ko if: $modal.withPoints -->
              <tr>
                <th>Ответы</th>

                <td>
                  <!-- ko if: email -->
                  <a class="d-flex align-items-center"
                     data-bind="attr: {
                      href: $modal.getAnswersLink($data)
                    }">
                    <foquz-icon params="icon: 'message'"
                                class="f-icon-primary"></foquz-icon>
                    <span class="f-color-text">Ответы</span>
                  </a>
                  <!-- /ko -->
                </td>
              </tr>
              <!-- /ko -->
            </tbody>
          </table>
        </div>
        <div class="mobile-item__answer">
          <!-- ko if: $data.skipped -->
          <div class="matrix-stats__client-answer py-2 matrix-answer--no-value">
            Пропуск оценки
          </div>
          <!-- /ko -->
          <!-- ko ifnot: $data.skipped -->
          <div class="matrix-answer-values">
            <div class="matrix-3d-table" data-bind="matrix3DTable">
              <div class="matrix-3d-table__desktop-row-headers">
                <div class="matrix-3d-table__row-header"></div>
                <!-- ko foreach: $modal.question.matrix.rows -->
                <div class="matrix-3d-table__row-header" data-bind="text: name"></div>
                <!-- /ko -->
              </div>
              <div class="matrix-3d-table__body">
                <div class="" data-bind="nativeScrollbar">
                  <div class="matrix-3d-table__wrapper">
                    <div class="matrix-3d-table__desktop-header">
                      <!-- ko foreach: $modal.question.matrix.columns -->
                      <div class="matrix-3d-table__header-item" data-bind="text: name"></div>
                      <!-- /ko -->
                    </div>
                    <!-- ko foreach: $modal.question.matrix.rows -->
                    <div class="matrix-3d-table__above-header" data-bind="text: name"></div>
                    <div class="matrix-3d-table__row">
                      <!-- ko foreach: $modal.question.matrix.columns -->
                      <div class="matrix-3d-table__row-item">
                        <div class="matrix-3d-table__col-header" data-bind="text: name"></div>
                        <!-- ko foreach: $parents[1].answer[$parent.id][id] -->
                        <!-- ko let: {
                          cellAnswer: $parent.variants.find(el => el.id == $data),
                        }-->
                        <!-- ko if: cellAnswer -->
                        <span data-bind="text: cellAnswer.name"></span>
                        <!-- /ko -->
                        <!-- ko ifnot: cellAnswer -->
                        <span class="matrix-3d-table__row-item--skipped">
                          Пропуск ответа
                        </span>
                        <!-- /ko -->
                        <!-- /ko -->
                        <!-- /ko -->
                      </div>
                      <!-- /ko -->
                    </div>
                    <!-- /ko -->
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->
    </interactive-table>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
