import { map as _map, reduce as _reduce } from 'lodash';

import { Client } from "../client";

export class Matrix3DClient extends Client {
  constructor(data, params) {
    super(data);

    this.recipientColumns = data.recipientColumns || null;
    this.recipientRows = data.recipientRows || null;
    this.answer = data.answer;
    this.selfVariantValue = data.selfAnswerValue || "";
    this.skipped = data.skipped;
    this.questionId = params.id;
    this.extraComments = _reduce(data.answer.extra || [], (acc, el, key) => {
      return {
        ...acc,
        ...({
          [key]: _reduce(el, (sAcc, sEl, sKey) => {
            let entry = sEl;
            if (sKey !== 'self_variant' && sKey !== 'answer') {
              entry = QUESTIONS.find(el => el.id === this.questionId).detail_answers.find(el => el.id === +sEl).variant;
            }
            return `${sAcc}${sAcc === '' ? '' : ', '}${entry}`;
          }, ''),
        }),
      };
    }, []);
  }
}
