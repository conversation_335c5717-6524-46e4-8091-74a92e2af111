<stats-sidesheet class="gallery-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints">

  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
             class="table foq-table question-statistics__priority-modal-dialog-table question-statistics__gallery-table">
        <thead>
          <tr>
            <th>Клиент</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th class="question-statistics__priority-modal-dialog-table-order-id-head-cell">
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->

            <th>Комментарий</th>
          </tr>
        </thead>

        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__priority-modal-dialog-table-row-data gallery-stats-client">
            <td data-bind="html: $modal.getText(name)"></td>
            <td class="question-statistics__priority-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)">
            </td>
            <td class="question-statistics__priority-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)">
            </td>
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td class="question-statistics__priority-modal-dialog-table-order-id-cell">
              <a class="question-statistics__priority-modal-dialog-table-link"
                 href="#"
                 data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->

            <td data-bind="html: $modal.getText(comment)"></td>
          </tr>

          
          <tr class="question-statistics__gallery-table-media">
            <td class="gallery-stats-answer" data-bind="attr: { colspan: $modal.getText.hasOrder ? 7 : 5 }">
              <!-- ko if: skipped -->
              Отказался от оценки
            <!-- /ko -->

            <!-- ko ifnot: skipped -->
              <div class="d-flex flex-wrap"
                   data-bind="let: {orderedMedia: $modal.getOrderedMedia(media) }">
                <!-- ko foreach: orderedMedia -->
                <div class="question-statistics__gallery-table-media-item"
                     data-bind="
                              css: {
                                'question-statistics__gallery-table-media-item--inactive': $parent.answer[$data.id] == 0
                              }
                            ">
                  <div class="question-statistics__gallery-table-media-wrapper"
                       data-bind="fancyboxGalleryItem: {
                                gallery: orderedMedia.map(function(m) {
                                  return {
                                    src: m.url,
                                    opts: {
                                      caption: m.description || ''
                                    }
                                  }
                                }),
                                index: $index()
                              }">
                    <img data-bind="
                                  attr: {
                                    src: $data.src,
                                    alt: $data.description || ''
                                  }">
                  </div>


                  <div class="question-statistics__gallery-table-media-rating">

                       <!-- ko if: $parent.answer[$data.id] -->
                       <!-- ko text: parseInt($parent.answer[$data.id]).toFixed(1) -->
                       <!-- /ko -->
                       <!-- /ko -->

                       <!-- ko ifnot: $parent.answer[$data.id] -->
                       —
                       <!-- /ko -->

                  </div>
                </div>
                <!-- /ko -->
              </div>
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>

    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <interactive-table params="table: $table">
      <!-- ko foreach: $table.items -->
      <div class="mobile-item border-bottom">
        <div class="mobile-item__client">
          <table>
            <tbody>

              <tr>
                <th class="question-statistics__profiles-modal-dialog-table-name-cell">
                  ФИО контакта
                </th>
                <td data-bind="html: $modal.getText(name)"></td>
              </tr>
              <tr>
                <th>Телефон</th>
                <td class="question-statistics__profiles-modal-dialog-table-phone-cell"
                    data-bind="html: $modal.getText(phone)">
                </td>
              </tr>
              <tr>
                <th>Email</th>
                <td class="question-statistics__profiles-modal-dialog-table-email-cell"
                    data-bind="html: $modal.getText(email)">
                </td>
              </tr>
              <tr>
                <th>Пройден</th>
                <td data-bind="html: $modal.getText(passedAt)"></td>
              </tr>
              <!-- ko if: $modal.hasOrder -->
              <tr>
                <th class="question-statistics__profiles-modal-dialog-table-order-id-head-cell">
                  № заказа
                </th>
                <td class="question-statistics__profiles-modal-dialog-table-order-id-cell">
                  <a class="question-statistics__profiles-modal-dialog-table-link"
                     href="#"
                     data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                  </a>
                </td>
              </tr>
              <tr>
                <th>Дата заказа</th>
                <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              </tr>
              <!-- /ko -->
              <tr>
                <th>Комментарий</th>
                <td data-bind="html: $modal.getText(comment)"></td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- ko if: skipped -->
        <div class="py-2">Отказался от оценки</div>
        <!-- /ko -->

        <!-- ko ifnot: skipped -->
        <div class="mobile-item__answer">
          <div class="d-flex flex-wrap"
               data-bind="let: {orderedMedia: $modal.getOrderedMedia(media) }">
            <!-- ko foreach: orderedMedia -->
            <div class="question-statistics__gallery-table-media-item"
                 data-bind="
                     css: {
                       'question-statistics__gallery-table-media-item--inactive': $parent.answer[$data.id] == 0
                     }
                   ">
              <div class="question-statistics__gallery-table-media-wrapper"
                   data-bind="fancyboxGalleryItem: {
                       gallery: orderedMedia.map(function(m) {
                         return {
                           src: m.url,
                           opts: {
                             caption: m.description || ''
                           }
                         }
                       }),
                       index: $index()
                     }">
                <img data-bind="
                         attr: {
                           src: $data.src,
                           alt: $data.description || ''
                         }">
              </div>

              <div class="question-statistics__gallery-table-media-rating"
                   data-bind="text: $parent.answer[$data.id] == 0 ? '—' : parseInt($parent.answer[$data.id]).toFixed(1)">

              </div>
            </div>
            <!-- /ko -->
          </div>
        </div>
        <!-- /ko -->
      </div>
      <!-- /ko -->
    </interactive-table>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
