import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.bindingHandlers.questionStatisticsAddressesModalDialogMap = {
  init: function (element, valueAccessor, allBindings) {
    const address = allBindings
      .get('questionStatisticsAddressesModalDialogMapAddress')
      .replace(
        '<span class="question-statistics__addresses-modal-dialog-table-highlight">',
        ''
      )
      .replace('</span>', '');

    const map = new ymaps.Map(
      element,
      {
        center: [55.010251, 82.958437],
        zoom: 9,
        controls: []
      },
      {
        searchControlProvider: 'yandex#search'
      }
    );

    ymaps.geocode(address).then(function (res) {
      const firstGeoObject = res.geoObjects.get(0),
        coords = firstGeoObject.geometry.getCoordinates(),
        bounds = firstGeoObject.properties.get('boundedBy');

      firstGeoObject.options.set('preset', 'islands#redDotIcon');
      firstGeoObject.properties.set(
        'iconCaption',
        firstGeoObject.getAddressLine()
      );

      map.geoObjects.add(firstGeoObject);

      map.setBounds(bounds, {
        checkZoomRange: true
      });
    });

    ko.utils.domNodeDisposal.addDisposeCallback(element, function () {
      map.destroy();
    });
  }
};

ko.components.register('stats-addresses-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('stats-addresses-sidesheet');

      return new ViewModel(params, element);
    }
  },
  template: html
});
