<stats-sidesheet class="addresses-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints">

  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table class="table foq-table question-statistics__clients-modal-dialog-table">
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <th>Адрес</th>
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <!-- ko let: { isOpen: ko.observable(false) } -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)">
            </td>
            <td class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)">
            </td>
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
              <a class="question-statistics__clients-modal-dialog-table-link"
                 data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <td>
              <a class="question-statistics__addresses-modal-dialog-table-link"
                 data-bind="html: $modal.getText(address), click: function () { isOpen(!isOpen()); }">
              </a>
            </td>
          </tr>
          <tr>
            <td class="question-statistics__addresses-modal-dialog-table-map-cell"
                data-bind="attr: { colspan: $modal.hasOrder ? 7 : 5 }">
              <!-- ko template: {
                            foreach: templateIf(isOpen(), $data),
                            afterAdd: slideAfterAddFactory(200),
                            beforeRemove: slideBeforeRemoveFactory(200)
                        } -->
              <div class="question-statistics__addresses-modal-dialog-table-map-cell-content"
                   data-bind="
                                            questionStatisticsAddressesModalDialogMap,
                                            questionStatisticsAddressesModalDialogMapAddress: address
                                         ">
              </div>
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table"
         data-bind="nativeScrollbar">
      <interactive-table params="table: $table, horizontal: true">
        <table class="table foq-table fixed-table">
          <tbody>

            <tr>
              <th>ФИО контакта</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(name)"></td>
              <!-- /ko -->
            </tr>

            <tr>
              <th>Телефон</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                  data-bind="html: $modal.getText(phone)">
              </td>
              <!-- /ko -->
            </tr>

            <tr>
              <th>Email</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-email-cell"
                  data-bind="html: $modal.getText(email)">
              </td>
              <!-- /ko -->
            </tr>

            <tr>
              <th>Пройден</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(passedAt)"></td>
              <!-- /ko -->
            </tr>

            <!-- ko if: $modal.hasOrder -->
            <tr>
              <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
                № заказа
              </th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
                <a class="question-statistics__clients-modal-dialog-table-link"
                   data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                </a>
              </td>
              <!-- /ko -->
            </tr>

            <tr>
              <th>Дата заказа</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->

            <tr>
              <th>Адрес</th>
              <!-- ko foreach: items -->
              <td data-bind="let: {isOpen: ko.observable(false)}">
                <a class="question-statistics__addresses-modal-dialog-table-link"
                   data-bind="html: $modal.getText(address), click: function () { isOpen(true); }">
                </a>

                <!-- ko template: {
                              foreach: templateIf(isOpen(), $data),
                              afterAdd: slideAfterAddFactory(200),
                              beforeRemove: slideBeforeRemoveFactory(200)
                          } -->
                <div class="mobile-map">
                  <div class="mobile-map__map"
                       data-bind="
                      questionStatisticsAddressesModalDialogMap,
                      questionStatisticsAddressesModalDialogMapAddress: address
                  ">
                  </div>
                  <button class="mobile-map__close button-ghost" type="button"
                    data-bind="click: function() {
                      isOpen(false);
                    }">
                    <svg-icon params="name: 'times'" class="f-color-service"></svg-icon>
                  </button>
                </div>
                <!-- /ko -->
              </td>
              <!-- /ko -->
            </tr>

          </tbody>
        </table>
      </interactive-table>
    </div>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
