.stats-nps-sidesheet {
  td {
    word-break: break-word;
  }

  /* .nps-variants-list {
    max-width: 500px;
  } */

  .skipped, .nps-variant {
    padding-left: 12px;
  }

  .nps-variant {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 7px;
    padding-bottom: 7px;
    border-bottom: 1px solid #E7EBED;
    min-height: 40px;

    @media screen and (max-width: 767.98px) {
     flex-wrap: wrap;
    }

    &__name {
      font-size: 12px;
      color: var(--f-color-service);

    }

    &__value {
      flex-shrink: 0;
      width: 30px;
      margin-left: auto;
      margin-right: 20px;
      @media screen and (max-width: 767.98px) {
        margin-right: 0;
       }
    }
    &__extra {
      .nps-variant__extra-title-separate {
        font-weight: 400;
        font-size: 12px;
        line-height: 110%;
        color: #73808D;
        margin-bottom: 5px;
      }
      width: 20%;
      &.type-1 {
        display: inline-block;
        vertical-align: top;
        padding-top: 7px;
      }
      @media screen and (max-width: 767.98px) {
        width: 100%;
       }
      &-value {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        .file-loader-preview_stats-clarify {
          flex-shrink: 0;
        }
      }
      &-title {
        font-weight: 700;
        font-size: 12px;
        line-height: 110%;
        color: #2E2F31;
        margin-bottom: 10px;
        margin-top: 8px;
      }
    }
  }

  .nps-variants-list {
    &.with-clarify {
      width: 79%;
      display: inline-block;
    }
  }

  @media screen and (max-width: 1199.98px) {
    .nps-variants-list {
      max-width: 100%;
    }

    .complex-table table {
      table-layout: auto;
    }

  }

  .deleted {
    color: var(--f-color-service);

    &:after {
      content: "(удален)";
      color: var(--f-color-danger);
      font-style: italic;
      margin-left: 4px;
    }
  }
}
