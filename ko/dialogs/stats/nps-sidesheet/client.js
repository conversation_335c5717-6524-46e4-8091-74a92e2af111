import { Client } from "../client";

function getLabel(rating) {
  if (rating < 7) return "Критик";
  if (rating < 9) return "Нейтрал";
  return "Промоутер";
}

export class NPSClient extends Client {
  constructor(data, params) {
    super(data);
    let question = window.QUESTIONS.find(i => i.id == params.id)
    this.filialName = data.filialName;
    this.rating = data.rating;
    this.ratingLabel = getLabel(data.rating);
    this.extraVariants = []

    if (question.extra_question_type == 1) {
      const extraValues = data.extra_answer ? data.extra_answer : [];
      extraValues.forEach(element => {
        const variant = question.detail_answers.find(i => i.id == element)
        if (variant) {
          this.extraVariants.push({
            text: variant.variant,
            file_id: variant.file_id,
            file_url: variant.file_url,
            preview_url: variant.preview_url
          })
        } else if (question.is_self_answer) {
          this.extraVariants.push({
            is_self_answer: true,
            text: element,
            file_id: question.selfVariantFile?.file_id,
            file_url: question.selfVariantFile?.file_url,
            preview_url: question.selfVariantFile?.preview_url
          })
        } else {
          this.extraVariants.push({
            text: element,
          })
        }
      });
    }
    this.variants = (Array.isArray(data.answer) ? data.answer : []).map((v) => {
      const value = parseInt(v.value);
      const extraValues = data.extra_answer && data.extra_answer[v.id] ? data.extra_answer[v.id] : [];
      let extra = [];
      extraValues.forEach(element => {
        const variant = question.detail_answers.find(i => i.id == element)
        const npsVariant = question.detail_answers.find(i => i.id == v.id)
        if (variant) {
          extra.push({
            text: variant.variant || `Вариант ${variant.position}`,
            file_id: variant.file_id,
            file_url: variant.file_url,
            preview_url: variant.preview_url
          })
        } else if (question.is_self_answer) {
          extra.push({
            text: `${question.self_variant_text || 'Свой вариант'}: ${element}`,
            file_id: question.selfVariantFile?.file_id,
            file_url: question.selfVariantFile?.file_url,
            preview_url: question.selfVariantFile?.preview_url,
          })
        } else if (npsVariant?.is_self_answer) {
          extra.push({
            text: `${npsVariant?.self_variant_text || 'Свой вариант'}: ${element}`,
            file_id: npsVariant?.selfVariantFile?.file_id,
            file_url: npsVariant?.selfVariantFile?.file_url,
            preview_url: npsVariant?.selfVariantFile?.preview_url,
          })
        } else {
          extra.push({
            text: element,
          })
        }
      });
      return {
        name: v.name,
        rating: value,
        ratingLabel: getLabel(value),
        deleted: v.is_deleted,
        extra: extra,
      };
    });

    this.skipped = data.skipped;
  }
}
