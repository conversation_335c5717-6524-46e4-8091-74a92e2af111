<stats-sidesheet
  class="nps-stats"
  params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints"
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__clients-modal-dialog-table"
        data-bind="log"
      >
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th
              class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
            >
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->

            <!-- ko ifnot: $modal.question.withVariants -->
            <th width="30">Оценка</th>
            <!-- /ko -->

            <!-- ko ifnot: $modal.question.extraQuestionType -->
            <th>Комментарий</th>
            <!-- /ko -->
            <th>Филиал</th>
            <!-- ko if: $modal.question.extraQuestionType -->
            <th>Ответ на доп. вопрос</th>
            <!-- /ko -->
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__clients-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__clients-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)"
            ></td>
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td
              class="question-statistics__clients-modal-dialog-table-order-id-cell"
            >
              <a
                class="question-statistics__clients-modal-dialog-table-link"
                href="#"
                data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order) }"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->

            <!-- ko ifnot: $modal.question.withVariants -->
            <td align="left">
              <!-- ko if: $data.skipped -->
              <div class="py-10p skipped">Отказался от оценки</div>
              <!-- /ko -->
              <!-- ko ifnot: $data.skipped -->

              <!-- ko if: rating > -1 -->
              <div class="d-flex align-items-center">
                <div class="nps-scale nps-scale--colored nps-scale--sm mr-2">
                  <div class="nps-scale__list m-0">
                    <div
                      class="nps-scale__item"
                      data-bind="style: {
                          backgroundColor: $modal.question.scale[rating]
                        }"
                    >
                      <span data-bind="text: rating"></span>
                    </div>
                  </div>
                </div>
                <div
                  class="f-fs-1 bold text-nowrap"
                  data-bind="html: $modal.getText(ratingLabel)"
                ></div>
              </div>
              <!-- /ko -->
              <!-- /ko -->
            </td>
            <!-- /ko -->

            <td data-bind="html: $modal.getText(filialName)"></td>
            <td data-bind="html: $modal.question.clarifyingQuestion ? '' : $modal.getText(comment)"></td>
          </tr>
          <!-- ko if: $modal.question.withVariants -->
          <tr>
            <td
              data-bind="attr: {
              colspan: $modal.hasOrder || $modal.question.clarifyingQuestion ? 7 : 5
            }"
              class="p-0"
            >
              <!-- ko if: $data.skipped -->
              <div class="py-10p skipped">Отказался от оценки</div>
              <!-- /ko -->
              <!-- ko ifnot: $data.skipped -->
              <div class="nps-variants-list" data-bind="css: {'with-clarify': $modal.question.clarifyingQuestion && $modal.question.extraQuestionType == 1}">
                <!-- ko foreach: { data: variants, as: 'field' } -->
                <div class="nps-variant">
                  <div
                    class="nps-variant__name"
                    data-bind="text: field.name, css: {
                      deleted: field.deleted
                    }"
                  ></div>
                  <div class="nps-variant__value" data-bind="click: function () {console.log(field)}">
                    <!-- ko if: field.rating > -1 -->
                    <fc-nps-item
                      params="value: field.rating, startColor: '#f96261', endColor: '#00c968', design: 1"
                    ></fc-nps-item>
                    <!-- /ko -->
                    <!-- ko ifnot: field.rating > -1 -->
                    -
                    <!-- /ko -->
                  </div>
                  <!-- ko if: $modal.question.extraQuestionType && $modal.question.extraQuestionType !== 1 -->
                  <div class="nps-variant__extra">
                    <!-- ko if: $modal.question.extraQuestionType == 3 && field.extra && field.extra.length  -->
                    <div class="nps-variant__extra-title-separate">Ответ на уточняющий вопрос</div>
                    <!-- /ko -->
                    <!-- ko foreach: field.extra -->
                      <div class="nps-variant__extra-value">
                        <!-- ko if: $data.file_id -->
                          <file-loader-preview class="file-loader-preview file-loader-preview_stats file-loader-preview_stats-clarify mr-10p" data-bind="click: function (_, event) {
                            event.stopPropagation();
                          }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url,
                          onRemove: function() { 
                              variant.file(null)
                              variant.value('')
                          }">

                        </file-loader-preview>
                        <!-- /ko -->
                        <div class="" data-bind="text: $data.text"></div>
                      </div>
                    <!-- /ko -->
                  </div>
                <!-- /ko -->
                </div>
                
                <!-- /ko -->
                
              </div>
              
              <!-- /ko -->
              <!-- ko if: $modal.question.clarifyingQuestion && $modal.question.extraQuestionType == 1 -->
              <div class="nps-variant__extra type-1">
                <!-- ko foreach: extraVariants -->
                <div class="nps-variant__extra-value">
                  <!-- ko if: $data.file_id -->
                    <file-loader-preview class="file-loader-preview file-loader-preview_stats file-loader-preview_stats-clarify mr-10p" data-bind="click: function (_, event) {
                        event.stopPropagation();
                      }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url,
                      onRemove: function() { 
                          variant.file(null)
                          variant.value('')
                      }">

                    </file-loader-preview>
                  <!-- /ko -->
                  <div>
                    <!-- ko if: $data.is_self_answer -->
                      <!-- ko text: $modal.question.selfVariantText || 'Свой вариант' -->
                      <!-- /ko -->:
                    <!-- /ko -->
                    <!-- ko text: $data.text -->
                    <!-- /ko -->
                  </div>
                </div>
              <!-- /ko -->
              </div>
              <!-- /ko -->
            </td>
            
          </tr>
          
          <!-- /ko -->
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table" data-bind="nativeScrollbar">
      <interactive-table params="table: $table">
        <table class="table foq-table fixed-table">
          <!-- ko foreach: items -->
          <tbody>
            <tr>
              <th>ФИО контакта</th>
              <td data-bind="html: $modal.getText(name)"></td>
            </tr>
            <tr>
              <th>Телефон</th>
              <td
                class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)"
              ></td>
            </tr>

            <tr>
              <th>Email</th>

              <td
                class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)"
              ></td>
            </tr>

            <tr>
              <th>Пройден</th>

              <td data-bind="html: $modal.getText(passedAt)"></td>
            </tr>

            <!-- ko if: $modal.hasOrder -->
            <tr>
              <th
                class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
              >
                № заказа
              </th>

              <td
                class="question-statistics__clients-modal-dialog-table-order-id-cell"
              >
                <a
                  class="question-statistics__clients-modal-dialog-table-link"
                  href="#"
                  data-bind="html: '#' + $modal.getText(order), attr: {href: $modal.getOrderLink(order) }"
                >
                </a>
              </td>
            </tr>
            <tr>
              <th>Дата заказа</th>

              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            </tr>
            <!--/ko -->

            <!-- ko ifnot: $modal.question.withVariants -->
            <tr>
              <th width="30">Оценка</th>

              <td align="center">
                <!-- ko if: $data.skipped -->
                Отказался от оценки
                <!-- /ko -->
                <!-- ko ifnot: $data.skipped -->

                <!-- ko if: rating > -1 -->
                <div class="d-flex align-items-center">
                  <div class="nps-scale nps-scale--colored nps-scale--sm mr-2">
                    <div class="nps-scale__list m-0">
                      <div
                        class="nps-scale__item"
                        data-bind="style: {
                              backgroundColor: $modal.question.scale[rating]
                            }"
                      >
                        <span data-bind="text: rating"></span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="f-fs-1 bold"
                    data-bind="html: $modal.getText(ratingLabel)"
                  ></div>
                </div>
                <!-- /ko -->
                <!-- /ko -->
              </td>
            </tr>
            <!-- /ko -->

            <tr>
              <th>Комментарий</th>

              <td data-bind="html: $modal.question.clarifyingQuestion ? '' : $modal.getText(comment)"></td>
            </tr>

            <!-- ko if: $modal.question.withVariants -->

            <tr>
              <td colspan="2" class="p-0">
                <!-- ko if: $data.skipped -->
                <div class="py-10p skipped">Отказался от оценки</div>
                <!-- /ko -->

                <!-- ko ifnot: $data.skipped -->
                <div class="nps-variants-list">
                  <!-- ko foreach: { data: variants, as: 'field' } -->
                  <div class="nps-variant px-15p">
                    <div
                      class="nps-variant__name"
                      data-bind="text: field.name, css: {
                        deleted: field.deleted
                      }"
                    ></div>
                    <div class="nps-variant__value" data-bind="click: function () {console.log($modal)}">
                      <!-- ko if: field.rating > -1 -->
                      <fc-nps-item
                        params="value: field.rating, startColor: '#f96261', endColor: '#00c968', design: 1"
                      ></fc-nps-item>
                      <!-- /ko -->
                      <!-- ko ifnot: field.rating > -1 -->
                      -
                      <!-- /ko -->
                    </div>
                    <!-- ko if: $modal.question.extraQuestionType && $modal.question.extraQuestionType !== 1 -->
                      <div class="nps-variant__extra">
                        <!-- ko if: $modal.question.extraQuestionType == 3 && field.extra && field.extra.length  -->
                        <div class="nps-variant__extra-title-separate">Ответ на уточняющий вопрос</div>
                        <!-- /ko -->
                        <!-- ko foreach: field.extra -->
                          <div class="nps-variant__extra-value">
                            <!-- ko if: $data.file_id -->
                              <file-loader-preview class="file-loader-preview file-loader-preview_stats file-loader-preview_stats-clarify mr-10p" data-bind="click: function (_, event) {
                                event.stopPropagation();
                              }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url,
                              onRemove: function() { 
                                  variant.file(null)
                                  variant.value('')
                              }">
    
                            </file-loader-preview>
                            <!-- /ko -->
                            <div class="" data-bind="text: $data.text"></div>
                          </div>
                        <!-- /ko -->
                      </div>
                    <!-- /ko -->
                    
                  </div>
                  <!-- /ko -->
                  <!-- ko if: $modal.question.clarifyingQuestion && $modal.question.extraQuestionType == 1 -->
                  <div class="nps-variant__extra pl-15p">
                    <div class="nps-variant__extra-title">Ответ на доп. вопрос</div>
                    <!-- ko foreach: extraVariants -->
                      <div class="nps-variant__extra-value">
                        <!-- ko if: $data.file_id -->
                          <file-loader-preview class="file-loader-preview file-loader-preview_stats file-loader-preview_stats-clarify mr-10p" data-bind="click: function (_, event) {
                              event.stopPropagation();
                            }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url,
                            onRemove: function() { 
                                variant.file(null)
                                variant.value('')
                            }">

                          </file-loader-preview>
                        <!-- /ko -->
                        <div>
                          <!-- /ko -->
                          <!-- ko if: $data.is_self_answer -->
                            <!-- ko text: $modal.question.selfVariantText || 'Свой вариант' -->
                            <!-- /ko -->:
                          <!-- /ko -->
                          <!-- ko text: $data.text -->
                          <!-- /ko -->
                        </div>

                      </div>
                    <!-- /ko -->
                  </div>
                <!-- /ko -->
                </div>
                <!-- /ko -->
              </td>
            </tr>
            <!-- /ko -->
            <tr>
              <th>Филиал</th>
              <td data-bind="html: $modal.getText(filialName)"></td>
            </tr>
          </tbody>
          <!-- /ko -->
        </table>
      </interactive-table>
    </div>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
