import { map as _map, reduce as _reduce } from 'lodash';

import { Client } from "../client";
import { Set } from 'core-js';

export class MatrixClient extends Client {
  constructor(data, params) {
    super(data);

    this.answer = data.answer;
    this.selfVariantValue = data.selfAnswerValue || "";
    this.skipped = !!data.skipped;
    this.questionId = params.id;
  }

  getWithoutCat(cards) {
    let withCat = [];
    Object.values(this.answer).forEach(item => {
      item.forEach(card => withCat.push(card));
    });
    withCat = Array.from(new Set(withCat));
    return cards.filter(card => !withCat.find(id => String(card.id) === id));
  }
}
