.card-sorting-stats__client-data {
  td {
    border-top: 2px solid #E7EBED;
    border-bottom: none;
  }
}

.card-sorting-stats .mobile-item__answer {
  padding: 0 !important;
}

.card-sorting-stats .mobile-item__client {
  border-bottom: none !important;
}

.card-sorting-stats__category-name {
  color: #73808D;
}
.card-sorting-stats__card + .card-sorting-stats__card {
  margin-top: 5px;
}
.card-sorting-stats__client-answer {
  position: relative;
  padding: 8px 10px !important;
  border-top: 1px solid #e7ebed;

  table {
    margin-bottom: 0;
  }
}

.card-sorting-answer-values {
  display: flex;
  flex-direction: column;
}

.card-sorting-answer-values__item {
  width: 100%;
  display: flex;
  padding-top: 8px;
  padding-bottom: 8px;

  &:last-child {
    border-bottom: unset;
  }
}

.card-sorting-answer-value__name {
  flex-grow: 1;
}

.card-sorting-answer-value__value {
  min-width: 200px;
  text-align: right;
}

.card-sorting-answer--no-value {
  font-size: 12px;
  font-weight: 400;
  line-height: 13px;
  color: #73808D;
}

.card-sorting-answer-value__wrapper {
  display: flex;
  justify-content: flex-end;
}
