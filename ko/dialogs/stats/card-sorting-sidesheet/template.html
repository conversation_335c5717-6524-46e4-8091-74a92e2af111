<stats-sidesheet class="card-sorting-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints">

  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table class="table card-sorting-stats-table foq-table question-statistics__clients-modal-dialog-table">
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <th>Комментарий</th>
          </tr>
        </thead>

        <tbody>
          <!-- ko foreach: items -->
            <tr class="card-sorting-stats__client-data question-statistics__clients-modal-dialog-table-row">
              <td data-bind="html: $modal.getText(name)"></td>
              <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                  data-bind="html: $modal.getText(phone)">
              </td>
              <td class="question-statistics__clients-modal-dialog-table-email-cell"
                  data-bind="html: $modal.getText(email)">
              </td>
              <td data-bind="html: $modal.getText(passedAt)"></td>

              <td data-bind="html: $modal.getText(comment)"></td>
            </tr>
            <!-- ko if: $data.skipped -->
              <tr class="card-sorting-stats__category">
                <td class="card-sorting-stats__client-answer card-sorting-stats__category-name" colspan="100%">
                  Отказался от ответа
                </td>
              </tr>
            <!-- /ko -->
            <!-- ko ifnot: $data.skipped -->
              <!-- ko foreach: $modal.question.matrix.cols -->
                <tr class="card-sorting-stats__category">
                  <td class="card-sorting-stats__client-answer card-sorting-stats__category-name" colspan="2">
                    <div data-bind="text: $data.name"></div>
                  </td>
                  <td class="card-sorting-stats__client-answer" colspan="2">
                    <!-- ko let: { answers: $parent.answer[$data.id] } -->
                      <!-- ko if: answers -->
                        <!-- ko foreach: answers -->
                        <div
                          class="card-sorting-stats__card"
                          data-bind="text: $modal.question.matrix.rows.find(({id}) => String(id) === $data).question"
                        ></div>
                        <!-- /ko -->
                      <!-- /ko -->
                    <!-- /ko -->
                  </td>
                  <td></td>
                </tr>
              <!-- /ko -->
              <!-- ko let: { answers: $data.getWithoutCat($modal.question.matrix.rows) } -->
                <!-- ko if: answers.length -->
                  <tr class="card-sorting-stats__category">
                    <td class="card-sorting-stats__client-answer card-sorting-stats__category-name" colspan="2">
                      <div>Без категории</div>
                    </td>
                    <td class="card-sorting-stats__client-answer" colspan="2">
                      <!-- ko foreach: answers -->
                        <div
                          class="card-sorting-stats__card"
                          data-bind="text: $data.question"
                        ></div>
                      <!-- /ko -->
                    </td>
                    <td></td>
                  </tr>
                <!-- /ko -->
              <!-- /ko -->
            <!-- /ko -->
          <!-- /ko -->
          <tr class="card-sorting-stats__client-data">
            <td colspan="100%"></td>
          </tr>
        </tbody>
      </table>

    </interactive-table>
  </media-query>


  <media-query params="query: 'mobile'">
    <interactive-table params="table: $table">
      <!-- ko foreach: $table.items -->
      <div class="mobile-item border-bottom">
        <div class="mobile-item__client">
          <table>
            <tbody>
              <tr>
                <th class="question-statistics__profiles-modal-dialog-table-name-cell">
                  ФИО контакта
                </th>
                <td data-bind="html: $modal.getText(name)"></td>
              </tr>
              <tr>
                <th>Телефон</th>
                <td class="question-statistics__profiles-modal-dialog-table-phone-cell"
                    data-bind="html: $modal.getText(phone)">
                </td>
              </tr>
              <tr>
                <th>Email</th>
                <td class="question-statistics__profiles-modal-dialog-table-email-cell"
                    data-bind="html: $modal.getText(email)">
                </td>
              </tr>
              <tr>
                <th>Пройден</th>
                <td data-bind="html: $modal.getText(passedAt)"></td>
              </tr>
              <tr>
                <th>Комментарий</th>
                <td data-bind="html: $modal.getText(comment)"></td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="mobile-item__answer">
          <div class="card-sorting-answer-values">
            <table>
              <tbody>
                <!-- ko if: $data.skipped -->
                  <tr class="card-sorting-stats__category">
                    <td class="card-sorting-stats__client-answer card-sorting-stats__category-name" colspan="100%">
                      Отказался от ответа
                    </td>
                  </tr>
                <!-- /ko -->
                <!-- ko ifnot: $data.skipped -->
                  <!-- ko foreach: $modal.question.matrix.cols -->
                    <tr class="card-sorting-stats__category">
                      <td class="card-sorting-stats__client-answer card-sorting-stats__category-name" colspan="50%">
                        <div data-bind="text: $data.name"></div>
                      </td>
                      <td class="card-sorting-stats__client-answer" colspan="50%">
                        <!-- ko let: { answers: $parent.answer[$data.id] } -->
                          <!-- ko if: answers -->
                            <!-- ko foreach: answers -->
                            <div
                              class="card-sorting-stats__card"
                              data-bind="text: $modal.question.matrix.rows.find(({id}) => String(id) === $data).question"
                            ></div>
                            <!-- /ko -->
                          <!-- /ko -->
                        <!-- /ko -->
                      </td>
                    </tr>
                  <!-- /ko -->
                  <!-- ko let: { answers: $data.getWithoutCat($modal.question.matrix.rows) } -->
                    <!-- ko if: answers.length -->
                      <tr class="card-sorting-stats__category">
                        <td class="card-sorting-stats__client-answer card-sorting-stats__category-name" colspan="50%">
                          <div>Без категории</div>
                        </td>
                        <td class="card-sorting-stats__client-answer" colspan="50%">
                          <!-- ko foreach: answers -->
                            <div
                              class="card-sorting-stats__card"
                              data-bind="text: $data.question"
                            ></div>
                          <!-- /ko -->
                        </td>
                      </tr>
                    <!-- /ko -->
                  <!-- /ko -->
                <!-- /ko -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- /ko -->
    </interactive-table>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
