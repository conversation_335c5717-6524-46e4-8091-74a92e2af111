<stats-sidesheet class="filials-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: false">

  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table class="table foq-table question-statistics__clients-modal-dialog-table">
        <thead>
          <tr>
            <th width="120">ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <th>Вариант</th>
            <th>Комментарий</th>
         
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)">
            </td>
            <td class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)">
            </td>
       
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
              <a class="question-statistics__clients-modal-dialog-table-link"
                 data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <td>
              <!-- ko if: skipped -->
              Отказался от оценки
              <!-- /ko -->

              <!-- ko ifnot: skipped -->
                <!-- ko if: !answers.length -->
                -
                <!-- /ko -->
                <!-- ko foreach: answers -->
                <div>
                  <!-- ko if: id == 'self' || id == -1 -->
                  <span data-bind="text: $modal.question.selfVariantText + ':'"></span>
                  <!-- /ko -->
                  <span data-bind="css: {
                    deleted: $data.deleted
                  }"><span data-bind="text: variant"></span></span>
                
                </div>
                <!-- /ko -->
              <!-- /ko -->
            </td>
            <td data-bind="html: $modal.getText(comment)"></td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table"
         data-bind="nativeScrollbar">
      <interactive-table params="table: $table, horizontal: true">
        <table class="table foq-table fixed-table">
          <tbody>

            <tr>
              <th>ФИО контакта</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(name)"></td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Телефон</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                  data-bind="html: $modal.getText(phone)">
              </td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Email</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-email-cell"
                  data-bind="html: $modal.getText(email)">
              </td>
              <!-- /ko -->
            </tr>
          
            <tr>
              <th>Пройден</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(passedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.hasOrder -->
            <tr>
              <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
                № заказа
              </th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
                <a class="question-statistics__clients-modal-dialog-table-link"
                   data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                </a>
              </td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Дата заказа</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th>Вариант</th>
              <!-- ko foreach: items -->
              <td>
                <!-- ko if: skipped -->
                Отказался от оценки
                <!-- /ko -->
  
                <!-- ko ifnot: skipped -->
                  <!-- ko if: !answers.length -->
                  -
                  <!-- /ko -->
                  <!-- ko foreach: answers -->
                  <div>
                    <!-- ko if: id == 'self' || id == -1 -->
                    <span data-bind="text: $modal.question.selfVariantText + ': '"></span>
                    <!-- /ko -->
                    <span data-bind="text: variant"></span>
                  
                  </div>
                  <!-- /ko -->
                <!-- /ko -->
              </td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Комментарий</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(comment)"></td>
              <!-- /ko -->
            </tr>
          </tbody>

        </table>
      </interactive-table>
    </div>
  </media-query>

  <!-- /ko -->
</stats-sidesheet>
