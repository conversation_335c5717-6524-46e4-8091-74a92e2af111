<stats-sidesheet  class="files-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints">

  <!-- ko let: { $table: table, $modal: $data } -->




  <interactive-table params="table: table">


    <div class="question-statistics__files-modal-dialog-list">
      <!-- ko foreach: items -->
      <div class="question-statistics__files-modal-dialog-list-item">
        <div class="question-statistics__files-modal-dialog-list-item-preview"
             data-bind="
                                  questionStatisticsFilesModalDialogFiles: $modal.getListItemPreviewData($data)
                               ">
          <span class="question-statistics__files-modal-dialog-list-item-preview-count"
                data-bind="text: files.length, css: {'audio' : files[0].type == 'audio'}">
          </span>

          <!-- ko if: files[0].type !== 'audio' -->
          <img class="question-statistics__files-modal-dialog-list-item-preview-image"
               data-bind="attr: {src: files[0].type !== 'audio' ? files[0].preview : '/img/audio-file-back.png'}, css: {'no-opacity': files[0].type == 'audio'}">
          <!-- /ko -->
          <!-- ko if: files[0].type == 'audio' -->
          <div class="question-statistics__files-modal-dialog-list-item-preview-image_audio">
            <svg xmlns="http://www.w3.org/2000/svg" width="49" height="49" viewBox="0 0 49 49" fill="none">
              <path d="M18.5 16.1667L18.3356 15.1803C17.8534 15.2606 17.5 15.6778 17.5 16.1667H18.5ZM34.5 13.5H35.5C35.5 13.206 35.3707 12.927 35.1464 12.737C34.9221 12.547 34.6256 12.4653 34.3356 12.5136L34.5 13.5ZM7.5 2.5H41.5V0.5H7.5V2.5ZM46.5 7.5V41.5H48.5V7.5H46.5ZM41.5 46.5H7.5V48.5H41.5V46.5ZM2.5 41.5V7.5H0.5V41.5H2.5ZM7.5 46.5C4.73858 46.5 2.5 44.2614 2.5 41.5H0.5C0.5 45.366 3.63401 48.5 7.5 48.5V46.5ZM46.5 41.5C46.5 44.2614 44.2614 46.5 41.5 46.5V48.5C45.366 48.5 48.5 45.366 48.5 41.5H46.5ZM41.5 2.5C44.2614 2.5 46.5 4.73858 46.5 7.5H48.5C48.5 3.63401 45.366 0.5 41.5 0.5V2.5ZM7.5 0.5C3.63401 0.5 0.5 3.63401 0.5 7.5H2.5C2.5 4.73858 4.73858 2.5 7.5 2.5V0.5ZM19.5 33.5V16.1667H17.5V33.5H19.5ZM18.6644 17.1531L34.6644 14.4864L34.3356 12.5136L18.3356 15.1803L18.6644 17.1531ZM33.5 13.5V30.8333H35.5V13.5H33.5ZM17.5 33.5C17.5 35.1569 16.1569 36.5 14.5 36.5V38.5C17.2614 38.5 19.5 36.2614 19.5 33.5H17.5ZM14.5 36.5C12.8431 36.5 11.5 35.1569 11.5 33.5H9.5C9.5 36.2614 11.7386 38.5 14.5 38.5V36.5ZM11.5 33.5C11.5 31.8431 12.8431 30.5 14.5 30.5V28.5C11.7386 28.5 9.5 30.7386 9.5 33.5H11.5ZM14.5 30.5C16.1569 30.5 17.5 31.8431 17.5 33.5H19.5C19.5 30.7386 17.2614 28.5 14.5 28.5V30.5ZM33.5 30.8333C33.5 32.4902 32.1569 33.8333 30.5 33.8333V35.8333C33.2614 35.8333 35.5 33.5948 35.5 30.8333H33.5ZM30.5 33.8333C28.8431 33.8333 27.5 32.4902 27.5 30.8333H25.5C25.5 33.5948 27.7386 35.8333 30.5 35.8333V33.8333ZM27.5 30.8333C27.5 29.1765 28.8431 27.8333 30.5 27.8333V25.8333C27.7386 25.8333 25.5 28.0719 25.5 30.8333H27.5ZM30.5 27.8333C32.1569 27.8333 33.5 29.1765 33.5 30.8333H35.5C35.5 28.0719 33.2614 25.8333 30.5 25.8333V27.8333Z" fill="#A6B1BC"/>
            </svg>
          </div>
          
          <!-- /ko -->
          <!-- ko if: comment -->
          <i class="question-statistics__files-modal-dialog-list-item-preview-comment-indicator" data-bind="css: {'audio': files[0].type == 'audio'}"></i>
          <!-- /ko -->
        </div>

        <div class="question-statistics__files-modal-dialog-list-item-info
                                      question-statistics__files-modal-dialog-list-item-first-info"
             data-bind="html: $modal.getText(firstInfo)">
        </div>
        <div class="question-statistics__files-modal-dialog-list-item-info
                                      question-statistics__files-modal-dialog-list-item-second-info"
             data-bind="html: $modal.getText(secondInfo)">
        </div>
      </div>
      <!-- /ko -->
    </div>

  </interactive-table>
  <!-- /ko -->
</stats-sidesheet>
