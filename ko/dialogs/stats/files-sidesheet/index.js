import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.bindingHandlers.questionStatisticsFilesModalDialogFiles = {
  init: function (element, valueAccessor) {
    const {
      urls,
      comment,
      orderId,
      orderCreatedAt,
      name,
      email,
      phone,
      passedAt
    } = valueAccessor();

    const audioExt = ['mp3', 'ogg', 'wav', 'm4a']

    $(element).on('click', () => {
      $.fancybox.open(
        urls.map((url) => {
          const urlArr = url.split('.')
            const res = { src: url }
            if (audioExt.includes(urlArr[urlArr.length - 1].toLowerCase())) {
              res.type = 'inline',
              res.src = `<audio class="fs-file-audio-stat" controls src="${url}"></audio>`
              res.mainClass = "fs-file-audio-container"
            }
            return res;
        }),
        {
          loop: false,
          buttons: ['rotate', 'zoom', 'slideShow', 'close'],
          caption: () => {
            let items = [name, phone, email].filter(Boolean);
            if (orderCreatedAt !== '' && orderCreatedAt !== undefined) {
              items.push(`Заказ ${orderCreatedAt}`);
            }
            if (orderId !== '' && orderId !== undefined) {
              items.push(`Заказ #${orderId}`);
            }
            items.push(`Пройден ${passedAt}`);

            let commentItem =
              comment !== null
                ? `<div class="question-statistics__files-modal-dialog-list-item-fancybox-comment">${comment}</div>`
                : '';

            items = items
              .map(
                (i) =>
                  `<span class="question-statistics__files-modal-dialog-list-item-fancybox-info">${i}</span>`
              )
              .join('');
            return `
                            <div class="question-statistics__files-modal-dialog-list-item-fancybox">
                                <div class="question-statistics__files-modal-dialog-list-item-fancybox-info-block">
                                    ${items}
                                </div>
                                ${commentItem}
                            </div>
                            `;
          }
        }
      );
    });
  }
};

ko.components.register('stats-files-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('stats-files-sidesheet');

      return new ViewModel(params, element);
    }
  },
  template: html
});
