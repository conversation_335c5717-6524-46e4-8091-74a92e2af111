import { StatsDialog } from '../dialog';
import { Table } from './table';

export class ViewModel extends StatsDialog {
  createTable() {
    return new Table();
  }

  getListItemPreviewData (client) {
    const result = {
      urls: client.files.map((f) => f.url),
      comment: client.comment,
      name: client.name,
      phone: client.phone,
      email: client.email,
      passedAt: client.passedAt
    };

    if (this.hasOrder) {
      result.pollId = POLL_ID;
      result.orderId = client.orderId;
      result.orderCreatedAt = client.orderCreatedAt;
    }

    return result;
  };
}
