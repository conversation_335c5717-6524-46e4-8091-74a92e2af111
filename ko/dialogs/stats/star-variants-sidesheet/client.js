import { reduce as _reduce } from 'lodash';

import { Client } from "../client";

export class StarVariantsClient extends Client {
  constructor(data) {
    super(data);

    this.answer = (data?.answer || []).map(v => {
      return {
        ...v,
        name: v.id === -1 ? `${v.name}: ${data.selfAnswerValue}` : v.name,
        deleted: v.is_deleted,
        extraComment: _reduce(v.extra, (acc, el) => `${acc}${acc === '' ? '' : ', '}${el}`, ''),
      }
    });
  }
}
