@import 'Style/breakpoints';

.stats-star-variants-sidesheet {
  .star-variants {
    .star-variant {
      display: flex;
      align-items: center;
      font-size: 12px;
      padding: 10px 0;

      &:not(:last-child) {
        border-bottom: 1px solid #e7ebed;
      }

      &__name {
        flex-grow: 1;

        color: var(--f-color-service);
      }
      &__value {
        flex-shrink: 0;
        max-width: 95px;
        display: flex;
        align-items: center;
        font-weight: bold;
        margin-left: 20px;

        .only-mobile({
          max-width: 70px;
        });

        .fc-icon {
          margin-right: 8px;
          color: var(--f-color-yellow);
        }
      }
    }
  }

  .deleted {
    color: var(--f-color-service);

    &:after {
      content: "(удален)";
      color: var(--f-color-danger);
      font-style: italic;
      margin-left: 4px;
    }
  }
}

.star-variant-value--skipped {
  font-size: 12px;
  font-weight: 400;
  line-height: 13px;
  color: #73808D;
  text-align: right;
}
