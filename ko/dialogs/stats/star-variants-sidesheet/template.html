<stats-sidesheet
  class="gallery-stats"
  params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints"
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__priority-modal-dialog-table question-statistics__gallery-table"
      >
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <!-- ko if: $modal.question.clarifyingQuestion -->
            <th>
              Ответ на доп. вопрос
            </th>
            <!-- /ko -->
            <!-- ko if: $modal.hasOrder -->
            <th
              class="question-statistics__priority-modal-dialog-table-order-id-head-cell"
            >
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <!-- ko ifnot: $modal.question.clarifyingQuestion -->
            <th style="max-width: 400px">Комментарий</th>
            <!-- /ko -->
          </tr>
        </thead>

        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__priority-modal-dialog-table-row-data">
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__priority-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__priority-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)"
            ></td>
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td
              class="question-statistics__priority-modal-dialog-table-order-id-cell"
            >
              <a
                class="question-statistics__priority-modal-dialog-table-link"
                href="#"
                data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->

            <td style="max-width: 400px" data-bind="html: $modal.getText(comment)"></td>
          </tr>
          <!-- ko if: $modal.question.skipped -->
          <tr>
            <td class="matrix-stats__client-answer" colspan="5">
              <div class="py-2 matrix-answer--no-value">Пропуск оценки</div>
            </td>
          </tr>
          <!-- /ko -->
          <!-- ko ifnot: $modal.question.skipped -->
          <!-- ko foreach: { data: answer, as: 'field' } -->
          <tr class="question-statistics__gallery-table-media">
            <td class="py-0" data-bind="attr: { colspan: $modal.getText.hasOrder ? 6 : 4 }">
              <div class="star-variants">
                <div class="star-variant">
                  <div
                    class="star-variant__name"
                    data-bind="text: field.name, css: {
                      deleted: field.deleted
                    }"
                  ></div>
                  <div class="star-variant__value">
                    <!-- ko if: field.value === "null" -->
                    <div class="star-variant-value--skipped">Пропуск оценки</div>
                    <!-- /ko -->
                    <!-- ko ifnot: field.value === "null" -->
                    <fc-icon params="name: 'rating-star'"></fc-icon>
                    <span data-bind="text: field.value"></span>
                    <!-- /ko -->
                  </div>
                </div>
              </div>
            </td>
            <!-- ko if: extraComment -->
            <td data-bind="text: extraComment"></td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <interactive-table params="table: $table">
      <!-- ko foreach: $table.items -->
      <div class="mobile-item border-bottom">
        <div class="mobile-item__client">
          <table class="mb-0">
            <tbody>
              <tr>
                <th
                  class="question-statistics__profiles-modal-dialog-table-name-cell"
                >
                ФИО контакта
                </th>
                <td data-bind="html: $modal.getText(name)"></td>
              </tr>
              <tr>
                <th>Телефон</th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-phone-cell"
                  data-bind="html: $modal.getText(phone)"
                ></td>
              </tr>
              <tr>
                <th>Email</th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-email-cell"
                  data-bind="html: $modal.getText(email)"
                ></td>
              </tr>
              <tr>
                <th>Пройден</th>
                <td data-bind="html: $modal.getText(passedAt)"></td>
              </tr>
              <!-- ko if: $modal.question.clarifyingQuestion -->
              <tr>
                <th>
                  Ответ на доп. вопрос
                </th>
                <!-- ko foreach: items -->
                <td data-bind="html: $modal.getText(answer)"></td>
                <!-- /ko -->
              </tr>
              <!-- /ko -->
              <!-- ko if: $modal.hasOrder -->
              <tr>
                <th
                  class="question-statistics__profiles-modal-dialog-table-order-id-head-cell"
                >
                  № заказа
                </th>
                <td
                  class="question-statistics__profiles-modal-dialog-table-order-id-cell"
                >
                  <a
                    class="question-statistics__profiles-modal-dialog-table-link"
                    href="#"
                    data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }"
                  >
                  </a>
                </td>
              </tr>
              <tr>
                <th>Дата заказа</th>
                <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              </tr>
              <!-- /ko -->
              <!-- ko ifnot: $modal.question.clarifyingQuestion -->
              <tr>
                <th>Комментарий</th>
                <td data-bind="html: $modal.getText(comment)"></td>
              </tr>
              <!-- /ko -->
            </tbody>
          </table>
        </div>

        <div class="mobile-item__answer">
          <div class="star-variants">
            <!-- ko if: $modal.question.skipped -->
            <div class="matrix-stats__client-answer py-2 matrix-answer--no-value">
              Пропуск оценки
            </div>
            <!-- /ko -->
            <!-- ko ifnot: $modal.question.skipped -->
            <!-- ko foreach: { data: answer, as: 'field' } -->
            <div class="star-variant">
              <div
                class="star-variant__name"
                data-bind="text: field.name, css: {
                  deleted: field.deleted
                }"
              ></div>
              <div class="star-variant__value">
                <!-- ko if: field.value === "null" -->
                <div class="star-variant-value--skipped">Пропуск оценки</div>
                <!-- /ko -->
                <!-- ko ifnot: field.value === "null" -->
                <fc-icon params="name: 'rating-star'"></fc-icon>
                <span data-bind="text: field.value"></span>
                <!-- /ko -->
              </div>
            </div>
            <!-- /ko -->
            <!-- /ko -->
          </div>
        </div>
      </div>
      <!-- /ko -->
    </interactive-table>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
