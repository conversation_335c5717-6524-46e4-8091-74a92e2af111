import { ViewModel } from './model';
import html from './template.html';
import './style.less';



ko.components.register('stats-star-variants-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('stats-star-variants-sidesheet');

      return new ViewModel(params, element);
    }
  },
  template: html
});
