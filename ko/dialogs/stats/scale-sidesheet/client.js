import { Client } from '../client';

export class ScaleClient extends Client {
  constructor(data) {
    super(data);

    this.rating = data.rating;
    this.end = data.end;
    this.start = data.start;

    this.variants = (Array.isArray(data.answer) ? data.answer : []).map((v) => {
      const value = parseInt(v.value)
      return {
        name: v.id === -1 ? `${v.name}: ${data.selfAnswerValue}` : v.name,
        rating: value,
        deleted: v.is_deleted,
        end: data.end,
        start: data.start
      };
    });

    this.skipped = data.skipped;
  }
}
