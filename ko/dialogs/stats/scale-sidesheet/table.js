import { StatsDataTable } from '../table';
import { ScaleClient } from './client';

export class Table extends StatsDataTable {
  constructor() {
    super();

    // ToDo reuse
    this.screenSize = ko.observable({
      width: window.innerWidth,
      height: window.innerHeight,
    });
    window.onresize = (() => {
      this.screenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }).bind(this);
  }

  handleResponse(response, params) {
    console.log('StatsDataTable', response, params)
    let answers = []
    if (response.set_variants) {

    } else {
      if (response.answers) {
        answers = Object.values(response.answers).filter(item => typeof item === 'object' && item !== null && !Array.isArray(item));
        answers = answers.map(item => {
          let start = response.answers.scaleRatingSetting ? response.answers.scaleRatingSetting.start : 0
          let end = response.answers.scaleRatingSetting ? response.answers.scaleRatingSetting.end : 100
          return { ...item, start, end }
        })
      }
    }

    console.log('StatsDataTable', answers)

    // answers.map(i => i.rating = parseInt(i.rating))
    console.log('StatsDataTable answers', answers)

    return answers.map(a => new ScaleClient(a));
  }

  get apiUrl() {
    return '/foquz/foquz-poll/get-scale-answers';
  }
}
