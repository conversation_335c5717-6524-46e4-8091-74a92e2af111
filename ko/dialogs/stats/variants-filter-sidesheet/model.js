import { DialogWrapper } from 'Dialogs/wrapper';

const actions = ['Выбрал вариант', 'Не выбрал вариант', 'Пропустил вопрос', 'Затруднился ответить']

const filterObj = (question, action, questions, variants, active) => {
  const obj = {
    value: ko.observable(question),
    action: ko.observable(action),
    answers: ko.observableArray(variants?.map(v => v == 0 ? 'self' : v) || []),
    checked: ko.observable(active),
    questions: questions,
    variants: ko.observableArray([]),
    answersError: ko.observable(false)
  }



  obj.questions.find(q => q.question_id == question).variants.forEach(element => {
    const variant = {
      id: element.id || 'self',
      text: ko.observable(element.question)
    }
    obj.variants.push(variant)
  });
  obj.answers.subscribe((v) => {
    if (v.length) obj.answersError(false)
  })
  obj.value.subscribe((v) => {
    const current = obj.questions.find(q => q.question_id == v)
    const answ = [];
    obj.answers([])

    current.variants.forEach(element => {
      const variant = {
        id: element.id || 'self',
        text: ko.observable(element.question)
      }
      answ.push(variant)
    });
    obj.variants(answ)

  });

  return obj
}

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    this.checked = ko.observable(true);

    this.params = params
    
    this.filters = ko.observableArray([]);
    this.questions = ko.observableArray([]);
    this.actions = ko.observableArray([]);
    this.defaultQuestions = params.questions;
    this.poll = params.poll.id
    this.edit = ko.observable(false)
    
    params.questions.forEach(element => {
      const obj = {
        id: element.question_id,
        text: ko.observable(element.name)
      }
      this.questions.push(obj)
    });

    actions.forEach((element, index) => {
      const obj = {
        id: index + 1,
        text: ko.observable(element)
      }
      this.actions.push(obj)
    });

    let settings = []

    if (params.updatedSettings.length) {
      settings = params.updatedSettings
    } else {
      settings = JSON.parse(FILTER_SETTINGS)
    }

    settings.forEach(item => {
      const obj = filterObj(item.question_id, item.action, this.defaultQuestions, item.variants?.map(v => v.toString()) || [], !!item.active)
      this.filters.push(obj)
    })

    if (this.filters().length) {
      this.edit(true)
    }

  }

  addFilter() {
    const obj = filterObj(this.questions()[0].id, this.actions()[0].id, this.defaultQuestions, [], 1)
    this.filters.push(obj)
  }

  removeFilter(item) {
    this.filters.remove(item)
  }

  reset() {
    this.hide()
  }

  submit() {
    const formData = new FormData
    let empty = false
    this.filters().forEach((item, index) => {
      if ([1,2].includes(item.action()) && !item.answers().length) {
        item.answersError(true)
        empty = true
        return false
      }
      formData.append(`questions[${index}][question_id]`, item.value())
      formData.append(`questions[${index}][action]`, item.action())
      formData.append(`questions[${index}][active]`, item.checked() ? 1 : 0)
      if (item.answers().length) {
        item.answers().forEach((ansv, i) => {
          formData.append(`questions[${index}][variants][${i}]`, ansv === 'self' ? 0 : ansv)
        })
      }
    })

    if (empty) return

    const self = this
    $.ajax({
      url : `/foquz/api/poll/save-stat-filter-settings?poll_id=${self.poll}&access-token=${window.APIConfig.apiKey}`,
      data : formData,
      type : "POST",
      processData: false,
      contentType: false,
      success : function (res) {
        self.params.callback(res.filter_settings)
        self.hide()
      }
  });
  }
}
