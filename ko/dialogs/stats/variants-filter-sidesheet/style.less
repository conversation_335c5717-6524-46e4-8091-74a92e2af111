.variants-filter-sidesheet {
    &-wrapper {
        padding: 33px 30px;
        overflow-y: scroll;
        flex: 1;
        &::-webkit-scrollbar {
            width: 4px;
            height: 4px;
          }
          
          &::-webkit-scrollbar-track {
            background: #E8EDEE;
            width: 8px;      /* цвет зоны отслеживания */
          }
          
          &::-webkit-scrollbar-thumb {
            background-color: #8E99A3;    /* цвет бегунка */
            border-radius: 20px;       /* округлось бегунка */
          }
    }
    &-title {
        color: var(--text, #2E2F31);
        font-feature-settings: 'clig' off, 'liga' off;
        /* h1 */
        font-family: Roboto;
        font-size: 22px;
        font-style: normal;
        font-weight: 900;
        line-height: 110%; /* 24.2px */
        .counter {
            color: #73808D;
            font-weight: 400;
        }
    }
    &-subtitle {
        color: var(--icons-hints-inactive-text, #73808D);
        font-feature-settings: 'clig' off, 'liga' off;
        /* hint text */
        font-family: Roboto;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 130%; /* 15.6px */
        margin-bottom: 30px;
    }
    &-add-button-lable {
        margin-left: 10px;
    }
    &-filter-item {
        border-top: 1px solid #E7EBED;
        padding-top: 15px;
    }
    &-footer {
        margin-top: auto;
    }
}
