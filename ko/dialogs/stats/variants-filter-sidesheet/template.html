<sidesheet class="variants-filter-sidesheet" params="ref: modal, dialogWrapper: $component">
  <div class="variants-filter-sidesheet-wrapper">
    <div class="flex mb-3">
      <div class="variants-filter-sidesheet-title">Условия по ответам <span class="counter" data-bind="text: filters().length"></span></span></div>
    </div>
    <div class="variants-filter-sidesheet-subtitle">Условия работают через «И», выбранные варианты ответов внутри условия — через «ИЛИ».</div>
    <fc-button
            class="mb-4"
            params="
              icon: 'plus',
              color: 'success',
              mode: 'text',
              label: 'Добавить условие',
              click: function() { addFilter() }
            "
          ></fc-button>
    <!-- ko foreach: filters -->
    <div class="variants-filter-sidesheet-filter-item">
      <div class="d-flex justify-content-between align-items-center mb-2">
        <fc-check params="checked: $data.checked, label: 'Активное', disabled: false"></fc-check>
        <fc-button
                params="color: 'danger', icon: 'times', shape: 'square', click: function() {$parent.removeFilter($data)}"
        ></fc-button>
      </div>
      <div class="row">
        <div class="col-8">
          <div class="form-group">
              <fc-label params="text:'Вопрос'"></fc-label>
              <fc-select
                  params="
                      options: $parent.questions,
                      value: $data.value,
                      disabled: false,
                      searchable: true
                  "
              ></fc-select>
          </div>
        </div>
        <div class="col-4">
          <div class="form-group">
              <fc-label params="text:'Действие респондента'"></fc-label>
              <fc-select
                  params="
                      options: $parent.actions,
                      value: $data.action,
                      disabled: false,
                  "
              ></fc-select>
          </div>
        </div>
      </div>
      <!-- ko template: {
                  foreach: templateIf($data.action() == 1 || $data.action() == 2, $data),
                  beforeRemove: slideBeforeRemoveFactory(400)
              } -->
              <div class="row">
                <div class="col-12">
                  <div class="form-group">
                      <fc-label params="text:'Ответы'"></fc-label>
                      <fc-select
                          params="
                              options: $parent.variants,
                              value: $data.answers,
                              disabled: false,
                              multiple: true,
                              invalid: $data.answersError(),
                              placeholder: 'Выберите ответы',
                          "
                      ></fc-select>
                      <fc-error class="mb-4" params="show: $data.answersError(), text: 'Обязательное поле'"></fc-error>
                  </div>
                </div>
              </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- ko if: filters().length -->
      <fc-button
            class="mb-4"
            params="
              icon: 'plus',
              color: 'success',
              mode: 'text',
              label: 'Добавить условие',
              click: function() { addFilter() }
            "
          ></fc-button>
    <!-- /ko -->
  </div>
  <div class="foquz-dialog__footer fixed-footer fixed variants-filter-sidesheet-footer">
    <div class="foquz-dialog__actions">
      <fc-button
        class="ml-2"
        params="label: 'Отменить', icon: 'bin', color: 'secondary', click: function() { reset() }"
      ></fc-button>

      <fc-button
        class="ml-2"
        params="label: 'Сохранить', icon: 'save', color: 'success', disabled: !filters().length && !edit(), click: function() { submit() }"
      ></fc-button>
    </div>

  </div>
  
</sidesheet>
