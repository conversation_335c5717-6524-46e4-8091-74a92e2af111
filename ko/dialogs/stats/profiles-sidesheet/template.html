<stats-sidesheet class="profiles-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints">

    <!-- ko let: { $table: table, $modal: $data } -->




    <media-query params="query: 'tablet+'">
        <interactive-table params="table: $table, fixedHeader: true">
            <table class="table foq-table question-statistics__profiles-modal-dialog-table">
                <thead>
                    <tr>
                        <th class="question-statistics__profiles-modal-dialog-table-name-cell">
                            ФИО контакта
                        </th>
                        <th>Телефон</th>
                        <th>Email</th>
                        <th>Пройден</th>
                        <!-- ko if: $modal.hasOrder -->
                        <th class="question-statistics__profiles-modal-dialog-table-order-id-head-cell">
                            № заказа
                        </th>
                        <th>Дата заказа</th>
                        <!-- /ko -->
                        <th>Филиал</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- ko foreach: items -->
                    <tr class="question-statistics__profiles-modal-dialog-table-row">
                        <td data-bind="html: $modal.getText(name)"></td>
                        <td class="question-statistics__profiles-modal-dialog-table-phone-cell"
                            data-bind="html: $modal.getText(phone)">
                        </td>
                        <td class="question-statistics__profiles-modal-dialog-table-email-cell"
                            data-bind="html: $modal.getText(email)">
                        </td>
                        <td data-bind="html: $modal.getText(passedAt)"></td>
                        <!-- ko if: $modal.hasOrder -->
                        <td class="question-statistics__profiles-modal-dialog-table-order-id-cell">
                            <a class="question-statistics__profiles-modal-dialog-table-link"
                               href="#"
                               data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                            </a>
                        </td>
                        <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
                        <!-- /ko -->
                        <td data-bind="html: $modal.getText(filialName)"></td>
                    </tr>
                    <tr>
                        <td class="question-statistics__profiles-modal-dialog-table-profile-cell"
                            data-bind="attr: { colspan: $modal.hasOrder ? 7 : 5 }">
                            <table class="question-statistics__profiles-modal-dialog-table-profile-cell-table">
                                <tbody>
                                    <!-- ko foreach: $modal.properties -->
                                    <tr>
                                        <th>
                                            <div data-bind="text: question"></div>
                                            <!-- ko if: linkWithClientField -->
                                            <div class="question-statistics__service-text"
                                                 data-bind="text: '(связан с параметром ' + linkedClientField + ' контакта)'">
                                            </div>
                                            <!-- /ko -->
                                        </th>
                                        <td
                                            data-bind="html: $modal.getText($parent.properties[question].toString() || '-')">
                                        </td>
                                    </tr>
                                    <!-- /ko -->
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <!-- /ko -->
                </tbody>
            </table>
        </interactive-table>
    </media-query>

    <media-query params="query: 'mobile'">
        <interactive-table params="table: $table">
            <!-- ko foreach: $table.items -->
            <div class="mobile-item border-bottom">
                <div class="mobile-item__client">
                    <table>
                        <tbody>

                            <tr>
                                <th class="question-statistics__profiles-modal-dialog-table-name-cell">
                                    ФИО контакта
                                </th>
                                <td data-bind="html: $modal.getText(name)"></td>
                            </tr>
                            <tr>
                                <th>Телефон</th>
                                <td class="question-statistics__profiles-modal-dialog-table-phone-cell"
                                    data-bind="html: $modal.getText(phone)">
                                </td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td class="question-statistics__profiles-modal-dialog-table-email-cell"
                                    data-bind="html: $modal.getText(email)">
                                </td>
                            </tr>
                            <tr>
                                <th>Пройден</th>
                                <td data-bind="html: $modal.getText(passedAt)"></td>
                            </tr>
                            <tr>
                                <th>Филиал</th>
                                <td data-bind="html: $modal.getText(filialName)"></td>
                            </tr>
                            <!-- ko if: $modal.hasOrder -->
                            <tr>
                                <th class="question-statistics__profiles-modal-dialog-table-order-id-head-cell">
                                    № заказа
                                </th>
                                <td class="question-statistics__profiles-modal-dialog-table-order-id-cell">
                                    <a class="question-statistics__profiles-modal-dialog-table-link"
                                       href="#"
                                       data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <th>Дата заказа</th>
                                <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
                            </tr>
                            <!-- /ko -->
                        </tbody>
                    </table>
                </div>
                <div class="mobile-item__answer">
                    <table class="question-statistics__profiles-modal-dialog-table-profile-cell-table">
                        <tbody>
                            <!-- ko foreach: $modal.properties -->
                            <tr>
                                <th>
                                    <div data-bind="text: question"></div>
                                    <!-- ko if: linkWithClientField -->
                                    <div class="question-statistics__service-text"
                                         data-bind="text: '(связан с параметром ' + linkedClientField + ' контакта)'">
                                    </div>
                                    <!-- /ko -->
                                </th>
                                <td data-bind="html: $modal.getText($parent.properties[question].toString() || '-')">
                                </td>
                            </tr>
                            <!-- /ko -->
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- /ko -->
        </interactive-table>
    </media-query>
    <!-- /ko -->
</stats-sidesheet>
