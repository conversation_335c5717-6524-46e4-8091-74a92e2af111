<!-- ko let: { $nodes: $componentTemplateNodes, $statsDialog: $component } -->
<sidesheet params="ref: modal, dialogWrapper: $component">

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll pt-4"
         data-bind="nativeScrollbar">

      <div class="container mb-3">
        <div class="stats-sidesheet__header">
          <div class="mb-2 px-2 pt-2">
            <h2 class="foquz-dialog__title"
                data-bind="html: title">
            </h2>

                        <!-- ko if: deleted -->
            <div class="mt-1 f-color-danger f-fs-1">Вариант ответа удален</div>
                        <!-- /ko -->

            <!-- ko if: withPoints -->
            <!-- ko if: question.type == 9 -->
            <div class="mt-1">
              <span class="f-color-service">Правильный вариант: </span>

              <span>
                <!-- ko foreach: question.correctVariants -->
                <a href="javascript:void(0)"
                   class="media-variants-correct-variant"
                   data-bind="text: $data.label, fancyboxGalleryItem: {
                        gallery: $parent.question.correctVariantsFancyGallery,
                        noCursor: true,
                        index: $index()
                    }"></a>
                <!-- /ko -->
              </span>

            </div>
            <!-- /ko -->

            <!-- ko if: question.type == 1 -->
            <div class="mt-1">
              <span class="f-color-service">Правильный вариант: </span>
              <span data-bind="text: question.correctVariantsText"></span>
            </div>
            <!-- /ko -->

            <!-- ko if: question.type == 3 -->
            <div class="mt-1">
              <span class="f-color-service">Правильный вариант: </span>
              <span data-bind="text: question.correctVariant"></span>
            </div>
            <!-- /ko -->

            <!-- ko if: question.type == 8 -->
            <div class="mt-1">
              <span class="f-color-service">Правильный вариант: </span>
              <span data-bind="text: question.correctVariantsString"></span>
            </div>
            <!-- /ko -->
            <!-- /ko -->
          </div>


          <!-- ko ifnot: withPoints -->
          <div class="mb-2 px-2">
            <search-field
                          params="value: query, placeholder: 'Поиск по ключевым словам', onEnter: function() { search() }">
              </seach-field>
          </div>
          <!-- /ko -->




        </div>

        <!-- ko if: withPoints -->
        <div class="stats-sidesheet__points-filters">
          <div class="mr-40p">
            <label class="form-label mb-4">Диапазон полученных баллов</label>
            <interval-slider
                             params="range: pointsRange, minLimit: question.minPoints || 0, maxLimit: question.maxPoints">
            </interval-slider>
          </div>
          <div class="stats-sidesheet__search">
            <search-field
                          params="value: query, placeholder: 'Поиск по ключевым словам', onEnter: function() { search() }">
              </seach-field>
          </div>
        </div>
        <!-- /ko -->

      </div>

      <div class="container pb-3" data-bind="event: {
        'stuck': function() {  $statsDialog.element.classList.add('stucked') },
        'unstuck': function() { $statsDialog.element.classList.remove('stucked') },
      }">
        <!-- ko using: dialogWrapper -->
        <!-- ko template: { nodes: $nodes } -->
        <!-- /ko -->
        <!-- /ko -->
      </div>
    </div>


  </div>


</sidesheet>
<!-- /ko -->
