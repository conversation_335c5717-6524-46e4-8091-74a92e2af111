import { DialogWrapper } from 'Dialogs/wrapper';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.title = params.title;
    this.query = ko.observable('');

    console.log({ params })

    this.question = params.question;
    this.deleted = params.deleted == 1;

    this.withPoints = params.withPoints && this.question.withPoints;

    if (this.withPoints) {
      this.pointsRange = ko.observable([
        this.question.minPoints || 0,
        this.question.maxPoints
      ]);

      this.pointsParams = ko.computed(() => {
        let range = this.pointsRange();
        if (!range) return [];
        if (
          range[0] == this.question.minPoints &&
          range[1] == this.question.maxPoints
        ) {
          return [];
        }
        return range;
      });

      let debouncedPointsSearch = _.debounce(
        (v) => this.emitEvent('search.points', v),
        400
      );

      this.pointsParams.subscribe(debouncedPointsSearch);
    }
  }

  get events() {
    return [...super.events, 'search.query', 'search.points'];
  }

  search() {
    this.emitEvent('search.query', this.query());
  }

  getParams() {
    let params = {
      q: this.query()
    };

    if (this.withPoints) {
      params.points = this.pointsParams();
    }

    return params;
  }
}
