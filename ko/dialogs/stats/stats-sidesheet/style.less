@import 'Style/breakpoints';
@import 'Style/colors';

.stats-sidesheet {
  &__header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-left: -8px;
    margin-right: -8px;

    .mobile-and-tablet({
      flex-direction: column;

      .foquz-dialog__title {
        padding-right: 40px;
      }

      .foquz-search-field {
        margin-top: 16px;
      }
    });
  }

  &__points-filters {
    display: flex;
    align-items: flex-end;
    margin-top: 8px;
    margin-bottom: 8px;

    .mobile-and-tablet({
      .stats-sidesheet__search {
        flex-grow: 1;
      }
      .foquz-search-field {
        width: 100%!important;
      }
    });

    .only-mobile({
      flex-direction: column;
      align-items: stretch;
    });
  }

  .foquz-search-field {
    .tablet-and-desktop({
      width: 400px;
    });
  }

  .interval-slider {
    .tablet-and-desktop({
      width: 300px;
    });

    .only-mobile({
      margin-bottom: 24px;
    });
  }

  .question-statistics__clients-modal-dialog-table {
    th {
      padding-bottom: 8px;
    }
  }

  .complex-table--fixed-head {
    //z-index: 101;
  }

  &.stucked {
    .os-scrollbar-vertical {
      top: 65px;
    }
  }

  .mobile-item {
    border-bottom: 1px solid @f-color-border;

    &:first-child {
      border-top: 1px solid @f-color-border;
    }

    &__client {
      border-bottom: 1px solid @f-color-border;
      padding: 12px 0;
      table {
        th,
        td {
          padding: 4px 0;
          font-size: 12px;
        }
        th {
          padding-right: 15px;
          min-width: 120px;
          font-weight: bold;
        }
        td {
          font-size: 12px;
          width: 100%;
        }
      }
    }
    &__answer {
      padding: 5px 0;
    }
  }

  .mobile-and-tablet({
    .os-scrollbar {
      display: none;
    }
  });

  .mobile-table {
    margin-left: -15px;
    margin-right: -15px;
    margin-top: 30px;

    th {
      min-width: 115px;
    }

    td {
      min-width: calc(100vw - 125px);
    }
  }

  .mobile-and-tablet({
    .foquz-interval-slider {
      margin-bottom: 15px;
    }
  });
  .only-mobile({
    &__points-filters {
      & > .mr-40p {
        margin-right: 0;
      }
    }
    &__header {
      .pt-2 {
        padding-top: 0!important;
      }
    }
    .os-content {
      padding-top: 20px!important;
    }
    .mobile-item {
      margin-left: -15px;
      margin-right: -15px;
      padding-left: 15px;
      padding-right: 15px;
      font-size: 12px;
    }
    .mobile-table {
      margin-top: 0;
    }
  });
}
