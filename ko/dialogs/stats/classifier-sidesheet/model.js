import { StatsDialog } from "../dialog";
import { Table } from "./table";
import { getTreeStructure } from "@/utils/tree/get-structure";
import { getTreeNodeParents } from "@/utils/tree/get-parents";
export class ViewModel extends StatsDialog {
  constructor(params, element) {
    super(params, element);
    this.tree = ko.unwrap(params.tree);
    this.treeStructure = getTreeStructure(this.tree);
    this.listType = params.listType;
  }
  getAnswerHtml(answer) {

    const parents = getTreeNodeParents(answer.id, this.treeStructure);
    const list = parents
      .map((id) => this.treeStructure[id])
      .map((node) => node.name)
      .map((name) => `<span class="f-color-service">${name}</span>`);
    list.push(answer.variant);
    return list.join(" / ");
  }
  createTable() {
    return new Table();
  }
}
