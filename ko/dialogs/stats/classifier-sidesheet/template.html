<stats-sidesheet
  class="filials-stats"
  params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: false"
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__clients-modal-dialog-table"
      >
        <thead>
          <tr>
            <th width="120">ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>

            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th
              class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
            >
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->

            <th>Комментарий</th>
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__clients-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__clients-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)"
            ></td>

            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td
              class="question-statistics__clients-modal-dialog-table-order-id-cell"
            >
              <a
                class="question-statistics__clients-modal-dialog-table-link"
                data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->

            <td data-bind="html: $modal.getText(comment)"></td>
          </tr>
          <tr>
            <td data-bind="attr: { colspan: $modal.hasOrder ? 7 : 5 }">
              <!-- ko if: $data.skipped -->
              Отказался от оценки
              <!-- /ko -->

              <!-- ko ifnot: $data.skipped -->
                <!-- ko if: !answers.length -->
                -
                <!-- /ko -->

                <!-- ko foreach: answers -->

                <div>
                  <span data-bind="html: $modal.getAnswerHtml($data)"></span>
                </div>

                <!-- /ko -->
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table" data-bind="log, nativeScrollbar">
      <!-- ko foreach: $table.items -->

      <div>
        <table class="table foq-table fixed-table">
          <tbody>
            <tr>
              <th>ФИО контакта</th>

              <td data-bind="html: $modal.getText(name)"></td>
            </tr>
            <tr>
              <th>Телефон</th>

              <td
                class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)"
              ></td>
            </tr>
            <tr>
              <th>Email</th>

              <td
                class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)"
              ></td>
            </tr>

            <tr>
              <th>Пройден</th>

              <td data-bind="html: $modal.getText(passedAt)"></td>
            </tr>
            <!-- ko if: $modal.hasOrder -->
            <tr>
              <th
                class="question-statistics__clients-modal-dialog-table-order-id-head-cell"
              >
                № заказа
              </th>

              <td
                class="question-statistics__clients-modal-dialog-table-order-id-cell"
              >
                <a
                  class="question-statistics__clients-modal-dialog-table-link"
                  data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }"
                >
                </a>
              </td>
            </tr>
            <tr>
              <th>Дата заказа</th>

              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            </tr>
            <!-- /ko -->

            <tr>
              <th>Комментарий</th>

              <td data-bind="html: $modal.getText(comment)"></td>
            </tr>
          </tbody>
        </table>

        <div>
          <!-- ko if: $data.skipped -->
          <div class="mb-3 pl-15p pr-10p">Отказался от оценки</div >
          <!-- /ko -->

          <!-- ko ifnot: $data.skipped -->
          <!-- ko if: !answers.length -->
          -
          <!-- /ko -->

          <!-- ko foreach: answers -->

          <div class="mb-3 pl-15p pr-10p">
            <span data-bind="html: $modal.getAnswerHtml($data)"></span>
          </div>

          <!-- /ko -->
          <!-- /ko -->
        </div>
      </div>

      <!-- /ko -->
    </div>
  </media-query>

  <!-- /ko -->
</stats-sidesheet>
