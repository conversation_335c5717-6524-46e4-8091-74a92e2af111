<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header  params="empty: !texts.title()">
    <!-- ko text: $parent.texts.title -->
    <!-- /ko -->
  </foquz-dialog-header>


  <!-- ko if: texts.text -->
  <div class="foquz-dialog__body">
    <div class="d-flex">
      <!-- ko if: warning -->
      <div class="warning mr-15p">
        <svg-icon params="name: 'exclamation-circle-light'" class="svg-icon--xl"></svg-icon>
      </div>
      <!-- /ko -->
      <div data-bind="html: texts.text"></div>
    </div>
  </div>
  <!-- /ko -->

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link px-2"
              data-bind="
                click: function() {
                  onReset();
                },
                text: texts.cancel"></button>
      <button type="button"
              class="f-btn"
              data-bind="
                click: function() {
                  onConfirm();
                },
                css: 'f-btn-' + mode(),
                text: texts.confirm"></button>
    </div>
  </div>

</foquz-dialog>
