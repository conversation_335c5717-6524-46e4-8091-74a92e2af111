import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/client-history';

import 'Legacy/blocks/components/editor/basic';
import 'Components/editor/tinymce';

import './components/review-processing';
import './components/review-editor';
import './components/review-data';
import './components/review-questions';
import '.'

ko.components.register('statistics__details-modal-dialog-slide', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        'swiper-slide',
        'statistics__details-modal-dialog-slide',
        'statistics__details-modal-dialog-slide--initializing'
      ]);

      const viewModel = new (function () {
        this.index = params.index;
        this.count = params.count;
      })();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass(
          'statistics__details-modal-dialog-slide--initializing'
        );

        viewModel.initializing(false);
      };

      return viewModel;
    }
  },
  template: {
    element: 'statistics-details-modal-dialog-slide-template'
  }
});
ko.components.register('statistics__details-modal-dialog-slider', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        'swiper-container',
        'statistics__details-modal-dialog-slider',
        'statistics__details-modal-dialog-slider--initializing'
      ]);

      const viewModel = new (function () {})();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass(
          'statistics__details-modal-dialog-slider--initializing'
        );

        viewModel.initializing(false);
      };

      viewModel.onChildrenInit = function () {
        console.log('onChildrenInit', $element.find('.swiper-slide'));
        setTimeout(() => {
          const swiper = new Swiper(
            $element,
            {
              spaceBetween: 8,
              on: {
                init: function () {
                  console.log('swiper init', this.slides);
                  this.$el
                    .find(
                      '.statistics__details-modal-dialog-slider-paginator-button--prev'
                    )
                    .on('click', () => {
                      this.slidePrev();
                    });
                  this.$el
                    .find(
                      '.statistics__details-modal-dialog-slider-paginator-button--next'
                    )
                    .on('click', () => {
                      console.log('next', this);
                      this.slideNext();
                    });
                }
              }
            },
            100
          );
        });
      };

      return viewModel;
    }
  },
  template: {
    element: 'statistics-details-modal-dialog-slider-template'
  }
});

ko.components.register('review-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('review-sidesheet');
      element.classList.add('review-sidesheet--responsive');
      element.classList.add('review-details-modal');

      return new ViewModel(params, element);
    }
  },
  template: html
});
