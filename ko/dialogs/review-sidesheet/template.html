<sidesheet params="ref: modal, dialogWrapper: $component">
  <!-- ko if: initializing() || loading() -->
  <spinner></spinner>
  <!-- /ko -->

  <!-- ko if: !initializing() && !loading() -->
  <div
    class="sidesheet__status-line"
    data-bind="css: 'processing-status--' + review.processingStatus()"
  ></div>

  <nav class="foquz-dialog__tabs nav nav-tabs review-details-modal-nav">
    <a
      class="nav-item nav-link nav-item--order active overflow-hidden"
      data-bind="attr: { id: 'nav-poll-tab-' + id, href: '#nav-poll-' + id, 'aria-controls': 'nav-poll-' + id }, event: {
        'show.bs.tab': function() {
          closeHistory();
          return true;
        }
        }"
      data-toggle="tab"
      role="tab"
      aria-selected="true"
    >
      <div class="d-flex d-md-none w-100">
        <!-- ko if: review.order && review.order.number -->
        <span
          class="review-details-modal-nav__order flex-grow-1 mt-1"
          data-bind="tooltip, text: '#' + review.order.number, tooltipText: _t('Номер заказа')"
        >
        </span>
        <!-- /ko -->
        <!-- ko ifnot: review.order && review.order.number -->
        <span
          class="review-details-modal-nav__order"
          data-bind="text: _t('answers', 'Анкета')"
        >
        </span>
        <!-- /ko -->

        <!-- ko if: review.complaint !== null -->
        <i
          class="review-details-modal-nav__complaint-indicator"
          data-bind="tooltip, tooltipText: _t('Есть жалоба')"
        ></i>
        <!-- /ko -->

        <!-- ko if: review.ratingQuestionsCount() > 0 -->
        <i
          class="review-details-modal-nav__rating-indicator"
          data-bind="
        css: 'review-details-modal-nav__rating-indicator--value_' + review.getQuestionsAverageRating(),
        attr: { title: review.getQuestionsAverageRatingLabel() }, tooltip"
        ></i>
        <!-- /ko -->
      </div>

      <div class="d-none d-md-flex overflow-hidden w-100">
        <div
          class="d-inline-flex flex-grow-1 align-items-center overflow-hidden"
        >
          <!-- ko if: review.isAuto -->
          <i
            class="review-details-modal-nav__icon icon icon-automatic"
            data-bind="tooltip, tooltipText: _t('Автоматический')"
            data-placement="top"
          ></i>

          <!-- /ko -->
          <!-- ko ifnot: review.isAuto -->
          <i
            class="review-details-modal-nav__icon icon icon-manual"
            data-bind="tooltip, tooltipText: _t('Ручной')"
            data-placement="top"
          ></i>
          <!-- /ko -->

          <div class="review-details-modal-nav__poll-name ml-2 mt-1">
            <span
              data-bind="text: review.pollName,
            attr: { title: review.pollName }, tooltip"
            ></span>
          </div>
        </div>
        <!-- ko if: review.order && review.order.number -->
        <span
          class="review-details-modal-nav__order mt-1"
          data-bind="tooltip,  tooltipText: _t('Номер заказа'), text: '#' + review.order.number"
        >
        </span>
        <!-- /ko -->
        <!-- ko if: review.complaint !== null -->
        <i
          class="review-details-modal-nav__complaint-indicator d-inline-block"
          data-bind="tooltip,  tooltipText: _t('Есть жалоба')"
        ></i>
        <!-- /ko -->
        <!-- ko if: review.ratingQuestionsCount() > 0 -->
        <i
          class="review-details-modal-nav__rating-indicator d-inline-block"
          data-bind="
          css: 'review-details-modal-nav__rating-indicator--value_' + review.getQuestionsAverageRating(),
          attr: { title: review.getQuestionsAverageRatingLabel() }, tooltip"
        ></i>
        <!-- /ko -->
      </div>
    </a>

    <a
      class="nav-item nav-link nav-item--history flex-shrink-0"
      data-bind="attr: { id: 'nav-history-tab-' + id, href: '#nav-history-' + id, 'aria-controls': 'nav-history-' + id }, event: {
        'show.bs.tab': function() {
          openHistory();
          return true;
        }
        }"
      data-toggle="tab"
      role="tab"
      aria-selected="true"
    >
      <!-- ko text: _t('answers', 'История') -->
      <!-- /ko -->
    </a>
  </nav>

  <div class="tab-content review-details-modal__tab-content foquz-dialog__body">
    <div
      class="tab-pane show active h-100"
      data-bind="attr: {
        id: 'nav-poll-' + id,
        'aria-labelledby': 'nav-poll-tab-' + id
      }"
      role="tabpanel"
    >
      <div class="foquz-dialog__scroll pt-4" data-bind="nativeScrollbar">
        <div class="container">
          <review-data
            params="review: review, directories: directories, answerTags: answerTags, hideClientData: hideClientData"
          ></review-data>
        </div>

        <div class="review-details__questions review-questions">
          <div class="container">
            <!-- ko if: review.withPoints() && review.processingId -->
            <div
              class="f-color-text mb-4 review-details__points"
              data-bind="html: getReviewPointsHtml(review)"
            ></div>
            <!-- /ko -->
            <div class="review-questions__header">
              <div class="review-questions__title">
                <!-- ko text: _t('Ответы') -->
                <!-- /ko -->
                <span
                  class="review-questions__answers-count text-nowrap"
                  data-bind="text: _t('main', '{num1} из {num2}', {
                  num1: review.questionsWithAnswer(),
                  num2: review.questions().length
                })"
                >
                </span>
                <!-- ko let: { randomSum: (review.randomOrder ? 1 : 0) + (review.pagesRandomOrder ? 2 : 0), randomTexts: {
                  1: _t('answers', 'Включен случайный порядок страниц'),
                  2: _t('answers', 'Для страниц включен случайный порядок вопросов'),
                  3: _t('answers', 'Включен случайный порядок страниц, для страниц включен случайный порядок вопросов')
                } } -->
                <!-- ko if: randomSum > 0 -->

                <span
                  class="d-inline-block pl-5p"
                  style="vertical-align: top; font-size: 0"
                  data-bind="tooltip,
                tooltipText: randomTexts[randomSum]"
                >
                  <svg-icon
                    params="name: 'shuffle'"
                    class="f-color-light svg-icon--sm"
                  ></svg-icon>
                </span>
                <!-- /ko -->
                <!-- /ko -->
              </div>
              <!-- ko if: review.link -->
              <fc-button
                params="
                  label: 'Прохождение анкеты',
                  mode: 'text',
                  color: 'primary',
                  linkMode: true,
                  linkAttrs: {
                    id: `edit-question-${review.answerId ? review.answerId : review.answerChannel.answer_id}`,
                    href: review.link,
                    target: '_blank'
                  },
                "
              ></fc-button>
              <!-- /ko -->
              <div class="spacer"></div>
            </div>
            <review-questions
              params="review: review,onChildEvent: handleChildEvent"
            ></review-questions>
            <!-- ko if: activeQuestion() && (!activeQuestion().isDeleted || activeQuestion().hasAnswer) && (activeQuestion().type == 1 || activeQuestion().type == 2 || activeQuestion().type == 3 || activeQuestion().type == 5 || activeQuestion().type == 6 || activeQuestion().type == 7 || activeQuestion().type == 11 || (activeQuestion().type == 12 && activeQuestion().set_variants == 0) || activeQuestion().type == 14 || activeQuestion().type == 15) -->
            <fc-button
            style="margin-top: 20px;"
              params="
                  label: 'Редактировать ответ',
                  mode: 'text',
                  color: 'primary',
                  icon: { name: 'pencil', color: 'secondary' },
                  click: function(){onReviewEditor()},
                "
            ></fc-button>
            <!-- /ko -->
            <!-- <fc-button
                params="
                    color: 'success',
                    size: 'xl',
                    icon: 'check',
                    click: function() { $parent.submitMailingFrequency() },
                "
            ></fc-button> -->
          </div>
        </div>
        <!-- ko if: review.complaint !== null -->
        <div class="container">
          <div class="review-details-modal__complaint review-complaint">
            <span
              class="review-complaint__title"
              data-bind="text: _t('Жалоба')"
            ></span>
            <!-- ko if: review.complaint.text !== null -->
            <span
              class="review-complaint__text"
              data-bind="text: review.complaint.text"
            ></span>
            <!-- /ko -->
            <!-- ko if: review.complaint.photoUrls.length > 0 -->
            <div class="review-complaint__photos">
              <!-- ko foreach: review.complaint.photoUrls -->
              <img
                class="review-complaint__photo"
                data-bind="
                                attr: { src: $data },

                                fancybox: {
                                  urls: $parent.review.complaint.photoUrls,
                                  caption: $parent.getComplaintFancyboxCaption.bind($parent),
                                  index: $index()
                                }
                            "
              />
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
        </div>
        <!-- /ko -->

        <!-- ko if: review.processing && !window.CURRENT_USER.watcher -->
        <hr class="mt-30p d-none d-md-block" />
        <!-- /ko -->

        <div class="container">
          <!-- ko if: review.processing -->
            <review-processing
              params="
                ref: processingForm,
                review: review,
                blocked: blocked,
                executorMode: executorMode,
              "
              data-bind="
                event: {
                  submit: function($data, event, data) {
                    onSubmit(data)
                  }
                }
              "
            ></review-processing>
          <!-- /ko -->
        </div>
      </div>
    </div>

    <div
      class="tab-pane pt-20p pt-md-30p h-100"
      data-bind="attr: {
        id: 'nav-history-' + id,
        'aria-labelledby': 'nav-history-tab-' + id
      }"
      role="tabpanel"
    >
      <foquz-client-history
        params="ref: history"
        data-bind="event: {
            reviewClick: function(_, e, reviewId) { openReview(reviewId) }
          }"
      ></foquz-client-history>
    </div>
  </div>

  <!-- ko if: !blocked && !historyOpened() && !window.CURRENT_USER.watcher -->
  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="d-flex justify-content-between mx-3">
      <!-- ko if: CURRENT_USER.admin && enableActions -->
      <button
        class="f-btn"
        type="button"
        data-bind="dropdown, dropdownMode: 'button'"
      >
        Действия
        <span class="f-btn-append-section">
          <foquz-icon params="icon: 'arrow-bottom'"></foquz-icon>
        </span>
        <template>
          <div class="tippy-list tippy-list-ansver-actions">
            <!-- ko if: false -->
            <a
              class="tippy-list__item"
              href="#"
              data-bind="
                click: function() {
                  printReview();
                },
              "
            >
              <span>Распечатать анкету</span>
            </a>
            <!-- /ko -->
            <a
              class="tippy-list__item"
              href="#"
              data-bind="
                click: function() {
                  printReview();
                },
              "
            >
              <span>Распечатать анкету</span>
            </a>
            <a
              class="tippy-list__item position-relative"
              href="#"
              data-bind="
                click: function() {
                  printReview(true);
                },
                css: {disabled: !review.dictionary_id}
              "
            >
              <span
                >Распечатать анкету<br />с группировкой<br />по
                справочнику</span
              >
              <svg
                class="position-absolute beta-icon"
                width="7"
                height="13"
                viewBox="0 0 7 13"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3.5 5.00537L3.25043 4.03701C2.76271 4.16271 2.44459 4.6315 2.50794 5.13116C2.57129 5.63081 2.99635 6.00537 3.5 6.00537V5.00537ZM0 12C0 12.5523 0.447715 13 1 13C1.55228 13 2 12.5523 2 12H0ZM2 3.00269C2 2.44765 2.44898 2 3 2V0C1.34188 0 0 1.34561 0 3.00269H2ZM3 2C3.55102 2 4 2.44765 4 3.00269H6C6 1.34561 4.65812 0 3 0V2ZM7 7.50873C7 5.57514 5.43426 4.00537 3.5 4.00537V6.00537C4.32716 6.00537 5 6.67718 5 7.50873H7ZM0 7.50873C0 9.44231 1.56574 11.0121 3.5 11.0121V9.01208C2.67284 9.01208 2 8.34027 2 7.50873H0ZM3.5 11.0121C5.43426 11.0121 7 9.44231 7 7.50873H5C5 8.34027 4.32716 9.01208 3.5 9.01208V11.0121ZM0 3.00269V7.50873H2V3.00269H0ZM0 7.50873V12H2V7.50873H0ZM4 3.00269C4 3.50659 3.6458 3.93512 3.25043 4.03701L3.74957 5.97373C5.07943 5.63099 6 4.36513 6 3.00269H4Z"
                  fill="#37A74A"
                />
              </svg>
            </a>
            <a
              class="tippy-list__item tippy-list__item--danger"
              href="#"
              data-bind="
                click: function() {
                  deleteReview();
                },
              "
            >
              Удалить анкету
            </a>
          </div>
        </template>
      </button>
      <!-- /ko -->
      <!-- ko ifnot: CURRENT_USER.admin && enableActions -->
      <button
        class="f-btn"
        type="button"
        data-bind="dropdown, dropdownMode: 'button'"
      >
        Действия
        <span class="f-btn-append-section">
          <foquz-icon params="icon: 'arrow-bottom'"></foquz-icon>
        </span>
        <template>
          <div class="tippy-list tippy-list-ansver-actions">
            <!-- ko if: false -->
            <a
              class="tippy-list__item"
              href="#"
              data-bind="
                click: function() {
                  printReview();
                },
              "
            >
              <span>Распечатать анкету</span>
            </a>
            <!-- /ko -->
            <a
              class="tippy-list__item"
              href="#"
              data-bind="
                click: function() {
                  printReview();
                },
              "
            >
              <span>Распечатать анкету</span>
            </a>
            <a
              class="tippy-list__item position-relative"
              href="#"
              data-bind="
                click: function() {
                  console.log(review)
                  printReview(true);
                },
                css: {disabled: !review.dictionary_id}
              "
            >
              <span
                >Распечатать анкету<br />с группировкой<br />по
                справочнику</span
              >
              <svg
                class="position-absolute beta-icon"
                width="7"
                height="13"
                viewBox="0 0 7 13"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3.5 5.00537L3.25043 4.03701C2.76271 4.16271 2.44459 4.6315 2.50794 5.13116C2.57129 5.63081 2.99635 6.00537 3.5 6.00537V5.00537ZM0 12C0 12.5523 0.447715 13 1 13C1.55228 13 2 12.5523 2 12H0ZM2 3.00269C2 2.44765 2.44898 2 3 2V0C1.34188 0 0 1.34561 0 3.00269H2ZM3 2C3.55102 2 4 2.44765 4 3.00269H6C6 1.34561 4.65812 0 3 0V2ZM7 7.50873C7 5.57514 5.43426 4.00537 3.5 4.00537V6.00537C4.32716 6.00537 5 6.67718 5 7.50873H7ZM0 7.50873C0 9.44231 1.56574 11.0121 3.5 11.0121V9.01208C2.67284 9.01208 2 8.34027 2 7.50873H0ZM3.5 11.0121C5.43426 11.0121 7 9.44231 7 7.50873H5C5 8.34027 4.32716 9.01208 3.5 9.01208V11.0121ZM0 3.00269V7.50873H2V3.00269H0ZM0 7.50873V12H2V7.50873H0ZM4 3.00269C4 3.50659 3.6458 3.93512 3.25043 4.03701L3.74957 5.97373C5.07943 5.63099 6 4.36513 6 3.00269H4Z"
                  fill="#37A74A"
                />
              </svg>
            </a>
          </div>
        </template>
      </button>
      <!-- /ko -->
      <div class="d-flex">
        <button
          type="button"
          class="f-btn"
          data-bind="
            click: function() {
              $dialog.hide();
            },
          "
        >
          <foquz-icon params="icon: 'bin'" class="f-btn-prepend"></foquz-icon>
          <!-- ko text: _t('Отменить') -->
          <!-- /ko -->
        </button>
        <button
          type="submit"
          class="f-btn f-btn-success"
          data-bind="
            click: function() {
              submit();
            },
          "
        >
          <foquz-icon params="icon: 'save'" class="f-btn-prepend"></foquz-icon>
          <!-- ko text: _t('Сохранить') -->
          <!-- /ko -->
        </button>
      </div>
    </div>

    <success-message params="show: showSaveMessage"></success-message>
  </div>
  <!-- /ko -->

  <!-- /ko -->
</sidesheet>

<template id="statistics-details-modal-dialog-slide-template">
  <!-- ko template: { afterRender: onInit } -->
  <!-- ko template: { nodes: $componentTemplateNodes } -->
  <!-- /ko -->

  <!-- ko if: $parent.file_text -->
  <span
    class="statistics__details-modal-dialog-slider-index"
    data-bind="text: $parent.file_text"
  ></span>
  <!-- /ko -->

  <div class="statistics__details-modal-dialog-slider-paginator">
    <button
      class="statistics__details-modal-dialog-slider-paginator-button statistics__details-modal-dialog-slider-paginator-button--prev"
      type="button"
    ></button>

    <button
      class="statistics__details-modal-dialog-slider-paginator-button statistics__details-modal-dialog-slider-paginator-button--next"
      type="button"
    ></button>

    <div class="statistics__details-modal-dialog-slider-counter">
      <!-- ko text: index + 1 -->
      <!-- /ko -->/
      <!-- ko text: count -->
      <!-- /ko -->
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="statistics-details-modal-dialog-slider-template">
  <!-- ko template: { afterRender: onInit } -->
  <div
    class="swiper-wrapper"
    data-bind="descendantsComplete: $component.onChildrenInit.bind($component)"
  >
    <!-- ko template: { nodes: $componentTemplateNodes } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
</template>
