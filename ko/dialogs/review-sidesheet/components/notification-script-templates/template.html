<!-- ko template: { afterRender: $component.onInit.bind($component) } -->
<div>
  <!-- ko if: templates().length -->
  <div class="ns-templates__content"
       data-bind="css: {
        'is-invalid': showErrors() && selectedTemplate() && !selectedTemplate().isValid()
    }">
    <div class="ns-templates-list">
      <div class="ns-templates-list__wrapper">
        <div class="ns-templates-list__container">
          <!-- ko foreach: { data: templates, as: 'template' } -->
          <div class="ns-templates-list__item"
               data-bind="
                                text: template.name,
                                click: function() { $component.selectTemplate(template); },
                                css: { 'ns-templates-list__item--selected': $component.selectedTemplate() === template },
                            ">
          </div>
          <!-- /ko -->
        </div>

        <button class="btn ns-templates-list__button ns-templates-list__backward-button"></button>
        <button class="btn ns-templates-list__button ns-templates-list__forward-button"></button>
      </div>
    </div><!-- .ns-templates-list -->

    <!-- ko if: selectedTemplate() -->
    <div class="ns-templates-editor">
      <!-- ko if: selectedTemplate().isHTML -->
      <div class="tinymce-wrapper editor"
           data-bind="tinymce: message, tinymceIsEmpty: selectedTemplate().isEmpty, element: elements.htmlEditor">
        <textarea class="ns-templates-editor__field editor__field"></textarea>
      </div>
      <!-- /ko -->

      <!-- ko ifnot: selectedTemplate().isHTML -->
      <div data-bind="basicEditor, element: elements.basicEditor">
        <textarea class="ns-templates-editor__field form-control"
                  data-bind="textInput: message"></textarea>
      </div>
      <!-- /ko -->

      <!-- ko ifnot: selectedTemplate().isDeleted -->
      <div class="ns-templates-editor__footer">
        <button class="btn btn-link"
                data-bind="click: reset, text: _t('answers', 'Шаблон по умолчанию')"></button>
      </div>
      <!-- /ko -->
    </div><!-- .ns-templates-editor -->
    <!-- /ko -->
  </div><!-- .ns-templates__content -->

  <!-- ko template: {
        foreach: showErrors() && selectedTemplate() && !selectedTemplate().isValid(),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
  <div class="form-error"
       data-bind="text: $parent.selectedTemplate().error"></div>
  <!-- /ko -->

  <!-- ko if: selectedTemplate() -->
  <div class="ns-templates__footer">
    <div class="ns-templates__variables">
      <!-- ko foreach: variables -->
      <div class="ns-templates__variable"
           data-bind="text: $data.text, click: function() {
                    $component.setVariable($data.value);
                }"></div>
      <!-- /ko -->

      <div class="ns-templates__variable"
           data-bind="click: function(_, e) {
                setPromocode(selectedTemplate().promocode);
            }">
        <!-- ko text: selectedTemplate().promocode.label  -->
        <!-- /ko -->
      </div>
    </div>
  </div><!-- .ns-templates__footer -->
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko ifnot: templates().length -->
  <div class="color-service" data-bind="text: _t('answers', 'Нет шаблонов')"></div>
  <!-- /ko -->
</div>
<!-- /ko -->
