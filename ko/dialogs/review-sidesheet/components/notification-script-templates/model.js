import { DialogsModule } from 'Utils/dialogs-module';
import 'Dialogs/promocode-dialog';

export class ViewModel {
  constructor(params, element) {
    this.$element = $(element);

    DialogsModule(this);

    this.templates = params.templates;
    this.root = params.root;
    this.message = ko.observable('');

    this.showErrors = params.showErrors;

    this.showErrors.subscribe((v) => {
      if (v) {
        let invalidTemplate = this.templates().find((t) => !t.isValid());
        if (invalidTemplate) {
          this.selectedTemplate(invalidTemplate);
        }
      }
    });

    this.selectedTemplate = ko.observable();
    this.selectedTemplate.subscribe((v) => {
      if (v) this.message(v.message());
    });
    this.message.subscribe((v) => {
      if (this.selectedTemplate()) {
        this.selectedTemplate().message(v);
      }
    });

    if (this.templates().length) this.selectedTemplate(this.templates()[0]);

    this.isAutoPoll = params.isAuto;

    const variables = [
      { text: _t('ФИО'), value: `{ФИО}` },
      {
        text: _t('Номер заказа'),
        value: '{№}',
        onlyAuto: true
      },
      {
        text: _t('Дата/время заказа'),
        value: `{Дата/время заказа}`,
        onlyAuto: true
      },
      {
        text: _t('Ссылка на опрос'),
        value:
          '<a target="_blank" style="display: inline-block; align-items: center; justify-content: center; width: 150px; height: 40px; line-height: 40px; text-decoration: none; background: #2E2F31; border-radius: 100px; font-family: Roboto, Arial, sans-serif; font-style: normal; font-weight: bold; font-size: 15px; text-align: center; color: #FFFFFF; outline: none; box-shadow: none; border: none" href="{Ссылка}">Пройти опрос</a>'
      },
      {
        text: _t('QR-код'),
        value: '{QR}',
        except: ['Push']
      },
      {
        text: _t('Отписаться от рассылки'),
        value: `<a class="unsubscribe-link">Отписаться от рассылки</a>`,
        channels: ['Email', 'Push']
      }
    ].filter((v) => {
      return this.isAutoPoll || !v.onlyAuto;
    });

    this.elements = {
      htmlEditor: ko.observable(null),
      basicEditor: ko.observable(null)
    };

    this.variables = ko.pureComputed(() => {
      const template = this.selectedTemplate();
      if (!template) return [];
      return variables.filter((v) => {
        if (v.channels) return v.channels.includes(template.channel);
        if (v.except) return !v.except.includes(template.channel);
        return true;
      });
    });

    this.selectTemplate = (template) => {
      if (this.selectedTemplate() !== template) {
        this.selectedTemplate(template);
      }
    };

    this.setVariable = (variable) => {
      if (this.elements.htmlEditor()) {
        $(this.elements.htmlEditor()).trigger('set.variable', variable);
      }

      if (this.elements.basicEditor()) {
        $(this.elements.basicEditor()).trigger('set.variable', variable);
      }
    };

    this.setPromocode = (codeModel, button) => {
      this.openDialog({
        name: 'promocode-dialog',
        params: {
          promocode: codeModel
        },
        events: {
          submit: () => {
            this.setVariable(`{${_t('Промокод')}}`);
          }
        }
      });

      // this.root.modalOpens.push({
      //   dialogTemplateName: 'set-promocode-modal-dialog-template',
      //   data: {
      //     promocode: codeModel
      //   },
      //   close: (res) => {
      //     if (res) {
      //       this.setVariable(`{${_t('Промокод')}}`);
      //     }
      //   }
      // });
    };

    this.reset = () => {
      if (this.selectedTemplate()) {
        this.selectedTemplate()
          .reset()
          .then(() => {
            this.message(this.selectedTemplate().message());
          });
      }
    };

    this.initializing = ko.observable(true);
  }

  onInit(el) {
    const scrollingSpeed = 300;
    const scrollingFrequency = 30;

    const $element = this.$element;

    let timerId = null;
    let scrollingState = 'stop';

    function stopScrolling() {
      if (scrollingState !== 'stop') {
        clearInterval(timerId);
        scrollingState = 'stop';
      }
    }

    $(el)
      .hide()
      .fadeIn(200, () => {
        const $templates = $element.find('.ns-templates-list');
        const $templatesWrapper = $element.find('.ns-templates-list__wrapper');
        const $templatesContainer = $element.find(
          '.ns-templates-list__container'
        );
        const $backwardButton = $element.find(
          '.ns-templates-list__backward-button'
        );
        const $forwardButton = $element.find(
          '.ns-templates-list__forward-button'
        );

        function updateTemplates() {
          $templates.toggleClass(
            'ns-templates-list--has-backward-scroll',
            $templatesWrapper.scrollLeft() > 0
          );
          $templates.toggleClass(
            'ns-templates-list--has-forward-scroll',
            $templatesWrapper.scrollLeft() + $templatesWrapper.width() <
              $templatesContainer.width()
          );
        }

        function scrollTemplatesWrapper(offset) {
          $templatesWrapper.scrollLeft($templatesWrapper.scrollLeft() + offset);
          updateTemplates();
        }

        function startScrollingBackward() {
          if (scrollingState !== 'scrollBackward') {
            stopScrolling();

            timerId = setInterval(() => {
              scrollTemplatesWrapper(-scrollingSpeed / scrollingFrequency);
            }, 1000 / scrollingFrequency);

            scrollingState = 'scrollBackward';
          }
        }

        function startScrollingForward() {
          if (scrollingState !== 'scrollForward') {
            stopScrolling();

            timerId = setInterval(() => {
              scrollTemplatesWrapper(scrollingSpeed / scrollingFrequency);
            }, 1000 / scrollingFrequency);

            scrollingState = 'scrollForward';
          }
        }

        $backwardButton.on('mousedown', () => startScrollingBackward());
        $backwardButton.on('mouseup', () => stopScrolling());

        $forwardButton.on('mousedown', () => startScrollingForward());
        $forwardButton.on('mouseup', () => stopScrolling());

        $(window).on('resize', updateTemplates);

        updateTemplates();

        this.initializing(false);
      });
  }
}
