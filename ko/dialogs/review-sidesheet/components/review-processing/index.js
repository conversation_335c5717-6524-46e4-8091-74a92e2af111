import './style.less';
import 'Components/input/select/fines-select';
import 'Components/input/select/employees-select';
import '../notification-script-templates';
import "@/presentation/views/fc-processing-history-button";
import { ViewModel } from './model';
import html from './template';

ko.components.register('review-processing', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('review-processing');
      return new ViewModel(params, element);
    }
  },
  template: html
});
