import { FoquzComponent } from "Models/foquz-component";
import { DialogsModule } from "Utils/dialogs-module";
import "Dialogs/promocode-dialog";
import { DiscountPool } from "@/entities/models/discount-pool";
import { ReviewProcessingEvent } from "@/utils/events/review";

window.currentUserId =
  window.currentUserId || (window.CURRENT_USER ? window.CURRENT_USER.id : "");

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);
    this.review = params.review;
    this.blocked = params.blocked;
    this.executorMode = params.executorMode;

    DialogsModule(this);

    this.isRequestProcessingEnabled = ko.computed(
      () => this.review.isRequestProcessingEnabled
    );

    const modelThis = this;
    this.isSubmitted = ko.observable(false).extend({ notify: "always" });

    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.directories = {
      reasons: new Directory("dictionaries/reasons?all=1"),
      compensations: new Directory("dictionaries/compensations?all=1"),
      users: new Directory("user"),
      statuses: new Directory("answer-processing/statuses"),
      scripts: new Directory("notification-scripts?all=1"),
      moderators: new Directory(
        "answers/moderators?answerId=" + this.review.id
      ),
      executors: new Directory(
        "answers/executors?answerId=" + this.review.id
      ),
    };

    // TODO:
    // отображать текущий сценарий даже если он был удален

    Object.keys(this.directories).forEach((key) => {
      if (["reasons", "compensations"].includes(key)) {
        this.directories[key].load("force");
        return;
      }
      this.directories[key].loaded() ? null : this.directories[key].load();
    });

    this.commonFormError = ko.observable("");

    this.init();
  }

  init() {
    const modelThis = this;
    const status = ko.observable("" + this.review.processingStatus() || "0");

    this.statusName = ko.pureComputed(() => {
      let _status = status();
      if (_status == "0") return "new";
      if (_status == "1") return "in-process";
      if (_status == "2") return "delayed";
      if (_status == "3") return "completed";
      if (_status == "4") return "handle";
    });

    this.showExecutor = ko.pureComputed(() => {
      let statusName = this.statusName();
      return statusName !== "new";
    });

    this.showProcessUp = ko.pureComputed(() => {
      let statusName = this.statusName();
      return statusName !== "delayed";
    });

    this.showDelayedUp = ko.pureComputed(() => {
      let statusName = this.statusName();
      return statusName === "delayed";
    });

    this.showReason = ko.pureComputed(() => {
      let statusName = this.statusName();
      return statusName !== "new";
    });

    this.showEmployee = ko.pureComputed(() => {
      let statusName = this.statusName();
      return statusName !== "new";
    });

    this.showFine = ko.pureComputed(() => {
      let statusName = this.statusName();
      return statusName !== "new";
    });

    this.showCompensation = ko.pureComputed(() => {
      let statusName = this.statusName();
      return statusName !== "new";
    });

    this.showScript = ko.pureComputed(() => {
      let statusName = this.statusName();
      return statusName !== "new" && !!this.review.contactId;
    });

    this.isCompleted = ko.pureComputed(() => {
      return this.review.processingStatus() === 3;
    });

    const comment = ko.observable("");
    const executorComment = ko.observable("");

    const employeeId = ko.observable("");

    const reasonId = ko.observable("").extend({
      required: {
        message: _t("Обязательное поле"),
        onlyIf: () => {
          return false;
        },
      },
    });
    const fineId = ko.observable("");

    this.fineData = ko.observable(null);

    const fineAmount = ko.observable("").extend({
      required: {
        message: _t("Обязательное поле"),
        onlyIf: () => {
          return this.fineData() && this.fineData().hrm_type == 1;
        },
      },
    });

    this.fineData.subscribe((v) => {
      if (!v && v !== 0) {
        fineAmount("");
        return;
      }
      if (v.first) return;
      fineAmount(v.amount);
    });

    const compensationId = ko.observable("");
    const proccessUp = ko.observable("").extend({
      validation: {
        validator: (v) => {
          if (!v) return true;
          return moment(v, "DD.MM.YYYY").format() !== "Invalid date";
        },
        message: _t("main", "Неверный формат параметра «{param}»", {
          param: _t("Дата"),
        }),
      },
    });

    const executorId = ko.observable("").extend({
      required: {
        message: _t("Обязательное поле"),
        onlyIf: (v) => {
          let statusName = this.statusName();

          return ["handle"].includes(statusName);
        },
      },
    });

    this.formatDate = (date) => {
      if (!date) return null;
      date = moment(date, "YYYY-MM-DD");
      if (!date.isValid) return null;
      return date.format("DD.MM.YYYY");
    };

    this.directories.executors.onLoad(() => {
      if (executorId()) {
        let data = this.directories.executors.getById(executorId());
        if (!data) executorId("");
      }
    });

    const moderatorId = ko.observable("").extend({
      required: {
        message: _t("Обязательное поле"),
        onlyIf: () => {
          return true;
        },
      },
    });

    const delayedUp = ko.observable("").extend({
      required: {
        message: _t("Обязательное поле"),
        onlyIf: () => {
          return ["delayed"].includes(this.statusName());
        },
      },
      validation: {
        validator: (v) => {
          if (!v) return true;
          return moment(v, "DD.MM.YYYY").format() !== "Invalid date";
        },
        message: _t("main", "Неверный формат параметра «{param}»", {
          param: _t("Дата"),
        }),
      },
    });

    const notificationScript = ko.observable("");
    this.openNotificationSelect = ko.observable(false);

    this.notificationScriptTemplatesModel = new (function (root) {
      this.list = ko.observableArray([]);

      this.blocked = ko.observable(false);
      this.loaded = ko.observable(true);
      this.loading = ko.observable(false);

      function TemplateModel(templateData, isScriptDeleted) {
        this.id = templateData.id || 0;
        this.channel = templateData.channel_name;
        this.name = templateData.name;

        this.defaultType = templateData.default_type; // 1 - канал, 2 - повтор
        this.defaultChannel = templateData.default_channel_id;

        this.isHTML = ["Email", "Push"].includes(this.channel);
        this.isEmpty = ko.observable(false);

        this.isDeleted = isScriptDeleted || !this.defaultChannel;

        const validation = this.isHTML
          ? {
              validation: {
                validator: () => false,
                message: _t("Обязательное поле"),
                onlyIf: () => this.isEmpty(),
              },
            }
          : {
              required: {
                message: _t("Обязательное поле"),
              },
            };

        this.message = ko.observable(templateData.text).extend(validation);

        this.isValid = ko.pureComputed(() => {
          return this.message.isValid();
        });
        this.error = this.message.error;

        this.update = (data) => {
          this.message(data.text);
        };

        this.reset = () => {
          return new Promise((resolve, reject) => {
            $.ajax({
              url: `${APIConfig.baseApiUrlPath}notification-scripts/reset-default?access-token=${APIConfig.apiKey}`,
              method: "GET",
              data: {
                type: this.defaultType,
                id: this.defaultChannel,
              },
              success: (data) => {
                this.update(data);
                resolve();
              },
            });
          });
        };

        this.promocode = new DiscountPool({
          poolId: templateData.pool_id,
          name: templateData.code,
        });

        this.getData = () => {
          return {
            id: this.id,
            name: this.name,
            channel_name: this.channel,
            delay: templateData.delay,
            delay_days: templateData.delay_days,
            sender: templateData.sender,
            sender_name: templateData.sender_name,
            image: templateData.image,
            subject: templateData.subject,
            text: this.message(),
            //code: this.promocode.name(),
            pool_id: this.promocode.poolId(),
            default_type: this.defaultType,
            default_channel_id: this.defaultChannel,
          };
        };
      }

      this.load = (scriptId) => {
        if (this.blocked()) return;

        this.loaded(false);
        this.loading(true);

        const delay = new Promise((res) => {
          setTimeout(() => {
            res();
          }, 600);
        });

        // Получение дефолтных шаблонов для сценария
        return new Promise((resolve, reject) => {
          $.ajax({
            url: `${APIConfig.baseApiUrlPath}notification-scripts/default-channels?access-token=${APIConfig.apiKey}`,
            method: "GET",
            data: {
              id: scriptId,
            },
            success: (data) => {
              this.list(data.map((t) => new TemplateModel(t)));

              resolve(data);
            },
            error: (response) => {
              const data = response.responseJSON;
              console.error(data);
            },
            complete: () => {
              delay.then(() => {
                this.loading(false);
                this.loaded(true);
              });
            },
          });
        });
      };

      this.setDefault = (templates, deleted) => {
        this.list(templates.map((t) => new TemplateModel(t, deleted)));
      };

      this.isValid = () => {
        return !this.list().some((t) => !t.isValid());
      };

      this.getData = () => {
        return this.list().map((templateModel) => templateModel.getData());
      };
    })(this);

    this.deletedNotificationScript = ko.observable(null);

    this.elements = {
      scriptsSelect: ko.observable(null),
      reasonsSelect: ko.observable(null),
      compensationsSelect: ko.observable(null),
    };

    notificationScript.subscribe((v) => {
      // Скрыть в селекте установленный для анкеты сценарий, если он был удален
      if (this.deletedNotificationScript()) {
        this.deletedNotificationScript(null);
        $(this.elements.scriptsSelect()).trigger("change");
      }
      if (v) this.notificationScriptTemplatesModel.load(v);
    });

    this.formData = ko.validatedObservable({
      status,
      comment,
      executorComment,
      employeeId,
      reasonId,
      fineId,
      fineAmount,
      compensationId,
      executorId,
      moderatorId,
      proccessUp,
      delayedUp,
      notificationScript,
    });

    this.directories.reasons.onLoad(() => {
      $(this.elements.reasonsSelect())
        .val(this.formData().reasonId())
        .trigger("change");
    });

    this.directories.compensations.onLoad(() => {
      $(this.elements.compensationsSelect())
        .val(this.formData().compensationId())
        .trigger("change");
    });

    this.needNotificationScript = ko.pureComputed(() => {
      return this.statusName() !== "new" && !!this.review.contactId;
    });

    this.setDefault = () => {
      const actualStatus = "" + this.review.processingStatus() || "0";
      this.formData().status(actualStatus);

      if (this.review.moderatorId()) {
        this.formData().moderatorId(this.review.moderatorId());
      } else if (this.review.defaultModeratorId()) {
        this.formData().moderatorId(this.review.defaultModeratorId());
      } else {
        this.formData().moderatorId(window.currentUserId);
      }

      if (this.review.executorId()) {
        this.formData().executorId(this.review.executorId());
      } else if (this.review.defaultExecutorId()) {
        this.formData().executorId(this.review.defaultExecutorId());
      }

      this.formData().comment(this.review.processing.comment || "");
      this.formData().executorComment(
        this.review.processing.executor_comment || ""
      );

      let processUp = "";
      if (this.review.processing.process_up) {
        let date = moment(this.review.processing.process_up, "YYYY-MM-DD");
        if (date.isValid) processUp = date.format("DD.MM.YYYY");
      }
      this.formData().proccessUp(processUp);

      let delayedUp = "";
      if (this.review.processing.delayed_up) {
        let date = moment(this.review.processing.delayed_up, "YYYY-MM-DD");
        if (date.isValid) delayedUp = date.format("DD.MM.YYYY");
      }
      this.formData().delayedUp(delayedUp);
      this.formData().employeeId(this.review.processing.employee_id || "");
      this.formData().reasonId(this.review.processing.reason_id || "");

      this.formData().fineId(this.review.processing.fine_id || "");
      this.fineData({
        id: this.review.processing.fine_id,
        amount: this.review.processing.fine_amount,
        hrm_type: this.review.processing.fineType,
        title: this.review.processing.fineName,
      });

      this.formData().compensationId(
        this.review.processing.compensation_id || ""
      );

      this.notificationScriptTemplatesModel.blocked(true);
      this.formData().notificationScript(
        this.review.processing.notification_script_id
          ? "" + this.review.processing.notification_script_id
          : ""
      );

      let scriptData = this.review.processing.notificationScript;
      if (scriptData && scriptData.deleted) {
        this.deletedNotificationScript({
          id: scriptData.id,
          name: scriptData.name,
        });
      }

      this.notificationScriptTemplatesModel.setDefault(
        this.review.processing.answerNotifications || [],
        scriptData && scriptData.deleted
      );
      this.notificationScriptTemplatesModel.blocked(false);
    };

    this.setDefault();

    this.notificationScriptTemplatesModel.loaded(true);

    this.reset = function () {
      this.setDefault();
    };

    this.isActiveStatus = (status) => {
      status = parseInt(status);

      if (window.EXECUTOR_MODE) {
        if (this.review.processingStatus() == 4) {
          return status == 3;
        }
        return false;
      }

      switch (this.review.processingStatus()) {
        case 0:
          return true;
        case 3:
          return status === 3;
        default:
          return status !== 0;
      }
    };

    this.userTemplateResult = (state) => {
      if (!state.id) {
        return state.text;
      }
      const userpic =
        $(state.element).data("userpic") || "assets/img/user-placeholder-2.png";
      return $(
        `<span class="user-option">
                        <span class="user-option__icon">
                            <img class="user-option__userpic" src="${userpic}">
                        </span>
                        <span class="user-option__name">${state.text}</span>
                        </span>`
      );
    };

    this.statusTemplateSelection = (state) => {
      return $("<span>")
        .addClass("status-option")
        .addClass("status--" + state.id)
        .toggleClass("disabled", state.disabled)
        .text(state.text);
    };

    this.cancel = function () {
      this.setDefault();
      this.review.fileLoader.clearErrors();
    };

    this.submit = function () {
      this.isSubmitted(true);
      this.commonFormError("");
      this.review.fileLoader.clearErrors();

      const status = this.formData().status();

      if (!this.formData.isValid()) return;

      if (this.needNotificationScript()) {
        if (!this.notificationScriptTemplatesModel.isValid()) return;
      }

      const data = {
        status: status,
      };
      data.executor_id = this.formData().executorId() || "";
      data.moderator_id = this.formData().moderatorId() || "";

      if (this.formData().proccessUp()) {
        let date = moment(this.formData().proccessUp(), "DD.MM.YYYY");
        data.process_up = date.format("YYYY-MM-DD");
      } else {
        data.process_up = "";
      }

      if (this.formData().delayedUp()) {
        let date = moment(this.formData().delayedUp(), "DD.MM.YYYY");
        data.delayed_up = date.format("YYYY-MM-DD");
      } else {
        data.delayed_up = "";
      }

      data.reason_id = this.formData().reasonId() || "";
      data.fine_id = this.formData().fineId() || "";

      data.fine_amount = this.formData().fineAmount();

      data.compensation_id = this.formData().compensationId() || "";
      data.employee_id = this.formData().employeeId() || "";

      data.comment = this.formData().comment() || "";

      data.executor_comment = this.formData().executorComment() || "";

      data.files = this.review.files().map((file) => file.id);

      if (this.needNotificationScript()) {
        if (this.formData().notificationScript()) {
          data.notification_script_id = this.formData().notificationScript();
          data.answerNotifications =
            this.notificationScriptTemplatesModel.getData();
        } else {
          data.notification_script_id = "";
        }
      }

      var _this = this;

      this.review.updateProcessing(
        data,
        function (data) {
          ReviewProcessingEvent.emit({
            id: _this.review.id,
            processingStatus: data.processing.status,
            data: data.processing,
          });

          _this.emitEvent("submit", data);
          modelThis.isSubmitted(false);

          _this.openNotificationSelect(false);
        },
        function (response) {
          const data = response.responseJSON;
          if (data.errors) {
            modelThis.commonFormError(
              Object.keys(data.errors)
                .map((key) => data.errors[key])
                .join("<br>")
            );
          } else {
            modelThis.commonFormError(_t("Что-то пошло не так..."));
          }
        }
      );
    };
  }
}
