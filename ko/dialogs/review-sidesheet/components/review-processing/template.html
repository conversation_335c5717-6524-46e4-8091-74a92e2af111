<!-- ko if: review.processingId && !window.CURRENT_USER.watcher -->
<div
  class="processing-form"
  data-bind="
    dnd: function(files) {
      review.fileLoader.loadFiles(files);
    },
    dndDisabled: review.processingStatus() == 3 || blocked,
  "
>
  <div class="d-flex justify-content-between align-items-center">
    <div
      class="processing-form__title"
      data-bind="text: _t('answers', 'Обработка')"
    ></div>
    <fc-processing-history-button
      params="answerId: review.id, mode: 'poll'"
    ></fc-processing-history-button>
  </div>

  <!-- ko template: {
      foreach: templateIf(!isCompleted(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
    <!-- ko template: {
      foreach: templateIf(commonFormError(), $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
      <div class="form-error d-flex align-items-center">
        <i class="fas fa-exclamation-circle mr-2"></i>
        <span data-bind="html: commonFormError"></span>
      </div>
    <!-- /ko -->
    <div class="processing-form__controls">
      <div class="row">
        <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
          <div class="form-group">
            <label
              class="form-label"
              data-bind="text: _t('answers', 'Статус обработки')"
            ></label>
            <!-- ko if: directories.statuses.loaded() -->
              <div
                class="select2-wrapper"
                data-bind="css: {
                  'is-invalid': formControlErrorStateMatcher(formData().status),
                  'is-valid': formControlSuccessStateMatcher(formData().status),
                }"
              >
                <select
                  data-bind="
                    value: formData().status,
                    valueAllowUnset: true,
                    disable: blocked,
                    lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      templateSelection: statusTemplateSelection,
                      templateResult: statusTemplateSelection,
                    }
                  "
                >
                  <!-- ko foreach: directories.statuses.data -->
                    <option
                      data-bind="
                        value: id,
                        text: name,
                        attr: {value: id},
                      "
                    ></option>
                  <!-- /ko -->
                </select>

                <!-- ko template: {
                  foreach: formControlErrorStateMatcher(formData().status),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: $parent.formData().error()"
                  ></div>
                <!-- /ko -->
              </div>
            <!-- /ko -->
          </div>
        </div>

        <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
          <div class="form-group">
            <label
              class="form-label"
              data-bind="text: _t('answers', 'Модератор')"
            ></label>
            <!-- ko template: {
              forEach: templateIf(directories.moderators.loaded(), $data),
              afterAdd: fadeAfterAddFactory(200),
            } -->
              <div
                class="select2-wrapper"
                data-bind="css: {
                  'is-invalid': formControlErrorStateMatcher(formData().moderatorId),
                  'is-valid': formControlSuccessStateMatcher(formData().moderatorId),
                }"
              >
                <select
                  data-bind="
                    value: formData().moderatorId,
                    valueAllowUnset: true,
                    disable: executorMode || blocked,
                    lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      minimumResultsForSearch: 0,
                      allowClear: true,
                      placeholder: _t('answers', 'Выберите модератора'),
                      templateResult: userTemplateResult,
                    },
                  "
                >
                  <option></option>
                  <!-- ko foreach: directories.moderators.data -->
                    <option
                      data-bind="
                        value: id,
                        text: name,
                        attr: {
                          'data-userpic': avatar,
                          selected: (id == $parent.review.moderatorId) || (id == window.currentUserId),
                        },
                      "
                    ></option>
                  <!-- /ko -->
                </select>
                <!-- ko template: {
                  foreach: formControlErrorStateMatcher(formData().moderatorId),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: $parent.formData().moderatorId.error()"
                  ></div>
                <!-- /ko -->
              </div>
            <!-- /ko -->
          </div>
        </div>

        <!-- ko if: showExecutor -->
          <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Исполнитель')">
              </label>
              <!-- ko template: {
                forEach: templateIf(directories.executors.loaded(), $data),
                afterAdd: fadeAfterAddFactory(200),
              } -->
                <div
                  class="select2-wrapper"
                  data-bind="css: {
                    'is-invalid': formControlErrorStateMatcher(formData().executorId),
                    'is-valid': formControlSuccessStateMatcher(formData().executorId),
                  }"
                >
                  <select
                    data-bind="
                      value: formData().executorId,
                      valueAllowUnset: true,
                      disable: blocked,
                      lazySelect2: {
                        containerCssClass: 'form-control',
                        wrapperCssClass: 'select2-container--form-control', minimumResultsForSearch: 0,
                        allowClear: true,
                        placeholder: _t('answers', 'Выберите исполнителя'),
                        templateResult: userTemplateResult,
                      },
                    "
                  >
                    <option></option>
                    <!-- ko foreach: directories.executors.data -->
                      <option
                        data-bind="
                          value: id,
                          text: name,
                          attr: { 'data-userpic': avatar, selected: id == $parent.formData().executorId }
                        "
                      ></option>
                    <!-- /ko -->
                  </select>
                  <!-- ko template: {
                    foreach: formControlErrorStateMatcher(formData().executorId),
                    afterAdd: fadeAfterAddFactory(200),
                    beforeRemove: fadeBeforeRemoveFactory(200)
                  } -->
                    <div
                      class="form-error"
                      data-bind="text: $parent.formData().executorId.error()"
                    ></div>
                  <!-- /ko -->
                </div>
              <!-- /ko -->
            </div>
          </div>
        <!-- /ko -->

        <!-- ko if: showProcessUp -->
          <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Обработать до')"
              ></label>
              <div
                class="input-group date-input-group"
                data-bind="dateInputGroup"
              >
                <input
                  class="form-control"
                  placeholder="00.00.0000"
                  data-bind="
                    textInput: formData().proccessUp,
                    disable: blocked,
                    periodPicker,
                    periodPickerArrowPosition: { anchor: 'right', offset: -27 },
                    periodPickerSeparator: '-',
                    periodPickerOpens: 'left',
                    periodPickerDrops: 'up',
                    periodPickerSingle: true,
                    css: {
                      'is-invalid': formControlErrorStateMatcher(formData().proccessUp),
                      'is-valid': formControlSuccessStateMatcher(formData().proccessUp),
                    },
                  "
                >
                <i class="date-input-group__icon"></i>
              </div>
              <!-- ko template: {
                foreach: formControlErrorStateMatcher(formData().proccessUp),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
                <div
                  class="form-error"
                  data-bind="text: $parent.formData().proccessUp.error()"
                ></div>
              <!-- /ko -->
            </div>
          </div>
        <!-- /ko -->

        <!-- ko if: showDelayedUp -->
          <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Отложить до')"
              ></label>
              <div
                class="input-group date-input-group"
                data-bind="dateInputGroup"
              >
                <input
                  class="form-control"
                  placeholder="00.00.0000"
                  data-bind="textInput: formData().delayedUp,
                    disable: blocked,
                    periodPicker,
                    periodPickerArrowPosition: { anchor: 'right', offset: -27 },
                    periodPickerSeparator: '-',
                    periodPickerOpens: 'left',
                    periodPickerDrops: 'up',
                    periodPickerSingle: true,
                    css: {
                      'is-invalid': formControlErrorStateMatcher(formData().delayedUp),
                      'is-valid': formControlSuccessStateMatcher(formData().delayedUp),
                    },
                  "
                >
                <i class="date-input-group__icon"></i>
              </div>
              <!-- ko template: {
                foreach: formControlErrorStateMatcher(formData().delayedUp),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
                <div
                  class="form-error"
                  data-bind="text: $parent.formData().delayedUp.error()"
                ></div>
              <!-- /ko -->
            </div>
          </div>
        <!-- /ko -->
      </div>

      <div class="row">
        <!-- ko if: showReason -->
          <div class="col-12 col-md-8 ">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Причина плохого отзыва')"
              ></label>
              <div
                class="select2-wrapper"
                data-bind="css: {
                  'is-invalid': formControlErrorStateMatcher(formData().reasonId),
                  'is-valid': formControlSuccessStateMatcher(formData().reasonId),
                }"
              >
                <select
                  data-bind="
                    value: formData().reasonId,
                    element: elements.reasonsSelect,
                    valueAllowUnset: true,
                    disable: blocked,
                    lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      minimumResultsForSearch: 0,
                      allowClear: true,
                      placeholder: _t('answers', 'Выберите причину'),
                    },
                    event: {
                      change: function() {
                        return true;
                      }
                    }
                  "
                >
                  <option></option>
                  <!-- ko foreach: directories.reasons.data -->
                    <!-- ko if: !deleted || id == $parent.formData().reasonId() -->
                    <option data-bind="value: id, text: title"></option>
                    <!-- /ko -->
                  <!-- /ko -->
                </select>
                <!-- ko template: {
                foreach: formControlErrorStateMatcher(formData().reasonId),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: $parent.formData().reasonId.error()"
                  ></div>
                <!-- /ko -->
              </div>
            </div>
          </div>
        <!-- /ko -->

        <!-- ko if: showEmployee -->
          <div class="col-12 col-md-4">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Сотрудник')"
              ></label>
              <div
                class="select2-wrapper"
                  data-bind="css: {
                  'is-invalid': formControlErrorStateMatcher(formData().employeeId),
                  'is-valid': formControlSuccessStateMatcher(formData().employeeId),
                },
              ">
                <employees-select
                  params="
                    value: formData().employeeId,
                    disabled: blocked,
                    config: {
                      placeholder: _t('answers', 'Выберите сотрудника')
                    }"
                  >
                  <!-- ko if: $parent.review.processing.employee_id -->
                    <option
                      data-bind="
                        value: $parent.review.processing.employee_id,
                        text: $parent.review.processing.employeeName
                      "
                      selected
                    ></option>
                  <!-- /ko -->
                </employees-select>
                <!-- ko template: {
                  foreach: formControlErrorStateMatcher(formData().employeeId),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: $parent.formData().employeeId.error()"
                  ></div>
                <!-- /ko -->
              </div>
            </div>
          </div>
        <!-- /ko -->

        <!-- ko if: showFine -->
          <div class="col-12 col-md-8">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Взыскание или нарушение')"
              ></label>
              <div
                class="select2-wrapper"
                data-bind="css: {
                  'is-invalid': formControlErrorStateMatcher(formData().fineId),
                  'is-valid': formControlSuccessStateMatcher(formData().fineId),
                }"
              >
                <fines-select
                  params="
                    value: formData().fineId,
                    disabled: blocked,
                    selectedOption: fineData,
                    config: { placeholder: _t('answers', 'Выберите нарушение')}
                  "
                >
                  <!-- ko if: $parent.review.processing.fine_id -->
                    <option
                      data-bind="
                        value: $parent.review.processing.fine_id,
                        text: $parent.review.processing.fineName,
                      "
                      selected
                    ></option>
                  <!-- /ko -->
                </fines-select>
                <!-- ko template: {
                foreach: formControlErrorStateMatcher(formData().fineId),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: $parent.formData().fineId.error()"
                  ></div>
                <!-- /ko -->
              </div>
            </div>
          </div>

          <!-- ko if: fineData -->
            <!-- ko if: fineData().hrm_type == 1 -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <label
                    class="form-label"
                    data-bind="text: _t('answers', 'Сумма взыскания, ₽')"
                  ></label>
                  <input
                    type="text"
                    class="form-control text-center"
                    data-bind="
                      textInput: formData().fineAmount,
                      onlyNumbers,
                      css: {
                        'is-invalid': formControlErrorStateMatcher(formData().fineAmount)
                      },
                      disable: blocked
                    "
                  >
                  <validation-feedback
                    params="
                      text: formData().fineAmount.error,
                      show: formControlErrorStateMatcher(formData().fineAmount)
                    "
                  ></validation-feedback>
                </div>
              </div>
            <!-- /ko -->
            <!-- ko if: fineData().hrm_type == 2 -->
            <div class="col-12 col-md-4">
              <div class="form-group">
                <label
                  class="form-label"
                  data-bind="text: _t('answers', 'Баллы за нарушение')"
                ></label>
                <input
                  type="text"
                  class="form-control text-center"
                  data-bind="
                    textInput: formData().fineAmount,
                    onlyNumbers,
                    css: {
                      'is-invalid': formControlErrorStateMatcher(formData().fineAmount)
                    },
                    disable: blocked
                  "
                >
                <validation-feedback
                  params="
                    text: formData().fineAmount.error,
                    show: formControlErrorStateMatcher(formData().fineAmount)
                  "
                ></validation-feedback>
              </div>
            </div>
            <!-- /ko -->
          <!-- /ko -->
        <!-- /ko -->
      </div>

      <div class="row">
        <!-- ko if: showCompensation -->
          <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Компенсация')"
              ></label>
              <div
                class="select2-wrapper"
                data-bind="css: {
                  'is-invalid': formControlErrorStateMatcher(formData().compensationId),
                  'is-valid': formControlSuccessStateMatcher(formData().compensationId),
                }"
              >
                <select
                  data-bind="
                    value: formData().compensationId,
                    element: elements.compensationsSelect,
                    valueAllowUnset: true,
                    disable: blocked,
                    lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      minimumResultsForSearch: 0,
                      allowClear: true,
                      placeholder: _t('answers', 'Выберите компенсацию'),
                    },
                  "
                >
                  <option></option>
                  <!-- ko foreach: directories.compensations.data -->
                    <!-- ko if: !deleted || id == $parent.formData().compensationId() -->
                      <option data-bind="value: id, text: title"></option>
                    <!-- /ko -->
                  <!-- /ko -->
                </select>

                <!-- ko template: {
                  foreach: formControlErrorStateMatcher(formData().compensationId),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: $parent.formData().compensationId.error()"
                  ></div>
                <!-- /ko -->
              </div>
            </div>
          </div>
        <!-- /ko -->

        <!-- Сценарий уведомлений -->
          <!-- ko if: showScript -->
            <div class="col-xs-8 col-sm-8 col-md-8 col-lg-8">
              <div class="form-group">
                <label class="form-label">
                  <!-- ko text: _t('answers', 'Сценарий уведомления') -->
                  <!-- /ko -->
                  <button
                    type="button"
                    class="btn-question"
                    data-bind="
                      tooltip,
                      tooltipText: _t('answers', 'Сценарий уведомления')
                    "
                  ></button>
                </label>

                <!-- ko template: {
                  foreach: templateIf(review.hasNotificationScript() && !openNotificationSelect(), $data),
                  afterAdd: fadeAfterAddFactory(400),
                } -->
                  <div>
                    <div
                      class="f-color-text f-fs-3 mt-2"
                      data-bind="text: review.processing.notificationScript.name"
                    ></div>
                    <a
                      href="javascript:void(0)"
                      class="mt-1 f-color-primary f-fs-1 font-weight-500"
                      data-bind="
                        click: function() {
                          openNotificationSelect(true);
                        },
                        text: _t('Изменить'),
                      "
                    ></a>
                  </div>
                <!-- /ko -->
                <!-- ko template: {
                  foreach: templateIf(!(review.hasNotificationScript() && !openNotificationSelect()), $data),
                  afterAdd: fadeAfterAddFactory(400),
                } -->
                  <!-- ko if: directories.scripts.loaded() -->
                    <div class="select2-wrapper">
                      <select
                        data-bind="
                          element: elements.scriptsSelect,
                          value: formData().notificationScript,
                          valueAllowUnset: true,
                          disable: directories.scripts.data().length == 0 || blocked,
                          lazySelect2: {
                            containerCssClass: 'form-control',
                            wrapperCssClass: 'select2-container--form-control',
                            minimumResultsForSearch: 0,
                            allowClear: true,
                            placeholder: _t('answers', 'Сценарий уведомления по умолчанию'),
                          },
                        "
                      >
                        <option></option>
                        <!-- Для анкеты установлен сценарий, который уже удален -->
                        <!-- ko if: deletedNotificationScript -->
                          <option
                            data-bind="
                              value: deletedNotificationScript().id,
                              text: deletedNotificationScript().name
                            "
                          ></option>
                        <!-- /ko -->
                        <!-- ko foreach: directories.scripts.data -->
                          <option data-bind="text: name, value: id"></option>
                        <!-- /ko -->
                      </select>
                    </div>
                  <!-- /ko -->
                <!-- /ko -->
              </div>
            </div>
            <!-- ko template: {
              foreach: templateIf(formData().notificationScript(), $data),
              afterAdd: slideAfterAddFactory(400),
              beforeRemove: slideBeforeRemoveFactory(400)
            } -->
              <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                <div class="form-group">
                  <label
                    class="form-label"
                    data-bind="text: _t('answers', 'Шаблоны уведомлений')"
                  ></label>
                  <button
                    type="button"
                    class="btn-question"
                    data-bind="tooltip, tooltipText: _t('answers', 'Шаблоны уведомлений')"
                  ></button>
                  <!-- ko template: {
                    foreach: templateIf(!notificationScriptTemplatesModel.loaded(), $data),
                    afterAdd: fadeAfterAddFactory(200),
                  } -->
                    <div class="spinner">
                      <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
                    </div>
                  <!-- /ko -->
                  <!-- ko if: notificationScriptTemplatesModel.loaded -->
                    <div
                      data-bind="
                        component: {
                          name: 'notification-script-templates',
                          params: {
                            templates: notificationScriptTemplatesModel.list,
                            root: $root,
                            showErrors: isSubmitted,
                            isAuto: review.isAuto,
                            disabled: blocked
                          },
                        },
                      "
                    ></div>
                  <!-- /ko -->
                </div>
              </div>
            <!-- /ko -->
          <!-- /ko-->
        <!-- /Сценарий уведомлений -->

        <!-- ko if: executorMode && review.processing.comment -->
          <div class="col-12">
            <div class="form-group">
              <div class="form-label">
                <!-- ko text: _t('answers', 'Комментарий модератора') -->
                <!-- /ko -->
              </div>
              <div class="f-color-text">
                <div data-bind="text: review.processing.comment"></div>
              </div>
            </div>
          </div>
        <!-- /ko -->

        <!-- ko if: !executorMode -->
          <div class="col-12">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Комментарий')"
              ></label>
              <div
                class="chars-counter chars-counter--type_textarea"
                data-bind="charsCounter, charsCounterCount: formData().comment().length"
              >
                <textarea
                  class="form-control"
                  maxlength="500"
                  data-bind="
                    textInput: formData().comment,
                    disable: blocked,
                    css: {
                      'is-invalid': formControlErrorStateMatcher(formData().comment),
                      'is-valid': formControlSuccessStateMatcher(formData().comment),
                    },
                  "
                ></textarea>
                <div class="chars-counter__value"></div>
              </div>
              <!-- ko template: {
                foreach: formControlErrorStateMatcher(formData().comment),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
                <div
                  class="form-error"
                  data-bind="text: $parent.formData().comment.error()"
                ></div>
              <!-- /ko -->
              <div
                class="mt-3"
                data-bind="let: { loaderError: ko.observable('') }"
              >
                <div class="d-flex flex-wrap">
                  <!-- ko foreach: review.files -->
                    <div
                      class="media-wrapper media-wrapper--sm mr-3 mb-3"
                      data-bind="css: {
                        'media-wrapper--loading': $data.loading
                      }"
                    >
                      <!-- ko if: $data.loading -->
                        <div class="media-wrapper__loader">
                          <i class="fa fa-spinner fa-pulse fa-fw"></i>
                        </div>
                      <!-- /ko -->
                      <!-- ko ifnot: $data.loading -->
                        <img
                          data-bind="
                            attr: { src: $data.poster },
                            fancyboxGalleryItem: {
                              gallery: $parent.review.files().map(function(file) {
                                return {
                                  src: file.url()
                                }
                              }),
                              index: $index(),
                            },
                          "
                          alt=""
                        >

                        <!-- ko ifnot: $component.blocked -->
                          <div
                            class="media-wrapper__remove"
                            data-bind="
                              click: function() {
                                $parent.review.removeFile($data);
                              },
                              tooltip,
                              tooltipText: _t('Удалить')
                            "
                          >&times;</div>
                        <!-- /ko -->
                      <!-- /ko -->
                    </div>
                  <!-- /ko -->

                  <!-- список файлов -->
                  <!-- ko ifnot: $component.blocked -->
                    <button
                      class="f-btn f-btn--base d-none d-md-inline-flex"
                      type="button"
                      data-bind="
                        fileLoader: {
                          images: true,
                          videos: true,
                          onLoad: function(file) {
                            loaderError('');
                            review.loadFile(file);
                          },
                          onError: function(error) {
                            loaderError(error);
                          },
                        },
                      "
                    >
                      <span class="f-btn-prepend">
                        <svg-icon params="name: 'clip'"></svg-icon>
                      </span>
                      <span data-bind="text: _t('Прикрепить фото/видео')"></span>
                    </button>
                    <button
                      class="f-btn f-btn--square d-md-none ml-0"
                      type="button"
                      data-bind="
                        fileLoader: {
                          images: true,
                          videos: true,
                          onLoad: function(file) {
                            loaderError('');
                            review.loadFile(file);
                          },
                          onError: function(error) {
                            loaderError(error);
                          },
                        },
                      "
                    >
                      <svg-icon params="name: 'clip'"></svg-icon>
                    </button>
                  <!-- /ko -->
                </div>

                <!-- ko foreach: review.fileLoader.errors -->
                  <file-loader-error params="error: $data"></file-loader-error>
                <!-- /ko -->

                <!-- ko template: {
                  foreach: templateIf(loaderError(), $data),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: loaderError"
                  ></div>
                <!-- /ko -->
              </div>
            </div>
          </div>
        <!-- /ko -->

        <!-- ko if: executorMode -->
          <div class="col-12">
            <div class="form-group">
              <label
                class="form-label"
                data-bind="text: _t('answers', 'Комментарий')"
              ></label>
              <div
                class="chars-counter chars-counter--type_textarea"
                data-bind="charsCounter, charsCounterCount: formData().executorComment().length"
              >
                <textarea
                  class="form-control"
                  maxlength="500"
                  data-bind="
                    textInput: formData().executorComment,
                    disable: isCompleted,
                    css: {
                      'is-invalid': formControlErrorStateMatcher(formData().executorComment),
                      'is-valid': formControlSuccessStateMatcher(formData().executorComment),
                    }
                  "
                ></textarea>
                <div class="chars-counter__value"></div>
              </div>
              <!-- ko template: {
                foreach: formControlErrorStateMatcher(formData().executorComment),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
                <div
                  class="form-error"
                  data-bind="text: $parent.formData().executorComment.error()"
                ></div>
              <!-- /ko -->
              <div
                class="mt-3"
                data-bind="let: { loaderError: ko.observable('') }"
              >
                <div class="d-flex flex-wrap">
                  <!-- ko foreach: review.files -->
                    <div class="media-wrapper media-wrapper--sm mr-3 mb-3">
                      <!-- ko if: $data.loading -->
                        <div class="media-wrapper__loader">
                          <i class="fa fa-spinner fa-pulse fa-fw color-active"></i>
                        </div>
                      <!-- /ko -->
                      <!-- ko ifnot: $data.loading -->
                        <img
                          data-bind="
                            attr: {
                              src: $data.poster
                            },
                            fancyboxGalleryItem: {
                              gallery: $parent.review.files().map(function(file) {
                                return {
                                  src: file.url()
                                }
                              }),
                              index: $index(),
                            }
                          "
                          alt=""
                        >
                        <div
                          class="media-wrapper__remove"
                          data-bind="
                            click: function() {
                              $parent.review.removeFile($data);
                            },
                            fbPopper,
                            title: _t('Удалить')
                          "
                        >&times;</div>
                      <!-- /ko -->
                    </div>
                  <!-- /ko -->
                  <!-- список файлов -->
                  <button
                    class="f-btn f-btn--base d-none d-md-inline-flex"
                    type="button"
                    data-bind="
                      fileLoader: {
                        images: true,
                        videos: true,
                        onLoad: function(file) {
                          loaderError('');
                          review.loadFile(file);
                        },
                        onError: function(error) {
                          loaderError(error);
                        },
                      },
                    "
                  >
                    <span class="f-btn-prepend">
                      <svg-icon params="name: 'clip'"></svg-icon>
                    </span>
                    <span  data-bind="text: _t( 'Прикрепить фото/видео')"></span>
                  </button>
                  <button
                    class="f-btn f-btn--square d-md-none ml-0"
                    type="button"
                    data-bind="
                      fileLoader: {
                        images: true,
                        videos: true,
                        onLoad: function(file) {
                          loaderError('');
                          review.loadFile(file);
                        },
                        onError: function(error) {
                          loaderError(error);
                        },
                      }
                    "
                  >
                    <svg-icon params="name: 'clip'"></svg-icon>
                  </button>
                </div>
                <!-- ko template: {
                  foreach: templateIf(loaderError(), $data),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: loaderError"
                  ></div>
                <!-- /ko -->
              </div>
            </div>
          </div>
        <!-- /ko -->

        <!-- ko if: !executorMode && review.processing.executor_comment -->
          <div class="col-12">
            <div class="form-group">
              <div class="form-label">
                <!-- ko text: _t('answers', 'Комментарий исполнителя') -->
                <!-- /ko -->
              </div>
              <div class="f-color-text">
                <div data-bind="text: review.processing.executor_comment"></div>
              </div>
            </div>
          </div>
        <!-- /ko -->
      </div>
    </div>
  <!-- /ko -->

  <!-- ko template: {
      foreach: templateIf(isCompleted(), $data),
      afterAdd: slideAfterAddFactory(200, 200),
  } -->
    <div class="f-color-text mt-4">
      <div class="row">
        <div class="col-12 col-md-3 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('Статус')"
          ></div>
          <div
            class="processing-status--3"
            data-bind="text: _t('answers', 'Обработана')"
          ></div>
        </div>

        <div class="col-12 col-md-3 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('answers', 'Модератор')"
          ></div>
          <div>
            <!-- ko if: review.processing.moderator_id -->
              <span data-bind="text: review.processing.moderator.name || review.processing.moderator.username"></span>
            <!-- /ko -->
            <!-- ko ifnot: review.processing.moderator_id -->
              &mdash;
            <!-- /ko -->
          </div>
        </div>

        <div class="col-12 col-md-3 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('answers', 'Исполнитель')"
          ></div>
          <div>
            <!-- ko if: review.processing.executor_id -->
              <span data-bind="text: review.processing.executor.name || review.processing.executor.username"></span>
            <!-- /ko -->
            <!-- ko ifnot: review.processing.executor_id -->
              &mdash;
            <!-- /ko -->
          </div>
        </div>

        <div class="col-12 col-md-3 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('answers', 'Обработать до')"
          ></div>
          <div>
            <span data-bind="text: formatDate(review.processing.process_up) || '—'"></span>
          </div>
        </div>
        <div class="col-12 col-md-3 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('answers', 'Причина плохого отзыва')"
          ></div>
          <div>
            <!-- ko if: review.processing.reasonName -->
              <span data-bind="text: review.processing.reasonName"></span>
            <!-- /ko -->
            <!-- ko ifnot: review.processing.reasonName -->
              &mdash;
            <!-- /ko -->
          </div>
        </div>

        <div class="col-12 col-md-3 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('answers', 'Сотрудник')"
          ></div>
          <div>
            <!-- ko if: review.processing.employeeName -->
              <span data-bind="text: review.processing.employeeName"></span>
            <!-- /ko -->
            <!-- ko ifnot: review.processing.employeeName -->
              &mdash;
            <!-- /ko -->
          </div>
        </div>

        <div class="col-12 col-md-3 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('answers', 'Взыскание или нарушение')"
          ></div>
          <div>
            <!-- ko if: review.processing.fineName -->
              <span data-bind="text: review.processing.fineName"></span>
              <!-- ko if: review.processing.fineType == 1 || review.processing.fineType == 2 -->
                <div
                  class="f-fs-1 font-weight-700 f-color-service"
                  data-bind="
                    let: {
                      fineType: review.processing.fineType,
                      fineTypeName: review.processing.fineType == '1'
                        ? _t('answers', 'Взыскание')
                        : _t('answers', 'Нарушение'),
                      hasAmount: review.processing.fine_amount || review.processing.fine_amount === 0,
                      fineAmount: review.processing.fine_amount,
                      fineUnit: review.processing.fineType == '1' ? '₽' : '',
                    }
                  "
                >
                  <!-- ko text: fineTypeName + (hasAmount ? ', ' + fineAmount + fineUnit : '') -->
                  <!-- /ko -->
                </div>
              <!-- /ko -->
            <!-- /ko -->
            <!-- ko ifnot: review.processing.fineName -->
              &mdash;
            <!-- /ko -->
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-md-3 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('answers', 'Компенсация')"
          ></div>
          <div>
            <!-- ko if: review.processing.compensationName -->
              <span data-bind="text: review.processing.compensationName"></span>
            <!-- /ko -->
            <!-- ko ifnot: review.processing.compensationName -->
              &mdash;
            <!-- /ko -->
          </div>
        </div>
        <div class="col-12 col-md-9 mb-4">
          <div
            class="f-fs-2 f-color-service mb-2"
            data-bind="text: _t('answers', 'Сценарий уведомления')"
          ></div>
          <div>
            <!-- ko if: review.processing.notificationScript -->
              <span data-bind="text: review.processing.notificationScript.name"></span>
            <!-- /ko -->
            <!-- ko ifnot: review.processing.notificationScript -->
              &mdash;
            <!-- /ko -->
          </div>
        </div>
      </div>
      <div class="row">
        <!-- ko if: review.processing.comment || review.files().length -->
        <div class="col-12 mt-5 mb-4">
          <div class="f-fs-2 f-color-service mb-2">
            <!-- ko text: _t('answers', 'Комментарий модератора') -->
            <!-- /ko -->
          </div>
          <div>
            <div data-bind="text: review.processing.comment"></div>
          </div>
        </div>
        <!-- /ko -->

        <!-- ko if: review.processing.executor_comment -->
          <div class="col-12 mb-4">
            <div class="f-fs-2 f-color-service mb-2">
              <!-- ko text: _t('answers', 'Комментарий исполнителя') -->
              <!-- /ko -->
            </div>
            <div>
              <div data-bind="text: review.processing.executor_comment"></div>
            </div>
          </div>
        <!-- /ko -->
        <!-- ko if: review.files().length -->
          <div class="col-12">
            <div class="d-flex">
              <!-- ko foreach: review.files -->
                <div class="media-wrapper media-wrapper--sm mr-3 mb-3">
                  <img
                    data-bind="
                      attr: {
                        src: $data.poster
                      },
                      fancyboxGalleryItem: {
                        gallery: $parent.review.files().map(function(file) {
                          return {
                            src: file.url()
                          }
                        }),
                        index: $index(),
                      }
                    "
                    alt=""
                  >
                </div>
              <!-- /ko -->
            </div>
          </div>
        <!-- /ko -->
      </div>
    </div>
  <!-- /ko -->

  <dnd-cover class="ma-n30p">
    <svg
      width="96"
      height="96"
    >
      <use href="#blank-file-icon"></use>
    </svg>
    <div
      class="mt-4"
      data-bind="text: _t('answers', 'Перетащите файлы сюда, чтобы прикрепить их к комментарию')"
    ></div>
    <div
      class="f-fs-1"
      data-bind="text: _t('main', 'Максимальный размер файла — {size} Мб',{size: 5})"
    ></div>
  </dnd-cover>
</div>
<!-- /ko -->
