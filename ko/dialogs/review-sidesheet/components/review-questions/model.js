import { FoquzComponent } from "Models/foquz-component";
import { isFancyboxActive } from "@/utils/fancybox";
import "Dialogs/review-sidesheet";

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);
    console.log("review-questions ViewModel", params);
    this.slider = ko.observable(null);
    this.onChildEvent = params.onChildEvent;
    
    this.review = params.review;
    this.questions = this.review.questions;
    console.log("review-questions this.questions", this.questions());

    this.sortedQuestions = ko.pureComputed(() => {
      let questions = this.questions();
      let notDeleted = questions.filter((q) => !q.isDeleted || q.hasAnswer);
      let deleted = questions.filter((q) => q.isDeleted && !q.hasAnswer);
      return [...notDeleted, ...deleted];
    });

    this.activeQuestion = ko.observable(null);

    document.addEventListener("keydown", (e) => {
      if (isFancyboxActive()) return;

      if (e.keyCode !== 37 && e.keyCode !== 39) return;
      let questions = this.sortedQuestions();
      let question = this.activeQuestion();
      if (!question) return;

      let questionIndex = questions.findIndex((q) => q == question);

      let nextIndex;

      if (e.keyCode == 37) {
        if (questionIndex == 0) return;
        let prevQuestions = questions.slice(0, questionIndex).reverse();

        let tmpIndex = prevQuestions.findIndex(
          (q) => !q.isDeleted && q.hasAnswer
        );

        if (tmpIndex == -1) return;

        nextIndex = questionIndex - tmpIndex - 1;
      }

      if (e.keyCode == 39) {
        let count = questions.length;
        if (questionIndex > count - 2) return;

        let nextQuestions = questions.slice(questionIndex + 1);
        let tmpIndex = nextQuestions.findIndex(
          (q) => !q.isDeleted && q.hasAnswer
        );
        nextIndex = questionIndex + tmpIndex + 1;
      }

      if (nextIndex == questionIndex) return;

      let nextQuestion = this.sortedQuestions()[nextIndex];
      this.activeQuestion(null);
      this.activeQuestion(nextQuestion);
      this.slider().slideTo(nextIndex);
    });

    this.openFirst();
  }

  openFirst() {
    if (window.editedQuestion) {
      const question = this.sortedQuestions().find(
        (q) => q.id == window.editedQuestion
      );
      this.activate(question)
      window.editedQuestion = null
      setTimeout(() => {
        const actveSlide = $('.review-questions__nav-edit').find('.swiper-pagination-bullet-active').attr('aria-label')
        const activeBullet = actveSlide.replace('Go to slide ', '') - 1
        $($('.review-questions__nav-edit-review').find('.swiper-pagination-bullet')[activeBullet]).click()
      }, 500);
      
      return
    }
    let firstQuestionWithAnswer = this.sortedQuestions().find(
      (q) => q.hasAnswer
    );
    if (firstQuestionWithAnswer) this.activate(firstQuestionWithAnswer);
  }

  activate(question, answerId, index) {
    if (answerId && index !== undefined) {
      let link = document.querySelector(`#edit-question-${answerId}`);
      if (link) {
        let url = new URL(link.href);
        url.searchParams.set("view", index + 1);
        link.href = url.href;
      }
    }

    let isSame = this.activeQuestion() == question;
    this.activeQuestion(null);
    if (!isSame) {
      setTimeout(() => {
        this.onChildEvent(question);
        this.activeQuestion(question);
      });
    } else {
      this.onChildEvent(null);
        this.activeQuestion(null);
    }
  }
}
