<!-- ko if: sortedQuestions().length > 0 -->
<div class="review-questions__nav review-questions__nav-edit-review">
  <div
    class="swiper-container"
    data-bind="
        ref: slider,
        swiper: {
          slidesPerView: 'auto',
          slidesPerGroupAuto: true,
          spaceBetween: 12,
          shortSwipes: false,
          touchAngle: 10,

          pagination: {
            el: '.swiper-pagination',
            clickable: true,
          },
        }"
  >
    <div class="swiper-pagination"></div>
    <div class="swiper-wrapper">
      <!-- ko foreach: sortedQuestions -->
      <review-question-preview
        class="swiper-slide"
        params="index: $index(),
         question: $data,
         review: $parent.review,
         active: $parent.activeQuestion() == $data,
         onClick: function() {
            if ($parent.review.answerId) {           
              $parent.activate($data, $parent.review.answerId, $index());
            } else {
              $parent.activate($data, $parent.review.answerChannel.answer_id, $index());
            }
         }"
      ></review-question-preview>
      <!-- /ko -->
    </div>
  </div>
</div>
<!-- /ko -->

<!-- ko template: {
  foreach: templateIf(activeQuestion(), $data),
  afterAdd: slideAfterAddFactory(200),
  beforeRemove: slideBeforeRemoveFactory(200)
} -->
<div class="review-questions__view">
  <review-question
    params="question: activeQuestion(),
              review: review"
  ></review-question>
</div>
<!-- /ko -->
