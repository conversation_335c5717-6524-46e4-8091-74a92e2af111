@import 'Style/breakpoints';

.review-questions-slider {
  display: block;
  position: relative;
  margin-top: -18px;

  .review-questions__nav {
    .swiper-container {
      padding-top: 36px;
    }
  }



  .swiper-pagination {
    position: absolute;
    top: 0px;
    right: 0;
    bottom: auto;
    text-align: right;

    &-bullet {
      width: 12px;
      height: 12px;

      &:only-child {
        display: none;
      }
    }

    .only-mobile({
      display: none;
    });
  }
}

&-paginator {
  display: flex;

  &__page {
    background: #cfd8dc;
    cursor: pointer;
    border-radius: 50%;

    &--active {
      background: #3f65f1;
    }
    &:not(:first-child) {
      margin-left: 12px;
    }
  }
}

.review-question-view-rating {
  margin-right: -8px;
  margin-bottom: 25px;
  flex-wrap: wrap;

  .review-rating {
    margin-left: 8px;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .star-rating {
    margin-left: 8px;
  }

  &__label {
    margin-left: 8px;
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.only-mobile({
  .review-questions {
    margin-top: 18px!important;
  }
  .review-questions-slider {
    .swiper-container {
      padding-top: 18px!important;
      &:before, &:after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 10px;
        z-index: 5;
      }
      &:before {
        left: 0;
        background: linear-gradient(90deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
      }
      &:after {
        right: 0;
        background: linear-gradient(-90deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
      }
    }
  }
});
