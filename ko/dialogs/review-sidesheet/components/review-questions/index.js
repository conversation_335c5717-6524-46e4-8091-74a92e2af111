import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/review-question';
import 'Components/review-question-preview';

ko.components.register('review-questions', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      
      console.log('review-questions father', params);
      element.classList.add('review-questions-slider');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
