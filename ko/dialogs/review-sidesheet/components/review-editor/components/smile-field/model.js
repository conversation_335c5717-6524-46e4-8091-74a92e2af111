import "inputmask/lib/extensions/inputmask.numeric.extensions";

export class ViewModel {
  constructor(params, element) {
    this.field = params.value;
    this.comment = params.comment;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.skipped = params.skipped
    this.isCanged = params.isCanged;
   
    this.items = ko.observableArray(this.question.smiles.map((i, index) => {
      return {
        img: i.smile_url,
        value: index + 1
      }
    }))


    
    
    this.isInvalid = ko.observable(false);
    
    this.disabled = params.disabled;
    this.inited = true

    this.field.subscribe((v) => {
      this.isCanged(true)
      console.log("change filed edit", v);
      if (v !==null) {
        this.skipped(0)
      }
      this.validateField()
    });
    this.comment.subscribe((v) => {
      this.validateField()
    });

    this.skipped.subscribe((v) => {
      if (v) {
        this.field(null)
      }
      this.validateField()
    });
    
  }
  
  validateField() {
    if (ko.isComputed(this.validStep)) {
      return
    }
    if (this.skipped()) {
      this.validStep(true)
    } else {
      const validComment = !this.question.commentRequired || (this.comment() && this.comment().length)
      if (this.question.isRequired) {
        this.validStep(this.field() !== null && validComment)
      } else {
        this.validStep(validComment)
      }
    }
  }
  /* dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  } */
}
