@import 'Style/breakpoints.less';
.smile-scale {
  
  .smile-scale__list {
    display: flex;
    justify-content: start;
  }
  &.smile-scale__list-edit {
    margin-bottom: 30px;
  }
  .smile-scale__item {
    width: 34px;
    height: 34px;
    margin-right: 30px;
    color: #fff;
    font-size: 15px;
    cursor: pointer;
    opacity: .2;
    transition: .3s;
    img {
      width: 100%;
      height: 100%;
    }
    &::before {
      border: none;
    }
    &.skipped {
      opacity: .2;
    }
    &.no-value {
      opacity: 1;
    }
    &.active {
      opacity: 1;
      transform: scale(1.4);
    }
  }
}


.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
}
