<div>
  <div class="row">
   <div class="col-12 smile-scale smile-scale__list-edit">
    <div class="smile-scale__list">
      <!-- ko foreach: items -->
      <div class="smile-scale__item" data-bind="css: {'active': $data.value == $parent.field(), 'no-value': $parent.field() == null && !$parent.skipped(), 'skipped': $parent.skipped()}, click: function () {
        if ($data.value == $parent.field()) {
          $parent.field(null)
        } else {
          $parent.field($data.value)
        }
        
      }">
      <img alt="" data-bind="attr: {'src': $data.img}">
      </div>
      <!-- /ko -->
    </div>
    <span class="error-message" data-bind="visible: question.isRequired && (!field() && !skipped())"
      >Нужно поставить оценку</span
    >
   </div>
  </div>
  <!-- ko template: {
      foreach: templateIf(question.commentEnabled && !skipped(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div class="comment-field">
    <div class="chars-counter chars-counter--type_input">
      <div class="comment-label">
        <span data-bind="text: question.commentLabel || 'Ваш комментарий'"></span>
        <!-- ko if: question.commentRequired -->
        <span class="comment-required">*</span>
        <!-- /ko -->
      </div>
      <textarea
        class="form-control form-control_full sm autosize-textarea"
        data-bind="textInput: comment, css: {'is-invalid': question.commentRequired && (comment() == null || !comment().length)}"
        style="min-height: 93px; overflow-y: auto; height: 93px"
        maxlength="3000"
        minlength="0"
        placeholder=""
      ></textarea>
      <span class="error-message" data-bind="visible: question.commentRequired && (comment() == null || !comment().length)"
      >Обязательное поле</span
    >
    </div>
  </div>
  <!-- /ko -->
  <!-- ko if: question.skip -->
  <div>
    <div class="f-check">
        <input type="checkbox" id="comment-required" class="f-check-input" data-bind="
            checked: skipped
        ">
        <label for="comment-required" class="f-check-label" data-bind="text: question.skipText || 'Не готов(а) оценить'">Обязательный</label>
    </div>
    </div>
  <!-- /ko -->
</div>
