import "inputmask/lib/extensions/inputmask.numeric.extensions";
const items = [
  {
    back: 'rgb(249, 98, 97)',
    value: 0
  },
  {
    back: 'rgb(247, 100, 100)',
    value: 1
  },
  {
    back: 'rgb(238, 109, 109)',
    value: 2
  },
  {
    back: 'rgb(223, 123, 125)',
    value: 3
  },
  {
    back: 'rgb(205, 140, 146)',
    value: 4
  },
  {
    back: 'rgb(187, 158, 166)',
    value: 5
  },
  {
    back: 'rgb(172, 172, 182)',
    value: 6
  },
  {
    back: 'rgb(159, 174, 176)',
    value: 7
  },
  {
    back: 'rgb(115, 182, 156)',
    value: 8
  },
  {
    back: 'rgb(53, 192, 128)',
    value: 9
  },
  {
    back: 'rgb(0, 201, 104)',
    value: 10
  }
]
export class ViewModel {
  constructor(params, element) {
    this.field = params.value;
    this.comment = params.comment;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.skipped = params.skipped
    this.isCanged = params.isCanged;

    this.items = ko.observableArray(this.question.fromOne ? items.filter(i => i.value) : items)


    
    
    this.isInvalid = ko.observable(false);
    
    this.disabled = params.disabled;
    this.inited = true

    this.field.subscribe((v) => {
      this.isCanged(true)
      console.log("change filed edit", v);
      if (v !==null) {
        this.skipped(0)
      }
      this.validateField()
    });
    this.comment.subscribe((v) => {
      this.isCanged(true)
      this.validateField()
    });

    this.skipped.subscribe((v) => {
      console.log("change filed edit", v);
      this.isCanged(true)
      if (v) {
        this.field(null)
      }
      this.validateField()
    });
    
  }
  
  validateField() {
    if (this.skipped()) {
      this.validStep(true)
    } else {
      console.log(this.question)
      const validComment = !this.question.commentRequired || (this.comment() && this.comment().length)

      if (this.question.isRequired) {
        this.validStep(this.field() !== null && validComment)
      } else {
        this.validStep(validComment)
      }
      
    }
  }
  /* dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  } */
}
