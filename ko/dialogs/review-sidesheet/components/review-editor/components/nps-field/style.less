@import 'Style/breakpoints.less';
.nps-scale {
  
  .nps-scale__list {
    justify-content: start;
  }
  &.nps-scale__list-edit {
    margin-bottom: 20px;
  }
  .nps-scale__item {
    width: 47px;
    color: #fff;
    font-size: 15px;
    cursor: pointer;
    opacity: .2;
    transition: .3s;
    &::before {
      border: none;
    }
    &.skipped {
      opacity: .2;
    }
    &.no-value {
      opacity: 1;
    }
    &.active {
      opacity: 1;
      &::before {
        transform: scaleX(1.24) scaleY(1.33);
      }
    }
  }
}


.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
}
