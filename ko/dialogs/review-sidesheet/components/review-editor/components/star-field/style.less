@import 'Style/breakpoints.less';

.star-item {
  cursor: pointer;
}

.skipped {
  opacity: .5;
}

.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.comment-field {
  margin-top: 0;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
}
