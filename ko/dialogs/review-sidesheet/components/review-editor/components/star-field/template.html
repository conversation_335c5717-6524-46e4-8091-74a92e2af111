<div>
  <div class="row">
   <div class="col-12 nps-scale nps-scale__list-edit" data-bind="css: {'skipped': skipped}">
    <div class="star-list">
      <!-- ko foreach: items -->
      <svg class="mr-20p star-item" width="24" height="24" viewBox="0 0 24 24" data-bind="attr: {'fill': $parent.field() >= $index() + 1 ? '#F8CD1C' : 'none'}, click: function () {
        if ($data.value == $parent.field()) {
          $parent.field(null)
        } else {
          $parent.field($data.value)
        }
        
      }" xmlns="http://www.w3.org/2000/svg">
        <path d="M18.3835 20.3615C18.5612 21.4819 18.7084 22.4104 18.7557 22.7043L12.4726 19.3331L11.9998 19.0795L11.527 19.3331L5.24426 22.7043L6.40104 15.416L6.47969 14.9205L6.12945 14.5612L1.03672 9.33713L8.0906 8.21993L8.60684 8.13817L8.83441 7.66764L12 1.12223L15.1657 7.66764L15.3932 8.13817L15.9095 8.21993L22.9634 9.33713L17.8706 14.5612L17.5204 14.9205L17.599 15.416L18.5867 15.2593L17.599 15.4161L17.5991 15.4163L17.5992 15.4173L17.5999 15.4214L17.6024 15.4373L17.6123 15.4994L17.6497 15.7352L17.7832 16.5768L18.1885 19.1316C18.2553 19.5524 18.3213 19.9687 18.3835 20.3615Z" stroke="#F8CD1C" stroke-width="2"/>
      </svg>
        
      <!-- /ko -->
    </div>
    <span class="error-message" data-bind="visible: question.isRequired && (!field() && !skipped())"
      >Нужно поставить оценку</span
    >
   </div>
  </div>
  <!-- ko template: {
      foreach: templateIf(extra(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <extra-field-edit params="question: question, value: extraValue, variants: extraVariants, valid: extraValid, selfValue: extraSelfValue" data-bind="event: {change: function () {console.log('change')}}"></extra-field-edit>
  <!-- /ko -->
  <!-- ko template: {
      foreach: templateIf(question.commentEnabled && !skipped(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div class="comment-field">
    <div class="chars-counter chars-counter--type_input">
      <div class="comment-label">
        <span data-bind="text: question.commentLabel || 'Ваш комментарий'"></span>
        <!-- ko if: question.commentRequired -->
        <span class="comment-required">*</span>
        <!-- /ko -->
      </div>
      <textarea
        class="form-control form-control_full sm autosize-textarea"
        data-bind="textInput: comment, css: {'is-invalid': question.commentRequired && (comment() == null || !comment().length)}"
        style="min-height: 93px; overflow-y: auto; height: 93px"
        maxlength="3000"
        minlength="0"
        placeholder=""
      ></textarea>
      <span class="error-message" data-bind="visible: question.commentRequired && (comment() == null || !comment().length)"
      >Обязательное поле</span
    >
    </div>
  </div>
  <!-- /ko -->
  <!-- ko if: question.skip -->
  <div>
    <div class="f-check">
        <input type="checkbox" id="comment-required" class="f-check-input" data-bind="
            checked: skipped
        ">
        <label for="comment-required" class="f-check-label" data-bind="text: question.skipText || 'Не готов(а) оценить'">Обязательный</label>
    </div>
    </div>
  <!-- /ko -->
</div>
