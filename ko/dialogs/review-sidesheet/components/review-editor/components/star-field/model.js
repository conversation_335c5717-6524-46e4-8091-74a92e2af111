export class ViewModel {
  constructor(params, element) {
    this.field = params.value;
    this.comment = params.comment;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.skipped = params.skipped
    this.extra = params.extra;
    this.isCanged = params.isCanged;

    this.extraValue = params.extraValue
    this.extraSelfValue = params.extraSelfValue
    this.extraVariants = params.extraVariants
    this.extraValid = params.extraValid
    console.log('xxxxxxxxxxxxxxxxxxxxxxxxxx')
    console.log(params)
    

    if (this.question.config.extra_question_rate_from && this.question.config.extra_question_rate_to) {
      this.extra = ko.computed(() => {
        if (!this.field()) {
          return false
        }
        return this.field() >= this.question.config.extra_question_rate_from && this.field() <= this.question.config.extra_question_rate_to;
      })
    }

    this.items = ko.observableArray()

    let index = 1

    while (index <= this.question.starRatingOptions.count) {
      this.items.push({value: index})
      index++
    }


    
    
    this.isInvalid = ko.observable(false);
    
    this.disabled = params.disabled;
    this.inited = true

    this.field.subscribe((v) => {
      this.isCanged(true)
      console.log("change filed edit", v);
      if (v !==null) {
        this.skipped(0)
      }
      this.validateField()
    });
    this.comment.subscribe((v) => {
      this.isCanged(true)
      this.validateField()
    });

    this.skipped.subscribe((v) => {
      this.isCanged(true)
      if (v) {
        this.field(null)
      }
      this.validateField()
    });

    this.extraValue.subscribe((v) => {
      this.isCanged(true)
      this.validateField()
    });

    this.extraSelfValue.subscribe((v) => {
      this.isCanged(true)
      this.validateField()
    });
    
  }
  
  validateField() {
    if (ko.isComputed(this.validStep)) {
      return
    }
    if (this.skipped()) {
      this.validStep(true)
    } else {
      const validComment = !this.question.commentRequired || (this.comment() && this.comment().length)

      if (this.question.isRequired) {
        console.log('555555555555555555555555555555')
        console.log(this.field())
        if (!this.extra()) {
          this.validStep(this.field() !== undefined && this.field() !== null && validComment)
        } else {
          let validExtra = !this.question.extraRequired || this.extraValue().length
          if (this.extraValue().includes('-1') && !this.extraSelfValue()?.length) {
            validExtra = false
          }
          this.validStep(this.field() !== undefined && this.field() !== null && validExtra)
        }
        
      } else {
        if (!this.extra()) {
          this.validStep(validComment)
        } else {
          let validExtra = !this.question.extraRequired || this.extraValue().length
          if (this.extraValue().includes('-1') && !this.extraSelfValue()?.length) {
            validExtra = false
          }
          this.validStep(this.field() !== null && validExtra)
        }
        
      }
      
    }
  }
  /* dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  } */
}
