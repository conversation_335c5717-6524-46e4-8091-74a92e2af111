import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";
import "../extra-field"
ko.components.register("star-field-edit", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      console.log("test", params);
      let element = componentInfo.element;
      element.classList.add("star-field");
      return new ViewModel(params, element);
    },
  },
  template: html,
});
