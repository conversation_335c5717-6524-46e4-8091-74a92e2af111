@import 'Style/breakpoints.less';

.with-file {
  display: flex;
  flex-wrap: wrap;
  column-gap: 10px;
  row-gap: 15px;
}
.self-variant {
  width: 100%;
}
.edit-variant-item {
  margin-bottom: 15px;
  &:last-child {
    margin-bottom: 0;
  }
  &.file-enabled {
    width: 284px;
    border-radius: 8px;
    border: 1px solid #E7EBED;
    position: relative;
    overflow: hidden;
    margin: 0 !important;
    .variant-file-wrapper {
      height: 212px;
      cursor: pointer;
      width: 100%;
      .file-loader-preview {
        width: 100%;
        height: 100%;
        border-radius: 0;
    }
  }
  &.checked {
    border-color: #3F65F1;
  }
  .f-check-label, .f-radio-label {
    position: static;
    padding: 10px;
  }
  .f-check-label::before, .f-radio-label::before {
    top: 10px;
    left: 10px;
  }
  .f-check-label::after, .f-radio-label::after {
    top: 18px;
    left: 17px;
  }
}
}

.skip-row {
  margin-top: 30px;
}

.edit-simple-text {
  width: 617px;
}

.comment-field {
  margin-top: 30px;
}


.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
}
