<div>
  <div class="row mb-25p">
   <div class="col-12">
    <div data-bind="css: {'with-file': variants.some(i => i.file_id)}">
      <!-- ko foreach: variants.filter(v => !v.deleted) -->
       <!-- ko let: { textVal: $data.text, fileId: $data.file_id, file: $data.file_url, preview: $data.preview_url } -->
       <div class="edit-variant-item" data-bind="css: {'self-variant': $data.id == '-1', 'file-enabled': fileId, 'checked': $parent.field().includes($data.id)}">
         <!-- ko if: fileId -->
          <div class="variant-file-wrapper">
            <file-loader-preview class="file-loader-preview file-loader-preview-selected-variant" data-bind="click: function (_, event) {
              event.stopPropagation();
            }," params="loading: false, disabled: true, file: file, preview: preview">
      
          </file-loader-preview>
          </div>
          
        <!-- /ko -->
        <!-- ko if: $parent.question.variantsType == 0 -->
        <foquz-radio params="name: $index(),
                checked: $parent.field().includes($data.id) ? $data.id : false,
                value: $data.id,
                disabled: false,
                event: {
                  change: function(_, e) {
                    $parent.toggleVariant($data)
                  }
                }">
            <div class="variant-text" data-bind="text: textVal"></div>
        </foquz-radio>
        <!-- /ko -->
        <!-- ko if: $parent.question.variantsType == 1 -->
        <foquz-checkbox params="checked: $parent.field().includes($data.id) ? $data.id : false, event: {
          change: function(_, e) {
            $parent.toggleVariant($data)
          }
      }">
     
      <div class="variant-text" data-bind="text: textVal"></div>
    </foquz-checkbox>
      <!-- /ko -->
       </div>
       <!-- /ko -->
       <!-- /ko -->
    </div>
    <!-- ko if: field().includes('-1') -->
    <div class="mt-10p">
      <div class="chars-counter chars-counter--type_input">
        <textarea
          class="form-control form-control_full sm autosize-textarea edit-simple-text"
          data-bind="textInput: selfAnswer, autosizeTextarea, css: {'is-invalid': !selfAnswer().length}"
          style="min-height: 72px; overflow-y: hidden; height: 70px"
          maxlength="3000"
          minlength="0"
          placeholder=""
        ></textarea>
        <span class="error-message" data-bind="visible: !selfAnswer().length"
        >Обязательное поле</span
      >
      </div>
    </div>
    <!-- /ko -->
    <span class="error-message" data-bind="visible: question.isRequired && (!field().length && !skipped())"
      >Нужно выбрать один из вариантов</span
    >
   </div>
  </div>
  <!-- ko template: {
      foreach: templateIf(question.commentEnabled && !skipped(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div class="comment-field">
    <div class="chars-counter chars-counter--type_input">
      <div class="comment-label">
        <span data-bind="text: question.commentLabel || 'Ваш комментарий'"></span>
        <!-- ko if: question.commentRequired -->
        <span class="comment-required">*</span>
        <!-- /ko -->
      </div>
      <textarea
        class="form-control form-control_full sm autosize-textarea"
        data-bind="textInput: comment, css: {'is-invalid': question.commentRequired && (comment() == null || !comment().length) || needComment}"
        style="min-height: 93px; overflow-y: auto; height: 93px"
        maxlength="3000"
        minlength="0"
        placeholder=""
      ></textarea>
      <span class="error-message" data-bind="visible: question.commentRequired && (comment() == null || !comment().length) || needComment"
      >Обязательное поле</span
    >
    </div>
  </div>
  <!-- /ko -->
  <!-- ko if: question.skip -->
  <div class="skip-row mb-25p">
    <div class="f-check">
        <input type="checkbox" id="comment-required" class="f-check-input" data-bind="
            checked: skipped
        ">
        <label for="comment-required" class="f-check-label" data-bind="text: question.skipText || 'Затрудняюсь ответить'"></label>
    </div>
    </div>
  <!-- /ko -->
</div>
