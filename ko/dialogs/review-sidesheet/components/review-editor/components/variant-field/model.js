export class ViewModel {
  constructor(params, element) {
    this.field = params.value;
    this.comment = params.comment;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.skipped = params.skipped
    this.variants = this.question.variants.map(i => i)
    this.reqComments = this.question.variants.filter(i => i.comment_required).map(i => i.id+'')
    this.isCanged = params.isCanged;
    this.inited = true

    if (this.question.selfVariantCommentRequired) {
      this.reqComments.push('-1')
    }

    

    this.selfAnswer = params.selfAnswer

    if (this.question.isSelfAnswer) {
      this.variants.push({
        deleted: 0,
        file_id: null,
        file_url: null,
        id: "-1",
        points: null,
        text: this.question.self_variant_text.length ? this.question.self_variant_text : "Свой вариант",
        type: this.question.selfVariantNothing ? 1 : 0
      })
      if (this.question.selfVariant) {
        this.field.push('-1')
      }
    }
    
    this.isInvalid = ko.observable(false);
    
    this.disabled = params.disabled;

    this.needComment = ko.computed(() => {
      let validComment = true

      if (this.reqComments.length) {
        this.field().forEach(element => {
          if (this.reqComments.includes(element)) {
            if (!this.comment() || !this.comment().length) {
              validComment = false
            }
            
          }
        });
      }
      return !validComment
    })

    this.field.subscribe((v) => {
      this.isCanged(true)
      
      if (v.length) {
        this.skipped(false)
      }
      
      this.validateField()
    });
    this.selfAnswer.subscribe((v) => {
      this.isCanged(true)
      
      this.validateField()
    });
    this.comment.subscribe((v) => {
      this.isCanged(true)
      this.validateField()
    });

    this.skipped.subscribe((v) => {
      this.isCanged(true)
      if (v) {
        this.field([])
      }
      this.validateField()
    });
    
  }

  toggleVariant(variant) {
    if (this.question.variantsType == 0) {
      this.field([variant.id])
    } else {
      if (this.field().includes(variant.id)) {
        const filtered = this.field().filter(i => i !== variant.id)
        this.field(this.field().filter(i => i !== variant.id))
      } else {
        if (variant.type == 0) {
          const empty = this.variants.filter(v => v.type == 1).map(i => i.id)
          if (empty.length) {
            const filtered = this.field().filter(v => !empty.includes(v))
            this.field(filtered)
          }
          
          
          this.field.push(variant.id)
        } else {
          this.field([variant.id])
        }
        
      }
    }
    
  }
  
  validateField() {
    if (this.field().length === undefined) {
      return
    }
    if (this.skipped()) {
      this.validStep(true)
    } else {
      let validComment = !this.question.commentRequired || (this.comment() && this.comment().length)
      if (this.reqComments.length) {
        this.field().forEach(element => {
          if (this.reqComments.includes(element)) {
            if (!this.comment() || !this.comment().length) {
              validComment = false
            }
            
          }
        });
      }

      if (this.question.isRequired) {
        if (this.field().includes('-1')) {
          this.validStep(this.field().length && validComment && this.selfAnswer().length)
        } else {
          this.validStep(this.field().length && validComment)
        }
        
      } else if (this.field().includes('-1')) {
        this.validStep(this.field().length && validComment && this.selfAnswer().length)
      } else {
        this.validStep(validComment)
      }
      
    }
  }
  
}
