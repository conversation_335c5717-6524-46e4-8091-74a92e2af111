@import 'Style/breakpoints.less';

.files-wrapper {
  display: flex;
  flex-wrap: wrap;
  column-gap: 20px;
  row-gap: 15px;
}

.empty {
  border: 1px solid #f96261;
}

.file-item {
  max-width: 105px;
  .file-name {
    margin-top: 10px;
    font-weight: 500;
    font-size: 12px;
    line-height: 110%;
    color: #2E2F31;
  }
}

.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.file-field__comment-field {
  margin-top: 30px !important;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
}
