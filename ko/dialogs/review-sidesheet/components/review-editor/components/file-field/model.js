import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";
import { FileModel } from "./file";

export class ViewModel {
  constructor(params, element) {
    this.field = params.value;
    this.comment = params.comment;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.skipped = params.skipped
    this.label = "Загрузить файл";
    this.type = this.question.fileTypes;
    this.uploading = ko.observable(false)

    if (this.type.length == 1 && this.type.includes("image")) this.label = "Загрузить фото";
    else if (this.type.length == 1 && this.type.includes("audio"))
      this.label = "Загрузить аудио";
    else if (this.type.length == 1 && this.type.includes("video"))
      this.label = "Загрузить видео";
    else if (this.type.length == 2 && this.type.includes("video") && this.type.includes("image"))
      this.label = "Загрузить фото, видео";
    else if (this.type.length == 2 && this.type.includes("video") && this.type.includes("audio"))
      this.label = "Загрузить видео, аудио";
    else if (this.type.length == 2 && this.type.includes("image") && this.type.includes("audio"))
      this.label = "Загрузить фото, аудио";
    else if (this.type.length == 3 || !this.type.length)
      this.label = "Загрузить фото, видео, аудио";

    let loaderConfig = null;
    this.mediaType = null;
    if (this.type.length == 3) {
      this.mediaType = "mix";
    }
    
    if (this.type.length == 1 && this.type.includes("image")) {
      this.mediaType = "image";
      loaderConfig = {
        presets: ["image"],
        errors: {
          format:
            "Можно загружать файлы форматов:" +
            " jpg, jpeg, png, gif, HEIC, WEBP",
        },
        multiple: true,
      };
    }
    if (this.type.length == 1 && this.type.includes("video")) {
      this.mediaType = "video";
      loaderConfig = {
        presets: ["video"],
        errors: {
          format:
            "Можно загружать файлы форматов:" +
            " .MP4, .WMV, .MOV, .3GP, .FLV, .MPEG-1,2, .WebM, AVI",
        },
        multiple: true,
      };
    }
    if (this.type.length == 1 && this.type.includes("audio")) {
      this.mediaType = "mix";
      loaderConfig = {
        presets: ["audio"],
        errors: {
          format:
            "Можно загружать файлы форматов:" +
            " MP3, OGG, WAV, M4A, ACC, AAC, AMR",
        },
        multiple: true,
      };
    }
    if (this.type.length == 2 && this.type.includes("audio") && this.type.includes("video")) {
      this.mediaType = "mix";
      loaderConfig = {
        presets: ["audio","video"],
        errors: {
          format:
            "Можно загружать файлы форматов:" +
            " MP3, OGG, WAV, M4A, ACC, AAC, AMR, MP4, .WMV, .MOV, .3GP, .FLV, .MPEG-1,2, .WebM, AVI",
        },
        multiple: true,
      };
    }
    if (this.type.length == 2 && this.type.includes("audio") && this.type.includes("image")) {
      this.mediaType = "mix";
      loaderConfig = {
        presets: ["audio","image"],
        errors: {
          format:
            "Можно загружать файлы форматов:" +
            "MP3, OGG, WAV, M4A, ACC, AAC, AMR, jpg, jpeg, png, gif, HEIC, WEBP",
        },
        multiple: true,
      };
    }
    if (this.type.length == 2 && this.type.includes("video") && this.type.includes("image")) {
      this.mediaType = "mix";
      loaderConfig = {
        presets: ["video","image"],
        errors: {
          format:
            "Можно загружать файлы форматов:" +
            "MP4, .WMV, .MOV, .3GP, .FLV, .MPEG-1,2, .WebM, AVI, jpg, jpeg, png, gif, HEIC, WEBP",
        },
        multiple: true,
      };
    }
    this.loader = new FoquzMultipleLoader(loaderConfig);
    this.uploadApiError = ko.observable(null);
    this.isCanged = params.isCanged;
    
    this.isInvalid = ko.observable(false);
    
    this.disabled = params.disabled;
    this.inited = true

    this.field.subscribe((v) => {
      this.isCanged(true)
      console.log("change filed edit", v);
      if (v !==null) {
        this.skipped(0)
      }
      this.validateField()
    });
    this.comment.subscribe((v) => {
      this.isCanged(true)
      this.validateField()
    });

    this.skipped.subscribe((v) => {
      this.isCanged(true)
      if (v) {
        this.field(null)
      }
      this.validateField()
    });


    this.loader.on("select", ({ file }) => {
      const anwerId = new URLSearchParams(window.location.href).get("reviewId");
      this.uploadApiError(null);
      this.uploading(true)
      const url =
          "/foquz/api/answers/answer-upload-files?answer_id=" +
          anwerId +
          "&question_id=" +
          this.question.id + `&access-token=${window.APIConfig.apiKey}`;

      const fd = new FormData();
      const self = this;
      

      for (let index = 0; index < file.length; index++) {

        const f = file[index];
        if (f.size <= 55000000) {
          let img = new FileModel();
          this.field.push(img);
          fd.append("files[]", f);
        } else {
          const errText = `${this.uploadApiError() || ''}<br/>Файл "${f.name}" слишком большой. Размер не должен превышать 50    МБ.`
          this.uploadApiError(errText)
        }
        
        
      }

      $.ajax({
        url: url,
        type: "post",
        data: fd,
        contentType: false,
        processData: false,
        success: function (response) {
          response.files.forEach(f => {
            const img = self.field().find(i => typeof i.id === 'function' && !i.id())

            if (f.type.indexOf("video") >= 0) {
              img.setData({
                id: f.id,
                preview: f.image,
                url: f.link,
                name: f.origin_name,
                file: f.link,
              });
            } else if (f.type.indexOf("audio") >= 0) {
              img.setData({
                id: f.id,
                preview: "/img/audio-file-back.png",
                url: f.link,
                name: f.origin_name,
                file: f.link,
              });
            } else {
              img.setData({
                id: f.id,
                preview: f.link || "/img/audio-file-back.png",
                url: f.link,
                name: f.origin_name,
                file: f.link,
              });
            }
          })
          if (response.errors?.file?.length) {
            let error = self.uploadApiError() || ''
            response.errors.file.forEach(e => {
              error = error + e + '<br/>'
            })
            self.uploadApiError(
              error
            );
            setTimeout(() => {
              self.uploadApiError(
                null
              );
            }, 3000);
            const emptyImages = self.field().filter(i => typeof i.id === 'function' && !i.id())
            emptyImages.forEach(i => {
              self.field.remove(i);
            })
            self.uploading(false)
            
            return;
          }
        },
      });
      if (self.uploadApiError()) {
        setTimeout(() => {
          self.uploadApiError(
            null
          );
        }, 3000);
      }
    });
    
  }

  removeFile(image) {
    this.field.remove(image);
    /* $.post(
      "/foquz/ajax/delete-answer-item-file",
      {
        id: image.id(),
        dummy: AUTH_KEY === "dummy" || AUTH_KEY === "dummyDesign" ? 1 : 0,
      },
      (response) => {
        
      }
    ); */
  };
  
  validateField() {
    if (this.skipped()) {
      this.validStep(true)
    } else {
      console.log(this.question)
      const validComment = !this.question.commentRequired || (this.comment() && this.comment().length)

      if (this.question.isRequired) {
        this.validStep(this.field().length && validComment)
      } else {
        this.validStep(validComment)
      }
      
    }
  }

  getPreviewForFile(file) {
    if (file.preview_url) return file.preview_url;

    const ext = file.url.split('.').pop().toLowerCase();
    const imageExts = ['png', 'jpg', 'jpeg', 'gif', 'heic', 'webp'];

    if(['webp'].includes(ext)){
      return file.url;
    }


    return imageExts.includes(ext)
        ? '/img/image-file-back.png'
        : '/img/audio-file-back.png';
  };

  /* dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  } */
}
