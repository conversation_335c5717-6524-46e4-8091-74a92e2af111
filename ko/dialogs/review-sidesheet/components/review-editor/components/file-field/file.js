export class FileModel {
  constructor() {
    this.loading = ko.observable(true);
    this.url = ko.observable('');
    this.preview_url = ko.observable('');
    this.id = ko.observable('');
    this.name = ko.observable('');
    this.file = ko.observable('');
  }

  setData(data) {
    this.url(data.url);
    this.preview_url(data.preview);
    this.id(data.id);
    this.name(data.name);
    this.file(data.file);

    this.loading(false);
  }
}
