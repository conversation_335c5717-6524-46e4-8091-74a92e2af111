<div class="review-editor-diff-field">
  <!-- ko foreach: fields -->
  <!-- ko let: { value: $data.value, parent: $parent } -->
  <div class="row review-editor-diff-field__row">
   <div class="col-12">
    <div class="diff-line" data-bind="css: {'error': $parent.question.isRequired && $data.value() == null && !$parent.skipped(), 'skipped': $parent.skipped()}">
      <div class="diff-label start" data-bind="text: $parent.labels[$index()].start, visible: parent.labels.some(l => l.start.length)"></div>
      <!-- ko foreach: $parent.items -->
      <div
        class="diff-item"
        data-bind="
          text: $data.value,
          css: {
            'active': $data.value == $parent.value(),
            'md': !parent.labels.some(l => l.start.length) || !parent.labels.some(l => l.end.length),
            'lg': !parent.labels.some(l => l.start.length) && !parent.labels.some(l => l.end.length)
          },
          click: function () {
            if ($data.value == $parent.value()) {
              $parent.value(null)
            } else {
              $parent.value($data.value)
            }
          }
        "
      ></div>
        
      <!-- /ko -->
      <div
        class="diff-label end"
        data-bind="text: $parent.labels[$index()].end, visible: parent.labels.some(l => l.end.length)"
      ></div>
    </div>
    
    
   </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->
  <span class="error-message diff-error" data-bind="visible: question.isRequired && (fields().some(i => i.value() == null)) && !skipped()"
      >Нужно поставить все оценки</span
    >
  <!-- ko template: {
      foreach: templateIf(question.commentEnabled && !skipped(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div class="comment-field diff-comment">
    <div class="chars-counter chars-counter--type_input">
      <div class="comment-label">
        <span data-bind="text: question.commentLabel || 'Ваш комментарий'"></span>
        <!-- ko if: question.commentRequired -->
        <span class="comment-required">*</span>
        <!-- /ko -->
      </div>
      <textarea
        class="form-control form-control_full sm autosize-textarea"
        data-bind="textInput: comment, css: {'is-invalid': question.commentRequired && (comment() == null || !comment().length)}"
        style="min-height: 93px; overflow-y: auto; height: 93px"
        maxlength="3000"
        minlength="0"
        placeholder=""
      ></textarea>
      <span class="error-message" data-bind="visible: question.commentRequired && (comment() == null || !comment().length)"
      >Обязательное поле</span
    >
    </div>
  </div>
  <!-- /ko -->
  <!-- ko if: question.skip -->
  <div class="diff-skip">
    <div class="f-check">
        <input type="checkbox" id="comment-required" class="f-check-input" data-bind="
            checked: skipped
        ">
        <label for="comment-required" class="f-check-label" data-bind="text: question.skipText || 'Не готов(а) оценить'"></label>
    </div>
    </div>
  <!-- /ko -->
</div>
