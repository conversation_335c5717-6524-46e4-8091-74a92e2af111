@import 'Style/breakpoints.less';

.review-editor-diff-field {
  &__row {
    margin-bottom: 1px;
  }
  .diff-line {
    display: flex;
    padding-top: 4px;
    padding-bottom: 5px;
    padding-left: 4px;
    padding-right: 4px;
    border-radius: 8px;
    max-width: 617px;
    &.error {
      background: rgba(249, 98, 97, 0.2);
    }
    &.skipped {
      opacity: .5;
    }
    .diff-item + .diff-item {
      margin-left: 2px;
    }
    .diff-item {
      width: 52px;
      min-height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      cursor: pointer;
      background: #E1E8EB;
      border-radius: 4px;
      transition: .3s;
      font-size: 12px;
  
      &.active {
        background-color: #3F65F1;
        color: #fff;
      }
      &.md {
        width: 86.8px;
      }
      &.lg {
        width: 121.8px;
      }
    }
    .diff-label {
      width: 155px;
      font-weight: 400;
      font-size: 15px;
      line-height: 120%;
      color: #2E2F31;
      display: flex;
      align-items: center;
      
      &.start {
        margin-right: 20px;
        justify-content: flex-end;
        padding: 0 4px 0 2px;
      }
      &.end {
        margin-left: 20px;
        padding: 0 4px;
      }
    }
  }
  
  .diff-skip {
    margin-top: 30px;
  }
  
  .diff-comment {
    margin-top: 30px !important;
  }
  
  .diff-error {
    max-width: 617px;
    text-align: center;
    margin-top: 16px;
  }
  
  .error-message {
    font-size: 13px;
    color: #ea1d27;
    margin-top: 6px;
    display: block;
  }
  
  .comment-field {
    margin-top: 0;
  }
  
  .date-form-control {
    .is-invalid {
      border: 1px solid #f96261 !important;
      box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
      padding-right: 0;
      padding-left: 0;
    }
  }
  
  .form-control {
    .is-invalid {
      border: 1px solid #f96261 !important;
      box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
      padding-right: 0;
    }
  }  
}

