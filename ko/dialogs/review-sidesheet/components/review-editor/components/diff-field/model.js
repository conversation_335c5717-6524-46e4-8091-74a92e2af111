export class ViewModel {
  constructor(params, element) {
    this.fields = params.fields;
    this.comment = params.comment;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.skipped = params.skipped
    this.isCanged = params.isCanged;

    this.items = ko.observableArray()

    let index = 1

    while (index <= 5) {
      this.items.push({value: index})
      index++
    }

    this.labels = []

    this.fields().forEach(i => {
      const row = this.question.differentialRows.find(r => r.id == i.id)
      this.labels.push({
        start: row.start_label,
        end: row.end_label
      })
    })


    
    
    this.isInvalid = ko.observable(false);
    
    this.disabled = params.disabled;
    this.inited = true

    this.fields().forEach(i => i.value.subscribe((v) => {
      this.isCanged(true)
      console.log("change filed edit", v);
      if (v !==null) {
        this.skipped(0)
      }
      this.validateField()
    }))

    this.comment.subscribe((v) => {
      this.isCanged(true)
      this.validateField()
    });

    this.skipped.subscribe((v) => {
      this.isCanged(true)
      if (v) {
        this.fields().forEach(element => {
          element.value(null)
        });
      }
      this.validateField()
    });

  }
  
  validateField() {
    if (this.skipped()) {
      this.validStep(true)
    } else {
      const validComment = !this.question.commentRequired || (this.comment() && this.comment().length)

      if (this.question.isRequired) {
          this.validStep(!this.fields().some(i => i.value() == null) && validComment)
      } else {
        this.validStep(validComment)
      }
      
    }
  }
  /* dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  } */
}
