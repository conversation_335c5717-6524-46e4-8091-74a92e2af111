import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";
ko.components.register("diff-field-edit", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      console.log("test", params);
      let element = componentInfo.element;
      element.classList.add("diff-field");
      return new ViewModel(params, element);
    },
  },
  template: html,
});
