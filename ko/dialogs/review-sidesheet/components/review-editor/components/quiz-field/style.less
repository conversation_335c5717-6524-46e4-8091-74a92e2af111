@import 'Style/breakpoints.less';

.date-question__time {
  width: 160px;
  input {
    text-align: center;
  }
}

.quiz-edit-row {
  .form-control_small {
    width: 294px;
  }
}

.quiz-day-month-edit {
  .day-input {
    width: 90px;
    input {
      text-align: center;
    }
  }

  .month-input {
    width: 189px;
    &.invalid {
      .fc-select__bg {
        border: 1px solid #f96261 !important;
        box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
      }
    }
  }
}

.field-name {
  font-weight: 700;
  font-size: 16px;
  line-height: 110%;
  color: #2E2F31;
  margin-bottom: 10px;
}

.quiz-period {
  .fc-date-picker {
    width: 294px;
  }
}

.day-control {
  width: 90px;
  position: relative;
  input {
    text-align: center;
  }
  .error-message {
    position: absolute;
    white-space: nowrap;
  }
}

.date-number {
  text-align: center;
  padding-right: 5px;
  padding-left: 5px;
}

.month-control {
  width: 160px;
}

.date-time-full {
  .date-form-control {
    
    margin-left: 30px;
    
  }
}

.date-question__time {
  width: 90px;
}

.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.question-required {
  color: #f96261;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
    .date-number {
      padding-right: 5px;
    }
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
  &.is-invalid {
    &.date-number {
      padding-right: 5px;
    }
  }
}
