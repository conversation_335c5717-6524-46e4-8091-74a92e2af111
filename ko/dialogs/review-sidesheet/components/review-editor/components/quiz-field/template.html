<div>
  <!-- ko foreach: fields -->
  <div class="row mb-25p quiz-edit-row">
    <!-- ko if: $data.maskType === 0 && !$data.isTextarea -->
     <div class="col-12 col-md-8">
      <div class="d-flex field-name">
        <span data-bind="text: $data.label"></span>
        <!-- ko if: $data.isRequired -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </div>
      <div class="chars-counter chars-counter--type_input">
        <input class="form-control" maxlength="3000" data-bind="textInput: $data.value, css: {'is-invalid': $data.error}, attr: {'placeholder': $data.placeholderText}" />
      </div>
      <span class="error-message" data-bind="visible: $data.error"
        >Обязательное поле</span>
     </div>
    <!-- /ko -->
  
    <!-- ko if: $data.maskType === 0 && $data.isTextarea -->
    <div class="col-12 col-md-8">
      <div class="d-flex field-name">
        <span data-bind="text: $data.label"></span>
        <!-- ko if: $data.isRequired -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </div>
      <div class="chars-counter chars-counter--type_input">
        <textarea
          class="form-control form-control_full sm autosize-textarea edit-simple-text"
          data-bind="textInput: $data.value, autosizeTextarea, css: {'is-invalid': $data.error}, attr: {'placeholder': $data.placeholderText}"
          style="min-height: 72px; overflow-y: hidden; height: 70px"
          maxlength="3000"
          minlength="0"
          placeholder=""
        ></textarea>
        <span class="error-message" data-bind="visible: $data.error"
        >Обязательное поле</span
      >
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: $data.maskType === 8 -->
   <div class="col-12 col-md-8 quiz-day-month-edit mb-25p">
    <div class="d-flex field-name">
      <span data-bind="text: $data.label"></span>
      <!-- ko if: $data.isRequired -->
      <span class="question-required">*</span>
      <!-- /ko -->
    </div>
    <div class="d-flex">
      <div class="day-input">
        <div class="chars-counter chars-counter--type_input">
          <input class="form-control" placeholder="00" maxlength="3000" data-bind="textInput: $data.day, onlyNumbers, css: {'is-invalid': $data.error() || $data.isInvalid()}" />
        </div>
      </div>
      <div class="col-md-6">
        <div class="chars-counter chars-counter--type_input month-input" data-bind="css: {'invalid': $data.isInvalid() && !month()}">
          <month-picker
          style="width: 100%"
          params="value: $data.month, start: 'today', allowEmptyMonth: !$data.isRequired"
        ></month-picker>
        </div>
      </div>
    </div>
    <span class="error-message" data-bind="visible: $data.isInvalid() && !$data.error()"
      >Некорректное значение</span
    >
    <span class="error-message" data-bind="visible: $data.error()"
      >Обязательное поле</span>
   </div>

  <!-- /ko -->

    <!-- ko if: $data.maskType === 7 -->
  <div class="col-12 col-md-8 masked-field--period">
    <div class="d-flex field-name">
      <span data-bind="text: $data.label"></span>
      <!-- ko if: $data.isRequired -->
      <span class="question-required">*</span>
      <!-- /ko -->
    </div>
    <div class="d-flex align-items-center quiz-period">
      <fc-date-picker
        params="value: $data.startDate, invalid: $data.error() || $data.isInvalidPeriod() || $data.isInvalid()"
      ></fc-date-picker>
      <span class="ml-10p mr-10p">–</span>
      <fc-date-picker
        params="value: $data.endDate, invalid: $data.error() || $data.isInvalidPeriod() || $data.isInvalid()"
      ></fc-date-picker>
    </div>
    <span class="error-message" data-bind="visible: $data.isInvalid()"
      >Неверный формат</span>
    <span class="error-message" data-bind="visible: $data.isInvalidPeriod()"
    >Некорректный период</span>
    <span class="error-message" data-bind="visible:  $data.error()"
      >Обязательное поле</span
    >
  </div>
  

  <!-- /ko -->

    <!-- ko if: $data.maskType === 2 -->
    <div class="col-12 col-md-4">
      <div class="d-flex field-name">
        <span data-bind="text: $data.label"></span>
        <!-- ko if: $data.isRequired -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </div>
      <div class="chars-counter chars-counter--type_input">
        <input
          class="form-control form-control_small edit-simple-text"
          maxlength="3000"
          data-bind="textInput: $data.value,
        attr:{
          placeholder: $data.placeholderText || '<EMAIL>'
        }, css: {'is-invalid': $data.error() || $data.isInvalid()}"
        />
      </div>
      <span class="error-message" data-bind="visible: $data.isInvalid()"
        >Неверный формат</span
      >
      <span class="error-message" data-bind="visible: $data.error()"
        >Обязательное поле</span
      >
    </div>
    <!-- /ko -->
    <!-- ko if: $data.maskType === 3 -->
    <div class="col-12 col-md-4">
      <div class="d-flex field-name">
        <span data-bind="text: $data.label"></span>
        <!-- ko if: $data.isRequired -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </div>
      <div class="chars-counter chars-counter--type_input">
        <input class="form-control form-control_small" maxlength="3000" type="number" data-bind="textInput: $data.value, css: {'is-invalid': $data.error() || $parent.isRequired()}, attr: {'placeholder': $data.placeholderText}" />
      </div>
      <span class="error-message" data-bind="visible: $data.error"
        >Обязательное поле</span>
    </div>
    <!-- /ko -->
  
    
    <!-- ko if: $data.maskType === 6 -->
    <div class="col-12 col-md-4">
      <div class="d-flex field-name">
        <span data-bind="text: $data.label"></span>
        <!-- ko if: $data.isRequired -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </div>
      <fc-date-picker
      style="width: 294px"
      params="value: $data.value, invalid: $data.error() || $data.isInvalid()"
      data-bind="css: {'is-invalid': $data.error}"
      class="form-control_small"
    ></fc-date-picker>
    <span class="error-message" data-bind="visible: $data.isInvalid"
        >Неверный формат</span>
        <span class="error-message" data-bind="visible: $data.error"
        >Обязательное поле</span
      >
    </div>
  
    <!-- /ko -->

    <!-- ko if: $data.maskType === 5 -->
  <div class="col-12 col-md-8">
    <div class="chars-counter chars-counter--type_input">
      <!-- ko if: typeof $data.surname === 'function' -->
      <label class="form-label" for="answer-surname">Фамилия
        <!-- ko if: $data.maskConfig.surname.required == 'true' -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </label>
      <div class="mb-4">
        <input class="form-control" maxlength="3000" id="answer-surname" data-bind="textInput: $data.surname, css: {'is-invalid': $data.maskConfig.surname.required == 'true' && !$data.surname().length}" />
        <span class="error-message" data-bind="visible: $data.maskConfig.surname.required == 'true' && !$data.surname().length"
          >Обязательное поле</span>
      </div>
      <!-- /ko -->
      <!-- ko if: typeof $data.name === 'function' -->
      <label class="form-label" for="answer-name">Имя
        <!-- ko if: $data.maskConfig.name.required == 'true' -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </label>
      <div class="mb-4">
        <input class="form-control" maxlength="3000" data-bind="textInput: $data.name, css: {'is-invalid': $data.maskConfig.name.required == 'true' && !$data.name().length}" id="answer-name"/>
      <span class="error-message" data-bind="visible: $data.maskConfig.name.required == 'true' && !$data.name().length"
        >Обязательное поле</span>
      </div>
      <!-- /ko -->
      <!-- ko if: typeof $data.patronymic === 'function' -->
      <label class="form-label" for="answer-patronymic">Отчество
        <!-- ko if: $data.maskConfig.patronymic.required == 'true' -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </label>
      <div class="">
        <input class="form-control" maxlength="3000" data-bind="textInput: $data.patronymic, css: {'is-invalid': $data.maskConfig.patronymic.required == 'true' && !$data.patronymic().length}" id="answer-patronymic"/>
      <span class="error-message" data-bind="visible: $data.maskConfig.patronymic.required == 'true' && !$data.patronymic().length"
        >Обязательное поле</span>
      </div>
      <!-- /ko -->
    </div>
  </div>

  <!-- /ko -->
    
    <!-- ko if: $data.maskType === 1 -->
    <div class="col-12 col-md-4">
      <div class="d-flex field-name">
        <span data-bind="text: $data.label"></span>
        <!-- ko if: $data.isRequired -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </div>
      <div class="chars-counter chars-counter--type_input">
        <input
          class="form-control form-control_small"
          maxlength="3000"
          data-bind="textInput: $data.value,
          attr:{
            placeholder: $data.placeholderText,
            telmask: '+7 (*************'
          }, css: {'is-invalid': $data.error() || $data.isInvalid()}
          "
        />
      </div>
      <span class="error-message" data-bind="visible: $data.isInvalid()"
        >Неверный формат</span
      >
      <span class="error-message" data-bind="visible: $data.error()"
        >Обязательное поле</span
      >
    </div>
    <!-- /ko -->
  
    <!-- ko if: $data.maskType === 4 -->
    <div class="col-12 col-md-8">
      <div class="d-flex field-name">
        <span data-bind="text: $data.label"></span>
        <!-- ko if: $data.isRequired -->
        <span class="question-required">*</span>
        <!-- /ko -->
      </div>
      <div class="chars-counter chars-counter--type_input">
        <input
          class="form-control"
          maxlength="3000"
          data-bind="textInput: $data.value,
        attr:{
          placeholder: $data.placeholderText || 'http://example.com',
        }, css: {'is-invalid': $data.error() || $data.isInvalid()}"
        />
      </div>
      <span class="error-message" data-bind="visible: $data.isInvalid"
        >Неверный формат</span
      >
      <span class="error-message" data-bind="visible: $data.error()"
        >Обязательное поле</span
      >
    </div>
    <!-- /ko -->
  </div>
<!-- /ko -->
</div>
