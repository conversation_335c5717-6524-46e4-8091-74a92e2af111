import "inputmask/lib/extensions/inputmask.numeric.extensions";
import moment from 'moment';

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];
    this.field = params.value;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.isCanged = params.isCanged;

    console.log('111111111111111')
    console.log(params)

    this.fields = params.fields
   
    /* if (this.question.type == 3 && !this.question.onlyDateMonth) {
      this.field(params.question.answer)
    }

    if (this.question.date_type == 1) {
      this.field(params.question.answer)
    }

    if (this.question.date_type == 2 && !this.question.onlyDateMonth) {
      this.field(params.question.answer)
      const [date, time] = this.field().split(' ')
      this.date(date)
      this.time(time)
    }

    if (this.question.date_type == 2 && this.question.onlyDateMonth) {
      this.field(params.question.answer)
      const [date, time] = this.field().split(' ')
      const [day, month] = date.split('.')
      this.time(time)
      this.month(month*1);
      this.day(day);
    } */

    // Подписка на изменение маски
    /* this.activeMask.subscribe((newMask) => {
      const input = $(element).find('input[data-bind*="mask"]');
      input.inputmask("remove"); // Удаление предыдущей маски
      input.inputmask(newMask); // Установка новой маски
    }); */

    // Начальная установка маски
    /* 

    this.field.subscribe((v) => {
      console.log("change filed edit", v);
      this.validateField()
    }); */

    setTimeout(() => {
      const input = $(element).find('input[data-bind*="telmask"]');
      console.log('sssssssssssssss')
      console.log(input)
      input.inputmask('+7 (*************');
    }, 0);

  }
  
  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
