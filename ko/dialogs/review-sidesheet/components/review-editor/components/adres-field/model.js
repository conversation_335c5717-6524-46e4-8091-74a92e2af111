export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];
    this.field = params.value;
    this.question = params.question()
    this.validStep = params.validStep
    this.field(params.question.answer)
    this.isRequired = ko.computed(() => {
      const error = this.question.isRequired && !this.field().length
      this.validStep(!error)
      return error
    });
    this.response = ko.observableArray([])
    this.focused = ko.observable(false)
    this.isCanged = params.isCanged;
    this.inited = true

    this.field.subscribe((v) => {
      this.isCanged(true)
      console.log("change filed edit", v);
      this.updateResults(v)
    });
  }
  updateResults(v) {
    if (!v.length) return
    const self = this
    let arResponse = [],
        promises = [];

    if (this.question.arRegionsIDs.length) {
      this.question.arRegionsIDs.forEach((regionID, index) => {
        promises.push(
          $.ajax({
            url: 'https://kladr-api.ru/api.php',
            dataType: 'jsonp',
            data: {
              query: this.field(),
              oneString: 1,
              regionId: regionID.id,
              cityId: this.question.arCityIDs
                ? this.question.arCityIDs[index].id
                : '',
              districtId:
              this.question.arDistrictsIDs &&
                (!this.question.arStreetsIDs ||
                  !this.question.arStreetsIDs[index])
                  ? this.question.arDistrictsIDs[index].id
                  : '',
              streetId: this.question.arStreetsIDs.length
                ? this.question.arStreetsIDs[index].id
                : '',
              limit: 15,
            },
            success: function (data) {
              if (data) {
                if (data.result) {
                  data.result.forEach((item) => {
                    arResponse.push({
                      type: 0,
                      id: item.id,
                      name: item.fullName,
                    });
                  });
                }
              }
            },
          }),
        );
      });

      Promise.all(promises).then(() => {
        arResponse.sort(function (a, b) {
          if (a.name > b.name) {
            return 1;
          }
          if (a.name < b.name) {
            return -1;
          }
          return 0;
        });
        self.response(arResponse);
      });
    } else {
      $.ajax({
        url: 'https://kladr-api.ru/api.php',
        dataType: 'jsonp',
        data: {
          query: this.field(),
          oneString: 1,
          regionId: '',
          cityId: '',
          districtId: '',
          streetId: '',
          limit: 30,
        },
        success: function (data) {
          if (data) {
            if (data.result) {
              data.result.forEach((item) => {
                arResponse.push({
                  type: 0,
                  id: item.id,
                  name: item.fullName,
                });
              });
            }

            self.response(arResponse);
          }
        },
      });
    }
    
  }
}
