@import 'Style/breakpoints.less';

.adres-field {
  display: block;

  .edit-simple-text {
    width: 617px;
    position: relative;
  }

  .result-list {
  
  padding: 15px 20px;
  width: 617px;
  

  /* back */
  background: #FFFFFF;
  border-radius: 9px;
  box-shadow: 0px 5px 15px 0px #2E2F314D;
  overflow: hidden;
  

  .result-list-wrapper {
    max-height: 350px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: #E8EDEE;
      width: 8px;      /* цвет зоны отслеживания */
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #8E99A3;    /* цвет бегунка */
      border-radius: 20px;       /* округлось бегунка */
    }
  }

  .result-item {
    /* Пожалуйста, прочитай */
    font-weight: 400;
    font-size: 14px;
    line-height: 110%;
    /* or 15px */
    /* primary */
    color: #3F65F1;
    margin-bottom: 20px;
    cursor: pointer;
    &:last-child {
      margin-bottom: 0;
    }
    &.active {
      color: #2E2F31;

    }
  }
  }
  
}

.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.day-input {
  .form-control {
    &.is-invalid {
      padding-right: 15px;
    }
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
  }
}
