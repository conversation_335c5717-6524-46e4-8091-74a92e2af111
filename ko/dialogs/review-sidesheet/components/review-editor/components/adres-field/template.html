<div>
  <div class="row">
   <div class="col-12 col-md-8">
    <div class="chars-counter chars-counter--type_input edit-simple-text">
      <input class="form-control" maxlength="3000" data-bind="textInput: field, css: {'is-invalid': isRequired}, event: { focus: function () {focused(true)}, blur: function () {
        setTimeout(() => {
          focused(false)
        }, 300);
      }}" />
      <div class="result-list" data-bind="visible: focused() && response().length">
        <div class="result-list-wrapper">
          <!-- ko foreach: response -->
          <div class="result-item" data-bind="text: $data.name, click: function () {$parent.field($data.name)}, css: {'active': $parent.field() == $data.name}"></div>
          <!-- /ko -->
        </div>
          
      </div>
    </div>
    <span class="error-message" data-bind="visible: isRequired"
      >Обязательное поле</span>
   </div>
</div>
</div>
