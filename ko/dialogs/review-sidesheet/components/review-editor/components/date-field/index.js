import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";
ko.components.register("date-field-edit", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      console.log("test", params);
      let element = componentInfo.element;
      element.classList.add("date-field");
      return new ViewModel(params, element);
    },
  },
  template: html,
});
