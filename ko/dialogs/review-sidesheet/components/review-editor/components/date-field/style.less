@import 'Style/breakpoints.less';

.date-question__time {
  width: 160px;
  input {
    text-align: center;
  }
}

.day-control {
  width: 90px;
  position: relative;
  input {
    text-align: center;
  }
  .error-message {
    position: absolute;
    white-space: nowrap;
  }
}

.date-number {
  text-align: center;
  padding-right: 5px;
  padding-left: 5px;
}

.month-control {
  width: 160px;
  &.error {
    .fc-select__bg {
      border: 1px solid #f96261 !important;
      box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    }
  }
}

.date-time-full {
  .date-form-control {
    
    margin-left: 30px;
    
  }
}

.date-question__time {
  width: 90px;
}

.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
    .date-number {
      padding-right: 5px;
    }
  }
}

.fc-input--invalid .fc-input__view {
  box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
  &.is-invalid {
    &.date-number {
      padding-right: 5px;
    }
  }
}
