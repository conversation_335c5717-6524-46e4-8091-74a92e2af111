import "inputmask/lib/extensions/inputmask.numeric.extensions";
import moment from 'moment';

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];
    this.field = params.value;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    
    this.maskType = params.question().mask_type;
    this.activeMask = ko.observable();
    this.isInvalid = ko.observable(false);
    this.isInvalidDate = ko.observable(false);
    this.isInvalidTime = ko.observable(false);
    
    this.disabled = params.disabled;
    this.date = ko.observable();
    this.time = ko.observable();
    this.day = ko.observable();
    this.month = ko.observable();
    this.isCanged = params.isCanged;
   
    if (this.question.type == 3 && !this.question.onlyDateMonth) {
      this.field(params.question().answer)
    }

    if (this.question.date_type == 1) {
      this.field(params.question().answer)
    }

    if (this.question.date_type == 2 && !this.question.onlyDateMonth) {
      this.field(params.question().answer)
        const [date, time] = this.field() ? this.field().split(' ') : ['','']
        this.date(date)
        this.time(time)
      
    }

    if (this.question.date_type == 2 && this.question.onlyDateMonth) {
      this.field(params.question().answer)
        const [date, time] = this.field() ? this.field().split(' ') : ['.','']
        const [day, month] = date.split('.')
        this.time(time)
        this.month(month*1);
        this.day(day);
      
    }

    // Подписка на изменение маски
    this.activeMask.subscribe((newMask) => {
      const input = $(element).find('input[data-bind*="mask"]');
      input.inputmask("remove"); // Удаление предыдущей маски
      input.inputmask(newMask); // Установка новой маски
    });

    // Начальная установка маски
    setTimeout(() => {
      const input = $(element).find('input[data-bind*="mask"]');
      input.inputmask(this.activeMask());
    }, 0);

    this.inited = true

    this.field.subscribe((v) => {
      this.isCanged(true)
      console.log("change filed edit", v);
      this.validateField()
    });
    this.date.subscribe((newStart) => {
      this.isCanged(true)
      this.updateField();
    });

    this.day.subscribe((newStart) => {
      this.isCanged(true)
      this.updateField();
    });

    this.month.subscribe((newStart) => {
      this.isCanged(true)
      this.updateField();
    });

    this.time.subscribe((newEnd) => {
      this.isCanged(true)
      this.updateField();
    });
    this.validateField()
  }
  updateField() {
    if (this.question.date_type == 2 && !this.question.onlyDateMonth) {
      this.field(`${this.date()} ${this.time()}`);
    }
    if (this.question.date_type == 2 && this.question.onlyDateMonth) {
      this.field(`${this.day()}.${this.month()} ${this.time()}`);
    }
    
  }
  formatMaskedData(data) {
    return data;
  }
  validateField() {
    this.isRequired(this.question.isRequired && !this.field()?.length)
    
    if (this.question.isRequired) {
      if (this.question.date_type == 1) {
        // Только для времени
        const [h, m] = this.field().split(':')
        this.isInvalid(this.field().length && (h*1 > 24 || !m || m.length < 2 || m*1 > 60));
      } else if (this.question.date_type == 2 && !this.question.onlyDateMonth) {
        // Только для даты + время полный
        const [d,mth,y] = this.date().split('.')
  
        this.isInvalidDate(!moment(this.date(), 'DD.MM.YYYY').isValid() || !mth.length || y.length < 4);
        const [h, m] = this.time() ? this.time().split(':') : ['','']
        this.isInvalidTime(h*1 > 24 || !m || m.length < 2 || m*1 > 60)
        this.isRequired(this.question.isRequired && (!this.time().length || !this.date().length))
      } else if (this.question.date_type == 2 && this.question.onlyDateMonth) {
        // Только для даты + время месяц
        const daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  
        this.isInvalid(this.day().length && ((this.day()*1 > (daysInMonth[this.month()*1 - 1] || 31) || this.day()*1 < 1)));
        const [h, m] = this.time() ? this.time().split(':') : ['','']
        this.isInvalidTime(h*1 > 24 || !m || m.length < 2 || m*1 > 60)
        this.isRequired(this.question.isRequired && (!this.time().length || !this.day().length))
      }
    } else {
      if (this.question.date_type == 1) {
        // Только для времени
        if (this.field().length) {
          const [h, m] = this.field().split(':')
          this.isInvalid(this.field().length && (h*1 > 24 || !m || m.length < 2 || m*1 > 60));
        } else {
          this.isInvalid(false)
        }
        
      } else if (this.question.date_type == 2 && this.question.onlyDateMonth) {
        if (this.day().length || this.month().length || this.time().length) {
          // Только для даты + время месяц
          const daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    
          this.isInvalid(this.day().length && ((this.day()*1 > (daysInMonth[this.month()*1 - 1] || 31) || this.day()*1 < 1)));
          if (this.time()?.length) {
            const [h, m] = this.time().split(':')
            this.isInvalidTime(h*1 > 24 || !m || m.length < 2 || m*1 > 60)
          } else {
            this.isInvalidTime(false)
          }
          
          const invalidMonth = !this.month().length && (this.day()?.length || this.time()?.length)
          const invalidDay = !this.day().length && (this.month()?.length || this.time()?.length)
          const invalidTime = !this.time().length && (this.month()?.length || this.day()?.length)
          
          this.isRequired(invalidMonth || invalidDay || invalidTime)
        }
        
      } else if (this.question.date_type == 2 && !this.question.onlyDateMonth) {
        // Только для даты + время полный
        const [d,mth,y] = this.date().split('.')
        const empty = !this.date().length && !this.time().length

        if (!empty) {
          this.isInvalidDate(!moment(this.date(), 'DD.MM.YYYY').isValid() || !mth.length || y.length < 4);
          const [h, m] = this.time().split(':')
          this.isInvalidTime(h*1 > 24 || !m || m.length < 2 || m*1 > 60)
          this.isRequired(!this.time().length || !this.date().length)
        } else {
          this.isInvalidDate(false)
          this.isInvalidTime(false)
          this.isRequired(false)
        }
  
        
      } 
    }

    
    this.validStep(!this.isInvalid() && !this.isRequired() && !this.isInvalidTime() && !this.isInvalidDate())

  }
  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
