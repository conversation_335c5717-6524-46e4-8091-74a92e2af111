<div>
  <div class="row">
    <!-- ko if: question.date_type == 1 -->
   <div class="col-12 col-md-8">
    <div class="chars-counter chars-counter--type_input date-form-control date-question__time">
      <input class="form-control" data-bind="textInput: field, mask, maskPattern: '00:00', css: {'is-invalid': isInvalid() || isRequired()},event: {change: function () {console.log('change1111111')}}" />
    </div>
    <span class="error-message" data-bind="visible: isInvalid() && !isRequired()"
      >Неверный формат</span>
    <span class="error-message" data-bind="visible: isRequired"
      >Обязательное поле</span>
   </div>
   <!-- /ko -->

   <!-- ko if: question.date_type == 2 && !question.onlyDateMonth-->
   <div class="col-12 col-md-8 date-time-full ">
    <div class="d-flex">
      <div>
        <fc-date-picker
          style="width: 174px"
          params="value: date, invalid: isInvalidDate() || (isRequired() && !date().length)"
          data-bind="css: {'is-invalid': isInvalidDate() || (isRequired() && !date().length)}"
        ></fc-date-picker>
        <span class="error-message" data-bind="visible: isInvalidDate() && date().length, click: function () {console.log(date())}"
        >Неверный формат</span>
        <span class="error-message" data-bind="visible: isRequired() && !date().length"
        >Обязательное поле</span>
      </div>
      <div class="date-form-control">
        <div class="chars-counter chars-counter--type_input date-question__time">
          <input class="form-control" data-bind="textInput: time, mask, maskPattern: '00:00', css: {'is-invalid': isInvalidTime() || (isRequired() && !time().length)}" />
        </div>
        <span class="error-message" data-bind="visible: isInvalidTime() && time().length"
        >Неверный формат</span>
        <span class="error-message" data-bind="visible: isRequired() && !time().length"
        >Обязательное поле</span>
      </div>
    </div>
    
   </div>
   
   <!-- /ko -->

   <!-- ko if: question.date_type == 2 && question.onlyDateMonth-->
   <div class="col-12 col-md-8 date-time-full">
    <div class="d-flex">
      <div>
        <div class="d-flex">
          <div class="day-control mr-15p">
            <div class="chars-counter chars-counter--type_input">
              <input class="form-control date-number" data-bind="textInput: day, onlyNumbers, css: {'is-invalid': isInvalid() || (isRequired() && !day().length)}" />
            </div>
            <span
              class="error-message"
              data-bind="visible: isRequired() && !day().length"
            >Обязательное<br/>поле</span>
            <span
              class="error-message"
              data-bind="visible: isInvalid()"
            >Некорректное<br/>значение</span>
          </div>
          <div class="month-control" data-bind="css: {'error': isRequired() && !month()}">
            <month-picker
            style="width: 100%"
            params="value: month, start: 'today', allowEmptyMonth: !question.isRequired"
            ></month-picker>
            <span
              class="error-message "
              data-bind="visible: isRequired() && !month(), click: function () {console.log(month())}"
            >Обязательное поле</span>
          </div>
        </div>
        
      </div>
      
      <div class="date-form-control">
        <div class="chars-counter chars-counter--type_input date-question__time">
          <input class="form-control" data-bind="textInput: time, mask, maskPattern: '00:00', css: {'is-invalid': isInvalidTime() || (isRequired() && !time().length)}" />
        </div>
        <span class="error-message" data-bind="visible: isInvalidTime() && time().length"
        >Неверный формат</span>
        <span class="error-message" data-bind="visible: isRequired() && !time().length"
          >Обязательное поле</span>
      </div>
    </div>
   </div>
   
   <!-- /ko -->
</div>
</div>
