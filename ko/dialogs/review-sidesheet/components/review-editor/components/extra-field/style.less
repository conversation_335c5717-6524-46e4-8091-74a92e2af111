@import 'Style/breakpoints.less';

.extra-name {
font-weight: 400;
font-size: 15px;
line-height: 120%;
color: #2E2F31;
margin-bottom: 15px;
}

.extra {
  margin-bottom: 30px;

  .variant-text {
    line-height: 18px;
  }
  .extra-name {
    font-size: 16px;
    line-height: 18px;
  }
}


.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
}
