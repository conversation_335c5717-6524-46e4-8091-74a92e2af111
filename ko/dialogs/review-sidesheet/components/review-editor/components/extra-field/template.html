<div class="extra">
  <div class="extra-name">
    <span data-bind="text: question.clarifyingQuestion"></span>
    <!-- ko if: question.extraRequired -->
      <span class="question-required">*</span>
    <!-- /ko -->
  </div>
  <div class="row">
    <!-- ko if: type == 0 -->
    <div class="col-12 col-md-8">
      <!-- ko foreach: variants -->
        <!-- ko let: { textVal: $data.variant || $data.text } -->
          <foquz-radio
            class="mb-15p"
            params="
              name: $index() + ' ' + $parent.id,
              checked: $parent.value().includes($data.id+'') ? $data.id : false,
              value: $data.id,
              disabled: false,
              event: {
                change: function(_, e) {
                  $parent.toggleVariant($data)
                }
              }
            "
          >
            <div class="variant-text" data-bind="text: textVal"></div>
          </foquz-radio>
        <!-- /ko -->
      <!-- /ko -->
       <span
        class="error-message"
        data-bind="visible: extraRequired && !value().length"
       >Нужно выбрать один из вариантов</span>    
    </div>
    <!-- /ko -->
    <!-- ko if: type == 1 -->
    <div class="col-12 col-md-8">
      <!-- ko foreach: variants -->
        <!-- ko let: { textVal: $data.variant || $data.text } -->
          <foquz-checkbox
            class="mb-15p"
            params="
              checked: $parent.value().includes($data.id+'') ? $data.id : false,
              event: {
                change: function(_, e) {
                  $parent.toggleVariant($data)
                }
              }
            "
          >
            <div class="variant-text" data-bind="text: textVal"></div>
          </foquz-checkbox>
        <!-- /ko -->
      <!-- /ko -->
      <span
        class="error-message"
        data-bind=" visible: extraRequired && !value().length
      "
      >Нужно выбрать один из вариантов</span>
    </div>
    <!-- /ko -->
     <!-- ko if: type == 1 || type == 0 -->
      <!-- ko if: value().includes('-1') -->
    <div class="mt-n5p col-12 col-md-8">
      <div class="chars-counter chars-counter--type_input">
        <textarea
          class="form-control form-control_full sm autosize-textarea edit-simple-text"
          data-bind="
            textInput: selfValue,
            autosizeTextarea,
            css: {
              'is-invalid': selfValue() == null || !selfValue().length
            },
          "
          style="min-height: 72px; overflow-y: hidden; height: 70px"
          maxlength="3000"
          minlength="0"
          placeholder=""
        ></textarea>
        <span
          class="error-message"
          data-bind="visible: selfValue() == null || !selfValue().length"
        >Обязательное поле</span>
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <!-- ko if: type == 2 -->
      <div class="col-12 col-md-8">
        <div class="chars-counter chars-counter--type_input">
          <textarea
            class="form-control form-control_full sm autosize-textarea edit-simple-text"
            data-bind="
              textInput: value,
              autosizeTextarea,
              css: {'is-invalid': extraRequired && !value().length}
            "
            style="min-height: 72px; overflow-y: hidden; height: 70px"
            maxlength="3000"
            minlength="0"
            placeholder=""
          ></textarea>
          <span
            class="error-message"
            data-bind="visible: extraRequired && !value().length"
          >Обязательное поле</span>
        </div>
      </div>
    <!-- /ko -->
   </div>
</div>
