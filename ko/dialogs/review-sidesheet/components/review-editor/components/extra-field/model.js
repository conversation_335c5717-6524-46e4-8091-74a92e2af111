export class ViewModel {
  constructor(params, element) {
    this.question = params.question
    this.value = params.value
    this.variants = params.variants
    this.type = this.question.variantsType
    this.selfValue = params.selfValue
    this.isCanged = params.isCanged;
    this.id = params.id;
    this.extraRequired = params.extraRequired ||  this.question.extraRequired;
  }
  
  toggleVariant(variant) {
    if (this.type == 0) {
      this.value([variant.id+''])
    } else {
      if (this.value().includes(variant.id+'')) {
        this.value(this.value().filter(i => i != variant.id))
      } else {
        this.value.push(variant.id+'')
      }
    }
    
  }
  
}
