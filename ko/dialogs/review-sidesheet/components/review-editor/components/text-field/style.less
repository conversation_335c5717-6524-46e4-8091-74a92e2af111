@import 'Style/breakpoints.less';

.text-field {
  display: block;

  .edit-simple-text {
    width: 617px;
  }

  .edit-control-sm {
    width: 294px !important;
  }

  .day-input {
    width: 90px;
    margin-left: 15px;
    input {
      text-align: center;
    }
  }

  .month-input {
    width: 189px;
    &.invalid {
      .fc-select__bg {
        border: 1px solid #f96261 !important;
        box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
      }
    }
  }

  .text-field__placeholder,
  .text-field__range {
    width: 100%;
  }

  &.compact {
    .tablet-and-desktop({
      .text-field__placeholder,
      .text-field__range {
        width: 50%;
      }
    });
  }
}

.masked-field--period {
  .fc-date-picker {
    width: 145px;
  }
}

.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.day-input {
  .form-control {
    &.is-invalid {
      padding-right: 15px;
    }
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
  }
}
