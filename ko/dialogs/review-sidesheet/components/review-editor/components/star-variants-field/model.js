export class ViewModel {
  constructor(params, element) {
    this.fields = params.fields;
    this.comment = params.comment;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.skipped = params.skipped
    this.extra = params.extra;

    this.extraValue = params.extraValue
    this.extraSelfValue = params.extraSelfValue
    this.extraVariants = params.extraVariants
    this.extraValid = params.extraValid
    this.isCanged = params.isCanged;

    this.items = ko.observableArray()
    let index = 1
    while (index <= this.question.starRatingOptions.count) {
      this.items.push({value: index})
      index++
    }
    
    this.isInvalid = ko.observable(false);
    
    this.disabled = params.disabled;    
  }
  
  validateField() {
    if (this.skipped()) {
      this.validStep(true)
    } else {
      const validComment = !this.question.commentRequired || (this.comment() && this.comment().length)

      if (this.question.isRequired) {
        if (!this.extra()) {
          this.validStep(this.fields() !== null && validComment)
        } else {
          let validExtra = !this.question.extraRequired || this.extraValue().length
          if (this.extraValue().includes('-1') && !this.extraSelfValue().length) {
            validExtra = false
          }
          this.validStep(this.fields() !== null && validComment && validExtra)
        }
        
      } else {
        if (!this.extra()) {
          this.validStep(validComment)
        } else {
          let validExtra = !this.question.extraRequired || this.extraValue().length
          if (this.extraValue().includes('-1') && !this.extraSelfValue().length) {
            validExtra = false
          }
          this.validStep(validComment && validExtra)
        }
        
      }
      
    }
  }
  /* dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  } */
}
