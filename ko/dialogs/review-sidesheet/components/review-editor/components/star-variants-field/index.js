import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";
import "../extra-field"
ko.components.register("star-variants-field-edit", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("star-variants-field");
      return new ViewModel(params, element);
    },
  },
  template: html,
});
