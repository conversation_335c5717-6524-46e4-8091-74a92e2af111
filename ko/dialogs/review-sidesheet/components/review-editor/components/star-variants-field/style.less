@import 'Style/breakpoints.less';

.star-variants-field {
  display: flex;
  flex-direction: column;
}

.star-item {
  cursor: pointer;
}

.variant-name {
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  color: #2E2F31;
}

.star-variants-extra {
  .extra {
    margin-bottom: 20px;

    .foquz-check-control {
      &:last-of-type {
        margin-bottom: 0 !important;
      }
    }

    .mt-n5p {
      margin-top: 10px !important;
    }
  }
}

.variant-item {
  margin-bottom: 20px;
  border-bottom: 1px solid #E7EBED;
  max-width: 617px;
  &:last-of-type {
    border-bottom: none;
  }
}

.error-message {
  font-size: 13px;
  color: #ea1d27;
  margin-top: 6px;
  display: block;
}

.comment-field {
  margin-top: 0;
}

.date-form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
    padding-left: 0;
  }
}

.form-control {
  .is-invalid {
    border: 1px solid #f96261 !important;
    box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    padding-right: 0;
  }
}
