import "./components/text-field";
import "./components/date-field";
import "./components/nps-field";
import "./components/smile-field";
import "./components/quiz-field";
import "./components/adres-field";
import "./components/variant-field";
import "./components/file-field";
import "./components/star-field";
import "./components/star-variants-field";
import "./components/diff-field";
import "./style.less";
import { ViewModel } from "./model";
import html from "./template.html";
import * as types from "Data/question-types";
import * as models from "../../../../components/question-form/models";
import 'Components/review-question-preview';

const QuestionModels = {
  [types.RATE_QUESTION]: models.RateQuestion,
  [types.VARIANTS_QUESTION]: models.VariantsQuestion,
  [types.TEXT_QUESTION]: models.TextQuestion,
  [types.DATE_QUESTION]: models.DateQuestion,
  [types.ADDRESS_QUESTION]: models.AddressQuestion,
  [types.FILE_QUESTION]: models.FileQuestion,
  [types.QUIZ_QUESTION]: models.QuizQuestion,
  [types.PRIORITY_QUESTION]: models.PriorityQuestion,
  [types.MEDIA_VARIANTS_QUESTION]: models.MediaVariantsQuestion,
  [types.GALLERY_QUESTION]: models.GalleryQuestion,
  [types.SMILE_QUESTION]: models.SmileQuestion,
  [types.NPS_QUESTION]: models.NPSQuestion,
  [types.MATRIX_QUESTION]: models.MatrixQuestion,
  [types.DIFF_QUESTION]: models.DiffQuestion,
  [types.STARS_QUESTION]: models.StarsQuestion,
  [types.STAR_VARIANTS_QUESTION]: models.StarVariantsQuestion,
  [types.RATING_QUESTION]: models.RatingQuestion,
  [types.FILIALS_QUESTION]: models.FilialQuestion,
  [types.CLASSIFIER_QUESTION]: models.ClassifierQuestion,
  [types.INTER_BLOCK]: models.InterBlock,
  [types.SCALE_QUESTION]: models.ScaleQuestion,
  [types.MATRIX_3D_QUESTION]: models.Matrix3DQuestion,
};

ko.components.register("review-editor", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("review-sidesheet");
      element.classList.add("review-sidesheet--responsive");
      element.classList.add("review-details-modal");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
