@import 'Style/breakpoints';

.review-rating {
  &-modal-dialog {
    width: 460px !important;

    &--initializing {
      visibility: hidden;
    }
  }

  &__table-wrapper {
    max-height: 300px;
    word-break: break-word;

    .scroll-element.scroll-y {
      right: 0;
    }

    .only-mobile({
      max-height: none;

      .os-scrollbar-vertical {
        display: none;
      }
    });
  }

  &__table {
    width: calc(100% - 19px);
    border-top: 1px solid #e7ebed;
    border-bottom: 1px solid #e7ebed;
    margin-right: 19px;

    font-weight: 500;
    font-size: 12px;

    color: #2e2f31;

    td {
      padding: 10px 0px;
      vertical-align: top;
      line-height: 15px;

      &.review-rating__point-cell {
        padding-right: 24px;
        min-width: 150px;
      }
    }

    .comment {
      min-height: 25px;
      display: flex;
      align-items: center;
    }

    .rating-point {
      flex-shrink: 0;
    }

    .only-mobile({
      width: 100%;
      margin-right: 0;
      table-layout: fixed;
    });
  }

  &__table-row {
    border-bottom: 1px solid #e7ebed;
  }


}

.review-rating-dialog {
  .only-mobile({
    .foquz-dialog__content {
      height: 100%;
      overflow: hidden;
    }
    .foquz-dialog__body {
      padding-bottom: 10px;
      overflow: hidden;
    }
    .review-rating__table-wrapper {
      max-height: 100%;
    }
  });
}
