import { DialogWrapper } from "Dialogs/wrapper";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.title = params.title;
    this.comments = params.comments.map((comment) => {
      if (typeof comment === "string") return comment;
      if (typeof comment === "object" && "name" in comment) {
        return [comment.surname, comment.name, comment.patronymic]
          .filter(Boolean)
          .join(" ");
      }
      return comment.toString();
    });
  }
}
