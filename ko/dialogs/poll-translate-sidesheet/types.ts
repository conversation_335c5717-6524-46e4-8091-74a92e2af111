import { COMMON_ELEMENTS } from "./constants";

/**
 * Перевод блока полей
 */

import { FullPollQuestion } from "@/entities/models/poll-question/types";
import { Filial } from "@/entities/structures/filial/types";


export type TranslationConfig = {
  question: FullPollQuestion;
  questions: Array<FullPollQuestion>;
  lang: KnockoutObservable<string>;
  filials: Array<Filial>;
};

export interface ITranslation {
  fields: {
    [key: string]: any;
  };
  getData: () => {
    [key: string]: any;
  };
  updateTranslation: (data: any) => void;
  isValid: () => boolean;
}

/**
 * Перевод текстового поля
 */
export type TextFieldTranslation = {
  value: KnockoutObservable<string>;
  original: string;
};

export type TextFieldTranslationResult = string;

/**
 * Перевод элемента галереи
 */
export type GalleryElementTranslation = {
  id: string;
  originalUrl: string;
  originalPoster: string;
  originalDescription: string;
  description: KnockoutObservable<string>;
  url: KnockoutObservable<string>;
  poster: KnockoutObservable<string>;
  file: KnockoutObservable<any>;
};

export type GalleryElementTranslationResult = {
  id: string;
  description: string;
};

/**
 * Перевод варианта
 */
export type VariantTranslation = {
  id: string;
  donorId?: string;
  original: string;
  description: KnockoutObservable<string>;
  value: KnockoutObservable<string>;
  type?: number;
  file_id?: number,
  file_url?: string,
  preview_url?: string,
  original_description: string;
};

export type VariantTranslationResult = {
  id: string;
  question: string;
  description: string;
};

export type NpsVariantTranslationResult = {
  id: string;
  question: string;
  description: string;
  detail_question?: string;
  placeholder_text?: string;
  self_variant_text?: string;
  self_variant_placeholder_text?: string;
};

/**
 * Перевод 3D матрица
 */
export type Matrix3DElementTranslation = {
  id: string;
  donorId?: string;
  original: string;
  value: KnockoutObservable<string>;
  children?: any;
  description?: KnockoutObservable<string>;
};

export type Matrix3DElementTranslationResult = {
  matrix_element_id: number;
  name: string;
  description: string;
};

export type Matrix3DVariantTranslationResult = {
  variant_id: number;
  name: string;
  description: string;
};

/**
 * Перевод основных полей вопроса
 */
export type QuestionBaseFieldsTranslation = {
  name: TextFieldTranslation;
  text: TextFieldTranslation;
  description: TextFieldTranslation;
  gallery?: Array<GalleryElementTranslation>;
  commentPlaceholder?: TextFieldTranslation;
  commentLabel?: TextFieldTranslation;
};

export type QuestionBaseFieldsTranslationResult = {
  name: TextFieldTranslationResult;
  description: TextFieldTranslationResult;
  sub_description: TextFieldTranslationResult;
  gallery?: Array<GalleryElementTranslationResult>;
  placeholder_text?: TextFieldTranslationResult;
  comment_label?: TextFieldTranslationResult;
};

/**
 * Перевод уточняющего вопроса
 */
export type ClarifyingQuestionTranslation = {
  donorId?: string,
  text: TextFieldTranslation;
  textAnswerPlaceholder?: TextFieldTranslation;
  variants?: Array<VariantTranslation>;
  selfAnswerText?: TextFieldTranslation;
  selfAnswerPlaceholder?: TextFieldTranslation;
  selfVariantFile?: object;
};

export type ClarifyingQuestionTranlationResult = {
  detail_question?: string;
  placeholder_text?: string;
  self_variant_text?: string;
  detail?: Array<VariantTranslationResult>;
};

/**
 * Перевод изображения с промежуточного экрана
 */
export type InterblockImageTranslation = {
  id: string;
  originalUrl: string;
  originalDescription: string;
  description: KnockoutObservable<string>;
  url: KnockoutObservable<string>;
  file: KnockoutObservable<any>;
};

export type InterblockImageTranslationResult = {
  logoId: string;
  description: string;
};

/**
 * Сохраненные переводы
 */
export type QuestionSavedTranslation = {
  name: string;
  description: string;
  description_html: string;
  sub_description: string;
  fileLangs?: Array<FileSavedTranslation>;
  placeholder_text?: string;
  comment_label?: string;
};

export type FileSavedTranslation = {
  foquz_question_file_id: string;
  end_screen_logo_id: string;
  description: string;
  file_path: string;
};

export type VariantSavedTranslation = {
  id: number;
  foquz_question_detail_id: number;
  question: string;
  description: string;
  detailLangs?: Array<VariantSavedTranslation>;
  detail_question?: string;
  self_variant_text?: string;
  self_variant_placeholder_text?: string;
  placeholder_text?: string;
};

export type Matrix3DElementSavedTranslation = {
  matrix_element_id: number;
  id: string;
  name: string;
  description: string;
};

export type ClarifyingQuestionSavedTranslation = {
  detailLangs?: Array<VariantSavedTranslation>;
  detail_question?: string;
  self_variant_text?: string;
  self_variant_placeholder_text?: string;
  placeholder_text?: string;
};
