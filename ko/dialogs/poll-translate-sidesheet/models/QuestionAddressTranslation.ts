import { IPollQuestionAddress } from "@/entities/models/poll-question/questionTypes/address.types";
import {
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  QuestionSavedTranslation,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
} from "../types";

type QuestionAddressTranslationFields = QuestionBaseFieldsTranslation & {
  placeholder: TextFieldTranslation;
};
type QuestionAddressTranslationResult = QuestionBaseFieldsTranslationResult & {
  placeholder_text: TextFieldTranslationResult;
};

type QuestionAddressSavedTranslation = QuestionSavedTranslation & {
  placeholder_text: string;
};
export class QuestionAddressTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionAddressTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionAddress;

    const { placeholder } = question;

    this.fields.placeholder = QuestionTextFieldTranslation(placeholder);
  }

  getData(): QuestionAddressTranslationResult {
    const result: QuestionAddressTranslationResult = {
      ...super.getData(),
      placeholder_text: QuestionTextFieldResult(this.fields.placeholder),
    };

    return result;
  }

  updateTranslation(data: QuestionAddressSavedTranslation): void {
    super.updateTranslation(data);

    const { placeholder_text } = data;

    updateQuestionTextFieldTranslation(
      this.fields.placeholder,
      placeholder_text
    );
  }
}
