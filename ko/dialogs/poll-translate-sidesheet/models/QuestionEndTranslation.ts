import { IPollQuestionInterscreen } from "@/entities/models/poll-question/questionTypes/inter.types";
import { DiscountPool } from "@/entities/models/discount-pool";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  FileSavedTranslation,
  InterblockImageTranslation,
  InterblockImageTranslationResult,
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
} from "../types";
import { InterblockImage } from "@/entities/models/poll-question/interblockImage/types";
import {
  QuestionInterblockImageTranslation,
  QuestionInterblockImageResult,
  updateQuestionInterblockIamgeTranslation,
} from "./InterblockImageTranslation";
import { QuestionTranslation } from "./QuestionTranslation";
import { uploadLangInterscreenImage } from "@/api/question/files";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionEndTranslationFields = QuestionBaseFieldsTranslation & {
  text: TextFieldTranslation;
  reportButtonText?: TextFieldTranslation;
  restartButtonText?: TextFieldTranslation;
  readyButtonText?: TextFieldTranslation;
  unsubscribeButtonText?: TextFieldTranslation;
  closeButtonForWidgetText?: TextFieldTranslation;
  complaintButtonText?: TextFieldTranslation;
  images?: Array<InterblockImageTranslation>;
  promocode: typeof DiscountPool;
};

type InterblockSettings = {
  id: string;
  text: TextFieldTranslationResult;
  scores_button_text?: TextFieldTranslationResult;
  start_over_button_text?: TextFieldTranslationResult;
  unsubscribe_button_text?: TextFieldTranslationResult;
  close_widget_button_text?: TextFieldTranslationResult;
  complaint_button_text?: TextFieldTranslationResult;
  ready_button_text?: TextFieldTranslationResult;
  pool_id: number;
};

type QuestionEndTextFieldTranslationResult = {
  intermediateSetting: InterblockSettings;
  gallery?: Array<InterblockImageTranslationResult>;
};

type QuestionEndSavedTranslation = QuestionSavedTranslation & {
  settingLangs: Array<InterblockSettings>;
  fileLangs: Array<FileSavedTranslation>;
};

const { computed } = ko;

export class QuestionEndTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionEndTranslationFields;
  interscreenId: string;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionInterscreen;

    const { interscreenConfig, images } = question;

    const {
      id,
      text,
      reportButtonEnabled,
      reportButtonText,
      restartButtonEnabled,
      readyButtonEnabled,
      readyButtonText,
      restartButtonText,
      unsubscribeButtonEnabled,
      unsubscribeButtonText,
      closeButtonForWidgetEnabled,
      closeButtonForWidgetText,
      complaintButtonEnabled,
      complaintButtonText,
      poolId,
    } = interscreenConfig;

    this.interscreenId = id;

    const promocode = new DiscountPool();
    promocode.update({
      poolId,
    });

    this.fields.text = QuestionTextFieldTranslation(text);
    this.fields.promocode = promocode;

    if (reportButtonEnabled) {
      this.fields.reportButtonText = QuestionTextFieldTranslation(
        reportButtonText || DEFAULT_TEXTS.reportButtonText
      );
    }

    if (restartButtonEnabled) {
      this.fields.restartButtonText = QuestionTextFieldTranslation(
        restartButtonText || DEFAULT_TEXTS.restartButtonText
      );
    }

    if (unsubscribeButtonEnabled) {
      this.fields.unsubscribeButtonText = QuestionTextFieldTranslation(
        unsubscribeButtonText || DEFAULT_TEXTS.unsubscribeButtonText
      );
    }

    if (closeButtonForWidgetEnabled) {
      this.fields.closeButtonForWidgetText = QuestionTextFieldTranslation(
        closeButtonForWidgetText || DEFAULT_TEXTS.closeButtonForWidgetText
      );
    }

    if (complaintButtonEnabled) {
      this.fields.complaintButtonText = QuestionTextFieldTranslation(
        complaintButtonText || DEFAULT_TEXTS.complaintButtonText
      );
    }

    if (readyButtonEnabled) {
      this.fields.readyButtonText = QuestionTextFieldTranslation(
        readyButtonText || DEFAULT_TEXTS.readyButtonText
      );
    }

    if (images.length) {
      this.fields.images = images.map(
        (i: InterblockImage): InterblockImageTranslation =>
          QuestionInterblockImageTranslation(i)
      );

      computed(() => {
        const errors = {};
        let hasErrors = false;

        this.fields.images.forEach((item: InterblockImageTranslation) => {
          if (!item.url() && !item.file()) {
            errors[item.id] = "Обязательное поле";
            hasErrors = true;
          }
        });

        this.setError("images", hasErrors ? errors : null);
      });
    }
  }

  getData(): QuestionEndTextFieldTranslationResult {
    const promocodeData = this.fields.promocode.getData();

    const settings: InterblockSettings = {
      id: this.interscreenId,
      text: QuestionTextFieldResult(this.fields.text),
      pool_id: promocodeData.poolId,
    };

    if (this.fields.reportButtonText) {
      settings.scores_button_text = QuestionTextFieldResult(
        this.fields.reportButtonText
      );
    }

    if (this.fields.restartButtonText) {
      settings.start_over_button_text = QuestionTextFieldResult(
        this.fields.restartButtonText
      );
    }

    if (this.fields.unsubscribeButtonText) {
      settings.unsubscribe_button_text = QuestionTextFieldResult(
        this.fields.unsubscribeButtonText
      );
    }

    if (this.fields.closeButtonForWidgetText) {
      settings.close_widget_button_text = QuestionTextFieldResult(
        this.fields.closeButtonForWidgetText
      );
    }

    if (this.fields.complaintButtonText) {
      settings.complaint_button_text = QuestionTextFieldResult(
        this.fields.complaintButtonText
      );
    }

    if (this.fields.readyButtonText) {
      settings.ready_button_text = QuestionTextFieldResult(
        this.fields.readyButtonText
      );
    }

    const result: QuestionEndTextFieldTranslationResult = {
      intermediateSetting: settings,
    };

    if (this.fields.images) {
      result.gallery = this.fields.images.map(
        (i: InterblockImageTranslation): InterblockImageTranslationResult => {
          return QuestionInterblockImageResult(i);
        }
      );
    }

    return result;
  }

  loadInterscreenFile(galleryElement, file) {
    uploadLangInterscreenImage(this.lang(), galleryElement.id, file);
  }

  updateTranslation({
    settingLangs = [],
    fileLangs,
  }: QuestionEndSavedTranslation): void {
    const {
      text,
      complaint_button_text,
      unsubscribe_button_text,
      ready_button_text,
      scores_button_text,
      start_over_button_text,
      close_widget_button_text,
    } = settingLangs[0] || {};

    updateQuestionTextFieldTranslation(this.fields.text, text);
    updateQuestionTextFieldTranslation(
      this.fields.unsubscribeButtonText,
      unsubscribe_button_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.closeButtonForWidgetText,
      close_widget_button_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.complaintButtonText,
      complaint_button_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.reportButtonText,
      scores_button_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.restartButtonText,
      start_over_button_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.readyButtonText,
      ready_button_text
    );

    this.updateImagesTranslation(fileLangs);
  }

  updateImagesTranslation(translation: Array<FileSavedTranslation> = []) {
    if (!this.fields.images) return;
    this.fields.images.forEach((item: InterblockImageTranslation) => {
      const { id } = item;
      const fileTranslation = translation.find((file: FileSavedTranslation) => {
        return file.end_screen_logo_id.toString() === id;
      });

      updateQuestionInterblockIamgeTranslation(item, fileTranslation);
    });
  }
}
