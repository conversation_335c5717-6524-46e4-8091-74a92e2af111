import { InterblockImage } from "@/entities/models/poll-question/interblockImage/types";
import {
  FileSavedTranslation,
  InterblockImageTranslationResult,
} from "../types";
import { InterblockImageTranslation } from "../types";

const { observable } = ko;

export function QuestionInterblockImageTranslation(
  data: InterblockImage
): InterblockImageTranslation {
  const { id, url, description } = data;

  return {
    id,
    originalUrl: url,
    originalDescription: description,
    description: observable(""),
    url: observable(url),
    file: observable(null),
  };
}

export function QuestionInterblockImageResult(
  translation: InterblockImageTranslation
): InterblockImageTranslationResult {
  const { id, description } = translation;
  return {
    logoId: id,
    description: description(),
  };
}

export function updateQuestionInterblockIamgeTranslation(
  item: InterblockImageTranslation,
  translation: FileSavedTranslation
) {
  let newDescription = "";
  let newUrl = item.url();

  if (translation) {
    const { file_path, description } = translation;
    if (file_path) newUrl = `/${file_path}`;
    newDescription = description || "";
  }

  item.url(newUrl);
  item.description(newDescription);
}
