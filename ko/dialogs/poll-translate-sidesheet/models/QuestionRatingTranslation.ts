import { IPollQuestionRating } from "@/entities/models/poll-question/questionTypes/rating.types";
import {
  QuestionClarifyingQuestionTranslation,
  QuestionsClarifyingQuestionResult,
  updateClarifyingQuestionTranslation,
} from "./ClarifyingQuestionTranslation";

import {
  ITranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  ClarifyingQuestionTranslation,
  ClarifyingQuestionTranlationResult,
  TranslationConfig,
  QuestionSavedTranslation,
  ClarifyingQuestionSavedTranslation,
} from "../types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionRatingTranslationFields = QuestionBaseFieldsTranslation & {
  labels: Array<TextFieldTranslation>;
  skipText?: TextFieldTranslation;
  clarifyingQuestion?: ClarifyingQuestionTranslation;
};
type QuestionRatingTranslationResult = QuestionBaseFieldsTranslationResult & {
  labels: Array<TextFieldTranslationResult>;
  skip_text?: TextFieldTranslationResult;
} & Partial<ClarifyingQuestionTranlationResult>;

type QuestionRatingSavedTranslation = QuestionSavedTranslation & {
  labels: Array<string>;
  skip_text?: string;
} & ClarifyingQuestionSavedTranslation;

export class QuestionRatingTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionRatingTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionRating;

    const {
      starRating,
      skip,
      skipText,
      clarifyingQuestion,
    } = question;

    this.fields.labels = [];

    if (starRating) {
      const { count, labels } = starRating;

      const labelsArray = Array(count)
        .fill(null)
        .map((_, i: number): TextFieldTranslation => {
          return QuestionTextFieldTranslation(labels[i]);
        });
      this.fields.labels = labelsArray;
    }

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }

    if (clarifyingQuestion.enabled) {
      this.fields.clarifyingQuestion =
        QuestionClarifyingQuestionTranslation(clarifyingQuestion);
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionRatingTranslationResult {
    let result: QuestionRatingTranslationResult = {
      ...super.getData(),

      labels: this.fields.labels.map(
        (label: TextFieldTranslation): TextFieldTranslationResult => {
          return QuestionTextFieldResult(label);
        }
      ),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    if (this.fields.clarifyingQuestion) {
      result = {
        ...result,
        ...QuestionsClarifyingQuestionResult(this.fields.clarifyingQuestion),
      };
    }

    return result;
  }

  updateTranslation(data: QuestionRatingSavedTranslation): void {
    super.updateTranslation(data);

    const { labels, skip_text } = data;

    this.fields.labels.forEach((label: TextFieldTranslation, i: number) => {
      const newValue = (labels && labels[i]) || "";
      updateQuestionTextFieldTranslation(label, newValue);
    });

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
    updateClarifyingQuestionTranslation(this.fields.clarifyingQuestion, data);
  }
}
