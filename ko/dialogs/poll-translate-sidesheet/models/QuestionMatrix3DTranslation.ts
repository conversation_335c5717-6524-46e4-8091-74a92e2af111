import { IPollQuestionMatrix3D } from "@/entities/models/poll-question/questionTypes/matrix-3d.types";
import { GalleryElement } from "@/entities/models/poll-question/questionGallery/types";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import { FullPollQuestion } from "@/entities/models/poll-question/types";
import {
  QuestionGalleryElementTranslation,
  QuestionGalleryElementResult,
} from "./GalleryElementTranslation";
import {
  ITranslation,
  Matrix3DElementTranslation,
  GalleryElementTranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  Matrix3DElementTranslationResult,
  GalleryElementTranslationResult,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  FileSavedTranslation,
  QuestionSavedTranslation,
  Matrix3DElementSavedTranslation,
} from "../types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  QuestionMatrix3DElementTranslation,
  QuestionMatrix3DElementResult,
  QuestionMatrix3DVariantResult,
  updateQuestionMatrix3DElementTranslation,
} from "./Matrix3DTranslation";

import { getAllRecipientVariants } from "@/utils/questions/donor";
import { syncVariants } from "../../../utils/questions/donor";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionMatrix3DTranslationFields = QuestionBaseFieldsTranslation & {
  rows: Array<Matrix3DElementTranslation>;
  cols: Array<Matrix3DElementTranslation>;
  skipText?: TextFieldTranslation;
  selectPlaceholder?: TextFieldTranslation;
};

type QuestionMatrix3DTranslationResult = QuestionBaseFieldsTranslationResult & {
  detail: Array<Matrix3DElementTranslationResult>;
  skip_text?: TextFieldTranslationResult;
  gallery?: Array<GalleryElementTranslationResult>;
  self_variant_text?: TextFieldTranslationResult;
  placeholder_text?: TextFieldTranslationResult;
  matrixElements: any;
  matrixVariants: any;
};

type QuestionMatrix3DSavedTranslation = QuestionSavedTranslation & {
  fileLangs?: Array<FileSavedTranslation>;
  detailLangs?: Array<Matrix3DElementSavedTranslation>;
  self_variant_text?: string;
  skip_text?: string;
  placeholder_text?: string;
  matrixElements: any;
  matrixVariants: any;
};

export class QuestionMatrix3DTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionMatrix3DTranslationFields;
  hasSelfAnswer: boolean = false;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionMatrix3D;
    console.log('2222222222222222222')
    console.log(question)

    const {
      rows,
      cols,
      skip,
      skipText,
      donorId,
      selectPlaceholder,
    } = question;

    if (donorId) {
      // ToDo
      // const donorVariants = getAllRecipientVariants(question, config.questions);
      // variantsList = syncVariants(donorVariants, variants);
    }

    this.fields.rows = rows.map(
      (variant: QuestionVariant): Matrix3DElementTranslation => {
        return QuestionMatrix3DElementTranslation(variant);
      }
    );
    this.fields.cols = cols.map(
      (variant: QuestionVariant): Matrix3DElementTranslation => {
        return QuestionMatrix3DElementTranslation(variant);
      }
    );

    this.fields.selectPlaceholder = QuestionTextFieldTranslation(
      selectPlaceholder || ''
    );

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipTextVariants
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionMatrix3DTranslationResult {
    const result: QuestionMatrix3DTranslationResult = {
      ...super.getData(),
    };

    result.matrixElements = [
      ...this.fields.rows.map(el => {
        return QuestionMatrix3DElementResult(el);
      }),
      ...this.fields.cols.map(el => {
        return QuestionMatrix3DElementResult(el);
      }),
    ]

    result.matrixVariants = [
      ...this.fields.cols.reduce((acc, el) => {
        return [...acc, ...el.children.map(el => QuestionMatrix3DVariantResult(el))];
      }, []),
    ]

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    result.placeholder_text = QuestionTextFieldResult(
      this.fields.selectPlaceholder
    );

    return result;
  }

  updateTranslation(data: QuestionMatrix3DSavedTranslation): void {
    super.updateTranslation(data);

    const {
      matrixElements,
      matrixVariants,
      skip_text,
      placeholder_text,
    } = data;

    updateQuestionTextFieldTranslation(
      this.fields.selectPlaceholder,
      placeholder_text
    );

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
    
    this.fields.rows.forEach(el => {
      return QuestionMatrix3DElementResult(el);
    })

    this.fields.rows.forEach((row: Matrix3DElementTranslation) => {
      const variantTranslation = (matrixElements || []).find(
        (v: Matrix3DElementSavedTranslation) => `${v.matrix_element_id}` === `${row.id}`
      );
      updateQuestionMatrix3DElementTranslation(row, variantTranslation);
    });
    this.fields.cols.forEach((col: Matrix3DElementTranslation) => {
      const variantTranslation = (matrixElements || []).find(
        (v: Matrix3DElementSavedTranslation) => `${v.matrix_element_id}` === `${col.id}`
      );
      updateQuestionMatrix3DElementTranslation(col, variantTranslation, matrixVariants);
    });
  }
}
