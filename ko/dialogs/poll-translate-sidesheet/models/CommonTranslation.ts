import { LangSettings } from "@/entities/models/poll/types";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import {
  ITranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
} from "../types";
import {
  QuestionTextFieldResult,
  QuestionTextFieldTranslation,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";

type CommonTranslationFields = {
  backButtonText: TextFieldTranslation;
  forwardButtonText: TextFieldTranslation;
  finishButtonText: TextFieldTranslation;
  optionalLabelText: TextFieldTranslation;
};

type CommonTranslationResult = {
  back_text: TextFieldTranslationResult;
  next_text: TextFieldTranslationResult;
  finish_text: TextFieldTranslationResult;
  unrequired_text: TextFieldTranslationResult;
};

export class CommonTranslation implements ITranslation {
  fields: CommonTranslationFields;

  constructor(lang: LangSettings) {
    const { backButtonText, forwardButtonText, finishButtonText,  optionalText } = lang;
    this.fields = {
      backButtonText: QuestionTextFieldTranslation(
        backButtonText || DEFAULT_TEXTS.backButtonText,
        ""
      ),
      forwardButtonText: QuestionTextFieldTranslation(
        forwardButtonText || DEFAULT_TEXTS.forwardButtonText,
        ""
      ),
      finishButtonText: QuestionTextFieldTranslation(
        finishButtonText || DEFAULT_TEXTS.finishButtonText,
        ""
      ),
      optionalLabelText: QuestionTextFieldTranslation(
        optionalText === null ? DEFAULT_TEXTS.optionalLabelText : optionalText,
        ""
      ),
    };
  }

  getData(): CommonTranslationResult {
    const result: CommonTranslationResult = {
      back_text: QuestionTextFieldResult(this.fields.backButtonText),
      next_text: QuestionTextFieldResult(this.fields.forwardButtonText),
      finish_text: QuestionTextFieldResult(this.fields.finishButtonText),
      unrequired_text: QuestionTextFieldResult(this.fields.optionalLabelText),
    };

    return result;
  }

  updateTranslation(data: CommonTranslationResult) {
    const { back_text, next_text, finish_text, unrequired_text } = data;
    updateQuestionTextFieldTranslation(this.fields.backButtonText, back_text);
    updateQuestionTextFieldTranslation(
      this.fields.forwardButtonText,
      next_text,
    );
    updateQuestionTextFieldTranslation(
      this.fields.finishButtonText,
      finish_text,
    );
    updateQuestionTextFieldTranslation(
      this.fields.optionalLabelText,
      unrequired_text,
    );
  }

  isValid() {
    return true;
  }
}
