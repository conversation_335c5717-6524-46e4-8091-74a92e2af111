import { IPollQuestionPriority } from "@/entities/models/poll-question/questionTypes/priority.types";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TranslationConfig,
  VariantSavedTranslation,
  VariantTranslation,
  VariantTranslationResult,
} from "../types";
import {
  QuestionVariantTranslation,
  QuestionVariantResult,
  updateQuestionVariantTranslation,
} from "./VariantTranslation";
import { getAllRecipientVariants, syncVariants } from "@/utils/questions/donor";
import { QuestionTranslation } from "./QuestionTranslation";

type QuestionPriorityTranslationFields = QuestionBaseFieldsTranslation & {
  variants: Array<VariantTranslation>;
};
type QuestionPriorityTranslationResult = QuestionBaseFieldsTranslationResult & {
  detail: Array<VariantTranslationResult>;
};

type QuestionPrioritySavedTranslation = QuestionSavedTranslation & {
  detailLangs?: Array<VariantSavedTranslation>;
};

export class QuestionPriorityTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionPriorityTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionPriority;

    const { variants, donorId } = question;

    let variantsList = variants;

    if (donorId) {
      const donorVariants = getAllRecipientVariants(question, config.questions);
      if (donorVariants.some(i => i.id === '-1') && !donorVariants.find(i => i.id === '-1').text.length) {
        donorVariants.find(i => i.id === '-1').text = 'Свой вариант'
      }
      variantsList = syncVariants(donorVariants, variants);
    }

    this.fields.variants = variantsList.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionPriorityTranslationResult {
    const result: QuestionPriorityTranslationResult = {
      ...super.getData(),
      detail: this.fields.variants.map(
        (variant: VariantTranslation): VariantTranslationResult => {
          return QuestionVariantResult(variant);
        }
      ),
    };

    return result;
  }

  updateTranslation(data: QuestionPrioritySavedTranslation): void {
    super.updateTranslation(data);

    const { detailLangs } = data;

    this.fields.variants.forEach((variant: VariantTranslation) => {
      const variantTranslation = (detailLangs || []).find(
        (v: VariantSavedTranslation) => {
          if (variant.id === "-1") return !v.foquz_question_detail_id;

          return (
            v.foquz_question_detail_id &&
            v.foquz_question_detail_id.toString() === variant.id
          );
        }
      );
      updateQuestionVariantTranslation(variant, variantTranslation);
    });
  }
}
