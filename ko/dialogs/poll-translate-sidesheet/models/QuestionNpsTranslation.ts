import { IPollQuestionNps } from "@/entities/models/poll-question/questionTypes/nps.types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  QuestionSavedTranslation,
  VariantTranslation,
  NpsVariantTranslationResult,
  VariantSavedTranslation,
  ClarifyingQuestionTranslation,
  ClarifyingQuestionTranlationResult,
  ClarifyingQuestionSavedTranslation,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import { getAllRecipientVariants, syncVariants } from "@/utils/questions/donor";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import { QuestionVariantResult, QuestionVariantTranslation, updateQuestionVariantTranslation } from "./VariantTranslation";
import {
  QuestionClarifyingQuestionTranslation,
  QuestionsClarifyingQuestionResult,
  updateClarifyingQuestionTranslation,
} from "./ClarifyingQuestionTranslation";


type QuestionNpsTranslationFields = QuestionBaseFieldsTranslation & {
  skipText?: TextFieldTranslation;
  labels: [TextFieldTranslation, TextFieldTranslation];
  variants: Array<VariantTranslation>;
  clarifyingQuestion?: ClarifyingQuestionTranslation;
  clarifyingToEachQuestion?: Array<ClarifyingQuestionTranslation>;
};
type QuestionNpsTranslationResult = QuestionBaseFieldsTranslationResult & {
  skip_text?: TextFieldTranslationResult;
  detail: Array<NpsVariantTranslationResult>;
  labels: [TextFieldTranslationResult, TextFieldTranslationResult];
} & Partial<ClarifyingQuestionTranlationResult>;

type QuestionNpsSavedTranslation = QuestionSavedTranslation & {
  skip_text?: string;
  labels: string;
  detailLangs?: Array<VariantSavedTranslation>
  & ClarifyingQuestionSavedTranslation;
};

export class QuestionNpsTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionNpsTranslationFields;
  setVariants: boolean;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionNps;

    const {
      npsConfig,
      skip,
      skipText,
      setVariants,
      variants,
      donorId,
      clarifyingQuestion,
      clarifyingToEachQuestion
    } = question;

    this.setVariants = setVariants;

    let variantsList = variants;

    if (setVariants) {
      if (donorId) {
        const donorVariants = getAllRecipientVariants(
          question,
          config.questions
        );
        variantsList = syncVariants(donorVariants, variants);
      }
    }

    this.fields.variants = variantsList.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );

    const { startLabel, endLabel } = npsConfig;

    this.fields.labels = [
      QuestionTextFieldTranslation(startLabel),
      QuestionTextFieldTranslation(endLabel),
    ];

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }

    if (clarifyingQuestion.enabled) {
      this.fields.clarifyingQuestion =
        QuestionClarifyingQuestionTranslation(clarifyingQuestion);
    }

    if (clarifyingToEachQuestion) {
      this.fields.clarifyingToEachQuestion = clarifyingToEachQuestion.map(QuestionClarifyingQuestionTranslation);
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionNpsTranslationResult {
    const result: QuestionNpsTranslationResult = {
      ...super.getData(),
      detail: this.fields.variants.map(
        (variant: VariantTranslation): NpsVariantTranslationResult => {
          return QuestionVariantResult(variant);
        }
      ),
      labels: [
        QuestionTextFieldResult(this.fields.labels[0]),
        QuestionTextFieldResult(this.fields.labels[1]),
      ],
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    if (this.fields.clarifyingToEachQuestion) {
      this.fields.clarifyingToEachQuestion.forEach((item) => {
        const clarifyingQuestion = QuestionsClarifyingQuestionResult(item);
        console.log('getData', clarifyingQuestion)
        const variant = result.detail.find((variant) => variant.id === item.donorId);
        if (variant) {
          variant.detail_question = clarifyingQuestion.detail_question
          variant.placeholder_text = clarifyingQuestion.placeholder_text
          variant.self_variant_placeholder_text = clarifyingQuestion.placeholder_text
          variant.self_variant_text = clarifyingQuestion.self_variant_text
          if (clarifyingQuestion.detail) {
            result.detail = [...result.detail, ...clarifyingQuestion.detail]
          }
        }
      })
    } else if (this.fields.clarifyingQuestion) {
      const clarifyingQuestion = QuestionsClarifyingQuestionResult(this.fields.clarifyingQuestion)
     
      result.detail_question = clarifyingQuestion.detail_question
      result.placeholder_text = clarifyingQuestion.placeholder_text
      result.self_variant_text = clarifyingQuestion.self_variant_text
      if (clarifyingQuestion.detail) {
        result.detail = [...result.detail, ...clarifyingQuestion.detail]
      }
    }

    return result;
  }

  updateTranslation(data: QuestionNpsSavedTranslation): void {
    super.updateTranslation(data);

    console.log('updateTranslation', data)

    const { detailLangs, labels, skip_text } = data;

    this.fields.variants.forEach((variant: VariantTranslation) => {
      const variantTranslation = (detailLangs || []).find(
        (v: VariantSavedTranslation) => {
          if (variant.id === "-1") return !v.foquz_question_detail_id;

          return (
            v.foquz_question_detail_id &&
            v.foquz_question_detail_id.toString() === variant.id
          );
        }
      );
      updateQuestionVariantTranslation(variant, variantTranslation);
      
    });

    let _labels = [];
    if (typeof labels === "object") {
      _labels = labels;
    } else if (labels) {
      try {
        _labels = JSON.parse(labels)
      } catch(e) {

      }
    }

    this.fields.labels.forEach((label: TextFieldTranslation, i) => {
      const newValue = (_labels && _labels[i]) || "";
      updateQuestionTextFieldTranslation(label, newValue);
    });

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
    if (this.fields.clarifyingToEachQuestion) {
      this.fields.clarifyingToEachQuestion.forEach((clarifying) => {
        updateClarifyingQuestionTranslation(clarifying, data, true);
      })
    } else {
      updateClarifyingQuestionTranslation(this.fields.clarifyingQuestion, data);
    }
  }
}
