import { GalleryElement } from "@/entities/models/poll-question/questionGallery/types";
import { FileSavedTranslation } from "../types";
import {
  GalleryElementTranslation,
  GalleryElementTranslationResult,
} from "../types";

const { observable } = ko;

export function QuestionGalleryElementTranslation(
  data: GalleryElement
): GalleryElementTranslation {
  const { id, url, poster, description } = data;

  return {
    id,
    originalUrl: url,
    originalPoster: poster,
    originalDescription: description,
    description: observable(""),
    poster: observable(poster),
    url: observable(url),
    file: observable(null),
  };
}

export function QuestionGalleryElementResult(
  translation: GalleryElementTranslation
): GalleryElementTranslationResult {
  const { id, description } = translation;
  return {
    id,
    description: description(),
  };
}

export function updateQuestionGalleryElementTranslation(
  item: GalleryElementTranslation,
  translation: FileSavedTranslation
) {
  let newDescription = "";
  let newUrl = item.url();
  let newPoster = item.poster();

  if (translation) {
    const { file_path, description } = translation;
    if (file_path) newUrl = `/${file_path}`;
    newDescription = description || "";
  }

  console.log("update gallery element", { item, translation });

  item.url(newUrl);
  item.poster(newPoster);
  item.description(newDescription);
}
