import { IPollQuestionVariants } from "@/entities/models/poll-question/questionTypes/variants.types";
import { GalleryElement } from "@/entities/models/poll-question/questionGallery/types";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import { FullPollQuestion } from "@/entities/models/poll-question/types";
import {
  QuestionGalleryElementTranslation,
  QuestionGalleryElementResult,
} from "./GalleryElementTranslation";
import {
  ITranslation,
  VariantTranslation,
  GalleryElementTranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  VariantTranslationResult,
  GalleryElementTranslationResult,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  FileSavedTranslation,
  QuestionSavedTranslation,
  VariantSavedTranslation,
} from "../types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  QuestionVariantTranslation,
  QuestionVariantResult,
  updateQuestionVariantTranslation,
} from "./VariantTranslation";

import { getAllRecipientVariants } from "@/utils/questions/donor";
import { syncVariants } from "../../../utils/questions/donor";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionVariantsTranslationFields = QuestionBaseFieldsTranslation & {
  variants: Array<VariantTranslation>;
  skipText?: TextFieldTranslation;
  selfAnswerText?: TextFieldTranslation;
  selfAnswerDescription?: TextFieldTranslation;
  selfAnswerPlaceholder?: TextFieldTranslation;
  commentLabel?: TextFieldTranslation;
  commentPlaceholder?: TextFieldTranslation;
};

type QuestionVariantsTranslationResult = QuestionBaseFieldsTranslationResult & {
  detail: Array<VariantTranslationResult>;
  skip_text?: TextFieldTranslationResult;
  gallery?: Array<GalleryElementTranslationResult>;
  self_variant_text?: TextFieldTranslationResult;
  self_variant_description?: TextFieldTranslationResult;
  placeholder_text?: TextFieldTranslationResult;
  self_variant_placeholder_text?: TextFieldTranslationResult;
};

type QuestionVariantsSavedTranslation = QuestionSavedTranslation & {
  fileLangs?: Array<FileSavedTranslation>;
  detailLangs?: Array<VariantSavedTranslation>;
  self_variant_text?: string;
  self_variant_description?: string;
  skip_text?: string;
  placeholder_text?: string;
  self_variant_placeholder_text?: string;
};

export class QuestionVariantsTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionVariantsTranslationFields;
  hasSelfAnswer: boolean = false;
  hasVariantsComment: boolean = false;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionVariants;

    const {
      variants,
      skip,
      skipText,
      donorId,
      selfAnswer,
      selfAnswerText,
      selfAnswerPlaceholder,
      commentEnabled,
      comment,
      selfAnswerDescription
    } = question;

    let variantsList = variants.filter((v) => !v.deleted);

    if (donorId) {
      const donorVariants = getAllRecipientVariants(question, config.questions);
      variantsList = syncVariants(donorVariants, variants);
    }

    this.fields.variants = variantsList.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );

    if (commentEnabled) {
      this.hasVariantsComment = true;
      this.fields.commentLabel = QuestionTextFieldTranslation(
        comment.label || DEFAULT_TEXTS.commentLabel
      );
      this.fields.commentPlaceholder = QuestionTextFieldTranslation(
        comment.placeholder
      );
    }

    if (selfAnswer) {
      this.hasSelfAnswer = true;
      this.fields.selfAnswerText = QuestionTextFieldTranslation(
        selfAnswerText || DEFAULT_TEXTS.selfAnswerText
      );
      this.fields.selfAnswerDescription = QuestionTextFieldTranslation(
        selfAnswerDescription || DEFAULT_TEXTS.selfAnswerDescription
      );
      this.fields.selfAnswerPlaceholder = QuestionTextFieldTranslation(
        selfAnswerPlaceholder
      );
    }

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipTextVariants
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  getData(): QuestionVariantsTranslationResult {
    const result: QuestionVariantsTranslationResult = {
      ...super.getData(),
      detail: this.fields.variants.map(
        (variant: VariantTranslation): VariantTranslationResult => {
          return QuestionVariantResult(variant);
        }
      ),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    if (this.hasSelfAnswer) {
      result.self_variant_text = QuestionTextFieldResult(
        this.fields.selfAnswerText
      );
      result.self_variant_description = QuestionTextFieldResult(
        this.fields.selfAnswerDescription
      );
      result.self_variant_placeholder_text = QuestionTextFieldResult(
        this.fields.selfAnswerPlaceholder
      );
    }

    if (this.hasVariantsComment) {
      if (this.fields.commentPlaceholder) {
        result.placeholder_text = QuestionTextFieldResult(
          this.fields.commentPlaceholder
        );
      }
      if (this.fields.commentLabel) {
        result.comment_label = QuestionTextFieldResult(
          this.fields.commentLabel
        );
      }
    }

    return result;
  }

  updateTranslation(data: QuestionVariantsSavedTranslation): void {
    super.updateTranslation(data);

    const { detailLangs, skip_text, self_variant_text, placeholder_text, self_variant_placeholder_text, comment_label, self_variant_description } =
      data;

    updateQuestionTextFieldTranslation(
      this.fields.selfAnswerText,
      self_variant_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.selfAnswerDescription,
      self_variant_description
    );
    updateQuestionTextFieldTranslation(
      this.fields.selfAnswerPlaceholder,
      self_variant_placeholder_text
    );

    updateQuestionTextFieldTranslation(
      this.fields.commentLabel,
      comment_label
    );
    updateQuestionTextFieldTranslation(
      this.fields.commentPlaceholder,
      placeholder_text
    );

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);

    this.fields.variants.forEach((variant: VariantTranslation) => {
      const variantTranslation = (detailLangs || []).find(
        (v: VariantSavedTranslation) => {
          if (variant.id === "-1") return !v.foquz_question_detail_id;

          return (
            v.foquz_question_detail_id &&
            v.foquz_question_detail_id.toString() === variant.id
          );
        }
      );
      updateQuestionVariantTranslation(variant, variantTranslation);
    });
  }
}
