import { IPollQuestionMediaVariants } from "@/entities/models/poll-question/questionTypes/mediaVariants.types";
import {
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  QuestionSavedTranslation,
} from "../types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionMediaVariantsTranslationFields = QuestionBaseFieldsTranslation & {
  skipText?: TextFieldTranslation;
};
type QuestionMediaVariantsTranslationResult =
  QuestionBaseFieldsTranslationResult & {
    skip_text?: TextFieldTranslationResult;
  };

type QuestionMediaVariantsSavedTranslation = QuestionSavedTranslation & {
  skip_text?: string;
};
export class QuestionMediaVariantsTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionMediaVariantsTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionMediaVariants;

    const { skip, skipText } = question;
    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipTextVariants
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionMediaVariantsTranslationResult {
    const result: QuestionMediaVariantsTranslationResult = {
      ...super.getData(),
    };
    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionMediaVariantsSavedTranslation): void {
    super.updateTranslation(data);

    const { skip_text } = data;
    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
  }
}
