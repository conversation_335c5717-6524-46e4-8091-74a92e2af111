import {
  IPollQuestionRate,
  RateTypes,
  RateViews,
} from "@/entities/models/poll-question/questionTypes/rate.types";
import {
  ClarifyingQuestionSavedTranslation,
  ClarifyingQuestionTranlationResult,
  ClarifyingQuestionTranslation,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  VariantSavedTranslation,
  VariantTranslation,
  VariantTranslationResult,
} from "../types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
} from "../types";
import { GalleryElement } from "@/entities/models/poll-question/questionGallery/types";
import {
  QuestionClarifyingQuestionTranslation,
  QuestionsClarifyingQuestionResult,
  updateClarifyingQuestionTranslation,
} from "./ClarifyingQuestionTranslation";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import { QuestionTranslation } from "./QuestionTranslation";
import {
  QuestionVariantResult,
  QuestionVariantTranslation,
  updateQuestionVariantTranslation,
} from "./VariantTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionRateTranslationFields = QuestionBaseFieldsTranslation & {
  rateText?: TextFieldTranslation;
  clarifyingQuestion?: ClarifyingQuestionTranslation;
  commentPlaceholder?: TextFieldTranslation;
  commentLabel?: TextFieldTranslation;
  variants?: Array<VariantTranslation>;
  selfAnswerText?: TextFieldTranslation;
  selfAnswerPlaceholder?: TextFieldTranslation;
};
type QuestionRateTranslationResult = QuestionBaseFieldsTranslationResult & {
  text?: TextFieldTranslationResult;
  detail?: Array<VariantTranslationResult>;
  self_variant_text?: TextFieldTranslationResult;
  placeholder_text?: TextFieldTranslationResult;
  comment_label?: TextFieldTranslationResult;
} & Partial<ClarifyingQuestionTranlationResult>;
type QuestionRateSavedTranslation = QuestionSavedTranslation & {
  detailLangs?: Array<VariantSavedTranslation>;
  self_variant_text?: string;
  placeholder_text: string;
  comment_label?: string;
  text?: string;
} & ClarifyingQuestionSavedTranslation;
export class QuestionRateTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionRateTranslationFields;
  ratingType: RateTypes;
  gallery: Array<GalleryElement>;
  hasSelfAnswer: boolean;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionRate;

    const {
      viewType,
      ratingText,
      gallery,
      ratingType,
      variants,
      selfAnswer,
      selfAnswerText,
      selfAnswerPlaceholder,
      clarifyingQuestion,
      commentEnabled,
      comment,
    } = question;

    this.gallery = gallery;
    this.ratingType = ratingType;

    if (viewType === RateViews.Text) {
      this.fields.rateText = QuestionTextFieldTranslation(ratingText);
    }

    if (ratingType === RateTypes.Stars) {
      if (clarifyingQuestion.enabled) {
        this.fields.clarifyingQuestion =
          QuestionClarifyingQuestionTranslation(clarifyingQuestion);
      }

      if (commentEnabled) {
        this.fields.commentPlaceholder = QuestionTextFieldTranslation(
          comment.placeholder
        );
        this.fields.commentLabel = QuestionTextFieldTranslation(comment.label || DEFAULT_TEXTS.commentLabel);
      }
    } else {
      this.fields.variants = variants.map(
        (variant: QuestionVariant): VariantTranslation => {
          return QuestionVariantTranslation(variant);
        }
      );

      if (selfAnswer) {
        this.hasSelfAnswer = true;
        this.fields.selfAnswerText = QuestionTextFieldTranslation(
          selfAnswerText || DEFAULT_TEXTS.selfAnswerText
        );
        this.fields.selfAnswerPlaceholder = QuestionTextFieldTranslation(
          selfAnswerPlaceholder
        );
      }
    }
  }

  getData(): QuestionRateTranslationResult {
    let result: QuestionRateTranslationResult = {
      ...super.getData(),
    };

    if (this.fields.rateText) {
      result.text = QuestionTextFieldResult(this.fields.rateText);
    }

    if (this.ratingType === RateTypes.Stars) {
      if (this.fields.clarifyingQuestion) {
        result = {
          ...result,
          ...QuestionsClarifyingQuestionResult(this.fields.clarifyingQuestion),
        };
      }
      if (this.fields.commentPlaceholder) {
        result.placeholder_text = QuestionTextFieldResult(
          this.fields.commentPlaceholder
        );
      }
      if (this.fields.commentLabel) {
        result.comment_label = QuestionTextFieldResult(
          this.fields.commentLabel
        );
      }
    } else {
      result.detail = this.fields.variants.map(
        (variant: VariantTranslation): VariantTranslationResult => {
          return QuestionVariantResult(variant);
        }
      );

      if (this.hasSelfAnswer) {
        result.self_variant_text = QuestionTextFieldResult(
          this.fields.selfAnswerText
        );
        result.placeholder_text = QuestionTextFieldResult(
          this.fields.selfAnswerPlaceholder
        );
      }
    }

    return result;
  }

  updateTranslation(data: QuestionRateSavedTranslation): void {
    super.updateTranslation(data);
    const { text, placeholder_text, comment_label, self_variant_text, detailLangs } = data;

    updateQuestionTextFieldTranslation(this.fields.rateText, text);
    updateQuestionTextFieldTranslation(
      this.fields.commentPlaceholder,
      placeholder_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.commentLabel,
      comment_label
    );
    updateClarifyingQuestionTranslation(this.fields.clarifyingQuestion, data);

    updateQuestionTextFieldTranslation(
      this.fields.selfAnswerText,
      self_variant_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.selfAnswerPlaceholder,
      placeholder_text
    );

    if (this.fields.variants) {
      this.fields.variants.forEach((variant: VariantTranslation) => {
        const variantTranslation = (detailLangs || []).find(
          (v: VariantSavedTranslation) => {
            return (
              v.foquz_question_detail_id &&
              v.foquz_question_detail_id.toString() === variant.id
            );
          }
        );
        updateQuestionVariantTranslation(variant, variantTranslation);
      });
    }
  }
}
