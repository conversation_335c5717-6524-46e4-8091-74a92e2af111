import {
  DiffRow,
  IPollQuestionDiff,
} from "@/entities/models/poll-question/questionTypes/diff.types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type DiffRowTranslation = {
  id: string;
  startLabel: TextFieldTranslation;
  endLabel: TextFieldTranslation;
};

type DiffRowTranslationResult = [
  TextFieldTranslationResult,
  TextFieldTranslationResult
];

type QuestionDiffTranslationFields = QuestionBaseFieldsTranslation & {
  skipText?: TextFieldTranslation;
  labels: Array<DiffRowTranslation>;
};
type QuestionDiffTranslationResult = QuestionBaseFieldsTranslationResult & {
  skip_text?: TextFieldTranslationResult;
  labels: {
    [key: string]: DiffRowTranslationResult;
  };
};

type QuestionDiffSavedTranslation = QuestionSavedTranslation & {
  labels: Array<string>;
  skip_text?: string;
};

function getDiffRowTranslation(data: DiffRow): DiffRowTranslation {
  const { id, startLabel, endLabel } = data;
  return {
    id,
    startLabel: QuestionTextFieldTranslation(startLabel),
    endLabel: QuestionTextFieldTranslation(endLabel),
  };
}

export class QuestionDiffTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionDiffTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionDiff;

    const { diffConfig, skip, skipText } = question;

    this.fields.labels = diffConfig.map(
      (row: DiffRow): DiffRowTranslation => getDiffRowTranslation(row)
    );

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionDiffTranslationResult {
    const labels: {
      [key: string]: DiffRowTranslationResult;
    } = {};

    this.fields.labels.forEach((row: DiffRowTranslation): void => {
      const { id, startLabel, endLabel } = row;

      labels[id] = [
        QuestionTextFieldResult(startLabel),
        QuestionTextFieldResult(endLabel),
      ];
    });

    const result: QuestionDiffTranslationResult = {
      ...super.getData(),
      labels,
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionDiffSavedTranslation): void {
    super.updateTranslation(data);

    const { labels, skip_text } = data;

    this.fields.labels.forEach((label: DiffRowTranslation) => {
      const { id, startLabel, endLabel } = label;
      const newValue = (labels && labels[id]) || [];
      updateQuestionTextFieldTranslation(startLabel, newValue[0] || "");
      updateQuestionTextFieldTranslation(endLabel, newValue[1] || "");
    });

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
  }
}
