import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  GalleryElementTranslationResult,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  FileSavedTranslation,
  QuestionSavedTranslation,
} from "../types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";

import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import {
  FilialId,
  IPollQuestionFilials,
} from "@/entities/models/poll-question/questionTypes/filials.types";
import { Filial } from "@/entities/structures/filial/types";

type FilialTranslation = {
  id: string;
  original: string;
  value: KnockoutObservable<string>;
};

type FilialTranslationResult = {
  id: string;
  question: string;
};

type FilialSavedTranslation = {
  detail_id: FilialId;
  question: string;
};

type QuestionFilialsTranslationFields = QuestionBaseFieldsTranslation & {
  filials: Array<FilialTranslation>;
  skipText?: TextFieldTranslation;
};

type QuestionFilialsTranslationResult = QuestionBaseFieldsTranslationResult & {
  detail: Array<FilialTranslationResult>;
  gallery?: Array<GalleryElementTranslationResult>;
  skip_text?: TextFieldTranslationResult;
};

type QuestionFilialsSavedTranslation = QuestionSavedTranslation & {
  fileLangs?: Array<FileSavedTranslation>;
  detailLangs?: Array<FilialSavedTranslation>;
  skip_text?: string;
};

const { observable } = ko;

function QuestionFilialTranslation(
  filialId: FilialId,
  filials: Array<Filial>
): FilialTranslation {
  const filial = filials.find((f) => f.id.toString() === filialId);
  return {
    id: filialId,
    original: filial?.name || "",
    value: observable(""),
  };
}

function updateQuestionFilialTranslation(
  filial: FilialTranslation,
  translation: FilialSavedTranslation
) {
  if (!filial) return;

  filial.value(translation?.question || "");
}

export class QuestionFilialsTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionFilialsTranslationFields;
  hasSelfAnswer: boolean = false;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionFilials;

    const filialsList = config.filials;

    const { filials, skip, skipText  } = question;

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }

    this.fields.filials = filials.map((fId: FilialId): FilialTranslation => {
      return QuestionFilialTranslation(fId, filialsList);
    });
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionFilialsTranslationResult {
    const result: QuestionFilialsTranslationResult = {
      ...super.getData(),
      detail: this.fields.filials.map(
        (filial: FilialTranslation): FilialTranslationResult => {
          return {
            id: filial.id,
            question: filial.value(),
          };
        }
      ),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionFilialsSavedTranslation): void {
    super.updateTranslation(data);

    const { detailLangs, skip_text } = data;
    
    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);

    this.fields.filials.forEach((filial: FilialTranslation) => {
      const filialTranslation = (detailLangs || []).find(
        (v: FilialSavedTranslation) => {
          return v.detail_id && v.detail_id.toString() === filial.id;
        }
      );
      updateQuestionFilialTranslation(filial, filialTranslation);
    });
  }
}
