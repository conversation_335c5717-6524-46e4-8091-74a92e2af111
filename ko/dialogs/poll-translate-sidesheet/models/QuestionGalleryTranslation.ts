import { IPollQuestionGallery } from "@/entities/models/poll-question/questionTypes/gallery.types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionGalleryTranslationFields = QuestionBaseFieldsTranslation & {
  skipText?: TextFieldTranslation;
};

type QuestionGalleryTranslationResult = QuestionBaseFieldsTranslationResult & {
  skip_text?: TextFieldTranslationResult;
};
type QuestionGallerySavedTranslation = QuestionSavedTranslation & {
  skip_text: string;
};
export class QuestionGalleryTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionGalleryTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionGallery;

    const { skip, skipText } = question;

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionGalleryTranslationResult {
    const result: QuestionGalleryTranslationResult = {
      ...super.getData(),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionGallerySavedTranslation): void {
    super.updateTranslation(data);

    const { skip_text } = data;

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
  }
}
