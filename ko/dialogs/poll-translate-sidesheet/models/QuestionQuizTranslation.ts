import {
  IPollQuestionQuiz,
  QuizItem,
} from "@/entities/models/poll-question/questionTypes/quiz.types";
import {
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
} from "../types";
import { NameField } from "../../../entities/structures/name-mask/types";
import { QuestionTranslation } from "./QuestionTranslation";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
} from "../types";

type QuizItemTranslation = {
  id: string;
  isNameMask: boolean;
  text?: TextFieldTranslation;
  placeholder?: TextFieldTranslation;
  nameText?: TextFieldTranslation;
  surnameText?: TextFieldTranslation;
  patronymicText?: TextFieldTranslation;
  namePlaceholder?: TextFieldTranslation;
  surnamePlaceholder?: TextFieldTranslation;
  patronymicPlaceholder?: TextFieldTranslation;
};

type QuizItemTranslationResult = {
  id: string;
  name?: TextFieldTranslationResult | NameField<TextFieldTranslationResult>;
  placeholder?:
    | TextFieldTranslationResult
    | NameField<TextFieldTranslationResult>;
};

type QuizItemSavedTranslation = {
  form_field_id: number;
  name: string | NameField<string>;
  placeholder: string | NameField<string>;
};

type QuestionQuizTranslationFields = QuestionBaseFieldsTranslation & {
  quizzes: Array<QuizItemTranslation>;
};
type QuestionQuizTranslationResult = QuestionBaseFieldsTranslationResult & {
  form: Array<QuizItemTranslationResult>;
};

type QuestionQuizSavedTranslation = QuestionSavedTranslation & {
  formLangs: Array<QuizItemSavedTranslation>;
};

function getQuizItemTranslation(data: QuizItem): QuizItemTranslation {
  const { id, label, mask } = data;
  const { isNameMask, placeholder } = mask;

  const result: QuizItemTranslation = {
    id,
    isNameMask: isNameMask,
  };

  if (isNameMask) {
    const { nameMask } = mask;
    const { name, surname, patronymic } = nameMask;

    if (name.visible) {
      result.nameText = QuestionTextFieldTranslation("Имя");
      result.namePlaceholder = QuestionTextFieldTranslation(name.placeholder);
    }
    if (surname.visible) {
      result.surnameText = QuestionTextFieldTranslation("Фамилия");
      result.surnamePlaceholder = QuestionTextFieldTranslation(
        surname.placeholder
      );
    }
    if (patronymic.visible) {
      result.patronymicText = QuestionTextFieldTranslation("Отчество");
      result.patronymicPlaceholder = QuestionTextFieldTranslation(
        patronymic.placeholder
      );
    }
  } else {
    result.text = QuestionTextFieldTranslation(label);
    if (!mask.isDateMonthMask) {
      result.placeholder = QuestionTextFieldTranslation(placeholder);
    }
  }

  return result;
}

function getQuizItemResult(
  data: QuizItemTranslation
): QuizItemTranslationResult {
  const result: QuizItemTranslationResult = {
    id: data.id,
  };

  if ("text" in data) {
    result.name = QuestionTextFieldResult(data.text);
  }

  if (data.isNameMask) {
    result.name = {
      name: QuestionTextFieldResult(data.nameText),
      surname: QuestionTextFieldResult(data.surnameText),
      patronymic: QuestionTextFieldResult(data.patronymicText),
    };
    result.placeholder = {
      name: QuestionTextFieldResult(data.namePlaceholder),
      surname: QuestionTextFieldResult(data.surnamePlaceholder),
      patronymic: QuestionTextFieldResult(data.patronymicPlaceholder),
    };
  } else if ("placeholder" in data) {
    result.placeholder = QuestionTextFieldResult(data.placeholder);
  }

  return result;
}

function updateQuizItemTranslation(
  item: QuizItemTranslation,
  translation: QuizItemSavedTranslation
) {
  if (!item) return;
  const { name, placeholder } = translation || {};

  if (item.isNameMask) {
    const nameValue = (name as NameField<string>) || {
      name: "",
      surname: "",
      patronymic: "",
    };
    const placeholderValue = (placeholder as NameField<string>) || {
      name: "",
      surname: "",
      patronymic: "",
    };
    updateQuestionTextFieldTranslation(item.nameText, nameValue.name);
    updateQuestionTextFieldTranslation(
      item.namePlaceholder,
      placeholderValue.name
    );
    updateQuestionTextFieldTranslation(item.surnameText, nameValue.surname);
    updateQuestionTextFieldTranslation(
      item.surnamePlaceholder,
      placeholderValue.surname
    );
    updateQuestionTextFieldTranslation(
      item.patronymicText,
      nameValue.patronymic
    );
    updateQuestionTextFieldTranslation(
      item.patronymicPlaceholder,
      placeholderValue.patronymic
    );
  } else {
    updateQuestionTextFieldTranslation(item.text, (name as string) || "");
    updateQuestionTextFieldTranslation(
      item.placeholder,
      (placeholder as string) || ""
    );
  }
}

export class QuestionQuizTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionQuizTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionQuiz;
    const { fields } = question;

    this.fields.quizzes = fields.map((quiz: QuizItem): QuizItemTranslation => {
      return getQuizItemTranslation(quiz);
    });
  }

  getData(): QuestionQuizTranslationResult {
    const result: QuestionQuizTranslationResult = {
      ...this.getQuestionBaseFieldsResults(this.fields),
      form: this.fields.quizzes.map(
        (item: QuizItemTranslation): QuizItemTranslationResult => {
          return getQuizItemResult(item);
        }
      ),
    };

    return result;
  }

  updateTranslation(data: QuestionQuizSavedTranslation): void {
    super.updateTranslation(data);

    const { formLangs } = data;
    const fields = (formLangs || []).filter(Boolean);

    this.fields.quizzes.forEach((quiz: QuizItemTranslation) => {
      const newValue = fields.find((el: QuizItemSavedTranslation) => {
        return el.form_field_id.toString() === quiz.id;
      });
      updateQuizItemTranslation(quiz, newValue);
    });
  }
}
