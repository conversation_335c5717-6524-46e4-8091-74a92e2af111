import { IPollQuestionMatrix } from "@/entities/models/poll-question/questionTypes/matrix.types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  ClarifyingQuestionTranslation,
  ClarifyingQuestionTranlationResult,
  ClarifyingQuestionSavedTranslation,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import {
  QuestionClarifyingQuestionTranslation,
  QuestionsClarifyingQuestionResult,
  updateClarifyingQuestionTranslation,
} from "./ClarifyingQuestionTranslation";

type QuestionMatrixTranslationFields = QuestionBaseFieldsTranslation & {
  skipText?: TextFieldTranslation;

  cols: Array<TextFieldTranslation>;
  rows: Array<TextFieldTranslation>;
  clarifyingQuestion?: ClarifyingQuestionTranslation;
  selectPlaceholder?: TextFieldTranslation;
};
type QuestionMatrixTranslationResult = QuestionBaseFieldsTranslationResult & {
  skip_text?: TextFieldTranslationResult;

  labels: {
    cols: Array<TextFieldTranslationResult>;
    rows: Array<TextFieldTranslationResult>;
  };
  select_placeholder_text?: TextFieldTranslationResult;
} & Partial<ClarifyingQuestionTranlationResult>;
type QuestionMatrixSavedTranslation = QuestionSavedTranslation & {
  skip_text?: string;
  labels: string | {
    cols: Array<string>;
    rows: Array<string>;
  };
  select_placeholder_text?: string;
} & ClarifyingQuestionSavedTranslation;

export class QuestionMatrixTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionMatrixTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionMatrix;

    const {
      matrixConfig,
      skip,
      skipText,
      clarifyingQuestion,
      dropdown,
      selectPlaceholder,
    } = question;

    const { cols, rows } = matrixConfig;

    this.fields.cols = cols.map(
      (col: string): TextFieldTranslation => QuestionTextFieldTranslation(col)
    );
    this.fields.rows = rows.map(
      (row: string): TextFieldTranslation => QuestionTextFieldTranslation(row)
    );

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipTextVariants
      );
    }

    if (clarifyingQuestion.enabled) {
      this.fields.clarifyingQuestion =
        QuestionClarifyingQuestionTranslation(clarifyingQuestion);
    }

    if (dropdown) {
      this.fields.selectPlaceholder = QuestionTextFieldTranslation(
        selectPlaceholder,
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionMatrixTranslationResult {
    let result: QuestionMatrixTranslationResult = {
      ...super.getData(),
      labels: {
        cols: this.fields.cols.map(
          (col: TextFieldTranslation): TextFieldTranslationResult =>
            QuestionTextFieldResult(col)
        ),
        rows: this.fields.rows.map(
          (row: TextFieldTranslation): TextFieldTranslationResult =>
            QuestionTextFieldResult(row)
        ),
      },
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    if (this.fields.clarifyingQuestion) {
      result = {
        ...result,
        ...QuestionsClarifyingQuestionResult(this.fields.clarifyingQuestion),
      };
    }

    if (this.fields.selectPlaceholder) {
      result.select_placeholder_text = QuestionTextFieldResult(
        this.fields.selectPlaceholder,
      );
    }

    return result;
  }

  updateTranslation(data: QuestionMatrixSavedTranslation): void {
    super.updateTranslation(data);

    const { labels, skip_text, select_placeholder_text } = data;

    let labelsData: {
      cols: Array<string>;
      rows: Array<string>;
    } = {
      cols: [],
      rows: []
    };
    if (typeof labels === 'string') {
      try {
        labelsData = JSON.parse(labels);
      } catch(e) {

      }
    } else {
      labelsData = labels;
    }

    this.fields.rows.forEach((row: TextFieldTranslation, i: number) => {
      const newValue = (labels && labelsData.rows && labelsData.rows[i]) || "";
      updateQuestionTextFieldTranslation(row, newValue);
    });
    this.fields.cols.forEach((col: TextFieldTranslation, i: number) => {
      const newValue = (labels && labelsData.cols && labelsData.cols[i]) || "";
      updateQuestionTextFieldTranslation(col, newValue);
    });
    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
    updateClarifyingQuestionTranslation(this.fields.clarifyingQuestion, data);
    updateQuestionTextFieldTranslation(
      this.fields.selectPlaceholder,
      select_placeholder_text
    );
  }
}
