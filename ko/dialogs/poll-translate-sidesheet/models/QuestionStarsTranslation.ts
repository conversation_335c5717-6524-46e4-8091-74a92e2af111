import { IPollQuestionStars } from "@/entities/models/poll-question/questionTypes/stars.types";
import {
  QuestionClarifyingQuestionTranslation,
  QuestionsClarifyingQuestionResult,
  updateClarifyingQuestionTranslation,
} from "./ClarifyingQuestionTranslation";

import {
  ITranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  ClarifyingQuestionTranslation,
  ClarifyingQuestionTranlationResult,
  TranslationConfig,
  QuestionSavedTranslation,
  ClarifyingQuestionSavedTranslation,
} from "../types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionStarsTranslationFields = QuestionBaseFieldsTranslation & {
  labels: Array<TextFieldTranslation>;
  skipText?: TextFieldTranslation;
  clarifyingQuestion?: ClarifyingQuestionTranslation;
};
type QuestionStarsTranslationResult = QuestionBaseFieldsTranslationResult & {
  labels: Array<TextFieldTranslationResult>;
  skip_text?: TextFieldTranslationResult;
} & Partial<ClarifyingQuestionTranlationResult>;

type QuestionStarsSavedTranslation = QuestionSavedTranslation & {
  labels: Array<string>;
  skip_text?: string;
} & ClarifyingQuestionSavedTranslation;

export class QuestionStarsTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionStarsTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionStars;

    const { starRating, skip, skipText, clarifyingQuestion } = question;

    this.fields.labels = [];

    if (starRating) {
      const { count, labels } = starRating;

      const labelsArray = Array(count)
        .fill(null)
        .map((_, i: number): TextFieldTranslation => {
          return QuestionTextFieldTranslation(labels[i]);
        });
      this.fields.labels = labelsArray;
    }

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }

    if (clarifyingQuestion.enabled) {
      this.fields.clarifyingQuestion =
        QuestionClarifyingQuestionTranslation(clarifyingQuestion);
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionStarsTranslationResult {
    let result: QuestionStarsTranslationResult = {
      ...super.getData(),

      labels: this.fields.labels.map(
        (label: TextFieldTranslation): TextFieldTranslationResult => {
          return QuestionTextFieldResult(label);
        }
      ),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    if (this.fields.clarifyingQuestion) {
      result = {
        ...result,
        ...QuestionsClarifyingQuestionResult(this.fields.clarifyingQuestion),
      };
    }

    return result;
  }

  updateTranslation(data: QuestionStarsSavedTranslation): void {
    super.updateTranslation(data);

    const { labels, skip_text } = data;

    this.fields.labels.forEach((label: TextFieldTranslation, i: number) => {
      const newValue = (labels && labels[i]) || "";
      updateQuestionTextFieldTranslation(label, newValue);
    });

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
    updateClarifyingQuestionTranslation(this.fields.clarifyingQuestion, data);
  }
}
