import {
  VariantTranslation,
  VariantTranslationResult,
  VariantSavedTranslation,
} from "../types";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";

const { observable } = ko;

export function QuestionVariantTranslation(
  data: QuestionVariant
): VariantTranslation {
  const { id, text, donorId, type, file_id, file_url, preview_url } = data;

  return {
    id,
    donorId,
    value: observable(""),
    description: observable(""),
    original: text,
    type,
    file_id,
    file_url,
    preview_url,
    original_description: data.description
  };
}

export function QuestionVariantResult(
  data: VariantTranslation,
): VariantTranslationResult {
  const { id, value, description} = data;
  return {
    id: id === '-1' ? null : id,
    question: value(),
    description: description()
  };
}

export function updateQuestionVariantTranslation(
  variant: VariantTranslation,
  translation: VariantSavedTranslation
) {
  if (!variant) return;

  variant.value(translation?.question || "");
  variant.description(translation?.description)
}
