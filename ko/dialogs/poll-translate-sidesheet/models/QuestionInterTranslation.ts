import { IPollQuestionInterscreen } from "@/entities/models/poll-question/questionTypes/inter.types";
import { DiscountPool } from "@/entities/models/discount-pool";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  InterblockImageTranslation,
  InterblockImageTranslationResult,
  FileSavedTranslation, QuestionBaseFieldsTranslationResult,
} from "../types";
import { InterblockImage } from "@/entities/models/poll-question/interblockImage/types";
import {
  QuestionInterblockImageTranslation,
  QuestionInterblockImageResult,
  updateQuestionInterblockIamgeTranslation,
} from "./InterblockImageTranslation";
import { QuestionTranslation } from "./QuestionTranslation";
import { uploadLangInterscreenImage } from "@/api/question/files";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionInterTranslationFields = QuestionBaseFieldsTranslation & {
  text: TextFieldTranslation;
  promocode: typeof DiscountPool;
  agreementText?: TextFieldTranslation;
  images?: Array<InterblockImageTranslation>;
};

type InterblockSettings = {
  id: string;
  text: TextFieldTranslationResult;
  agreement_text?: TextFieldTranslationResult;
  pool_id: number;
};

type QuestionInterTranslationResult = {
  intermediateSetting: InterblockSettings;
  gallery?: Array<InterblockImageTranslationResult>;
  name?: string;
};

type QuestionInterSavedTranslation = QuestionSavedTranslation & {
  settingLangs: Array<InterblockSettings>;
  fileLangs: Array<FileSavedTranslation>;
};

const { computed } = ko

export class QuestionInterTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionInterTranslationFields;
  interscreenId: string;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionInterscreen;

    const { interscreenConfig, images } = question;
    const { id, text, agreementButtonEnabled, agreementButtonText, poolId } =
      interscreenConfig;

    this.interscreenId = id;

    const promocode = new DiscountPool();
    promocode.update({
      poolId,
    });

    console.log({ promocode, question, poolId });

    this.fields.text = QuestionTextFieldTranslation(text);
    this.fields.promocode = promocode;

    if (agreementButtonEnabled) {
      this.fields.agreementText = QuestionTextFieldTranslation(
        agreementButtonText || DEFAULT_TEXTS.agreementText
      );
    }

    if (images.length) {
      this.fields.images = images.map(
        (i: InterblockImage): InterblockImageTranslation =>
          QuestionInterblockImageTranslation(i)
      );

      computed(() => {
        const errors = {};
        let hasErrors = false;

        this.fields.images.forEach((item: InterblockImageTranslation) => {
          if (!item.url() && !item.file()) {
            errors[item.id] = "Обязательное поле";
            hasErrors = true;
          }
        });

        this.setError("images", hasErrors ? errors : null);
      });
    }
  }

  getData(): QuestionInterTranslationResult {
    const promocodeData = this.fields.promocode.getData();

    const settings: InterblockSettings = {
      id: this.interscreenId,
      text: QuestionTextFieldResult(this.fields.text),
      pool_id: promocodeData.poolId,
    };

    if (this.fields.agreementText) {
      settings.agreement_text = QuestionTextFieldResult(
        this.fields.agreementText
      );
    }

    const result: QuestionInterTranslationResult = {
      intermediateSetting: settings,
      name: QuestionTextFieldResult(this.fields.name),
    };

    if (this.fields.images) {
      result.gallery = this.fields.images.map(
        (i: InterblockImageTranslation): InterblockImageTranslationResult => {
          return QuestionInterblockImageResult(i);
        }
      );
    }

    return result;
  }

  loadInterscreenFile(galleryElement, file) {
    uploadLangInterscreenImage(this.lang(), galleryElement.id, file);
  }

  updateTranslation({
    settingLangs = [],
    fileLangs,
    name
  }: QuestionInterSavedTranslation): void {
    const { text, agreement_text } = settingLangs[0] || {};
    updateQuestionTextFieldTranslation(this.fields.text, text);
    updateQuestionTextFieldTranslation(
      this.fields.agreementText,
      agreement_text
    );
    updateQuestionTextFieldTranslation(this.fields.name, name);
    this.updateImagesTranslation(fileLangs);
  }

  updateImagesTranslation(translation: Array<FileSavedTranslation> = []) {
    if (!this.fields.images) return;
    this.fields.images.forEach((item: InterblockImageTranslation) => {
      const { id } = item;
      const fileTranslation = translation.find((file: FileSavedTranslation) => {
        return file.end_screen_logo_id.toString() === id;
      });

      updateQuestionInterblockIamgeTranslation(item, fileTranslation);
    });
  }
}
