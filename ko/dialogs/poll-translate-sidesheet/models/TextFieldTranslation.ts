import { TextFieldTranslation, TextFieldTranslationResult } from "../types";

const { observable } = ko;

export function QuestionTextFieldTranslation(
  original: string = "",
  value: string = ""
): TextFieldTranslation {
  return {
    value: observable(value),
    original,
  };
}

export function updateQuestionTextFieldTranslation(translation: TextFieldTranslation, newValue: string) {
  if (!translation) return;
  translation.value(newValue || "");
}

export function QuestionTextFieldResult(
  translation?: TextFieldTranslation
): TextFieldTranslationResult {
  if (!translation) return "";
  return translation.value();
}
