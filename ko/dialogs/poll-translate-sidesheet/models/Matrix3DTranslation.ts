import {
  Matrix3DElementTranslation,
  Matrix3DElementTranslationResult,
  Matrix3DVariantTranslationResult,
  Matrix3DElementSavedTranslation,
} from "../types";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";

const { observable } = ko;

export function QuestionMatrix3DElementTranslation(
  data: QuestionVariant
): Matrix3DElementTranslation {
  const { id, text, donorId, children } = data;
  const obj = {
    id,
    donorId,
    value: observable(""),
    description: observable(""),
    original: text,
    original_description: data.description
  }
  if (children?.length) {
    Object.assign(obj, {
      children: children.map(el => QuestionMatrix3DElementTranslation(el)),
    })
  }

  return obj;
}

export function QuestionMatrix3DElementResult(
  data: Matrix3DElementTranslation,
): Matrix3DElementTranslationResult {
  const { id, value, description} = data;
  return {
    matrix_element_id: parseInt(id),
    name: value(),
    description: description()
  };
}

export function QuestionMatrix3DVariantResult(
  data: Matrix3DElementTranslation,
): Matrix3DVariantTranslationResult {
  const { id, value, description} = data;
  return {
    variant_id: parseInt(id),
    name: value(),
    description: description()
  };
}

export function updateQuestionMatrix3DElementTranslation(
  variant: Matrix3DElementTranslation,
  translation: Matrix3DElementSavedTranslation,
  matrixVariants?: any,
) {
  if (!variant) {
    return;
  }
  variant.value(translation?.name);
  variant.description(translation?.description)
  if (!variant.children || !matrixVariants) {
    return;
  }
  variant.children.forEach(childVariant => {
    const matrixVariant = matrixVariants.find(el => `${el.variant_id}` === `${childVariant.id}`);
    if (!matrixVariant) {
      return;
    }
    childVariant.value(matrixVariant.name);
  })
}
