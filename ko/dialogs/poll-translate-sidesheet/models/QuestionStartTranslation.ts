import { IPollQuestionInterscreen } from "@/entities/models/poll-question/questionTypes/inter.types";
import { DiscountPool } from "@/entities/models/discount-pool";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  InterblockImageTranslation,
  InterblockImageTranslationResult,
  FileSavedTranslation,
} from "../types";
import { InterblockImage } from "@/entities/models/poll-question/interblockImage/types";
import {
  QuestionInterblockImageTranslation,
  QuestionInterblockImageResult,
  updateQuestionInterblockIamgeTranslation,
} from "./InterblockImageTranslation";
import { QuestionTranslation } from "./QuestionTranslation";
import { uploadLangInterscreenImage } from "@/api/question/files";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionStartTranslationFields = QuestionBaseFieldsTranslation & {
  text: TextFieldTranslation;
  agreementText?: TextFieldTranslation;
  pollButtonText?: TextFieldTranslation;
  unsubscribeButtonText?: TextFieldTranslation;
  complaintButtonText?: TextFieldTranslation;
  images?: Array<InterblockImageTranslation>;
  promocode: typeof DiscountPool;
};

type InterblockSettings = {
  id: string;
  text: TextFieldTranslationResult;
  agreement_text?: TextFieldTranslationResult;
  poll_button_text?: TextFieldTranslationResult;
  unsubscribe_button_text?: TextFieldTranslationResult;
  complaint_button_text?: TextFieldTranslationResult;
  pool_id: number;
};

type QuestionStartTranslationResult = {
  intermediateSetting: InterblockSettings;
  gallery?: Array<InterblockImageTranslationResult>;
};

type QuestionStartSavedTranslation = QuestionSavedTranslation & {
  settingLangs: Array<InterblockSettings>;
  fileLangs: Array<FileSavedTranslation>;
};

const { computed } = ko;

export class QuestionStartTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionStartTranslationFields;
  interscreenId: string;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionInterscreen;

    const { interscreenConfig, images } = question;
    const {
      id,
      text,
      pollButtonText,

      agreementButtonEnabled,
      agreementButtonText,
      unsubscribeButtonEnabled,
      unsubscribeButtonText,
      complaintButtonEnabled,
      complaintButtonText,

      poolId,
    } = interscreenConfig;

    this.interscreenId = id;

    const promocode = new DiscountPool();
    promocode.update({
      poolId,
    });

    this.fields.text = QuestionTextFieldTranslation(text);
    this.fields.pollButtonText = QuestionTextFieldTranslation(
      pollButtonText || DEFAULT_TEXTS.pollButtonText
    );
    this.fields.promocode = promocode;

    if (agreementButtonEnabled) {
      this.fields.agreementText = QuestionTextFieldTranslation(
        agreementButtonText || DEFAULT_TEXTS.agreementText
      );
    }

    if (unsubscribeButtonEnabled) {
      this.fields.unsubscribeButtonText = QuestionTextFieldTranslation(
        unsubscribeButtonText || DEFAULT_TEXTS.unsubscribeButtonText
      );
    }

    if (complaintButtonEnabled) {
      this.fields.complaintButtonText = QuestionTextFieldTranslation(
        complaintButtonText || DEFAULT_TEXTS.complaintButtonText
      );
    }

    if (images.length) {
      this.fields.images = images.map(
        (i: InterblockImage): InterblockImageTranslation =>
          QuestionInterblockImageTranslation(i)
      );

      computed(() => {
        const errors = {};
        let hasErrors = false;

        this.fields.images.forEach((item: InterblockImageTranslation) => {
          if (!item.url() && !item.file()) {
            errors[item.id] = "Обязательное поле";
            hasErrors = true;
          }
        });

        this.setError("images", hasErrors ? errors : null);
      });
    }
  }

  getData(): QuestionStartTranslationResult {
    const promocodeData = this.fields.promocode.getData();

    const settings: InterblockSettings = {
      id: this.interscreenId,
      text: QuestionTextFieldResult(this.fields.text),
      poll_button_text: QuestionTextFieldResult(this.fields.pollButtonText),
      pool_id: promocodeData.poolId,
    };

    if (this.fields.agreementText) {
      settings.agreement_text = QuestionTextFieldResult(
        this.fields.agreementText
      );
    }

    if (this.fields.unsubscribeButtonText) {
      settings.unsubscribe_button_text = QuestionTextFieldResult(
        this.fields.unsubscribeButtonText
      );
    }

    if (this.fields.complaintButtonText) {
      settings.complaint_button_text = QuestionTextFieldResult(
        this.fields.complaintButtonText
      );
    }

    const result: QuestionStartTranslationResult = {
      intermediateSetting: settings,
    };

    if (this.fields.images) {
      result.gallery = this.fields.images.map(
        (i: InterblockImageTranslation): InterblockImageTranslationResult => {
          return QuestionInterblockImageResult(i);
        }
      );
    }

    return result;
  }

  loadInterscreenFile(galleryElement, file) {
    uploadLangInterscreenImage(this.lang(), galleryElement.id, file);
  }

  updateTranslation({ settingLangs, fileLangs }: QuestionStartSavedTranslation): void {
    const {
      text,
      poll_button_text,
      complaint_button_text,
      agreement_text,
      unsubscribe_button_text,
      pool_id,
    } = settingLangs && settingLangs[0] || {};

    if (pool_id) {
      this.fields.promocode.update({
        poolId: pool_id,
      });
    }

    updateQuestionTextFieldTranslation(this.fields.text, text);
    updateQuestionTextFieldTranslation(
      this.fields.pollButtonText,
      poll_button_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.agreementText,
      agreement_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.unsubscribeButtonText,
      unsubscribe_button_text
    );
    updateQuestionTextFieldTranslation(
      this.fields.complaintButtonText,
      complaint_button_text
    );
    this.updateImagesTranslation(fileLangs);
  }

  updateImagesTranslation(translation: Array<FileSavedTranslation> = []) {
    if (!this.fields.images) return;
    this.fields.images.forEach((item: InterblockImageTranslation) => {
      const { id } = item;
      const fileTranslation = translation.find((file: FileSavedTranslation) => {
        return file.end_screen_logo_id.toString() === id;
      });

      updateQuestionInterblockIamgeTranslation(item, fileTranslation);
    });
  }
}
