import {
  FileSavedTranslation,
  GalleryElementTranslation,
  GalleryElementTranslationResult,
  QuestionBaseFieldsTranslation,
  QuestionSavedTranslation,
  TranslationConfig,
} from "../types";
import { FullPollQuestion } from "@/entities/models/poll-question/types";
import {
  QuestionTextFieldResult,
  QuestionTextFieldTranslation,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import { getFileType } from "@/utils/file/utils";
import { FileTypes } from "@/constants/file/types";
import { QuestionBaseFieldsTranslationResult } from "../types";
import {
  QuestionGalleryElementResult,
  QuestionGalleryElementTranslation,
  updateQuestionGalleryElementTranslation,
} from "./GalleryElementTranslation";
import {
  uploadLangGalleryImage,
  uploadLangGalleryVideo,
} from "@/api/question/files";
import { GalleryElement } from "@/entities/models/poll-question/questionGallery/types";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

const { observable, computed, toJS } = ko;

export class QuestionTranslation {
  fields: QuestionBaseFieldsTranslation;
  lang: KnockoutObservable<string>;
  question: FullPollQuestion;
  alias: string;
  errors: KnockoutObservable<{
    [key: string]: string;
  }>;

  constructor(config: TranslationConfig) {
    this.lang = config.lang;
    this.question = config.question;
    this.alias = this.question.alias;

    const { galleryEnabled, gallery, commentEnabled, comment } = this.question;

    this.fields = {
      ...this.getQuestionBaseFieldsTranslations(),
    };

    this.errors = observable({});

    if (this.hasGallery) {
      if (galleryEnabled) {
        this.fields.gallery = gallery.map(
          (element: GalleryElement): GalleryElementTranslation => {
            return QuestionGalleryElementTranslation(element);
          }
        );

        computed(() => {
          const errors = {};
          let hasErrors = false;

          this.fields.gallery.forEach((item: GalleryElementTranslation) => {
            if (!item.url() && !item.file()) {
              errors[item.id] = "Обязательное поле";
              hasErrors = true;
            }
          });

          this.setError("gallery", hasErrors ? errors : null);
        });

        console.log("question gallery", ko.toJS(this.fields.gallery));
      }
    }

    if (this.hasComment) {
      if (commentEnabled && comment) {
        this.fields.commentPlaceholder = QuestionTextFieldTranslation(
          comment.placeholder
        );
        this.fields.commentLabel = QuestionTextFieldTranslation(
          comment.label || DEFAULT_TEXTS.commentLabel
        );
      }
    }
  }

  get hasGallery() {
    return false;
  }

  get hasComment() {
    return false;
  }

  getData(): any {
    const result = this.getQuestionBaseFieldsResults(this.fields);

    if (this.hasGallery && this.fields.gallery) {
      result.gallery = this.getGalleryResult(this.fields.gallery);
    }

    if (this.hasComment) {
      if (this.fields.commentPlaceholder) {
        result.placeholder_text = QuestionTextFieldResult(
          this.fields.commentPlaceholder
        );
      }
      if (this.fields.commentLabel) {
        result.comment_label = QuestionTextFieldResult(
          this.fields.commentLabel
        );
      }
    }

    return result;
  }

  getQuestionBaseFieldsTranslations(): QuestionBaseFieldsTranslation {
    const { name, text, description } = this.question;
    return {
      name: QuestionTextFieldTranslation(name),
      text: QuestionTextFieldTranslation(text),
      description: QuestionTextFieldTranslation(description),
    };
  }

  getGalleryResult(
    gallery: Array<GalleryElementTranslation>
  ): Array<GalleryElementTranslationResult> {
    if (!gallery) return [];
    return this.fields.gallery.map(
      (item: GalleryElementTranslation): GalleryElementTranslationResult => {
        return QuestionGalleryElementResult(item);
      }
    );
  }

  getQuestionBaseFieldsResults(
    fields: QuestionBaseFieldsTranslation
  ): QuestionBaseFieldsTranslationResult {
    return {
      name: QuestionTextFieldResult(fields.name),
      description: QuestionTextFieldResult(fields.text),
      sub_description: QuestionTextFieldResult(fields.description),
    };
  }

  updateQuestionBaseFields(
    fields: QuestionBaseFieldsTranslation,
    translation: QuestionSavedTranslation
  ) {
    const { name, description_html, sub_description } = translation;
    updateQuestionTextFieldTranslation(fields.name, name);
    updateQuestionTextFieldTranslation(fields.text, description_html);
    updateQuestionTextFieldTranslation(fields.description, sub_description);
  }

  loadGalleryFile(galleryElement, file) {
    const type = getFileType(file);

    if (type === FileTypes.Image) {
      uploadLangGalleryImage(this.lang(), galleryElement.id, file);
    } else if (type === FileTypes.Video) {
      uploadLangGalleryVideo(this.lang(), galleryElement.id, file);
    }
  }

  updateTranslation(data: QuestionSavedTranslation) {
    this.updateQuestionBaseFields(this.fields, data);
    if (this.hasGallery) {
      const { fileLangs } = data;
      this.updateGalleryTranslation(this.fields.gallery, fileLangs);
    }
    if (this.hasComment) {
      this.updateCommentTranslation(this.fields, data)
    }
  }

  updateCommentTranslation(fields: QuestionBaseFieldsTranslation,
    translation: QuestionSavedTranslation) {
      const { placeholder_text, comment_label } = translation;
      updateQuestionTextFieldTranslation(
        this.fields.commentPlaceholder,
        placeholder_text
      );
      updateQuestionTextFieldTranslation(this.fields.commentLabel, comment_label);
  }

  updateGalleryTranslation(
    gallery: Array<GalleryElementTranslation>,
    translation: Array<FileSavedTranslation> = []
  ) {
    if (!gallery) return;

    gallery.forEach((item: GalleryElementTranslation) => {
      const { id } = item;
      const fileTranslation = translation.find((file: FileSavedTranslation) => {
        return file.foquz_question_file_id.toString() === id;
      });

      updateQuestionGalleryElementTranslation(item, fileTranslation);
    });
  }

  setError(field: string, error: any) {
    this.errors({
      ...this.errors(),
      [field]: error,
    });
  }

  isValid(): boolean {
    if (this.hasGallery && this.fields.gallery) {
      const gallery = toJS(this.fields.gallery);
      return !gallery.some((item) => !item.url && !item.file);
    }
    return true;
  }
}
