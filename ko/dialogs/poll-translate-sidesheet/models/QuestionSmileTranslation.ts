import {
  IPollQuestionSmile,
  Smile,
} from "@/entities/models/poll-question/questionTypes/smile.types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  NpsVariantTranslationResult,
  ClarifyingQuestionTranslation,
  ClarifyingQuestionTranlationResult,
  ClarifyingQuestionSavedTranslation,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import {
  QuestionClarifyingQuestionTranslation,
  QuestionsClarifyingQuestionResult,
  updateClarifyingQuestionTranslation,
} from "./ClarifyingQuestionTranslation";

type SmileTranslation = {
  id: string;
  url: string;
  original: string;
  value: KnockoutObservable<string>;
};

type SmileTranslationResult = string;

type QuestionSmileTranslationFields = QuestionBaseFieldsTranslation & {
  smiles: Array<SmileTranslation>;
  skipText?: TextFieldTranslation;
  clarifyingQuestion?: ClarifyingQuestionTranslation;
};
type QuestionSmileTranslationResult = QuestionBaseFieldsTranslationResult & {
  skip_text?: TextFieldTranslationResult;
  detail: Array<NpsVariantTranslationResult>;
  labels: {
    [key: string]: SmileTranslationResult;
  };
} & Partial<ClarifyingQuestionTranlationResult>;

type QuestionSmileSavedTranslation = QuestionSavedTranslation & {
  skip_text?: string;
  labels: {
    [key: string]: string;
  }
  & ClarifyingQuestionSavedTranslation;
};

const { observable } = ko;

function SmileItem(data: Smile): SmileTranslation {
  const { id, url, text } = data;

  return {
    id,
    url,
    original: text,
    value: observable(""),
  };
}

function updateSmileItem(smile: SmileTranslation, translation: string) {
  if (!smile) return;
  smile.value(translation || "");
}

export class QuestionSmileTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionSmileTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionSmile;

    const { smiles, skip, skipText, clarifyingQuestion } = question;
        
    if (clarifyingQuestion.enabled) {
      this.fields.clarifyingQuestion =
        QuestionClarifyingQuestionTranslation(clarifyingQuestion);
    }

    this.fields.smiles = smiles.map(
      (smile: Smile): SmileTranslation => SmileItem(smile)
    );

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionSmileTranslationResult {
    const labels: {
      [key: string]: SmileTranslationResult;
    } = {};

    this.fields.smiles.forEach((smile: SmileTranslation): void => {
      const { id, value } = smile;
      labels[id] = value();
    });

    const result: QuestionSmileTranslationResult = {
      ...super.getData(),
      labels,
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }
    
    if (this.fields.clarifyingQuestion) {
      const clarifyingQuestion = QuestionsClarifyingQuestionResult(this.fields.clarifyingQuestion)
     
      result.detail_question = clarifyingQuestion.detail_question
      result.placeholder_text = clarifyingQuestion.placeholder_text
      result.self_variant_text = clarifyingQuestion.self_variant_text
      if (clarifyingQuestion.detail) {
        result.detail = [...clarifyingQuestion.detail]
      }
    }

    return result;
  }

  updateTranslation(data: QuestionSmileSavedTranslation): void {
    super.updateTranslation(data);

    const { labels, skip_text } = data;

    this.fields.smiles.forEach((smile: SmileTranslation) => {
      const newValue = (labels && labels[smile.id]) || "";
      updateSmileItem(smile, newValue);
    });

    updateClarifyingQuestionTranslation(this.fields.clarifyingQuestion, data);
    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
  }
}
