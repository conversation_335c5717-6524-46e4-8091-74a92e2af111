import { IPollQuestionStarVariants } from "@/entities/models/poll-question/questionTypes/starVariants.types";
import { QuestionTranslation } from "./QuestionTranslation";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  VariantSavedTranslation,
  VariantTranslation,
  VariantTranslationResult,
  ClarifyingQuestionTranslation,
  ClarifyingQuestionTranlationResult,
  ClarifyingQuestionSavedTranslation,
} from "../types";
import { getAllRecipientVariants, syncVariants } from "@/utils/questions/donor";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import {
  QuestionVariantResult,
  QuestionVariantTranslation,
  updateQuestionVariantTranslation,
} from "./VariantTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import {
  QuestionClarifyingQuestionTranslation,
  QuestionsClarifyingQuestionResult,
  updateClarifyingQuestionTranslation,
} from "./ClarifyingQuestionTranslation";

type QuestionStarVariantsTranslationFields = QuestionBaseFieldsTranslation & {
  variants: Array<VariantTranslation>;
  labels: Array<TextFieldTranslation>;
  skipText?: TextFieldTranslation;
  clarifyingQuestion?: ClarifyingQuestionTranslation;
};
type QuestionStarVariantsTranslationResult =
  QuestionBaseFieldsTranslationResult & {
    detail: Array<VariantTranslationResult>;
    labels: Array<TextFieldTranslationResult>;
    skip_text?: TextFieldTranslationResult;
  } & Partial<ClarifyingQuestionTranlationResult>;
  
type QuestionStarVariantsSavedTranslation = QuestionSavedTranslation & {
  labels: string;
  skip_text?: string;
  detailLangs?: Array<VariantSavedTranslation>;
} & ClarifyingQuestionSavedTranslation;

export class QuestionStarVariantsTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionStarVariantsTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionStarVariants;

    const {
      variants,
      donorId,
      starRating,
      skip,
      skipText,
      clarifyingQuestion,
    } = question;

    let variantsList = question.variants;

    if (donorId) {
      const donorVariants = getAllRecipientVariants(question, config.questions);
      variantsList = syncVariants(donorVariants, variants);
    }

    this.fields.variants = variantsList.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );

    this.fields.labels = [];

    if (starRating) {
      const { count, labels } = starRating;

      const labelsArray = Array(count)
        .fill(null)
        .map((_, i: number): TextFieldTranslation => {
          return QuestionTextFieldTranslation(labels[i]);
        });
      this.fields.labels = labelsArray;
    }

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }

    if (clarifyingQuestion.enabled) {
      this.fields.clarifyingQuestion =
        QuestionClarifyingQuestionTranslation(clarifyingQuestion);
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionStarVariantsTranslationResult {
    let result: QuestionStarVariantsTranslationResult = {
      ...super.getData(),
      detail: this.fields.variants.map(
        (variant: VariantTranslation): VariantTranslationResult => {
          return QuestionVariantResult(variant);
        }
      ),
      labels: this.fields.labels.map(
        (label: TextFieldTranslation): TextFieldTranslationResult => {
          return QuestionTextFieldResult(label);
        }
      ),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    if (this.fields.clarifyingQuestion) {
      const clarifyingQuestion = QuestionsClarifyingQuestionResult(this.fields.clarifyingQuestion)
     
      result.detail_question = clarifyingQuestion.detail_question
      result.placeholder_text = clarifyingQuestion.placeholder_text
      if (clarifyingQuestion.detail) {
        result.detail = [...result.detail, ...clarifyingQuestion.detail]
      }
    }

    return result;
  }

  updateTranslation(data: QuestionStarVariantsSavedTranslation): void {
    super.updateTranslation(data);

    const { detailLangs, labels, skip_text } = data;

    this.fields.variants.forEach((variant: VariantTranslation) => {
      const variantTranslation = (detailLangs || []).find(
        (v: VariantSavedTranslation) => {
          if (variant.id === "-1") return !v.foquz_question_detail_id;

          return (
            v.foquz_question_detail_id &&
            v.foquz_question_detail_id.toString() === variant.id
          );
        }
      );
      updateQuestionVariantTranslation(variant, variantTranslation);
    });

    let _labels = null;
    if (typeof labels === "object") {
      _labels = labels;
    } else if (labels) {
      try {
        _labels = JSON.parse(labels);
      } catch(e) {}
    }

    this.fields.labels.forEach((label: TextFieldTranslation, i: number) => {
      const newValue = (_labels && _labels[i]) || "";
      updateQuestionTextFieldTranslation(label, newValue);
    });

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
    updateClarifyingQuestionTranslation(this.fields.clarifyingQuestion, data);
  }
}
