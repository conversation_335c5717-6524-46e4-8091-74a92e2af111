import { IPollQuestionScale } from "@/entities/models/poll-question/questionTypes/scale.types";
import {
  QuestionTextFieldResult,
  QuestionTextFieldTranslation,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  VariantSavedTranslation,
  VariantTranslation,
  VariantTranslationResult,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import { getAllRecipientVariants, syncVariants } from "@/utils/questions/donor";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import {
  QuestionVariantResult,
  QuestionVariantTranslation,
  updateQuestionVariantTranslation
} from "./VariantTranslation";

type QuestionScaleTranslationFields = QuestionBaseFieldsTranslation & {
  skipText?: TextFieldTranslation;
  variants: Array<VariantTranslation>;
};
type QuestionScaleTranslationResult = QuestionBaseFieldsTranslationResult & {
  skip_text?: TextFieldTranslationResult;
  detail: Array<VariantTranslationResult>;
  labels: [TextFieldTranslationResult, TextFieldTranslationResult, TextFieldTranslationResult];
};

type QuestionScaleSavedTranslation = QuestionSavedTranslation & {
  skip_text?: string;
  labels: string;
  detailLangs?: Array<VariantSavedTranslation>;
};

export class QuestionScaleTranslation
  extends QuestionTranslation
  implements ITranslation {
  fields: QuestionScaleTranslationFields;
  setVariants: boolean;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionScale;

    const { skip, skipText, setVariants, variants, donorId } =
      question;

    this.setVariants = setVariants;

    let variantsList = variants;

    if (setVariants) {
      if (donorId) {
        const donorVariants = getAllRecipientVariants(
          question,
          config.questions
        );
        variantsList = syncVariants(donorVariants, variants);
      }
    }
    console.log(this.fields)
    this.fields.variants = variantsList.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionScaleTranslationResult {
    const result: QuestionScaleTranslationResult = {
      ...super.getData(),
      detail: this.fields.variants.map(
        (variant: VariantTranslation): VariantTranslationResult => {
          return QuestionVariantResult(variant);
        }
      ),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionScaleSavedTranslation): void {
    super.updateTranslation(data);

    const { detailLangs, labels, skip_text } = data;


    this.fields.variants.forEach((variant: VariantTranslation) => {
      const variantTranslation = (detailLangs || []).find(
        (v: VariantSavedTranslation) => {
          if (variant.id === "-1") return !v.foquz_question_detail_id;

          return (
            v.foquz_question_detail_id &&
            v.foquz_question_detail_id.toString() === variant.id
          );
        }
      );
      updateQuestionVariantTranslation(variant, variantTranslation);
    });

    let _labels = [];
    if (labels) {
      try {
        _labels = JSON.parse(labels)
      } catch (e) {

      }
    }
    
    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
  }
}
