import { IPollQuestionDate } from "@/entities/models/poll-question/questionTypes/date.types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TranslationConfig,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";

type QuestionDateTranslationFields = QuestionBaseFieldsTranslation & {};
type QuestionDateTranslationResult = QuestionBaseFieldsTranslationResult & {};

export class QuestionDateTranslation extends QuestionTranslation implements ITranslation {
  fields: QuestionDateTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    this.fields = {
      ...this.getQuestionBaseFieldsTranslations(),
    };
  }

  getData(): QuestionDateTranslationResult {
    const result: QuestionDateTranslationResult = {
      ...this.getQuestionBaseFieldsResults(this.fields),
    };

    return result;
  }

  updateTranslation(data: QuestionSavedTranslation): void {
    this.updateQuestionBaseFields(this.fields, data);
  }
}
