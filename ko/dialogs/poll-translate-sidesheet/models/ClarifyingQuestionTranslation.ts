import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import { ClarifyingQuestion } from "../../../entities/models/poll-question/clarifyingQuestion/types";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import {
  ClarifyingQuestionSavedTranslation,
  VariantSavedTranslation,
} from "../types";
import {
  ClarifyingQuestionTranslation,
  VariantTranslation,
  ClarifyingQuestionTranlationResult,
  VariantTranslationResult,
} from "../types";
import {
  QuestionTextFieldResult,
  QuestionTextFieldTranslation,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  QuestionVariantResult,
  QuestionVariantTranslation,
  updateQuestionVariantTranslation,
} from "./VariantTranslation";

export function QuestionClarifyingQuestionTranslation(
  clarifyingQuestion: ClarifyingQuestion
): ClarifyingQuestionTranslation {
  console.log('QuestionClarifyingQuestionTranslation', clarifyingQuestion)
  const {
    text,
    donorId,
    textAnswerPlaceholder,
    selfAnswerPlaceholder,
    selfAnswerText,
    selfVariantFile
  } = clarifyingQuestion;

  const clarifyingQuestionFields: ClarifyingQuestionTranslation = {
    text: QuestionTextFieldTranslation(text),
  };

  if (donorId) {
    clarifyingQuestionFields.donorId = `${donorId}`;
  }

  if (clarifyingQuestion.isTextAnswer) {
    clarifyingQuestionFields.textAnswerPlaceholder =
      QuestionTextFieldTranslation(textAnswerPlaceholder);
  } else {
    clarifyingQuestionFields.variants = clarifyingQuestion.variants.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );
    if (clarifyingQuestion.selfAnswer) {
      clarifyingQuestionFields.selfAnswerText =
        QuestionTextFieldTranslation(selfAnswerText || DEFAULT_TEXTS.selfAnswerText);
      clarifyingQuestionFields.selfAnswerPlaceholder =
        QuestionTextFieldTranslation(selfAnswerPlaceholder);
      clarifyingQuestionFields.selfVariantFile = selfVariantFile
    }
  }

  return clarifyingQuestionFields;
}

export function QuestionsClarifyingQuestionResult(
  translation: ClarifyingQuestionTranslation
): ClarifyingQuestionTranlationResult {
  const result: ClarifyingQuestionTranlationResult = {};
  result.detail_question = QuestionTextFieldResult(translation.text);
  if (translation.textAnswerPlaceholder) {
    result.placeholder_text = QuestionTextFieldResult(
      translation.textAnswerPlaceholder
    );
  } else {
    result.detail = translation.variants.map(
      (variant: VariantTranslation): VariantTranslationResult => {
        return QuestionVariantResult(variant);
      }
    );
    if (translation.selfAnswerText) {
      result.self_variant_text = QuestionTextFieldResult(
        translation.selfAnswerText
      );
      result.placeholder_text = QuestionTextFieldResult(
        translation.selfAnswerPlaceholder
      );
    }
  }
  return result;
}

export function updateClarifyingQuestionTranslation(
  clarifyingQuestion: ClarifyingQuestionTranslation,
  translation: ClarifyingQuestionSavedTranslation,
  clarifyingToEach: boolean = false
) {
  if (!clarifyingQuestion) return;

  let currentTranslation: ClarifyingQuestionSavedTranslation;
  const detailLangs = translation.detailLangs;

  if (clarifyingToEach) {
    currentTranslation = (detailLangs || []).find(
      (v: VariantSavedTranslation) => {
        return v.foquz_question_detail_id.toString() === clarifyingQuestion.donorId;
      }
    ) || {};
  } else {
    currentTranslation = translation;
  }

  console.log('updateTranslation', clarifyingQuestion)
  console.log('updateTranslation', currentTranslation)

  const {
    detail_question,
    self_variant_text,
    self_variant_placeholder_text,
    placeholder_text
  } = currentTranslation;

  updateQuestionTextFieldTranslation(clarifyingQuestion.text, detail_question);

  if (clarifyingQuestion.selfAnswerText) {
    updateQuestionTextFieldTranslation(
      clarifyingQuestion.selfAnswerText,
      self_variant_text
    );
    updateQuestionTextFieldTranslation(
      clarifyingQuestion.selfAnswerPlaceholder,
      self_variant_placeholder_text || placeholder_text
    );
  } else if (clarifyingQuestion.textAnswerPlaceholder) {
    updateQuestionTextFieldTranslation(
      clarifyingQuestion.textAnswerPlaceholder,
      self_variant_placeholder_text || placeholder_text
    );
  }

  if (clarifyingQuestion.variants) {
    clarifyingQuestion.variants.forEach((variant: VariantTranslation) => {
      const variantTranslation = (detailLangs || []).find(
        (v: VariantSavedTranslation) => {
          return `${v.foquz_question_detail_id}` === variant.id;
        }
      );
      updateQuestionVariantTranslation(variant, variantTranslation);
    });
  }
}
