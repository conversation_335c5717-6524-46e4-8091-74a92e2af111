import {QuestionTypes} from "@/constants/question/questionTypes";
import {Constructor} from "@/types";

import {ITranslation, TranslationConfig} from "../types";

import {QuestionEndTranslation} from "./QuestionEndTranslation";
import {QuestionInterTranslation} from "./QuestionInterTranslation";
import {QuestionInterFiveSecondTestTranslation} from "./QuestionInterFiveSecondTestTranslation";
import {QuestionStartTranslation} from "./QuestionStartTranslation";
import {QuestionStarsTranslation} from "./QuestionStarsTranslation";
import {QuestionRatingTranslation} from "./QuestionRatingTranslation";
import {QuestionVariantsTranslation} from "./QuestionVariantsTranslation";
import {QuestionTextTranslation} from "./QuestionTextTranslation";
import {QuestionDateTranslation} from "./QuestionDateTranslation";
import {QuestionAddressTranslation} from "./QuestionAddressTranslation";
import {QuestionFileTranslation} from "./QuestionFileTranslation";
import {QuestionQuizTranslation} from "./QuestionQuizTranslation";
import {QuestionStarVariantsTranslation} from "./QuestionStarVariantsTranslation";
import {QuestionPriorityTranslation} from "./QuestionPriorityTranslation";
import {QuestionMediaVariantsTranslation} from "./QuestionMediaVariantsTranslation";
import {QuestionGalleryTranslation} from "./QuestionGalleryTranslation";
import {QuestionSmileTranslation} from "./QuestionSmileTranslation";
import {QuestionNpsTranslation} from "./QuestionNpsTranslation";
import {QuestionMatrixTranslation} from "./QuestionMatrixTranslation";
import {QuestionMatrix3DTranslation} from "./QuestionMatrix3DTranslation";
import {QuestionRateTranslation} from "./QuestionRateTranslation";
import {QuestionDiffTranslation} from './QuestionDiffTranslation';
import {QuestionFilialsTranslation} from "./QuestionFilialsTranslation";
import {QuestionClassifierTranslation} from "./QuestionClassifierTranslation";
import {QuestionScaleTranslation} from "./QuestionScaleTranslation";
import {QuestionDistributionScaleTranslation} from "./QuestionDistributionScaleTranslation";
import {QuestionCardSortingTranslation} from "./QuestionCardSortingTranslation";
import {QuestionFirstClickTestTranslation} from "./QuestionFirstClickTestTranslation";

type QuestionConstructor = Constructor<ITranslation>;

const models: {
  [key: string]: QuestionConstructor;
} = {
  [QuestionTypes.Stars]: QuestionStarsTranslation,
  [QuestionTypes.Variants]: QuestionVariantsTranslation,
  [QuestionTypes.Text]: QuestionTextTranslation,
  [QuestionTypes.Date]: QuestionDateTranslation,
  [QuestionTypes.Address]: QuestionAddressTranslation,
  [QuestionTypes.File]: QuestionFileTranslation,
  [QuestionTypes.Quiz]: QuestionQuizTranslation,
  [QuestionTypes.Priority]: QuestionPriorityTranslation,
  [QuestionTypes.MediaVariants]: QuestionMediaVariantsTranslation,
  [QuestionTypes.Gallery]: QuestionGalleryTranslation,
  [QuestionTypes.Smile]: QuestionSmileTranslation,
  [QuestionTypes.Nps]: QuestionNpsTranslation,
  [QuestionTypes.Matrix]: QuestionMatrixTranslation,
  [QuestionTypes.Matrix3D]: QuestionMatrix3DTranslation,
  [QuestionTypes.Diff]: QuestionDiffTranslation,
  [QuestionTypes.StarVariants]: QuestionStarVariantsTranslation,
  [QuestionTypes.Rating]: QuestionRatingTranslation,
  [QuestionTypes.Rate]: QuestionRateTranslation,
  [QuestionTypes.Filials]: QuestionFilialsTranslation,
  [QuestionTypes.Classifier]: QuestionClassifierTranslation,
  [QuestionTypes.Scale]: QuestionScaleTranslation,
  [QuestionTypes.DistributionScale]: QuestionDistributionScaleTranslation,
  [QuestionTypes.CardSorting]: QuestionCardSortingTranslation,
  [QuestionTypes.FirstClickTest]: QuestionFirstClickTestTranslation,
};

export function QuestionTranslationFactory(config: TranslationConfig): ITranslation {
  const {question} = config;
  if (question.type === QuestionTypes.Inter) {
    if (question.isInterStart) {
      return new QuestionStartTranslation(config);
    } else if (question.isInterEnd) {
      return new QuestionEndTranslation(config);
    } else if (question.isInterFiveSecondTest) {
      return new QuestionInterFiveSecondTestTranslation(config);
    }
    return new QuestionInterTranslation(config);
  }

  const model: QuestionConstructor = models[question.type];

  if (!model) return null;

  return new model(config);
}
