import { IPollQuestionCardSorting } from "@/entities/models/poll-question/questionTypes/card-sorting.types";
import { GalleryElement } from "@/entities/models/poll-question/questionGallery/types";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import { FullPollQuestion } from "@/entities/models/poll-question/types";
import {
  QuestionGalleryElementTranslation,
  QuestionGalleryElementResult,
} from "./GalleryElementTranslation";
import {
  ITranslation,
  VariantTranslation,
  GalleryElementTranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  VariantTranslationResult,
  GalleryElementTranslationResult,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  FileSavedTranslation,
  QuestionSavedTranslation,
  VariantSavedTranslation,
} from "../types";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  QuestionVariantTranslation,
  QuestionVariantResult,
  updateQuestionVariantTranslation,
} from "./VariantTranslation";

import { getAllRecipientVariants } from "@/utils/questions/donor";
import { syncVariants } from "../../../utils/questions/donor";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";

type QuestionCardSortingTranslationFields = QuestionBaseFieldsTranslation & {
  variants: Array<VariantTranslation>;
  categoryList: Array<VariantTranslation>;
  skipText?: TextFieldTranslation;
  cardColumnText: TextFieldTranslation;
  categoryColumnText: TextFieldTranslation;
  commentLabel?: TextFieldTranslation;
  commentPlaceholder?: TextFieldTranslation;
};

type QuestionCardSortingTranslationResult = QuestionBaseFieldsTranslationResult & {
  detail: Array<VariantTranslationResult>;
  cardSortingCategory: Array<VariantTranslationResult>;
  skip_text?: TextFieldTranslationResult;
  cardColumnText: TextFieldTranslationResult;
  categoryColumnText: TextFieldTranslationResult;
  gallery?: Array<GalleryElementTranslationResult>;
};

type QuestionCardSortingSavedTranslation = QuestionSavedTranslation & {
  fileLangs?: Array<FileSavedTranslation>;
  detailLangs?: Array<VariantSavedTranslation>;
  cardSortingCategorylangs?: Array<CardSortingCategoryTranslationResult>;
  skip_text?: string;
  card_column_text?: string;
  category_column_text?: string;
};

type CardSortingCategoryTranslationResult = {
  id: string,
  category_id: number,
  name: string,
}

export class QuestionCardSortingTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionCardSortingTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionCardSorting;

    const {
      categoryList,
      cardColumnText,
      categoryColumnText,
      variants,
      skip,
      skipText,
      commentEnabled,
      comment,
    } = question;

    let variantsList = variants.filter((v) => !v.deleted);

    this.fields.variants = variantsList.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );

    this.fields.categoryList = categoryList.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );

    this.fields.cardColumnText = QuestionTextFieldTranslation(cardColumnText)
    this.fields.categoryColumnText = QuestionTextFieldTranslation(categoryColumnText)

    if (commentEnabled) {
      this.fields.commentLabel = QuestionTextFieldTranslation(
        comment.label || DEFAULT_TEXTS.commentLabel
      );
      this.fields.commentPlaceholder = QuestionTextFieldTranslation(
        comment.placeholder
      );
    }

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipTextVariants
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }
  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionCardSortingTranslationResult {
    const result: QuestionCardSortingTranslationResult = {
      ...super.getData(),
      card_column_text: QuestionTextFieldResult(this.fields.cardColumnText),
      category_column_text: QuestionTextFieldResult(this.fields.categoryColumnText),
      detail: this.fields.variants.map(
        (variant: VariantTranslation): VariantTranslationResult => {
          return QuestionVariantResult(variant);
        }
      ),
      cardSortingCategory: this.fields.categoryList.map(
        (variant: VariantTranslation): CardSortingCategoryTranslationResult => {
          return {
            id: variant.id,
            category_id: Number(variant.id),
            name: variant.value(),
          };
        }
      ),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionCardSortingSavedTranslation): void {
    super.updateTranslation(data);

    const {
      detailLangs,
      cardSortingCategorylangs,
      skip_text,
      placeholder_text,
      comment_label,
      category_column_text,
      card_column_text,
    } = data;

    updateQuestionTextFieldTranslation(
      this.fields.commentLabel,
      comment_label,
    );
    updateQuestionTextFieldTranslation(
      this.fields.commentPlaceholder,
      placeholder_text,
    );

    updateQuestionTextFieldTranslation(
      this.fields.categoryColumnText,
      category_column_text,
    );
    updateQuestionTextFieldTranslation(
      this.fields.cardColumnText,
      card_column_text,
    );

    updateQuestionTextFieldTranslation(
      this.fields.skipText,
      skip_text,
    );

    this.fields.variants.forEach((variant: VariantTranslation) => {
      const variantTranslation = (detailLangs || []).find(
        (v: VariantSavedTranslation) => {
          if (variant.id === "-1") return !v.foquz_question_detail_id;

          return (
            v.foquz_question_detail_id &&
            v.foquz_question_detail_id.toString() === variant.id
          );
        }
      );
      updateQuestionVariantTranslation(variant, variantTranslation);
    });

    this.fields.categoryList.forEach((variant: VariantTranslation) => {
      const variantTranslation = (cardSortingCategorylangs || []).find(
        (v: CardSortingCategoryTranslationResult) => {
          return (
            v.category_id &&
            String(v.category_id) === String(variant.id)
          );
        }
      );
      updateQuestionVariantTranslation(variant, { question: variantTranslation?.name } as VariantSavedTranslation);
    });
  }
}
