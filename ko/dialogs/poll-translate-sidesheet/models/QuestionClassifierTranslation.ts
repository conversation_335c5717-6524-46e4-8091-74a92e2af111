import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  GalleryElementTranslationResult,
  TranslationConfig,
  FileSavedTranslation,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
} from "../types";

import { QuestionTranslation } from "./QuestionTranslation";
import { FilialId } from "@/entities/models/poll-question/questionTypes/filials.types";
import { IPollQuestionClassifier } from "../../../entities/models/poll-question/questionTypes/classifier.types";
import { getCollection } from "@/api/collections/collection";
import { FvmTree } from "@/presentation/viewModels/fvm-tree";
import { formatCollection } from "@/utils/collections/format-collection";
import { filterCollectionTree } from "@/utils/collections/filter-collection-tree";
import { sortCollectionTree } from "@/utils/collections/sort-collection-tree";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import {
  QuestionTextFieldResult,
  QuestionTextFieldTranslation,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";

type CollectionItemTranslationResult = {
  id: string;
  question: string;
};

type CollectionItemSavedTranslation = {
  detail_id: string;
  question: string;
  description: string;
};

type QuestionClassifierTranslationFields = QuestionBaseFieldsTranslation & {
  items: {
    [key: string]: KnockoutObservable<string>;
  };
  descriptions: {
    [key: string]: KnockoutObservable<string>;
  };
  skipText?: TextFieldTranslation;
};

type QuestionClassifierTranslationResult =
  QuestionBaseFieldsTranslationResult & {
    detail: Array<CollectionItemTranslationResult>;
    category: Array<CollectionItemTranslationResult>;
    gallery?: Array<GalleryElementTranslationResult>;
    skip_text?: TextFieldTranslationResult;
  };

type QuestionClassifierSavedTranslation = QuestionSavedTranslation & {
  fileLangs?: Array<FileSavedTranslation>;
  detailLangs?: Array<CollectionItemSavedTranslation>;
  skip_text?: string;
};

const { observable } = ko;

export class QuestionClassifierTranslation
  extends QuestionTranslation
  implements ITranslation
{
  loading: KnockoutObservable<boolean>;
  tree: FvmTree;
  fields: QuestionClassifierTranslationFields;

  _savedData: CollectionItemSavedTranslation[];

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionClassifier;



    const { dictionaryId, dictionaryItems, skip, skipText } = question;

    this.loading = ko.observable(true);
    this.tree = null;

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipText
      );
    }

    this.fields.items = {};
    this.fields.descriptions = {};

    this._savedData = null;

    getCollection(dictionaryId).then((list) => {
      const tree = formatCollection(list.elements);
      const filteredTree = filterCollectionTree(tree, dictionaryItems);
      this.tree = new FvmTree(filteredTree);

      this.tree.unwrap();
      
      setTimeout(() => {
        Object.keys(this.tree.structure).forEach((id) => {
          let translation = "";
          let translation_d = "";
          if (this._savedData) {
           
            let item = this._savedData.find((v) => {
              return v.detail_id && v.detail_id.toString() === id;
            });
            console.log(item)
            translation = item?.question || "";
            translation_d = item?.description || "";
          }
  
          this.fields.items[id] = observable(translation || "");
          this.fields.descriptions[id] = observable(translation_d || "");
          console.log(this.fields.descriptions[id]())
        });
  
        this._savedData = null;
  
        this.loading(false);
      }, 10);

      
    });
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionClassifierTranslationResult {
    const elements = Object.entries(this.fields.items).filter(
      ([id]) => !this.tree.structure[id].category
    );
    const descriptions = Object.entries(this.fields.descriptions)
    const categories = Object.entries(this.fields.items).filter(
      ([id]) => this.tree.structure[id].category
    );

    const result: QuestionClassifierTranslationResult = {
      ...super.getData(),
      detail: elements.map(([id, value], index) => {

        const descr = descriptions.find(i => i[0] == id)
        console.log(ko.toJS(descr))
        return {
          id,
          question: value(),
          description: descr[1]()
        };
      }),
      category: categories.map(([id, value]) => {
        const descr = descriptions.find(i => i[0] == id)
        return {
          id,
          question: value(),
          description: descr[1]()
        };
      }),
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionClassifierSavedTranslation): void {
    super.updateTranslation(data);

    const { detailLangs, skip_text } = data;

    if (this.loading()) {
      this._savedData = detailLangs;
      return;
    }

    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
   
    Object.entries(this.fields.items).forEach(([id, value]) => {
      const translation = (detailLangs || []).find(
        (v: CollectionItemSavedTranslation) => {
          return v.detail_id && v.detail_id.toString() === id;
        }
      );
      const descr = this.fields.descriptions[id]
      if (translation) {
        value(translation.question);
        descr(translation.description)
      } else {
        value("");
        descr("")
      }
    });
  }
}
