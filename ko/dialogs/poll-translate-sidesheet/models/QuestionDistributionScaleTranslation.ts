import { IPollQuestionDistributionScale } from "@/entities/models/poll-question/questionTypes/distributionScale.types";
import {
  QuestionTextFieldResult,
  QuestionTextFieldTranslation,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
  VariantSavedTranslation,
  VariantTranslation,
  VariantTranslationResult,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "../constants/defaultTexts";
import { QuestionVariant } from "@/entities/models/poll-question/questionVariant/types";
import {
  QuestionVariantResult,
  QuestionVariantTranslation,
  updateQuestionVariantTranslation
} from "./VariantTranslation";

type QuestionDistributionScaleTranslationFields = QuestionBaseFieldsTranslation & {
  skipText?: TextFieldTranslation;
  indicatorText?: TextFieldTranslation;
  variants: Array<VariantTranslation>;
};
type QuestionDistributionScaleTranslationResult = QuestionBaseFieldsTranslationResult & {
  skip_text?: TextFieldTranslationResult;
  self_variant_text?: string;
  detail: Array<VariantTranslationResult>;
  labels: [TextFieldTranslationResult, TextFieldTranslationResult, TextFieldTranslationResult];
};

type QuestionDistributionScaleSavedTranslation = QuestionSavedTranslation & {
  skip_text?: string;
  self_variant_text?: string;
  labels: string;
  detailLangs?: Array<VariantSavedTranslation>;
};

export class QuestionDistributionScaleTranslation
  extends QuestionTranslation
  implements ITranslation {
  fields: QuestionDistributionScaleTranslationFields;
  setVariants: boolean;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionDistributionScale;

    const { skip, skipText, variants, indicatorText } =
      question;
    
    let variantsList = variants.filter((v) => !v.deleted);

    this.fields.variants = variantsList.map(
      (variant: QuestionVariant): VariantTranslation => {
        return QuestionVariantTranslation(variant);
      }
    );

    this.fields.indicatorText = QuestionTextFieldTranslation(
      indicatorText || DEFAULT_TEXTS.indicatorText
    );

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipTextVariants
      );
    }
  }

  get hasGallery(): boolean {
    return true;
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionDistributionScaleTranslationResult {
    const result: QuestionDistributionScaleTranslationResult = {
      ...super.getData(),
      detail: this.fields.variants.map(
        (variant: VariantTranslation): VariantTranslationResult => {
          return QuestionVariantResult(variant);
        }
      ),
    };

    result.self_variant_text = QuestionTextFieldResult(this.fields.indicatorText);

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionDistributionScaleSavedTranslation): void {
    super.updateTranslation(data);

    const { detailLangs, labels, skip_text, self_variant_text } = data;

    updateQuestionTextFieldTranslation(this.fields.indicatorText, self_variant_text);

    this.fields.variants.forEach((variant: VariantTranslation) => {
      const variantTranslation = (detailLangs || []).find(
        (v: VariantSavedTranslation) => {
          if (variant.id === "-1") return !v.foquz_question_detail_id;

          return (
            v.foquz_question_detail_id &&
            v.foquz_question_detail_id.toString() === variant.id
          );
        }
      );
      updateQuestionVariantTranslation(variant, variantTranslation);
    });

    let _labels = [];
    if (labels) {
      try {
        _labels = JSON.parse(labels)
      } catch (e) {

      }
    }
    
    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
  }
}
