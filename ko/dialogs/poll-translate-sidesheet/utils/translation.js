export function formatTranslation(data) {
  const { questions = [], back_text, next_text, finish_text, unrequired_text } = data;

  const commonTranslation = {
    back_text,
    next_text,
    finish_text,
    unrequired_text,
  };
  const result = {
    common: commonTranslation,
  };

  questions.forEach((q) => {
    const { foquz_question_id } = q;
    result[foquz_question_id] = q;
    if (typeof q.labels === 'string') {
      result[foquz_question_id].labels = JSON.parse(q.labels)
    }
  });
  console.log('formatTranslation(result)', result);

  return result;
}
