import { QuestionTypes } from "@/constants/question/questionTypes";
import { FullPollQuestion } from "@/entities/models/poll-question/types";

import { COMMON_ELEMENTS } from "../constants";

import * as CommonTranslationView from "../components/common/index";
import * as InterStartTranslationView from "../components/interStart";
import * as InterEndTranslationView from "../components/interEnd";
import * as InterTextTranslationView from "../components/interText";
import * as StarsTranslationView from "../components/stars";
import * as VariantsTranslationView from "../components/variants";
import * as TextTranslationView from "../components/text";
import * as DateTranslationView from "../components/date";
import * as AddressTranslationView from "../components/address";
import * as FileTranslationView from "../components/file";
import * as QuizTranslationView from "../components/quiz";
import * as PriorityTranslationView from "../components/priority";
import * as MediaVariantsTranslationView from "../components/mediaVariants";
import * as GalleryTranslationView from "../components/gallery";
import * as SmileTranslationView from "../components/smile";
import * as NpsTranslationView from "../components/nps";
import * as MatrixTranslationView from "../components/matrix";
import * as Matrix3DTranslationView from "../components/matrix-3d";
import * as StarVariantsTranslationView from "../components/starVariants";
import * as DiffTranslationView from "../components/diff";
import * as RateTranslationView from "../components/rate";
import * as RatingTranslationView from "../components/rating";
import * as FilialsTranslationView from "../components/filials";
import * as ClassifierTranslationView from "../components/classifier";
import * as ScaleTranslationView from "../components/scale";
import * as DistributionScaleTranslationView from "../components/distribution-scale";
import * as CardSortingTranslationView from "../components/card-sorting";
import * as FirstClickTestTranslationView from "../components/first-click-test";

import * as QuestionGallery from "../components/questionGallery";
import * as QuestionBaseFields from "../components/questionBaseFields";
import * as OriginalText from "../components/originalText";
import * as TranslatedField from "../components/translatedField";
import * as TranslatedMaskedField from "../components/translatedMaskedField";
import * as ClarifyingQuestion from "../components/clarifyingQuestion";
import * as StarRatingLabels from "../components/starRatingLabels";

import { registerComponents } from "@/utils/engine/register-component";

const components: {
  [key: string]: string;
} = {
  Common: "fc-translate-common",
  Start: "fc-translate-start",
  End: "fc-translate-end",
  Inter: "fc-translate-inter",
  [QuestionTypes.Stars]: "fc-translate-stars",
  [QuestionTypes.Variants]: "fc-translate-variants",
  [QuestionTypes.Text]: "fc-translate-text",
  [QuestionTypes.Date]: "fc-translate-date",
  [QuestionTypes.Address]: "fc-translate-address",
  [QuestionTypes.File]: "fc-translate-file",
  [QuestionTypes.Quiz]: "fc-translate-quiz",
  [QuestionTypes.Priority]: "fc-translate-priority",
  [QuestionTypes.MediaVariants]: "fc-translate-media-variants",
  [QuestionTypes.Gallery]: "fc-translate-gallery",
  [QuestionTypes.Smile]: "fc-translate-smile",
  [QuestionTypes.Nps]: "fc-translate-nps",
  [QuestionTypes.Matrix]: "fc-translate-matrix",
  [QuestionTypes.Matrix3D]: "fc-translate-matrix-3d",
  [QuestionTypes.StarVariants]: "fc-translate-star-variants",
  [QuestionTypes.Diff]: "fc-translate-diff",
  [QuestionTypes.Rate]: "fc-translate-rate",
  [QuestionTypes.Rating]: "fc-translate-rating",
  [QuestionTypes.Filials]: "fc-translate-filials",
  [QuestionTypes.Classifier]: "fc-translate-classifier",
  [QuestionTypes.Scale]: "fc-translate-scale",
  [QuestionTypes.DistributionScale]: "fc-translate-distribution-scale",
  [QuestionTypes.CardSorting]: "fc-translate-card-sorting",
  [QuestionTypes.FirstClickTest]: "fc-translate-first-click-test",
};

registerComponents({
  [components.Common]: CommonTranslationView,
  [components.Start]: InterStartTranslationView,
  [components.End]: InterEndTranslationView,
  [components.Inter]: InterTextTranslationView,
  [components[QuestionTypes.Stars]]: StarsTranslationView,
  [components[QuestionTypes.Variants]]: VariantsTranslationView,
  [components[QuestionTypes.Text]]: TextTranslationView,
  [components[QuestionTypes.Date]]: DateTranslationView,
  [components[QuestionTypes.Address]]: AddressTranslationView,
  [components[QuestionTypes.File]]: FileTranslationView,
  [components[QuestionTypes.Quiz]]: QuizTranslationView,
  [components[QuestionTypes.Priority]]: PriorityTranslationView,
  [components[QuestionTypes.MediaVariants]]: MediaVariantsTranslationView,
  [components[QuestionTypes.Gallery]]: GalleryTranslationView,
  [components[QuestionTypes.Smile]]: SmileTranslationView,
  [components[QuestionTypes.Nps]]: NpsTranslationView,
  [components[QuestionTypes.Matrix]]: MatrixTranslationView,
  [components[QuestionTypes.Matrix3D]]: Matrix3DTranslationView,
  [components[QuestionTypes.StarVariants]]: StarVariantsTranslationView,
  [components[QuestionTypes.Diff]]: DiffTranslationView,
  [components[QuestionTypes.Rate]]: RateTranslationView,
  [components[QuestionTypes.Rating]]: RatingTranslationView,
  [components[QuestionTypes.Filials]]: FilialsTranslationView,
  [components[QuestionTypes.Classifier]]: ClassifierTranslationView,
  [components[QuestionTypes.Scale]]: ScaleTranslationView,
  [components[QuestionTypes.DistributionScale]]:
    DistributionScaleTranslationView,
  [components[QuestionTypes.CardSorting]]: CardSortingTranslationView,
  [components[QuestionTypes.FirstClickTest]]: FirstClickTestTranslationView,

  "fc-gallery": QuestionGallery,
  "fc-base-fields": QuestionBaseFields,
  "fc-original-text": OriginalText,
  "fc-translated-field": TranslatedField,
  "fc-translated-masked-field": TranslatedMaskedField,
  "fc-clarifying-question": ClarifyingQuestion,
  "fc-star-rating-labels": StarRatingLabels,
});

export function getComponent(activeElement: {
  id: string;
  question?: FullPollQuestion;
}): string {
  if (activeElement.id === COMMON_ELEMENTS) return components.Common;
  const question = activeElement.question;

  if (question.type === QuestionTypes.Inter) {
    if (question.isInterStart) return components.Start;
    if (question.isInterEnd) return components.End;
    if (question.isInterFiveSecondTest) return components.Inter;
    return components.Inter;
  }

  const componentName = components[question.type];

  return componentName || null;
}
