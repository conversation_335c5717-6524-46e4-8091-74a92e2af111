<div class="star-rating-labels__wrapper">
  <!-- ko foreach: { data: labels, as: 'label' } -->
  <div class="star-rating-label">
    <fc-input params="value: label.value, maxlength: 150, disabled: $parent.disabled"></fc-input>
    <!-- ko ifnot: $parent.hideHints -->
    <div class="mt-2" data-bind="text: $parent.getStarLabel($index() + 1)"></div>
    <!-- /ko -->
    <fc-original-text class="mt-15p" params="text: label.original"></fc-original-text>
  </div>
  <!-- /ko -->
</div>
