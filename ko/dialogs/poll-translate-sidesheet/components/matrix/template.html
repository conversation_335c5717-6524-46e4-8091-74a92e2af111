<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<fc-base-fields params="fields: fields, disabled: disabled"></fc-base-fields>

<div class="form-group matrix-labels">
  <fc-label params="text: 'Метки столбцов', hint: 'Метки столбцов'"></fc-label>

  <div class="matrix-labels__wrapper">
    <!-- ko foreach: { data: fields.cols, as: 'col' } -->
    <div class="matrix-label">
      <fc-translated-field
        params="field: col, maxlength: 255, counter: true, disabled: $parent.disabled"
      ></fc-translated-field>
    </div>
    <!-- /ko -->
  </div>
</div>

<div class="form-group matrix-labels">
  <fc-label params="text: 'Метки рядов', hint: 'Метки рядов'"></fc-label>

  <div class="matrix-labels__wrapper">
    <!-- ko foreach: { data: fields.rows, as: 'row' } -->
    <div class="matrix-label">
      <fc-translated-field
        params="field: row, maxlength: 255, counter: true, disabled: $parent.disabled"
      ></fc-translated-field>
    </div>
    <!-- /ko -->
  </div>
</div>

<!-- ko if: fields.selectPlaceholder -->
<fc-translated-field
  class="form-group"
  params="field: fields.selectPlaceholder, text: 'Подсказка для выпадающего списка', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
<!-- /ko -->

<!-- ko if: fields.skipText -->
<fc-translated-field
  class="form-group"
  params="field: fields.skipText, text: 'Текст (пропуск оценки)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
<!-- /ko -->

<!-- ko if: fields.gallery -->
<div class="form-group">
  <fc-label params="text: 'Галерея фото/видео'"></fc-label>
  <fc-gallery params="gallery: fields.gallery, loadFile: loadFile, errors: galleryErrors, disabled: disabled"></fc-gallery>
</div>
<!-- /ko -->

<!-- ko if: fields.commentLabel -->

<fc-translated-field
  class="form-group"
  params="field: fields.commentLabel, text: 'Название поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentPlaceholder -->

<fc-translated-field
  class="form-group"
  params="field: fields.commentPlaceholder, text: 'Подсказка внутри поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.clarifyingQuestion -->
<fc-clarifying-question
  params="clarifyingQuestion: fields.clarifyingQuestion, disabled: disabled"
></fc-clarifying-question>
<!-- /ko -->
