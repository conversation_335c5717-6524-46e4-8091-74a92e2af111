import {
  COPY_PROMOCODE_VARIABLE,
  PROMOCODE_VARIABLE,
} from "@/constants/editor-variables";

const { pureComputed } = ko;

export function ViewModel(params, element) {
  const { poll, translation, showErrors, disabled } = params;

  const { alias, fields, loadInterscreenFile, errors } = translation;

  let baseVariables = [
    "name",
    PROMOCODE_VARIABLE,
    COPY_PROMOCODE_VARIABLE,
    "lang",
  ];

  if (poll.hasOrder) {
    baseVariables = [
      "name",
      "orderNumber",
      "orderTime",
      PROMOCODE_VARIABLE,
      COPY_PROMOCODE_VARIABLE,
      "lang",
    ];
  }

  const variables = ko.computed(() => {
    if (poll.withPoints) {
      return [
        ...baseVariables,
        "pointsTotal",
        "points",
        "pointsPercent",
        "pointsResult",
        "pointsDescription",
      ];
    }
    return baseVariables;
  });

  return {
    variables,
    alias,
    fields,
    loadFile: loadInterscreenFile.bind(translation),
    imagesErrors: pureComputed(() => {
      if (!showErrors()) return null;
      return errors().images;
    }),
    disabled,
  };
}
