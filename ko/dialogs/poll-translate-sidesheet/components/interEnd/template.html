<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<div>
  <div class="form-group">
    <fc-label
      params="text: 'Текст на странице', hint: 'Текст на странице'"
    ></fc-label>
    <ckeditor
      params="
      value: fields.text.value,
      variables: variables,
      promocode: fields.promocode,
      disabled: disabled,
      "
    ></ckeditor>
    <fc-original-text
      params="text: fields.text.original, html: true"
    ></fc-original-text>
  </div>
</div>

<div class="row">
  <!-- ko if: fields.restartButtonText -->
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.restartButtonText, text: 'Текст кнопки «Начать заново»', disabled: disabled"
    ></fc-translated-field>
  </div>
  <!-- /ko -->

  <!-- ko if: fields.complaintButtonText -->
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.complaintButtonText, text: 'Текст ссылки «Пожаловаться»', disabled: disabled"
    ></fc-translated-field>
  </div>
  <!-- /ko -->

  <!-- ko if: fields.unsubscribeButtonText -->
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.unsubscribeButtonText, text: 'Текст ссылки «Отписаться»', disabled: disabled"
    ></fc-translated-field>
  </div>
  <!-- /ko -->

  <!-- ko if: fields.closeButtonForWidgetText -->
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.closeButtonForWidgetText, text: 'Текст кнопки «Закрыть» для виджета', disabled: disabled, hint: 'Кнопка отображается только для виджета опроса и закрывает его при клике'"
    ></fc-translated-field>
  </div>
  <!-- /ko -->

  <!-- ko if: fields.readyButtonText -->
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.readyButtonText, text: 'Текст кнопки «Готово»', disabled: disabled"
    ></fc-translated-field>
  </div>
  <!-- /ko -->

  <!-- ko if: fields.reportButtonText -->
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.reportButtonText, text: 'Текст кнопки «Отчет о тестировании»', disabled: disabled"
    ></fc-translated-field>
  </div>
  <!-- /ko -->
 </div>

<!-- ko if: fields.images -->
<fc-label params="text: 'Изображения'"></fc-label>
<div>
  <fc-gallery
    params="gallery: fields.images, loadFile: loadFile, errors: imagesErrors, videoDisabled: true, disabled: disabled"
  ></fc-gallery>
</div>
<!-- /ko -->
