const { pureComputed } = ko;

export function ViewModel(params, element) {
  const { translation, showErrors, disabled } = params;

  const { alias, fields, loadGalleryFile, errors, setVariants, question } = translation;
  let hasDonor = false;
  if (question) {
    if (question.variants && question.variants.length && question.variants[0].donorId !== '-1') {
      hasDonor = question.variants[0].donorId !== '-1';
    }
  }

  const getClarifyingQuestion = (variant) => {
    return fields.clarifyingToEachQuestion.find(({ donorId }) => donorId === variant.id)
  }

  return {
    setVariants,
    getClarifyingQuestion,
    alias,
    fields,
    loadFile: loadGalleryFile.bind(translation),
    galleryErrors: pureComputed(() => {
      if (!showErrors()) return null;
      return errors().gallery;
    }),
    disabled,
  };
}
