<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<fc-base-fields params="fields: fields, disabled: disabled"></fc-base-fields>

<div class="row">
    <div class="col col-6">
        <fc-translated-field
                class="form-group"
                params="field: fields.labels[0], text: 'Метка начальной точки', maxlength: 150, counter: true, disabled: disabled"
        ></fc-translated-field>
    </div>

    <div class="col col-6">
        <fc-translated-field
                class="form-group"
                params="field: fields.labels[1], text: 'Метка конечной точки', maxlength: 150, counter: true, disabled: disabled"
        ></fc-translated-field>
    </div>
</div>

<!-- ko if: fields.skipText -->

<fc-translated-field
        class="form-group"
        params="field: fields.skipText, text: 'Текст (пропуск оценки)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.gallery -->
<div class="form-group">
    <fc-label params="text: 'Галерея фото/видео'"></fc-label>
    <fc-gallery
            params="gallery: fields.gallery, loadFile: loadFile, errors: galleryErrors"
    ></fc-gallery>
</div>
<!-- /ko -->

<!-- ko if: fields.commentLabel -->

<fc-translated-field
        class="form-group"
        params="field: fields.commentLabel, text: 'Название поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentPlaceholder -->

<fc-translated-field
        class="form-group"
        params="field: fields.commentPlaceholder, text: 'Подсказка внутри поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: setVariants -->
<div class="form-group">
        <fc-label
                params="text: 'Варианты для оценки', hint: 'Варианты для оценки'"
        ></fc-label>

        <!-- ko foreach: { data: fields.variants, as: 'variant' } -->
                <div>
                <fc-translated-field
                        class="form-group"
                        params="field: variant, disabled: $parent.disabled"
                ></fc-translated-field>
                </div>

                <!-- ko if: $component.fields.clarifyingToEachQuestion -->
                <!--ko let: {clarifyingQuestion: $component.getClarifyingQuestion(variant)}-->
                <div
                class="pl-15p"
                data-bind="if: !!clarifyingQuestion"
                >
                <fc-clarifying-question
                        params="clarifyingQuestion: clarifyingQuestion, disabled: $component.disabled"
                ></fc-clarifying-question>
                </div>
                <!-- /ko -->
                <!-- /ko -->
        <!-- /ko -->

</div>
<!-- /ko -->

<!-- ko if: fields.clarifyingQuestion && !fields.clarifyingToEachQuestion -->
<fc-clarifying-question
  params="clarifyingQuestion: fields.clarifyingQuestion, disabled: disabled"
></fc-clarifying-question>
<!-- /ko -->
