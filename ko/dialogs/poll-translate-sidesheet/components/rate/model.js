const { pureComputed, toJS } = ko;

export function ViewModel(params, element) {
  const { translation, disabled } = params;

  const { alias, fields, gallery } = translation;

  return {
    alias,
    fields,
    gallery,
    fancyboxGallery: pureComputed(() => {
      const list = toJS(gallery);
      console.log({ list });
      if (list && list.length) {
        return list.map((i) => ({ src: i.url }));
      }
      return [];
    }),
    disabled,
  };
}
