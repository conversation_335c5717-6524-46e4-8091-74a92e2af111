<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<fc-base-fields params="fields: fields, disabled: disabled"></fc-base-fields>

<!-- ko if: fields.rateText -->
<div>
  <fc-translated-field
    class="form-group"
    params="field: fields.rateText, text: 'Текст', maxlength: 500, counter: true, textarea: true, disabled: disabled"
  ></fc-translated-field>
</div>
<!-- /ko -->

<!-- ko if: gallery && gallery.length -->
<div class="form-group">
  <div class="gallery-button d-flex align-items-center" data-bind="fancyboxGalleryItem: {
    gallery: fancyboxGallery,
  }">
    <fc-icon params="name: 'image-bold', color: 'secondary'" class="mr-15p"></fc-icon>
  <span class="font-weight-700 f-color-primary f-fs-3">Галерея</span></div>
</div>
<!-- /ko -->

<!-- ko if: fields.commentLabel -->

<fc-translated-field
  class="form-group"
  params="field: fields.commentLabel, text: 'Название поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentPlaceholder -->

<fc-translated-field
  class="form-group"
  params="field: fields.commentPlaceholder, text: 'Подсказка внутри поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.clarifyingQuestion -->
<fc-clarifying-question
  params="clarifyingQuestion: fields.clarifyingQuestion, disabled: disabled"
></fc-clarifying-question>
<!-- /ko -->

<!-- ko if: fields.variants -->
<div>
  <div class="form-group">
    <fc-label
      params="text: 'Варианты ответов', hint: 'Варианты ответов'"
    ></fc-label>

    <!-- ko foreach: { data: fields.variants, as: 'variant' } -->

    <fc-translated-field
      class="form-group"
      params="field: variant, maxlength: 250, counter: true, disabled: $parent.disabled"
    ></fc-translated-field>
    <!-- /ko -->
  </div>
</div>

<!-- ko if: fields.selfAnswerText -->
<div>
  <fc-translated-field
    class="form-group"
    params="field: fields.selfAnswerText, text: 'Текст (свой вариант)', maxlength: 125, counter: true, disabled: disabled"
  ></fc-translated-field>
</div>
<!-- /ko -->

<!-- ko if: fields.selfAnswerPlaceholder -->
<div>
  <fc-translated-field
    class="form-group"
    params="field: fields.selfAnswerPlaceholder, text: 'Подсказка внутри поля (свой вариант)', maxlength: 125, counter: true, disabled: disabled"
  ></fc-translated-field>
</div>
<!-- /ko -->
<!-- /ko -->
