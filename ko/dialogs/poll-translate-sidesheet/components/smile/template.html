<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<fc-base-fields params="fields: fields, disabled: disabled"></fc-base-fields>

<div class="smile-labels form-group">
  <fc-label params="text : 'Метки', hint: 'Ме<PERSON><PERSON><PERSON>', disabled: disabled"></fc-label>
  <div class="smile-labels__wrapper">
    <!-- ko foreach: { data: fields.smiles, as: 'smile' } -->
    <div class="smile-label">
      <fc-input params="value: smile.value, disabled: $parent.disabled"></fc-input>
      <div class="smile">
        <img
          data-bind="attr: {
              src: smile.url
            }"
          alt=""
        />
      </div>
      <fc-original-text params="text: smile.original"></fc-original-text>
    </div>
    <!-- /ko -->
  </div>
</div>

<!-- ko if: fields.skipText -->

<fc-translated-field
  class="form-group"
  params="field: fields.skipText, text: 'Текст (пропуск оценки)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.gallery -->
<div class="form-group">
  <fc-label params="text: 'Галерея фото/видео'"></fc-label>
  <fc-gallery params="gallery: fields.gallery, loadFile: loadFile, errors: galleryErrors, disabled: disabled"></fc-gallery>
</div>
<!-- /ko -->

<!-- ko if: fields.commentLabel -->

<fc-translated-field
  class="form-group"
  params="field: fields.commentLabel, text: 'Название поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentPlaceholder -->

<fc-translated-field
  class="form-group"
  params="field: fields.commentPlaceholder, text: 'Подсказка внутри поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.clarifyingQuestion -->
<fc-clarifying-question
  params="clarifyingQuestion: fields.clarifyingQuestion, disabled: disabled"
></fc-clarifying-question>
<!-- /ko -->
