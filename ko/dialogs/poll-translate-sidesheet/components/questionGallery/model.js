import { getFilePreview } from "@/utils/file-loader/get-preview";
import { checkFileSize, getSizeError } from "@/utils/file/size";

const { observable } = ko;

export function ViewModel(params, element) {
  const { gallery, loadFile, videoDisabled, errors, disabled } = params;

  const loaderErrors = observable({});

  console.log("question gallery", { params: ko.toJS(params) });


  return {
    gallery,

    errors,
    loaderErrors,

    videoDisabled,

    accept: videoDisabled ? `image/*` : `image/*,video/*`,

    updateElement(galleryElement, file) {
      if (!file) {
        galleryElement.file(null);
        galleryElement.url("");
        return;
      }
      const { type, valid, limit } = checkFileSize(file);

      if (!valid) {
        loaderErrors({
          ...errors,
          [galleryElement.id]: getSizeError(type, limit),
        });

        setTimeout(() => {
          const updatedErrors = loaderErrors();
          delete updatedErrors[galleryElement.id];
          loaderErrors(updatedErrors);
        }, 3000);

        return;
      }

      galleryElement.file(file);

      getFilePreview(file).then((url) => {
        galleryElement.url(url);
        galleryElement.poster(url);
      });
      if (typeof loadFile === "function") loadFile(galleryElement, file);
    },
    disabled,
  };
}
