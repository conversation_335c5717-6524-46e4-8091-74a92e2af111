<div class="question-gallery" data-bind="let: { $gallery: $component }">
  <!-- ko foreach: { data: gallery, as: 'galleryElement' } -->
  <div class="question-gallery-item">
    <div class="question-gallery-item__wrapper">
      <div class="question-gallery-item__preview">
        <fc-img-preview
          params="
            url: galleryElement.url,
            preview: galleryElement.poster,
            canRemove: !$parent.disabled,
            onRemove: function() {
              $gallery.updateElement(galleryElement, null);
            },
          "
        >
          <fc-load-button
            params="onLoad: function(files) {
          $gallery.updateElement(galleryElement, files[0])
        },
        accept: $gallery.accept"
          >
            <fc-icon
              params="name: $gallery.videoDisabled ? 'image-light' : 'video-image', size: 40, color: 'secondary'"
            ></fc-icon>
          </fc-load-button>
        </fc-img-preview>
      </div>
      <div class="question-gallery-item__description">
        <fc-translated-field
          params="value: galleryElement.description, 
          original: galleryElement.originalDescription, 
          maxlength: 500, 
          counter: true, 
          textarea: true,
          disabled: $parent.disabled"
        ></fc-translated-field>
      </div>
    </div>

    <fc-error
      params="text: $gallery.loaderErrors()[galleryElement.id], show:  $gallery.loaderErrors()[galleryElement.id]"
    ></fc-error>

    <!-- ko if: $gallery.errors  -->
    <fc-error
      params="text: $gallery.errors()[galleryElement.id], show:  $gallery.errors()[galleryElement.id]"
    ></fc-error>
    <!-- /ko -->
  </div>

  <!-- /ko -->
</div>
