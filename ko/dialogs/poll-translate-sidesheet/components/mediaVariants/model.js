const { pureComputed } = ko;

export function ViewModel(params, element) {
  const { translation, showErrors, disabled } = params;

  const { alias, fields, loadGalleryFile, errors } = translation;

  return {
    alias,
    fields,
    loadFile: loadGalleryFile.bind(translation),
    galleryErrors: pureComputed(() => {
      if (!showErrors()) return null;
      return errors().gallery;
    }),
    disabled,
  };
}
