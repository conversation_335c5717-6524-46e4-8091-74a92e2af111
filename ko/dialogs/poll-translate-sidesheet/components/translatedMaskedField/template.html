<!-- ko if: field.isNameMask  -->

<!-- ko if: field.nameText -->
<fc-translated-field
  class="form-group"
  params="field: field.nameText, maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
<!-- /ko -->
<!-- ko if: field.namePlaceholder -->
<div class="row">
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: field.namePlaceholder, text: 'Подсказка внутри поля', maxlength: 125, counter: true, disabled: disabled"
    ></fc-translated-field>
  </div>
</div>
<!-- /ko -->

<!-- ko if: field.surnameText -->
<fc-translated-field
  class="form-group"
  params="field: field.surnameText, maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
<!-- /ko -->
<!-- ko if: field.surnamePlaceholder -->
<div class="row">
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: field.surnamePlaceholder, text: 'Подсказка внутри поля', maxlength: 125, counter: true, disabled: disabled"
    ></fc-translated-field>
  </div>
</div>
<!-- /ko -->

<!-- ko if: field.patronymicText -->
<fc-translated-field
  class="form-group"
  params="field: field.patronymicText, maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
<!-- /ko -->
<!-- ko if: field.patronymicPlaceholder -->
<div class="row">
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: field.patronymicPlaceholder, text: 'Подсказка внутри поля', maxlength: 125, counter: true, disabled: disabled"
    ></fc-translated-field>
  </div>
</div>
<!-- /ko -->
<!-- /ko -->

<!-- ko ifnot: field.isNameMask -->
<!-- ko ifnot: single -->
<!-- ko if: field.text -->
<fc-translated-field
  class="form-group"
  params="field: field.text, text: 'Название поля ' + index(), maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
<!-- /ko -->
<!-- /ko -->
<!-- ko if: field.placeholder -->
<div class="row">
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: field.placeholder, text: 'Подсказка внутри поля', maxlength: 125, counter: true, disabled: disabled"
    ></fc-translated-field>
  </div>
</div>
<!-- /ko -->
<!-- /ko -->
