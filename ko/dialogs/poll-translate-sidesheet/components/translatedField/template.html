<!-- ko if: text -->
<fc-label
  params="text: text, hint: hint"
></fc-label>
<!-- /ko -->
<!-- ko if: disabled -->
<!-- ko if: ckeditor -->
<ckeditor
  params="
    value: value,
    variables: [],
    toolbar: ['bold', 'underline', 'italic', 'strikethrough', 'fontSize'],
    disabled: true,
    editable: false,
  "
></ckeditor>
<!-- /ko -->
<!-- ko ifnot: ckeditor -->
<fc-input params="value: value, maxlength: maxlength, counter: counter, disabled: true, placeholder: placeholder"></fc-input>
<!-- /ko -->
<!-- /ko -->

<!-- ko ifnot: disabled -->
<!-- ko if: ckeditor -->
<ckeditor
  params="
    value: value,
    variables: [],
    toolbar: ['bold', 'underline', 'italic', 'strikethrough', 'fontSize'],
  "
></ckeditor>
<!-- /ko -->
<!-- ko ifnot: ckeditor -->
<!-- ko if: textarea -->
<fc-textarea params="value: value, maxlength: maxlength, counter: counter, height: height"></fc-textarea>
<!-- /ko -->
<!-- ko ifnot: textarea -->
<fc-input params="value: value, maxlength: maxlength, counter: counter, placeholder: placeholder"></fc-input>
<!-- /ko -->
<!-- /ko -->
<!-- /ko -->
<!-- ko if: original -->
<fc-original-text params="text: original, html: true"></fc-original-text>
<!-- /ko -->