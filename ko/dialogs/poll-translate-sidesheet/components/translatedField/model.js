export function ViewModel(params, element) {
  element.classList.add("translated-field");
  
  const { value, original, text, hint, hideHint, field, textarea, ckeditor, maxlength, counter, height, disabled, placeholder } = params;
  let disabledBoolean = !!disabled
  return {
    value: field ? field.value : value,
    original: field ? field.original : original,
    text,
    hint: hideHint ? '' : hint || text,
    textarea,
    ckeditor,
    maxlength,
    counter,
    height,
    disabled: disabledBoolean,
    placeholder
  };
}
