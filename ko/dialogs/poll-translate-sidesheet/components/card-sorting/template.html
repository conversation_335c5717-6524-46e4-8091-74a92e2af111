<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<fc-base-fields params="fields: fields, disabled: disabled"></fc-base-fields>

<div class="form-group">
  <fc-label
    params="text: 'Наименование колонки с карточками', hint: 'Наименование колонки с карточками'"
  ></fc-label>

  <fc-translated-field
    class="mb-15p flex-1"
    params="field: fields.cardColumnText, maxlength: 125, counter: true, disabled: disabled"
  ></fc-translated-field>

  <div class="pl-20p">
    <div class="card-sorting__sublabel">Карточки</div>
    <!-- ko foreach: { data: fields.variants, as: 'variant' } -->
      <div>
        <div class="d-flex align-items-start">
          <!-- ko if: variant.file_id -->
          <file-loader-preview class="file-loader-preview file-loader-preview-selected-variant mr-15p mb-15p shrink-0" data-bind="click: function (_, event) {
              event.stopPropagation();
            }," params="loading: false, disabled: true, file: variant.file_url, preview: variant.preview_url">
      
          </file-loader-preview>
        <!-- /ko -->
          <fc-translated-field
            class="mb-15p flex-1"
            params="field: variant, maxlength: 250, counter: true, disabled: $parent.disabled"
          ></fc-translated-field>
        </div>
      </div>
      <div>
        <!-- ko if: $parent.show_tooltips -->
          <fc-translated-field
          class="mb-15p"
          params="value: variant.description, original: variant.original_description, maxlength: 500, counter: true, disabled: $parent.disabled, placeholder: 'Подсказка для варианта'"
        ></fc-translated-field>
        <!-- /ko -->
      </div>
    <!-- /ko -->
  </div>
</div>

<div class="form-group">
  <fc-label
    params="text: 'Наименование колонки с категориями', hint: 'Наименование колонки с категориями'"
  ></fc-label>

  <fc-translated-field
    class="mb-15p flex-1"
    params="field: fields.categoryColumnText, maxlength: 125, counter: true, disabled: disabled"
  ></fc-translated-field>

  <div class="pl-20p">
    <div class="card-sorting__sublabel">Категории</div>
    <!-- ko foreach: { data: fields.categoryList, as: 'variant' } -->
      <div>
        <div class="d-flex align-items-start">
          <!-- ko if: variant.file_id -->
          <file-loader-preview class="file-loader-preview file-loader-preview-selected-variant mr-15p mb-15p shrink-0" data-bind="click: function (_, event) {
              event.stopPropagation();
            }," params="loading: false, disabled: true, file: variant.file_url, preview: variant.preview_url">
      
          </file-loader-preview>
        <!-- /ko -->
          <fc-translated-field
            class="mb-15p flex-1"
            params="field: variant, maxlength: 250, counter: true, disabled: $parent.disabled"
          ></fc-translated-field>
        </div>
      </div>
      <div>
        <!-- ko if: $parent.show_tooltips -->
          <fc-translated-field
          class="mb-15p"
          params="value: variant.description, original: variant.original_description, maxlength: 500, counter: true, disabled: $parent.disabled, placeholder: 'Подсказка для варианта'"
        ></fc-translated-field>
        <!-- /ko -->
      </div>
    <!-- /ko -->
  </div>
</div>

<!-- ko if: fields.selfAnswerText -->

<fc-translated-field
  class="mb-15p"
  params="field: fields.selfAnswerText, text: 'Текст (свой вариант)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
  <!-- ko if: show_tooltips -->
    <fc-translated-field
    class="form-group"
    params="field: fields.selfAnswerDescription, maxlength: 500, counter: true, disabled: disabled, placeholder: 'Подсказка для своего варианта'"
    ></fc-translated-field>
  <!-- /ko -->
<!-- /ko -->

<!-- ko if: fields.selfAnswerPlaceholder -->

<fc-translated-field
  class="form-group"
  params="field: fields.selfAnswerPlaceholder, text: 'Подсказка внутри поля (свой вариант)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentLabel -->

<fc-translated-field
  class="form-group"
  params="field: fields.commentLabel, text: 'Название поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentPlaceholder -->

<fc-translated-field
  class="form-group"
  params="field: fields.commentPlaceholder, text: 'Подсказка внутри поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.skipText -->

<fc-translated-field
  class="form-group"
  params="field: fields.skipText, text: 'Текст (пропуск оценки)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.gallery -->
<div class="form-group">
  <fc-label params="text: 'Галерея фото/видео'"></fc-label>
  <fc-gallery
    params="gallery: fields.gallery, loadFile: loadFile, errors: galleryErrors, disabled: disabled"
  ></fc-gallery>
</div>

<!-- /ko -->