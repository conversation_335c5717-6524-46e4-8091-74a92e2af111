const { pureComputed } = ko;

export function ViewModel(params, element) {
  const { translation, showErrors, disabled } = params;

  const { alias, fields, loadGalleryFile, errors } = translation;

  if (fields?.skipText?.original == "Не готов(а) оценить") {
    fields.skipText.original = 'Затрудняюсь ответить'
  }


  return {
    alias,
    fields,
    loadFile: loadGalleryFile.bind(translation),
    galleryErrors: pureComputed(() => {
      if (!showErrors()) return null;
      return errors().gallery;
    }),
    disabled,
  };
}
