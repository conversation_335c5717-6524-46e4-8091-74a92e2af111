<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<fc-base-fields params="fields: fields, disabled: disabled"></fc-base-fields>

<div class="form-group">
    <fc-label
            params="text: 'Варианты ответов', hint: 'Варианты ответов'"
    ></fc-label>

    <!-- ko foreach: { data: fields.variants, as: 'variant' } -->
        <div>
                <fc-translated-field
                        class="form-group"
                        params="field: variant, disabled: $parent.disabled"
                ></fc-translated-field>
        </div>
    <!-- /ko -->
</div>

<div class="form-group">
        <fc-translated-field
                class="form-group"
                params="
                        field: fields.indicatorText,
                        text: 'Текст для индикатора распределения',
                        maxlength: 125,
                        counter: true,
                        disabled: disabled
                "
        ></fc-translated-field>
</div>

<!-- ko if: fields.skipText -->

<fc-translated-field
        class="form-group"
        params="field: fields.skipText, text: 'Текст (пропуск оценки)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.gallery -->
<div class="form-group">
    <fc-label params="text: 'Галерея фото/видео'"></fc-label>
    <fc-gallery
            params="gallery: fields.gallery, loadFile: loadFile, errors: galleryErrors, disabled: disabled"
    ></fc-gallery>
</div>
<!-- /ko -->

<!-- ko if: fields.commentLabel -->

<fc-translated-field
        class="form-group"
        params="field: fields.commentLabel, text: 'Название поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentPlaceholder -->

<fc-translated-field
        class="form-group"
        params="field: fields.commentPlaceholder, text: 'Подсказка внутри поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->
