import { TreeNodeViewProps } from "@/presentation/views/fc-tree/types";

export function ViewModel(props: TreeNodeViewProps, element: HTMLElement) {
  const { node, nodeStructure, itemData } = props;
  const { items, descriptions } = itemData;
  console.log("item view", ko.toJS(itemData), { node, nodeStructure });
  return {
    value: items[node.id],
    description_value: descriptions[node.id],
    isCategory: nodeStructure.category,
    name: nodeStructure.name,
    description: node.data.description,
  };
}
