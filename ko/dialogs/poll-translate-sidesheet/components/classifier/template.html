<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<fc-base-fields params="fields: fields, disabled: disabled"></fc-base-fields>

    <h2 class="f-h2 mb-20p pb-0">
      Варианты ответов
    </h2>

    <div class="form-group">
      <classifier-translation-tree
        class="classifier-translation-tree"
        params="
          tree: tree,
          itemComponentName: 'classifier-translation-tree-item',
          itemData: {
            items: fields.items,
            descriptions: fields.descriptions,
            isFullBlocked: disabled,
            showTooltips: show_tooltips
          },
        "
      ></classifier-translation-tree>
    </div>


  <!-- ko if: fields.gallery -->
  <div class="form-group">
    <fc-label params="text: 'Галерея фото/видео'"></fc-label>
    <fc-gallery
      params="gallery: fields.gallery, loadFile: loadFile, errors: galleryErrors, disabled: disabled"
    ></fc-gallery>
  </div>
  <!-- /ko -->

 <!-- ko if: fields.commentLabel -->

<fc-translated-field
class="form-group"
params="field: fields.commentLabel, text: 'Название поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentPlaceholder -->

<fc-translated-field
class="form-group"
params="field: fields.commentPlaceholder, text: 'Подсказка внутри поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.skipText -->

<fc-translated-field
  class="form-group"
  params="field: fields.skipText, text: 'Текст (пропуск оценки)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->
</div>
