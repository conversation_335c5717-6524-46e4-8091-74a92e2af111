const { pureComputed } = ko;

export function ViewModel(params, element) {
  const { translation, showErrors, disabled } = params;

  const { alias, fields, loadGalleryFile, errors, tree } = translation;
  const show_tooltips = translation.question.show_tooltips

 if (fields?.skipText?.original == "Не готов(а) оценить") {
    fields.skipText.original = 'Затрудняюсь ответить'
  }

  return {
    alias,
    tree,
    fields,
    loadFile: loadGalleryFile.bind(translation),
    galleryErrors: pureComputed(() => {
      if (!showErrors()) return null;
      return errors().gallery;
    }),
    disabled,
    show_tooltips: ko.observable(show_tooltips)
  };
}
