<div>
  <fc-translated-field
    class="form-group"
    params="field: clarifyingQuestion.text, text: 'Уточняющий вопрос', textarea: true, maxlength: 500, counter: true, disabled: disabled"
  ></fc-translated-field>
</div>

<!-- ko if: clarifyingQuestion.textAnswerPlaceholder -->
<div>
  <fc-translated-field
    class="form-group"
    params="field: clarifyingQuestion.textAnswerPlaceholder, text: 'Подсказка внутри поля (текстовый ответ)', maxlength: 125, counter: true, disabled: disabled"
  ></fc-translated-field>
</div>
<!-- /ko -->

<!-- ko if: clarifyingQuestion.variants -->
<div>
  <div class="form-group">
    <fc-label
      params="text: 'Варианты ответов на уточняющий вопрос', hint: 'Варианты ответов'"
    ></fc-label>

    <!-- ko foreach: { data: clarifyingQuestion.variants, as: 'variant' } -->

    <div class="d-flex align-items-start">
      <!-- ko if: variant.file_id -->
      <file-loader-preview class="file-loader-preview file-loader-preview-selected-variant mr-15p mb-15p shrink-0" data-bind="click: function (_, event) {
          event.stopPropagation();
        }," params="loading: false, disabled: true, file: variant.file_url, preview: variant.preview_url">
  
      </file-loader-preview>
    <!-- /ko -->
      <fc-translated-field
        class="mb-15p flex-1"
        params="field: variant, maxlength: 250, counter: true, disabled: $parent.disabled"
      ></fc-translated-field>
    </div>
    <!-- /ko -->
  </div>
</div>
<!-- /ko -->

<!-- ko if: clarifyingQuestion.selfAnswerText -->
<div>
  <div class="d-flex align-items-start">
    <!-- ko if: clarifyingQuestion.selfVariantFile -->
        <file-loader-preview class="file-loader-preview file-loader-preview-selected-variant mr-15p mb-15p shrink-0" data-bind="click: function (_, event) {
          event.stopPropagation();
        }," params="loading: false, disabled: true, file: clarifyingQuestion.selfVariantFile.file_url, preview: clarifyingQuestion.selfVariantFile.preview_url">

      </file-loader-preview>
    <!-- /ko -->
    <fc-translated-field
    data-bind="click: function () {console.log(clarifyingQuestion)}"
    class="form-group flex-1"
    params="field: clarifyingQuestion.selfAnswerText, text: 'Текст (свой вариант)', maxlength: 125, counter: true, disabled: disabled"
  ></fc-translated-field>
  </div>
  
</div>
<!-- /ko -->

<!-- ko if: clarifyingQuestion.selfAnswerPlaceholder -->
<div>
  <fc-translated-field
    class="form-group"
    params="field: clarifyingQuestion.selfAnswerPlaceholder, text: 'Подсказка внутри поля (свой вариант)', maxlength: 125, counter: true, disabled: disabled"
  ></fc-translated-field>
</div>
<!-- /ko -->
