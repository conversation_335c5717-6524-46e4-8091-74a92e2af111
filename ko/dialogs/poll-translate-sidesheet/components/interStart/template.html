<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<div class="row">
  <div class="col col-12">
    <div class="form-group">
      <fc-label
        params="text: 'Текст на странице', hint: 'Текст на странице'"
      ></fc-label>
      <ckeditor
        params="
        value: fields.text.value,
        variables: variables,
        promocode: fields.promocode,
        disabled: disabled,
        "
      ></ckeditor>

      <fc-original-text
        params="text: fields.text.original, html: true"
      ></fc-original-text>
    </div>
  </div>

  <!-- ko if: fields.agreementText -->
  <div class="col col-12">
    <div class="form-group">
      <fc-label
        params="text: 'Текст согласия', hint: 'Текст согласия'"
      ></fc-label>
      <ckeditor
        params="
          value: fields.agreementText.value,
          variables: [],
          blankExternalLinks: true,
          toolbar: ['bold', 'underline', 'italic', 'strikethrough', 'link', 'alignment:left', 'alignment:right', 'alignment:center', 'alignment:justify', 'fontSize'],
          disabled: disabled,
        "
        class="ckeditor_min"
      ></ckeditor>

      <fc-original-text
        params="text: fields.agreementText.original, html: true"
      ></fc-original-text>
    </div>
  </div>
  <!-- /ko -->

  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.pollButtonText, text: 'Текст кнопки «Пройти опрос»', disabled: disabled"
    ></fc-translated-field>
  </div>

  <!-- ko if: fields.unsubscribeButtonText -->
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.unsubscribeButtonText, text: 'Текст ссылки «Отписаться»', disabled: disabled"
    ></fc-translated-field>
  </div>
  <!-- /ko -->

  <!-- ko if: fields.complaintButtonText -->
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.complaintButtonText, text: 'Текст ссылки «Пожаловаться»', disabled: disabled"
    ></fc-translated-field>
  </div>
  <!-- /ko -->
</div>

<!-- ko if: fields.images -->
<fc-label params="text: 'Изображения'"></fc-label>
<div>
  <fc-gallery
    params="gallery: fields.images, loadFile: loadFile, errors: imagesErrors, videoDisabled: true, disabled: disabled"
  ></fc-gallery>
</div>
<!-- /ko -->
