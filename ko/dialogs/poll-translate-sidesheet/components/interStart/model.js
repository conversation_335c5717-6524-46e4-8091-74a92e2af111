import {
  COPY_PROMOCODE_VARIABLE,
  PROMOCODE_VARIABLE,
} from "@/constants/editor-variables";

const { pureComputed } = ko;

export function ViewModel(params, element) {
  const { poll, translation, showErrors, disabled } = params;

  const { fields, loadInterscreenFile, alias, errors } = translation;

  let variables = ["name", PROMOCODE_VARIABLE, COPY_PROMOCODE_VARIABLE, "lang"];

  if (poll.hasOrder) {
    variables = [
      "name",
      "orderNumber",
      "orderTime",
      PROMOCODE_VARIABLE,
      COPY_PROMOCODE_VARIABLE,
      "lang"
    ];
  }

  return {
    variables,
    alias,
    fields,
    loadFile: loadInterscreenFile.bind(translation),
    imagesErrors: pureComputed(() => {
      if (!showErrors()) return null;
      return errors().images;
    }),
    disabled,
  };
}
