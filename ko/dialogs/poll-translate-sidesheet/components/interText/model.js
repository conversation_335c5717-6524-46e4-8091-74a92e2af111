import {
  COPY_PROMOCODE_VARIABLE,
  PROMOCODE_VARIABLE,
} from "@/constants/editor-variables";

const { pureComputed } = ko;

export function ViewModel(params, element) {
  const { poll, translation, showErrors, disabled } = params;

  const { alias, fields, loadInterscreenFile, errors, question } = translation;
  let variables = fields.isInterFiveSecondTest ? 
  ["name"] : 
  ["name", PROMOCODE_VARIABLE, COPY_PROMOCODE_VARIABLE, "lang"];

  const showQuestionNumber = question.showQuestionNumber;

  if (poll.hasOrder) {
    variables = [
      "name",
      "orderNumber",
      "orderTime",
      PROMOCODE_VARIABLE,
      COPY_PROMOCODE_VARIABLE,
      "lang"
    ];
  }

  return {
    variables,
    alias,
    fields,
    loadFile: loadInterscreenFile.bind(translation),
    imagesErrors: pureComputed(() => {
      if (!showErrors()) return null;
      return errors().images;
    }),
    disabled,
    showQuestionNumber
  };
}
