<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<div class="row">
  <!-- ko if: showQuestionNumber -->
  <div class="col col-12">
    <fc-translated-field
            class="form-group"
            params="field: fields.name, text: 'Название вопроса', maxlength: 60, counter: true, disabled: disabled"
    ></fc-translated-field>
  </div>
  <!-- /ko -->
  <div class="col col-12">

    
    <div class="form-group">
      <!-- ko ifnot: fields.isInterFiveSecondTest -->
      <fc-label
        params="text: 'Текст на странице', hint: 'Текст на странице'"
      ></fc-label>
      <!-- /ko -->
      <!-- ko if: fields.isInterFiveSecondTest -->
      <fc-label
        params="text: 'Инструкция'"
      ></fc-label>
      <!-- /ko -->
      <ckeditor
        params="
        value: fields.text.value,
        variables: variables,
        promocode: fields.promocode,
        disabled: disabled,
        "
      ></ckeditor>

      <fc-original-text
        params="text: fields.text.original, html: true"
      ></fc-original-text>
    </div>
  </div>

  <!-- ko if: fields.agreementText && !fields.isInterFiveSecondTest -->
  <div class="col col-12">
    <div class="form-group">
      <fc-label
        params="text: 'Текст согласия', hint: 'Текст согласия'"
      ></fc-label>
      <ckeditor
        params="
          value: fields.agreementText.value,
          variables: [],
          blankExternalLinks: true,
          toolbar: ['bold', 'underline', 'italic', 'strikethrough', 'link', 'alignment:left', 'alignment:right', 'alignment:center', 'alignment:justify', 'fontSize'],
          disabled: disabled,
        "
        class="ckeditor_min"
      ></ckeditor>

      <fc-original-text
        params="text: fields.agreementText.original, html: true"
      ></fc-original-text>
    </div>
  </div>
  <!-- /ko -->
</div>

<!-- ko if: fields.images && !fields.isInterFiveSecondTest -->
<fc-label params="text: 'Изображения'"></fc-label>
<div>
  <fc-gallery
    params="gallery: fields.images, loadFile: loadFile, errors: imagesErrors, videoDisabled: true, disabled: disabled"
  ></fc-gallery>
</div>
<!-- /ko -->

<!-- ko if: fields.isInterFiveSecondTest -->
<div class="row">
  <div class="col col-6">
    <fc-translated-field
      class="form-group"
      params="field: fields.showImageButtonText, text: 'Текст кнопки «Показать изображение»', hideHint: true, maxlength: 40, counter: true, disabled: disabled"
    ></fc-translated-field>
  </div>
</div>
<!-- /ko -->
