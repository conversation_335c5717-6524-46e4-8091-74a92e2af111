const { pureComputed } = ko;

export function ViewModel(params, element) {
  const { translation, showErrors, disabled } = params;

  const { alias, fields, loadGalleryFile, errors } = translation;
  const show_tooltips = translation.question.show_tooltips

  return {
    alias,
    fields,
    loadFile: loadGalleryFile.bind(translation),
    galleryErrors: pureComputed(() => {
      if (!showErrors()) return null;
      return errors().gallery;
    }),
    showErrors,
    disabled,
    show_tooltips: ko.observable(show_tooltips)
  };
}
