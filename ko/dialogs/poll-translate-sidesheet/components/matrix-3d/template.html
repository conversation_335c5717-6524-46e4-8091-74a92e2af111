<!-- ko if: alias -->
<div class="alias" data-bind="text: alias"></div>
<!-- /ko -->

<fc-base-fields params="fields: fields, disabled: disabled"></fc-base-fields>

<div class="form-group">
  <div class="field-label mb-20p">Метки строк</div>
  <!-- ko foreach: { data: fields.rows, as: 'variant' } -->
  <fc-translated-field
    class="mb-15p"
    params="field: variant, maxlength: 250, counter: true, disabled: $parent.disabled, placeholder: 'Наименование строки'"
  ></fc-translated-field>
  <!-- ko if: $parent.show_tooltips -->
  <fc-translated-field
    class="mb-15p"
    params="value: variant.description, original: variant.original_description, maxlength: 500, counter: true, disabled: $parent.disabled, placeholder: 'Подсказка для строки'"
  ></fc-translated-field>
  <!-- /ko -->
  <!-- /ko -->
</div>

<div class="form-group">
  <div class="field-label mb-20p">Метки столбцов</div>
  <!-- ko foreach: { data: fields.cols, as: 'variant' } -->
  <fc-translated-field
    class="mb-15p"
    params="field: variant, maxlength: 250, counter: true, disabled: $parent.disabled, placeholder: 'Наименование столбца'"
  ></fc-translated-field>
  <!-- ko if: $parent.show_tooltips -->
  <fc-translated-field
    class="mb-15p"
    params="value: variant.description, original: variant.original_description, maxlength: 500, counter: true, disabled: $parent.disabled, placeholder: 'Подсказка для столбца'"
  ></fc-translated-field>
  <!-- /ko -->
  
  <div style="margin-left: 20px">
    <p style="margin-bottom: 10px">
      Ответы столбца
    </p>
    <!-- ko foreach: { data: variant.children, as: 'childVariant' } -->
    <fc-translated-field
      class="mb-15p"
      params="
        field: childVariant,
        maxlength: 250,
        counter: true,
        disabled: $parents[1].disabled,
      "
    ></fc-translated-field>
    <!-- /ko -->
  </div>
  <!-- /ko -->
</div>

<!-- ko if: fields.selectPlaceholder -->
<fc-translated-field
  class="form-group"
  params="field: fields.selectPlaceholder, text: 'Подсказка внутри поля выбора варианта', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
<!-- /ko -->

<!-- ko if: fields.commentLabel -->

<fc-translated-field
class="form-group"
params="field: fields.commentLabel, text: 'Название поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>

<!-- /ko -->

<!-- ko if: fields.commentPlaceholder -->

<!-- <fc-translated-field
class="form-group"
params="field: fields.commentPlaceholder, text: 'Подсказка внутри поля комментария', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field> -->

<!-- /ko -->

<!-- ko if: fields.skipText -->
<fc-translated-field
  class="form-group"
  params="field: fields.skipText, text: 'Текст (пропуск ответа)', maxlength: 125, counter: true, disabled: disabled"
></fc-translated-field>
<!-- /ko -->

<!-- ko if: fields.gallery -->
<div class="form-group">
  <fc-label params="text: 'Галерея фото/видео'"></fc-label>
  <fc-gallery
    params="gallery: fields.gallery, loadFile: loadFile, errors: galleryErrors, disabled: disabled"
  ></fc-gallery>
</div>
<!-- /ko -->
