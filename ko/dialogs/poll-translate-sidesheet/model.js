import { DialogWrapper } from "@/dialogs/wrapper";
import { PollQuestionFactory } from "@/entities/models/poll-question";
import { getPollDesign } from "@/api/poll/get-poll-design";
import { CommonTranslation } from "./models/CommonTranslation";
import { QuestionTranslationFactory } from "./models/questionTranslationFactory";
import { groupPollQuestions } from "@/utils/poll/group-poll-questions";
import { COMMON_ELEMENTS } from "./constants";
import * as QuestionsPaginator from "@/presentation/views/fc-questions/fc-questions-paginator";
import { registerComponent } from "@/utils/engine/register-component";
import { getComponent } from "./utils/components.ts";
import {
  getPollTranslation,
  savePollTranslation,
} from "@/api/poll/poll-translation";
import { formatTranslation } from "./utils/translation";
import { PollDesign } from "@/entities/models/poll-design";
import { toQuestionPreview } from "../../router/poll";
import { useSearchParams } from "../../utils/url/use-search-params";
import { getCompanyFilialsList } from "@/api/company/get-filials";

registerComponent("fc-translate-paginator", QuestionsPaginator);

const { observable, computed } = ko;

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    const { poll, questions, updateLiveQuestion } = params;

    const [searchParams, setSearchParams] = useSearchParams();

    this.showSuccessMessage = observable(false);
    this.isSubmitted = observable(false);
    this.design = observable(null);
    this.poll = poll;

    this.updateLiveQuestion = updateLiveQuestion;

    this.inited = observable(false);
    this.loadingTranslation = observable(false);
    this.savedTranslation = null;

    this.langs = poll.langs
      .filter((lang) => !lang.isDefaultLang)
      .map((lang) => {
        return {
          id: lang.id,
          text: lang.langName,
          shortName: lang.langShortName,
        };
      });

    const defaultLang = poll.langs.find((lang) => lang.isDefaultLang);
    this.activeLang = observable(this.langs[0].id);
    this.activeLangData = computed(() => {
      const activeLangId = this.activeLang();
      return this.langs.find((l) => l.id === activeLangId);
    });

    const savedLang = searchParams().pollLang;

    const updateSearchParams = () => {
      const lang = this.activeLangData();
      setSearchParams({
        ...searchParams(),
        pollLang: lang && lang.shortName,
      });
    };

    if (savedLang) {
      const lang = this.langs.find((l) => l.shortName === savedLang);
      if (lang) this.activeLang(lang.id);
    } else {
      updateSearchParams();
    }

    this.activeLang.subscribe(() => {
      updateSearchParams();
    });

    this.commonElements = new CommonTranslation(defaultLang);

    console.log('DialogWrapper questions', questions)
    const questionsList = questions.map((q) => PollQuestionFactory(q));

    this.questions = questionsList;

    this.previewLink = computed(() => {
      const activeLang = this.activeLang();
      console.log('questionsList', questionsList)
      const lang = this.langs.find((l) => l.id === activeLang);
      return toQuestionPreview(questionsList[0].id, {
        lang: lang?.shortName || "",
      });
    });

    const questionsOrder = groupPollQuestions(poll, questionsList);

    this.pagination = [
      { displayText: "Общие настройки", id: COMMON_ELEMENTS },
      ...questionsOrder,
    ];

    this.activeLang.subscribe((v) => {
      this.loadTranslation();
    });

    Promise.all([this.loadFilials(), this.loadDesign()]).then(([filials]) => {
      this.elements = [
        {
          id: COMMON_ELEMENTS,
          translation: this.commonElements,
        },
        ...questionsList.map((q) => {
          return {
            id: q.id,
            question: q,
            translation: QuestionTranslationFactory({
              question: q,
              questions: this.questions,
              lang: this.activeLang,
              filials: filials,
            }),
          };
        }),
      ];

      this.activeElementId = observable(COMMON_ELEMENTS);
      this.activeElement = computed(() => {
        const activeElementId = this.activeElementId();
        return this.elements.find((el) => el.id === activeElementId);
      });
      this.activeComponent = computed(() => {
        const activeElement = this.activeElement();
        console.log('4676 activeElement', activeElement)

        return getComponent(activeElement);
      });

      this.loadTranslation();

      this.inited(true);
    });
  }

  loadFilials() {
    return getCompanyFilialsList();
  }

  loadDesign() {
    return getPollDesign(this.poll.id).then((response) => {
      if (response.data) {
        this.design(PollDesign(response.data));
      }
    });
  }

  loadTranslation() {
    this.loadingTranslation(true);

    const lang = this.activeLang();

    getPollTranslation(this.poll.id, lang).then((response) => {
      console.log('4676 getPollTranslation', response)
      const translation = formatTranslation(response.data);
      this.savedTranslation = translation;

      this.setTranslation(translation);
      this.loadingTranslation(false);
    });
  }

  setTranslation(translation) {
    console.log('4676', this.elements)
    this.elements.forEach((el) => {
      const elementTranslation = translation[el.id] || {};

      el.translation.updateTranslation(elementTranslation);
    });
  }

  reset() {
    this.setTranslation(this.savedTranslation);
  }

  submit() {
    this.isSubmitted(true);
    const firstInvalidElement = this.elements.find((element) => {
      return !element.translation.isValid();
    });
    if (firstInvalidElement) {
      this.activeElementId(firstInvalidElement.id);
      return;
    }

    const lang = this.activeLang();

    let result = {
      q: {},
    };

    this.elements.forEach((element) => {
      const translation = element.translation.getData();
      if (element.id === COMMON_ELEMENTS) {
        result = {
          ...result,
          ...translation,
        };
      } else {
        result.q[element.id] = translation;
      }
    });

    savePollTranslation(this.poll.id, lang, result).then((response) => {
      const translation = formatTranslation(response.data);
      this.savedTranslation = translation;
      this.showSuccessMessage(true);
      if (typeof this.updateLiveQuestion === "function") {
        this.updateLiveQuestion();
      }
    });
  }
}
