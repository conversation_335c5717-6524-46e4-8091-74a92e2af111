<!-- ko let: { $dialogWrapper: $component } -->
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2
        class="foquz-dialog__title"
        data-bind="text: 'Настройка языковых вариантов'"
      ></h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <!-- ko if: !inited() -->
    <fc-spinner class="f-color-primary"></fc-spinner>
    <!-- /ko -->

    <!-- ko if: inited -->

    <div class="container">
      <div class="langs-toolbar">
        <div class="langs-toggler">
          <fc-button-group
            params="
            value: activeLang, options: langs, size: 'xl'"
          ></fc-button-group>
        </div>
        <div class="">
          <a
            class="preview-link"
            target="_blank"
            data-bind="attr: {
            href: previewLink
          }"
          >
            <fc-icon
              params="name: 'outer-link', color: 'secondary', size: 12"
              class="mr-10p"
            ></fc-icon>
            <span>Открыть предпросмотр</span>
          </a>
        </div>
      </div>
    </div>

    <div class="questions-paginator">
      <div class="container">
        <fc-translate-paginator
          params="
          items: pagination, active: activeElementId"
        ></fc-translate-paginator>
      </div>
    </div>

    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="settings-block">
        <!-- ko if: loadingTranslation() -->
        <fc-spinner class="f-color-primary"></fc-spinner>
        <!-- /ko -->

        <!-- ko if: !loadingTranslation() -->

        <div class="container">
          <!-- ko if: activeComponent -->
          <div
            data-bind="component: {
            name: activeComponent,
            params: {
              poll: poll,
              translation: activeElement().translation,
              questions: questions,
              lang: activeLang,
              showErrors: isSubmitted,
              design: design,
              disabled: window.CURRENT_USER.watcher, 
            }
          }"
          ></div>
          <!-- /ko -->
        </div>

        <!-- /ko -->
      </div>
    </div>

    <!-- /ko -->
  </div>
  <!-- ko ifnot: window.CURRENT_USER.watcher -->
  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <fc-button
        params="
        label: 'Отменить',
        icon: { name: 'bin' },
        click: function() {
          reset()
        }"
      ></fc-button>
      <fc-button
        class="ml-10p"
        params="
        label: 'Сохранить',
        icon: { name: 'save' },
        color: 'success',
        click: function() {
          submit()
        }"
      ></fc-button>
    </div>
    <fc-success params="show: showSuccessMessage"></fc-success>
  </div>
  <!-- /ko -->
</sidesheet>
<!-- /ko -->
