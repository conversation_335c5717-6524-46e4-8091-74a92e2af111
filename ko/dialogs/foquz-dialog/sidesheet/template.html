<!-- ko let: { $dialog: $component, $_nodes: $componentTemplateNodes } -->


<div class="foquz-dialog__mask"
     data-bind="click: onMaskClick"></div>

<div class="foquz-dialog__container" >
  <div class="foquz-dialog__content">
    <button type="button" class="foquz-dialog__close" data-bind="click: function() {
      $dialog.hide('sidesheet-close-button')
    }">
      <svg-icon params="name: 'times'" class="svg-icon--sm"></svg-icon>
    </button>
    <!-- ko using: dialogWrapper ? dialogWrapper : $component -->

    <!-- ko template: { nodes: $_nodes } -->
    <!-- /ko -->

    <!-- /ko -->
  </div>
</div>

<!-- /ko -->
