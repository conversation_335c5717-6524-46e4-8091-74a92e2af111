import { DialogWrapper } from 'Dialogs/wrapper';
import { PollModel } from './models/poll';
import { Translator } from '@/utils/translate';
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    this.translator = Translator('questions')

    this.query = ko.observable('');
    this.dict = {};

    this.poll = params.poll;

    this.polls = ko.observableArray([]);
    this.polls.subscribe((v) => {
      this.loadPolls(v);
    });

    this.hasStartScreen = params.hasStartScreen;
    this.pagesMode = params.pagesMode;
    this.pages = params.pages;

    this.filteredPolls = ko.pureComputed(() => {
      if (!this.polls().length) return [];

      return this.polls()
        .map((pollId) => this.dict[pollId])
        .filter((poll) => poll.filteredQuestions().length);
    });

    this.copied = ko.computed(() => {
      return _.flatten(this.filteredPolls().map(poll => poll.getQuestions()))
    });

    this.onCopy = params.onCopy;
  }

  loadPolls(pollIds) {
    pollIds.forEach((id) => {

      let pollModel = this.dict[id];

      if (!pollModel) {
        this.dict[id] = new PollModel(id, {
          query: this.query,
          hasStartScreen: this.hasStartScreen
        });
      }
    });

    Object.keys(this.dict).forEach((id) => {
      if (!pollIds.includes(id)) {
        this.dict[id] = null;
      }
    });
  }

  copy(pageId) {
    if (typeof this.onCopy == 'function') {
      this.onCopy({ questions: this.copied(), page: pageId });
      this.hide();
    }
  }


}
