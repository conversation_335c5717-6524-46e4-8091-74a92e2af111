<!-- ko let: { $dialogWrapper: $component } -->
<sidesheet params="ref: modal, dialogWrapper: $component">

  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title" data-bind="text: $dialogWrapper.translator.t('Копировать вопросы')">

      </h2>
    </div>
  </div>


  <div class="foquz-dialog__body">
    <div class="container">
      <div class="pr-10p">

        <div class="f-color-service f-fs-1 mb-20p" data-bind="text: $dialogWrapper.translator.t('Сначала нужно выбрать опрос, из которого нужно скопировать вопрос')">
        </div>

        <div class="row">
          <div class="col-6">
            <div class="form-group">
              <label class="form-label" data-bind="text: $dialogWrapper.translator.t('Опрос')"></label>

              <polls-select params="isAuto: false, value: polls,
              placeholder: $dialogWrapper.translator.t('Выберите опрос'), multiple: true, filter: function(data) {
                return data.id != poll;
              }"></polls-select>
            </div>
          </div>

          <div class="col-6">
            <!-- ko if: polls().length -->
            <div class="form-group ghost-label">
              <search-field params="value: query"></search-field>
            </div>
            <!-- /ko -->
          </div>
        </div>

      </div>
    </div>

    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container">
        <!-- ko if: polls().length -->
        <div class="pb-4 pr-20p">

          <!-- ko ifnot: filteredPolls().length -->
          <div class="text-center f-color-service pt-35p border-top"><span data-bind="text: $dialogWrapper.translator.t('Ничего не найдено')"></span></div>
          <!-- /ko -->

          <!-- ko if: filteredPolls().length -->
          <!-- ko foreach: filteredPolls -->
          <copy-questions-poll-block params="poll: $data"></copy-questions-poll-block>
          <!-- /ko -->
          <!-- /ko -->


        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>



  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn"
              data-bind="click: function() {
                  $dialog.hide();
                }">
        <foquz-icon params="icon: 'bin'"
                    class="f-btn-prepend"></foquz-icon>
        <span data-bind="text: $dialogWrapper.translator.t('Отменить')"></span>
      </button>


      <!-- ko if: pagesMode && pages.length > 1 -->
      <button class="f-btn f-btn-success"
              type="button"
              data-bind="dropdown, dropdownMode: 'button', enable: copied().length"
              aria-expanded="false">
              <span class="f-btn-prepend">
                <svg-icon params="name: 'link'" class="svg-icon--lg"></svg-icon>
              </span>
        <span data-bind="text: $dialogWrapper.translator.t('Копировать на страницу')"></span>
        <span class="f-btn-append-section">
          <foquz-icon params="icon: 'arrow-bottom'"></foquz-icon>
        </span>

        <template>
          <div class="tippy-list"
               data-bind="">
            <!-- ko foreach: pages -->
            <a class="tippy-list__item"
               href="javascript:void(0)"
               data-bind="click: function () {
                  $dialogWrapper.copy(id)
                }, text: name() || $dialogWrapper.translator.t('Название страницы {number}', {
                  number: $index() + 1
                })">

            </a>
            <!-- /ko -->

          </div>
        </template>
      </button>
      <!-- /ko -->
      <!-- ko if: !pagesMode || pages.length <= 1 -->
      <button type="button"
              class="f-btn f-btn-success"
              data-bind="click: function() { copy();  }, enable: copied().length">
        <foquz-icon params="icon: 'save'"
                    class="f-btn-prepend"></foquz-icon>
        <span data-bind="text: $dialogWrapper.translator.t('Копировать')"></span>
      </button>
      <!-- /ko -->
    </div>
  </div>

</sidesheet>
<!-- /ko -->
