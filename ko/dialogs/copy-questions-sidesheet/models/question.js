import { MATRIX_3D_QUESTION } from "@/data/question-types";
import {
  isEndScreen,
  isInterblock,
  isShowNumber,
  isStartScreen,
  isTextScreen
} from 'Utils/questions/interblock';

export class QuestionModel {
  constructor(data, config = {}) {
    this.id = data.id;
    this.type = data.main_question_type;
    this.name = data.questionName || '';
    this.required = data.is_required == 1;
    this.checked = ko.observable(false);
    this.query = config.query;
    this.hasStartScreen = config.hasStartScreen;
    this.pointName = data.pointName;
    this.donorId = this.type == MATRIX_3D_QUESTION ? data.donor_rows || data.donor_columns : data.donor;

    this.visible = ko.computed(() => {
      let q = this.query();
      if (!q) return true;

      q = q.toLowerCase();
      return this.name.toLowerCase().includes(q);
    });

    this.visible.subscribe((v) => {
      if (!v) this.checked(false);
    });

    data.intermediateBlock = {
      screen_type: data.screen_type,
      show_question_number: data.show_question_number == 1
    };

    this.isInterScreen = isInterblock(data);
    this.isStartScreen = isStartScreen(data);
    this.isEndScreen = isEndScreen(data);
    this.isTextScreen = isTextScreen(data);
    this.isShowNumber = isShowNumber(data);

    if (this.isInterScreen) {
      this.required = false;
    }

    this.disabled = this.hasStartScreen && this.isStartScreen;
  }
}
