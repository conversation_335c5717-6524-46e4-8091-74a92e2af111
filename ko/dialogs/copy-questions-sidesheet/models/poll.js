import { INTER_BLOCK } from 'Data/question-types';
import { QuestionModel } from './question';
import { Delay } from 'Utils/timer/delay';
import { linkValues } from 'Utils/link-values';

export class PollModel {
  constructor(id, config = {}) {
    this.id = id;

    this.loading = ko.observable(true);
    this.questions = ko.observableArray([]);
    this.name = ko.observable('');

    this.checked = ko.observable(false);

    this.query = config.query;
    this.hasStartScreen = config.hasStartScreen;
    this.filteredQuestions = ko.computed(() => {
      return this.questions().filter((question) => {
        return (question.name || question.type == INTER_BLOCK) && question.visible()
      });
    });

    this.load();
  }

  load() {
    let delay = Delay(400);
    return new Promise((res) => {
      $.ajax({
        url: `${APIConfig.baseApiUrlPath}poll/get-questions?id=${this.id}&access-token=${APIConfig.apiKey}`,
        success: (response) => {
          this.name(response.pollName);



          let questions = response.items.map(
            (q) => new QuestionModel(q, { query: this.query, hasStartScreen: this.hasStartScreen })
          );



          let counter = 1;
          questions.forEach((q) => {
            if (q.isShowNumber) q.index = counter++;
          });

          linkValues(
            this.checked,
            questions.filter(q => !q.disabled).map((q) => q.checked)
          );

          let list = [
            ...questions.filter(q => q.isStartScreen),
            ...questions.filter(q => !q.isStartScreen && !q.isEndScreen),
            ...questions.filter(q => q.isEndScreen)
          ];

          this.questions(list);
          delay.then(() => this.loading(false));
        }
      });
    });
  }

  getQuestions() {
    return this.filteredQuestions().filter(question => question.checked()).map(question => question.id);
  }
}
