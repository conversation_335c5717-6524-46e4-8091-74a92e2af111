import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './components/question';
import './components/poll';
import 'Components/input/select/polls-select';

ko.components.register('copy-questions-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('copy-questions-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
