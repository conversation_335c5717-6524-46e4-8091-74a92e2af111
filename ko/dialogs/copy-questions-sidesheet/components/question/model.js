import { getTypeLabel } from 'Data/question-types';
import { Translator } from '@/utils/translate';
export class ViewModel {
  constructor(params, element) {
    this.translator = Translator('questions');
    this.question = params.question;
    this.typeName = getTypeLabel(this.question.type);

    this.disabled = this.question.disabled;
    if (this.disabled) element.classList.add('disabled');

    this.questions = ko.toJS(params.questions);
    this.donorId = this.question.donorId;
    this.donorIndex = null;
    if (this.donorId) {
      const donor = this.questions.find(q => q.id === this.donorId);
      if (donor) this.donorIndex = donor.index;
    }
  }
}
