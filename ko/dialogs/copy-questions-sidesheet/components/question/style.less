@import 'Style/colors';

.copy-questions-question-block {
  display: block;

  padding-top: 12px;
  padding-bottom: 12px;

  border-bottom: 1px solid @f-color-border;

  &__indicator {
    width: 60px;
    padding-right: 15px;
    text-align: right;
    display: flex;
    justify-content: flex-end;

    .start-screen, .end-screen {
      color: #9BB0FB;
      font-size: 10px;
      font-weight: 700;
    }

    .text-screen {
      color: #9BB0FB;
      font-size: 19px;
      font-weight: 700;
    }

    .question-index {
      font-size: 19px;
      font-weight: 500;
      color: @f-color-text;
    }

    .question-point {
      margin-top: 6px;
    }
  }

  &__title {
    font-size: 19px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  &.disabled  {
    .copy-questions-question-block__indicator,
    .copy-questions-question-block__info {
      opacity: 0.5;
    }
  }

  &__type {
    color: @f-color-service;
    font-size: 12px;
    font-weight: 500;
  }

  &__recipient {
    font-weight: 400;
    color: var(--f-color-danger);
    margin-left: 10px;

    .fc-question {
      vertical-align: middle;
    }
  }
}
