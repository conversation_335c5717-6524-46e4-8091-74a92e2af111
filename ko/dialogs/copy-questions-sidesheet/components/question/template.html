

<div class="d-flex">
  <foquz-checkbox params="checked: question.checked, disabled: disabled" data-bind="style: {
    visibility: donorId ? 'hidden' : ''
  }"></foquz-checkbox>

  <div class="copy-questions-question-block__indicator">
    <!-- ko if: question.pointName -->
    <span class="question-point mr-5p" data-bind="tooltip, tooltipText: $component.translator.t('Вопрос связан с точкой контакта {point}', {point: question.pointName})">
      <svg-icon params="name: 'aim'" class="svg-icon--sm"></svg-icon>
    </span>
    <!-- /ko -->

    <!-- ko if: question.isInterScreen -->

      <!-- ko if: question.isStartScreen -->
      <span class="start-screen">start</span>
      <!-- /ko -->

      <!-- ko if: question.isEndScreen -->
      <span class="end-screen">end</span>
      <!-- /ko -->

      <!-- ko if: question.isTextScreen -->
        <!-- ko if: question.isShowNumber -->
        <span class="question-index" data-bind="text: question.index + '.'"></span>
        <!-- /ko -->
        <!-- ko ifnot: question.isShowNumber -->
        <span class="text-screen">*</span>
        <!-- /ko -->

      <!-- /ko -->

    <!-- /ko -->


    <!-- ko ifnot: question.isInterScreen -->
    <span class="question-index" data-bind="text: question.index + '.'"></span>
    <!-- /ko -->
  </div>

  <div class="copy-questions-question-block__info">
    <div  class="copy-questions-question-block__title">
      <span data-bind="html: question.name"></span>

      <!-- ko if: question.required -->
      <span class="copy-questions-question-block__required f-color-danger">*</span>
      <!-- /ko -->
    </div>
    <div  class="copy-questions-question-block__type" >
      <span data-bind="text: typeName"></span>
      <!-- ko if: donorId -->
      <span class="copy-questions-question-block__recipient">Реципиент вопроса <span data-bind="text: donorIndex"></span>
        <fc-question params="text: 'Вопрос-реципиент использует ответ респондента на вопрос ' + donorIndex"></fc-question>
      </span>
      <!-- /ko -->
    </div>
  </div>
</div>
