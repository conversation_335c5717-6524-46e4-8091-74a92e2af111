import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('copy-questions-question-block', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('copy-questions-question-block');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
