<!-- ko if: poll && poll.filteredQuestions().length -->
<div class="copy-questions-poll-block__wrapper"
     data-bind="let: { opened: ko.observable(true) }">
  <!-- ko if: poll.name -->
  <div class="d-flex copy-questions-poll-block__header">
    <foquz-checkbox params="checked: poll.checked"></foquz-checkbox>
    <div class="ml-2 d-flex align-items-center border-bottom flex-grow-1 pb-15p cursor-pointer" data-bind="click: function() { opened(!opened()); }">
      <div class="copy-questions-poll-block__title mr-3"
           data-bind="text: poll.name"></div>
      <foquz-icon params="icon: 'arrow-bottom'"
                  class="f-transform--transition"
                  data-bind="css: {
        'f-transform-rotate-180': !opened()
      }"></foquz-icon>
    </div>
  </div>

  <!-- /ko -->

  <!-- ko if: poll.loading -->
  <spinner></spinner>
  <!-- /ko -->

  <!-- ko ifnot: poll.loading -->
  <!-- ko template: {
    foreach: templateIf(opened(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400)
  } -->
  <div class="copy-questions-poll-block__questions">
    <!-- ko foreach: poll.filteredQuestions -->
    <copy-questions-question-block params="question: $data, questions: $parent.poll.questions"></copy-questions-question-block>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /ko -->
</div>
<!-- /ko -->
