import { DialogWrapper } from 'Dialogs/wrapper';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.submitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.submitted
    );

    this.promocode = params.promocode;

    this.loaded = ko.observable(false);
    this.pools = this.promocode.poolsList;
    this.coupons = this.promocode.reusableCouponsList;

    this.isPool = ko.observable(this.promocode.isPool() ? 1 : 0);
    this.name = ko.observable(this.promocode.poolId()).extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => !this.isPool()
      }
    });
    this.poolId = ko.observable(this.promocode.poolId()).extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.isPool()
      }
    });
  }

  submit() {
    this.submitted(true);
    if (!this.name.isValid() || !this.poolId.isValid()) return;

    if (this.isPool()) {
      this.promocode.setPoolId(this.poolId());
    } else {
      this.promocode.setPoolId(this.name());
    }

    this.emitEvent('submit');

    this.hide();
  }
}
