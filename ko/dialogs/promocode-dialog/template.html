<foquz-dialog params="ref: modal, dialogWrapper: $component">

  <foquz-dialog-header>
    Добавить промокод
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <div class="hat-radio-group form-control">
        <div class="hat-radio-group__radio">
          <input class="hat-radio-group__radio-input"
                 type="radio"
                 name="promo-type"
                 id="common-promo"
                 data-bind="checked: isPool, value: 0"
                 checked />
          <label class="hat-radio-group__radio-label"
                 for="common-promo">
            <span class="hat-radio-group__radio-indicator"></span>
            Один для всех
          </label>
        </div>

        <div class="hat-radio-group__radio">
          <input class="hat-radio-group__radio-input"
                 type="radio"
                 name="promo-type"
                 id="pool-promo"
                 data-bind="checked: isPool, value: 1" />
          <label class="hat-radio-group__radio-label"
                 for="pool-promo">
            <span class="hat-radio-group__radio-indicator"></span>
            Пул купонов
          </label>
        </div>
      </div>
    </div>
    <!-- ko ifnot: isPool -->
    <div class="form-group">
      <!-- ko if: coupons().length -->
      <div class="select2-wrapper"
           data-bind="css: {
              'is-invalid': formControlErrorStateMatcher(name),
          }">

        <select data-bind="value: name,
              valueAllowUnset: true,
              lazySelect2: {
                  containerCssClass: 'form-control',
                  wrapperCssClass: 'select2-container--form-control',
                  allowClear: true,
                  minimumResultsForSearch: 0,
                  dropdownAutoWidth: false,
              }"
                data-placeholder="Выберите многоразовый купон">
          <!-- ko foreach: coupons -->
          <option data-bind="text: fullTitle, value: id"></option>
          <!-- /ko -->
        </select>

        <!-- ko template: {
              foreach: formControlErrorStateMatcher(name),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
        <div class="form-error"
             data-bind="text: $parent.name.error()"></div>
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- ko ifnot: coupons().length -->
      <p>
        Нет данных для отображения.
        Для создания <b class="bold">многоразового купона</b> <a href="/foquz/foquz-contact?tab=pools"
           target="_blank">перейдите по ссылке</a>.
      </p>
      <!-- /ko -->
    </div>


    <!-- /ko -->
    <!-- ko if: isPool -->
    <div class="form-group">

      <!-- ko if: pools().length -->
      <div class="select2-wrapper"
           data-bind="css: {
              'is-invalid': formControlErrorStateMatcher(poolId),
          }">

        <select data-bind="value: poolId,
              valueAllowUnset: true,
              lazySelect2: {
                  containerCssClass: 'form-control',
                  wrapperCssClass: 'select2-container--form-control',
                  allowClear: true,
                  minimumResultsForSearch: 0,
              }"
                data-placeholder="Выберите пул купонов">
          <!-- ko foreach: pools -->
          <option data-bind="text: title, value: id"></option>
          <!-- /ko -->
        </select>

        <!-- ko template: {
              foreach: formControlErrorStateMatcher(poolId),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
        <div class="form-error"
             data-bind="text: $parent.poolId.error()"></div>
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- ko ifnot: pools().length -->
      <p>
        Нет данных для отображения.
        Для создания <b class="bold">пула купонов</b> <a href="/foquz/foquz-contact?tab=pools"
           target="_blank">перейдите по ссылке</a>.
      </p>
      <!-- /ko -->

    </div>
    <!-- /ko -->
  </div>


  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link px-2"
              data-bind="
              click: function() {
                hide();
              }">
        Отменить
      </button>
      <button type="button"
              class="f-btn f-btn-success"
              data-bind="
              click: function() {
                submit();
              }, disable: isPool() && !pools().length || !isPool() && !coupons().length">
        Добавить
      </button>
    </div>
  </div>


</foquz-dialog>
