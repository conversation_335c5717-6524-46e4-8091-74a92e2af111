import { DialogWrapper } from 'Dialogs/wrapper';

var IIKO_TYPE = '1';

class IikoBizServiceSubformViewModel {
  constructor(data) {
    this.formModel = ko.validatedObservable(null, {
      deep: true,
      live: true
    });

    let isService = false;
    if (data) {
      if (window.MODE == 'edit') {
        if (data.service == IIKO_TYPE) isService = true;
      } else {
        if (data.service == 0) isService = true;
      }
    }

    this.formModel({
      mainApiUrl: ko.observable(isService ? data.mainApiUrl : '').extend({
        required: {
          message: 'Обязательное поле'
        }
      }),
      additionalApiUrl: ko
        .observable(
          isService && data.additionalApiUrl ? data.additionalApiUrl : ''
        )
        .extend(),
      login: ko.observable(isService ? data.login : '').extend({
        required: {
          message: 'Обязательное поле'
        },
        minLength: {
          params: 2,
          message: 'Должно быть введено хотя бы 2 символа'
        },
        pattern: {
          params: /^\S*$/,
          message: 'Неверный формат параметра «Логин»'
        }
      }),
      password: ko.observable(isService ? data.password : '').extend({
        required: {
          message: 'Обязательное поле'
        }
      }),
      active: ko.observable(isService ? data.active : false)
    });

    this.reset = function () {
      this.formModel().mainApiUrl(isService ? data.mainApiUrl : '');
      this.formModel().additionalApiUrl(
        isService && data.additionalApiUrl ? data.additionalApiUrl : ''
      );
      this.formModel().login(isService ? data.login : '');
      this.formModel().password(isService ? data.password : '');
      this.formModel().active(isService ? data.active : '');
    };
  }
}

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    this.mode = params.mode;
    this.data = params.data;

    console.log("params", params)

    this.serviceTypes = params.services;

    this.isSubmitted = ko.observable(this.mode === 'edit');

    this.formModel = ko.validatedObservable({
      service: ko.observable(
        this.mode === 'edit' ? this.data.service.toString() : '1'
      ),
      name: ko.observable(this.mode === 'edit' ? this.data.name : '').extend({
        required: {
          message: 'Обязательное поле'
        }
      })
    });

    this.iikoBizServiceSubformViewModel = new IikoBizServiceSubformViewModel(
      this.mode === 'edit' ? this.data : null
    );

    this.isValid = ko.computed(() => {
      if (
        this.formModel().service() === '1' &&
        !this.iikoBizServiceSubformViewModel.formModel.isValid()
      ) {
        return false;
      }

      return this.formModel.isValid();
    });

    this.formControlErrorStateMatcher = function (formControl) {
      return ko.computed(() => {
        if ('isValid' in formControl) {
          return this.isSubmitted() && !formControl.isValid();
        } else {
          return false;
        }
      });
    };

    this.formControlSuccessStateMatcher = function (formControl) {
      return ko.computed(() => {
        if ('isValid' in formControl) {
          return this.isSubmitted() && formControl.isValid();
        } else {
          return this.isSubmitted();
        }
      });
    };
  }

  cancel() {
    if (this.mode === 'create') {
      this.isSubmitted(false);
      this.formModel().service('1');
      this.formModel().name('');
      this.iikoBizServiceSubformViewModel.reset();
    } else {
      this.formModel().service(this.data.service.toString());
      this.formModel().name(this.data.name);
      this.iikoBizServiceSubformViewModel.reset();
    }
  }

  submit() {
    this.isSubmitted(true);

    if (this.isValid()) {
      const data = {
        service_type_id: +this.formModel().service(),
        name: this.formModel().name()
      };

      if (this.formModel().service() === '1') {
        data.url = this.iikoBizServiceSubformViewModel.formModel().mainApiUrl();
        if (
          this.iikoBizServiceSubformViewModel.formModel().additionalApiUrl() !==
          ''
        ) {
          data.url2 = this.iikoBizServiceSubformViewModel
            .formModel()
            .additionalApiUrl();
        } else {
          data.url2 = null;
        }
        data.username = this.iikoBizServiceSubformViewModel.formModel().login();
        data.password = this.iikoBizServiceSubformViewModel
          .formModel()
          .password();
        data.is_active =
          this.iikoBizServiceSubformViewModel.formModel().active() == true
            ? 1
            : 0;
      }
      let urlAjax =
        this.mode == 'create'
          ? '/foquz/api/company-access/create?access-token=' +
            APIConfig.apiKey +
            '&companyId=' +
            $.urlParam('id')
          : '/foquz/api/company-access/update?access-token=' +
            APIConfig.apiKey +
            '&id=' +
            this.data.id;
      $.ajax({
        type: 'POST',
        url: urlAjax,
        data: { CompanyIikoAccess: data },
        success: function (id) {
          data.id = id;
          data.mainApiUrl = data.url;
          data.additionalApiUrl = data.url2;
          data.login = data.username;
          data.active = data.is_active;
          data.service = data.service_type_id;

          this.emitEvent('submit', data);
          this.hide();
        }
      });
    }
  }
}
