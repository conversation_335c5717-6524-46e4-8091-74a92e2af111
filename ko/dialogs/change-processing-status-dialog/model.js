import { DialogWrapper } from 'Dialogs/wrapper';
import {
  PROCESSING_DELAYED,
  PROCESSING_DONE,
  PROCESSING_NEW,
  PROCESSING_PROCESS,
  PROCESSING_WORK
} from 'Data/processing-statuses';
import { dateValidator } from '../../utils/validation/date';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.title = params.title || 'Изменение статуса';

    this.loading = ko.observable(false);
    this.isExecutor = params.isExecutor;

    this.currentStatus = params.currentStatus;
    this.selectedStatus = ko.observable(params.selectedStatus);

    this.isModeratorRequired = ko.observable(!this.isExecutor);
    this.moderator = ko.observable(CURRENT_USER.id).extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.isModeratorRequired()
      }
    });

    this.isExecutorRequired = ko.computed(
      () => this.selectedStatus() == PROCESSING_WORK
    );

    this.executor = ko.observable('').extend({
      requred: {
        message: 'Обязательное поле',
        onlyIf: () => this.isExecutorRequired()
      }
    });

    this.isDelayedToRequired = ko.computed(
      () => this.selectedStatus() == PROCESSING_DELAYED
    );

    this.delayedTo = ko.observable('').extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.isDelayedToRequired()
      },
      validation: {
        validator: dateValidator(),
        message: 'Некорректный формат'
      }
    });

    this.comment = ko.observable('');

    this.formModel = ko.validatedObservable(
      {
        status: this.selectedStatus,
        moderator: this.moderator,
        executor: this.executor,
        delayedTo: this.delayedTo
      },
      { deep: true, live: true }
    );

    this.statuses = [
      {
        id: PROCESSING_NEW,
        disabled: this.isStatusDisabled(0),
        text: 'Новый',
        color: 'mint'
      },
      {
        id: PROCESSING_PROCESS,
        disabled: this.isStatusDisabled(1),
        text: 'В процессе',
        color: 'blue'
      },
      {
        id: PROCESSING_WORK,
        disabled: this.isStatusDisabled(4),
        text: 'Обрабатывается исполнителем',
        color: 'gold'
      },
      {
        id: PROCESSING_DELAYED,
        disabled: this.isStatusDisabled(2),
        text: 'Отложена',
        color: 'red'
      },
      {
        id: PROCESSING_DONE,
        disabled: this.isStatusDisabled(3),
        text: 'Обработана',
        color: 'violet'
      }
    ];
  }

  getModerators(params, success) {
    $.ajax({
      url: ApiUrl('answers/moderators'),
      success: (response) => {
        success(response.items);
      }
    });
  }

  getExecutors(params, success) {
    $.ajax({
      url: ApiUrl('answers/executors'),
      success: (response) => {
        success(response.items);
      }
    });
  }

  getParams() {
    let status = this.selectedStatus();
    let params = {
      comment: this.comment(),
      status,
      moderator_id: this.moderator()
    };

    if (this.isExecutorRequired()) params.executor_id = this.executor();
    if (this.isDelayedToRequired()) params.delayed_up = this.delayedTo();

    return params;
  }

  submit() {
    this.isSubmitted(true);
    if (!this.formModel.isValid()) return;

    this.isSubmitted(false);
    this.loading(true);

    this.emitEvent('dialog.submit', this.getParams());
  }

  isStatusDisabled(status) {
    console.log('is disabled', status, this.isExecutor, this.currentStatus);
    if (this.isExecutor) {
      return !(this.currentStatus == 4 && status == 3);
    }
    switch (this.currentStatus) {
      case 0:
        return false;
      case 1:
      case 2:
      case 4:
        return status === 0;
      case 3:
        return [0, 1, 2].includes(status);
      default:
        return false;
    }
  }
}
