<foquz-dialog params="ref: modal, dialogWrapper: $component">

  <foquz-dialog-header>
    <!-- ko text: $parent.title -->
    <!-- /ko -->
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <fc-label params="text:_t('answers', 'Новый статус') "></fc-label>
      <fc-select params="value: selectedStatus, options: statuses">

        <template data-slot="option">
          <!-- ko if: $data.disabled -->
          <span class="f-color-service"
                style="opacity: 0.6"
                data-bind="text: $data.text"></span>
          <!-- /ko -->
          <!-- ko ifnot: $data.disabled -->
          <span data-bind="class: 'f-color-' + $data.color, text: $data.text"></span>
          <!-- /ko -->
        </template>
        <template data-slot="result">
          <!-- ko if: $data.disabled -->
          <span class="f-color-service"
                style="opacity: 0.6"
                data-bind="text: $data.text"></span>
          <!-- /ko -->
          <!-- ko ifnot: $data.disabled -->
          <span data-bind="class: 'f-color-' + $data.color, text: $data.text"></span>
          <!-- /ko -->
        </template>

      </fc-select>
    </div>

    <!-- ko if: isModeratorRequired -->
    <div class="form-group">
      <fc-label params="text: _t('answers', 'Модератор') "></fc-label>
      <fc-select params="value: moderator, options: getModerators, placeholder: 'Выберите модератора',
          invalid: formControlErrorStateMatcher(moderator), fields: { text: 'name'},"></fc-select>
      <fc-error params="show: formControlErrorStateMatcher(moderator), text: moderator.error"></fc-error>
    </div>
    <!-- /ko -->

    <!-- ko if: isExecutorRequired -->
    <div class="form-group">
      <fc-label params="text: _t('answers', 'Исполнитель') "></fc-label>
      <fc-select params="value: executor, options: getExecutors, placeholder: 'Выберите исполнителя',
          invalid: formControlErrorStateMatcher(executor), fields: { text: 'name'},">
      </fc-select>
      <fc-error params="show: formControlErrorStateMatcher(executor), text: executor.error"></fc-error>
    </div>
    <!-- /ko -->

    <!-- ko if: isDelayedToRequired -->
    <div class="form-group">
      <fc-label params="text: 'Отложить до'"></fc-label>
      <date-picker params="value: delayedTo, invalid: formControlErrorStateMatcher(delayedTo)"></date-picker>
      <fc-error params="show: formControlErrorStateMatcher(delayedTo), text: delayedTo.error"></fc-error>
    </div>
    <!-- /ko -->

    <div class="form-group">
      <label class="form-label">Комментарий</label>

      <div class="chars-counter chars-counter--type_textarea"
           data-bind="charsCounter, charsCounterCount: comment().length">
        <textarea class="form-control"
                  maxlength="500"
                  data-bind="textInput: comment"></textarea>

        <div class="chars-counter__value"></div>
      </div>
    </div>
  </div>


  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link px-2"
              data-bind="
              click: function() {
                hide();
              }">
        Отменить
      </button>
      <button type="button"
              class="f-btn f-btn-success"
              data-bind="
              click: function() {
                submit();
              }">
        Сохранить
      </button>
    </div>
  </div>


</foquz-dialog>
