import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('change-processing-status-dialog', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('change-processing-status-dialog');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
