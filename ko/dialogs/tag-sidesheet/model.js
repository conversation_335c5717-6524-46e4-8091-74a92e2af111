import { DialogWrapper } from 'Dialogs/wrapper';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    function createTag(tagData) {
      return new Promise((res, rej) => {
        $.ajax({
          method: 'POST',
          url:
            window.APIConfig.baseApiUrlPath +
            'contact-tags/create?access-token=' +
            window.APIConfig.apiKey,
          data: tagData,
          success: function (data) {
            res(data);
          },
          error: function (response) {
            rej(response.responseJSON);
          }
        });
      });
    }

    function editTag(id, tagData) {
      return new Promise((res, rej) => {
        $.ajax({
          method: 'PUT',
          url:
            window.APIConfig.baseApiUrlPath +
            'contact-tags/update?access-token=' +
            window.APIConfig.apiKey +
            '&id=' +
            id,
          data: tagData,
          success: function (data) {
            res(data);
          },
          error: function (response) {
            rej(response.responseJSON);
          }
        });
      });
    }

    this.initializing = ko.observable(true);

    this.onInit = function () {
      this.initializing(false);
    };

    this.submitted = ko.observable(false);

    this.tag = params.data.tag;
    this.isNew = !this.tag;

    this.serverErrors = {
      name: ko.observable('')
    };

    this.name = ko.observable(this.tag ? this.tag.name() : '').extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: {
        validator: () => false,
        onlyIf: () => this.serverErrors.name(),
        message: () => this.serverErrors.name()
      }
    });
    this.name.subscribe((_) => this.serverErrors.name(''));

    this.autoAdd = ko.observable(this.tag ? this.tag.autoAdd() : false);
    this.conditions = ko.observableArray([]);
    this.isConditionsValid = ko.pureComputed(() => {
      return !this.conditions().some((c) => !c.model.isValid());
    });
    this.noConditionsError = ko.observable('');

    this.isValid = ko.pureComputed(() => {
      if (!this.name.isValid()) return false;
      if (this.autoAdd()) {
        if (this.conditions().length == 0) {
          this.noConditionsError(
            'Для автоматического добавления тега обязательно нужно выбрать хотя бы одно условие'
          );
          return false;
        }

        if (!this.isConditionsValid()) return false;
      }

      return true;
    });

    this.removeTag = function () {
      this.tag.remove().then(() => this.hide());
    };

    this.cancel = function () {
      this.hide();
    };

    this.submit = () => {
      this.noConditionsError('');
      this.submitted(true);
      if (!this.isValid()) return;

      const data = {
        name: this.name(),
        auto_add: this.autoAdd() ? 1 : 0,
        conditions: this.conditions().map((c) => c.getData())
      };
      if (this.isNew) {
        createTag(data)
          .then((tagData) => {
            this.emitEvent('create', tagData);
            this.hide();
          })
          .catch((response) => {
            if (response.errors) {
              Object.keys(response.errors).forEach((key) => {
                if (key in this.serverErrors) {
                  this.serverErrors[key](response.errors[key]);
                }
              });
            }
          });
      } else {
        editTag(this.tag.id, data)
          .then((tagData) => {
            this.emitEvent('update', tagData);
            this.hide();
          })
          .catch((response) => {
            if (response.errors) {
              Object.keys(response.errors).forEach((key) => {
                if (key in this.serverErrors) {
                  this.serverErrors[key](response.errors[key]);
                }
              });
            }
          });
      }
    };
  }
}
