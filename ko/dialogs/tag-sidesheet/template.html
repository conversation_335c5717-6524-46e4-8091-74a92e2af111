<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title"
          data-bind="text: isNew ? 'Новый тег' : 'Редактировать тег'">

      </h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container">
        <div class="row">

          <div class="col col-12 col-lg-6">
            <div class="form-group">
              <label class="form-label"
                     for="tag-details-modal-name">Название</label>
              <button type="button"
                      class="btn-question"
                      title="Название"
                      data-bind="tooltip"></button>

              <input class="form-control"
                     id="tag-details-modal-name"
                     data-bind="textInput: name, css: {
             'is-invalid': submitted() && !name.isValid(),
             'is-valid': submitted() && name.isValid()
           }">

              <!-- ko template: {
        foreach: submitted() && !name.isValid(),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200),
        } -->
              <div class="form-error"
                   data-bind="text: $parent.name.error()"></div>
              <!-- /ko -->
            </div>
          </div>


        </div>

        <div class="row">
          <div class="col col-12">
            <div class="form-group">
              <div class="d-flex align-items-center">
                <label class="switch mr-3">
                  <input type="checkbox"
                         id="tag-details-modal-auto-add"
                         data-bind="checked: autoAdd"
                         checked>
                  <span class="switch__slider"></span>
                </label>
                <label for="tag-details-modal-auto-add"
                       class="switch__label">Автоматическое добавление</label>
              </div>

              <div class="service-text mt-4">
                Тег с условием автоматически проходится по текущему разделу Контакты и добавляется к ним с частотой один
                раз в сутки
              </div>
            </div>
          </div>
        </div>

        <!-- ko template: {
        foreach: autoAdd,
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
        <div class="mt-2">
          <!-- ko component: {
        name: 'conditions-list',
        params: {
            title: 'Условия',
            subtitle: 'Добавление условий для применения тега к контактам',
            error: $parent.noConditionsError,
            showErrors: $parent.submitted,
            conditions: $parent.conditions,
            data: $parent.tag ? $parent.tag.conditions() : null
        }

        } -->
          <!-- /ko -->
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
       <!-- ko ifnot: isNew -->
       <button type="button" class="f-btn f-btn-danger" data-bind="click: function() { removeTag(); }">
        <span class="f-btn-prepend">
            <svg-icon params="name: 'times'" class="svg-icon--sm"></svg-icon>
        </span>
        Удалить
    </button>
    <!-- /ko -->

      <button type="button"
              class="f-btn"
              data-bind="click: function() {
                $dialog.hide();
              }">
        <foquz-icon params="icon: 'bin'"
                    class="f-btn-prepend"></foquz-icon>
        Отменить
      </button>
      <button type="submit"
              class="f-btn f-btn-success"
              data-bind="click: function() { submit()  }">
        <foquz-icon params="icon: 'save'"
                    class="f-btn-prepend"></foquz-icon>
        Сохранить
      </button>
    </div>
  </div>

</sidesheet>
