import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import '../client-sidesheet/client-card';

import 'Components/client-history';


ko.components.register('feedback-review-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('client-sidesheet');
      element.classList.add('edit-client-sidesheet');
      element.classList.add('feedback-review-sidesheet');

      return new ViewModel(params, element);
    }
  },
  template: html
});
