export function ViewModel(params, element) {

  element.classList.add("review-data");

  const review = ko.toJS(params.review);

  const sendAt = review.createdAt;
  let processedAt = null;

  if (review.processing) {
    const { updatedAt, createdAt } = review.processing;
    processedAt = updatedAt || createdAt;
  }

  return {
    review: review,
    hideClientData: params.hideClientData,
    sendAt,
    processedAt,
    client: review.client,
    formatDate(date) {
      if (!date) return "";
      return date
        .split(" ")
        .map((i) => `<span>${i}</span>`)
        .join(" ");
    },
  };
}
