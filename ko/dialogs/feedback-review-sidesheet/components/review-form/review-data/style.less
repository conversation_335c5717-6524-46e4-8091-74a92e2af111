@import "Style/colors";
@import "Style/breakpoints";

// Исправление конфликта стилей
.feedback-review-sidesheet__info-block--dates {
  max-width: 158px;
}

.review-data {
  display: block;

  .review-date {
    .svg-icon {
      flex-shrink: 0;
      margin-right: 8px;
      vertical-align: middle;
    }
    & > span {
      display: flex;
      align-items: center;
    }
  }
  .client-info {
    .badge {
      align-self: flex-start;
      margin-right: 12px;
    }
  }
  .review-lang {
    font-size: 12px;
    font-weight: 500;
    color: #73808d;
  }

  .review-details-modal__info {
    flex-wrap: nowrap;

    &-block {
      &--dates {
        padding-top: 10px;
        padding-bottom: 10px;
      }
      &--client--call {
        max-width: 95px;
      }
    }
  }

  .only-mobile({
    .review-date {
      flex-grow: 1;
      flex-basis: 50%;
      align-self: flex-start;
      display: block;

      .svg-icon {
        margin-bottom: 0px;
      }
      .date {
       display: block;
      }
    }
    .review-date > span {
      flex-direction: row;
      
    }
    .badge {
      margin-top: 2px;
    }

  });

  @media screen and (max-width: 991px) {
  }
}
