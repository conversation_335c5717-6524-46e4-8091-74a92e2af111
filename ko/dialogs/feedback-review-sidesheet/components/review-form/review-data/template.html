<!-- ko if: review -->

<div class="review-details-modal__info d-none d-md-flex">
  <!-- ko if: client -->
  <!-- Данные клиента -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--client"
  >
    <div class="client-info">
      <!-- ko ifnot: hideClientData -->
      <div class="client-info__title d-flex aling-items-center">
        <span class="client-info__name" data-bind="text: client.name"></span>
        <span
          class="ml-2 badge badge-active client-info__order-count"
          data-bind="tooltip, text: client.ordersCount, tooltipText: _t('answers', 'Количество заказов')"
        ></span>
      </div>

      <!-- ko if: client.phone || client.systemFields.email -->
      <div class="client-info__phone mt-2 f-fs-2">
        <!-- ko if: client.phone -->
        <span
          class="font-weight-700"
          data-bind="text: client.phone + (client.systemFields.email ? ',' : '')"
        >
        </span>
        <!-- /ko -->

        <!-- ko if: client.systemFields.email -->
        <span data-bind="text: client.systemFields.email"></span>
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- /ko -->

      <!-- ko if: review.filial -->
      <div class="service-text bold mt-2" data-bind="text: review.filial"></div>
      <!-- /ko -->

      <fc-client-fields
        class="mt-10p"
        params="client: client, hideClientData: hideClientData"
      ></fc-client-fields>
    </div>
  </div>

  <!-- /Данные клиента -->
  <!-- /ko -->

  <!-- ko if: !review.isAuto && review.filial -->
  <!-- Данные клиента -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--client justify-content-start"
  >
    <div class="client-info">
      <div class="service-text bold" data-bind="text: review.filial"></div>
    </div>
  </div>
  <!-- /Данные клиента -->
  <!-- /ko -->

  <!-- ko if: client && client.phone -->
  <!-- Позвонить клиенту -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--client--call"
  >
    <a
      class="btn call-block"
      data-bind="attr: { href: 'tel:' + client.phone }, tooltip, tooltipText: _t('answers',  'Позвонить клиенту')"
    ></a>
  </div>
  <!-- /Позвонить клиенту -->
  <!-- /ko -->

  <!-- Даты -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--dates feedback-review-sidesheet__info-block--dates"
  >
    <div class="review-date">
      <span
        data-bind="tooltip, tooltipText: _t('answers', 'Дата отправки сообщения')"
      >
        <svg-icon
          params="name: 'review-passed'"
          class="svg-icon--sm f-color-service"
        ></svg-icon>

        <span class="date" data-bind="html: formatDate(sendAt)"></span>
      </span>
    </div>

    <!-- ko if: processedAt -->
    <div class="review-date">
      <span data-bind="tooltip, tooltipText: _t('answers', 'Дата обработки')">
        <svg-icon
          params="name: 'review-processed'"
          class="svg-icon--sm f-color-service"
        ></svg-icon>
        <span class="date" data-bind="html: formatDate(processedAt)"></span>
      </span>
    </div>
    <!-- /ko -->
  </div>
  <!-- /Даты -->
</div>

<div class="review-details-modal__info d-md-none">
  <div
    class="review-details-modal__info-block review-details-modal__info-block--full"
  >
    <div class="d-flex align-items-center">
      <div class="client-info flex-grow-1">
        <div class="client-info__title d-flex aling-items-center">
          <span class="client-info__name" data-bind="text: client.name"></span>
          <!-- ko if: client.name || client.ordersCount > 0 -->
          <span
            class="ml-2 badge badge-active client-info__order-count"
            data-bind="tooltip, text: client.ordersCount, tooltipText: _t('answers', 'Количество заказов')"
          ></span>
          <!-- /ko -->
        </div>
      </div>
    </div>

    <!-- ko if: client.systemFields.email -->
    <div
      class="client-info__phone mt-1"
      data-bind="text: client.systemFields.email"
    ></div>
    <!-- /ko -->

    <fc-client-fields
      class="mt-15p mb-15p"
      params="client: client"
    ></fc-client-fields>

    <div class="d-flex justify-content-between mt-0 review-dates">
      <div class="review-date">
        <span
          data-bind="tooltip, tooltipText: _t('answers', 'Дата отправки сообщения')"
          class="d-flex"
        >
          <svg-icon
            params="name: 'review-passed'"
            class="svg-icon--sm"
          ></svg-icon>
          <span>
            <span class="date" data-bind="html: formatDate(sendAt)"></span>
          </span>
        </span>
      </div>

      <!-- ko if: processedAt -->
      <div class="review-date">
        <span data-bind="tooltip, tooltipText: _t('answers', 'Дата обработки')">
          <svg-icon
            params="name: 'review-processed'"
            class="svg-icon--sm"
          ></svg-icon>
          <span class="date" data-bind="html: formatDate(processedAt)"></span>
        </span>
      </div>
      <!-- /ko -->
    </div>
  </div>
</div>

<!-- ko if: client && client.phone -->
<div class="d-md-none">
  <!-- Позвонить клиенту -->
  <a
    class="f-bg-success call-button"
    data-bind="attr: { href: 'tel:' + client.phone }, tooltip, tooltipText: _t('Позвонить клиенту')"
  >
    <span class="call-block"></span>
    <span data-bind="text: client.phone"></span>
  </a>
  <!-- /Позвонить клиенту -->
</div>
<!-- /ko -->

<!-- /ko -->
