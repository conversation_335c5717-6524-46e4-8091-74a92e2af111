<!-- ko if: loading -->
<div class="container">
  <fc-spinner class="f-color-primary"></fc-spinner>
</div>

<!-- /ko -->

<!-- ko ifnot: loading -->
<div class="flex-grow-1">
  <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
    <div class="container">
      <!-- ko if: review -->

      <feedback-review-meta
        params="review: review, hideClientData: hideClientData"
        class="mb-30p"
      ></feedback-review-meta>

      <div>
        <h2 class="f-h2 pb-0 mb-25p">Сообщение</h2>

        <feedback-review-message
          params="review: review"
        ></feedback-review-message>
      </div>

      <!-- ko if: review().processing -->
      <hr />
      <feedback-review-processing
        params="review: review, ref: processingForm, showErrors: isSubmitted"
      ></feedback-review-processing>
      <!-- /ko -->
      <!-- /ko -->

      <!-- ko ifnot: review -->
      <div class="service-text text-center p-4">
        Не удалось загрузить данные отзыва
      </div>
      <!-- /ko -->
    </div>
  </div>
</div>



<!-- ko if: review().processing -->
<div class="foquz-dialog__footer fixed-footer fixed">
  <div class="foquz-dialog__actions">
    <button
      type="button"
      class="f-btn"
      data-bind="click: function() {
                reset();
              }"
    >
      <foquz-icon params="icon: 'bin'" class="f-btn-prepend"></foquz-icon>
      <span data-bind="text: _t('Отменить')"></span>
    </button>
    <button
      type="submit"
      class="f-btn f-btn-success"
      data-bind="click: function() { submit()  }"
    >
      <foquz-icon params="icon: 'save'" class="f-btn-prepend"></foquz-icon>
      <span data-bind="text: _t('Сохранить')"></span>
    </button>
  </div>
  <fc-success params="show: showSuccessMessage"></fc-success>
</div>
<!-- /ko -->
<!-- /ko -->
