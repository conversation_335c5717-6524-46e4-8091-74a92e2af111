import { FeedbackReview } from '@/entities/models/review/types';

interface ReviewMessageProps {
    review: FeedbackReview
}

export function ViewModel(params: ReviewMessageProps, element: HTMLElement) {
    element.classList.add('fc-review-feedback-message')
    const review = ko.toJS(params.review);
    const { name, text, phone, files } = review.message;

    return {
        name,
        text,
        phone,
        files,
        gallery: files.map(url => ({ src: url }))
    }
}