import { FeedbackReviewModel } from "@/entities/models/review/feedback-review";
import { getFeedbackReview } from "../../api/getFeedbackReview";

import * as ReviewData from "./review-data";
import * as ReviewMessage from "./review-message";
import { ProcessingForm } from "@/presentation/views/fc-processing-form";

import { registerComponent } from "@/utils/engine/register-component";
import { Processing } from "@/entities/models/processing/types";
import { SaveReviewProcessing } from "../../api/processing";
import { ProcessingModel } from "@/entities/models/processing/poll";
import { ReviewProcessingEvent } from "@/utils/events/review";

registerComponent("feedback-review-meta", ReviewData);
registerComponent("feedback-review-message", ReviewMessage);
registerComponent("feedback-review-processing", ProcessingForm);

interface ReviewFormProps {
  reviewId: number;
  hideClientData: boolean;
}

export function ViewModel(params: ReviewFormProps, element: HTMLElement) {
  element.classList.add("fc-feedback-review-form");

  const isSubmitted = ko.observable(false);
  const pending = ko.observable(false);

  const { reviewId, hideClientData } = params;

  const loading = ko.observable(true);
  const review = ko.observable(null);
  const processingForm = ko.observable(null);
  const showSuccessMessage = ko.observable(false);

  getFeedbackReview(reviewId).then((data) => {
    const model = FeedbackReviewModel(data);
    review(model);
    ReviewProcessingEvent.emit({
      id: reviewId,
      processingStatus: model.processing?.status,
      data: model,
    });
    loading(false);
  });

  return {
    isSubmitted,
    pending,

    hideClientData,

    loading,
    review,
    processingForm,

    showSuccessMessage,

    reset() {
      const form = processingForm();
      form?.cancel();
    },

    submit() {
      const form = processingForm();
      if (form) {
        isSubmitted(true);

        if (!form.isValid()) {
          console.warn("Invalid form");
          return;
        }
        pending(true);
        const { id, ...data } = form.getData();

        SaveReviewProcessing(id, data)
          .then((response) => {
            const model = ProcessingModel(response);
            console.log({ model, response });
            form.update(model);
            pending(false);
            isSubmitted(false);
            showSuccessMessage(true);

            ReviewProcessingEvent.emit({
              id: reviewId,
              processingStatus: model.status,
              data: model,
            });
          })
          .catch((err) => {
            console.error(err);
          });
      }
    },
  };
}
