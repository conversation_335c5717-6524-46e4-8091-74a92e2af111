import { request } from "@/utils/api/request";
import { FeedbackReviewVars } from "@/entities/models/review/types";
import { ChannelNames } from "@/constants/channels";

export async function getFeedbackReview(reviewId: number) {
  const response = await request<FeedbackReviewVars>(
    "/foquz/api/contact/feedback",
    {
      method: "GET",
      params: {
        id: reviewId,
      },
    }
  );

  // response.data.contact_id = 11294;
  // response.data.processing = {
  //   id: 51705,
  //   poll_answer_id: null,
  //   status: 1,
  //   executor_id: null,
  //   process_up: null,
  //   delayed_up: null,
  //   reason_id: null,
  //   employee_id: null,
  //   fine_id: null,
  //   compensation_id: null,
  //   comment: null,
  //   updated_at: null,
  //   notification_script_id: 43,
  //   moderator_id: 556,
  //   executor_comment: null,
  //   fine_amount: null,
  //   resolved_at: null,
  //   created_at: "2022-12-19 10:45:31",
  //   request_project_answer_id: null,
  //   custom_fields: null,
  //   company_feedback_id: 148,
  //   answerNotifications: [
  //     {
  //       id: 0,
  //       name: "SMS / Шаблон #1",
  //       channel_name: ChannelNames.SMS,
  //       //delay: "",
  //       //delay_days: null,
  //       sender: "HATIMAKI",
  //       sender_name: null,
  //       //image: null,
  //       subject: null,
  //       text: "{QR}{Дата}",
  //       pool_id: null,
  //       answer_processing_id: 1,
  //       //default_type: 1,
  //       //default_channel_id: 245,
  //     },
  //   ],
  //   notificationScript: null,
  //   statusName: 32133,
  //   moderatorName: null,
  //   executorName: null,
  //   reasonName: null,
  //   employeeName: null,
  //   fineName: null,
  //   fineType: null,
  //   compensationName: null,
  //   processingFiles: [],
  // };

  return response.data;
}
