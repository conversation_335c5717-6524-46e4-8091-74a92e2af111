import { ProcessingVars } from "@/entities/models/processing/types";
import { request } from "@/utils/api/request";
const fields = {
  moderatorId: "moderator_id",
  executorId: "executor_id",
  processUp: "process_up",
  delayedUp: "delayed_up",
  reasonId: "reason_id",
  employeeId: "employee_id",
  fineId: "fine_id",
  fineAmount: "fine_amount",
  compensationId: "compensation_id",
  executorComment: "executor_comment",
  notificationScriptId: "notification_script_id",
};

function handleData({ processing, notifications }) {
  const params: {
    [key: string]: any;
  } = {};
  Object.entries(processing).forEach(([key, value]) => {
    const fieldName = fields[key] || key;
    params[fieldName] = value;
  });
  params.answerNotifications = notifications;

  return params;
}

export async function SaveReviewProcessing(processingId, processingData) {
  const params = handleData(processingData);
  console.log({ processingData, params })

  const { error, data } = await request<ProcessingVars>("/foquz/api/company-feedback/processing", {
    params: {
      id: processingId,
    },
    method: "PUT",
    body: params,
  });

  if (error) throw new Error(error)

  return data;
}
