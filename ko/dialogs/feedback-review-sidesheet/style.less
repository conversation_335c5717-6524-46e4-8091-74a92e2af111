.feedback-review-sidesheet {
  .review-form {
    .os-content > .container {
      min-height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .processing-line {
    height: 4px;
    position: relative;
    z-index: 10;

    &[data-status='0'] {
      background-color: #16CEB9;
    }
    &[data-status='1'] {
      background-color: #2D99FF;
    }
    &[data-status='2'] {
      background-color: #F96261;
    }
    &[data-status='3'] {
      background-color: #8400FF;
    }
    &[data-status='4'] {
      background-color: #CAAD46;
    }
  }

  .nav-tabs {
    height: 48px;
    background-color: #dfe3e5;

    .nav-item {
      height: 48px;
      width: 33%;
      &:not(:last-child) {
        border-right: 1px solid white;
      }
    }
  }

  @media screen and (max-width: 1199px) {
    .foquz-dialog__close {
      width: 48px;
      height: 48px;
    }

    .nav-tabs {
      padding-right: 48px;

      .nav-item {
        width: auto;
        &:last-child {
          border-right: 1px solid white;
        }
        &--review {
          flex-grow: 1;
        }
      }
    }

    .review-details-modal__info-block {
      border-radius: 8px;
    }
  }

  @media screen and (max-width: 767px) {
    .review-details-modal__info-block {
      border-radius: 0px;
    }
  }
}