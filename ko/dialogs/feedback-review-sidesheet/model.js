import { DialogWrapper } from "Dialogs/wrapper";
import { ClientCard } from "../client-sidesheet/models/client-card";

import "Dialogs/review-sidesheet";

import * as ReviewForm from "./components/review-form";
import { registerComponent } from "@/utils/engine/register-component";
import { ReviewProcessingEvent } from "../../utils/events/review";

registerComponent("fc-feedback-review-form", ReviewForm);

const loadReviewSidesheet = (function () {
  let promise = null;

  return function () {
    return Promise.resolve();

    if (!promise)
      promise = new Promise((res) => {
        import("Dialogs/review-sidesheet").then((mod) => {});
      });
    return promise;
  };
})();
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.title = _t("Контакт");

    this.blocked = !!window.CURRENT_USER.blockActions;

    const review = params.data.review;
    this.hideClientData = params.data.hideClientData;

    this.review = review;
    this.activeTab = ko.observable("review");
    this.processingStatus = ko.observable();

    ReviewProcessingEvent.on((e) => {
      if (e.id === this.review.id) {
        this.processingStatus(e.processingStatus);
      }
    });

    this.clientId = review.clientId;

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.card = new ClientCard(this.clientId);

    this.history = ko.observable(null);
    this.historyOpened = ko.observable(false);

    this.openHistory = () => {
      this.historyOpened(true);
      this.history().load(this.clientId);
    };

    this.closeHistory = () => {
      this.historyOpened(false);
      this.history().reset();
    };

    this.activeTab.subscribe((v) => {
      if (v === "history") {
        this.openHistory();
      } else {
        this.closeHistory();
      }
    });

    this._onSubmit = params.onSubmit;
  }

  openReview(reviewId) {
    loadReviewSidesheet().then(() => {
      this.dialogRoot.add({
        name: "review-sidesheet",
        params: { reviewId, hideClientData: this.hideClientData },
      });
    });
  }

  submit() {
    this.isSubmitted(true);
    if (!this.card.isValid()) return;

    let params = ko.toJSON(this.card.getData());

    $.ajax({
      url: "/foquz/foquz-contact/update?id=" + this.clientId,
      method: "POST",
      data: params,
      success: (response) => {
        if (response.success) {
          this.hide();
          if (typeof this._onSubmit == "function") this._onSubmit();
        } else if (response.errors instanceof Array) {
          this.card.setErrors(response.errors);
        }
      },
    });
  }
}
