<sidesheet
  params="ref: modal, dialogWrapper: $component"
  class="review-details-modal review-sidesheet feedback-review-sidesheet"
>
  <!-- ko if: processingStatus() !== undefined -->
  <div
    class="processing-line"
    data-bind="attr: {
    'data-status': processingStatus
  }"
  ></div>
  <!-- /ko -->

  <nav class="nav nav-tabs review-details-modal-nav flex-shrink-0">
    <a
      class="nav-item nav-link active nav-item--review"
      id="review-form-tab"
      data-toggle="tab"
      role="tab"
      href="#review-form"
      aria-controls="review-form"
      data-bind="
        event: {
          'show.bs.tab': function() {
            activeTab('review');
            return true;
          }
        }, 
        "
    >
      <fc-icon params="name: 'message'" class="mr-15p"></fc-icon>
      <!-- ko if: review.theme -->
      <span
        data-bind="text: review.theme.name, style: {
          color: review.theme.color
        }"
      ></span>
      <!-- /ko -->
    </a>

    <a
      class="nav-item nav-link nav-item--client"
      id="client-form-tab"
      data-toggle="tab"
      role="tab"
      href="#client-form"
      aria-controls="client-form"
      data-bind="
         event: {
           'show.bs.tab': function() {
            activeTab('client');
             return true;
           }
         }
         "
    >
      <fc-icon class="d-md-none" params="name: 'user'"></fc-icon>
      <span class="d-none d-md-block" data-bind="text: title"></span>
    </a>

    <a
      class="nav-item nav-link flex-shrink-0 nav-item--history"
      id="client-history-tab"
      data-toggle="tab"
      role="tab"
      href="#client-history"
      aria-controls="client-history"
      data-bind="
        event: {
          'show.bs.tab': function() {
            activeTab('history');
            return true;
          }
        },
        "
    >
      <fc-icon class="d-md-none" params="name: 'history'"></fc-icon>
      <span class="d-none d-md-block" data-bind=" text: _t('История')"></span>
    </a>
  </nav>

  <div class="foquz-dialog__body tab-content">
    <div
      class="tab-pane show active pt-4 h-100"
      role="tabpanel"
      id="review-form"
      class="review-form"
    >
      <!-- ko if: activeTab() === 'review' -->
      <fc-feedback-review-form
        params="reviewId: review.id, hideClientData: hideClientData"
      ></fc-feedback-review-form>
      <!-- /ko -->
    </div>

    <div class="tab-pane pt-4 h-100" role="tabpanel" id="client-form">
      <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
        <div class="container">
          <client-card
            params="card: card, formControlErrorStateMatcher: formControlErrorStateMatcher, blocked: blocked"
          ></client-card>
        </div>
      </div>
    </div>

    <div class="tab-pane pt-30p h-100" role="tabpanel" id="client-history">
      <foquz-client-history
        params="ref: history"
        data-bind="event: {
              reviewClick: function(_, e, reviewId) { openReview(reviewId) }
            }"
      ></foquz-client-history>
    </div>
  </div>

  <!-- ko ifnot: blocked -->
  <!-- ko if: activeTab() === 'client' -->
  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() {
                $dialog.hide();
              }"
      >
        <foquz-icon params="icon: 'bin'" class="f-btn-prepend"></foquz-icon>
        <span data-bind="text: _t('Отменить')"></span>
      </button>
      <button
        type="submit"
        class="f-btn f-btn-success"
        data-bind="click: function() { submit()  }"
      >
        <foquz-icon params="icon: 'save'" class="f-btn-prepend"></foquz-icon>
        <span data-bind="text: _t('Сохранить')"></span>
      </button>
    </div>
  </div>
  <!-- /ko -->

  <!-- /ko -->
</sidesheet>
