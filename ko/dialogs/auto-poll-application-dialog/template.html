<foquz-dialog params="ref: modal, dialogWrapper: $component">

  <foquz-dialog-header>
    Оставить заявку
  </foquz-dialog-header>

  <div class="foquz-dialog__body autopoll__body">
    <div class="form-group autopoll__form__group">
      <div class="d-flex justify-content-between align-items-center">
        <label for=""
               class="form-label">Ваше имя</label>
      </div>

      <input type="text"
             class="form-control"
             data-bind="textInput: name">

    </div>

    <div class="form-group mb-0">

      <label for="" class="form-label">Телефон<span class="f-color-danger">*</span></label>

      <input type="text"
             class="form-control"
             data-bind="phoneMask, textInput: phone, css: {
        'is-valid': formControlSuccessStateMatcher(phone),
        'is-invalid': formControlErrorStateMatcher(phone),
      }">

      <!-- ko template: {
        foreach: formControlErrorStateMatcher(phone),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
      <div class="form-error"
           data-bind="text: $parent.phone.error()"></div>
      <!-- /ko -->

    </div>
  </div>


  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">

      <button type="button"
              class="f-btn f-btn-link"
              data-bind="
                click: function() {
                  $dialog.hide('close');
                }">Отменить</button>

      <button type="button"
              class="f-btn"
              data-bind="
              click: function() {
                    submit();
                  }">Отправить</button>
    </div>
  </div>

</foquz-dialog>
