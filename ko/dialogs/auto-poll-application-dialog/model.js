import { DialogWrapper } from 'Dialogs/wrapper';
import { DialogsModule } from "Utils/dialogs-module";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);
    this.url = params.url;

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.name = ko.observable('');

    if (CURRENT_USER && (CURRENT_USER.fullName || CURRENT_USER.name)) {
      if (CURRENT_USER.fullName) {
        this.name(CURRENT_USER.fullName);
      } else {
        this.name(CURRENT_USER.name);
      }
    }

    this.phone = ko.observable('').extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => !this.canEdit
      },
      validation: {
        validator: (value) => {
          const pattern = utils.regex.phone;
          const phone = value.trim();
          return pattern.test(phone);
        },
        message: 'Неверный формат',
        onlyIf: () => !this.canEdit
      }
    });

    this.validationObject = ko.validatedObservable(
      {
        phone: this.phone
      },
      { deep: true, liv: true }
    );
  }

  submit() {
    this.isSubmitted(true);

    if (!this.validationObject.isValid()) return;

    let url = `${APIConfig.baseApiUrlPath}${this.url}?access-token=${APIConfig.apiKey}`

    let params = {
      name: this.name().toString(),
      phone: this.phone().toString()
    };

    $.ajax({
      url,
      data: params,
      method: 'POST',
      success: (data) => {
        this.emitEvent('submit', data);
        this.hide();
        setTimeout(() => {
          this.info({
            text: 'Заявка успешно отправлена. В ближайшее время наш сотрудник свяжется с вами.'
          });
        }, 200);
      }
    });
  }
}
