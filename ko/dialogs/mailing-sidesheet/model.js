import { get as _get } from "lodash";
import { DialogWrapper } from "Dialogs/wrapper";
import { TableListModel } from "Legacy/models/table";
import "Legacy/components/tags-select";
import { BirthdayFilterModel } from "Legacy/components/birthday-filter";
import { Observer } from "Legacy/utils/observer";
import { NEW_MAILING } from "Legacy/data/mailing-statuses";
import { ContactModel } from "./contact";
import { CheckedModel } from "Legacy/models/table/checked";
import { GroupActions } from "Legacy/models/table/group-actions";
// import '../select-contacts-modal';
import { getClientFields } from "Legacy/utils/client-fields";
// import 'Modals/select-contacts-modal-page';
import "Dialogs/select-contacts-sidesheet";
import { TagsDirectory } from "@/utils/directory/tags";

const statuses = {
  0: "new",
  1: "launched",
  2: "stopped",
};

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    this.modals = ko.observableArray([]);
    console.log("mailing-sidesheet", params);
    this.mailing = params.data.mailing;
    this.filterSetting = ko.observable(null);

    // TODO: Save filters
    // this.contactsFilter = ko.observable('');
    // this.answeredOnlyFilter = ko.observable(false);
    // this.passedAtStringFilter = ko.observable('');
    // this.statusStringFilter = ko.observable('');
    // this.lastConnectionChannelStringFilter = ko.observable('');
    // this.nameStringFilter = ko.observable('');
    // this.phoneStringFilter = ko.observable('');
    // this.emailStringFilter = ko.observable('');
    // this.genderStringFilter = ko.observable('');
    // this.birthdayStringFilter = ko.observable('');
    // this.tagStringFilter = ko.observable('');
    // this.addedAtStringFilter = ko.observable('');

    // TODO: Save filters
    // this.getFilterObj = function () {
    //   var sort = null;
    //
    //   $(
    //       '.foq-table__sorting-icon',
    //       '.mailings__details-modal-dialog-table'
    //   ).each(function () {
    //     var order_key;
    //     if ($(this).hasClass('foq-table__sorting-icon--order_asc')) {
    //       order_key = $(this).attr('order-key');
    //       sort = {
    //         key: order_key,
    //         order: 'asc'
    //       };
    //     } else if ($(this).hasClass('foq-table__sorting-icon--order_desc')) {
    //       order_key = $(this).attr('order-key');
    //       sort = {
    //         key: order_key,
    //         order: 'desc'
    //       };
    //     }
    //   });
    //
    //   // Почему-то делает массив при смене операции в интерфейсе
    //   var tagOperation = this.filters.tagsOperation();
    //   if (tagOperation instanceof Array) tagOperation = tagOperation[0];
    //
    //   var obj = {
    //     sort: sort,
    //     advanced: {
    //       contacts: ko.toJS(this.contactsFilter()).trim(),
    //
    //       answeredOnly: ko.toJS(this.answeredOnlyFilter()),
    //       status: ko.toJS(this.filters.statuses()),
    //       connectionChannel: ko.toJS(this.filters.connectionChannel()),
    //
    //       gender: ko.toJS(this.filters.genders()),
    //       tags: ko.toJS(this.filters.tags()),
    //       tagsOperation: tagOperation,
    //
    //       birthday: this.birthdayFilter.getParams()
    //     },
    //
    //     simple: {
    //       passedAt: this.passedAtStringFilter(),
    //       passedAtFormat: '%d.%m.%Y',
    //       status: this.statusStringFilter(),
    //       lastConnection: this.lastConnectionChannelStringFilter(),
    //       name: this.nameStringFilter(),
    //       phone: this.phoneStringFilter(),
    //       email: this.emailStringFilter(),
    //       gender: this.genderStringFilter(),
    //       birthday: this.birthdayStringFilter(),
    //       birthdayFormat: '%d.%m.%Y',
    //       tag: this.tagStringFilter(),
    //       addedAt: this.addedAtStringFilter(),
    //       addedAtFormat: '%d.%m.%Y'
    //     }
    //   };
    //
    //   if (
    //       ['1', '0'].includes(this.filters.filled()) &&
    //       this.filters.fields().length
    //   ) {
    //     obj.records = {
    //       filled: this.filters.filled(),
    //       fields: this.filters.fields()
    //     };
    //   }
    //
    //   return obj;
    // };
    this.dialogs = ko.observable(null);

    this.isLaunched = ko.pureComputed(() => {
      return this.mailing.status() != NEW_MAILING;
    });

    this.statusName = ko.pureComputed(() => {
      return statuses[this.mailing.status()];
    });

    this.hasContacts = ko.pureComputed(() => {
      return this.mailing.contactsCount() > 0;
    });

    this.observer = new Observer(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            this.load();
          }
        });
      },
      {
        rootMargin: "50px",
      }
    );

    this.directories = {
      tags: new TagsDirectory("contact-tags?all=1"),
    };

    Object.keys(this.directories).forEach((key) =>
      this.directories[key].load()
    );

    this.clientFields = getClientFields();

    this.isLoading = ko.observable(false);
    this.hasResults = ko.observable(true);
    this.isLastPage = ko.observable(false);
    this.page = ko.observable(1);
    this.items = ko.observableArray([]);

    this.totalCount = ko.observable(0);
    this.countWithoutFilters = ko.observable(0);
    this.checked = new CheckedModel({
      items: this.items,
      totalCount: this.totalCount,
    });

    this.groupActions = new GroupActions({
      selectedCount: this.checked.checkedCount,
      selectAll: () => {
        this.checked.checkAll();
      },
      actions: [{ id: 1, text: "Удалить" }],
      apply: () => {
        // TODO
        let action = this.groupActions.action();
        if (!action) return;

        if (action == 1) {
          this.removeContacts();
        }

        this.checked.uncheckAll();
      },
      reset: () => {
        this.checked.uncheckAll();
      },
    });

    this.filters = {
      statuses: ko.observableArray([]),
      clientSearch: ko.observable(""),
      genders: ko.observableArray([]),
      tags: ko.observableArray([]),
      tagsOperation: ko.observable("1"),
      filled: ko.observable(""),

      // TODO: Save filters
      fields: ko.observableArray([]),
      // connectionChannel: ko.observableArray([]),
    };

    this.birthdayFilter = new BirthdayFilterModel({
      format: "YYYY-MM-DD",
    });

    this.newMailingTable = {};
    this.launchedMailingTable = {};

    TableListModel(this.newMailingTable, {
      columns: [
        {
          id: "name",
          name: "ФИО",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
        {
          id: "phone",
          name: "Телефон",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
        {
          id: "email",
          name: "Email",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
        {
          id: "gender",
          name: "Пол",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
        {
          id: "birthday",
          name: "Дата рождения",
          searchable: true,
          sortable: true,
          placeholder: "00.00.0000",
        },
        {
          id: "tags",
          name: "Теги",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
        {
          id: "added_at",
          name: "Добавлен",
          searchable: true,
          sortable: true,
          placeholder: "00.00.0000",
        },
      ],
      onChange: () => this.applyFilters(),
      localStorageKey: "fmailing-mailing-new-" + this.mailing.id(),
      searchParam: "search",
      orderParam: "order",
      defaultSort: "added_at",
    });

    TableListModel(this.launchedMailingTable, {
      columns: [
        {
          id: "sended_at",
          name: "Отправлена",
          searchable: true,
          sortable: true,
          placeholder: "00.00.0000",
        },
        {
          id: "status",
          name: "Статус",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
        {
          id: "name",
          name: "ФИО",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
        {
          id: "phone",
          name: "Телефон",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
        {
          id: "email",
          name: "Email",
          searchable: true,
          sortable: true,
          placeholder: "Все",
        },
      ],
      onChange: () => this.applyFilters(),
      localStorageKey: "fmailing-mailing-launched-" + this.mailing.id(),
      searchParam: "search",
      orderParam: "order",
      defaultSort: "sended_at",
    });

    this.activeTable = ko.pureComputed(() => {
      return this.mailing.isNew()
        ? this.newMailingTable
        : this.launchedMailingTable;
    });
    this.close = params.close;

    this.mailing.status.subscribe((v) => {
      this.applyFilters();
    });

    this.loadPreviousFilterSetting = params.loadPreviousFilterSetting;
    this.mailingId = params.mailingId;
  }

  removeContacts() {
    let params = {};

    if (this.checked.allItemsChecked()) {
      params.type = "filter";
      params = {
        ...params,
        type: "filter",
        ...this.getSearchParams(),
      };
    } else {
      params.clients = this.checked.getChecked().map((c) => c.listContactId);
    }

    this.mailing.removeContacts(params).then(() => {
      this.applyFilters();
    });
  }

  getSearchParams() {
    const params = {
      order: this.activeTable().sort.getParams(),
      search: this.activeTable().search.getParams(),
    };

    if (this.filters.clientSearch()) {
      params.client = this.filters.clientSearch();
    }

    if (this.mailing.isNew()) {
      params.tags = this.filters.tags();
      params.tagsOperation = this.filters.tagsOperation();
      params.gender = this.filters.genders();
      params.birthday = this.birthdayFilter.getParams();
      if (
        ["1", "0"].includes(this.filters.filled()) &&
        this.filters.fields().length
      ) {
        params.records = {
          filled: this.filters.filled(),
          fields: this.filters.fields(),
        };
      }
    } else {
      params.statuses = this.filters.statuses();
    }

    return params;
  }

  resetFilters() {
    // TODO: Save filters
    // this.contactsFilter('');
    // this.answeredOnlyFilter(false);
    // this.filters.connectionChannel([]);
    // this.passedAtStringFilter('');
    // this.statusStringFilter('');
    // this.lastConnectionChannelStringFilter('');
    // this.nameStringFilter('');
    // this.phoneStringFilter('');
    // this.emailStringFilter('');
    // this.genderStringFilter('');
    // this.birthdayStringFilter('');
    // this.tagStringFilter('');
    // this.addedAtStringFilter('');
    this.filters.statuses([]);
    this.filters.clientSearch("");
    this.filters.tags([]);
    this.filters.tagsOperation("1");
    this.filters.genders([]);
    this.filters.filled("");
    this.filters.fields([]);

    this.birthdayFilter.reset();

    this.activeTable().sort.reset();
    this.activeTable().search.reset();

    this.applyFilters();
  }

  applyFilters() {
    this.page(1);
    this.isLastPage(false);
    this.items.removeAll();
    this.load();
  }

  load() {
    return new Promise((res, rej) => {
      if (this.isLoading()) {
        res();
        return;
      }
      if (this.isLastPage()) {
        res();
        return;
      }

      this.isLoading(true);
      let params = this.getSearchParams();

      $.ajax({
        url: `${
          APIConfig.baseApiUrlPath
        }mailing-lists/get-contacts?access-token=${
          APIConfig.apiKey
        }&id=${this.mailing.id()}`,
        data: {
          ...params,
          page: this.page(),
        },
        method: "GET",
        success: (data) => {
          this.updateData(data);
          this.isLoading(false);
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.isLoading(false);
          rej();
        },
      });
    });
  }

  loadSavedFilterSetting() {
    return new Promise((res, rej) => {
      $.ajax({
        url: `${
          APIConfig.baseApiUrlPath
        }mailing-lists/get-filter-settings?access-token=${
          APIConfig.apiKey
        }&id=${this.mailingId}`,
        method: "GET",
        success: (data) => {
          if (_get(data, 'result.length')) {
            try {
              this.filterSetting(JSON.parse(data.result));
            } catch (e) {}
          }
          res();
        },
        error: (response) => {
          rej();
        },
      });
    });
  }

  updateData(data) {
    this.page(this.page() + 1);

    data.contacts.forEach((c) => {
      let contact = new ContactModel(c);
      this.items.push(contact);
      if (this.checked.allItemsChecked()) {
        contact.checked(true);
      }
    });
    if (!data.contacts.length) this.isLastPage(true);
    this.hasResults(this.items().length > 0);

    this.mailing.updateStats(data.statuses, data.statusesUnfiltered);

    this.totalCount(parseInt(data.filterContactsCount));
    this.countWithoutFilters(parseInt(data.allContactsCount));
    if (_get(data, 'filterSetting.data.length')) {
      try {
        this.filterSetting(JSON.parse(data.filterSetting.data));
      } catch (e) {}
    }

    // "success": true,
    // "allTags": []
  }

  removeMailing() {
    this.mailing.remove().then(() => {
      this.hide();
    });
  }

  addContacts() {
    this.dialogs().add({
      name: "select-contacts-sidesheet",
      params: {
        data: {
          filterSetting: this.filterSetting,
          tagsDirectory: this.directories.tags,
          onSubmit: (params) => {
            return this.mailing.addContacts(params);
          },
        },
        onHide: () => {
          console.log("HIDE");
          this.mailing.update().then(() => {
            this.initObserver();
            this.applyFilters();
          });
        },
      },
    });
    // this.modals.push({
    //   name: 'select-contacts-modal-page',
    //   params: {
    //     data: {
    //       tagsDirectory: this.directories.tags,
    //       onSubmit: (params) => {
    //         return this.mailing.addContacts(params);
    //       }
    //     },
    //     onClose: () => {
    //       this.mailing.update().then(() => {
    //         this.initObserver();
    //         this.applyFilters();
    //       });
    //     }
    //   }
    // });
  }

  initObserver() {
    let bottom = document.getElementById("mailing-contacts-bottom");
    this.observer.deactivate();
    this.observer.setTarget(bottom);
    this.observer.activate();
  }

  async onInit() {
    this.load().then(() => {
      this.initObserver();
      if (this.loadPreviousFilterSetting && this.mailingId) {
        this.loadSavedFilterSetting();
      }
    });
  }
}
