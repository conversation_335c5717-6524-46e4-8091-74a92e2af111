<!-- ko let: { $mailingSidesheet: $component }, descendantsComplete: $component.onInit.bind($component) -->
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="sidesheet__status-line"
       data-bind="attr: {
    'data-status': statusName
  }"></div>

  <div class="foquz-dialog__header">
    <div class="container">
      <div class="w-100 d-flex justify-content-between ">
        <div class="modal-title">
          <!-- ko component: {
              name: 'name-edit-form',
              params: {
                  name: mailing.name,
                  onEdit: function(newValue) {
                    mailing.editName(newValue)
                  }
              }
          } -->
          <!-- /ko -->
        </div>
        <div>
          <div class="d-flex align-items-center">
            <!-- ko if: mailing.isNew -->
            <button class="f-btn f-btn-text bold ml-4"
                    type="button"
                    data-bind="
                enable: mailing.contactsCount() > 0,
                click: mailing.start.bind(mailing)
              ">
              <span class="f-btn-prepend">
                <span class="f-icon f-icon--start">
                  <svg>
                    <use href="#start-icon"></use>
                  </svg>
                </span>

              </span>

              <!-- ko if: mailing.launchedAt -->
              <span class="text-left">
                Будет запущена
                <br>
                <div class="font-weight-normal"
                     style="margin-top: 2px"
                     data-bind="text: mailing.launchedAt"></div>
              </span>
              <!-- /ko -->

              <!-- ko ifnot: mailing.launchedAt -->
              Запустить
              <!-- /ko -->

              <span class="f-btn-append">
                <span class="f-icon f-icon--calendar f-icon-light">
                  <svg>
                    <use href="#calendar-icon"></use>
                  </svg>
                </span>
              </span>
            </button>
            <!-- /ko -->

            <!-- ko if: mailing.isLaunched -->
            <button class="f-btn f-btn-text bold ml-4"
                    type="button"
                    data-bind="click: mailing.stop.bind(mailing)">
              <span class="f-btn-prepend">
                <span class="f-icon f-icon--stop">
                  <svg>
                    <use href="#stop-icon"></use>
                  </svg>
                </span>

              </span>
              Остановить
            </button>
            <!-- /ko -->

            <!-- ko if: mailing.isStopped -->
            <button class="f-btn f-btn-text bold ml-4"
                    type="button"
                    data-bind="click: mailing.restart.bind(mailing)">
              <span class="f-btn-prepend">
                <span class="f-icon f-icon--restart">
                  <svg>
                    <use href="#restart-icon"></use>
                  </svg>
                </span>

              </span>
              Возобновить
            </button>
            <!-- /ko -->

            <!-- ko if: mailing.canEdit -->
            <button class="f-btn f-btn-text bold ml-4"
                    type="button"
                    data-bind="
                enable: mailing.canRemove,
                click: function() {
                  $mailingSidesheet.removeMailing();
                }
              ">
              <span class="f-btn-prepend">
                <span class="f-icon f-icon--bin f-icon-danger">
                  <svg>
                    <use href="#bin-icon"></use>
                  </svg>
                </span>

              </span>
              Удалить
            </button>
            <!-- /ko -->

            <!-- ko if: mailing.canEdit -->
            <button class="f-btn f-btn-primary f-btn-lg ml-4"
                    data-bind="click: addContacts">
              Добавить контакты
            </button>
            <!-- /ko -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container">
        <div class="full-width">
          <div class="f-card__divider"></div>
          <div class="f-card__divider">
            <div class="info-row px-15p foquz-stats-group"
                 data-bind="let: {
              stats: mailing.statistics(),
              filteredStats: mailing.filteredStatistics()
            }">
              <div data-bind="log: 'stats'" style="display: none"></div>
              <!-- ko if: countWithoutFilters -->
              <foquz-stats-item>
                <div class="value">
                  <span data-bind="text: $parent.totalCount().toLocaleString()"></span>
                  <small>/<span data-bind="text: $parent.countWithoutFilters().toLocaleString()"></span></small>
                </div>
                <div class="label">Контактов<br>добавлено</div>
              </foquz-stats-item>
              <!-- /ko -->

              <!-- ko ifnot: mailing.isNew() -->

              <foquz-stats-item>
                <div class="value f-color-status--primary">
                  <span data-bind="text: filteredStats.sent.toLocaleString()"></span>
                  <small>/<span data-bind="text: stats.sent.toLocaleString()"></span></small>
                </div>
                <div class="label">Отправлено</div>
              </foquz-stats-item>
              <foquz-stats-item>
                <div class="value f-color-status--violet">

                  <span data-bind="text: filteredStats.notSent.toLocaleString()"></span>
                  <small>/<span data-bind="text: stats.notSent.toLocaleString()"></span></small>
                </div>
                <div class="label">Не отправлено</div>
              </foquz-stats-item>
              <foquz-stats-item>
                <div class="value f-color-status--red">

                  <span data-bind="text: filteredStats.emailOpened.toLocaleString()"></span>
                  <small>/<span data-bind="text: stats.emailOpened.toLocaleString()"></span></small>

                </div>
                <div class="label">Письмо открыто<br>
                  <span class="font-weight-normal">(только для <span class="font-weigth-500">email</span>)</span>
                </div>
              </foquz-stats-item>
              <!-- /ko -->
              <div class="info-row__section">
                <div class="d-flex">
                  <div class="stats-item">
                    <div class="stats-item__text"
                         data-bind="text: mailing.createdAt"></div>
                    <div class="stats-item__label">Создана</div>
                  </div>
                  <div class="stats-item ml-4">
                    <div class="bold stats-item__text"
                         data-bind="text: mailing.statusName, css: 'f-color-status--' + mailing.statusId()"></div>
                    <div class="stats-item__label">Статус</div>
                  </div>
                  <div class="stats-item ml-4">
                    <div class="stats-item__text"
                         data-bind="text: mailing.authorName"></div>
                    <div class="stats-item__label">Автор</div>
                  </div>
                </div>
              </div>
              <div class="info-row__section">
                <div class="d-flex align-items-center mx-n5p">
                  <!-- ko foreach: mailing.activeChannels -->
                  <channel-icon class="mx-5p"
                                params="channel: type, repeats: repeats"></channel-icon>
                  <!-- /ko -->
                </div>
              </div>
            </div>
          </div>
          <!-- ko if: isLaunched() || hasContacts() -->
          <div class="f-card__divider px-30p f-card__section">
            <div class="d-flex flex-wrap align-items-center">
              <div class="f-filters-group">
                <!-- ko template: {
                  foreach: templateIf(!isLaunched(), $data),
                  name: 'new-mailing-filters-template'
                } -->
                <!-- /ko -->
                <!-- ko template: {
                  foreach: templateIf(isLaunched(), $data),
                  name: 'launched-mailing-filters-template'
                } -->
                <!-- /ko -->
              </div>
              <div class="ml-auto">
                <div class="d-flex align-items-center">
                  <button class="f-btn f-btn-link mr-3"
                          data-bind="click: resetFilters"
                          type="button">Сбросить</button>
                  <button class="f-btn f-btn-success f-btn-lg"
                          data-bind="click: applyFilters"
                          type="button">
                    <span class="f-btn-prepend">
                      <svg class="f-btn-icon f-btn-icon--check">
                        <use href="#check-icon"></use>
                      </svg>
                    </span>
                    Применить
                  </button>
                </div>
              </div>
            </div>
          </div>
          <!-- /ko -->
        </div>

        <div class="full-width f-card__section f-card__grow  overflow-hidden">
          <div class="pb-4"
               data-bind="fScrollbar: {onlyY: true, gradient: true}">
            <!-- ko if: !isLaunched() && !hasContacts() -->
            <div class="p-4 f-fs-2 f-color-service text-center">
              Добавьте контакты для рассылки
            </div>
            <!-- /ko -->

            <!-- ko if: isLaunched() || hasContacts() -->

            <div class="py-4 position-relative">
              <!-- ko template: {
                foreach: templateIf(!isLaunched(), $data),
                name: 'new-mailing-table-template'
              } -->
              <!-- /ko -->

              <!-- ko template: {
                foreach: templateIf(isLaunched(), $data),
                name: 'launched-mailing-table-template'
              } -->
              <!-- /ko -->

              <!-- ko if: !isLoading() && !hasResults() -->
              <div class="p-4 text-center f-color-service">
                Ничего не найдено
              </div>
              <!-- /ko -->

              <div id="mailing-contacts-bottom"></div>

              <!-- ko if: isLoading -->
              <div class="text-center p-4">
                <i class="fa fa-spinner fa-pulse fa-2x fa-fw f-color-primary"></i>
              </div>
              <!-- /ko -->
            </div>

            <!-- /ko -->
          </div>

        </div>
      </div>
    </div>
  </div>

  <foquz-modals-container params="modals: modals"></foquz-modals-container>

</sidesheet>

<template id="new-mailing-filters-template">
  <div class="f-filters-group__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">ФИО, телефон, email</label>
      <!-- ko let: { autosizeInput: ko.observable(null) } -->
      <input type="text"
             class="form-control"
             data-bind="textInput: filters.clientSearch, autosizeInput"
             placeholder="Любые"
             style="min-width: 80px">
      <!-- /ko -->
    </div>
  </div>


  <!-- ko if: directories.tags.loaded -->
  <div class="f-filters-group__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Теги</label>

      <!-- ko component: {
        name: 'tags-select',
        params: {
          selectedOptions: filters.tags,
          directory: directories.tags,
          element: ko.observable(null),
          select2Options: {
            wrapperCssClass: 'select2-container--form-control',
            dropdownCssClass: 'dense-form-group__dropdown tags-dropdown',
            containerCss: { 'min-width': '27px', },
          }
        }
      } -->
      <!-- /ko -->

    </div>
  </div>

  <div class="f-filters-group__filter"
       data-bind="fade: filters.tags().length > 0">
    <div class="form-group dense-form-group">
      <label class="form-label">Условие</label>

      <select data-bind="
          value: filters.tagsOperation,
          select2: {
              wrapperCssClass: 'select2-container--form-control',
              dropdownCssClass: 'dense-form-group__dropdown'
          }
      "
              data-placeholder="Все">
        <option value="1">Один из</option>
        <option value="2">Все</option>
      </select>
    </div>
  </div>
  <!-- /ko -->

  <div class="f-filters-group__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Пол</label>
      <select multiple
              data-bind="
        selectedOptions: filters.genders,
        select2: {
            wrapperCssClass: 'select2-container--form-control',
            dropdownCssClass: 'dense-form-group__dropdown',
        }"
              data-placeholder="Все">
        <option value="1">Мужской</option>
        <option value="2">Женский</option>
        <option value="3">Не указан</option>
      </select>
    </div>
  </div>

  <birthday-filter-dense params="model: birthdayFilter"
                         class="mr-4"></birthday-filter-dense>


  <div class="mailings__details-modal-dialog-filter mr-4">
    <div class="form-group dense-form-group">
      <label class="form-label">Данные контакта</label>

      <select data-bind="
          value: filters.filled,
          select2: {
              wrapperCssClass: 'select2-container--form-control',
              dropdownCssClass: 'dense-form-group__dropdown',
              allowClear: true,
              placeholder: 'Не выбрано'
          }
      "
              data-placeholder="Не выбрано">
        <option></option>
        <option value="1">Заполнены</option>
        <option value="0">Не заполнены</option>
      </select>
    </div>
  </div>


  <div class="mailings__details-modal-dialog-filter"
       data-bind="fade: clientFields.loaded() && ['1', '0'].includes(filters.filled())">

    <div class="form-group dense-form-group ">
      <label class="form-label">Значения</label>
      <select multiple
              data-bind="
          selectedOptions: filters.fields,
          lazySelect2: {
              wrapperCssClass: 'select2-container--form-control',
              minimumResultsForSearch: 0,
              dropdownCssClass: 'dense-form-group__dropdown',
              containerCss: { 'min-width': '40px' },
          }
      "
              data-placeholder="Выберите поля">
        <optgroup label="Системные">
          <!-- ko foreach: {data: clientFields.system, as: 'field'} -->
          <option data-bind="text: field.text, value: field.id"></option>
          <!-- /ko -->
        </optgroup>
        <optgroup label="Пользовательские">
          <!-- ko foreach: {data: clientFields.additional, as: 'field'} -->
          <option data-bind="text: field.text, value: field.id"></option>
          <!-- /ko -->
        </optgroup>
      </select>
    </div>
  </div>
</template>

<template id="launched-mailing-filters-template">
  <div class="f-filters-group__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Статус</label>

      <select multiple
              data-bind="
          selectedOptions: filters.statuses,
          select2: {
              wrapperCssClass: 'select2-container--form-control',
              dropdownCssClass: 'dense-form-group__dropdown',
          }
      "
              data-placeholder="Все статусы">

        <option value="2">Отправлена</option>
        <option value="1">Не отправлена</option>
        <option value="3">Письмо открыто</option>
      </select>
    </div>
  </div>

  <div class="f-filters-group__filter">
    <div class="form-group dense-form-group"
         data-bind="log">
      <label class="form-label">ФИО, телефон, email</label>
      <!-- ko let: { autosizeInput: ko.observable(null) } -->
      <input type="text"
             class="form-control"
             data-bind="textInput: filters.clientSearch, autosizeInput"
             placeholder="Любые"
             style="min-width: 80px">
      <!-- /ko -->
    </div>
  </div>
</template>


<template id="new-mailing-table-template">
  <div data-bind="log">
    <!-- ko component: {
      name: 'group-actions',
      params: {
        controller: groupActions
      }
    } -->
    <!-- /ko -->
    <table class="f-table table f-table--searchable mb-2 min-w-100"
           data-bind="let: {
      tableModel: newMailingTable
    }">
      <thead>
        <tr>
          <th class="f-table__outer-element-head-cell"></th>
          <th class="pl-0">
            <div class="f-check">
              <input type="checkbox"
                     class="f-check-input"
                     data-bind="checked: checked.allItemsChecked, enable: items().length > 0">
              <label class="f-check-label"
                     data-bind="click: function() {

                      $mailingSidesheet.checked.toggleAll();
              }"></label>
            </div>
          </th>

          <!-- ko foreach: { data: tableModel.table.columnsList, as: 'columnId' } -->

          <!-- ko template: {
                    name: 'table-head-cell-template',
                    data: {
                        column: tableModel.table.columns[columnId],
                        searchModel: tableModel.search,
                        sortModel: tableModel.sort,
                        onSearch: $mailingSidesheet.applyFilters.bind($mailingSidesheet),
                    }
                } -->
          <!-- /ko -->
          <!-- /ko -->

          <th align="right">
            <button class="f-icon f-icon-button f-icon--settings"
                    data-bind="click: tableModel.table.editColumns.bind(tableModel.table)">
              <svg>
                <use href="#settings-icon"></use>
              </svg>
            </button>
          </th>
          <th class="f-table__outer-element-head-cell"></th>
        </tr>
      </thead>
      <tbody>
        <!-- ko foreach: { data: $mailingSidesheet.items, as: 'contact' } -->
        <tr class="f-table__row"
            data-bind="css: {
          'f-table__row--selected': contact.checked
        }">
          <td class="f-table__outer-element-cell"></td>
          <td valign="middle"
              class="pl-0">
            <div class="f-check">
              <input type="checkbox"
                     class="f-check-input"
                     data-bind="checked: contact.checked">
              <label class="f-check-label"
                     data-bind="click: function() {
                      $mailingSidesheet.checked.toggleItem(contact);
              }"></label>
            </div>
          </td>

          <!-- ko let: { column: tableModel.table.columns.name } -->
          <!-- ko if: column.visible -->
          <td valign="middle">
            <div class="f-table__cell-content"
                 data-bind="text: contact.name"></div>
          </td>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko let: { column: tableModel.table.columns.phone } -->
          <!-- ko if: column.visible -->
          <td valign="middle"
              width="150">
            <div class="f-table__cell-content nowrap"
                 data-bind="text: contact.phone"></div>
          </td>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko let: { column: tableModel.table.columns.email } -->
          <!-- ko if: column.visible -->
          <td valign="middle">
            <div class="f-table__cell-content"
                 data-bind="text: contact.email"></div>
          </td>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko let: { column: tableModel.table.columns.gender } -->
          <!-- ko if: column.visible -->
          <td valign="middle">
            <div class="f-table__cell-content"
                 data-bind="text: contact.gender"></div>
          </td>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko let: { column: tableModel.table.columns.birthday } -->
          <!-- ko if: column.visible -->
          <td width="130"
              valign="middle">
            <div class="f-table__cell-content"
                 data-bind="text: contact.birthday"></div>
          </td>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko let: { column: tableModel.table.columns.tags } -->
          <!-- ko if: column.visible -->
          <td valign="middle">
            <div class="f-table__cell-content ">
              <!-- ko foreach: contact.tags -->
              <span class="f-fs-1 font-weight-500 mr-3"
                    data-bind="text: tag, css: {
                'f-color-success': auto_add == '1'
              }"></span>
              <!-- /ko -->
            </div>
          </td>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko let: { column: tableModel.table.columns.added_at } -->
          <!-- ko if: column.visible -->
          <td valign="middle">
            <div class="f-table__cell-content"
                 data-bind="text: contact.createdAt"></div>
          </td>
          <!-- /ko -->
          <!-- /ko -->

          <td></td>

          <td class="f-table__outer-element-cell"></td>
        </tr>
        <!-- /ko -->
      </tbody>
    </table>
  </div>
</template>

<template id="launched-mailing-table-template">
  <table class="f-table table f-table--searchable mb-2 min-w-100"
         data-bind="let: {
    tableModel: launchedMailingTable
  }">
    <thead>
      <tr>
        <!-- ko foreach: { data: tableModel.table.columnsList, as: 'columnId' } -->
        <!-- ko template: {
                  name: 'table-head-cell-template',
                  data: {
                      column: tableModel.table.columns[columnId],
                      searchModel: tableModel.search,
                      sortModel: tableModel.sort,
                      onSearch: $mailingSidesheet.applyFilters.bind($mailingSidesheet),
                  }
              } -->
        <!-- /ko -->
        <!-- /ko -->

        <th align="right">
          <button class="f-icon f-icon-button f-icon--settings"
                  data-bind="click: tableModel.table.editColumns.bind(tableModel.table)">
            <svg>
              <use href="#settings-icon"></use>
            </svg>
          </button>
        </th>
      </tr>
    </thead>
    <tbody>
      <!-- ko foreach: { data: $mailingSidesheet.items, as: 'contact' } -->
      <tr>

        <!-- ko let: { column: tableModel.table.columns.sended_at } -->
        <!-- ko if: column.visible -->
        <td valign="middle">
          <div class="f-table__cell-content">
            <!-- ko if: contact.isPassed -->
            <!-- ko text: contact.passedAt -->
            <!-- /ko -->
            <!-- /ko -->

            <!-- ko ifnot: contact.isPassed -->
            &ndash;
            <!-- /ko -->
          </div>
        </td>
        <!-- /ko -->
        <!-- /ko -->

        <!-- ko let: { column: tableModel.table.columns.status } -->
        <!-- ko if: column.visible -->
        <td valign="middle">
          <div class="f-table__cell-content f-fs-1 bold"
               data-bind="text: contact.statusName, class: contact.statusClass"></div>
        </td>
        <!-- /ko -->
        <!-- /ko -->

        <!-- ko let: { column: tableModel.table.columns.name } -->
        <!-- ko if: column.visible -->
        <td valign="middle">
          <div class="f-table__cell-content"
               data-bind="text: contact.name"></div>
        </td>
        <!-- /ko -->
        <!-- /ko -->

        <!-- ko let: { column: tableModel.table.columns.phone } -->
        <!-- ko if: column.visible -->
        <td valign="middle">
          <div class="f-table__cell-content"
               data-bind="text: contact.phone"></div>
        </td>
        <!-- /ko -->
        <!-- /ko -->

        <!-- ko let: { column: tableModel.table.columns.email } -->
        <!-- ko if: column.visible -->
        <td valign="middle">
          <div class="f-table__cell-content"
               data-bind="text: contact.email"></div>
        </td>
        <!-- /ko -->
        <!-- /ko -->
      </tr>
      <!-- /ko -->
    </tbody>
  </table>
</template>

<dialogs-container params="ref: dialogs"></dialogs-container>
<!-- /ko -->
