<sidesheet params="ref: modal, dialogWrapper: $component" data-bind="event: {
  'hide': function() {
    channel.reset()
  }
}">

  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">
        <svg-icon class="svg-icon--xl"
                  params="name: 'channel-' + channel.channelType"
                  data-bind="css: 'f-color-channel-' + channel.channelType"></svg-icon>
        <!-- ko text: channel.title -->
        <!-- /ko -->
      </h2>
    </div>
  </div>


  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
        <div class="container">
          <div class="pb-4 pt-10p">
            <switch params="checked: channel.isActive, disabled: blocked">Активен</switch>
            <!-- ko component: {
                name: 'channelForm',
                params: {
                    channel: channel.channel,
                    blocked: blocked
                }
            } -->
            <!-- /ko -->
          </div>
        </div>
      </div>
  </div>


  <!-- ko ifnot: blocked -->
  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button type="button"
                class="f-btn"
                data-bind="click: function() {
                  $dialog.hide();
                }">
          <foquz-icon params="icon: 'bin'"
                      class="f-btn-prepend"></foquz-icon>
          Отменить
        </button>


        <button type="submit"
                class="f-btn f-btn-success"
                data-bind="click: function() { channel.save();  }">
          <foquz-icon params="icon: 'save'"
                      class="f-btn-prepend"></foquz-icon>
          Сохранить
        </button>
    </div>

    <saved-message params="show: channel.showSavedMessage"></saved-message>
  </div>
  <!-- /ko -->

</sidesheet>
