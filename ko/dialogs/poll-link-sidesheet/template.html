<sidesheet class="poll-link-sidesheet" params="ref: modal, beforeHide: beforeHide, dialogWrapper: $component">
  <div class="poll-link-sidesheet__wrapper">
    <div class="d-flex align-items-center mb-35p">
      <!-- ko if: !editingName() -->
        <div class="f-h1" data-bind="text: linkName"></div>
        <button
          class="f-btn f-btn--round f-btn-white ml-10p"
          type="button"
          title="Редактировать название"
          data-bind="
            click: () => editingName(true),
            tooltip,
            attr: { disabled: linkModel.pending() },
          "
        >
          <svg-icon
            params="name: 'pencil'"
            class="f-color-service"
          ></svg-icon>
        </button>
      <!-- /ko -->
      <!-- ko if: editingName() -->
        <edit-form
          params="value: linkName,
            maxLength: 255,
            disabled: linkModel.pending(),
            onSave: async (v) => { 
              await linkModel.saveLinkName(v);
              linkName(v)
              editingName(false);
            },
            onCancel: () => editingName(false),
          "
        ></edit-form>
      <!-- /ko -->
    </div>

    <div class="mb-35p">
      <switch
        params="checked: linkModel.active, disabled: linkModel.pending()"
        class="mb-0"
      >
        <span>Активная</span>
      </switch>
    </div>

    <div class="tab-btn-group">
      <div
        class="tab-btn-group__item"
        data-bind="
          css: { active: activeTab() === 'settings' },
          click: () => nextTab('settings'),
        "
      >
        Настройки
      </div>
      <div
        class="tab-btn-group__item"
        data-bind="
          css: { active: activeTab() === 'quotas' },
          click: () => nextTab('quotas'),
        "
      >
        Квота по ссылкам
        <svg class="ml-5p" width="7" height="18" viewBox="0 0 7 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 7.50873C1 8.89129 2.11929 10.0121 3.5 10.0121C4.88071 10.0121 6 8.89129 6 7.50873C6 6.12616 4.88071 5.00537 3.5 5.00537C4.36261 4.78305 5 3.93586 5 3.00269C5 1.89663 4.10457 1 3 1C1.89543 1 1 1.89663 1 3.00269V7.50873ZM1 7.50873V12" stroke="#37A74A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <!-- ko if: linkModel.hasQuote.link() -->
        <svg class="ml-10p" xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M14.3334 7C14.3334 10.866 11.1994 14 7.33337 14C3.46738 14 0.333374 10.866 0.333374 7C0.333374 3.13401 3.46738 0 7.33337 0C11.1994 0 14.3334 3.13401 14.3334 7Z" fill="#37A74A"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0135 5.67039C11.3837 5.26058 11.3516 4.62823 10.9418 4.25799C10.532 3.88775 9.89966 3.91982 9.52942 4.32963L6.61597 7.55441L5.03557 5.99573C4.64235 5.60792 4.0092 5.6123 3.62139 6.00552C3.23358 6.39873 3.23796 7.03188 3.63118 7.41969L5.95542 9.71199C6.14965 9.90355 6.41372 10.0074 6.68641 9.99959C6.95911 9.99174 7.21675 9.87281 7.39964 9.67039L11.0135 5.67039Z" fill="white"/>
        </svg>
        <!-- /ko -->
      </div>
      <div
        class="tab-btn-group__item"
        data-bind="
          css: { active: activeTab() === 'answers' },
          click: () => nextTab('answers'),
        "
      >
        Квоты по ответам
        <svg class="ml-5p" width="7" height="18" viewBox="0 0 7 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 7.50873C1 8.89129 2.11929 10.0121 3.5 10.0121C4.88071 10.0121 6 8.89129 6 7.50873C6 6.12616 4.88071 5.00537 3.5 5.00537C4.36261 4.78305 5 3.93586 5 3.00269C5 1.89663 4.10457 1 3 1C1.89543 1 1 1.89663 1 3.00269V7.50873ZM1 7.50873V12" stroke="#37A74A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <!-- ko if: linkModel.hasQuote.answers() -->
        <svg class="ml-10p" xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M14.3334 7C14.3334 10.866 11.1994 14 7.33337 14C3.46738 14 0.333374 10.866 0.333374 7C0.333374 3.13401 3.46738 0 7.33337 0C11.1994 0 14.3334 3.13401 14.3334 7Z" fill="#37A74A"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0135 5.67039C11.3837 5.26058 11.3516 4.62823 10.9418 4.25799C10.532 3.88775 9.89966 3.91982 9.52942 4.32963L6.61597 7.55441L5.03557 5.99573C4.64235 5.60792 4.0092 5.6123 3.62139 6.00552C3.23358 6.39873 3.23796 7.03188 3.63118 7.41969L5.95542 9.71199C6.14965 9.90355 6.41372 10.0074 6.68641 9.99959C6.95911 9.99174 7.21675 9.87281 7.39964 9.67039L11.0135 5.67039Z" fill="white"/>
        </svg>
        <!-- /ko -->
      </div>
    </div>

    <!-- ko if: activeTab() === 'settings' -->
    <poll-link-sidesheet-links
      params="tabParams: tabParams"
    ></poll-link-sidesheet-links>
    <!-- /ko -->

    <!-- ko if: activeTab() === 'quotas' -->
      <poll-link-sidesheet-quotas
        params="tabParams: tabParams"
      ></poll-link-sidesheet-quotas>
    <!-- /ko -->

    <!-- ko if: activeTab() === 'answers' -->
      <poll-link-sidesheet-answers
        params="tabParams: tabParams"
      ></poll-link-sidesheet-answers>
    <!-- /ko -->

  </div>
  <!-- ko template: {
      foreach: templateIf(tabParams.footer(), $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <div class="foquz-dialog__footer fixed-footer fixed poll-widget-sidesheet-footer">
    <div class="foquz-dialog__actions">
      <fc-button
        class="ml-2"
        params="
          label: 'Отменить',
          icon: 'bin',
          color: 'secondary',
          click: () => reset(),
        "
      ></fc-button>

      <fc-button
        class="ml-2"
        params="
          label: 'Сохранить',
          icon: 'save',
          color: 'success',
          disabled: linkModel.pending() || !isValid(),
          click: () => submit()
        "
      ></fc-button>
    </div>
    <fc-success params="show: tabParams.success()" class="fc-success">
      Сохранено успешно
    </fc-success>
  </div>
  <!-- /ko -->
</sidesheet>
