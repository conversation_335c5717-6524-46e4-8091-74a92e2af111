export class ViewModel {
  constructor({ tabParams: { footer, callbacks, linkModel, pollLink, blocked, error, success, isValid }}) {
    footer(true)

    this.blocked = blocked;

    this.error = error;
    this.success = success;

    this.poll = pollLink.poll; 
    this.linkModel = linkModel;

    this.limitAnswer = this.linkModel.formattedLimitAnswer;
    this.datetimeEnd = this.linkModel.datetimeEnd;
    this.datetimeEndTime = this.linkModel.datetimeEndTime;

    callbacks.submit = this.submit.bind(this);
    callbacks.reset = this.reset.bind(this);

    this.linkModel.unsubscribeAll();
    this.linkModel.subscribeAll([
      this.limitAnswer,
      this.datetimeEnd,
      this.datetimeEndTime,
    ]);
    this.linkModel.isChanged(false);

    this.linkModel.datetimeIsValid.subscribe((v) => {
      isValid(v)
    });
  }

  async submit() {
    try {
      await this.linkModel.editQuote();
      this.success(true);

      this.linkModel.unsubscribeAll();
      this.linkModel.subscribeAll([
        this.limitAnswer,
        this.datetimeEnd,
        this.datetimeEndTime,
      ]);
      this.linkModel.isChanged(false);
    } catch(e) {
      let errors = e?.responseJSON?.errors;
      this.error(errors);
    }
  }

  async reset() {
    this.linkModel.resetLinkQuotes();

    this.linkModel.unsubscribeAll();
    this.linkModel.subscribeAll([
      this.limitAnswer,
      this.datetimeEnd,
      this.datetimeEndTime,
    ]);
    this.linkModel.isChanged(false);
  }
}