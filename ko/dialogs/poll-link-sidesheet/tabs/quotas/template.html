
<div class="row">
  <div class="col-12 col-lg-4 mb-25p">
    <fc-label params="text: 'Лимит ответов по ссылкам', hint: 'Лимит ответов по ссылкам'"></fc-label>
    <input
      type="text"
      class="form-control"
      placeholder="Не ограничено"
      data-bind="
        textInput: limitAnswer,
        css: { 'is-invalid': linkModel.limitAnswerError }
      "
      maxlength="6"
    >
  </div>
  <div class="col-12 col-lg-8">
    <fc-label params="text: 'Закончить приём ответов', hint: 'Закончить приём ответов'"></fc-label>
    <div class="row">
      <div class="col-12 col-lg-6 mb-25p pr-lg-0">
        <fc-date-picker
          class="w-100"
          params="
            value: datetimeEnd,
            invalid: linkModel.formControlErrorStateMatcher(datetimeEnd),
            placeholder: 'Не ограничено',
            clearable: true,
          "
        ></fc-date-picker>
        <validation-feedback
          params="
            show: linkModel.formControlErrorStateMatcher(datetimeEnd),
            text: datetimeEnd.error
          "
        ></validation-feedback>
      </div>
      <div class="col-12 col-lg-4 mb-25p">
        <div class="d-flex align-items-center">
          <fc-input
            style="width: 91px;"
            params="
              value: datetimeEndTime,
              mask: 'time',
              invalid: linkModel.formControlErrorStateMatcher(datetimeEndTime),
              placeholder: '00:00',
              disabled: false
            "
            class="text-center"
          ></fc-input>
          <span
            class="ml-15p"
          >
            чч : мм
          </span>
        </div>
        <validation-feedback
          params="
            show: linkModel.formControlErrorStateMatcher(datetimeEndTime),
            text: datetimeEndTime.error
          "
        ></validation-feedback>
      </div>
    </div>
  </div>
</div>