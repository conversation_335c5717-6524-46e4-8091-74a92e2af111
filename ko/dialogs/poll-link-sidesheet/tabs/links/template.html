
<div class="position-relative">
  <!-- ko if: linkPreview -->
    <div class="row">
      <div class="col-lg-4">
        <div class="form-group">
          <fc-label params="text: 'Филиал', hint: 'Филиал'"></fc-label>
          <div class="filials-select">
            <fc-select
              class="categorized"
              params="
                options: filials,
                value: filialId,
                onChange: (newValue) => onChangeFilialId(newValue),
                placeholder: 'Без филиала',
                clearable: true,
              "
            ></fc-select>
          </div>
          <fc-button
            class="mt-10p"
            params="label: 'Выгрузить ссылки для всех филиалов (CSV)', color: 'primary', mode: 'text'"
            data-bind="click: () => linkModel.downloadAllLinks()"
          ></fc-button>
        </div>
      </div>
      <!-- ko if: langs.length > 0 -->
      <div class="col-lg-4 mb-25p">
        <div class="form-group">
          <fc-label params="text: 'Язык прохождения', hint: 'Язык прохождения'"></fc-label>
          <fc-select
            params="
              value: langId,
              options: langs,
              clearable: true,
            "></fc-select>
        </div>
      </div>
      <!-- /ko -->
      <div class="col-lg-4 mb-25p">
        <div class="form-control-wrapper pt-40p">
          <fc-check
            params="
              type: 'checkbox',
              checked: linkPreview().tablet,
              label: 'Опрос для планшета в торговой точке',
              hint: 'Опрос для планшета в торговой точке',
            "
          ></fc-check>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-lg-8 mb-25p">
        <fc-label
          params="
            text: 'Ссылка',
            hint: 'Общая ссылка для прохождения опроса. Для ссылки есть возможность изменить код'
          "
        ></fc-label>
        <div
          class="d-md-flex"
          data-bind="
            let: {
              copied: ko.observable(false)
            }
          "
        >
          <div
            id="link-block-input"
            class="link-block link-block--input flex-grow-1 mr-15p"
          >
            <div
              class="form-control d-flex align-items-center pr-2"
              data-bind="
                css: {
                  'is-invalid': linkPreview().formControlErrorStateMatcher(linkPreview().key)
                }
              "
            >
              <div class="poll-link-sidesheet-links__link d-flex flex-wrap align-items-center flex-grow-1">
                <span
                  class="text-muted"
                  data-bind="text: linkPreview().baseWithParams()"
                ></span>
                <fc-auto-input params="value: linkPreview().key, space: 0"></fc-auto-input>
                <span
                  class="text-muted link-params"
                  data-bind="text: linkPreview().queryWithParams()"
                ></span>
              </div>

              <div class="ml-auto">
                <fc-button
                  class="copy-btn mt-1"
                  params="
                    mode: 'text',
                    size: 'auto',
                    icon: {name: 'copy', size: 20},
                    click: () => {
                      copy(linkPreview().fullLinkWithParams());
                      copied(true);
                    }
                  "
                  data-bind="
                    fbPopper,
                    title: 'Скопировать в буфер',
                  "
                ></fc-button>
              </div>
            </div>
          </div> 
          <fc-status-popper
            params="
              target: 'link-block-input',
              show: copied,
              mode: 'success'
            "
          >
            Ссылка скопирована в буфер
          </fc-status-popper>

          <!-- ko ifnot: blocked -->
          <div class="d-none d-md-block">
            <button
              type="button"
              class="btn btn-success btn-lg i-ajax-btn"
              data-bind="
                click: changeLink,
                attr: { disabled: linkModel.pending() || !linkPreview().key.isValid() },
              "
            >
              <span class="i"></span>
            </button>
          </div>

          <div class="d-md-none mt-15p mt-md-4">
            <button type="button"
                    class="f-btn w-100 f-btn-success"
                    data-bind="click: changeLink">

              <svg-icon params="name: 'check'"
                        class="mr-2"></svg-icon>
              Применить
            </button>
          </div>
          <!-- /ko -->
        </div>
        <!-- ko ifnot: blocked -->
          <div class="text-muted sender-page__hint">
            При изменении ссылки изменится ссылка в HTML-коде и QR-коде
          </div>
        <!-- /ko -->
        <div>
          <fc-button
            params="
              label: 'Сгенерировать уникальные ссылки (CSV)',
              color: 'primary',
              mode: 'text',
              disabled: !HAS_CONTACTS,
            "
            class="mt-10p"
            data-bind="
              click: function() {
                generateClientLinks();
              },
            "
          ></fc-button>
        </div>
        <!-- ko template: {
          foreach: templateIf(success(), $data),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200),
        } -->
          <div class="d-flex align-items-center mt-1">
            <div class="text-success d-flex">
              <i class="fas fa-check-circle"></i>
              <span class="ml-2">Код успешно сохранен</span>
            </div>
          </div>
        <!-- /ko -->
        <div>
          <validation-feedback
            params="
              show: error() || linkPreview().formControlErrorStateMatcher(linkPreview().key),
              html: error() || linkPreview().key.error
            "
          ></validation-feedback>
        </div>
      </div>
      <div
        class="col-lg-4 mb-25p"
        data-bind="let: { copied: ko.observable(false) }"
      >
        <fc-label
          params="
            text: 'Короткая ссылка',
            hint: 'Короткая ссылка',
          "
        ></fc-label>
        <fc-copy-input
          params="
            value: linkPreview().shortLink,
            disabled: true,
            status: 'Ссылка скопирована в буфер',
          "
          id="short-link-input"
          data-bind="
            event: {
              copied: () => {
                copied(true);
              },
            },
          "
        ></fc-copy-input>
        <fc-status-popper
          params="
            target: 'short-link-input',
            show: copied,
            mode: 'success',
          "
        >
          Ссылка скопирована в буфер
        </fc-status-popper>
        <!-- ko if: langs.length > 0 -->
        <div class="text-muted sender-page__hint">
          Выбор языка не влияет на короткую ссылку. По короткой ссылке отображается основная версия опроса с учетом выбранного филиала
        </div>
        <!-- /ko -->
      </div>
    </div>
    <div class="row sender-page__codes mb-25p">
      <div class="col-12 col-md-4">
        <div class="sender-page__qr">
          <label class="d-flex align-items-center form-label">
            <span>QR-код</span>
            <button
              type="button"
              class="btn-question"
              data-toggle="tooltip"
              data-placement="top"
              title=""
              data-original-title="QR-код общей ссылки опроса"
            ></button>
          </label>
    
          <div class="sender-page__qr-code">
            <img
              data-bind="attr: { src: linkPreview().qrWithParams }"
              class="img-responsive mb-2"
              style="margin-left: -8px;"
            >
          </div>
          <a
            class="btn btn-success"
            data-bind="attr: {  href: loadQr }"
          >
            Скачать QR-код
          </a>
        </div>
      </div>
      <foquz-share
        class="col-12 col-md-8"
        params="
          link: linkPreview().fullLink,
          name: poll.name,
        "
      ></foquz-share>
    </div>
  <!-- /ko -->
  <!-- ko ifnot: linkPreview -->
    <div class="sender-page__loader">
      <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
    </div>
  <!-- /ko -->
</div>