import "@/presentation/views/fc-copy-input";
import "Components/foquz-share";
import "Dialogs/generate-unique-client-links";
import { DialogsModule } from "Utils/dialogs-module";
import { FilialsDataCollection } from "Models/data-collection/filials";
import { HidePoppersEvent } from "@/utils/events/modal";

export class ViewModel {
  constructor({ tabParams: { footer, confirmNotChanged, blocked, pollLink, linkModel }}) {
    footer(false)
    DialogsModule(this);   

    this.collections = {
      filials: new FilialsDataCollection({
        integrated: false,
        undefinedFilialOption: false,
        selectableCategories: false,
      }),
    };
    this.collections.filials.load();

    this.filials = this.collections.filials.list;
    this.blocked = blocked;

    this.error = ko.observable("");
    this.success = ko.observable(false);

 
    this.filialId = ko.observable('');

    this.linkPreview = ko.observable(null);

    this.poll = pollLink.poll;
    this.langs = this.poll.langs.map((lang) => ({
      id: lang.id,
      text: lang.langName,
    }));

    this.forTablet = ko.observable(false);
    
    this.linkModel = linkModel;

    this.error.subscribe((v) => {
      if (v) setTimeout(() => this.error(''), 3000);
    });
    
    this.success.subscribe((v) => {
      if (v) setTimeout(() => this.success(false), 3000);
    });

    this.linkName = ko.observable(this.linkModel.linkName());

    this.langId = ko.observable();
    this.selectedLang = ko.computed(() => {
      const id = this.langId();
      if (!id) return null;
      return this.poll.langs.find((l) => l.id == id)?.langShortName;
    });
    this.selectedLang.subscribe((lang) => {
      this.linkPreview().lang(lang || '');
    });

    this.confirmNotChanged = confirmNotChanged;
    this.onChangeFilialId();
  }

  async onChangeFilialId(filialId = '') {
    if (this.linkPreview()) {
      if (this.linkModel.isChanged()) {
        await this.confirmNotChanged();
      }

      this.linkPreview(null);
    }
    
    this.filialId(filialId);
    const linkPreview = await this.linkModel.getLinkPreview(filialId);
    this.linkPreview(linkPreview);

    this.linkModel.unsubscribeAll();
    this.linkModel.subscribeAll(this.linkPreview().key);
    this.linkModel.isChanged(false);
  }
  
  get loadQr() {
    const fileLink = encodeURIComponent(this.linkPreview().fullLinkWithParams());
    const name = encodeURIComponent(this.poll.name);
    return `/foquz/foquz-poll/download-qr-code?fileLink=${fileLink}&name=${name}`;
  }

  copy(link) {
    copyToClipboard(link);
    HidePoppersEvent.emit();
  }

  async changeLink() {
    if (this.linkPreview().key.isValid()) {
      this.error('');
      this.success(false);
      try {
        await this.linkPreview().changeLink();
        this.success(true);
        this.linkModel.unsubscribeAll();
        this.linkModel.subscribeAll(this.linkPreview().key);
        this.linkModel.isChanged(false);
      } catch(e) {
        let errors = e.responseJSON.errors;
        this.error(errors.newKey);
      }
    }
  }

  generateClientLinks() {
    if (!HAS_CONTACTS) {
      this.info({
        text: `Уникальные ссылки генерируются для списка контактов. Функция будет доступна при добавлении хотя бы одного <a href="/foquz/foquz-contact" target="_blank">контакта</a>`,
      });
      return;
    }
    this.openSidesheet({
      name: "generate-unique-client-links",
      params: {
        linkKey: this.linkModel.link.key(),
        ...this.poll,
        ...(this.filialId() ? { filial_id: this.filialId() } : {}),
        ...(this.linkPreview().lang() ? { lang: this.linkPreview().lang() } : {}),
      }
    });
  }
}
