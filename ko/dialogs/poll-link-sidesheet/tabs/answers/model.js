import "./quota";

export class ViewModel {
  constructor({ tabParams: { footer, callbacks, linkModel, pollLink, blocked, error, success, isValid, isSubmited }}) {
    footer(true)

    this.blocked = blocked;

    this.error = error;
    this.success = success;

    this.pollLink = pollLink; 
    this.linkModel = linkModel;

    this.isSubmited = isSubmited;

    callbacks.submit = this.submit.bind(this);
    callbacks.reset = this.reset.bind(this);

    this.preloader = ko.observable(false);

    this.quotesIsValid = ko.computed(() => {
      const preloader = this.preloader();
      if (preloader) return false;

      const answerQuotes = this.linkModel.answerQuotes();
      const isValid = !answerQuotes.some(quota => !quota.isValid());
      return isValid;
    });

    this.quotesIsValid.subscribe((v) => {
      isValid(v)
    });

    this.init();
  }

  async init() {
    const requests = []
    if (!this.pollLink.questions.length) {
      requests.push(this.pollLink.getQuestions());
    }
    if (this.linkModel.hasAnswerQuotes() && !this.linkModel.answerQuotes().length) {
      requests.push(this.linkModel.getAnswerQuotes());
    }
    this.preloader(true);
    await Promise.all(requests);
    this.linkModel.isDownloadAnswerQuotes(true)
    this.preloader(false);

    this.linkModel.unsubscribeAll();
    this.linkModel.subscribeAll(this.linkModel.answerQuotes);
    this.linkModel.isChanged(false);
  }

  async submit() {
    try {
      await this.linkModel.saveAnswerQuotes();
      this.success(true);

      this.linkModel.unsubscribeAll();
      this.linkModel.subscribeAll(this.linkModel.answerQuotes);
      this.linkModel.isChanged(false);
    } catch(e) {
      console.error(e);
      let errors = e?.responseJSON?.errors;
      this.error(errors);
    }
  }

  async reset() {
    this.linkModel.resetAnswerQuotes();

    this.linkModel.unsubscribeAll();
    this.linkModel.subscribeAll(this.linkModel.answerQuotes);
    this.linkModel.isChanged(false);
  }
}