import './condition';
import { INTER_BLOCK } from 'Data/question-types';

export class ViewModel {
  constructor({ answerQuota, pollLink, linkModel, index, isSubmited }) {
    this.opened = ko.observable(!answerQuota.id());
    this.answerQuota = answerQuota;
    this.linkModel = linkModel;

    this.isSubmited = isSubmited;
    this.isSubmited.subscribe((v) => {
      if (v && !answerQuota.isValid()) {
        this.opened(true);
      }
    })

    this.name = ko.computed(() => {
      const i = index();
      return `Квота ${i + 1}`
    });
    this.index = index;
    this.pollLink = pollLink;
    this.nextPosition = ko.computed(() => {
      const conditions = answerQuota.conditions();
      return conditions.length;
    });

    this.questions = pollLink
      .questions
      .filter(({ main_question_type }) => String(main_question_type) === String(INTER_BLOCK))
      .map(({ id, service_name }, index) => {
        return {
          id: String(id),
          text: service_name || `Конечный экран ${index + 1}`,
        }
      });

    this.endScreenTypes = [
      {
        id: '',
        text: "Стандартное завершение",
      },
      ...this.questions
    ];
    
    this.endScreenId = answerQuota.endScreen.extend({
      required: {
        message: () => 'Обязательное поле',
      },
    });
  }
}