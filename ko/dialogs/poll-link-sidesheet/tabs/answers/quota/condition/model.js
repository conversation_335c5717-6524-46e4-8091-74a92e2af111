import '../criteria'

export class ViewModel {
  constructor({ condition, pollLink, answerQuota, isSubmited, index }) {
    this.name = `${index() + 1}.`;
    this.index = index;
    this.pollLink = pollLink;
    this.isGroup = !!condition.isGroup;
    this.answerQuota = answerQuota;
    this.isSubmited = isSubmited;

    if (this.isGroup) {
      this.conditions = condition.criteria;
      this.condition = condition;
      this.logicOperation = condition.logicOperation;
      this.group = condition;
      this.nextPosition = ko.computed(() => {
        const conditions = this.group.conditions();
        return conditions.length;
      });
    } else {
      this.conditions = [condition];
      this.condition = answerQuota;
    }
  }
}