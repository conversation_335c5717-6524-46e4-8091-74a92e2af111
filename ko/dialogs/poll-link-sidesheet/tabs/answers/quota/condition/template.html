<div>
  <hr class="ml-20p mt-0 mb-25p" />
  <div class="ml-20p d-flex align-items-start mb-25p">
    <div
      class="poll-link-sidesheet-answers-quota-condition__index bold pl-20p"
      data-bind="text: name"
    ></div>
    <div class="flex-grow-1">
      <!-- ko if: isGroup -->
        <div class="d-flex align-items-center justify-content-between mb-10p">
          <div class="bold">
            Логика работы условий в группе
          </div>
          <div>
            <button
              class="poll-link-sidesheet-answers-quota__btn-del f-btn f-btn-text text-nowrap flex-shrink-0"
              data-bind="click: () => answerQuota.deleteCondition(group.id, index())"
              type="button"
            >
              <span class="f-btn-prepend">
                <foquz-icon
                  params="icon: 'bin'"
                  class="f-icon"
                ></foquz-icon>
              </span>
              <span>Удалить группу</span>
            </button>
          </div>
        </div>
        <div class="row mb-25p">
          <div class="col-12 col-lg-6">
            <radio-group
              params="
                options: [
                  { value: 0, label: 'Через И' },
                  { value: 1, label: 'Через ИЛИ' }
                ],
                value: logicOperation,
              "
            ></radio-group>
          </div>
        </div>
      <!-- /ko -->
      <!-- ko template: {
        foreach: conditions,
        afterAdd: slideAfterAddFactory(200),
        beforeRemove: slideBeforeRemoveFactory(200)
      } -->
        <poll-link-sidesheet-answers-quota-criteria
          params="
            criteria: $data,
            pollLink: $component.pollLink,
            answerQuota: $component.condition,
            isSubmited: $component.isSubmited,
            index: $component.isGroup ? $index() : $component.index,
          "
        ></poll-link-sidesheet-answers-quota-criteria>
      <!-- /ko -->
      <!-- ko if: isGroup -->
        <button
          class="f-btn f-btn-success f-btn-text text-nowrap flex-shrink-0 mt-25p"
          data-bind="
            click: () => group.addCriteria(nextPosition()),
            enable: true,
          "
          type="button"
        >
          <span class="f-btn-prepend">
            <foquz-icon
              params="icon: 'plus'"
              class="f-icon f-icon--plus"
            ></foquz-icon>
          </span>
          <span>Добавить условие</span>
        </button>
      <!-- /ko -->
    </div>
  </div>
</div>