import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('poll-link-sidesheet-answers-quota-criteria', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.className = 'poll-link-sidesheet-answers-quota-criteria';

      return new ViewModel(params, element);
    }
  },
  template: html
});
