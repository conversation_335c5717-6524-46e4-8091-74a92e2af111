<div>
  <div class="d-flex align-items-start">
    <div class="poll-link-sidesheet-answers-quota-criteria__card px-20p py-10p flex-grow-1">
      <div class="row">
        <div class="col-12 col-lg-6 py-10p">
          <div class="bold mb-10p">
            Вопрос
          </div>
          <fc-select
            params="
              options: questions,
              value: questionId,
            "
          ></fc-select>
        </div>
        <div class="col-12 col-lg-6 py-10p">
          <div class="bold mb-10p">
            Поведение респондента
          </div>
          <fc-select
            params="
              options: behaviors,
              value: behavior,
              disabled: false,
            "
          ></fc-select>
        </div>
        <!-- ko template: {
          foreach: templateIf(enableVariants(), $data),
          afterAdd: slideAfterAddFactory(200),
          beforeRemove: slideBeforeRemoveFactory(200)
        } -->
          <div class="col-12 py-10p">
            <div class="bold mb-10p">
              Ответы
            </div>
            <fc-select
              params="
                options: variants,
                value: selectedVariants,
                disabled: false,
                multiple: true,
                placeholder: 'Выберите ответы',
                invalid: formControlErrorStateMatcher(selectedVariants)
              "
            ></fc-select>       
            <validation-feedback
              params="
                show: formControlErrorStateMatcher(selectedVariants),
                text: selectedVariants.error
              "
            ></validation-feedback>     
          </div>
        <!-- /ko -->
      </div>
    </div>
    <div class="pl-15p">
      <button
        class="poll-link-sidesheet-answers-quota-criteria__bin f-btn f-btn-text text-nowrap flex-shrink-0"
        data-bind="click: () => deleteCondition(id, index())"
        type="button"
      >
        <foquz-icon
          params="icon: 'bin'"
          class="f-icon"
        ></foquz-icon>
      </button>
    </div>
  </div>
</div>