import { VARIANTS_QUESTION, INTER_BLOCK } from 'Data/question-types';

export class ViewModel {
  constructor({ criteria, pollLink, answerQuota, isSubmited, index }) {
    this.pollLink = pollLink;
    this.criteria = criteria;

    this.id = criteria.id;
    this.index = ko.computed(() => index());
    this.deleteCondition = answerQuota.deleteCondition;
    this.isSubmited = isSubmited;

    this.questions = pollLink
      .questions
      .filter(({ is_deleted }) => !is_deleted)
      .filter(({ main_question_type }) => String(main_question_type) !== String(INTER_BLOCK))
      .map(({ position, ...question }, index) => {
        question.position = index + 1;
        return question;
      })
      .filter(({ main_question_type }) => String(main_question_type) === String(VARIANTS_QUESTION))
      .map(({
        id,
        service_name,
        description,
        position,
        is_self_answer,
        self_variant_text,
        detail_answers,
        donor,
      }) => {
        let variants = [...detail_answers];
        variants = variants.filter(({ is_deleted }) => !is_deleted);
        if (!!is_self_answer) {
          variants.push({
            id: -1,
            variant: self_variant_text || 'Свой вариант',
          });
        }
        variants = variants.map(({ id, variant, question, is_self_answer }, index) => {
          let text = variant || question;
          if (is_self_answer) {
            const donorQuestion = pollLink.questions.find(({ id }) => id === donor);
            text = donorQuestion?.self_variant_text || 'Свой вариант';
          }
          if (!text) {
            text = `Вариант ${index + 1}`;
          }
          return {
            id: String(id),
            text,
          }
        });

        return {
          id: String(id),
          text: `${position}. ${service_name || description}`,
          variants,
        }
      });

    this.questionId = criteria.questionId;
    if (!this.questionId() || (this.questionId() === '0')) {
      this.questionId(this.questions[0].id);
    }

    this.behavior = criteria.behavior;
    this.behaviors = [
      { id: 1, text: "Выбрал варианты" },
      { id: 2, text: "Не выбрал варианты" },
      { id: 3, text: "Пропустил вопрос" },
      { id: 4, text: "Затруднился ответить" },
    ];

    this.variants = ko.computed(() => {
      const questionId = this.questionId();
      const questions = this.questions.find(({ id }) => id === questionId);
      if (questions) {
        return questions.variants;
      } else {
        return [];
      }
    });

    this.enableVariants = ko.computed(() => {
      const behavior = this.behavior();
      const enableVariants = behavior == 1 || behavior == 2;
      return enableVariants;
    });

    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(isSubmited);

    this.selectedVariants = criteria.variants.extend({
      validation: {
        validator: (v) => v && v.length > 0,
        message: () => 'Обязательное поле',
        onlyIf: () => this.enableVariants(),
      },
    });
  }
}