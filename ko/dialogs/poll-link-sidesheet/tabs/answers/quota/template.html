<div>
  <div class="poll-link-sidesheet-answers-quota__header d-flex align-items-center justify-content-between">
    <div
      class="flex-grow-1 py-10p pl-20p cursor-pointer"
      data-bind="click: () => opened(!opened())"
    >
      <div class="d-flex align-items-center">
        <div
          class="f-h2"
          data-bind="text: name"
        ></div>
        <span
          class="f-icon f-icon-arrow f-icon-arrow--top f-transform--transition ml-2"
          data-bind="css: { 'f-transform-rotate-180': !opened() }"
        >
          <svg>
              <use href="#arrow-top-icon"></use>
          </svg>
        </span>
      </div>
      <div
        class="poll-link-sidesheet-answers-quota__count"
        data-bind="text: 'Условий/групп: ' + nextPosition()"
      ></div>
    </div>
    <div class="py-10p pr-20p">
      <button
        class="poll-link-sidesheet-answers-quota__btn-del f-btn f-btn-text text-nowrap flex-shrink-0"
        data-bind="click: () => linkModel.deleteAnswerQuotes(answerQuota.id, index())"
        type="button"
      >
        <span class="f-btn-prepend">
          <foquz-icon
            params="icon: 'bin'"
            class="f-icon"
          ></foquz-icon>
        </span>
        <span>Удалить квоту</span>
      </button>
    </div>
  </div>
  <!-- ko template: {
    foreach: templateIf(opened(), $data),
    afterAdd: slideAfterAddFactory(200),
    beforeRemove: slideBeforeRemoveFactory(200)
  } -->
    <div>
      <div class="pt-25p pl-20p">
        <div class="row">
          <div class="col-12 col-lg-4 mb-25p">
            <div class="bold mb-10p">
              Лимит кол-ва ответов
            </div>
            <fc-input
              params="
                value: answerQuota.answersLimit,
                mask: 'numbers',
                placeholder: 'Не ограничено',
                maxlength: 10,
              "
            ></fc-input>
          </div>
          <div class="col-12 col-lg-4 mb-25p">
            <div class="bold mb-10p">
              Логика работы условий/групп
            </div>
            <radio-group
              params="
                options: [
                  { value: 0, label: 'Через И' },
                  { value: 1, label: 'Через ИЛИ' }
                ],
                value: answerQuota.logicOperation,
              "
            ></radio-group>
          </div>
          <div class="col-12 col-lg-4 mb-25p">
            <div class="bold mb-10p">
              Конечный экран
            </div>
            <fc-select
              params="
                options: endScreenTypes,
                value: endScreenId,
              "
            ></fc-select>
          </div>
        </div>
      </div>
      <!-- ko template: {
        foreach: answerQuota.conditions(),
        afterAdd: slideAfterAddFactory(200),
        beforeRemove: slideBeforeRemoveFactory(200)
      } -->
        <poll-link-sidesheet-answers-quota-condition
          params="
            condition: $data,
            pollLink: $component.pollLink,
            answerQuota: $component.answerQuota,
            isSubmited: $component.isSubmited,
            index: $index(),
          "
        ></poll-link-sidesheet-answers-quota-condition>
      <!-- /ko -->
      <hr class="ml-20p mb-25p mt-0" />
      <div class="d-flex ml-40p mb-25p">
        <button
          class="f-btn f-btn-success f-btn-text text-nowrap flex-shrink-0 mr-20p"
          data-bind="
            click: () => answerQuota.addCriteria(nextPosition()),
            enable: true,
          "
          type="button"
        >
          <span class="f-btn-prepend">
            <foquz-icon
              params="icon: 'plus'"
              class="f-icon f-icon--plus"
            ></foquz-icon>
          </span>
          <span>Добавить условие</span>
        </button>
        <button
          class="f-btn f-btn-success f-btn-text text-nowrap flex-shrink-0"
          data-bind="
            click: () => answerQuota.addGroup(nextPosition()),
            enable: true,
          "
          type="button"
        >
          <span class="f-btn-prepend">
            <foquz-icon
              params="icon: 'plus'"
              class="f-icon f-icon--plus"
            ></foquz-icon>
          </span>
          <span>Добавить группу условий</span>
        </button>
      </div>
    </div>
  <!-- /ko -->
</div>
