import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('poll-link-sidesheet-answers', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.className = 'poll-link-sidesheet-answers flex-grow-1 d-flex flex-column';

      return new ViewModel(params, element);
    }
  },
  template: html
});
