<div class="position-relative flex-grow-1 d-flex flex-column">
  <!-- ko if: preloader -->
    <div class="sender-page__loader">
      <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
    </div>
  <!-- /ko -->
  <!-- ko ifnot: preloader -->
   <!-- ko if: pollLink.allowCreateQuote -->
    <div class="d-flex justify-content-end mb-20p">
      <button
        class="button-add"
        data-bind="click: () => linkModel.addAnswerQuotes()"
      >
        Добавить квоту
      </button>
    </div>
    <!-- ko if: linkModel.answerQuotes().length -->
      <!-- ko template: {
        foreach: linkModel.answerQuotes,
        afterAdd: slideAfterAddFactory(200),
        beforeRemove: slideBeforeRemoveFactory(200)
      } -->
        <poll-link-sidesheet-answers-quota
          class=""
          params="
            pollLink: $component.pollLink,
            linkModel: $component.linkModel,
            answerQuota: $data,
            isSubmited: $component.isSubmited,
            index: $index(),
          "
        ></poll-link-sidesheet-answers-quota>
      <!-- /ko -->
      <div class="d-flex justify-content-end mt-20p">
        <button
          class="button-add"
          data-bind="click: () => linkModel.addAnswerQuotes()"
        >
          Добавить квоту
        </button>
      </div>
    <!-- /ko -->
    <!-- ko ifnot: linkModel.answerQuotes().length -->
      <div class="poll-link-sidesheet-answers__placeholder flex-grow-1 d-flex flex-column align-items-center justify-content-center">
        <div class="text-center">
          Квот по ответам для ссылки пока не добавлено
        </div>
      </div>
    <!-- /ko -->
  <!-- /ko -->
    <!-- ko ifnot: pollLink.allowCreateQuote -->
      <div class="poll-link-sidesheet-answers__placeholder flex-grow-1 d-flex flex-column align-items-center justify-content-center">
        <div class="text-center">
          Чтобы добавить квоту по ответам, нужно создать хотя бы один вопрос типа
          <strong class="bold">Варианты ответов</strong>
        </div>
      </div>
    <!-- /ko -->
  <!-- /ko -->
</div>