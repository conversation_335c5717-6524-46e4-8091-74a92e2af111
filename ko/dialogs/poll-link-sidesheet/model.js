
import "@/presentation/views/fc-copy-input";
import "Components/foquz-share";
import "./tabs/links";
import "./tabs/quotas";
import "./tabs/answers";
import "Dialogs/generate-unique-client-links";
import { DialogWrapper } from 'Dialogs/wrapper';
import { DialogsModule } from "Utils/dialogs-module";

// bild

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);    

    this.linkModel = params.linkModel;
    this.linkName = ko.observable(this.linkModel.linkName());
    this.editingName = ko.observable(false);
    this.activeTab = ko.observable('settings');
    this.isSubmited = ko.observable(false);

    this.callbacks = {
      submit: null,
      reset: null,
    };

    this.confirmNotChanged = this.confirmNotChanged.bind(this);
    this.beforeHide = this.beforeHide.bind(this);

    this.tabParams = {
      error: ko.observable(""),
      success: ko.observable(false),
      blocked: params.blocked,
      pollLink: params.pollLink,
      linkModel: this.linkModel,
      footer: ko.observable(false),
      isValid: ko.observable(true),
      isSubmited: this.isSubmited,
      callbacks: this.callbacks,
      confirmNotChanged: this.confirmNotChanged,
    }

    this.tabParams.error.subscribe((v) => {
      if (v) setTimeout(() => this.tabParams.error(''), 3000);
    });
    
    this.tabParams.success.subscribe((v) => {
      if (v) setTimeout(() => this.tabParams.success(false), 3000);
    });

    this.isValid = ko.computed(() => {
      const isSubmited = this.isSubmited();
      const isValid = this.tabParams.isValid();

      return !isSubmited || isValid;
    })
  }

  async beforeHide() {
    if (this.linkModel.isChanged()) {
      await this.confirmNotChanged();
      this.linkModel.unsubscribeAll();

      this.linkModel.resetAnswerQuotes();
      this.linkModel.resetLinkQuotes();
      
      this.linkModel.isChanged(false);
    }
  }

  async confirmNotChanged() {
    return this.confirm({
      title: "Данные не сохранены",
      text: "<span class='review-edit-confirm'>Данные редактирования текущей квоты не сохранены и будут потеряны без возможности восстановления.<span>",
      confirm: "Перейти",
    });
  }

  async nextTab(tab) {
    await this.beforeHide();
    this.activeTab(tab);
  }

  async submit() {
    this.isSubmited(true);
    if (!this.tabParams.isValid()) return;

    const callback = this.callbacks.submit;
    if (typeof callback === 'function') {
      await callback();
    }
    this.isSubmited(false);
  }

  async reset() {
    const callback = this.callbacks.reset;
    if (typeof callback === 'function') {
      await callback();
    }
  }
}