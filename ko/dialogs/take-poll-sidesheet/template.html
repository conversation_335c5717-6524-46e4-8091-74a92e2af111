<sidesheet params="ref: modal, dialogWrapper: $component">

  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title"
          data-bind="text: _t('answers', 'Пройти опрос за контакта')">
      </h2>
    </div>
  </div>


  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container"
           data-bind="using: table">
        <div class="row mb-1 mb-md-3">
          <div class="col-12 col-md-8 col-lg-6">
            <!-- ko if: allPolls -->
            <div class="form-group">
              <label class="form-label"
                     data-bind="text: _t('Опрос')"></label>

              <!-- ko ifnot: pollsDirectory.loaded -->
              <div class="text-center p-4">
                <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
              </div>
              <!-- /ko -->

              <!-- ko if: pollsDirectory.loaded -->
              <div class="select2-wrapper">
                <select data-bind="value: pollId,
                lazySelect2: {
                  containerCssClass: 'form-control',
                  wrapperCssClass: 'select2-container--form-control',
                  minimumResultsForSearch: 0,
                  dropdownAutoWidth: false,
                  placeholder: _t('Выберите опрос')
                }"
                        style="width: 100%">
                  <option></option>
                  <!-- ko foreach: pollsDirectory.data -->
                  <option data-bind="value: id, text: name"></option>
                  <!-- /ko -->
                </select>
              </div>
              <!-- /ko -->
            </div>
            <!-- /ko -->

            <!-- ko if: pollId -->
            <div class="form-group">
              <div class="f-input-group">
                <div class="f-input-group__prepend">
                  <span class="f-icon f-icon--search">
                    <svg>
                      <use href="#search-icon"></use>
                    </svg>
                  </span>
                </div>

                <!-- ko if: query -->
                <div class="f-input-group__append cursor-pointer"
                     data-bind="click: function() {
                  query('');
                  load();
                }, tooltip, tooltipText: _t('Отменить')">
                  <span class="f-icon f-icon--times f-icon-sm f-icon-danger">
                    <svg>
                      <use href="#times-icon"></use>
                    </svg>
                  </span>
                </div>
                <!-- /ko -->

                <input type="text"
                       class="form-control"
                       data-bind="textInput: query, onEnter: function() { load(); }, attr: {
                  placeholder: hasOrder() ? _t('Поиск контакта по ФИО, телефону, email или номеру заказа') : _t('Поиск контакта по ФИО, телефону или email')
                }"
                       autocomplete="off">
              </div>
            </div>

            <!-- ko if: isManual -->
            <div class="mb-20p">
              <a class="f-btn f-btn-lg f-btn-base take-poll-sidesheet__anon"
                 data-bind="attr: {
                href: '/p/' + pollKey() + '?kiosk=1',

              }, text: _t('answers', 'Пройти анонимно')"
                 target="_blank"></a>
            </div>
            <!-- /ko -->
            <!-- /ko -->
          </div>
        </div>

        <!-- ko if: pollId() && isLoaded() -->
        <!-- ko let: { $table: $data }-->
        <media-query params="query: 'tablet+'">
          <!-- ko using: $table -->
          <table class="table f-table">
            <thead>
              <tr>
                <th width="190"
                    data-bind="text: _t('ФИО контакта')"></th>
                <th width="100"
                    data-bind="text: _t('Телефон')"></th>
                <th data-bind="text: _t('Email')"></th>

                <!-- ko if: hasOrder() -->
                <th width="80"
                    data-bind="text: _t('№ заказа')"></th>
                <th width="150"
                    data-bind="text: _t('Дата и время заказа')"></th>
                <th width="120"
                    align="right"
                    data-bind="text: _t('Сумма заказа')"></th>
                <!-- /ko -->

                <th width="0"></th>
              </tr>
            </thead>

            <tbody>
              <!-- ko foreach: items -->
              <tr>
                <td valign="middle"
                    data-bind="text: name"></td>
                <td valign="middle"
                    data-bind="text: phone"
                    class="nowrap"></td>
                <td valign="middle"
                    data-bind="html: $table.emailToHtml(email)"></td>

                <!-- ko if: $parent.hasOrder() -->
                <td valign="middle"
                    data-bind="text: orderNumber"></td>
                <td valign="middle"
                    data-bind="text: orderDate"></td>
                <td valign="middle"
                    align="right"
                    data-bind="text: orderSum + '₽'"></td>
                <!-- /ko -->

                <td valign="middle"
                    align="right"
                    class="py-2">
                  <!-- ko ifnot: $parent.hasOrder -->
                  <a class="f-btn f-btn-lg text-nowrap"
                     data-bind="attr: {
                      href: '/foquz/answers/as-client-link?pollId=' + $parent.pollId() + '&contactId=' + id,

                    }, text: _t('Пройти опрос')"
                     target="_blank"></a>
                  <!-- /ko -->

                  <!-- ko if: $parent.hasOrder -->
                  <a class="f-btn f-btn-lg text-nowrap"
                     data-bind="attr: {
                      href: '/foquz/answers/as-client-link?pollId=' + $parent.pollId() + '&mailingListSendId=' + mailing_list_send_id + '&contactId=' + clientId,

                    }, text: _t('Пройти опрос')"
                     target="_blank"></a>
                  <!-- /ko -->
                </td>
              </tr>
              <!-- ko if: $data.error -->
              <tr>
                <td class="p-0"
                    style="border-top: 0"
                    align="right"
                    data-bind="attr: {
                    colspan: $parent.hasOrder() ? 7 : 4
                  }">
                  <div class="form-error mb-1"
                       style="text-align: right;"
                       data-bind="text: $data.error"></div>
                </td>
              </tr>
              <!-- /ko -->
              <!-- /ko -->
            </tbody>
          </table>

          <!-- ko ifnot: items().length -->
          <div class="f-color-service text-center pt-2"
               data-bind="text: _t('Ничего не найдено')"></div>
          <!-- /ko -->
          <!-- /ko -->
        </media-query>

        <media-query params="query: 'mobile'">
          <!-- ko using: $table -->
          <div class=" mobile-table "
               data-bind="nativeScrollbar">

            <!-- ko ifnot: items().length -->
            <div class="f-color-service text-center pt-2"
                 data-bind="text: _t('Ничего не найдено')"></div>
            <!-- /ko -->

            <!-- ko if: items().length -->
            <table class="fixed-table f-color-text f-border-top"
                   style="min-width: 100%">
              <tbody>
                <tr>
                  <th><span data-bind="text: _t('ФИО контакта')"></span></th>
                  <!-- ko foreach: items -->
                  <td data-bind="text: name"></td>
                  <!-- /ko -->
                </tr>
                <tr>
                  <th><span data-bind="text: _t('Телефон')"></span></th>
                  <!-- ko foreach: items -->
                  <td data-bind="text: phone"></td>
                  <!-- /ko -->
                </tr>
                <tr>
                  <th><span data-bind="text: _t('Email')"></span></th>
                  <!-- ko foreach: items -->
                  <td data-bind="html: $table.emailToHtml(email)"></td>
                  <!-- /ko -->
                </tr>
                <!-- ko if: hasOrder() -->
                <tr>
                  <th><span data-bind="text: _t('№ заказа')"></span></th>
                  <!-- ko foreach: items -->
                  <td data-bind="text: orderNumber"></td>
                  <!-- /ko -->
                </tr>
                <tr>
                  <th><span data-bind="text: _t('Дата и время заказа')"></span></th>
                  <!-- ko foreach: items -->
                  <td data-bind="text: orderDate"></td>
                  <!-- /ko -->
                </tr>
                <tr>
                  <th><span data-bind="text: _t('Сумма заказа')"></span></th>
                  <!-- ko foreach: items -->
                  <td data-bind="text: orderSum + '₽'"></td>
                  <!-- /ko -->
                </tr>
                <!-- /ko -->
                <tr>
                  <th></th>
                  <!-- ko foreach: items -->
                  <td>
                    <!-- ko ifnot: $parent.hasOrder -->
                    <a class="f-btn"
                       data-bind="attr: {
                        href: '/foquz/answers/as-client-link?pollId=' + $parent.pollId() + '&contactId=' + id,

                      }, text: _t('Пройти опрос')"
                       target="_blank"></a>
                    <!-- /ko -->

                    <!-- ko if: $parent.hasOrder -->
                    <a class="f-btn"
                       data-bind="attr: {
                        href: '/foquz/answers/as-client-link?pollId=' + $parent.pollId() + '&mailingListSendId=' + mailing_list_send_id + '&contactId=' + clientId,

                      }, text: _t('Пройти опрос')"
                       target="_blank"></a>
                    <!-- /ko -->
                  </td>
                  <!-- /ko -->
                </tr>
              </tbody>
            </table>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </media-query>
        <!-- /ko -->
        <!-- /ko -->
      </div>
    </div>
  </div>


</sidesheet>
