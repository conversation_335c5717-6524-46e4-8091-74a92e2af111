import { InteractiveTable } from 'Models/interactive-table';
import { isOrderTrigger } from 'Data/triggers';
import { emailToHtml } from 'Utils/string/email-to-html';
export class Table extends InteractiveTable {
  constructor(params) {
    super();
    let data = params.data || {};

    this.pollId = ko.observable(data.pollId);
    this.hasOrder = ko.observable(data.hasOrder);
    this.isManual = ko.observable(false);
    this.pollKey = ko.observable('');
    this.allPolls = !data.pollId;

    this.query = ko.observable('');

    this.isLoaded = ko.observable(false);

    if (this.allPolls) {
      this.pollsDirectory = new Directory('poll');
      this.pollsDirectory.load();
      this.pollId.subscribe((v) => {
        if (!v) return;
        this.items([]);
        this.query('');
        this.isLoaded(false);
        let pollData = this.pollsDirectory.getById(v);
        this.hasOrder(pollData.is_auto && isOrderTrigger(pollData.trigger));
        this.pollKey(pollData.key);
        this.isManual(!pollData.is_auto);
      });
    }
  }

  load() {
    let query = this.query().trim();
    this.query(query);
    this.onReset();

    if (!query) {
      this.isLoaded(false);
      return Promise.resolve(false);
    }

    return new Promise((res) => {
      if (this.beforeLoad()) {
        let url = this.hasOrder()
          ? `${
              APIConfig.baseApiUrlPath
            }answers/client-answers?pollId=${this.pollId()}`
          : `${APIConfig.baseApiUrlPath}answers/client-search`;

        $.ajax({
          url,
          method: 'GET',
          data: {
            'access-token': APIConfig.apiKey,
            q: this.query()
          },
          success: (data) => {
            let items = data.items.map((i) => {
              let order = i.order || {};
              return {
                name: i.name,
                email: i.email,
                phone: i.phone,
                id: i.id,
                clientId: i.clientId,
                orderNumber: order.number,
                orderDate: order
                  ? moment(order.date, 'DD.MM.YYYY HH:mm').format(
                      'DD.MM.YYYY HH:mm'
                    )
                  : null,
                orderSum: order.sum || 0,
                link: i.link,
                mailing_list_send_id: i.mailing_list_send_id,
                error: ko.observable('')
              };
            });

            this.afterLoad(items);
            this.isLoaded(true);
          },
          error: () => {
            this.onError();
            res(false);
          }
        });
      } else {
        res(false);
      }
    });
  }

  emailToHtml(email) {
    return emailToHtml(email);
  }
}
