<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">Управление настройками отображения</h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <!-- ko ifnot: inited -->
    <div style="height: 100%; display: flex; justify-content: center; align-items: center;"><fc-spinner class="f-color-primary"></fc-spinner></div>
    <!-- /ko -->

    <!-- ko if: inited -->

    <!-- ko if: empty -->
    <div style="height: 100%; display: flex; justify-content: center; align-items: center;">Сохраненных настроек пока нет</div>
    <!-- /ko -->

    <!-- ko ifnot: empty -->
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container">
        <table class="table f-table f-table-dense w-100 mt-40p">
          <thead>
            <tr>
              <th>
                <table-head-cell
                  params="sort: sort, sortName: 'name', searchValue: search.name, onSearch: function() {
                        applyFilters()
                      },"
                  >Название</table-head-cell
                >
              </th>
              <th width="20%">
                <table-head-cell
                  params=" sort: sort, sortName: 'author', searchValue: search.author, onSearch: function() {
                        applyFilters()
                      }"
                  >Владелец</table-head-cell
                >
              </th>
              <th width="200">
                <table-head-cell
                  params=" sort: sort, sortName: 'updated', searchValue: search.updated, onSearch: function() {
                        applyFilters()
                      }"
                  >Последнее изменение</table-head-cell
                >
              </th>
              <th width="60"></th>
            </tr>
          </thead>
          <tbody>
            <!-- ko foreach: presets -->
            <tr>
              <td valign="middle">
                <a
                  data-bind="text: $data.name, attr: {
                  href: '/foquz/answers?preset=' + $data.id
                }"
                ></a>
              </td>
              <td valign="middle">
                <div>
                  <span data-bind="text: $data.author.name"></span>
                  <!-- ko if: $data.author.id == $parent.currentUserId -->
                  <span>(вы)</span>
                  <!-- /ko -->
                </div>
              </td>
              <td valign="middle">
                <div data-bind="text: $data.updated"></div>
              </td>
              <td align="right" valign="middle">
                <div class="d-flex align-items-center">
                  <!-- ko if: $data.author.id == $parent.currentUserId -->
                  <fc-button
                    params="mode: 'text', size: 'auto', icon: { name: 'share' }, color: 'secondary',
                  click: function() {
                    $parent.sharePreset($data);
                  }"
                  ></fc-button>
                  <fc-button
                    class="ml-3"
                    params="mode: 'text', size: 'auto', icon: { name: 'bin' }, color: 'secondary',
                  click: function() {
                    $parent.removePreset($data);
                  }"
                  ></fc-button>
                  <!-- /ko -->
                </div>
              </td>
            </tr>
            <!-- /ko -->
          </tbody>
        </table>

        <fc-intersection-watcher
          data-bind="event: {
          'bottom.show.intersect': function() {
            nextPage()
          }
        }"
        ></fc-intersection-watcher>

        <!-- ko if: loading -->
        <fc-spinner class="f-color-primary"></fc-spinner>
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->

    <!-- /ko -->
  </div>
</sidesheet>
