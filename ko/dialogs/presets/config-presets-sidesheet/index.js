import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";

import "../share-preset-dialog";
import "../remove-preset-dialog";

ko.components.register("config-presets-sidesheet", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("config-presets-sidesheet");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
