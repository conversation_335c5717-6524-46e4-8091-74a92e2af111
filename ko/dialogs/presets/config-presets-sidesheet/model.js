import { DialogWrapper } from "@/dialogs/wrapper";
import {
  getPresetSettings,
  getPresetsListRequest,
} from "@/api/answers/presets";
import { currentUserId } from "@/api/user";
import { DialogsModule } from "@/utils/dialogs-module";
import { SortModel } from "../../../models/sort";
import { toAnswers } from "@/router/answers"

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);

    this.inited = ko.observable(false);

    this.empty = ko.observable(false);
    this.loading = ko.observable(true);
    this.loadingPreset = ko.observable(false);

    this.page = ko.observable(1);
    this.sort = new SortModel("name", true);

    this.sort.on("sort", () => {
      this.applyFilters();
    });

    this.lastPage = ko.observable(false);
    this.currentUserId = currentUserId;

    this.presets = ko.observableArray([]);

    this.search = {
      name: ko.observable(""),
      author: ko.observable(""),
      updated: ko.observable(""),
    };

    this.load();
  }

  getParams() {
    return {
      page: this.page(),
      sort: this.sort.sort(),
      name: this.search.name(),
      author: this.search.author(),
      updated: this.search.updated(),
    };
  }

  load() {
    this.loading(true);
    const params = this.getParams();

    getPresetsListRequest(params)
      .then((data) => {
        const { items, _meta } = data;

        if (_meta.pageCount === _meta.currentPage) {
          this.lastPage(true);
        }

        this.presets([...this.presets(), ...items]);

        if (!this.inited()) {
          this.empty(items.length === 0);
        }
      
        this.inited(true);
      })
      .finally(() => {
        this.loading(false);
      });
  }

  nextPage() {
    if (this.loading()) return;
    if (this.lastPage()) return;
    this.page(this.page() + 1);
    this.load();
  }

  applyFilters() {
    this.lastPage(false);
    this.presets([]);
    this.page(1);
    this.load();
  }

  removePreset(preset) {
    this.openDialog({
      name: "remove-preset-dialog",
      params: {
        presetId: preset.id,
        onRemoved: () => {
          this.presets.remove((p) => p.id === preset.id);
        },
      },
    });
  }

  sharePreset(preset) {
    this.openDialog({
      name: "share-preset-dialog",
      params: {
        presetId: preset.id,
      },
    });
  }

  applyPreset(preset) {
    location.href = toAnswers({ preset: preset.id });
  }
}
