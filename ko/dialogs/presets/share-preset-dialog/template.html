<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    Поделиться настройками
    <!-- ko if: $parent.presetData -->
    <div
      class="font-weight-normal f-fs-2"
      data-bind="text: '«' + $parent.presetData().name + '»'"
    ></div>
    <!-- /ko -->
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <!-- ko if: loading() -->
    <fc-spinner class="f-color-primary"></fc-spinner>
    <!-- /ko -->

    <!-- ko ifnot: loading()  -->
    <div class="form-group">
      <fc-label params="text: 'Пользователи'"></fc-label>
      <fc-select
        params="options: filteredUsers, value: selectedUsers, multiple: true, searchable: true"
      >
      </fc-select>
    </div>

    <div data-bind="let: {open: ko.observable(true)}">
      <div class="d-flex align-items-center">
        <fc-label params="text: 'У кого уже есть доступ'"> </fc-label>

        <fc-expander params="open: open" class="ml-15p"></fc-expander>
      </div>

      <!-- ko template: {
         foreach: templateIf(open(), $data),
         afterAdd: slideAfterAddFactory(400),
         beforeRemove: slideBeforeRemoveFactory(400)
      } -->
      <div>
        <table class="table f-table f-table-dense preset-users">
          <tbody>
            <!-- ko foreach: filteredPresetUsers -->
            <!-- ko component: {
              name: 'fc-preset-user',
              params: {
                user: $data,
                authorId: $parent.presetAuthorId,
                users: $parent.usersList,
                unlink: function(userId) {
                  $parent.unlinkUser(userId)
                }
              }
            } -->
            <!-- /ko -->
            <!-- /ko -->
          </tbody>
        </table>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="label: 'Отменить', inverse: true, color: 'primary',
      click: function() { hide() }"
        class="px-2"
      ></fc-button>

      <fc-button
        params="label: 'Сохранить', color: 'success',
      click: function() { save() }, pending: pending,
      disabled: pending() || !hasChanges()"
      ></fc-button>
    </div>

    <fc-success
      params="show: showSuccessMessage, text: 'Сохранено успешно'"
    ></fc-success>
  </div>
</foquz-dialog>
