import { currentUserId } from "@/api/user";
import { User } from "@/entities/models/user/types";

interface PresetUserProps {
  user: {
    id: string;
    username: string;
  };
  authorId: KnockoutObservable<string | null>;
  users: KnockoutObservableArray<User>;
  unlink: (userId: string) => void;
}

export function ViewModel(params: PresetUserProps, element: HTMLElement) {
  const { user, authorId, users, unlink } = params;

  console.log({ user, authorId, users });

  const id = user.id.toString();

  const userData = ko.computed(() => {
    const list = users();

    if (!list) return null;

    const model = list.find((u) => u.id.toString() === id);
    console.log({ model });
    return model;
  });

  return {
    name: user.username,
    avatar: ko.computed(() => {
      return userData()?.avatar;
    }),
    isAuthor: ko.computed(() => {
      return id === authorId();
    }),
    isCurrentUser: id === currentUserId.toString(),
    roleName: ko.computed(() => {
      return userData()?.roleName;
    }),
    unlink: function () {
      if (typeof unlink === "function") {
        unlink(id);
      }
    },
  };
}
