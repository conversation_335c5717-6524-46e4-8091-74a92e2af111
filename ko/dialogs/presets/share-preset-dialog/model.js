import { DialogWrapper } from "Dialogs/wrapper";
import { useApiWrapper } from "@/api/utils/use-api-wrapper";
import { getPresetInfo } from "@/api/answers/presets";
import { getUsersList } from "@/api/company/users";
import { sharePreset } from "@/api/answers/presets";
import { UserRoles } from "@/constants/users/roles";
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    const { presetId } = params;

    const { data: users, loading: usersLoading } = useApiWrapper(
      getUsersList,
      (list) => {
        return list
          .filter((user) => {
            return [
              UserRoles.Admin,
              UserRoles.Editor,
              UserRoles.FilialEmployee,
            ].includes(user.role);
          })
          .map((user) => {
            return {
              ...user,
              text: user.username,
            };
          });
      }
    )();

    this.presetLoading = ko.observable(true);
    this.usersLoading = usersLoading;

    this.loading = ko.pureComputed(
      () => this.presetLoading() || this.usersLoading()
    );

    this.usersList = users;
    this.presetUsersList = ko.observableArray([]);
    this.presetData = ko.observable(null);
    this.presetAuthorId = ko.computed(() => {
      return this.presetData()?.author.id.toString();
    });

    this.isSubmitted = ko.observable(false);
    this.pending = ko.observable(false);
    this.showSuccessMessage = ko.observable(false);

    this.selectedUsers = ko.observableArray([]);
    this.unlinked = ko.observableArray([]);

    this.filteredPresetUsers = ko.computed(() => {
      if (this.loading()) return [];

      const authorData = this.presetData().author;

      const unlinked = this.unlinked();
      const users = this.presetUsersList().filter(
        (user) => !unlinked.includes(user.id.toString())
      );

      return [{ id: authorData.id, username: authorData.name }, ...users];
    });

    this.filteredUsers = ko.computed(() => {
      if (this.loading()) return [];

      const presetData = this.presetData();
      const presetsUsersIds = this.filteredPresetUsers().map((user) =>
        user.id.toString()
      );

      return (users() || []).filter((user) => {
        if (user.id == presetData.author.id) return false;
        if (presetsUsersIds.includes(user.id.toString())) return false;
        return true;
      });
    });

    this.hasChanges = ko.computed(() => {
      return this.selectedUsers().length || this.unlinked().length;
    });

    getPresetInfo(presetId)
      .then((data) => {
        const { preset, users } = data;
        this.presetData(preset);

        const author = preset.author;

        const list = users.filter(
          (u) => u.id.toString() !== author.id.toString()
        );
        this.presetUsersList(list);
      })
      .finally(() => {
        this.presetLoading(false);
      });
  }

  unlinkUser(userId) {
    this.unlinked.push(userId);
  }

  save() {
    if (!this.hasChanges()) return;

    this.isSubmitted(true);
    this.pending(true);

    sharePreset(this.presetData().id, this.selectedUsers(), this.unlinked())
      .then((data) => {
        const { share, unlink } = data;
        const successUnlink = unlink.success.map((id) => id.toString());
        const successShare = share.success;

        this.selectedUsers([]);
        this.unlinked([]);

        let presetUsers = this.presetUsersList();
        presetUsers = presetUsers.filter((user) => {
          return !successUnlink.includes(user.id.toString());
        });

        const users = this.usersList();

        successShare.forEach((id) => {
          const user = users.find((u) => u.id == id);
          if (user) {
            presetUsers.push({
              id: user.id,
              username: user.username,
            });
          }
        });

        this.presetUsersList(presetUsers);

        this.showSuccessMessage(true);
      })
      .finally(() => {
        this.pending(false);
      });
  }
}
