import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";

import "./preset-user";

ko.components.register("share-preset-dialog", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("share-preset-dialog");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
