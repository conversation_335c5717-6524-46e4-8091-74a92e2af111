import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import '../after-save-preset-dialog'

ko.components.register('save-preset-dialog', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('save-preset-dialog');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
