<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header> Сохранить настройки </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <fc-radio-group params="options: actions, value: action"></fc-radio-group>
    </div>

    <!-- ko if: action() === 'create' -->
    <div>
      <fc-label params="text: 'Название настроек', hint: ''"></fc-label>
      <fc-input
        params="value: name, counter: true, maxlength: 30, placeholder: 'Введите название',
        invalid: isSubmitted() && !name.isValid()"
      ></fc-input>
      <fc-error
        params="show: isSubmitted() && !name.isValid(), text: name.error"
      ></fc-error>
    </div>
    <!-- /ko -->

    <!-- ko if: action() === 'update' -->
    <!-- ko if: noPresets -->
    <div class="f-color-service">Сохранённых настроек для фильтров нет</div>
    <!-- /ko -->
    <!-- ko ifnot: noPresets -->
    <div>
      <fc-label params="text: 'Настройки'"></fc-label>
      <fc-select
        params="options: presets, value: preset, clearable: true,
        invalid: isSubmitted() && !preset.isValid(), placeholder: 'Выберите сохранённые настройки'"
      ></fc-select>
      <fc-error
        params="show: isSubmitted() && !preset.isValid(), text: preset.error"
      ></fc-error>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko template: {
       foreach: templateIf(errors().length, $data),
       afterAdd: fadeAfterAddFactory(400),
       beforeRemove: fadeBeforeRemoveFactory(400)
    } -->
    <div class="mt-15p">
      <!-- ko foreach: errors -->
      <div class="d-flex align-items-center mb-10p">
        <fc-icon
          params="name: 'exclamation-circle-filled'"
          class="mr-15p"
        ></fc-icon>
        <div data-bind="text: $data" class="fs-1-2 f-color-danger"></div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="label: 'Отменить', inverse: true, color: 'primary',
      click: function() { hide() }"
        class="px-2"
      ></fc-button>

      <fc-button
        params="label: 'Сохранить', color: 'success',
      click: function() { save() },
      disabled: pending() || action() === 'update' && noPresets(),
      pending: pending,"
      ></fc-button>
    </div>
  </div>
</foquz-dialog>
