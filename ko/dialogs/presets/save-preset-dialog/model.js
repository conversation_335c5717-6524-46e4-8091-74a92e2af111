import { DialogWrapper } from "@/dialogs/wrapper";
import { useApiWrapper } from "@/api/utils/use-api-wrapper";
import { getPresetsList } from "@/api/answers/presets";
import { ERROR_TEXT } from "@/constants/validators/errors";
import { createPreset, updatePreset } from "@/api/answers/presets";
import { currentUserId } from "@/api/user/index";
import { DialogsModule } from "@/utils/dialogs-module";
import { MultipleError } from "@/utils/error/multiple-error";
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);

    this.isSubmitted = ko.observable(false);
    this.pending = ko.observable(false);
    this.errors = ko.observableArray(null);

    const { data, onCreated, onUpdated } = params;
    this.data = data;
    this.onCreated = onCreated;
    this.onUpdated = onUpdated;

    this.actions = [
      { id: "create", label: "Как новые" },
      { id: "update", label: "Обновить" },
    ];
    const { data: presets, loading } = useApiWrapper(getPresetsList, (list) => {
      const result = list
        .filter((preset) => {
          return preset.author.id.toString() === currentUserId.toString();
        })
        .map((preset) => {
          return {
            id: preset.id,
            text: preset.name,
          };
        });
        result.sort((a, b) => a.text.toUpperCase() < b.text.toUpperCase() ? -1 : 1);
        return result;
    })();
    this.presets = presets;
    this.loadingPresets = loading;
    this.noPresets = ko.pureComputed(() => {
      return !loading() && (!presets() || !presets().length);
    });

    this.action = ko.observable("create");
    this.name = ko.observable("").extend({
      required: {
        message: ERROR_TEXT.required,
        onlyIf: () => this.action() === "create",
      },
    });
    this.preset = ko.observable(null).extend({
      required: {
        message: ERROR_TEXT.required,
        onlyIf: () => this.action() === "update",
      },
    });

    this.blockSubmit = ko.computed(() => {
      return this.action() === 'update' && !this.preset()
    })

    const sb = presets.subscribe((v) => {
      if (v) {
        sb.dispose();
        this.preset(v[0]?.id);
      }
    });
  }

  save() {
    this.errors([]);
    this.isSubmitted(true);
    if (!this.name.isValid() || !this.preset.isValid()) return;

    this.pending(true);

    let request;

    if (this.action() === "create") {
      request = createPreset(this.name(), this.data).then((data) => {
        if (typeof this.onCreated === "function") this.onCreated();
        return data;
      });
    } else {
      const id = this.preset();

      const preset = this.presets().find((p) => p.id === id);
      console.log({ preset })
      request = updatePreset(id, preset.text, this.data).then((data) => {
        if (typeof this.onUpdated === "function") this.onUpdated();
        return data;
      });
    }

    request
      .then((preset) => {
        this.openDialog({
          name: "after-save-preset-dialog",
          params: {
            preset,
            updated: this.action() === "update",
          },
        });
        this.hide();
      })
      .catch((err) => {
        if (err instanceof MultipleError) {
          this.errors(err.errors);
        } else {
          this.errors(["Что-то пошло не так..."]);
        }
      })
      .finally(() => {
        this.pending(false);
      });
  }
}
