<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header> Применить настройки </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <!-- ko if: noPresets -->
    <div class="f-color-service">
      Нет доступных наборов настроек для раздела
    </div>
    <!-- /ko -->

    <!-- ko ifnot: noPresets -->
    <div>
      <fc-label params="text: 'Настройки'"></fc-label>
      <fc-select params="options: presets, value: preset">
        <template data-slot="option">
          <div data-bind="log: 'option'">
            <div data-bind="text: $data.text"></div>
            <div
              data-bind="text: $data.authorName"
              style="font-size: 11px"
              class="f-color-service"
            ></div>
          </div>
        </template>
      </fc-select>
    </div>
    <!-- /ko -->
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="label: 'Отменить', inverse: true, color: 'primary',
      click: function() { hide() }"
        class="px-2"
      ></fc-button>

      <fc-button
        params="label: 'Применить', color: 'secondary',
      click: function() { apply() },
      disabled: noPresets() || pending(),
      pending: pending"
      ></fc-button>
    </div>
  </div>
</foquz-dialog>
