import { DialogWrapper } from "Dialogs/wrapper";
import { useApiWrapper } from "@/api/utils/use-api-wrapper";
import { getPresetSettings, getPresetsList } from "@/api/answers/presets";
import { getUsersList } from "@/api/company/users";
import { currentUserId } from "@/api/user";
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.pending = ko.observable(false);

    const { onApply } = params;

    const { data: users } = useApiWrapper(getUsersList);
    this.users = users;

    const { data: presets, loading } = useApiWrapper(getPresetsList, (list) => {
      const result = list
        .map((preset) => {
          return {
            id: preset.id,
            text: preset.name,
            authorName: `${preset.author.name} ${
              preset.author.id.toString() === currentUserId ? "(вы)" : ""
            }`,
          };
        });

      result.sort((a, b) => {
        return a.text.toLowerCase() < b.text.toLowerCase() ? -1 : 1;
      });

      return result;
    })();
    this.presets = presets;
    this.loadingPresets = loading;
    this.noPresets = ko.pureComputed(() => {
      return !loading() && (!presets() || !presets().length);
    });

    this.preset = ko.observable(null);

    const sb = presets.subscribe((v) => {
      if (v) {
        sb.dispose();
        this.preset(v[0]?.id);
      }
    });

    this.onApply = onApply;
  }

  getUserName(userId) {
    const list = this.users();
    if (!list) return "";

    const user = list.find((u) => u.id == userId);
    console.log({ userId, list, user });
    return "user";
  }

  apply() {
    this.pending(true);
    getPresetSettings(this.preset()).then((data) => {
      this.hide();
      if (typeof this.onApply === "function") this.onApply(data);
    });
  }
}
