import { DialogWrapper } from "Dialogs/wrapper";
import { useApiWrapper } from "@/api/utils/use-api-wrapper";
import { deletePreset, getPresetInfo } from "@/api/answers/presets";
import { getUsersList } from "@/api/company/users";
import { currentUserId } from "@/api/user";
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    const { presetId, onRemoved } = params;

    this.onRemoved = onRemoved
    this.presetId = presetId

    this.isSubmitted = ko.observable(false);
    this.pending = ko.observable(false);
    this.showSuccessMessage = ko.observable(false);

    const { data: users, loading: usersLoading } =
      useApiWrapper(getUsersList)();


    this.presetLoading = ko.observable(true);
    this.loading = ko.pureComputed(
      () => this.presetLoading() || usersLoading()
    );

    this.presetUsersList = ko.observableArray([]);
    this.presetData = ko.observable(null);

    this.users = ko.computed(() => {
      if (this.loading()) return [];
      const usersList = users();
      return this.presetUsersList().map((user) => {
        const userData = usersList.find(
          (u) => u.id.toString() === user.id.toString()
        );
        return {
          ...userData,
          isCurrentUser: userData.id == currentUserId
        }
      });
    });

    getPresetInfo(presetId)
      .then((data) => {
        const { preset, users } = data;
        this.presetData(preset);

        const author = preset.author;

        const list = users.filter(
          (u) => u.id.toString() !== author.id.toString()
        );
        this.presetUsersList(list);
      })
      .finally(() => {
        this.presetLoading(false);
      });
  }

  submit() {
    this.isSubmitted(true);
    this.pending(true)

    deletePreset(this.presetId).then(() => {
      this.hide()
      if (typeof this.onRemoved === 'function') this.onRemoved()
    }).finally(() => {
      this.pending(false);
    })
  }
}
