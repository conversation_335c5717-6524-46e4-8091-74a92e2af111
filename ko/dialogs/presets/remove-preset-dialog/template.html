<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header> Удаление настроек </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <!-- ko if: loading -->
    <fc-spinner class="f-color-primary"></fc-spinner>
    <!-- /ko -->

    <!-- ko ifnot: loading -->

    <!-- ko if: users().length -->
    <div>
      Настройки
      <span class="bold"
        >«<span data-bind="text: presetData().name"></span>»</span
      > будут удалены также для всех, с кем вы поделились:

      <div class="mt-15p">
        <!-- ko foreach: users -->

        <div class="d-flex align-items-center mb-10p">
          <fc-media
            class="fc-user__avatar"
            params="url: avatar, size: 30"
          ></fc-media>
          <div>
            <div class="font-weight-500 f-fs-2">
              <span data-bind="text: name"></span>
              <!-- ko if: isCurrentUser -->
              <span>(вы)</span>
              <!-- /ko -->
            </div>
            <div
              data-bind="text: roleName"
              style="font-size: 11px"
              class="f-color-service"
            ></div>
          </div>
        </div>

        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->

    <!-- ko ifnot: users().length -->
    <div>
      Настройки
      <span class="bold"
        >«<span data-bind="text: presetData().name"></span>»</span
      >будут удалены.
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="label: 'Отменить', inverse: true, color: 'primary',
      click: function() { hide() }"
        class="px-2"
      ></fc-button>

      <fc-button
        params="label: 'Удалить', color: 'danger',
      click: function() { submit() }, disabled: pending, pending: pending"
      ></fc-button>
    </div>
  </div>
</foquz-dialog>
