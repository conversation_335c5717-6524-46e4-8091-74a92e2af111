import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";

import "../share-preset-dialog";

ko.components.register("after-save-preset-dialog", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("after-save-preset-dialog");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
