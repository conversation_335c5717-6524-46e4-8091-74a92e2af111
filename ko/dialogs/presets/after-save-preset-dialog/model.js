import { DialogWrapper } from "Dialogs/wrapper";
import { DialogsModule } from "@/utils/dialogs-module";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);

    const { updated, preset } = params;

    this.preset = preset;
    this.updated = updated;
  }

  share() {
   
    this.openDialog({
      name: "share-preset-dialog",
      params: {
        presetId: this.preset.id,
      },
    });
    this.hide();
  }
}
