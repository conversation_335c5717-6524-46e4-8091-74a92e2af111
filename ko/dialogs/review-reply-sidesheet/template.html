<sidesheet params="ref: modal, dialogWrapper: $component">


  <div class="foquz-dialog__body"
       data-bind="using: review">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container">
        <div class="d-flex align-items-center py-15p">

          <user params="userpic: userpic, name: userName, link: userLink"
                class="mr-4"></user>

          <!-- ko if: sourceLink -->
          <a class="d-flex align-items-center"
             data-bind="attr: {
                    href: sourceLink,
                }"
             target="_blank"
             rel="noopener noreferrer nofollow">
            <svg-icon params="name: 'map-marker-' + sourceType"
                      class="mr-2 svg-icon--lg"></svg-icon>
            <span data-bind="text: sourceName"></span>
          </a>
          <!-- /ko -->

          <!-- ko ifnot: sourceLink -->
          <div class="d-flex align-items-center">
            <svg-icon params="name: 'map-marker-' + sourceType"
                      class="mr-2 svg-icon--lg"></svg-icon>
            <span data-bind="text: sourceName"></span>
          </div>
          <!-- /ko -->

        </div>
      </div>

      <hr class="m-0">

      <div class="container pt-15p">

        <star-rating params="value: rating, label: true"
                     class="star-rating--lg mb-20p"></star-rating>

        <div class="f-fs-1 f-color-service font-weight-500 mb-2">
          <span data-bind="text: createdAt"></span>
          <span data-bind="text: filial.name"></span>

          <!-- ko if: images.length -->
          <span class="d-inline-flex align-items-center"
                data-bind="fancyGalleryItem: {
                    gallery: images
                }">
            <svg-icon params="name: 'image'"></svg-icon>
            <span data-bind="text: images.length + ' фото'"></span>
          </span>
          <!-- /ko -->

        </div>
        <div class="f-fs-2-5">
          <!-- ko if: comment -->
          <span data-bind="text: comment"></span>
          <!-- /ko -->

          <!-- ko if: sourceLink -->
          <a class="f-fs-1-5"
             data-bind="attr: {
                    href: sourceLink,
                }, text: _t('reviews', 'Перейти к отзыву')"
             target="_blank"
             rel="noopener noreferrer nofollow">

          </a>
          <!-- /ko -->
        </div>

        <!-- ko if: reply -->
        <div class="mt-30p reply" data-bind="using: reply">
          <div class='reply__date'
               data-bind="text: updatedAt"></div>
          <div data-bind="text: comment"></div>
        </div>
        <!-- /ko -->


      </div>

      <!-- ko if: !reply() && !$parent.canReply() -->
      <hr>
      <!-- /ko -->


    </div>
  </div>

  <!-- ko if: canReply -->
  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="container">
      <div class="d-flex align-items-end reply-form">
        <div class="reply-form__field">

          <textarea data-bind="autosizeTextarea, minHeight: 48, maxHeight: 250, textInput: replyText, css: {
            'is-invalid': formControlErrorStateMatcher(replyText)
          }, attr: {
            placeholder: _t('reviews', 'Написать ответ')
          }"
                    class="form-control"></textarea>
          <validation-feedback params="show: formControlErrorStateMatcher(replyText), text: replyText.error">
          </validation-feedback>
        </div>
        <button class="f-btn f-btn-success f-btn-lg"
                type="button"
                data-bind="click: function() { submit() }, disable: blocked, tooltip, tooltipText: _t('Отправить')" >
          <svg-icon params="name: 'check'"
                    class="svg-icon--lg"></svg-icon>
        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->

</sidesheet>
