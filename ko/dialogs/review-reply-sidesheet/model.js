import { DialogWrapper } from 'Dialogs/wrapper';
import { ApiUrl } from 'Utils/url/api-url';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.review = params.review;

    this.canReply = ko.pureComputed(
      () => {
        return !params.blocked && this.review.sourceType == 'google' && !this.review.reply()
      }
    );

    this.submitted = ko.observable(false);

    this.replyText = ko.observable('').extend({
      required: {
        message: _t('Обязательное поле')
      }
    });
    this.blocked = ko.pureComputed(() => {
      return !this.replyText().trim() || this.submitted();
    });
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.submitted
    );
  }

  submit() {
    this.submitted(true);

    if (!this.replyText.isValid()) return;

    $.ajax({
      method: 'POST',
      url: ApiUrl('filial-reviews/reply', { id: this.review.id }),
      data: {
        text: this.replyText()
      },
      success: (response) => {
        this.review.updateReply(response.reply);
      },
      error: (response) => {
        console.error(response.responseJSON);
        this.submitted(false);
      }
    });
  }
}
