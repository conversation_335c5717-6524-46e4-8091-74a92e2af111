import { DialogWrapper } from 'Dialogs/wrapper';
import 'Bindings/sortable';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.showSuccessMessage = ko.observable(false);

    this.field = params.field;

    this.isRequired = ko.observable(false);
    this.type = ko.observable('text');
    this.label = ko.observable('').extend({
      required: {
        message: _t('Обязательное поле')
      }
    });
    this.name = ko.observable('customField' + Date.now());
    this.placeholder = ko.observable('');
    this.description = ko.observable('');

    this.selectType = ko.observable('single');
    this.options = ko.observableArray([]);
    this.optionsList = ko.observable(null);

    this.textType = ko.observable('one-line');
    this.range = ko.observableArray([1, 500]);

    this.isValid = ko.computed(() => {
      if (!this.label.isValid()) return false;
      if (this.type() === 'select') {
        let list = this.optionsList();
        if (list && 'isValid' in list) {
          if (!list.isValid()) return false;
        }
      }
      return true;
    });

    if (this.field) {
      this.setFieldData(this.field);
    }
  }

  onRender() {}

  setFieldData(data) {
    this.type(data.type_field);
    this.label(data.label);
    this.name(data.name);
    this.placeholder(data.placeholder);
    this.description(data.description);
    this.isRequired(data.is_required);

    if (this.type() === 'text') {
      this.textType(data.text_field_type);
      this.range([data.text_field_min_length, data.text_field_max_length]);
    }

    if (this.type() === 'select') {
      this.selectType(data.select_field_type);
      let options = data.select_options.map((o) => {
        return {
          id: o.id,
          text: o.name
        };
      });
      this.options(options);
    }
  }

  getFieldData() {
    let data = {
      id: this.id,
      type_field: this.type(),
      label: this.label(),
      name: this.name(),
      placeholder: this.placeholder(),
      description: this.description(),
      is_required: this.isRequired() ? 1 : 0
    };

    if (this.type() === 'text') {
      data.text_field_type = this.textType();
      let range = this.range();
      data.text_field_min_length = range[0];
      data.text_field_max_length = range[1];
    }

    if (this.type() === 'select') {
      data.select_field_type = this.selectType();
      data.select_options = this.optionsList()
        .getData()
        .map((option) => {
          return {
            ...option,
            name: option.text
          };
        });
    }
    return data;
  }

  save() {
    this.isSubmitted(true);
    if (!this.isValid()) return;

    this.emitEvent('submit', this.getFieldData());
  }

  reset() {
    this.setFieldData(this.field);
  }

  onDelete() {
    this.emitEvent('delete');
  }
}
