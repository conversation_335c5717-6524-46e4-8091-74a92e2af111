<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header mb-4">
    <div class="container">
      <h2 class="foquz-dialog__title">
        <!-- ko if: field -->
        <span data-bind="text: _t('request-projects', 'Редактировать поле')"></span>
        <!-- /ko -->

        <!-- ko ifnot: field -->
        <span data-bind="text: _t('request-projects', 'Новое поле')"></span>
        <!-- /ko -->
      </h2>
    </div>
  </div>

  <div class="foquz-dialog__body" data-drag-containment
       data-bind="descendantsComplete: function() { onRender() }">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container pb-40p">
        <switch params="checked: isRequired">
          <span data-bind="text: _t('request-projects', 'Обязательное')"></span>
        </switch>

        <div class="row">
          <div class="col col-6">
            <div class="form-group">
              <fc-label params="text: _t('request-projects', 'Тип поля')"></fc-label>


              <select2 params="value: type, allowUnset: true">
                <option value="date">Дата</option>
                <option value="select">Список</option>
                <option value="text">Текст</option>

              </select2>
            </div>
          </div>
          <div class="col col-6">
            <div class="form-group">
              <fc-label params="text: _t('request-projects', 'Наименование поля')"></fc-label>

              <fc-input params="counter: true,
                  maxlength: 250,
                  value: label,
                  invalid: formControlErrorStateMatcher(label)"></fc-input>

              <fc-error params="show: formControlErrorStateMatcher(label), text: label.error">
              </fc-error>
            </div>
          </div>
          <div class="col col-6">
            <div class="form-group">
              <fc-label params="text: _t('request-projects', 'Подсказка внутри поля'),
                    hint: _t('request-projects', 'Подсказка внутри поля')"></fc-label>

                <fc-input params="counter: true,
                    maxlength: 125,
                    value: placeholder,
                    invalid: formControlErrorStateMatcher(placeholder),
                    placeholder: type() === 'date' ? '00.00.0000' : ''"></fc-input>


              <div class="mt-5p f-fs-1 f-color-service"
                   data-bind="text: _t('request-projects', 'Если подсказка не нужна, оставьте поле пустым')"></div>
            </div>
          </div>
          <div class="col col-6">
            <div class="form-group">
              <fc-label params="text: _t('request-projects', 'Описание поля'),
                    hint: _t('request-projects', 'Описание поля')"></fc-label>

              <fc-textarea params="counter: true,
                    maxlength: 500,
                    value: description,
                    height: 'input',
                    invalid: formControlErrorStateMatcher(placeholder),
                    placeholder: _t('request-projects', 'Введите описание')"></fc-textarea>

              <div class="mt-5p f-fs-1 f-color-service"
                   data-bind="text: _t('request-projects', 'Если описание не нужно, оставьте поле пустым')"></div>
            </div>
          </div>

          <!-- ko template: {
            foreach: templateIf(type() === 'text', $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
          } -->
          <div class="col col-6">
            <div class="form-group">
              <fc-label params="text: _t('request-projects', 'Вид поля')"></fc-label>


              <radio-group class="text-type"
                           params="options: [
                { value: 'one-line', label: _t('request-projects', 'Однострочное'), icon: 'input-single-line' },
                { value: 'multiline', label: _t('request-projects', 'Многострочное'), icon: 'input-multi-line' },
              ], value: textType"></radio-group>
            </div>
          </div>
          <div class="col col-6">
            <div class="form-group">
              <fc-label params="text: _t('request-projects', 'Допустимое количество символов в ответе')"></fc-label>


              <interval-slider params="minLimit: 0, maxLimit: 500, range: range, showLabels: true" class="mt-2"></interval-slider>
            </div>
          </div>
          <!-- /ko -->

          <!-- ko template: {
            foreach: templateIf(type() === 'select', $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
          } -->
          <div class="col col-6">
            <div class="mb-45p">
              <fc-label params="text: _t('request-projects', 'Кол-во выбираемых вариантов'),
                    hint: _t('request-projects', 'Кол-во выбираемых вариантов')"></fc-label>



              <radio-group params="options: [
                { value: 'single', label: _t('request-projects', 'Один'), icon: 'input-radio' },
                { value: 'multiple', label: _t('request-projects', 'Несколько'), icon: 'input-checkbox' },
              ], value: selectType"></radio-group>
            </div>
          </div>

          <div class="col-12">
            <options-list
                          params="ref: optionsList, options: options, formControlErrorStateMatcher: formControlErrorStateMatcher">
            </options-list>
          </div>
          <!-- /ko -->
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <!-- ko if: field -->
      <fc-button params="label: 'Удалить', icon: 'times', color: 'danger', click: function() { onDelete() }"></fc-button>
      <!-- /ko -->

      <fc-button class="ml-2" params="label: 'Отменить', icon: 'bin', color: 'secondary', click: function() { reset() }"></fc-button>

      <fc-button class="ml-2" params="label: 'Сохранить', icon: 'save', color: 'success', click: function() { save() }"></fc-button>
    </div>

    <success-message params="show: showSuccessMessage"></success-message>
  </div>

</sidesheet>
