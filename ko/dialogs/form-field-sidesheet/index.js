import { ViewModel } from './model';
import html from './template.html';
import './style.less';
import 'Components/options-list';

ko.components.register('form-field-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('form-field-sidesheet');

      return new ViewModel(params, element);
    }
  },
  template: html
});
