@import 'Style/breakpoints';

.items-group {
  display: block;
  margin-top: 25px;

  &__header {
    display: flex;
    margin-bottom: 16px;
    .f-check {
      &-label {
        font-weight: 900;
        font-size: 15px;
      }
    }
  }
  .drag-handle {
    padding-left: 12px;
    padding-right: 6px;
    padding-top: 2px;
    cursor: pointer;
  }
  .table-items__list {
    margin-top: 0;
    display: flex;
    flex-wrap: wrap;
  }

  .table-item {
    width: 25%;
    padding-top: 6px;
    padding-bottom: 8px;
    display: flex;
    min-height: 38px;

    &.ui-sortable-helper {
      .f-check-label {
        padding-left: 0;
        &:before,
        &:after {
          display: none;
        }
      }
    }
  }

  .mobile-and-tablet({
    .table-item {
      width: 33.3333%;
      padding-left: 33px;
      .drag-handle {
        display: none;

      }
    }
  });

  .only-mobile({
    .table-item {
      width: 100%;
    }
  });
}
