<div class="items-group__header">
  <foquz-checkbox params="checked: hasVisibleItems, partially: partiallyChecked,
    event: {
        change: function() {
          toggle();
        }
    }">
    <!-- ko text: $parent.group.name -->
    <!-- /ko -->

    <!-- ko if: $parent.group.icon -->
    <fc-icon class="ml-10p" params="name: $parent.group.icon"></fc-icon>
    <!-- /ko -->
  </foquz-checkbox>


  <button class="button-ghost ml-10p mt-5p"
          data-bind="click: function() {
    isOpen(!isOpen())
  }">
    <toggler-icon params="isOpen: isOpen"
                  class="f-color-service"></toggler-icon>
  </button>

</div>

<!-- ko template: {
  foreach: templateIf(isOpen(), $data),
  afterAdd: slideAfterAddFactory(400),
  beforeRemove: slideBeforeRemoveFactory(400)
} -->
<div class="table-items__list styled"
     data-bind="foquzSortable: {
      data: group.list,
      as: 'item',
      connectClass: '',

      options: {
        handle: '.drag-handle',
        connectWith: '',
        items: '> .table-item'
      }
    }">

  <div class="table-item"
       data-bind="visible: item.filtered, css: item.css || ''">
    <div class="drag-handle"
         draggable="true">
      <svg-icon params="name: 'drag-arrow'"></svg-icon>
    </div>
    <foquz-checkbox params="checked: item.visible">
      <!-- ko text: item.elementName || item.name -->
      <!-- /ko -->
    </foquz-checkbox>
  </div>

</div>
<!-- /ko -->
