import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.group = params.group;
    this.isOpen = ko.observable(true);

    element.style.display = this.group.hasFilteredItems() ? '' : 'none';

    this.subscriptions.push(
      this.group.hasFilteredItems.subscribe((v) => {
        element.style.display = v ? '' : 'none';
      })
    );

    this.visibleItems = ko.pureComputed(() => {
      return this.group.list().filter((i) => i.visible());
    });

    this.isAllItemsVisible = ko.pureComputed(() => {
      return this.visibleItems().length === this.group.list().length;
    });

    this.hasVisibleItems = ko.pureComputed(() => {
      return this.visibleItems().length > 0;
    });

    this.partiallyChecked = ko.pureComputed(() => {
      return this.hasVisibleItems() && !this.isAllItemsVisible();
    });

    this.noChecked = ko.pureComputed(() => {
      return this.visibleItems().length === 0;
    });

    if (this.group.hideIfEmpty && this.noChecked()) this.isOpen(false);
  }

  toggle() {
    if (this.hasVisibleItems()) {
      this.group.list().forEach((c) => c.visible(false));
    } else {
      this.group.list().forEach((c) => c.visible(true));
      this.isOpen(true);
    }
  }
}
