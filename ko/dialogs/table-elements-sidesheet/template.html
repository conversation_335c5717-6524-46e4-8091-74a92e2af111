<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2
        class="foquz-dialog__title"
        data-bind="text: title || _t('answers', 'Настроить фильтры')"
      ></h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll">
      <div class="container h-100">
        <div class="table-elements-dialog__column h-100 d-flex flex-column">
          <div
            class="border-bottom pb-30p d-flex flex-wrap justify-content-between align-items-center"
          >
            <search-field
              params="placeholder: _t('Поиск по названию'), value: query"
            ></search-field>
          </div>
          <div class="table-elements-dialog__wrapper">
            <div class="table-elements h-100" data-bind="nativeScrollbar">
              <div class="pb-4">
                <!-- ko foreach: { data: groups, as: 'group' } -->
                <items-group params="group: group"></items-group>
                <!-- /ko -->
              </div>

              <!-- ko ifnot: hasResults -->
              <div
                class="text-center f-color-service f-fs-2 py-10p"
                data-bind="text: _t('Ничего не найдено')"
              ></div>
              <!-- /ko -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() {
                $dialog.hide();
              }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        <!-- ko text: _t('Отменить') -->
        <!-- /ko -->
      </button>
      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="click: function() { apply()  }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        <!-- ko text: _t('Сохранить') -->
        <!-- /ko -->
      </button>
    </div>
  </div>
</sidesheet>
