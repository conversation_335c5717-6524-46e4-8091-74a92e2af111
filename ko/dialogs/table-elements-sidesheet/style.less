@import 'Style/breakpoints';
@import 'Style/colors';

.table-elements-sidesheet {
  .foquz-dialog__title {
    margin-bottom: 20px;

    .only-mobile({
      margin-bottom: 8px;
    });
  }
  .table-elements {
    overflow: hidden;

    &__list {
      margin-top: 10px;
      column-count: 3;
      vertical-align: top;

      .only-mobile({
        column-count: 1;
      });

      .foquz-checkbox {
        padding-top: 8px;
        padding-bottom: 8px;
      }
    }

    .table-element {
      min-height: 24px;
      width: 100%;
      break-inside: avoid;
    }

    .items-group {
      padding-bottom: 18px;

      border-bottom: 1px solid @f-color-border;

      &__header {
        display: flex;
        margin-bottom: 8px;

        .f-check-label {
          font-size: 19px;
          padding-top: 2px;
        }
      }

      .only-mobile({
        &__header {
          .f-check-label {
            font-size: 16px;
          }
        }
      });
    }
  }

  .table-elements-dialog__wrapper {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin-right: -30px;
    padding-bottom: 0px;

    .os-content {
      padding-right: 30px !important;
    }
  }

  .foquz-search-field {
    width: 480px;
  }

  .mobile-and-tablet({
    .foquz-search-field {
      width: 350px;
    }

    .table-elements-dialog__wrapper {
      margin-right: 0;

      .os-content {
        padding-right: 0px!important;
      }
    }
  });

  .only-mobile({
    .foquz-search-field {
      width: 100%;
      margin-bottom: 10px;
    }

    .foquz-dialog__header {
      padding-top: 20px;
      padding-bottom: 16px;
    }

    .foquz-dialog__close {
      top: 4px;
    }

    .foquz-dialog__body {
      overflow: hidden;
    }

    .table-elements-dialog__column {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      max-height: 100%;
    }


    .table-elements {
      flex-grow: 1;

      max-height: 100%;
      overflow: hidden!important;

    }
  });
}
