import { DialogWrapper } from 'Dialogs/wrapper';
import { ItemGroup } from './group';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.query = ko.observable('');
    const state = params.state;

    this.title = params.title;

    this.groups = params.groups.map((g, i) => new ItemGroup(g, state, this.query, i));

    this.hasResults = ko.pureComputed(() => {
      return this.groups.some((g) => g.hasFilteredItems());
    });
  }

  setDefaultState() {
    this.groups.forEach((g) => g.setDefaultState());
  }

  apply() {
    let state = {};
    this.groups.forEach((g) => {
      state[g.id] = g.getState();
    });
    this.emitEvent('apply', state);
    this.hide();
  }
}
