import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './items-group';

ko.components.register('table-elements-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('table-elements-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
