/**
 * items - [ {id, name}, { id, name } ]
 * state - { id: {position, visible} }
 */

export class ItemGroup {
  constructor(group, state, query, positionOffset) {
    this.query = query;
    let lQuery = ko.computed(() => {
      return this.query().toLowerCase().trim();
    });

    this.name = group.name;
    this.id = group.id;
    this.hideIfEmpty = group.hideIfEmpty;
    this.positionOffset = positionOffset * 100;

    this.items = group.items;
    this.state = group.state || state;
    this.icon = group.icon;

    let list = this.items.map((item) => {
      let itemState = this.state[item.id];
      let lName = item.name.toLowerCase();

      return {
        id: item.id,
        name: item.name,
        elementName: item.elementName,
        css: item.css,
        position: itemState.position,
        visible: ko.observable(itemState.visible),
        filtered: ko.pureComputed(() => {
          if (!this.query()) return true;
          return lName.includes(lQuery());
        })
      };
    });
    list.sort((a, b) => a.position - b.position);
    this.list = ko.observableArray(list);

    this.hasFilteredItems = ko.computed(() => {
      return this.list().some((item) => item.filtered());
    });
  }

  setDefaultState() {
    let list = [...this.list()];
    list.forEach((item) => {
      let itemState = this.state[item.id];
      item.visible(itemState.visible);
      item.position = itemState.position;
    });
    list.sort((a, b) => a.position - b.position);
    this.list(list);
  }

  getState() {
    let state = {};
    this.list().forEach((item, i) => {
      state[item.id] = {
        visible: item.visible(),
        position: i + this.positionOffset
      };
    });
    return state;
  }
}
