import { get as _get } from "lodash";
import { DialogWrapper } from 'Dialogs/wrapper';
import {
  setMailingLaunchStatus,
  setMailingStatus
} from './utils/set-mailing-status';
import { CheckedModel } from 'Legacy/models/table/checked';
import { GroupActions } from 'Legacy/models/table/group-actions';
import 'Legacy/components/tags-select';
import { getClientFields } from 'Legacy/utils/client-fields';
import { BirthdayFilterModel } from 'Legacy/components/birthday-filter';
// import 'Modals/select-contacts-modal-page';
import 'Dialogs/select-contacts-sidesheet';
import 'Dialogs/launch-mailing-dialog';

import { DialogsModule } from 'Utils/dialogs-module';
import { TableColumnsModel } from 'Models/table-columns-model';
import 'Dialogs/table-columns-dialog';
import { ApiUrl } from '@/utils/url/api-url';
import { Mailing } from "Models/mailing";

const statuses = {
  0: 'new',
  1: 'launched',
  2: 'stopped'
};

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    console.log('poll-mailing-sidesheet', params);

    DialogsModule(this);

    let $element = $(element);
    this.mailing = params.data.mailing;
    this.allTags = params.data.allTags;

    this.statusName = ko.pureComputed(() => {
      return statuses[this.mailing.status()];
    });

    this.modalOpens = params.modalOpens;
    this.modals = ko.observableArray([]);

    this.dialogs = ko.observable(null);

    this.tagsDirectory = params.data.tagsDirectory;

    this.statusFilterElement = ko.observable(null);
    this.connectionChannelFilterElement = ko.observable(null);
    this.tagFilterElement = ko.observable(null);
    this.tagOperationFilterElement = ko.observable(null);
    this.genderFilterElement = ko.observable(null);

    this.clientFields = getClientFields();

    this.filterSetting = ko.observable(null);

    this.modalOpens = params.data.modalOpens;

    this.mailing.load();

    this.checked = new CheckedModel({
      items: this.mailing.clients,
      totalCount: this.mailing.filterClientsCount
    });

    this.groupActions = new GroupActions({
      selectedCount: this.checked.checkedCount,
      selectAll: () => {
        this.checked.checkAll();
      },
      actions: [{ id: 1, text: 'Удалить' }],
      apply: () => {
        this.performGroupAction();
      },
      reset: () => {
        this.checked.uncheckAll();
      }
    });

    this.filterTags = ko.pureComputed(() => {
      let list = this.tagsDirectory.data();
      return this.mailing
        .allTags()
        .map((t) => {
          let tag = list.find((i) => i.name == t);
          return tag;
        })
        .filter(Boolean);
    });

    this.tagsTemplateResult = (state) => {
      let span = $('<span>').text(state.text);

      if (state.element && state.element.hasAttribute('data-auto'))
        span.addClass('color-success');
      else if (state.children) span.addClass('color-service');
      else if (state.id) span.addClass('color-text');

      return span;
    };

    this.isAutoTag = (tagName) => {
      let tag = this.tagsDirectory.data().find((t) => t.name == tagName);
      if (tag) return tag.isAuto;
      return false;
    };

    this.isNameEditing = ko.observable(false);

    this.loading = false;
    this.stopLoading = false;
    this.updating = ko.observable(false);
    let t = this;
    this.page = 1;
    this.showEmptyMessage = ko.observable(false);

    this.loadClients = function (params, onSuccess) {
      if (!params || params.page == 1) {
        t.updating(true);
      }

      $('#clients-loader').show();
      $.get(
        ApiUrl('foquz-poll-mailings/contacts', { id: t.mailing.id() }),
        // '/foquz/foquz-poll-mailings/contacts?id=' + t.mailing.id(),
        params,
        (response) => {
          $.each(response.contacts, (key, c) => {
            t.mailing.clients.push(t.convertClient2(c));
          });
          if (response.contacts.length == 0) t.stopLoading = true;

          var cnt = response.allContactsCount;
          t.mailing.allClientsCount(cnt === null ? 0 : parseInt(cnt));
          var filterCnt = response.filterContactsCount;
          t.mailing.filterClientsCount(
            filterCnt === null ? 0 : parseInt(filterCnt)
          );

          t.mailing.updateStats(
            response.statuses || {},
            response.statusesUnfiltered || {}
          );

          t.showEmptyMessage(t.mailing.clients().length == 0);

          this.updating(false);
          if (typeof onSuccess == 'function') onSuccess();
        },
        'json'
      ).always(function () {
        $('#clients-loader').hide();
        t.updating(false);
      });
    };

    this.loadFilterSettings = function (params) {
      console.log('loadFilterSettings2', params);
      console.log('t.mailing', t.mailing);
      let url = `/foquz/foquz-poll-mailings/get-notifications?id=${t.mailing.foquz_poll_id}`;

      $.get(
        "/foquz/foquz-poll-mailings/get-notifications?id=" + t.mailing.foquz_poll_id,
        {},
        (response) => {
          if (response.filterSetting) {
            this.filterSetting(response.filterSetting.data);
          }
          // const order = response.order;
          console.log('loadFilterSettings response', response);

        }
      );
    };

    this.reloadClients = function (page1 = 1, onSuccess) {
      if (t.mailing.isNew()) return;
      try {
        if (page1 == 1) {
          t.loading = false;
          t.stopLoading = false;
          t.page = 1;
        }

        if (t.stopLoading) return;

        var obj = this.getFilterObj();
        obj.page = page1;

        if (page1 == 1) t.mailing.clients.removeAll();

        this.loadClients(obj, onSuccess);

        if (page1 == 1) {
          t.mailing.clients.removeAll();
          t.mailing.allClientsCount(0);
        }
      } catch (err) {
        $('#clients-loader').hide();
        this.updating(false);
        throw err;
      }
    };

    var reloadClients = this.reloadClients;

    this.nextPage = () => {
      if (this.loading) return;
      this.loading = true;
      this.page += 1;
      this.reloadClients(t.page, () => {
        this.loading = false;
      });
    };

    var t3 = this;

    let createMailing = (mailingData) => {
      return new Promise((resolve, reject) => {
        $.ajax({
          url: '/foquz/foquz-poll-mailings/create',
          method: 'POST',
          data: mailingData,
          success: (response) => {
            if (response.success) {
              resolve(response.data.mailing);
            } else {
              if (response.data.errors instanceof Array) {
                var errors = $.map(response.data.errors, function (e) {
                  return e.message;
                }).join(' ');
                alert(errors);
              } else {
                alert('Ошибка сохранения списка рассылок');
              }
              reject();
            }
          }
        });
      });
    };

    let addContactsToMailing = (mailingId, params) => {
      return new Promise((res, rej) => {
        $.ajax({
          method: 'POST',
          url: `/foquz/foquz-poll-mailings/add-contacts?id=${mailingId}`,
          data: params,
          success: (response) => {
            res(response.data.stat_id);
          }
        });
      });
    };

    this.openSelectClientsModal = function () {
      t3.dialogs().add({
        name: 'select-contacts-sidesheet',
        params: {
          data: {
            statsUrl: '/foquz/foquz-poll-mailings/get-link-contacts-stat',
            tagsDirectory: this.tagsDirectory,
            filterSetting: this.filterSetting,
            formatStats: (response) => {
              if (response.data) {
                return response.data;
              }
              return {
                status: 'error'
              };
            },
            onSubmit: (params) => {
              if (t3.mailing.isNew()) {
                return createMailing(ko.toJS(t3.mailing)).then((mailing) => {
                  t3.mailing.id(mailing.id);
                  t3.mailing.name(mailing.name);
                  t3.mailing.isNew(false);
                  return addContactsToMailing(this.mailing.id(), params);
                });
              } else {
                return addContactsToMailing(this.mailing.id(), params);
              }
            }
          }
        },
        events: {
          hide: () => {
            t3.reloadClients();
          }
        }
      });
    };

    this.getMainInfoStatusElementValueClass = function (status) {
      switch (status) {
        case 0:
          return 'mailings__details-modal-dialog-main-info-status-element-value--new';
        case 1:
          return 'mailings__details-modal-dialog-main-info-status-element-value--launched';
      }
    };

    this.getMainInfoStatusElementValueText = function (status) {
      switch (status) {
        case 0:
          return 'Новый';
        case 1:
          return 'В процессе';
        case 2:
          return 'Остановлена';
      }
    };

    this.statusFilter = ko.observable('');
    this.connectionChannelFilter = ko.observable('');
    this.contactsFilter = ko.observable('');
    this.tagFilter = ko.observable([]);
    this.tagOperationFilter = ko.observable(1);
    this.genderFilter = ko.observable([]);

    this.birthdayFilter = new BirthdayFilterModel({
      format: 'YYYY-MM-DD'
    });

    this.filledFilter = ko.observable('');
    this.fieldsFilter = ko.observableArray([]);
    this.ageFilter = ko.observable('');
    this.answeredOnlyFilter = ko.observable(false);

    this.isFiltersEmpty = ko.computed(() => {
      if (this.mailing.status() === 0) {
        return (
          this.contactsFilter() === '' &&
          this.tagFilter().length === 0 &&
          this.birthdayFilter.type() === '' &&
          this.genderFilter().length === 0
        );
      } else {
        return (
          this.statusFilter().length === 0 &&
          this.connectionChannelFilter().length === 0 &&
          this.contactsFilter() === '' &&
          this.answeredOnlyFilter() === false
        );
      }
    }, this);

    this.resetFilters = function () {
      this.contactsFilter('');

      if (this.mailing.status() === 0) {
        this.tagFilter([]);
        this.tagOperationFilter(1);
        $(this.tagFilterElement()).val([]).trigger('change');
        this.genderFilter([]);
        $(this.genderFilterElement()).val([]).trigger('change');
        this.ageFilter('');
        this.birthdayFilter.reset();
        this.filledFilter('');
        this.fieldsFilter([]);
        this.passedAtStringFilter('');
        this.statusStringFilter('');
        this.lastConnectionChannelStringFilter('');
        this.nameStringFilter('');
        this.phoneStringFilter('');
        this.emailStringFilter('');
        this.genderStringFilter('');
        this.birthdayStringFilter('');
        this.tagStringFilter('');
        this.addedAtStringFilter('');
      } else {
        this.statusFilter([]);
        $(this.statusFilterElement()).val([]).trigger('change');
        this.connectionChannelFilter([]);
        $(this.connectionChannelFilterElement()).val([]).trigger('change');
        this.answeredOnlyFilter(false);
        this.passedAtStringFilter('');
        this.statusStringFilter('');
        this.lastConnectionChannelStringFilter('');
        this.nameStringFilter('');
        this.phoneStringFilter('');
        this.emailStringFilter('');
        this.genderStringFilter('');
        this.birthdayStringFilter('');
        this.tagStringFilter('');
        this.addedAtStringFilter('');
      }
    };

    this.passedAtStringFilter = ko.observable('');
    this.statusStringFilter = ko.observable('');
    this.lastConnectionChannelStringFilter = ko.observable('');
    this.nameStringFilter = ko.observable('');
    this.phoneStringFilter = ko.observable('');
    this.emailStringFilter = ko.observable('');
    this.genderStringFilter = ko.observable('');
    this.birthdayStringFilter = ko.observable('');
    this.tagStringFilter = ko.observable('');
    this.addedAtStringFilter = ko.observable('');

    this.getFilterObj = function () {
      let sort = null;
      let cols = [];

      $(
        '.foq-table__sorting-icon,.foq-table__sorting-icon--fake',
        '.mailings__details-modal-dialog-table'
      ).each(function () {
        cols.push($(this).attr('order-key'));
        if ($(this).hasClass('foq-table__sorting-icon--fake')) {
          return;
        }
        let order_key;
        if ($(this).hasClass('foq-table__sorting-icon--order_asc')) {
          order_key = $(this).attr('order-key');
          sort = {
            key: order_key,
            order: 'asc'
          };
        } else if ($(this).hasClass('foq-table__sorting-icon--order_desc')) {
          order_key = $(this).attr('order-key');
          sort = {
            key: order_key,
            order: 'desc'
          };
        }
      });

      // Почему-то делает массив при смене операции в интерфейсе
      var tagOperation = this.tagOperationFilter();
      if (tagOperation instanceof Array) tagOperation = tagOperation[0];

      var obj = {
        sort: sort,
        advanced: {
          contacts: ko.toJS(this.contactsFilter()).trim(),

          answeredOnly: ko.toJS(this.answeredOnlyFilter()),
          status: ko.toJS(this.statusFilter()),
          connectionChannel: ko.toJS(this.connectionChannelFilter()),

          gender: ko.toJS(this.genderFilter()),
          tags: ko.toJS(this.tagFilter()),
          tagsOperation: tagOperation,

          birthday: this.birthdayFilter.getParams()
        },

        simple: {
          passedAt: this.passedAtStringFilter(),
          passedAtFormat: '%d.%m.%Y',
          status: this.statusStringFilter(),
          channel: this.lastConnectionChannelStringFilter(),
          name: this.nameStringFilter(),
          phone: this.phoneStringFilter(),
          email: this.emailStringFilter(),
          gender: this.genderStringFilter(),
          birthday: this.birthdayStringFilter(),
          birthdayFormat: '%d.%m.%Y',
          tag: this.tagStringFilter(),
          addedAt: this.addedAtStringFilter(),
          addedAtFormat: '%d.%m.%Y'
        }
      };

      if (
        ['1', '0'].includes(this.filledFilter()) &&
        this.fieldsFilter().length
      ) {
        obj.records = {
          filled: this.filledFilter(),
          fields: this.fieldsFilter()
        };
      }
      obj.mailingId = this.mailing.id;
      obj.cols = cols;

      return obj;
    };

    var that = this;

    this.convertClient2 = function (c) {
      var res = c;
      res.selected = ko.observable(false);
      res.checked = ko.observable(false);
      res.checked(that.checked.allItemsChecked());

      var name = c.foquzContact.last_name ? c.foquzContact.last_name : '';
      if (c.foquzContact.first_name) {
        name += (name ? ' ' : '') + c.foquzContact.first_name;
      }
      if (c.foquzContact.patronymic) {
        name += (name ? ' ' : '') + c.foquzContact.patronymic;
      }

      res.name = name;
      res.passedAt = _get(res, 'sends[0].answer.passedAt', null);
      res.status = _get(res, 'sends[0].answer.answStatus', -1);

      //res.status = parseInt(c.answer_status);
      res.sendingList = [];
      res.birthday = ko.observable(
        c.foquzContact.birthday == null || c.foquzContact.birthday == ''
          ? ''
          : moment(c.foquzContact.birthday, 'YYYY-MM-DD').format('DD.MM.YYYY')
      );
      res.phone = c.foquzContact.phone;
      res.email = c.foquzContact.email;
      res.gender = parseInt(c.foquzContact.gender);
      res.tags = ko.observableArray($.map(res.foquzContact.tags, (t) => t.tag));
      res.addedAt =
        c.created_at == null || c.created_at == ''
          ? ''
          : moment(c.created_at, 'X').format('DD.MM.YYYY');

      var send = null,
        prevName = '';

      for (var i in res.sends) {
        var s = res.sends[i];
        if (s.channel) {
          name = s.channel.name;
        }
        if (name != prevName) {
          if (send) {
            res.sendingList.push(send);
          }

          send = {
            sentAt: moment(s.sended, 'YYYY-MM-DD').format('DD.MM.YYYY'),
            connectionChannel: 0,
            repeatCount: 0,
            hasResponse: false
          };

          if (name == 'SMS') send.connectionChannel = 1;
          else if (name == 'Viber') send.connectionChannel = 2;
          else if (name == 'Telegram') send.connectionChannel = 3;
          prevName = name;
        }

        if (send) {
          send.repeatCount++;
          send.sentAt = moment(s.sended, 'YYYY-MM-DD').format('DD.MM.YYYY');

          if (s.status > 1) {
            send.hasResponse = true;
          }
        }
      }

      if (send) {
        res.sendingList.push(send);
      }

      return res;
    };

    let state = this.mailing.status() ? 1 : 0;

    this.newTableColumns = new TableColumnsModel(
      'fpoll-mailing-new-' + this.mailing.id(),
      [
        {
          id: 'name',
          name: 'ФИО',
          placeholder: "Все",
        },
        {
          id: 'phone',
          name: 'Телефон',
          placeholder: "Все",
        },
        {
          id: 'email',
          name: 'Email',
          placeholder: "Все",
        },
        {
          id: 'gender',
          name: 'Пол',
          placeholder: "Все",
        },
        {
          id: 'birthday',
          name: 'Дата рождения',
          placeholder: "00.00.0000",
        },
        {
          id: 'tags',
          name: 'Теги',
          placeholder: "Все",
        },
        {
          id: 'addedAt',
          name: 'Добавлен',
          placeholder: "00.00.0000",
        },
      ],
    );
    this.launchedTableColumns = new TableColumnsModel(
      'fpoll-mailing-launched-' + this.mailing.id(),
      [
        {
          id: 'passed',
          name: 'Пройдена',
          enabled: ko.observable(true),
          placeholder: "00.00.0000",
        },
        {
          id: 'status',
          name: 'Статус',
          enabled: ko.observable(true),
          placeholder: "Все",
        },
        {
          id: 'channel',
          name: 'Канал связи',
          enabled: ko.observable(true),
          placeholder: "Все",
        },
        {
          id: 'name',
          name: 'ФИО',
          enabled: ko.observable(true),
          placeholder: "Все",
        },
        {
          id: 'phone',
          name: 'Телефон',
          enabled: ko.observable(true),
          placeholder: "Все",
        },
        {
          id: 'email',
          name: 'Email',
          enabled: ko.observable(true),
          placeholder: "Все",
        },
      ],
    );

    this.tableColumns =
      this.mailing.status() === 0
        ? this.newTableColumns
        : this.launchedTableColumns;

    this.openEditColumnsModal = function () {
      this.openDialog({
        name: 'table-columns-dialog',
        params: {
          model: this.tableColumns
        }
      });
    };

    this.groupAction = ko.observable('');

    this.everyClientSelected = ko.computed(function () {
      return this.mailing.clients().every((r) => r.selected());
    }, this);

    this.selectedClientCount = ko.computed(function () {
      return this.mailing.clients().filter((r) => r.selected()).length;
    }, this);

    this.selectedClientCount.subscribe((count) => {
      if (count === 0) {
        this.groupAction('');
      }
    });

    this.toggleAllSelection = function () {
      if (this.everyClientSelected()) {
        this.mailing.clients().forEach((r) => r.selected(false));
      } else {
        this.mailing.clients().forEach((r) => r.selected(true));
      }
    };

    this.selectAllClient = function () {
      this.mailing.clients().forEach((r) => r.selected(true));
    };

    this.unselectAllClient = function () {
      this.mailing.clients().forEach((r) => r.selected(false));
    };

    this.performGroupAction = function () {
      var obj;
      // if (this.everyClientSelected()) {
      if (this.checked.allItemsChecked()) {
        obj = { type: 'filter', filter: this.getFilterObj() };
        obj.filter.page = -1;
      } else {
        var obj = {
          clients: $.map(
            $.grep(ko.toJS(this.mailing.clients()), function (c) {
              return c.checked;
            }),
            function (c) {
              return c.id;
            }
          )
        };
      }
      let action = this.groupActions.action();
      switch (action) {
        case 1:
          $.post(
            '/foquz/foquz-poll-mailings/delete-many?id=' + this.mailing.id(),
            obj,
            (response) => {
              if (response.success) {
                this.reloadClients();
              }
            },
            'json'
          );
          this.checked.uncheckAll();
          break;

        default:
          this.checked.uncheckAll();
        // this.unselectAllClient();
      }
    };

    this.getTableStatusClass = function (status) {
      status = '' + status;
      switch (status) {
        case 'Отправлена':
          return 'f-color-mailing-sent';
        case 'Письмо открыто':
          return 'f-color-mailing-mail-open';
        case 'Опрос открыт':
          return 'f-color-mailing-open';
        case 'В процессе':
          return 'f-color-mailing-progress';
        case 'Заполнена':
          return 'f-color-mailing-filled';
        default:
          return '';
      }
    };

    this.getTableStatusText = function (status) {
      status = '' + status;
      switch (status) {
        case '-1':
          return '–';
        default:
          return status;
      }
    };

    this.getConnectionChannelClass = function (connectionChannel) {
      switch (connectionChannel) {
        case 0:
          return 'mailings__details-modal-dialog-connection-channel--email';
        case 1:
          return 'mailings__details-modal-dialog-connection-channel--sms';
        case 2:
          return 'mailings__details-modal-dialog-connection-channel--viber';
        case 3:
          return 'mailings__details-modal-dialog-connection-channel--telegram';
      }
    };

    this.getConnectionChannelText = function (connectionChannel) {
      switch (connectionChannel) {
        case 0:
          return 'Email';
        case 1:
          return 'SMS';
        case 2:
          return 'Viber';
        case 3:
          return 'Telegram';
      }
    };

    this.cancel = function () {
      if ('cancel' in params) {
        params.cancel();
      }
    };

    this.submit = function () {
      this.emitEvent('submit');
    };

    this.afterAdd = function (element) {
      $(element).hide().fadeIn(200);
    };

    this.beforeRemove = function (element) {
      $(element).fadeOut(200, () => $(element).remove());
    };

    if (!this.mailing.isNew()) {
      this.mailing.clients.removeAll();
      var obj = this.getFilterObj();
      obj.page = 1;

      this.loadClients(obj);
    }
    this.loadFilterSettings(this.getFilterObj())

    this.initializing = ko.observable(true);

    this.onRender = function () {
      $element.removeClass('mailings__details-modal-dialog--initializing');
      var t = this;

      $(
        '.foq-table__sorting-icon',
        '.mailings__details-modal-dialog-table'
      ).click(function () {
        if ($(this).hasClass('foq-table__sorting-icon--order_asc')) {
          $('.foq-table__sorting-icon', '.mailings__details-modal-dialog-table')
            .removeClass('foq-table__sorting-icon--order_desc')
            .removeClass('foq-table__sorting-icon--order_asc');
          $(this)
            .removeClass(
              'foq-table__sorting-icon--order_asc',
              '.mailings__details-modal-dialog-table'
            )
            .addClass('foq-table__sorting-icon--order_desc');
        } else if ($(this).hasClass('foq-table__sorting-icon--order_desc')) {
          $('.foq-table__sorting-icon', '.mailings__details-modal-dialog-table')
            .removeClass('foq-table__sorting-icon--order_desc')
            .removeClass('foq-table__sorting-icon--order_asc');
          $(this)
            .addClass('foq-table__sorting-icon--order_asc')
            .removeClass('foq-table__sorting-icon--order_desc');
        } else {
          $('.foq-table__sorting-icon', '.mailings__details-modal-dialog-table')
            .removeClass('foq-table__sorting-icon--order_desc')
            .removeClass('foq-table__sorting-icon--order_asc');
          $(this)
            .removeClass('foq-table__sorting-icon--order_asc')
            .addClass('foq-table__sorting-icon--order_desc');
        }
        const activeCell = document.querySelector('.foq-table .foq-table__head-cell-name--active');
        if (activeCell) {
          activeCell.classList.remove('foq-table__head-cell-name--active');
        }
        this.parentElement.querySelector('.foq-table__head-cell-name').classList.add('foq-table__head-cell-name--active');
        t.reloadClients();
      });
      $(
        '.foq-table__head-cell-name:not(.disable-sort)',
        '.mailings__details-modal-dialog-table'
      ).click(function () {
        $(this).parent().find('.foq-table__sorting-icon').click();
      });
      $(
        '.foq-table__head-cell-filter',
        '.mailings__details-modal-dialog-table'
      ).on('keydown', function (e) {
        if (e.which == 13) {
          e.preventDefault();
          t.reloadClients();
        }
      });

      this.initializing(false);
    };
  }

  exportCSV () {
    const obj = this.getFilterObj();
    obj.page = -1;
    obj['access-token'] = APIConfig.apiKey;
    window.location.href = "/foquz/api/foquz-poll-mailings/export-csv?" + $.param(obj);
  };
}
