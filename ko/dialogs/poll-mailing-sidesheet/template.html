<!-- ko let: { $mailingModal: $component }-->
<sidesheet
  params="ref: modal, dialogWrapper: $component"
  data-bind="descendantsComplete: $component.onRender.bind($component)"
>
  <div
    class="sidesheet__status-line"
    data-bind="attr: {
    'data-status': statusName
  }"
  ></div>

  <div class="foquz-dialog__header">
    <div class="container">
      <div class="page-header">
        <div
          class="breadcrumbs page-header__breadcrumbs mailings__details-modal-dialog-header-breadcrumbs"
        >
          <a class="breadcrumbs__item">
            <!-- ko if: !isNameEditing() -->
            <!-- ko text: mailing.name() -->
            <!-- /ko -->
            <!-- /ko -->

            <!-- ko if: isNameEditing() && !window.CURRENT_USER.watcher -->
            <edit-form
              params="value: mailing.name(),
            onSave: function(value) {
              mailing.name(value);
              isNameEditing(false);
              submit()
            },
            onCancel: function() { isNameEditing(false); }"
            ></edit-form>
            <!-- /ko -->
          </a>
        </div>

        <!-- ko if: !isNameEditing() && !window.CURRENT_USER.watcher -->
        <button
          class="btn btn-icon btn-icon--simple btn-default btn-default mailings__details-modal-dialog-header-edit-button"
          type="button"
          title="Редактировать название"
          data-bind="click: function() { isNameEditing(true); }"
        ></button>
        <!-- /ko -->

        <div class="spacer"></div>

        <!-- ko if: !window.CURRENT_USER.watcher -->
        <!-- ko if: mailing.status() === 0 -->

        <button
          class="f-btn f-btn-text bold mr-3"
          type="button"
          data-bind="click: function() { emitEvent('launch' )}, disable: mailing.allClientsCount() === 0 || mailing.isNew()"
        >
          <span class="f-btn-prepend">
            <span class="f-icon f-icon--start">
              <svg>
                <use href="#start-icon"></use>
              </svg>
            </span>
          </span>

          <!-- ko if: mailing.launchedAt() -->
          <span class="text-left">
            Будет запущена
            <br />
            <div
              class="font-weight-normal"
              style="margin-top: 2px"
              data-bind="text: mailing.launchedAt"
            ></div>
          </span>
          <!-- /ko -->
          <!-- ko ifnot: mailing.launchedAt() -->
          Запустить
          <!-- /ko -->

          <span class="f-btn-append">
            <span class="f-icon f-icon--calendar f-icon-light">
              <svg>
                <use href="#calendar-icon"></use>
              </svg>
            </span>
          </span>
        </button>

        <button
          class="f-btn f-btn-text bold"
          type="button"
          data-bind="click: function() { emitEvent('delete') }, disable: mailing.isNew()"
        >
          <span class="f-btn-prepend">
            <span class="f-icon f-icon--bin f-icon-danger">
              <svg>
                <use href="#bin-icon"></use>
              </svg>
            </span>
          </span>
          Удалить
        </button>

        <button
          class="btn button-add mailings__details-modal-dialog-add-clients-button"
          type="button"
          data-bind="click: openSelectClientsModal"
        >
          Добавить контакты
        </button>
        <!-- /ko -->

        <!-- ko if: mailing.status() === 1 -->
        <button
          type="button"
          class="btn btn-danger btn-with-icon btn-with-icon--simple mailings__details-modal-dialog-stop-button"
          data-bind="click: function() { emitEvent('stop') }"
        >
          Остановить
        </button>
        <!-- /ko -->

        <!-- ko if: mailing.status() === 2 -->
        <button
          type="button"
          class="btn btn-danger btn-with-icon btn-with-icon--simple mailings__details-modal-dialog-resume-button"
          data-bind="click: function() { emitEvent('resume')}"
        >
          Возобновить
        </button>
        <!-- /ko -->
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container">
        <div class="full-width">
          <div class="mailings__details-modal-dialog-body">
            <div
              class="mailings__details-modal-dialog-filters"
              data-bind="visible: mailing.allClientsCount() !== 0 && !mailing.isNew()"
            >
              <div class="mailings__details-modal-dialog-filters-content">
                <!-- ko if: mailing.status() !== 0 -->
                <div class="mailings__details-modal-dialog-filter">
                  <div class="form-group dense-form-group">
                    <label class="form-label">Статус</label>
                    <select
                      multiple
                      data-bind="
                        element: statusFilterElement,
                        selectedOptions: statusFilter,
                        select2: {
                            wrapperCssClass: 'select2-container--form-control',
                            dropdownCssClass: 'dense-form-group__dropdown',
                            containerCss: { 'min-width': '27px' }
                        }
                      "
                      data-placeholder="Все"
                    >
                      <option value="0">Отправлена</option>
                      <option value="1">Письмо открыто</option>
                      <option value="2">Опрос открыт</option>
                      <option value="3">В процессе</option>
                      <option value="4">Заполнена</option>
                    </select>
                  </div>
                </div>
                <div class="mailings__details-modal-dialog-filter">
                  <div class="form-group dense-form-group">
                    <label class="form-label">Каналы связи</label>
                    <select
                      multiple
                      data-bind="
                                                      element: connectionChannelFilterElement,
                                                      selectedOptions: connectionChannelFilter,
                                                      select2: {
                                                          wrapperCssClass: 'select2-container--form-control',
                                                          dropdownCssClass: 'dense-form-group__dropdown',
                                                          containerCss: { 'min-width': '27px' }
                                                      }
                                                  "
                      data-placeholder="Все"
                    >
                      <option value="0">Email</option>
                      <option value="1">SMS</option>
                      <option value="2">Viber</option>
                      <option value="3">Telegram</option>
                    </select>
                  </div>
                </div>
                <!-- /ko -->
                <div class="mailings__details-modal-dialog-filter">
                  <div class="form-group dense-form-group">
                    <label class="form-label">ФИО, телефон, email</label>
                    <input
                      class="form-control"
                      placeholder="Любые"
                      data-bind="
                                              value: contactsFilter,
                                              autosizeInput,
                                              style: { width: '118.69px', 'min-width': '118.69px' }
                                          "
                    />
                  </div>
                </div>
                <!-- ko if: mailing.status() === 1 -->
                <div
                  class="mailings__details-modal-dialog-filter mailings__details-modal-dialog-answered-only-filter"
                >
                  <div class="form-group form-check">
                    <input
                      type="checkbox"
                      class="form-check-input"
                      id="mailingsDetailsModalDialogAnsweredOnlyFilterCheckbox"
                      data-bind="checked: answeredOnlyFilter"
                    />
                    <label
                      class="form-check-label"
                      for="mailingsDetailsModalDialogAnsweredOnlyFilterCheckbox"
                    >
                      Только с ответами
                    </label>
                  </div>
                </div>
                <!-- /ko -->
                <!-- ko if: mailing.status() === 0 -->
                <div class="mailings__details-modal-dialog-filter">
                  <div class="form-group dense-form-group">
                    <label class="form-label">Теги</label>
                    <!-- ko if: tagsDirectory.loaded -->
                    <select
                      multiple
                      data-bind="
                        element: tagFilterElement,
                        selectedOptions: tagFilter,
                        lazySelect2: {
                            wrapperCssClass: 'select2-container--form-control',
                            dropdownCssClass: 'dense-form-group__dropdown tags-dropdown',
                            containerCss: { 'min-width': '27px', },
                            templateResult: tagsTemplateResult,
                        }
                    "
                      data-placeholder="Все"
                    >
                      <!-- ko let: {
                        autoTags: tagsDirectory.getTagsWithConditions(),
                        defaultTags: tagsDirectory.getTagsWithoutConditions()
                      } -->
                      <!-- ko if: autoTags.length -->
                      <optgroup data-auto label="С условием">
                        <!-- ko foreach: { data: autoTags } -->
                        <option
                          data-bind="value: $data.name, text: $data.name"
                        ></option>
                        <!-- /ko -->
                      </optgroup>
                      <!-- /ko -->
                      <!-- ko if: defaultTags.length -->
                      <optgroup label="Без условия">
                        <!-- ko foreach: { data: defaultTags } -->
                        <option
                          data-bind="value: $data.name, text: $data.name"
                        ></option>
                        <!-- /ko -->
                      </optgroup>
                      <!-- /ko -->
                      <!-- /ko -->
                    </select>
                    <!-- /ko -->
                  </div>
                </div>
                <div
                  class="mailings__details-modal-dialog-filter"
                  data-bind="fade: tagFilter().length > 0"
                >
                  <div class="form-group dense-form-group">
                    <label class="form-label">Условие</label>
                    <select
                      data-bind="
                                                  element: tagOperationFilterElement,
                                                  selectedOptions: tagOperationFilter,
                                                  select2: {
                                                      wrapperCssClass: 'select2-container--form-control',
                                                      dropdownCssClass: 'dense-form-group__dropdown'
                                                  }
                                              "
                      data-placeholder="Все"
                    >
                      <option value="1">Один из</option>
                      <option value="2">Все</option>
                    </select>
                  </div>
                </div>
                <div class="mailings__details-modal-dialog-filter">
                  <div class="form-group dense-form-group">
                    <label class="form-label">Пол</label>
                    <select
                      multiple
                      data-bind="
                                                  element: genderFilterElement,
                                                  selectedOptions: genderFilter,
                                                  select2: {
                                                      wrapperCssClass: 'select2-container--form-control',
                                                      dropdownCssClass: 'dense-form-group__dropdown',
                                                      containerCss: { 'min-width': '27px' }
                                                  }
                                              "
                      data-placeholder="Все"
                    >
                      <option value="1">Мужской</option>
                      <option value="2">Женский</option>
                      <option value="3">Не указан</option>
                    </select>
                  </div>
                </div>
                <birthday-filter-dense
                  params="model: birthdayFilter"
                ></birthday-filter-dense>
                <div class="mailings__details-modal-dialog-filter">
                  <div class="form-group dense-form-group">
                    <label class="form-label">Данные контакта</label>
                    <select
                      data-bind="
                        value: filledFilter,
                        select2: {
                            wrapperCssClass: 'select2-container--form-control',
                            dropdownCssClass: 'dense-form-group__dropdown',
                            allowClear: true,
                            placeholder: 'Не выбрано'
                        }
                    "
                      data-placeholder="Не выбрано"
                    >
                      <option></option>
                      <option value="1">Заполнены</option>
                      <option value="0">Не заполнены</option>
                    </select>
                  </div>
                </div>
                <div
                  class="mailings__details-modal-dialog-filter"
                  data-bind="fade: clientFields.loaded() && ['1', '0'].includes(filledFilter())"
                >
                  <div class="form-group dense-form-group">
                    <label class="form-label">Значения</label>
                    <select
                      multiple
                      data-bind="
                        selectedOptions: fieldsFilter,
                        lazySelect2: {
                            wrapperCssClass: 'select2-container--form-control',
                            minimumResultsForSearch: 0,
                            dropdownCssClass: 'dense-form-group__dropdown',
                            containerCss: { 'min-width': '40px' },
                        }
                    "
                      data-placeholder="Выберите поля"
                    >
                      <optgroup label="Системные">
                        <!-- ko foreach: {data: clientFields.system, as: 'field'} -->
                        <option
                          data-bind="text: field.text, value: field.id"
                        ></option>
                        <!-- /ko -->
                      </optgroup>
                      <optgroup label="Пользовательские">
                        <!-- ko foreach: {data: clientFields.additional, as: 'field'} -->
                        <option
                          data-bind="text: field.text, value: field.id"
                        ></option>
                        <!-- /ko -->
                      </optgroup>
                    </select>
                  </div>
                </div>
                <!-- /ko -->
                <div
                  class="spacer mailings__details-modal-dialog-filters-actions-spacer"
                ></div>
                <div class="mailings__details-modal-dialog-filters-actions">
                  <button
                    type="submit"
                    class="btn btn-link mailings__details-modal-dialog-filters-reset-button"
                    data-bind="click: function () {resetFilters(); reloadClients(1);}"
                  >
                    Сбросить
                  </button>
                  <button
                    type="submit"
                    class="btn btn-success btn-with-icon btn-apply mailings__details-modal-dialog-filters-apply-button"
                    data-bind="click: function () { reloadClients(1); }"
                  >
                    Применить
                  </button>
                </div>
              </div>
            </div>
            <div class="meta-row">
              <!-- ko if: mailing.allClientsCount-->
              <div class="meta-item pl-10p mr-n10p flex-grow-0">
                <div class="foquz-stats-group">
                  <foquz-stats-item>
                    <div class="value">
                      <span
                        data-bind="text: $parent.mailing.filterClientsCount().toLocaleString()"
                      ></span>
                      <small
                        >/<span
                          data-bind="text: $parent.mailing.allClientsCount().toLocaleString()"
                        ></span
                      ></small>
                    </div>
                    <div class="label">Контактов<br />добавлено</div>
                  </foquz-stats-item>
                </div>
              </div>
              <!-- /ko -->
              <!-- ko if: mailing.status() !== 0 -->
              <div
                class="meta-item pl-10p mr-n10p meta-item--running"
                data-bind="log: 'stats'"
              >
                <div
                  class="foquz-stats-group"
                  data-bind="let: { stats: mailing.statistics(), filteredStats: mailing.filteredStatistics() }"
                >
                  <foquz-stats-item>
                    <div class="value f-color-mailing-sent">
                      <span
                        data-bind="text: filteredStats.sent.toLocaleString()"
                      ></span>
                      <small
                        >/<span
                          data-bind="text: stats.sent.toLocaleString()"
                        ></span
                      ></small>
                    </div>
                    <div class="label">Отправлено</div>
                  </foquz-stats-item>
                  <foquz-stats-item>
                    <div class="value f-color-mailing-mail-open">
                      <span
                        data-bind="text: filteredStats.emailOpened.toLocaleString()"
                      ></span>
                      <small
                        >/<span
                          data-bind="text: stats.emailOpened.toLocaleString()"
                        ></span
                      ></small>
                    </div>
                    <div class="label">
                      Письмо открыто
                      <br />
                      <span class="font-weight-normal"
                        >(только для
                        <span class="font-weight-500">email</span>)</span
                      >
                    </div>
                  </foquz-stats-item>
                  <foquz-stats-item>
                    <div class="value f-color-mailing-open">
                      <span
                        data-bind="text: filteredStats.open.toLocaleString()"
                      ></span>
                      <small
                        >/<span
                          data-bind="text: stats.open.toLocaleString()"
                        ></span
                      ></small>
                    </div>
                    <div class="label">Опрос открыт</div>
                  </foquz-stats-item>
                  <foquz-stats-item>
                    <div class="value f-color-mailing-progress">
                      <span
                        data-bind="text: filteredStats.progress.toLocaleString()"
                      ></span>
                      <small
                        >/<span
                          data-bind="text: stats.progress.toLocaleString()"
                        ></span
                      ></small>
                    </div>
                    <div class="label">В процессе</div>
                  </foquz-stats-item>
                  <foquz-stats-item>
                    <div class="value f-color-mailing-filled">
                      <span
                        data-bind="text: filteredStats.done.toLocaleString()"
                      ></span>
                      <small
                        >/<span
                          data-bind="text: stats.done.toLocaleString()"
                        ></span
                      ></small>
                    </div>
                    <div class="label">Заполнено</div>
                  </foquz-stats-item>
                </div>
              </div>
              <!-- /ko -->

              <div class="meta-group">
                <div
                  class="meta-item pl-30p pr-20p py-15p mr-n10p border-right"
                >
                  <div class="mailings__details-modal-dialog-main-info">
                    <div
                      class="mailings__details-modal-dialog-main-info-element"
                    >
                      <span
                        class="mailings__details-modal-dialog-main-info-element-value"
                        data-bind="text: mailing.createdAt"
                      ></span>
                      <span
                        class="mailings__details-modal-dialog-main-info-element-label"
                        >Создан</span
                      >
                    </div>
                    <div
                      class="mailings__details-modal-dialog-main-info-element mailings__details-modal-dialog-main-info-status-element"
                    >
                      <span
                        class="mailings__details-modal-dialog-main-info-element-value mailings__details-modal-dialog-main-info-status-element-value"
                        data-bind="class: getMainInfoStatusElementValueClass(mailing.status()), text: getMainInfoStatusElementValueText(mailing.status())"
                      >
                      </span>
                      <span
                        class="mailings__details-modal-dialog-main-info-element-label"
                        >Статус</span
                      >
                    </div>
                    <div
                      class="mailings__details-modal-dialog-main-info-element"
                    >
                      <span
                        class="mailings__details-modal-dialog-main-info-element-value"
                        data-bind="text: mailing.author"
                      ></span>
                      <span
                        class="mailings__details-modal-dialog-main-info-element-label"
                        >Автор</span
                      >
                    </div>
                  </div>
                </div>
                <div class="meta-item pl-30p pr-20p py-15p">
                  <div
                    class="mailings__details-modal-dialog-notifications-block"
                  >
                    <!-- ko foreach: mailing.order -->
                    <!-- ko let: { channel: $parent.mailing.notifications[name.toLowerCase()](), channelId: name.toLowerCase()  } -->
                    <!-- ko if: channel -->
                    <a
                      class="mailings__details-modal-dialog-notifications-block-item"
                      data-bind="
                            class: 'mailings__details-modal-dialog-notifications-block-' + channelId + '-item',
                            css: {
                                'mailings__details-modal-dialog-notifications-block-item--disabled': !channel
                            },
                            
                                title: channel
                                ? 'Канал связи &#34;' + name + '&#34;. Количество повторов: ' + channel.repeatCount
                                : 'Канал связи &#34;' + name + '&#34; не используется для рассылки',
                           
                            fbPopper
                        "
                      href="javascript:void(0)"
                    >
                      <i
                        class="icon"
                        data-bind="class: 'icon-' + channelId"
                      ></i>
                      <div
                        class="mailings__details-modal-dialog-notifications-block-item-repeat-count"
                        data-bind="text: channel ? channel.repeatCount : 0"
                      ></div>
                    </a>
                    <!-- /ko -->
                    <!-- /ko -->
                    <!-- /ko -->
                    <div class="mailings__channel-settings-link">
                      <fc-button
                        params="
                          label: 'Настройки каналов',
                          color: 'primary',
                          mode: 'text',
                          linkMode: true,
                          linkAttrs: {
                            href: `./channels?id=${POLL.id}&amp;mailing_id=${mailing.id()}`,
                          },
                        "
                      ></fc-button>
                      <!-- ko if: mailing.customChannelSettings -->
                      <div class="channel-settings-link__individual-sign">
                        <svg viewBox="0 0 18 16" width="14" height="13">
                          <use
                            href="#foquz-icon-user"
                            xlink:href="#foquz-icon-user"
                          ></use>
                        </svg>
                        <span> Индивидуальные </span>
                      </div>
                      <!-- /ko -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="meta-group-right meta-item pr-30p">
                <div class="csv-button">
                  <button
                    class="f-btn btn-default clients__save-as-csv-button"
                    data-bind="
                      disable: !mailing.allClientsCount(),
                      click: function() { exportCSV() },
                      fbPopper,
                      fbPopperDisabled: mailing.allClientsCount(),
                      title: 'Для выгрузки данных в CSV файл необходимо добавить контакты в приглашение',
                    "
                  >
                    Скачать в CSV
                  </button>
                </div>
                <button
                  type="button"
                  class="btn btn-icon btn-icon--simple foq-table__edit-columns-button"
                  title="Настройка столбцов"
                  data-bind="click: openEditColumnsModal"
                ></button>
              </div>
            </div>
            <span
              class="mailings__details-modal-dialog-clients-placeholder"
              data-bind="visible: !updating() && (mailing.isNew() || mailing.allClientsCount() == 0)"
            >
              Добавьте контакты для приглашений
            </span>
            <!-- ko if: updating -->
            <div
              class="p-4 text-center f-color-primary"
              title="Пожалуйста, подождите ..."
            >
              <i class="fa fa-spinner fa-pulse fa-2x fa-fw top"></i>
            </div>
            <!-- /ko -->
            <div
              class="foq-table__section mailings__details-modal-dialog-table-section"
              data-bind="visible: mailing.allClientsCount() !== 0 && !mailing.isNew() && !updating()"
            >
              <div
                class="foq-table__group-actions-wrapper mailings__details-modal-dialog-table-group-actions-wrapper"
              >
                <div
                  class="foq-table__group-actions mailings__details-modal-dialog-table-group-actions"
                  data-bind="fade: selectedClientCount() !== 0"
                >
                  <span class="foq-table__group-actions-counter">
                    <span class="foq-table__group-actions-counter-label"
                      >Выбрано</span
                    >
                    <span
                      class="foq-table__group-actions-counter-value"
                      data-bind="text: selectedClientCount()"
                    ></span>
                  </span>
                  <button
                    type="submit"
                    class="btn btn-default foq-table__group-actions-select-all-button"
                    data-bind="click: selectAllClient"
                  >
                    Выбрать все
                  </button>
                  <select
                    data-bind="value: groupAction, select2: {
                                          containerCssClass: 'form-control',
                                          wrapperCssClass: 'select2-container--form-control foq-table__group-actions-selector',
                                          dropdownCssClass: 'foq-table__group-actions-selector-dropdown',
                                          minimumResultsForSearch: 0,
                                          allowClear: true
                                      }"
                    data-placeholder="Выберите действие"
                  >
                    <option></option>
                    <option value="1">Удалить</option>
                  </select>
                  <!-- ko if: ['2', '3'].includes(groupAction()) -->
                  <div
                    data-bind="component: {
                                                  name: 'clients-tag-input',
                                                  params: {
                                                      value: groupActionSelectedTags,
                                                      list: $root.tags,
                                                      ...{ ['addButton']: groupAction() === '2' ? null : { label: 'Добавить тег' } }
                                                  }
                                              }"
                    class="mailings__details-modal-dialog-table-group-actions-tags"
                  ></div>
                  <!-- /ko -->
                  <button
                    type="submit"
                    class="btn btn-danger foq-table__group-actions-dismiss-button"
                    data-bind="click: unselectAllClient"
                  ></button>
                  <button
                    type="submit"
                    class="btn btn-success foq-table__group-actions-apply-button"
                    data-bind="click: performGroupAction, attr: { disabled: groupAction() === '' }"
                  ></button>
                </div>
              </div>
              <!-- ko component: {
                name: 'group-actions',
                params: {
                  controller: groupActions
                }
              } -->
              <!-- /ko -->
              <table
                class="table foq-table foq-table--filterable mailings__details-modal-dialog-table"
              >
                <thead>
                  <tr>
                    <th class="foq-table__outer-element-head-cell"></th>
                    <!-- ko if: mailing.status() === 0 && !window.CURRENT_USER.watcher -->
                    <th class="foq-table__selection-head-cell">
                      <div class="form-check foq-table__checkbox">
                        <input
                          type="checkbox"
                          class="form-check-input"
                          data-bind="checked: checked.allItemsChecked()"
                        />
                        <label
                          class="form-check-label"
                          data-bind="click: function () { $mailingModal.checked.toggleAll(); }"
                        ></label>
                      </div>
                    </th>
                    <!-- /ko -->
                    <!-- ko if: mailing.status() !== 0 -->
                    <!-- ko if: launchedTableColumns.state.passed -->
                    <th>
                      <div class="foq-table__head-cell-title cursor-pointer">
                        <div class="foq-table__head-cell-name">Пройдена</div>
                        <i
                          class="foq-table__sorting-icon foq-table__sorting-icon--default"
                          order-key="passedAt"
                        ></i>
                        <!-- ko if: passedAtStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: passedAtStringFilter,
                          attr: {
                            placeholder: launchedTableColumns.placeholders.passed,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- ko if: launchedTableColumns.state.status -->
                    <th>
                      <div class="foq-table__head-cell-title cursor-pointer">
                        <div class="foq-table__head-cell-name">Статус</div>
                        <i
                          class="foq-table__sorting-icon foq-table__sorting-icon--default"
                          order-key="status"
                        ></i>
                        <!-- ko if: statusStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: statusStringFilter,
                          attr: {
                            placeholder: launchedTableColumns.placeholders.status,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- ko if: launchedTableColumns.state.channel -->
                    <th>
                      <div class="foq-table__head-cell-title">
                        <div class="foq-table__head-cell-name">Канал связи</div>
                        <i
                          class="foq-table__sorting-icon--fake"
                          order-key="channel"
                        ></i>
                        <!-- ko if: lastConnectionChannelStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: lastConnectionChannelStringFilter,
                          attr: {
                            placeholder: launchedTableColumns.placeholders.channel,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- /ko -->

                    <!-- ko if: tableColumns.state.name -->
                    <th>
                      <div class="foq-table__head-cell-title cursor-pointer">
                        <div
                          class="foq-table__head-cell-name"
                          data-bind="text: 'ФИО'"
                        ></div>
                        <i class="foq-table__sorting-icon foq-table__sorting-icon--default" order-key="name"></i>
                        <!-- ko if: nameStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: nameStringFilter,
                          attr: {
                            placeholder: tableColumns.placeholders.name,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- ko if: tableColumns.state.phone -->
                    <th>
                      <div class="foq-table__head-cell-title cursor-pointer">
                        <div class="foq-table__head-cell-name">Телефон</div>
                        <i
                          class="foq-table__sorting-icon foq-table__sorting-icon--default"
                          order-key="phone"
                        ></i>
                        <!-- ko if: phoneStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: phoneStringFilter,
                          attr: {
                            placeholder: tableColumns.placeholders.phone,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- ko if: tableColumns.state.email -->
                    <th>
                      <div class="foq-table__head-cell-title cursor-pointer">
                        <div class="foq-table__head-cell-name">Email</div>
                        <i
                          class="foq-table__sorting-icon foq-table__sorting-icon--default"
                          order-key="email"
                        ></i>
                        <!-- ko if: emailStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: emailStringFilter,
                          attr: {
                            placeholder: tableColumns.placeholders.email,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- ko if: mailing.status() === 0 -->
                    <!-- ko if: newTableColumns.state.gender -->
                    <th>
                      <div class="foq-table__head-cell-title cursor-pointer">
                        <div class="foq-table__head-cell-name">Пол</div>
                        <i
                          class="foq-table__sorting-icon foq-table__sorting-icon--default"
                          order-key="gender"
                        ></i>
                        <!-- ko if: genderStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: genderStringFilter,
                          attr: {
                            placeholder: tableColumns.placeholders.gender,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- ko if: newTableColumns.state.birthday -->
                    <th>
                      <div class="foq-table__head-cell-title cursor-pointer">
                        <div class="foq-table__head-cell-name">
                          Дата рождения
                        </div>
                        <i
                          class="foq-table__sorting-icon foq-table__sorting-icon--default"
                          order-key="birthday"
                        ></i>
                        <!-- ko if: birthdayStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: birthdayStringFilter,
                          attr: {
                            placeholder: tableColumns.placeholders.birthday,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- ko if: newTableColumns.state.tags -->
                    <th>
                      <div class="foq-table__head-cell-title">
                        <div class="foq-table__head-cell-name disable-sort">
                          Теги
                        </div>
                        <i
                          class="foq-table__sorting-icon"
                          order-key="tags"
                          hidden
                        ></i>
                        <!-- ko if: tagStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: tagStringFilter,
                          attr: {
                            placeholder: tableColumns.placeholders.tags,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- ko if: newTableColumns.state.addedAt -->
                    <th>
                      <div class="foq-table__head-cell-title cursor-pointer">
                        <div class="foq-table__head-cell-name foq-table__head-cell-name--active">Добавлен</div>
                        <i
                          class="foq-table__sorting-icon foq-table__sorting-icon--order_desc"
                          order-key="addedAt"
                        ></i>
                        <!-- ko if: addedAtStringFilter() !== '' -->
                        <i class="foq-table__filtering-icon"></i>
                        <!-- /ko -->
                      </div>
                      <input
                        class="foq-table__head-cell-filter"
                        data-bind="
                          textInput: addedAtStringFilter,
                          attr: {
                            placeholder: tableColumns.placeholders.addedAt,
                          },
                        "
                      />
                    </th>
                    <!-- /ko -->
                    <!-- /ko -->
                    <th
                      class="mailings__details-modal-dialog-table-actions-head-cell"
                    ></th>
                    <th class="foq-table__outer-element-head-cell"></th>
                  </tr>
                </thead>
                <tbody>
                  <!-- ko foreach: { data: mailing.clients, as: 'client', beforeRemove: beforeRemove, afterAdd: afterAdd } -->
                  <tr
                    class="foq-table__row mailings__details-modal-dialog-table-row"
                    data-bind="css: {'foq-table__row--selected': client.checked}
                                              "
                  >
                    <td class="foq-table__outer-element-cell"></td>

                    <!-- ko if: $mailingModal.mailing.status() === 0 && !window.CURRENT_USER.watcher -->
                    <td class="foq-table__selection-cell">
                      <div class="form-check foq-table__checkbox">
                        <input
                          type="checkbox"
                          class="form-check-input"
                          data-bind="checked: checked"
                        />
                        <label
                          class="form-check-label"
                          data-bind="click: function (_, event) {
                          $mailingModal.checked.toggleItem(client);
                          event.stopPropagation();
                        }"
                        ></label>
                      </div>
                    </td>
                    <!-- /ko -->
                    <!-- ko if: $mailingModal.mailing.status() !== 0 -->
                    <!-- ko if: $mailingModal.launchedTableColumns.state.passed -->
                    <td
                      data-bind="text: passedAt !== null ? passedAt :  '–'"
                    ></td>
                    <!-- /ko -->
                    <!-- ko if: $mailingModal.launchedTableColumns.state.status -->
                    <td>
                      <span
                        class="mailings__details-modal-dialog-table-status"
                        data-bind="text: $mailingModal.getTableStatusText(status), class: $mailingModal.getTableStatusClass(status)"
                      >
                      </span>
                    </td>
                    <!-- /ko -->
                    <!-- ko if: $mailingModal.launchedTableColumns.state.channel -->
                    <td>
                      <!-- ko if: sendingList.length >0 -->
                      <!-- ko let: { lastSending: sendingList[sendingList.length - 1] } -->
                      <div class="dropdown">
                        <div
                          class="mailings__details-modal-dialog-connection-channel mailings__details-modal-dialog-table-connection-channel"
                          data-toggle="dropdown"
                          data-flip="false"
                          data-bind="
                                                                          text: $mailingModal.getConnectionChannelText(lastSending.connectionChannel),
                                                                          class: $mailingModal.getConnectionChannelClass(lastSending.connectionChannel)
                                                                      "
                        ></div>
                        <div
                          class="dropdown-menu mailings__details-modal-dialog-table-sending-list-dropdown-menu"
                        >
                          <div
                            class="mailings__details-modal-dialog-table-sending-list-table-wrapper"
                            data-bind="fScrollbar"
                          >
                            <table
                              class="table foq-table mailings__details-modal-dialog-table-sending-list-table"
                            >
                              <thead>
                                <tr>
                                  <th>Отправлена</th>
                                  <th>Каналы</th>
                                  <th
                                    class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-repeats-head-cell"
                                  >
                                    Повторы
                                  </th>
                                  <th
                                    class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-response-head-cell"
                                  >
                                    Ответ
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                <!-- ko foreach: sendingList -->
                                <tr>
                                  <td data-bind="text: sentAt"></td>
                                  <td>
                                    <div
                                      class="mailings__details-modal-dialog-connection-channel"
                                      data-bind="
                                                                                                  text: $mailingModal.getConnectionChannelText(connectionChannel),
                                                                                                  class: $mailingModal.getConnectionChannelClass(connectionChannel)
                                                                                              "
                                    ></div>
                                  </td>
                                  <td
                                    class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-repeats-cell"
                                  >
                                    <div
                                      class="mailings__details-modal-dialog-repeats"
                                      data-bind="text: repeatCount"
                                    ></div>
                                  </td>
                                  <td
                                    class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-response-cell"
                                    data-bind="text: hasResponse ? 'есть' : '—'"
                                  ></td>
                                </tr>
                                <!-- /ko -->
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                      <!-- /ko -->
                      <!-- /ko -->
                    </td>
                    <!-- /ko -->
                    <!-- /ko -->

                    <!-- ko if: $mailingModal.tableColumns.state.name -->
                    <td data-bind="text: name"></td>
                    <!-- /ko -->
                    <!-- ko if: $mailingModal.tableColumns.state.phone -->
                    <td
                      class="mailings__details-modal-dialog-table-phone-cell"
                      data-bind="text: phone !== null ? phone : ''"
                    ></td>
                    <!-- /ko -->
                    <!-- ko if: $mailingModal.tableColumns.state.email -->
                    <td data-bind="text: email !== null ? email : ''"></td>
                    <!-- /ko -->

                    <!-- ko if: $mailingModal.mailing.status() === 0 -->
                    <!-- ko if: $mailingModal.newTableColumns.state.gender -->
                    <td>
                      <!-- ko if: gender === 1 -->
                      муж.
                      <!-- /ko -->
                      <!-- ko if: gender === 2 -->
                      жен.
                      <!-- /ko -->
                      <!-- ko if: gender === 3 -->
                      —
                      <!-- /ko -->
                    </td>
                    <!-- /ko -->
                    <!-- ko if: $mailingModal.newTableColumns.state.birthday -->
                    <td data-bind="text: birthday"></td>
                    <!-- /ko -->
                    <!-- ko if: $mailingModal.newTableColumns.state.tags -->
                    <td>
                      <div class="mailings__details-modal-dialog-tag-list">
                        <div
                          class="mailings__details-modal-dialog-tag-list-content"
                        >
                          <!-- ko foreach: tags -->
                          <span
                            class="mailings__details-modal-dialog-tag-list-item"
                            data-bind="text: $data, css: {
                            'color-success': $mailingModal.isAutoTag($data)
                          }"
                          >
                          </span>
                          <!-- /ko -->
                        </div>
                      </div>
                    </td>
                    <!-- /ko -->
                    <!-- ko if: $mailingModal.newTableColumns.state.addedAt -->
                    <td data-bind="text: addedAt"></td>
                    <!-- /ko -->
                    <!-- /ko -->
                    <td
                      class="mailings__details-modal-dialog-table-actions-cell"
                    ></td>
                    <td class="foq-table__outer-element-cell"></td>
                  </tr>
                  <!-- /ko -->
                </tbody>
              </table>
              <div
                id="clients-loader"
                class="pages-loader"
                style="display: none"
                title="Пожалуйста, подождите ..."
              >
                <i class="fa fa-spinner fa-pulse fa-2x fa-fw top"></i>
              </div>
              <!-- ko template: {
                foreach: templateIf(showEmptyMessage(), $data),
                afterAdd: fadeAfterAddFactory(200, 200)
              } -->
              <div class="text-center p-4 color-service">Ничего не найдено</div>
              <!-- /ko -->

              <observer-target
                params="action: function() { nextPage() }"
              ></observer-target>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <foquz-modals-container params="modals: modals"></foquz-modals-container>
</sidesheet>
<dialogs-container params="ref: dialogs"></dialogs-container>
<!-- /ko -->
