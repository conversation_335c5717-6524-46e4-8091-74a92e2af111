export function setMailingStatus (mailing, newStatus, convertResult, onSuccess, date, time) {
  var wasStatus = mailing.status()
  mailing.status(newStatus)

  if (newStatus == 1) {
      var now = moment()
      mailing.launched_at = now.format('YYYY-MM-DD HH:mm:ss');
      mailing.launchedAt(now.format('DD.MM.YYYY HH:mm'));
  }

  var m = ko.toJS(mailing);
  delete m.launchedAt;

  m.clients = undefined
  $.post('/foquz/foquz-poll-mailings/update', m, (response) => {
      var errors;
      if (!response.success ) {
          mailing.status(wasStatus);
          if (response.data.errors instanceof Array) {
              errors = $.map(response.data.errors, function (e) { return e.message; }).join(' ');
          } else {
              errors = 'Ошибка сохранения списка рассылок';
          }
          $.get('/foquz/foquz-poll-mailings/get?id='+mailing.id(), null, (response) => {
              if (response.success) {
                  mailings.replace(mailing, convertResult(response.data.mailing))
              }
          }).always(() => alert(errors))
          alert('Ошибка сохранения списка рассылок');
      } else {
          onSuccess();
      }
  }, 'json')
  .fail(function () {
      mailing.status(wasStatus);
      $.get('/foquz/foquz-poll-mailings/get?id='+mailing.id(), null, (response) => {
          if (response.success) {
              mailings.replace(mailing, convertMailingList(response.data.mailing))
              alert('Ошибка сохранения списка рассылок');
          }
      })
  });
}

export function setMailingLaunchStatus(mailing, launchData, convertResult, onSuccess) {
    let launchType = launchData.type;
    if (launchType == '1') {
        setMailingStatus(mailing, 1, convertResult, onSuccess);
        return;
    }

    if (launchType == '2') {
        let date = moment(launchData.date, 'DD.MM.YYYY HH:mm:ss');
        mailing.launched_at = date.format('YYYY-MM-DD HH:mm:ss');
        mailing.launchedAt(date.format('DD.MM.YYYY HH:mm'));
        setMailingStatus(mailing, 0, convertResult, onSuccess);
        return;
    }

    mailing.launched_at = null;
    mailing.launchedAt(null);
    setMailingStatus(mailing, 0, convertResult, onSuccess);
}
