<foquz-dialog
  params="
    ref: modal,
    dialogWrapper: $component,
  "
>
  <foquz-dialog-header>
    <!-- ko text: $parent.headerText --><!-- /ko -->
  </foquz-dialog-header>
  <div class="foquz-dialog__body">
    <p data-bind="text: modalText"></p>
    <!-- ko if: showWithAnswers -->
    <fc-check
      params="
        checked: withAnswers,
        label: withAnswersText,
      "
    ></fc-check>
    <!-- /ko -->
  </div>
  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="
          click: function() {
            $dialog.hide()
          }
        "
      >
        Отменить
      </button>
      <button
        type="button"
        class="f-btn f-btn-danger"
        data-bind="
          click: function() {
            submit();
          },
        "
      >
        Удалить
      </button>
    </div>
  </div>
</foquz-dialog>
