import { DialogWrapper } from "Dialogs/wrapper";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    this.showWithAnswers = ko.observable(params.hasAnswers);
    this.withAnswers = ko.observable(false);
    this.headerText = params.headerText || "Удаление приглашения";
    this.modalText = params.modalText || "Приглашение будет удалено без возможности восстановления.";
    this.withAnswersText = params.withAnswersText || "Удалить созданные анкеты для контактов и анкеты с ответами, полученные по этому приглашению";
  }

  submit() {
    this.emitEvent("submit", {
      with_answers: this.withAnswers(),
    });
    this.hide();
  }
}
