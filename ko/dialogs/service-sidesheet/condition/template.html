<!-- ko if: canRemove -->
<button type="button"
        class="f-btn f-btn-danger f-btn--square service-condition__remove"
        data-bind="click: onRemove, tooltip"
        title="Удалить">
  <foquz-icon params="icon: 'times'" class="f-icon-sm"></foquz-icon>
</button>
<!-- /ko -->

<div class="service-condition__content">
  <div class="form-group">
    <label class="form-label">
      Что проверять
      <button type="button"
              class="btn-question"
              data-bind="tooltip"
              title="Что проверять"></button>
    </label>

    <div class="hat-radio-group form-control"
         data-bind="let: { inputName: 'condition-type-' + unique }">
      <div class="hat-radio-group__radio"
           data-bind="let: { inputId: 'condition-type-phone-' + unique }">
        <input type="radio"
               class="hat-radio-group__radio-input"
               value="1"
               data-bind="checked: conditionModel.type, attr: { name: inputName, id: inputId }">
        <label data-bind="attr: { 'for': inputId }"
               class="hat-radio-group__radio-label">
          <span class="hat-radio-group__radio-indicator"></span>
          Телефон
        </label>
      </div>

      <div class="hat-radio-group__radio"
           data-bind="let: { inputId: 'condition-type-email-' + unique }">
        <input type="radio"
               class="hat-radio-group__radio-input"
               value="2"
               data-bind="checked: conditionModel.type, attr: { name: inputName, id: inputId }">
        <label data-bind="attr: { 'for': inputId }"
               class="hat-radio-group__radio-label">
          <span class="hat-radio-group__radio-indicator"></span>
          Email
        </label>
      </div>
    </div>
  </div>

  <div class="form-group">
    <label class="form-label">
      Условие
      <button type="button"
              class="btn-question"
              data-bind="tooltip"
              title="Условие"></button>
    </label>

    <div class="row">
      <div class="col-6">
        <select data-bind="
                  value: conditionModel.condition,
                  valueAllowUnset: true,
                  lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control'
                  }
              "
                data-placeholder="">
          <option value="1">Равно</option>
          <option value="2">Содержит</option>
          <option value="3">Маска</option>
        </select>
      </div>

      <div class="col-6">
        <!-- ko ifnot: conditionModel.condition() == 3 -->
        <input type="text" class="form-control" data-bind="textInput: conditionModel.value, css: {
          'is-invalid': formControlErrorStateMatcher(conditionModel.value)
        }">
        <validation-feedback params="show: formControlErrorStateMatcher(conditionModel.value), text: conditionModel.value.error"></validation-feedback>
        <!-- /ko -->

        <!-- ko if: conditionModel.condition() == 3 -->
        <input type="text" class="form-control" placeholder="*@*.*" data-bind="textInput: conditionModel.value, css: {
          'is-invalid': formControlErrorStateMatcher(conditionModel.value)
        }">
        <validation-feedback params="show: formControlErrorStateMatcher(conditionModel.value), text: conditionModel.value.error"></validation-feedback>
        <div class="form-hint">Звездочкой обозначаются изменяемые части маски</div>
        <!-- /ko -->


      </div>
    </div>
  </div>
</div>
