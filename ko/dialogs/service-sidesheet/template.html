<!-- ko let: {$ctx: $component} -->
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">
        Подключить сервис
      </h2>
    </div>
  </div>

  <div class="foquz-dialog__body"
       data-bind="descendantsComplete: function() { onInit(); }">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container pb-4">
        <div class="row">
          <div class="col-6"
               data-bind="using: formModel()">
            <div class="form-group">
              <label class="form-label"
                     for="service">Сервис</label>

              <button class="btn-question"
                      data-bind="tooltip, tooltipPlacement: 'top'"
                      type="button"
                      title="Сервис">
              </button>

              <select data-bind="
                value: service,
                valueAllowUnset: true,
                lazySelect2: {
                    containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control'
                },
                css: {
                    'is-invalid': $ctx.formControlErrorStateMatcher(service),
                    'is-valid': $ctx.formControlSuccessStateMatcher(service)
                }
            "
                      id="service"
                      data-placeholder="">
                <option></option>
                <!-- ko foreach: $ctx.serviceTypes -->
                <option data-bind="value: id, text: name"></option>
                <!-- /ko -->
              </select>

              <!-- ko if: $ctx.formControlErrorStateMatcher(service) -->
              <div class="form-error"
                   data-bind="text: service.error()"></div>
              <!-- /ko -->
            </div>

            <div class="form-group">
              <label class="form-label"
                     for="name">Название</label>

              <button class="btn-question"
                      data-bind="tooltip, tooltipPlacement: 'top'"
                      type="button"
                      title="Название">
              </button>

              <div class="chars-counter chars-counter--type_input"
                   data-bind="charsCounter, charsCounterCount: name().length">
                <input class="form-control"
                       data-bind="
                        textInput: name,
                        css: {
                            'is-invalid': $ctx.formControlErrorStateMatcher(name),
                            'is-valid': $ctx.formControlSuccessStateMatcher(name)
                        }
                    "
                       id="name"
                       maxlength="50">

                <div class="chars-counter__value"></div>
              </div>

              <!-- ko if: $ctx.formControlErrorStateMatcher(name) -->
              <div class="form-error"
                   data-bind="text: name.error()"></div>
              <!-- /ko -->
            </div>

            <div class="service-access-details-modal-dialog__additional-controls-container">
              <div class="service-access-details-modal-dialog__additional-controls"
                   data-bind="fade: service() === '1', fadeInDelay: 200, using: $ctx.iikoBizServiceSubformViewModel.formModel()">

                <div class="form-group">
                  <label class="form-label"
                         for="main-api-url">Основной URL API</label>

                  <button class="btn-question"
                          data-bind="tooltip, tooltipPlacement: 'top'"
                          type="button"
                          title="Основной URL API">
                  </button>

                  <div class="chars-counter chars-counter--type_input"
                       data-bind="charsCounter, charsCounterCount: mainApiUrl().length">
                    <input class="form-control"
                           data-bind="
                                    textInput: mainApiUrl,
                                    css: {
                                        'is-invalid': $ctx.formControlErrorStateMatcher(mainApiUrl),
                                        'is-valid': $ctx.formControlSuccessStateMatcher(mainApiUrl)
                                    }
                                "
                           id="main-api-url"
                           maxlength="50">

                    <div class="chars-counter__value"></div>
                  </div>

                  <!-- ko if: $ctx.formControlErrorStateMatcher(mainApiUrl) -->
                  <div class="form-error"
                       data-bind="text: mainApiUrl.error()"></div>
                  <!-- /ko -->
                </div>

                <div class="form-group">
                  <div class="form-group-header">
                    <label class="form-label"
                           for="additional-api-url">Дополнительный URL API</label>

                    <button class="btn-question"
                            data-bind="tooltip, tooltipPlacement: 'top'"
                            type="button"
                            title="Дополнительный URL API">
                    </button>
                    <span class="spacer"></span>
                    <span class="form-group-note">необязательное</span>
                  </div>

                  <div class="chars-counter chars-counter--type_input"
                       data-bind="charsCounter, charsCounterCount: additionalApiUrl().length">
                    <input class="form-control"
                           data-bind="
                                    textInput: additionalApiUrl,
                                    css: {
                                        'is-invalid': $ctx.formControlErrorStateMatcher(additionalApiUrl),
                                        'is-valid': $ctx.formControlSuccessStateMatcher(additionalApiUrl)
                                    }
                                "
                           id="additional-api-url"
                           maxlength="50">

                    <div class="chars-counter__value"></div>
                  </div>

                  <!-- ko if: $ctx.formControlErrorStateMatcher(additionalApiUrl) -->
                  <div class="form-error"
                       data-bind="text: additionalApiUrl.error()"></div>
                  <!-- /ko -->
                </div>


                <div class="row">
                  <div class="col-6">
                    <div class="form-group">
                      <label class="form-label"
                             for="login">Логин</label>

                      <button class="btn-question"
                              data-bind="tooltip, tooltipPlacement: 'top'"
                              type="button"
                              title="Логин">
                      </button>

                      <div class="chars-counter chars-counter--type_input"
                           data-bind="charsCounter, charsCounterCount: login().length">
                        <input class="form-control"
                               data-bind="
                                    textInput: login,
                                    css: {
                                        'is-invalid': $ctx.formControlErrorStateMatcher(login),
                                        'is-valid': $ctx.formControlSuccessStateMatcher(login)
                                    }
                                "
                               id="login"
                               maxlength="50"
                               autocomplete="off">

                        <div class="chars-counter__value"></div>
                      </div>

                      <!-- ko if: $ctx.formControlErrorStateMatcher(login) -->
                      <div class="form-error"
                           data-bind="text: login.error()"></div>
                      <!-- /ko -->
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="form-group">
                      <label class="form-label"
                             for="password">Пароль</label>

                      <button class="btn-question"
                              data-bind="tooltip, tooltipPlacement: 'top'"
                              type="button"
                              title="Пароль">
                      </button>

                      <!-- ko let: { isPasswordHidden: ko.observable(true), tooltip: ko.observable(null) } -->
                      <div class="input-group inner-input-group password-inner-input-group">
                        <input class="form-control"
                               data-bind="
                                        textInput: password,
                                        css: {
                                            'is-invalid': $ctx.formControlErrorStateMatcher(password),
                                            'is-valid': $ctx.formControlSuccessStateMatcher(password)
                                        },
                                        attr: { type: isPasswordHidden() ? 'password' : 'text' }
                                    "
                               id="password"
                               maxlength="50"
                               autocomplete="off">
                        <button class="btn btn-icon btn-icon--simple inner-input-group__suffix password-inner-input-group__visibility-toggle-icon-button-suffix"
                                data-bind="
                                            click: function () { isPasswordHidden(!isPasswordHidden()); },
                                            css: {
                                                'password-inner-input-group__show-password-icon-button-suffix': isPasswordHidden,
                                                'password-inner-input-group__hide-password-icon-button-suffix': !isPasswordHidden()
                                            },
                                            attr: { title: isPasswordHidden() ? 'Показать пароль' : 'Скрыть пароль' },
                                            tooltip: tooltip
                                        ">
                        </button>
                      </div>
                      <!-- /ko -->

                      <!-- ko if: $ctx.formControlErrorStateMatcher(password) -->
                      <div class="form-error"
                           data-bind="text: password.error()"></div>
                      <!-- /ko -->
                    </div>
                  </div>
                </div>

                <div class="f-check">
                  <input type="checkbox"
                         class="f-check-input"
                         data-bind="checked: active"
                         id="active">
                  <label class="f-check-label"
                         for="active">Активный доступ</label>
                </div>
              </div>
            </div>
          </div>

          <div class="col-6">
            <div class="form-group">
              <switch params="checked: formModel().correctPhone">Загружать клиентов только с корректными номерами
                телефонов <button type="button"
                        title="Номера телефонов клиентов будут проверены на количество символов"
                        data-bind="tooltip"
                        class="btn-question"></button></switch>
            </div>

            <div class="form-group">
              <switch params="checked: formModel().ignoreClients">Игнорировать клиентов <button type="button"
                        title="Можно указать вручную несколько условий для номера телефона и почты клиента, которые считать некорректными"
                        data-bind="tooltip"
                        class="btn-question"></button></switch>
            </div>

            <!-- ko template: {
            foreach: templateIf(formModel().ignoreClients(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
          } -->
            <!-- ko using: conditions -->
            <div class="service-conditions">
              <div class="d-flex service-conditions__header">
                <div class="flex-grow-1">
                  <div class="f-h2">Условия
                    <span class="font-weight-normal f-color-service"
                          data-bind="text: list().length"></span>
                  </div>
                  <div class="f-color-service f-fs-1 mt-2">Добавление условий, по которым нужно игнорировать клиентов
                  </div>
                </div>

                <div>
                  <button type="button"
                          class="f-btn f-btn-success"
                          data-bind="click: add">
                    <foquz-icon class="f-btn-prepend"
                                params="icon: 'plus'"></foquz-icon>
                    Добавить
                  </button>
                </div>
              </div>

              <!-- ko foreach: list -->
              <div>
                <service-condition params="condition: $data,
                        onRemove: function() {
                          $parent.remove($data);
                        },
                        canRemove: $parent.list().length > 1,
                        formControlErrorStateMatcher: $ctx.formControlErrorStateMatcher"></service-condition>
              </div>
              <!-- /ko -->
            </div>
            <!-- /ko -->
            <!-- /ko -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button type="submit"
              class="f-btn"
              data-bind="click: cancel">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Отменить
      </button>

      <button type="submit"
              class="f-btn f-btn-success"
              data-bind="click: submit">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        Сохранить
      </button>
    </div>
  </div>

</sidesheet>
<!-- /ko -->
