export class Condition {
  constructor() {
    this.type = ko.observable('1'); // 1 телефон, 2 email
    this.condition = ko.observable('1'); // 1 равно, 2 содержит, 3 маска
    this.value = ko.observable('').extend({
      required: {
        message: 'Обязательное поле'
      }
    });

    this.isValid = this.value.isValid;
  }

  setData(data) {
    this.type((data.type || 1) + '');
    this.condition((data.condition || 1) + '');
    this.value(data.value || '');
  }

  getData() {
    return {
      type: this.type(),
      condition: this.condition(),
      value: this.value(),
    }
  }
}
