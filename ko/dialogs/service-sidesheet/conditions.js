import { Condition } from './condition-model';

export class Conditions {
  constructor($element) {
    this.$element = $element;

    this.list = ko.observableArray([]);

    this.isValid = ko.computed(() => {
      console.log('conditions valid', ko.toJS(this.list))
      if (!this.list().length) return true;
      return !this.list().some((c) => !c.isValid());
    });
    this.add();
  }

  scroll() {
    let el = this.$element.find('.ps').get(0);
    if (el) {
      setTimeout(() => {
        el.scrollTop = el.scrollHeight;
      }, 400);
    }
  }

  add() {
    this.list.push(new Condition());

    this.scroll();
  }

  remove(condition) {
    this.list.remove(condition);
  }

  getData() {
    return this.list().map((c) => c.getData());
  }

  setData(conditions) {
    if (!conditions) {
      this.list.removeAll();
      this.add();
      return;
    }
    while (conditions.length < this.list().length) {
      this.list.pop();
    }

    if (conditions.length > this.list().length) {
      for (let i = this.list().length; i < conditions.length; i++) {
        this.add();
      }
    }

    conditions.forEach((c, i) => {
      this.list()[i].setData(c);
    });

    if (!this.list().length) this.add();
  }
}
