import { ViewModel } from './model';
import html from './template.html';
import './style.less';
import './condition';

ko.components.register('service-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('service-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});


