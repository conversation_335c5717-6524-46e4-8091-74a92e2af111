export class IikoBizServiceSubformViewModel {
  constructor(data) {
    this.formModel = ko.validatedObservable(null, {
      deep: true,
      live: true
    });

    this.defaults = data;

    this.formModel({
      mainApiUrl: ko.observable('').extend({
        required: {
          message: 'Обязательное поле'
        }
      }),
      additionalApiUrl: ko.observable(''),
      login: ko.observable('').extend({
        required: {
          message: 'Обязательное поле'
        },
        minLength: {
          params: 2,
          message: 'Должно быть введено хотя бы 2 символа'
        },
        pattern: {
          params: '^\\S*$',
          message: 'Неверный формат параметра «Логин»'
        }
      }),
      password: ko.observable('')
        .extend({
          required: {
            message: 'Обязательное поле'
          }
        }),
      active: ko.observable(false)
    });

    this.setData(data);
  }

  reset() {
    this.setData(this.defaults);
  }

  setData(data) {
    if (data && data.service == IIKO_TYPE) {
      this.formModel().mainApiUrl(data.mainApiUrl || '');
      this.formModel().additionalApiUrl(
        data.additionalApiUrl !== null
          ? data.additionalApiUrl
          : ''
      );
      this.formModel().login(data.login || '');
      this.formModel().password(data.password || '');
      this.formModel().active(!!data.active);
    } else {
      this.formModel().mainApiUrl('');
      this.formModel().additionalApiUrl('');
      this.formModel().login('');
      this.formModel().password('');
      this.formModel().active(false);
    }
  }
}
