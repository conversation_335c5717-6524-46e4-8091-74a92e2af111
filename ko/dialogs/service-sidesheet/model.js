import { DialogWrapper } from 'Dialogs/wrapper';

import { IikoBizServiceSubformViewModel } from './models/iiko';
import { Conditions } from './conditions';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.$element = $(element);

    this.initializing = ko.observable(true);

    this.mode = params.mode;
    this.service = ko.toJS(params.service);

    this.serviceTypes = params.services;

    this.isSubmitted = ko.observable(false);

    this.formModel = ko.validatedObservable({
      service: ko.observable(''),
      name: ko.observable('').extend({
        required: {
          message: 'Обязательное поле'
        }
      }),
      correctPhone: ko.observable(false),
      ignoreClients: ko.observable(false)
    });

    this.iikoBizServiceSubformViewModel = new IikoBizServiceSubformViewModel(
      this.mode === 'edit' ? this.service : null
    );

    this.conditions = new Conditions(this.$element);

    this.isValid = ko.computed(() => {
      if (

        this.formModel().service() === IIKO_TYPE &&
        !this.iikoBizServiceSubformViewModel.formModel.isValid()
      ) {
        console.log('1')
        return false;
      }


      if (this.formModel().ignoreClients() && !this.conditions.isValid()) {
        return false;
      }

      return this.formModel.isValid();
    });

    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );


    this.setDefaults();
  }

  setDefaults() {
    if (this.mode === 'create') {
      this.formModel().service('1');
      this.formModel().name('');
      this.formModel().correctPhone(false);
      this.formModel().ignoreClients(false);
      this.conditions.setData([]);
    } else {
      this.formModel().service('' + this.service.service);
      this.formModel().name(this.service.name);
      this.formModel().correctPhone(!!this.service.correctPhone);
      this.formModel().ignoreClients(!!this.service.ignoreClients);
      this.conditions.setData(this.service.conditions);
    }
  }

  cancel() {
    this.isSubmitted(false);
    this.setDefaults();
    this.iikoBizServiceSubformViewModel.reset();
    this.submit();
  }

  getParams() {
    let formModel = ko.toJS(this.formModel);
    const data = {
      service_type_id: +formModel.service,
      name: formModel.name,
      correct_phone_numbers: formModel.correctPhone ? 1 : 0,
      ignore_clients: formModel.ignoreClients ? 1 : 0
    };

    if (formModel.service === IIKO_TYPE) {
      let iikoModel = ko.toJS(this.iikoBizServiceSubformViewModel.formModel);
      data.url = iikoModel.mainApiUrl;
      if (iikoModel.additionalApiUrl) {
        data.url2 = iikoModel.additionalApiUrl;
      } else {
        data.url2 = null;
      }
      data.username = iikoModel.login;
      data.password = iikoModel.password;
      data.is_active = iikoModel.active ? 1 : 0;
    }

    return data;
  }

  get actionUrl() {
    return this.mode == 'create'
      ? '/foquz/api/company-access/create?access-token=' +
          APIConfig.apiKey +
          '&companyId=' +
          COMPANY_ID
      : '/foquz/api/company-access/update?access-token=' +
          APIConfig.apiKey +
          '&id=' +
          this.service.id;
  }

  submit() {
    this.isSubmitted(true);

    if (!this.isValid()) return;


      let params = this.getParams();
      let conditions = this.conditions.getData();

      $.ajax({
        type: 'POST',
        url: this.actionUrl,
        data: { CompanyIikoAccess: params, conditions },
        success: (id) => {
          this.isSubmitted(false);
          this.emitEvent('submit', {
            id,
            ...params,
            mainApiUrl: params.url,
            additionalApiUrl: params.url2,
            login: params.username,
            active: params.is_active,
            service: params.service_type_id,
            correct_phone_numbers: params.correct_phone_numbers,
            ignore_clients: params.ignore_clients,
            conditions
          });
          this.hide();
        }
      });

  }

  onInit() {
    this.$element.removeClass(
      'service-access-details-modal-dialog--initializing'
    );

    this.initializing(false);
  }
}
