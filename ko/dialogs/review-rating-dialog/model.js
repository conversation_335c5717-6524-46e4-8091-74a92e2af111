import { get as _get } from 'lodash';

import { DialogWrapper } from "Dialogs/wrapper";
import { MATRIX_QUESTION, STARS_QUESTION } from 'Data/question-types';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.answers = params.answers;
    this.title = params.title;
  }

  commentToString(comment) {
    console.log({ comment })
    if (!comment) return '';
    if (typeof comment === "string") return comment;
    if (typeof comment === "object" && "name" in comment) {
      return [comment.surname, comment.name, comment.patronymic]
        .filter(Boolean)
        .join(" ");
    }
    return comment.toString();
  }
}
