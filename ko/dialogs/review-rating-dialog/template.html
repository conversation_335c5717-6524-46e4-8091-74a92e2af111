<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header>
    <!-- ko text: $parent.title || _t('answers', 'Комментарии') -->
    <!-- /ko -->
  </foquz-dialog-header>



  <div class="foquz-dialog__body">
    <div class="review-rating__table-wrapper" data-bind="nativeScrollbar">
      <!-- ko foreach: { data: answers, as: 'answer' } -->
      <div class="review-comments__item">
        <!-- ko if: (answer.clarifyingComments && answer.clarifyingComments.length) || (answer.selfAnswer && answer.selfAnswer.length) -->
        <!-- ko foreach: answer.clarifyingComments -->
        <div class="review-comments__header">
          <!-- ko if: $parent.type == 15 -->
          <rating-point
            params="
              value: rating,
              point: ratingPoint,
              type: $parent.ratingView,
              icon: $parent.icon,
            "
          ></rating-point>
          <!-- /ko -->
          <!-- ko if: $parent.type == 13 -->
          <div class="review-comments-header__matrix-icon">
            <fc-icon
              params="
                'name': 'question-matrix',
              "
            ></fc-icon>
          </div>
          <!-- /ko -->
          <div class="review-comments__header-text" data-bind="text: headerText"></div>
        </div>
        <div class="comment" data-bind="text: commentText"></div>
        <!-- /ko -->
        <!-- /ko -->
        <!-- ko if: (comment !== null && comment !== '') || (answer.selfAnswer && answer.selfAnswer.length) || (answer.self_answer && answer.self_answer.length) -->
        <div class="review-comments__header">
          <!-- ko if: $parent.type == 15 -->
          <rating-point
            params="
              value: answer.rating,
              point: answer.normalizedRating || answer.ratingPoint,
              type: answer.ratingView,
              icon: answer.icon
            "
          ></rating-point>
          <!-- /ko -->
          <!-- ko if: $parent.type == 13 -->
          <div class="review-comments-header__matrix-icon">
            <fc-icon
              params="
                'name': 'question-matrix',
              "
            ></fc-icon>
          </div>
          <!-- /ko -->
          <div class="review-comments__header-text" data-bind="text: answer.questionName || answer.question.name"></div>
        </div>
        <!-- ko if: $parent.commentToString(answer.comment) !== $parent.commentToString(answer.selfAnswer || answer.self_answer) -->
        <div class="comment" data-bind="text: $parent.commentToString(answer.selfAnswer || answer.self_answer)"></div>
        <!-- /ko -->
        <div class="comment" data-bind="text: $parent.commentToString(answer.comment)"></div>
        <!-- /ko -->
      </div>
      <!-- /ko -->
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link px-2"
              data-bind="
                click: function() {
                  $dialog.hide('close');
                }, text: _t('Закрыть')"></button>
    </div>
  </div>

</foquz-dialog>
