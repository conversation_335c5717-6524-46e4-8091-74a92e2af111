<!-- ko if: dialogs().length -->

<div class="dialogs-container__wrapper">

  <!-- ko foreach: dialogs -->
  <div class="dialogs-container__item"
  data-bind="component: {
    name: $data.name,
    params: $data.params
  }, attr: {
    'data-single': $parent.dialogs().length == 1,
    'data-first': $index() == 0,
    'data-last': $index() == $parent.dialogs().length - 1,
  }, descendantsComplete: function(el) { init(el) }">
  </div>
  <!-- /ko -->

  <!-- ko if: dialogs().length > 1 -->
  <button class="dialogs-container__remove-all"
          data-bind="click: removeAll"
          type="button">Все
        <svg-icon params="name: 'times'" class="svg-icon--sm"></svg-icon>
        </button>
  <!-- /ko -->
</div>
<!-- /ko -->
