<foquz-dialog params="ref: modal, dialogWrapper: $component">
    <!-- ko ifnot: hasSizeAnswers -->

    <!-- ko if: isLoaded -->
    <foquz-dialog-header>Выгрузка файлов в архив завершена</foquz-dialog-header>
    <!-- /ko -->

    <!-- ko ifnot: isLoaded -->
    <foquz-dialog-header>Выгрузка файлов в архив</foquz-dialog-header>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: preparing -->

    <div class="py-3">
        <spinner></spinner>
    </div>

    <!-- /ko -->
    <!-- ko ifnot: preparing -->

    <div class="foquz-dialog__body">


        <!-- ko if: hasSizeAnswers -->
        <span data-bind="html: texts.adviceText"></span>
        <!-- /ko -->

        <!-- ko ifnot: hasSizeAnswers -->

        <!-- ko ifnot: hasFiles -->

        <!-- ko if: overSize -->
        <div class="download-archive-dialog__oversize-text">
            <svg style="margin-right: 10px" xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                 viewBox="0 0 14 14" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M14 7C14 10.866 10.866 14 7 14C3.13401 14 0 10.866 0 7C0 3.13401 3.13401 0 7 0C10.866 0 14 3.13401 14 7ZM7 3C6.44772 3 6 3.44772 6 4V7C6 7.55228 6.44772 8 7 8C7.55228 8 8 7.55228 8 7V4C8 3.44772 7.55228 3 7 3ZM7 9C6.44772 9 6 9.44772 6 10C6 10.5523 6.44772 11 7 11C7.55228 11 8 10.5523 8 10C8 9.44772 7.55228 9 7 9Z"
                      fill="#F96261"/>
            </svg>
            <span style="color: #EA1D27;" data-bind="text: texts.overSizeText"></span>
        </div>
        <!-- /ko -->

        <!-- ko ifnot: overSize -->
        <span data-bind="text: texts.noFilesText"></span>
        <!-- /ko -->

        <!-- /ko -->

        <!-- ko if: hasFiles -->
        <span data-bind="html: texts.regularText(filesCount, filesSize, unit, isLoaded)"></span>
        <!-- /ko -->


        <!-- /ko -->

    </div>
    <div class="foquz-dialog__footer">
        <div class="foquz-dialog__actions">
            <!-- ko ifnot: hasFiles -->
            <button
                    type="button"
                    class="f-btn f-btn-text f-btn-text_3F65F1"
                    data-bind="click: function() {
                            $dialog.hide();
                          }"
            >
                <span data-bind="text: _t('Закрыть')"></span>
            </button>
            <!-- /ko -->
            <!-- ko if: isLoaded -->
            <button
                    type="button"
                    class="f-btn f-btn-text f-btn-text_3F65F1"
                    data-bind="click: function() {
                            $dialog.hide();
                          }"
            >
                <span data-bind="text: _t('Закрыть')"></span>
            </button>
            <!-- /ko -->

            <!-- ko ifnot: isLoaded -->
            <!-- ko if: hasFiles -->
            <button
                    type="button"
                    class="f-btn f-btn-text"
                    data-bind="click: function() {
                $dialog.hide();
              }"
            >
                <span data-bind="text: _t('Отменить')"></span>
            </button>
            <button
                    type="submit"
                    class="f-btn f-btn-success"
                    data-bind="click: function() { submit()  }, disable: preparing"
            >
                <foquz-icon params="icon: 'save'" class="f-btn-prepend"></foquz-icon>
                <span data-bind="text: _t('Скачать')"></span>
            </button>
            <!-- /ko -->
            <!-- /ko -->

        </div>
    </div>
    <!-- /ko -->

</foquz-dialog>
