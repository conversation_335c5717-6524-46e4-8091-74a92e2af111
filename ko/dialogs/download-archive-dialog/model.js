import { DialogWrapper } from 'Dialogs/wrapper';
import { requestFile } from "Utils/api/request";

const texts = {
  noFilesText: 'В данном опросе пока нет ответов с прикрепленными файлами.',
  regularText: (count, size, unit, loaded) => {
    return `${loaded() ? 'Выгружено' : 'Найдено'} <span style="font-weight: 700">${count}</span>, размер архива <span style="font-weight: 700">${size}${unit}</span>`
  },
  overSizeText: 'Общий размер файлов более 500 Мб. Выгрузка невозможна. Попробуте ограничить выборку ответов.',
  adviceText: 'Если в опросе есть вопрос с типом <span style="font-weight: 700">«загрузка файла»</span>, то вы можете выгрузить прикрепленные респондентами файлы в архив. В данном опросе нет вопроса такого типа.',
}

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    this.hasFiles = false;

    this.isLoading = ko.observable(false)
    this.isLoaded = ko.observable(false)
    console.log('download-archive-dialog', params, params.data());
    this.overSize = false;
    this.hasSizeAnswers = params.hasSizeAnswers;
    this.filesCount = 0;
    this.filesSize = 0;
    this.filesMaxSize = 500;
    this.texts = texts;
    this.unit = 'Мб';
    this.answersId = params.answersId;
    this.url = params.url;

    this.preparing = params.preparing;
    this.data = params.data;

    // Subscribe to changes
    this.preparing.subscribe(() => {
      console.log("Preparing changed to", this.preparing());
    });
    this.data.subscribe((newData) => {
      // This will run whenever `data` changes
      const { data, error } = newData
      if (error) {
        this.overSize = true;
      } else {
        if (data.files) {
          const { size, count, unit } = data.files
          this.filesCount = count;
          this.filesSize = size;
          this.unit = unit;
          this.filesCount = count + ' ' + utils.declOfNum(count, ['файл', 'файла', 'файлов']);
          this.hasFiles = count > 0;
        }
      }

    });

    console.log('this.filesCount', this.filesCount)
    if (this.filesSize === 0) {
      this.hasFiles = false;
    }
  }

  async submit() {
    this.preparing(true);
    // let dwnurl = `/foquz/api/answers/download?action=zip`;

    const response = await requestFile(this.url, {
      method: "GET"
    });
    // await new Promise(resolve => setTimeout(resolve, 3000));

    this.preparing(false);
    console.log(response)
    if (response.error) {
      this.overSize = true
      console.error('Ошибка при запросе на скачивание архива', response.error);
      return
    }
    if (response.data) {
      this.isLoaded(true)
    }
    // Создание ссылки на файл
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    const fileName = 'respondentsArchive.zip'; // Имя файла
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
  }

}
