
<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header params="empty: !texts.title()">
    <!-- ko text: $parent.texts.title -->
    <!-- /ko -->
  </foquz-dialog-header>

  <!-- ko if: texts.text -->
  <div class="foquz-dialog__body" data-bind="html: texts.text">

  </div>
  <!-- /ko -->


  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">

      <button type="button"
              class="f-btn f-btn-link"
              data-bind="
                click: function() {
                  $dialog.hide('close');
                },
                css: 'f-btn-' + mode(),
                text: texts.close"></button>
    </div>
  </div>

</foquz-dialog>
