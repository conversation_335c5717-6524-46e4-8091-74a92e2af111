import { ApiUrl } from "@/utils/url/api-url";
import axios from "axios";

export const getUniqueClientsTotalCount = (params) =>
  new Promise((resolve, reject) => {
    $.ajax({
      url: `/foquz/foquz-contact/get-contacts-count`,
      data: {
        ...params,
      },
      method: "GET",
      success: (response) => {
        resolve(response.data.total);
      },
      error: (response) => {
        reject(response.responseJSON);
      },
    });
  });

export const createTask = async (params) => {
  const options = {
    file_type: "csv",
    entity_type: "poll-links",
    id: params.id,
    linkKey: params.linkKey,
    tags: params.tags,
    tags_except: params.tags_except,
  };
  if (params.filial_id) {
    options.filial_id = params.filial_id;
  }
  if (params.lang) {
    options.lang = params.lang;
  }
  const res = await axios
    .get(
      ApiUrl("export/request", options)
    );
  return res.data.id;
};
