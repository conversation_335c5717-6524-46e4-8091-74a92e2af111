<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="container">
    <div class="foquz-dialog__header mb-4">
      <h2 class="foquz-dialog__title">
        Сгенерировать уникальные ссылки для клиентов
      </h2>
    </div>

    <div class="foquz-dialog__body">
      <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
        <div
          class="container"
          data-bind="
            css: {
              'container--disabled': isSubmit(),
            },
          "
        >
          <div class="mb-4 generate-unique-client-links__col">
            <label class="form-label">Фильтр</label>
            <div class="select2-wrapper">
              <select
                data-bind="
                  value: selectedFilter,
                  select2: {
                    containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control generate-unique-client-links__col',
                    placeholder: 'Не выбрано',
                    minimumResultsForSearch: 10,
                  },
                  options: filterOptions,
                  optionsText: 'name',
                  optionsValue: 'id',
                "
                data-placeholder="Не выбрано"
              ></select>
            </div>
          </div>
          <div class="generate-unique-client-links__col">
            <fc-label params="text: 'Теги контакта'"></fc-label>
            <fc-select
              params="
                multiple: true,
                options: tagsList,
                value: selectedTags,
                placeholder: 'Не выбраны',
                blockSelectedGroup: true,
              "
            ></fc-select>
          </div>
          <div
            class="generate-unique-client-links__cloak"
            data-bind="
              visible: isSubmit(),
              click: async function(_, e) {
                e.preventDefault();
                e.stopPropagation();
              },
            "
          ></div>
        </div>
      </div>
    </div>
    <div
      class="foquz-dialog__footer fixed-footer fixed generate-unique-client-links__footer"
    >
      <div class="foquz-dialog__actions">
        <button
          type="button"
          class="f-btn"
          data-bind="click: function() {
                  $dialog.hide();
                }"
        >
          <foquz-icon params="icon: 'bin'" class="f-btn-prepend"></foquz-icon>
          Отменить
        </button>

        <!-- ko ifnot: isAdding -->
        <button
          type="submit"
          class="f-btn f-btn-success"
          data-bind="
            click: submit,
            text: buttonText(),
            disable: isSubmit(),
            css: {
              'generate-unique-client-links__submit--disabled': !isFieldsEmpty() && totalClients() == 0,
            }
          "
        ></button>
        <!-- /ko -->

        <!-- ko if: isAdding -->
        <div
          class="ml-2"
          data-bind="component: {
            name: 'progress-bar',
            params: { value: addingProgress(), theme: 'success' } }"
        ></div>
        <!-- /ko -->
      </div>
    </div>
  </div>
</sidesheet>
