import { DialogWrapper } from "Dialogs/wrapper";
import { DialogsModule } from "Utils/dialogs-module";
import { debounce } from "Utils/timer/debounce";
import { getUniqueClientsTotalCount, createTask } from "./api";
import { getTags } from "@/presentation/views/fc-conditions/_api/get-tags";

const STATUS_STARTING = 1;
const STATUS_PROCESSING = 2;
const STATUS_FAILED = 3;
const STATUS_DONE = 4;

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    DialogsModule(this);

    this.id = params.id;
    this.linkKey = params.linkKey;
    this.filial_id = params.filial_id;
    this.lang = params.lang;

    this.isSubmit = ko.observable(false);
    this.loading = ko.observable(false);
    this.statId = ko.observable(0);

    this.isAdding = ko.observable(false);
    this.addingProgress = ko.observable(0);
    this.statsRequests = [];

    this.filterOptions = [
      {
        id: 1,
        name: "Включить контакты с тегами",
      },
      {
        id: 2,
        name: "Исключить контакты с тегами",
      },
    ];

    this.selectedFilter = ko.observable(this.filterOptions[0].id);
    this.selectedTags = ko.observableArray([]);
    this.totalClients = ko.observable(0);
    this.tagsList = getTags().data;

    this.isFieldsEmpty = ko.pureComputed(() => {
      if (this.selectedTags().length && this.selectedFilter() !== 1) {
        return this.load();
      }

      if (this.selectedFilter() === 1 && this.selectedTags().length) {
        return this.load();
      }

      return true;
    });

    this.buttonText = ko.pureComputed(() => {
      if (this.loading()) {
        return "Сгенерировать для ... контактов";
      }

      if (!this.isFieldsEmpty() && this.totalClients()) {
        return `Сгенерировать для (${this.totalClients()}) контактов`;
      } else if (!this.isFieldsEmpty() && !this.totalClients()) {
        return "0 контактов";
      }

      return "Сгенерировать для всех контактов";
    });

    this.load = debounce(() => this.fetchClientsTotalCount(), 500);
  }

  async submit() {
    if (!this.isFieldsEmpty() && this.totalClients() == 0) {
      this.info({
        text: "Не найдено контактов с выбранными фильтрами. Измените параметры фильтров.",
      });
      return;
    }
    this.isSubmit(true);

    this.isAdding(true);
    this.addingProgress(0);

    createTask({
      id: this.id,
      linkKey: this.linkKey,
      tags: this.selectedFilter() === 1 ? this.selectedTags() : undefined,
      tags_except: this.selectedFilter() !== 1 ? this.selectedTags() : undefined,
      filial_id: this.filial_id,
      lang: this.lang,
    }).then((statId) => {
      this.statId(statId);
      this.getStat();
    });
  }

  async fetchClientsTotalCount() {
    this.loading(true);
    const params = {
      advanced: {},
    };

    if (this.selectedFilter() === 1) {
      params.advanced["tags"] = this.selectedTags();
    } else {
      params.advanced["tags_except"] = this.selectedTags();
    }

    const fetchedTotalCount = await getUniqueClientsTotalCount(params);
    this.totalClients(fetchedTotalCount);
    this.loading(false);
    return false;
  }

  getStatRequest() {
    return new Promise((res, rej) => {
      const xhr = $.ajax({
        method: "GET",
        url: `${APIConfig.baseApiUrlPath}export/status?access-token=${APIConfig.apiKey}`,
        data: {
          id: this.statId(),
        },
        success: (data) => {
          if (!this.isAdding()) {
            rej();
            return;
          }

          if (!data.success) {
            rej(data);
            return;
          }

          const stat = data.data;
          const status = stat.status;

          if (stat.error) {
            rej(data.error);
            return;
          }

          if (status == STATUS_FAILED) {
            rej(data.error);
            return;
          }

          if (status == STATUS_DONE) {
            res({
              progress: 100,
            });
            return;
          }

          if (!("total" in stat)) {
            rej();
            return;
          }

          if (stat.total == 0) {
            res({
              progress: 0,
            });
            return;
          }

          let progress = 0;
          if (status != STATUS_STARTING) {
            progress = Math.min(
              (stat.processed * 100) / stat.total,
              100
            );
          }

          res({
            progress: progress,
          });
        },
        error: (response) => {
          rej();
        },
      });
      this.statsRequests.push(xhr);
    });
  }

  abortAllRequests() {
    this.statsRequests.forEach((xhr) => xhr.abort());
    this.statsRequests = [];
  }

  getStat() {
    let intervalId = setInterval(() => {
      this.getStatRequest()
        .then((res) => {
          if (res.progress >= 100) {
            this.addingProgress(100);
            this.isSubmit(false);
            this.isAdding(false);
            clearInterval(intervalId);
            window.location.href = `/foquz/api/export/get-file?id=${this.statId()}&access-token=${APIConfig.apiKey}`;
          } else if (this.addingProgress() < res.progress) {
            this.addingProgress(res.progress);
          }
        })
        .catch((e) => {
          if (this.isAdding() == false && this.isSubmit() == false) {
            return;
          }
          this.isSubmit(false);
          this.isAdding(false);
          clearInterval(intervalId);
          this.abortAllRequests();
        });
    }, 800);
  }
}
