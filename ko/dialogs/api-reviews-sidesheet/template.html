<template id="api-reviews-sending-list-template">
  <sending-list params="list: list"></sending-list>
</template>

<sidesheet params="ref: modal, dialogWrapper: $component">

  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">
        Анкеты, отправленные по API
      </h2>
    </div>
  </div>


  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container"
           data-bind="using: table">
        <div class="f-border-top f-border-bottom full-width">
          <div class="px-md-10p">
            <div class="stats"
                 data-bind="using: stats">
              <foquz-stats-item class="adaptive">
                <div class="value f-color-primary"
                     data-bind="text: $parent.new"></div>
                <div class="label">Отправлено</div>
              </foquz-stats-item>
              <foquz-stats-item class="adaptive">
                <div class="value f-color-violet"
                     data-bind="text: $parent.open"></div>
                <div class="label">Открыто</div>
              </foquz-stats-item>
              <foquz-stats-item class="adaptive">
                <div class="value f-color-blue"
                     data-bind="text: $parent.inProgress"></div>
                <div class="label">В процессе</div>
              </foquz-stats-item>
              <foquz-stats-item class="adaptive">
                <div class="value f-color-mint"
                     data-bind="text: $parent.done"></div>
                <div class="label">Заполнено</div>
              </foquz-stats-item>
            </div>
          </div>
        </div>
        <div class="f-border-bottom filters-wrapper">
          <div class="foquz-modal-page__container">
            <div class="filters api-reviews-filters">
              <!-- ko using: filters -->
              <div class="api-reviews-filters__item">
                <div class="form-group dense-form-group">
                  <label class="form-label">Статус</label>
                  <select multiple
                          data-bind="
                        selectedOptions: statuses,
                        select2: {
                            wrapperCssClass: 'select2-container--form-control',
                            dropdownCssClass: 'dense-form-group__dropdown',
                            containerCss: { 'min-width': '30px' }
                        }
                    "
                          data-placeholder="Все">
                    <option></option>
                    <option value="new">Отправлена</option>
                    <option value="open">Открыта</option>
                    <option value="inProgress">В процессе</option>
                    <option value="done">Заполнена</option>
                  </select>
                </div>
              </div>
              <div class="api-reviews-filters__item">
                <div class="form-group dense-form-group">
                  <label class="form-label">Каналы связи</label>
                  <select multiple
                          data-bind="
                        selectedOptions: channels,
                        select2: {
                            wrapperCssClass: 'select2-container--form-control',
                            dropdownCssClass: 'dense-form-group__dropdown',
                            containerCss: { 'min-width': '30px' }
                        }
                    "
                          data-placeholder="Все">
                    <option></option>
                    <option value="Email">Email</option>
                    <option value="SMS">SMS</option>
                    <option value="Telegram">Telegram</option>
                    <option value="Viber">Viber</option>
                    <option value="Push">Push</option>
                  </select>
                </div>
              </div>
              <div class="api-reviews-filters__item">
                <div class="form-group dense-form-group">
                  <label class="form-label">ФИО, телефон, email</label>
                  <input type="text"
                         class="form-control"
                         data-bind="autosizeInput, textInput: query"
                         placeholder="Любые">
                </div>
              </div>
              <div class="py-10p pr-25p d-flex align-items-center">
                <div class="f-check">
                  <input type="checkbox"
                         class="f-check-input"
                         id="api-reviews-with-answers"
                         data-bind="checked: withAnswer">
                  <label for="api-reviews-with-answers"
                         class="f-check-label">Только с ответами</label>
                </div>
              </div>
              <!-- /ko -->
              <div class="filters-actions">
                <button class="f-btn f-btn-link mr-3 d-none d-md-flex"
                        type="button"
                        data-bind="click: resetFilters">Сбросить</button>
                <button class="f-btn f-btn-lg d-md-none"
                        type="button"
                        data-bind="click: resetFilters">
                  <span class="f-btn-prepend">
                    <svg-icon params="name: 'bin'"></svg-icon>
                  </span>
                  Сбросить
                </button>
                <button class="f-btn f-btn-success f-btn-lg"
                        type="button"
                        data-bind="click: applyFilters">
                  <span class="f-btn-prepend">
                    <svg-icon params="name: 'check'"></svg-icon>
                  </span>
                  Применить
                </button>
              </div>
            </div>
            <div class="filters-toggler">
              <button class="f-btn f-btn-link"
                      type="button"
                      data-close
                      data-bind="click: function() {
                  $('.api-reviews-filters').slideUp().attr('data-closed', true);
                }">Свернуть фильтры</button>
              <button class="f-btn f-btn-link"
                      type="button"
                      data-open
                      data-bind="click: function() {
                  $('.api-reviews-filters').slideDown().removeAttr('data-closed');
                }">Развернуть фильтры</button>
            </div>
          </div>
        </div>
        <div class="foquz-modal-page__container mt-md-4 pt-md-1"
             data-bind="let: { $table: $data }">

          <media-query params="query: 'tablet+'">
            <interactive-table params="table: $table">
              <table class="table f-table f-table--searchable">
                <thead>
                  <tr>
                    <th data-bind="component: {
                        name: 'table-head-cell',
                        params: {
                          sort: sort,
                          sortName: 'created_at',
                          withSearch: true,
                          searchValue: search.created_at,
                          onSearch: function() { applyFilters() }
                        }
                      }">Отправлена</th>
                    <th data-bind="component: {
                        name: 'table-head-cell',
                        params: {
                          sort: sort,
                          sortName: 'filial',
                          withSearch: true,
                          searchValue: search.filial,
                          onSearch: function() { applyFilters() }
                        }
                      }">Филиал</th>
                    <th data-bind="component: {
                        name: 'table-head-cell',
                        params: {
                          sort: sort,
                          sortName: 'statusName',
                          withSearch: true,
                          searchValue: search.status,
                          onSearch: function() { applyFilters() }
                        }
                      }">Статус</th>
                    <th data-bind="component: {
                        name: 'table-head-cell',
                        params: {
                          sort: sort,
                          sortName: 'done_at',
                          withSearch: true,
                          searchValue: search.done_at,
                          onSearch: function() { applyFilters() }
                        }
                      }">Пройдена</th>
                    <th data-bind="component: {
                        name: 'table-head-cell',
                        params: {
                          sort: sort,
                          sortName: 'channel_name',
                          withSearch: true,
                          searchValue: search.channel,
                          onSearch: function() { applyFilters() }
                        }
                      }">Канал&nbsp;связи</th>
                    <th data-bind="component: {
                        name: 'table-head-cell',
                        params: {
                          sort: sort,
                          sortName: 'contactName',
                          withSearch: true,
                          searchValue: search.contact,
                          onSearch: function() { applyFilters() }
                        }
                      }">ФИО</th>
                    <th style="min-width: 80px"
                        data-bind="component: {
                        name: 'table-head-cell',
                        params: {
                          sort: sort,
                          sortName: 'phone',
                          withSearch: true,
                          searchValue: search.phone,
                          onSearch: function() { applyFilters() }
                        }
                      }">Телефон</th>
                    <th data-bind="component: {
                        name: 'table-head-cell',
                        params: {
                          sort: sort,
                          sortName: 'email',
                          withSearch: true,
                          searchValue: search.email,
                          onSearch: function() { applyFilters() }
                        }
                      }">Email</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- ko foreach: { data: items, as: 'review'} -->
                  <tr>
                    <td data-bind="text: review.createdAt"></td>
                    <td data-bind="text: review.filial"></td>
                    <td>
                      <span class="f-fs-1 font-weight-500"
                            data-bind="text: review.statusName, css: review.statusClass"></span>
                    </td>
                    <td data-bind="text: review.doneAt || '—'"></td>
                    <td data-bind="let: { list: review.getChannelsStats() }">
                      <!-- ko if: channelName -->
                      <div class="d-flex align-items-center cursor-pointer"
                           data-bind="dropdown: 'api-reviews-sending-list-template'">
                        <!-- ko if: channelType -->
                        <foquz-icon params="prefix: 'channel', icon: review.channelType"
                                    class="mr-2"></foquz-icon>
                        <!-- /ko -->
                        <span data-bind="text: review.channelName"></span>
                      </div>
                      <!-- /ko -->

                      <!-- ko ifnot: channelName -->
                      —
                      <!-- /ko -->
                    </td>
                    <td data-bind="text: review.contact || '—'"></td>
                    <td data-bind="text: review.phone || '—'"></td>
                    <td data-bind="html: $parent.formatEmail(review.email)"></td>
                  </tr>
                  <!-- /ko -->
                </tbody>
              </table>
            </interactive-table>
          </media-query>

          <media-query params="query: 'mobile'">
            <div class=" mobile-table"
                 data-bind="nativeScrollbar">
              <interactive-table params="table: $table, horizontal: true">

                <table class="fixed-table f-color-text"
                       style="min-width: 100%">
                  <tbody>
                    <tr>
                      <th class="f-border-top">Отправлена</th>
                      <!-- ko foreach: { data: items, as: 'review'} -->
                      <td class="f-border-top" data-bind="text: review.createdAt"></td>
                      <!-- /ko -->
                    </tr>
                    <tr>
                      <th>Филиал</th>
                      <!-- ko foreach: { data: items, as: 'review'} -->
                      <td data-bind="text: review.filial"></td>
                      <!-- /ko -->
                    </tr>
                    <tr>
                      <th>Статус</th>
                      <!-- ko foreach: { data: items, as: 'review'} -->
                      <td>
                        <span class="f-fs-1 font-weight-500"
                              data-bind="text: review.statusName, css: review.statusClass"></span>
                      </td>
                      <!-- /ko -->
                    </tr>
                    <tr>
                      <th>Пройдена</th>
                      <!-- ko foreach: { data: items, as: 'review'} -->
                      <td data-bind="text: review.doneAt || '—'"></td>
                      <!-- /ko -->
                    </tr>
                    <tr>
                      <th>Канал связи</th>
                      <!-- ko foreach: { data: items, as: 'review'} -->
                      <td data-bind="let: { list: review.getChannelsStats() }">
                        <!-- ko if: channelName -->
                        <div class="d-flex align-items-center cursor-pointer"
                             data-bind="dropdown: 'api-reviews-sending-list-template'">
                          <!-- ko if: channelType -->
                          <foquz-icon params="prefix: 'channel', icon: review.channelType"
                                      class="mr-2"></foquz-icon>
                          <!-- /ko -->
                          <span data-bind="text: review.channelName"></span>
                        </div>
                        <!-- /ko -->

                        <!-- ko ifnot: channelName -->
                        —
                        <!-- /ko -->
                      </td>
                      <!-- /ko -->
                    </tr>
                    <tr>
                      <th>ФИО контакта</th>
                      <!-- ko foreach: { data: items, as: 'review'} -->
                      <td data-bind="text: review.contact || '—'">

                      </td>
                      <!-- /ko -->
                    </tr>
                    <tr>
                      <th>Телефон</th>
                      <!-- ko foreach: { data: items, as: 'review'} -->
                      <td data-bind="text: review.phone || '—'">

                      </td>
                      <!-- /ko -->
                    </tr>
                    <tr>
                      <th>Email</th>
                      <!-- ko foreach: { data: items, as: 'review'} -->
                      <td data-bind="text: review.email || '—'">

                      </td>
                      <!-- /ko -->
                    </tr>
                  </tbody>
                </table>

              </interactive-table>
            </div>
          </media-query>



        </div>
      </div>
    </div>
  </div>




</sidesheet>
