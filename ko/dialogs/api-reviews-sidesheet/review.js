const statusClasses = {
  'new': 'f-color-primary',
  'open': 'f-color-violet',
  'inProgress': 'f-color-blue',
  'in-progress': 'f-color-blue',
  'done': 'f-color-mint'
};

export class ReviewModel {
  constructor(data) {
    this.createdAt = this.formatDate(data.created_at);
    this.filial = data.filialName;
    this.statusName = data.statusName;
    this.statusClass = statusClasses[data.status];
    this.doneAt = this.formatDate(data.done_at);

    this.channelName = data.channel_name || '';
    this.channelType = '';
    let type = this.channelName.toLowerCase();
    if (['email', 'sms', 'viber', 'telegram', 'push'].includes(type)) {
      this.channelType = type;
    }

    this.contact = data.contactName;
    this.phone = data.phone;
    this.email = data.email;

    this.sends = data.sends;
  }

  formatDate(date) {
    let d = moment(date, 'YYYY-MM-DD HH:mm');
    return d.isValid() ? d.format('DD.MM.YYYY HH:mm') : '';
  }

  getChannelType(channelName) {
    switch (channelName) {
      case 'Email':
        return 'email';
      case 'SMS':
        return 'sms';
      case 'Telegram':
        return 'telegram';
      case 'Viber':
        return 'viber';
      case 'Push':
        return 'push';
      case 'Ссылка':
        return 'link';
      default:
        return null;
    }
  }

  getChannelsStats() {
    let stats = [];
    let channels = {};

    this.sends.forEach((c) => {
      let channelName = c.channel_name;

      if (!channels[channelName]) {
        let channel = {
          name: channelName,
          sended: null,
          repeats: 0,
          hasResponse: false,
          type: this.getChannelType(channelName)
        };
        channels[channelName] = channel;
        stats.push(channel);
      }

      let channel = channels[channelName];
      channel.sended = moment(c.sended, 'YYYY-MM-DD').format('DD.MM.YYYY');
      channel.repeats++;
      if (c.status == 2) channel.hasResponse = true;
    });

    return stats;
  }
}
