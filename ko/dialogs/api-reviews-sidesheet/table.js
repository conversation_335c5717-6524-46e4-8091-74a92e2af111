import { InteractiveTable } from 'Models/interactive-table';
import { SortModel } from 'Models/sort';
import { ReviewModel } from './review';
export class Table extends InteractiveTable {
  constructor(params) {
    super();

    this.pollId = params.pollId;

    this.isLoading = ko.observable(false);

    this.stats = {
      new: ko.observable(0),
      open: ko.observable(0),
      inProgress: ko.observable(0),
      done: ko.observable(0)
    };

    this.filters = {
      statuses: ko.observableArray([]),
      channels: ko.observableArray([]),
      query: ko.observable(''),
      withAnswer: ko.observable(false)
    };

    this.sort = new SortModel('created_at', true);
    this.sort.on('sort', () => {
      this.applyFilters();
    });

    let searchFields = [
      'created_at',
      'status',
      'done_at',
      'channel',
      'contact',
      'phone',
      'email',
      'filial'
    ];
    this.search = {};
    searchFields.forEach((fieldName) => {
      this.search[fieldName] = ko.observable('');
    });

    this.page = ko.observable(1);
    this.isLastRequestEmpty = ko.observable(false);
  }

  formatEmail(email) {
    if (!email) return '—';
    let atIndex = email.indexOf('@');
    let name = email.slice(0, atIndex + 1);
    let domain = email.slice(atIndex + 1);
    return `<span>${name}</span><span>${domain}</span>`;
  }

  updateStats(stats) {
    this.stats.new(stats.new);
    this.stats.open(stats.open);
    this.stats.inProgress(stats.inProgress);
    this.stats.done(stats.done);
  }

  getParams() {
    let filters = ko.toJS(this.filters);
    let search = ko.toJS(this.search);
    let order = this.sort.sort();

    return {
      page: this.page(),
      pollId: this.pollId,
      filters: {
        channels: filters.channels,
        statuses: filters.statuses
      },
      query: filters.query,
      withAnswer: filters.withAnswer ? 1 : 0,
      search,
      order
    };
  }

  load() {
    if (!this.beforeLoad()) {
      return;
    }

    let params = this.getParams();

    $.ajax({
      method: 'GET',
      url: `${APIConfig.baseApiUrlPath}answers/by-api?access-token=${APIConfig.apiKey}`,
      data: params,
      success: (response) => {
        this.updateStats(response.stats);
        let items = response.items.map(i => new ReviewModel(i));
        this.afterLoad(items);
      },
      error: () => {
        this.onError();
      }
    });
  }

  applyFilters() {
    this.reset();
    this.load();
  }

  resetFilters() {
    this.filters.statuses([]);
    this.filters.channels([]);
    this.filters.query('');
    this.filters.withAnswer(false);
    this.sort.update('created_at', true);
    Object.values(this.search).forEach((v) => v(''));

    this.applyFilters();
  }

}
