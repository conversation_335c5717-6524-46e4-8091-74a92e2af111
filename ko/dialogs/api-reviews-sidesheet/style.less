@import 'Style/breakpoints';
@import 'Style/colors';

.api-reviews-sidesheet {
  .stats {
    display: flex;
    .only-mobile({
      flex-direction: column;
    });
  }

  .filters {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding-top: 10px;
    padding-bottom: 10px;
    margin-left: -30px;
    margin-right: -30px;
    padding-left: 30px;
    padding-right: 30px;

    &-actions {
      margin-left: auto;
      padding-top: 5px;
      padding-bottom: 5px;
      display: flex;
      align-items: center;
    }

    .api-reviews-filters__item {
      padding-top: 10px;
      padding-bottom: 10px;
      padding-right: 25px;
    }

    .only-mobile({
      flex-direction: column;
      align-items: stretch;
      flex-wrap: nowrap;
      margin-left: 0px;
      margin-right: 0px;
      padding-left: 0px;
      padding-right: 0px;

      .api-reviews-filters__item {
        padding-top: 7px;
        padding-bottom: 7px;
      }

      &-actions {
        margin-left: -10px;
        margin-right: -10px;
        padding-bottom: 0;

        .f-btn {
          flex-grow: 1;
          margin-left: 10px;
          margin-right: 10px;
        }
      }
    });
  }

  .filters-toggler {
    display: none;

    .f-btn {
      height: 48px;
      margin-left: 0 !important;
    }

    .only-mobile({
      display: block;
      width: 100%;
    });
  }

  .filters + .filters-toggler {
    [data-open] {
      display: none;
    }
    [data-close] {
      display: block;
    }
  }

  .filters[data-closed] + .filters-toggler {
    [data-open] {
      display: block;
    }
    [data-close] {
      display: none;
    }
  }

  .foquz-dialog__scroll {
    padding-top: 26px;
  }

  .mobile-table {
    margin-left: -15px;
    margin-right: -15px;
  }

  .fixed-table {
    th {
      min-width: 110px;
    }
    td {
      min-width: calc(100vw - 110px);
    }
  }

  .only-mobile({
    .foquz-modal-page__container {
      padding-bottom: 30px;

    }
    .filters-wrapper .foquz-modal-page__container {
      padding-bottom: 0;
    }
    .foquz-dialog__scroll {
      padding-top: 16px;
    }
    .api-reviews-filters {
      padding-bottom: 0;
    }
    .filters-toggler {
      .f-btn {
        width: 100%;
      }
    }
  });
}
