import { DialogWrapper } from "Dialogs/wrapper";
import { useFoldersList } from "@/presentation/hooks/useFoldersList";
import { getPollFolders } from "@/api/poll/get-poll-folders";
import { request } from "@/utils/api/request";
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    const { list, loading } = useFoldersList(getPollFolders);

    this.loading = loading;
    this.locations = list;
    this.location = ko.observable("0");

    list.subscribe((v) => {
      this.location(v.find((i) => !i.inactive).id);

      if (params.folder) {
        this.location("" + params.folder.folderId);
      }
    });

    this.id = null;

    this.name = ko.observable("").extend({
      required: {
        message: _t("Обязательное поле"),
      },
      minLength: {
        params: 2,
        message: _t("main", "Не менее {characters}", {
          characters: _t("main", "{count} символов", {
            count: 2,
          }),
        }),
      },
    });

    this.description = ko.observable("");

    if (params.folder) {
      this.id = params.folder.id;
      this.name(params.folder.name);
      this.description(params.folder.description);
    }
  }

  onEnter() {
    this.submit();
  }

  locationTemplateResult(state) {
    if (!state.id) {
      return state.text;
    }

    const level = parseInt($(state.element).data("level"));

    return $(
      `<i class="survey-list__new-folder-modal-dialog-location-form-control-folder-icon" style="flex-shrink: 0; margin-left: ${
        level * 14
      }px"></i>` +
        "<span>" +
        state.text +
        "</span>"
    );
  }

  async submit() {
    this.isSubmitted(true);
    if (!this.name.isValid()) return;
    let params = {
      name: this.name(),
      location: this.location(),
      description: this.description(),
    };

    if (this.id) {
      const { data, error } = await request("/foquz/api/v1/folder/update", {
        method: "POST",
        params: {
          id: this.id,
        },
        body: params,
      });

      if (error) {
        console.error(error);
      } else {
        this.emitEvent("update");
        this.hide();
      }
      return;
    }

    $.ajax({
      url: "/foquz/ajax/create-folder",
      method: "POST",
      dataType: "json",
      data: JSON.stringify(params),
      success: (response) => {
        if (response.success) {
          this.emitEvent("create");
          this.hide();
        }
      },
      error: (response) => {
        console.error(response.responseJSON);
      },
    });
  }
}
