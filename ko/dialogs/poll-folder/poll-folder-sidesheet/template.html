<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header pb-20p">
    <div class="container">
      <h2 class="foquz-dialog__title">
        <span data-bind="text: id ? _t('polls', 'Редактировать папку') : _t('polls', 'Новая папка')"></span>
      </h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="container">
      <!-- ko if: loading -->
      <fc-spinner class="f-color-primary"></fc-spinner>
      <!-- /ko -->

      <!-- ko ifnot: loading -->

      <div class="row">
        <div class="col col-6">
          <div class="form-group">
            <label class="form-label" data-bind="text: _t('Название')"></label>

            <foquz-chars-counter params="value: name, max: 150">
              <input
                class="form-control"
                data-bind="textInput: $parent.name,
                      attr: {
                        placeholder: _t('polls', 'Новая папка'),
                      },
                      css: {
                        'is-invalid': $parent.formControlErrorStateMatcher($parent.name), 'is-valid': $parent.formControlSuccessStateMatcher($parent.name) }"
                maxlength="150"
              />
            </foquz-chars-counter>

            <validation-feedback
              params="show: formControlErrorStateMatcher(name), text: name.error"
            ></validation-feedback>
          </div>
        </div>
        <div class="col col-6">
          <div class="form-group mb-0 mb-md-4">
            <label
              class="form-label"
              data-bind="text: _t('polls', 'Местоположение папки')"
              >Местоположение папки</label
            >

            <select
              data-bind="
                  value: location,
                  select2: {
                      minimumResultsForSearch: 0,
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      templateResult: locationTemplateResult,
                      dropdownAutoWidth: false
                  },
                  foreach: locations
              "
            >
              <option
                data-bind="attr: { 'data-level': $data.level, value: $data.id }, disable: $data.inactive"
              >
                <!--ko text: $data.name-->
                <!--/ko-->
              </option>
            </select>
          </div>
        </div>
      </div>

      <div>
        <fc-label params="text: 'Описание'"></fc-label>
        <ckeditor
          params="
            value: description, variables: []"
        ></ckeditor>
      </div>

      <!-- /ko -->
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <fc-button class="mr-10p"
        params="icon: { name: 'bin' }, click: function() {
        $dialog.hide('close');
      }, label: _t('Отменить')"
      ></fc-button>

      <fc-button
        params="icon: { name: 'bin' }, click: function() {
        submit()
      }, label: _t('Сохранить'), color: 'success'"
      ></fc-button>
    </div>
  </div>
</sidesheet>
