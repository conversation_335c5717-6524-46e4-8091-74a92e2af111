import { ViewModel } from './model';
import html from './template.html';
import './style.less';
import "@/components/editor/ckeditor";

ko.components.register('poll-folder-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('poll-folder-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
