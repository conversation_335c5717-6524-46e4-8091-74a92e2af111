<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    <span data-bind="text: _t('polls', 'Переименовать папку')"></span>
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    

    <div class="form-group">
      <label class="form-label" data-bind="text: _t('Название')"></label>

      <fc-input params="value: name, counter: true, maxlength: 150, placeholder: _t('polls', 'Новая папка'),
        invalid: formControlErrorStateMatcher(name),
        valid: formControlSuccessStateMatcher(name)"></fc-input>

      <validation-feedback
        params="show: formControlErrorStateMatcher(name), text: name.error"
      ></validation-feedback>
    </div>

  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="
                click: function() {
                  $dialog.hide('close');
                }, text: _t('Отменить')"
      ></button>

      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="
                  click: function() {
                    submit();
                  }, text: _t('Сохранить')"
      ></button>
    </div>
  </div>
</foquz-dialog>
