import { DialogWrapper } from "Dialogs/wrapper";
import { request } from "@/utils/api/request";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.folder = params.folder;

    this.name = ko.observable(this.folder.name).extend({
      required: {
        message: _t("Обязательное поле"),
      },
      minLength: {
        params: 2,
        message: _t("main", "Не менее {characters}", {
          characters: _t("main", "{count} символов", {
            count: 2,
          }),
        }),
      },
    });
  }

  onEnter() {
    this.submit();
  }

  async submit() {
    this.isSubmitted(true);
    if (!this.name.isValid()) return;

    let params = {
      name: this.name(),
    };

    const { data, error } = await request("/foquz/api/v1/folder/update", {
      method: "POST",
      params: {
        id: this.folder.id,

      },
      body: params,
    });

    if (error) {
      console.error(error);
    } else {
      this.emitEvent("update");
      this.hide();
    }

   
  }
}
