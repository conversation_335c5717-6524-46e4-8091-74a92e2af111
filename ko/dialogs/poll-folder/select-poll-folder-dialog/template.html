<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    <!-- ko text: $parent.title -->
    <!-- /ko -->
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="survey-list__move-modal-dialog-search-string">
      <i class="survey-list__move-modal-dialog-search-string-icon"></i>
      <input
        class="form-control survey-list__move-modal-dialog-search-string-control"
        data-bind="textInput: searchString, attr: {
               placeholder: _t('Поиск по названию')
             }"
      />
      <!-- ko if: searchString().length > 0-->
      <button
        class="btn survey-list__move-modal-dialog-search-string-clear-button"
        data-bind="click: function() { searchString(''); }"
      ></button>
      <!-- /ko -->
    </div>

    <div class="survey-list__move-modal-dialog-folder-picker folder-picker">
      <!-- ko if: loading -->
      <fc-spinner class="f-color-primary"></fc-spinner>
      <!-- /ko -->

      <!-- ko if: flattenedFolders().length > 0 -->
      <div
        class="survey-list__move-modal-dialog-folder-picker-list"
        data-bind="nativeScrollbar"
      >
        <move-to-folder-dialog-list
          params="
            list: flattenedFolders,
            current: current,
            value: value,
            disabled: disabled"
        ></move-to-folder-dialog-list>
      </div>
      <!-- /ko -->

      <!-- ko if: !loading() && flattenedFolders().length === 0 -->
      <span
        data-bind="text: _t('polls', 'Папки с указанным названием в проект не добавлено.')"
      ></span>

      <!-- /ko -->
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="
                click: function() {
                  $dialog.hide('close');
                }, text: _t('Отменить')"
      ></button>

      <button
        type="button"
        class="f-btn"
        data-bind="
                  class: 'f-btn-' + mode,
                  click: function() {
                    submit();
                  },
                  disabled: selectedFolderId() === null, text: confirm"
      ></button>
    </div>
  </div>
</foquz-dialog>
