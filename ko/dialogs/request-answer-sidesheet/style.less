@import 'Style/colors';
@import 'Style/breakpoints';

.request-answer-sidesheet {
  .period-picker {
    min-width: 270px;
  }

  .foquz-dialog__header {
    padding-top: 15px;

    .only-mobile({
      display: flex;
      align-items: center;
      padding-top: 10px;
      padding-bottom: 10px;
    });
  }

  .foquz-dialog__body {
    padding-top: 20px;
  }

  .request-answer-meta {
    margin-left: -5px;
    margin-right: -5px;
    margin-bottom: 30px;
    overflow: hidden;

    display: flex;

    &__block {
      margin: 0 5px;

      background: #eceff1;
      border-radius: 8px;
      padding: 16px 16px 10px;

      min-height: 95px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }

  .request-answer-client {
    flex-grow: 1;
    max-width: 100%;
    overflow: hidden;

    &__name {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;

      .badge {
        margin-left: 8px;
      }

      .only-mobile({
        margin-bottom: 3px;
      })
    }

    &__info {
      .only-mobile({
        margin-bottom: 3px;
        .phone {
          display: none;
        }
      });
    }

    &__fields {
      margin-top: 8px;

      .only-mobile({
        margin-top: 15px;
      })
    }
  }

  .request-answer-phone {
    width: 95px;
    padding: 16px;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: center;

    .fc-btn-b {
      background-color: #37a74a;
      color: white;
      width: 44px;
      height: 44px;
    }
  }

  .request-answer-phone-btn {
    display: none;
    .fc-btn-b {
      background-color: @f-color-green;
      border-radius: 0;
    }
  }

  .request-answer-dates {
    width: 160px;

    .request-answer-date {
      color: @f-color-service;
      font-size: 12px;
      font-weight: 700;
      margin: 3px 0;

      display: flex;
      align-items: center;

      .fc-icon {
        margin-right: 10px;
      }

      .date {
        margin-right: 8px;
      }
    }

    .only-mobile({
      padding-top: 18px;
      padding-bottom: 18px;
    })
  }

  .request-answer-fields {
    .request-answer-field {
      margin-bottom: 24px;

      &__label {
        font-size: 14px;
        color: @f-color-service;
        margin-bottom: 8px;
      }
    }
  }

  hr {
    margin-left: -30px;
    margin-top: 5px;
  }

  .request-answer-processing {
    .request-form-field {
      .fc-textarea__field {
        min-height: 150px !important;
      }
    }
    &__files {
      display: flex;
      flex-wrap: wrap;
      margin-left: -8px;
      margin-right: -8px;

      .fc-media-wrapper {
        margin: 0 8px 8px;
      }

      .request-answer-processing__load-file {
        margin: 0 8px 8px;
      }
    }

    .fc-label--style_sm {
      margin-bottom: 8px;
      .fc-label__text {
        font-weight: 400;
      }
    }
  }

  .mobile-and-tablet({
    .foquz-dialog__close {
      background-color: #DFE3E5;
    }
  });

  .only-mobile({
    .foquz-dialog__body {
      padding-top: 0;
    }

    .request-answer-meta {
      display: block;
      margin-left: -15px;
      margin-right: -15px;

      &__block {
        width: 100%;
        min-height: 0;
        margin: 0;
        padding-left: 15px;
        padding-right: 15px;
        border-radius: 0;
      }
    }

    .request-answer-client {
      padding-bottom: 0;
    }

    .request-answer-phone {
      display: none;
    }
    .request-answer-phone-btn {
      display: block;
    }

    .request-answer-dates {
      flex-direction: row;

      .request-answer-date {
        flex-grow: 1;
      }
    }
  });
}
