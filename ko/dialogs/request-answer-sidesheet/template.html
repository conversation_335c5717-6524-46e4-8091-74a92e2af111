<template id="request-answer-field">
  <!-- ko if: field.name == 'priority'-->
  <!-- ko if: answer.answerFields[field.name] == 1 -->
  <span style="color: #40D259">Низкий</span>
  <!-- /ko -->
  <!-- ko if: answer.answerFields[field.name] == 2 -->
  <span style="color: #FF9900">Нормальный</span>
  <!-- /ko -->
  <!-- ko if: answer.answerFields[field.name] == 3 -->
  <span style="color: #FF0200">Высокий</span>
  <!-- /ko -->
  <!-- ko ifnot: answer.answerFields[field.name] -->
  –
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko if: field.name == 'files'-->
  <div class="d-flex">
    <!-- ko foreach: { data: answer.answerFields.files, as: 'file'} -->
    <fc-media params="url: file.src,
      gallery: $parent.answer.answerFields.files,
      index: $index"
              class="mr-5p"></fc-media>
    <!-- /ko -->

    <!-- ko ifnot: answer.answerFields.files.length -->
    –
    <!-- /ko -->
  </div>
  <!-- /ko -->


  <!-- ko if: ['priority', 'files'].indexOf(field.name) === -1-->
  <span data-bind="text: $component.dialogWrapper.formatFieldAnswer(field)"></span>
  <!-- /ko -->
</template>

<sidesheet params="ref: modal, dialogWrapper: $component">
  

  <!-- ko if: loading -->
  <fc-spinner class="f-color-primary"></fc-spinner>
  <!-- /ko -->

  <!-- ko ifnot: loading -->
  <div class="sidesheet__status-line"
       data-bind="class: 'f-color-' + statusColor()"></div>
       
  <div class="foquz-dialog__header">
    <div class="container">
      Проект <span class="bold"
            data-bind="text: answer.projectName"></span>
    </div>
  </div>

  <div class="foquz-dialog__body"
       data-bind="descendantsComplete: function() { onRender() }">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <div class="container pb-40p"
           data-tooltip-container>

        <div class="request-answer-meta">
          <!-- ko if: answer.client -->
          <div class="request-answer-meta__block request-answer-client">
            <!-- ko ifnot: hideClientData -->
            <!-- ko if: answer.clientSystemFields.name -->
            <div class="request-answer-client__name">
              <span data-bind="text: answer.clientSystemFields.name"></span>
              <!-- ko if: answer.ordersCount -->
              <span class="badge badge-active"
                    data-bind="text: answer.ordersCount, fbPopper, title: 'Количество заказов'"></span>
              <!-- /ko -->
            </div>
            <!-- /ko -->

            <!-- ko if: answer.clientSystemFields.phone || answer.clientSystemFields.email -->
            <div class="request-answer-client__info">

              <!-- ko if: answer.clientSystemFields.phone -->
              <span class="bold phone"
                    data-bind="text: answer.clientSystemFields.phone + (answer.clientSystemFields.email ? ',' : '')"></span>
              <!-- /ko -->

              <!-- ko if: answer.clientSystemFields.email -->
              <span class=""
                    data-bind="text: answer.clientSystemFields.email"></span>
              <!-- /ko -->

            </div>
            <!-- /ko -->
            <!-- /ko -->

            <client-fields class="request-answer-client__fields"
                           params="review: answer, hideClientData: hideClientData"></client-fields>
          </div>

          <!-- ko ifnot: hideClientData -->
          <!-- ko if: answer.clientSystemFields.phone -->
          <div class="request-answer-meta__block request-answer-phone">
            <fc-button params="linkMode: true,
              linkAttrs: { href: 'tel:' + answer.clientSystemFields.phone },
              shape: 'circle',
              icon: { name: 'phone', size: 21 }"></fc-button>
          </div>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /ko -->

          <div class="request-answer-meta__block request-answer-dates">
            <div class="request-answer-date"
                 data-bind="fbPopper, title: 'Дата создания заявки: ' + answer.createdAtDate">
              <fc-icon params="name: 'review-passed', size: 'sm', color: 'secondary'"></fc-icon>
              <span class="date"
                    data-bind="text: answer.createdAtDate"></span>
              <span class="time"
                    data-bind="text: answer.createdAtTime"></span>
            </div>
            <div class="request-answer-date"
                 data-bind="fbPopper, title: 'Дата обновления статуса заявки: ' + answer.updatedAtDate()">
              <fc-icon params="name: 'review-processed', size: 'sm', color: 'secondary'"></fc-icon>
              <span class="date"
                    data-bind="text: answer.updatedAtDate"></span>
              <span class="time"
                    data-bind="text: answer.updatedAtTime"></span>
            </div>
          </div>

          <!-- ko if: answer.clientSystemFields.phone -->
          <fc-button class="request-answer-phone-btn"
                     params="linkMode: true,
              label:  answer.clientSystemFields.phone,
              block: true,
              color: 'black',
              linkAttrs: { href: 'tel:' + answer.clientSystemFields.phone },
              icon: { name: 'phone', size: 21 }"></fc-button>
          <!-- /ko -->
        </div>


        <div class="request-answer-fields">
          <h2>Заявка</h2>

          <!-- ko if: answer.themeName -->
          <div class="row">
            <div class="col-12 col-md-6">
              <div class="request-answer-field">
                <div class="request-answer-field__label">Тема</div>
                <div class="request-answer-field__value"
                     data-bind="text: answer.themeName"></div>
              </div>
            </div>
          </div>
          <!-- /ko -->

          <div class="row">
            <!-- ko foreach: { data: orderFields, as: 'field' } -->
            <div class="col-12 col-md-6">
              <div class="request-answer-field">
                <div class="request-answer-field__label"
                     data-bind="text: field.type_field === 'file' ? 'Файлы' : field.label"></div>
                <div class="request-answer-field__value"
                     data-bind="template: {
                  name: 'request-answer-field',
                  data: { field: field, answer: $parent.answer }
                }"></div>
              </div>
            </div>
            <!-- /ko -->
          </div>
        </div>

        <hr>

        <div data-bind="using: form, dnd: function(files) {
          fileLoader.loadFiles(files);
        }, dndDisabled: isProcessingFinished,
        css: {
          'request-answer-processing--finished': isProcessingFinished
        }"
             class="request-answer-processing">
          <div class="mb-4 d-flex align-items-center justify-content-between">
            <h2 class="mb-0 pb-0">Обработка</h2>
  
            <fc-processing-history-button params="answerId: $parent.answerId, mode: 'request'"></fc-processing-history-button>
          </div>

          <div data-bind="if: !$parent.isProcessingFinished()">
            <div class="row">
              <!-- Статус (для всех) -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Статус'"></fc-label>
                  <fc-select params="value: status,
                    options: $parent.statuses,
                    invalid: $parent.formControlErrorStateMatcher(status)">
                    <template data-slot="option">
                      <!-- ko if: $data.disabled -->
                      <span class="f-color-service"
                            style="opacity: 0.6"
                            data-bind="text: $data.text"></span>
                      <!-- /ko -->
                      <!-- ko ifnot: $data.disabled -->
                      <span data-bind="class: 'f-color-' + $data.color, text: $data.text"></span>
                      <!-- /ko -->
                    </template>
                    <template data-slot="result">
                      <!-- ko if: $data.disabled -->
                      <span class="f-color-service"
                            style="opacity: 0.6"
                            data-bind="text: $data.text"></span>
                      <!-- /ko -->
                      <!-- ko ifnot: $data.disabled -->
                      <span data-bind="class: 'f-color-' + $data.color, text: $data.text"></span>
                      <!-- /ko -->
                    </template>
                  </fc-select>
                  <fc-error params="show: $parent.formControlErrorStateMatcher(status), text: status.error"></fc-error>
                </div>
              </div>

              <!-- Модератор (админ) -->
              <!-- ko ifnot: $parent.isExecutor -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Модератор'"></fc-label>
                  <fc-select
                             params="value: moderator,
                    fields: { text: 'name'},
                    options: $parent.getModerators,
                    clearable: true,
                    invalid: $parent.formControlErrorStateMatcher(moderator),            placeholder: 'Выберите модератора'">

<template data-slot="option">

  <div class="d-flex align-items-center user-option">
    <!-- ko if: $data.avatar -->
    <span class="user-option__icon"><img width="30" class="mr-10p user-option__userpic" data-bind="attr: { src: avatar }"></span>
    <!-- /ko -->
    <span data-bind="text: $data.name" class="user-option__name"></span>
  </div>
</template>

                  </fc-select>
                  <fc-error params="show: $parent.formControlErrorStateMatcher(moderator), text: moderator.error">
                  </fc-error>
                </div>
              </div>
              <!-- /ko -->

              <!-- Исполнитель (админ) -->
              <!-- ko ifnot: $parent.isExecutor -->
              <!-- ko ifnot: status() == 0 -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Исполнитель', style: $parent.isProcessingFinished() && 'sm'"></fc-label>
                  <fc-select params="value: executor,
                      fields: { text: 'name'},
                      options: $parent.getExecutors,
                      clearable: true,
                      invalid: $parent.formControlErrorStateMatcher(executor),
                      placeholder: 'Выберите исполнителя'">
                      <template data-slot="option">

                        <div class="d-flex align-items-center user-option">
                          <!-- ko if: $data.avatar -->
                          <span class="user-option__icon"><img width="30" class="mr-10p user-option__userpic" data-bind="attr: { src: avatar }"></span>
                          <!-- /ko -->
                          <span data-bind="text: $data.name" class="user-option__name"></span>
                        </div>
                      </template>
                    </fc-select>
                  <fc-error params="show: $parent.formControlErrorStateMatcher(executor), text: executor.error">
                  </fc-error>
                </div>
              </div>
              <!-- /ko -->
              <!-- /ko -->

              <!-- Обработать до (админ) -->
              <!-- ko ifnot: $parent.isExecutor -->
              <!-- ko ifnot: status() == 2 -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Обработать до'"></fc-label>
                  <date-picker params="value: processUp"
                               data-bind="css: {
                    'is-invalid': $parent.formControlErrorStateMatcher(processUp)
                  }"></date-picker>
                  <fc-error params="show: $parent.formControlErrorStateMatcher(processUp), text: processUp.error">
                  </fc-error>
                </div>
              </div>
              <!-- /ko -->
              <!-- /ko -->

              <!-- Отложить до (админ) -->
              <!-- ko ifnot: $parent.isExecutor -->
              <!-- ko if: status() == 2 -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Отложить до', style: $parent.isExecutor && 'sm'"></fc-label>
                  <date-picker params="value: delayedUp, mode: $parent.isExecutor ? 'view' : ''"
                               data-bind="css: {
                    'is-invalid': $parent.formControlErrorStateMatcher(delayedUp)
                  }"></date-picker>
                  <fc-error params="show: $parent.formControlErrorStateMatcher(delayedUp), text: delayedUp.error">
                  </fc-error>
                </div>
              </div>
              <!-- /ko -->
              <!-- /ko -->

              <!-- Сотрудник (исполнитель) -->
              <!-- ko if: $parent.isExecutor -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Сотрудник'"></fc-label>
                  <fc-select params="value: employee,
                    fields: { text: 'name'},
                    options: $parent.getEmployees,
                    optionsForSearch: 0,
                    clearable: true,
                    invalid: $parent.formControlErrorStateMatcher(employee),
                    placeholder: 'Выберите сотрудника'"></fc-select>
                  <fc-error params="show: $parent.formControlErrorStateMatcher(employee), text: employee.error">
                  </fc-error>
                </div>
              </div>
              <!-- /ko -->
            </div>

            <!-- ko ifnot: status() == 0 -->
            <div class="row">
              <!-- Причина плохого отзыва (админ) -->
              <!-- ko ifnot: $parent.isExecutor -->
              <div class="col-12 col-md-8">
                <div class="form-group">
                  <fc-label params="text: 'Причина плохого отзыва'"></fc-label>
                  <fc-select params="value: reason,
                    fields: { text: 'title'},
                    options: $parent.getReasons,
                    optionsForSearch: 0,
                    clearable: true,
                    placeholder: 'Выберите причину'"></fc-select>
                </div>
              </div>
              <!-- /ko -->

              <!-- Сотрудник (админ) -->
              <!-- ko ifnot: $parent.isExecutor -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Сотрудник'"></fc-label>
                  <fc-select params="value: employee,
                    fields: { text: 'name'},
                    options: $parent.getEmployees,
                    optionsForSearch: 0,
                    clearable: true,
                    invalid: $parent.formControlErrorStateMatcher(employee),
                    placeholder: 'Выберите сотрудника'"></fc-select>
                  <fc-error params="show: $parent.formControlErrorStateMatcher(employee), text: employee.error">
                  </fc-error>
                </div>
              </div>
              <!-- /ko -->

              <!-- Штраф или нарушение (для всех) -->
              <div class="col-12 col-md-8">
                <div class="form-group">
                  <fc-label params="text: 'Взыскание или нарушение'"></fc-label>

                  <fc-select params="value: fine, selected: $parent.selectedFine,
                    fields: { text: 'title', children: 'items' },
                    options: $parent.getFines,
                    clearable: true,
                    optionsForSearch: 0,
                    invalid: $parent.formControlErrorStateMatcher(fine),
                    placeholder: 'Выберите штраф'"></fc-select>

                  <fc-error params="show: $parent.formControlErrorStateMatcher(fine), text: fine.error">
                  </fc-error>
                </div>
              </div>
              <div class="col-12 col-md-4">
                <!-- ko if: $parent.selectedFine() -->

                <!-- ko if: $parent.selectedFine().hrm_type == 1 -->
                <div class="form-group">
                  <fc-label params="text: 'Сумма взыскания, ₽'"></fc-label>
                  <fc-input params="value: fineAmount, mask: 'numbers',"></fc-input>
                </div>
                <!-- /ko -->

                <!-- ko if: $parent.selectedFine().hrm_type == 2 -->
                <div class="form-group">
                  <fc-label params="text: 'Баллы за нарушение'"></fc-label>
                  <fc-input params="value: fineAmount, mask: 'numbers',"></fc-input>
                </div>
                <!-- /ko -->

                <!-- /ko -->
              </div>

              <!-- Компенсация (админ) -->
              <!-- ko ifnot: $parent.isExecutor -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Компенсация'"></fc-label>
                  <fc-select params="value: compensation,
                    fields: { text: 'title'},
                    options: $parent.getCompensations,
                    optionsForSearch: 0,
                    clearable: true,
                    placeholder: 'Выберите компенсацию'"></fc-select>
                </div>
              </div>
              <!-- /ko -->
            </div>
            <!-- /ko -->

            <!-- ReadOnly (исполнитель) -->
            <!-- ko if: $parent.isExecutor -->
            <div class="row">

              <!-- Модератор (исполнитель) -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Модератор', style: 'sm'"></fc-label>
                  <fc-select params="value: moderator,
                    fields: { text: 'name'},
                    options: $parent.getModerators,
                    readMode:true"></fc-select>
                </div>
              </div>

              <!-- Обработать до (исполнитель) -->
              <!-- ko ifnot: status() == 2 -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Обработать до', style: 'sm'"></fc-label>
                  <date-picker params="value: processUp, mode: 'view'"></date-picker>
                </div>
              </div>
              <!-- /ko -->

              <!-- Исполнитель (исполнитель) -->
              <!-- ko ifnot: status() == 0 -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Исполнитель', style: 'sm'"></fc-label>
                  <fc-select params="value: executor,
                      fields: { text: 'name'},
                      options: $parent.getExecutors,
                      readMode: true"></fc-select>

                </div>
              </div>
              <!-- /ko -->

              <!-- Причина плохого отзыва (исполнитель) -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Причина плохого отзыва', style: 'sm'"></fc-label>
                  <fc-select params="value: reason,
                    fields: { text: 'title'},
                    options: $parent.getReasons,
                    readMode: true"></fc-select>
                </div>
              </div>

              <!-- Компенсация (исполнитель) -->
              <div class="col-12 col-md-4">
                <div class="form-group">
                  <fc-label params="text: 'Компенсация', style: 'sm'"></fc-label>
                  <fc-select params="value: compensation,
                    fields: { text: 'title'},
                    options: $parent.getCompensations,
                    readMode: true"></fc-select>
                </div>
              </div>


            </div>
            <!-- /ko -->


            <div class="row">
              <div class="col-12">
                <!-- ko if: $parent.isExecutor -->
                <div class="form-group">
                  <fc-label params="text: 'Комментарий модератора', style: 'sm'"></fc-label>
                  <fc-textarea params="value: comment, readMode: true"></fc-textarea>
                </div>
                <!-- /ko -->

                <!-- ko ifnot: $parent.isExecutor -->
                <!-- ko if: executorComment -->
                <div class="form-group">
                  <fc-label params="text: 'Комментарий исполнителя', style: 'sm'"></fc-label>
                  <fc-textarea params="value: executorComment, readMode: true"></fc-textarea>
                </div>
                <!-- /ko -->
                <!-- /ko -->

                <div class="form-group">
                  <fc-label params="text: 'Комментарий', style: $parent.isProcessingFinished() && 'sm'"></fc-label>
                  <fc-textarea
                               params="value: $parent.isExecutor ? executorComment : comment, counter: true, maxlength: 500">
                  </fc-textarea>

                  <div class="mt-3 request-answer-processing__files">
                    <!-- ko foreach: { data: files, as: 'file'} -->
                    <fc-media-wrapper params="url: file.src,
                        loading: file.loading,
                        gallery: $parents[1].answer.processingFiles,
                        removeOnHover: false,
                        remove: true,
                        index: $index" data-bind="event: {
                          remove: function(file) {
                            $parents[1].removeFile(file)
                          }
                        }"></fc-media-wrapper>
                    <!-- /ko -->

                    <!-- ko ifnot: $parent.isProcessingFinished() -->
                    <fc-button class="request-answer-processing__load-file"
                               params="label: 'Прикрепить фото/видео',
                    icon: 'clip',
                    click: function() {
                      $parent.fileLoader.open();
                    }"></fc-button>
                    <!-- /ko -->
                  </div>

                  <!-- ko foreach: $parent.fileLoader.errors -->
                  <file-loader-error params="error: $data"></file-loader-error>
                  <!-- /ko -->

                </div>
              </div>
            </div>

            <div class="d-none d-md-block">
              <div class="row">
                <div class="col-6">
                  <!-- ko foreach: $parent.processingFields -->
                  <!-- ko ifnot: $index() % 2 -->
                  <div class="form-group">
                    <request-form-field params="field: $data, formControlErrorStateMatcher: $parents[1].formControlErrorStateMatcher,
                    readMode: $parents[1].isExecutor"></request-form-field>
                  </div>
                  <!-- /ko -->
                  <!-- /ko -->
                </div>
                <div class="col-6">
                  <!-- ko foreach: $parent.processingFields -->
                  <!-- ko if: $index() % 2 -->
                  <div class="form-group">
                    <request-form-field params="field: $data, formControlErrorStateMatcher: $parents[1].formControlErrorStateMatcher,
                    readMode: $parents[1].isExecutor"></request-form-field>
                  </div>
                  <!-- /ko -->
                  <!-- /ko -->
                </div>
              </div>
            </div>

            <div class="d-md-none">
              <div class="row">
                <!-- ko foreach: $parent.processingFields -->
                <div class="col-12">
                  <div class="form-group">
                    <request-form-field params="field: $data, formControlErrorStateMatcher: $parents[1].formControlErrorStateMatcher,
                    readMode: $parents[1].isExecutor"></request-form-field>
                  </div>
                </div>
                <!-- /ko -->
              </div>
            </div>

            <dnd-cover class="ma-n30p">
              <svg width="96"
                   height="96">
                <use href="#blank-file-icon"></use>
              </svg>

              <div class="mt-4"
                   data-bind="text: _t('answers', 'Перетащите файлы сюда, чтобы прикрепить их к комментарию')">
              </div>
              <div class="f-fs-1"
                   data-bind="text: _t('main', 'Максимальный размер файла — {size} Мб', {
                size: 5
              })"></div>
            </dnd-cover>
          </div>

          <div data-bind="if: $parent.isProcessingFinished()">
            <div class="row">
              <!-- Статус -->
              <div class="col-12 col-md-3">
                <div class="form-group">
                  <fc-label params="text: 'Статус', style: 'sm'"></fc-label>
                  <fc-select params="value: status,
                    options: $parent.statuses,
                    readMode: true">
                    <template data-slot="option">
                      <!-- ko if: $data.disabled -->
                      <span class="f-color-service"
                            style="opacity: 0.6"
                            data-bind="text: $data.text"></span>
                      <!-- /ko -->
                      <!-- ko ifnot: $data.disabled -->
                      <span data-bind="class: 'f-color-' + $data.color, text: $data.text"></span>
                      <!-- /ko -->
                    </template>
                    <template data-slot="result">
                      <!-- ko if: $data.disabled -->
                      <span class="f-color-service"
                            style="opacity: 0.6"
                            data-bind="text: $data.text"></span>
                      <!-- /ko -->
                      <!-- ko ifnot: $data.disabled -->
                      <span data-bind="class: 'f-color-' + $data.color, text: $data.text"></span>
                      <!-- /ko -->
                    </template>
                  </fc-select>
                </div>
              </div>

              <!-- Модератор -->

              <div class="col-12 col-md-3">
                <div class="form-group">
                  <fc-label params="text: 'Модератор', style: 'sm'"></fc-label>
                  <fc-select params="value: moderator,
                    fields: { text: 'name'},
                    options: $parent.getModerators,
                    readMode: true,
                    placeholder: 'Выберите модератора'"></fc-select>
                  </fc-error>
                </div>
              </div>


              <!-- Исполнитель -->
              <div class="col-12 col-md-3">
                <div class="form-group">
                  <fc-label params="text: 'Исполнитель', style: 'sm'"></fc-label>
                  <fc-select params="value: executor,
                      fields: { text: 'name'},
                      options: $parent.getExecutors,

                      readMode: true,
                      placeholder: 'Выберите исполнителя'"></fc-select>
                  </fc-error>
                </div>
              </div>


              <!-- Обработать до -->


              <div class="col-12 col-md-3">
                <div class="form-group">
                  <fc-label params="text: 'Обработать до', style:'sm'"></fc-label>
                  <date-picker params="value: processUp, mode: 'view' "></date-picker>
                  </fc-error>
                </div>
              </div>




              <!-- Причина плохого отзыва  -->

              <div class="col-12 col-md-3">
                <div class="form-group">
                  <fc-label params="text: 'Причина плохого отзыва', style: 'sm'"></fc-label>
                  <fc-select params="value: reason,
                    fields: { text: 'title'},
                    options: $parent.getReasons,

                    readMode: true,
                    placeholder: 'Выберите причину'"></fc-select>
                </div>
              </div>


              <!-- Сотрудник  -->

              <div class="col-12 col-md-3">
                <div class="form-group">
                  <fc-label params="text: 'Сотрудник', style: 'sm'"></fc-label>
                  <fc-select params="value: employee,
                    fields: { text: 'name'},
                    options: $parent.getEmployees,

                    readMode: true,
                    placeholder: 'Выберите сотрудника'"></fc-select>
                  </fc-error>
                </div>
              </div>

              <!-- Штраф или нарушение (для всех) -->
              <div class="col-12 col-md-3">
                <div class="form-group">
                  <fc-label params="text: 'Взыскание или нарушение', style: 'sm'">
                  </fc-label>

                  <fc-select params="value: fine, selected: $parent.selectedFine,
                    fields: { text: 'title', children: 'items' },
                    options: $parent.getFines,

                    readMode:true,
                    placeholder: 'Выберите взыскание'"></fc-select>

                  <!-- ko if: $parent.selectedFine() -->

                  <!-- ko if: $parent.selectedFine().hrm_type == 1 -->
                  <div class="f-fs-1 f-color-service font-weight-500">
                    Взыскание, <span data-bind="text: fineAmount() + '₽'"></span>
                  </div>
                  <!-- /ko -->

                  <!-- ko if: $parent.selectedFine().hrm_type == 2 -->
                  <div class="f-fs-1 f-color-service font-weight-500">
                    Нарушение, <span data-bind="text: fineAmount() + '₽'"></span>

                  </div>
                  <!-- /ko -->

                  <!-- /ko -->

                </div>


              </div>

              <!-- Компенсация  -->
              <div class="col-12 col-md-3">
                <div class="form-group">
                  <fc-label params="text: 'Компенсация', style:'sm'"></fc-label>
                  <fc-select params="value: compensation,
                    fields: { text: 'title'},
                    options: $parent.getCompensations,

                    readMode: true,
                    placeholder: 'Выберите компенсацию'"></fc-select>
                </div>
              </div>

            </div>


            <div class="row">
              <div class="col-12">

                <div class="form-group">
                  <fc-label params="text: 'Комментарий модератора', style: 'sm'"></fc-label>
                  <fc-textarea params="value: comment, readMode: true"></fc-textarea>
                </div>



                <div class="form-group">
                  <fc-label params="text: 'Комментарий исполнителя', style: 'sm'"></fc-label>
                  <fc-textarea params="value: executorComment, readMode: true"></fc-textarea>

                  <div class="mt-3 request-answer-processing__files">
                    <!-- ko foreach: { data: files, as: 'file'} -->
                    <fc-media-wrapper params="url: file.src,
                        loading: file.loading,
                        gallery: $parents[1].answer.processingFiles,
                        remove: false,
                        removeOnHover: false,
                        index: $index"></fc-media-wrapper>
                    <!-- /ko -->
                  </div>


                </div>
              </div>
            </div>

            <div class="d-none d-md-block">
              <div class="row">
                <div class="col-6">
                  <!-- ko foreach: $parent.processingFields -->
                  <!-- ko ifnot: $index() % 2 -->
                  <div class="form-group">
                    <request-form-field params="field: $data, formControlErrorStateMatcher: $parents[1].formControlErrorStateMatcher,
                    readMode:true"></request-form-field>
                  </div>
                  <!-- /ko -->
                  <!-- /ko -->
                </div>
                <div class="col-6">
                  <!-- ko foreach: $parent.processingFields -->
                  <!-- ko if: $index() % 2 -->
                  <div class="form-group">
                    <request-form-field params="field: $data, formControlErrorStateMatcher: $parents[1].formControlErrorStateMatcher,
                    readMode: true"></request-form-field>
                  </div>
                  <!-- /ko -->
                  <!-- /ko -->
                </div>
              </div>
            </div>

            <div class="d-md-none">
              <div class="row">
                <!-- ko foreach: $parent.processingFields -->
                <div class="col-12">
                  <div class="form-group">
                    <request-form-field params="field: $data, formControlErrorStateMatcher: $parents[1].formControlErrorStateMatcher,
                    readMode:true"></request-form-field>
                  </div>
                </div>
                <!-- /ko -->
              </div>
            </div>


          </div>


        </div>

      </div>
    </div>
  </div>

  <!-- ko ifnot: isProcessingFinished -->
  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn"
              data-bind="click: function() {
                $dialog.hide();
              }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Отменить
      </button>
      <button type="button"
              class="f-btn f-btn-success"
              data-bind="click: function() {
                submit();
              }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        Сохранить
      </button>
    </div>

    <fc-success params="show: showSuccessMessage"></fc-success>
  </div>
  <!-- /ko -->
  <!-- /ko -->

</sidesheet>
