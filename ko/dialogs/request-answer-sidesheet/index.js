import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/client-fields';
import 'Components/request-form-field';

ko.components.register('request-answer-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('request-answer-sidesheet');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
