import { DialogWrapper } from "Dialogs/wrapper";
import { ApiUrl } from "Utils/url/api-url";
import { dateValidator } from "Utils/validation/date";
import { formatClientDateToServer } from "Utils/date/format";
import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";
import { RequestFormField } from "Entities/models/request-form-field";
import {
  PROCESSING_DONE,
  PROCESSING_NEW,
  PROCESSING_WORK,
} from "Data/processing-statuses";
import { request } from "@/utils/api/request";
import { RequestAnswer } from "@/entities/models/request-answer";
import "@/presentation/views/fc-processing-history-button";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.loading = ko.observable(true);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.showSuccessMessage = ko.observable(false);

    this.isExecutor = params.isExecutor;
    this.hideClientData = params.hideClientData;

    this.statusColors = {
      0: "mint",
      1: "blue",
      4: "gold",
      2: "danger",
      3: "violet",
    };

    this.orderFields = ko.observableArray([]);
    this.orderHasFiles = false;

    this.processingFieldsLoading = ko.observable(true);
    this.processingFields = ko.observableArray([]);


    this.fileLoader = new FoquzMultipleLoader();
    this.fileLoader.on("select", ({ file }) => {
      this.loadFile(file);
    });

    this.answerId = params.answer.entityId || params.answer.id

    request("/foquz/api/requests-projects-answers/index?", {
      params: {
        id: this.answerId,
      },
    }).then((response) => {
      const { data } = response;
      this.init(data.items);

      this.loading(false);
    });
  }

  init(answer) {
    console.log('reuest-answer-sidesheet init', answer)
    this.answer = new RequestAnswer(answer);

    this.projectId = this.answer.project.id;

    this.status = this.answer.processingStatus;

    this.isProcessingFinished = ko.computed(() => {
      return this.status() == 3;
    });
    this.statusColor = ko.pureComputed(() => {
      let status = this.status();
      return this.statusColors[status];
    });

    this.getFields("order").then((items) => {
      items = (items || []).filter((item) => {
        if (item.name === "client_id") return false;
        return true;
      });
      this.orderFields(items);
    });

    let processing = this.answer.processing();
    this.processingId = processing.id;


    let processingCustomFields = processing.custom_fields || {};

    this.getFields("processing").then((items) => {
      this.processingFields(
        (items || []).map((f) => {
          let field = new RequestFormField(f);
          let value = processingCustomFields[f.name];
          if (value) field.setValue(value);
          return field;
        })
      );
      this.processingFieldsLoading(false);
    });

    let processUp = processing.process_up
      ? moment(processing.process_up, "YYYY-MM-DD").format("DD.MM.YYYY")
      : "";
    let delayedUp = processing.delayed_up
      ? moment(processing.delayed_up, "YYYY-MM-DD").format("DD.MM.YYYY")
      : "";

    let status = ko.observable(processing.status).extend({
      required: {
        message: "Обязательное поле",
      },
    });

    let fine = processing.fine || {};
    this.selectedFine = ko.observable(null);
    this.savedFine = ko.observable(fine.id);

    this.form = ko.validatedObservable(
      {
        status,
        moderator: ko.observable(processing.moderator?.id).extend({
          required: {
            message: "Обязательное поле",
          },
        }),
        executor: ko.observable(processing.executor?.id).extend({
          required: {
            message: "Обязательное поле",
            onlyIf: () => status() == 4,
          },
        }),
        processUp: ko.observable(processUp).extend({
          validation: {
            validator: dateValidator(),
            message: "Некорректный формат",
          },
        }),
        delayedUp: ko.observable(delayedUp).extend({
          required: {
            message: "Обязательное поле",
            onlyIf: () => status() == 2,
          },
          validation: {
            validator: dateValidator(),
            message: "Некорректный формат",
          },
        }),
        reason: ko.observable(processing.reason && processing.reason.id),
        employee: ko
          .observable(processing.employee && processing.employee.id)
          .extend({
            required: {
              message: "Обязательное поле",
              onlyIf: () => this.isExecutor,
            },
          }),
        fine: ko.observable(fine.id).extend({
          required: {
            message: "Обязательное поле",
            onlyIf: () => this.isExecutor,
          },
        }),
        fineAmount: ko.observable(processing.fine_amount || fine.amount),
        compensation: ko.observable(
          processing.compensation && processing.compensation.id
        ),
        comment: ko.observable(processing.comment),
        executorComment: ko.observable(processing.executorn_comment),
        files: ko.observableArray(
          this.answer.processingFiles().map((file) => {
            return {
              src: ko.observable(file.src),
              loading: ko.observable(false),
              id: file.id,
              poster: file.src,
            };
          })
        ),
      },
      { live: true, deep: true }
    );

    this.statuses = [
      {
        id: 0,
        text: "Новая",
        color: "mint",
        disabled: !this.isActiveStatus(0),
      },
      {
        id: 1,
        text: "В процессе",
        color: "blue",
        disabled: !this.isActiveStatus(1),
      },
      {
        id: 4,
        text: "Обрабатывается исполнителем",
        color: "gold",
        disabled: !this.isActiveStatus(4),
      },
      {
        id: 2,
        text: "Отложена",
        color: "red",
        disabled: !this.isActiveStatus(2),
      },
      {
        id: 3,
        text: "Обработана",
        color: "violet",
        disabled: !this.isActiveStatus(3),
      },
    ];
  }

  formatFieldAnswer(field) {
    let value = this.answer.answerFields[field.name];
    if (!value) return "–";
    if (field.type_field === "date") {
      let date = moment(value, "YYYY-MM-DD").format("DD.MM.YYYY");
      return date;
    }
    if (field.type_field === "select") {
      let options = field.select_options;
      let values = Array.isArray(value) ? [...value] : [value];
      return values
        .map((vId) => {
          let option = options.find(({ id }) => id == vId);
          if (option) return option.text;
        })
        .filter(Boolean)
        .join(", ");
    }
    return value;
  }

  isActiveStatus(newStatus) {
    newStatus = parseInt(newStatus);

    let currentStatus = this.status();
    currentStatus = parseInt(currentStatus);

    if (this.isExecutor) {
      if (currentStatus == PROCESSING_WORK) {
        return newStatus == PROCESSING_DONE;
      }
      return false;
    }

    switch (currentStatus) {
      case PROCESSING_NEW:
        return true;
      case PROCESSING_DONE:
        return newStatus == PROCESSING_DONE;
      default:
        return newStatus !== PROCESSING_NEW;
    }
  }

  loadFile(file) {
    let fileObj = {
      loading: ko.observable(true),
      src: ko.observable(""),
    };
    this.form().files.push(fileObj);
    let fd = new FormData();
    fd.append("FileUploadForm[file]", file);
    $.ajax({
      method: "POST",
      url: `${APIConfig.baseApiUrlPath}answer-processing/upload-file?id=${this.processingId}&access-token=${APIConfig.apiKey}`,
      processData: false,
      contentType: false,
      data: fd,
      success: (response) => {
        let file = response.file;
        fileObj.id = file.id;
        fileObj.poster = file.poster_url || file.file_path;
        fileObj.src(file.file_path);
        fileObj.loading(false);

        this.answer.processingFiles.push({
          src: file.file_path,
          id: file.id,
        });
      },
      error: (response) => {
        this.form().files.remove(fileObj);
        console.error(response);
      },
    });
  }

  removeFile(file) {
    if (
      !this.form()
        .files()
        .some((f) => f === file)
    ) {
      return;
    }

    this.form().files.remove(file);
    this.answer.processingFiles.remove((f) => f.id === file.id);

    // $.ajax({
    //   method: "DELETE",
    //   url: `${APIConfig.baseApiUrlPath}answer-processing/remove-file?id=${file.id}&access-token=${APIConfig.apiKey}`,
    //   success: (response) => {},
    //   error: (response) => {
    //     console.error(response);
    //   },
    // });
  }

  getModerators(params, success) {
    fetch(ApiUrl("answers/moderators"))
      .then((res) => res.json())
      .then((json) => {
        success(json.items);
      });
  }

  getExecutors(params, success) {
    fetch(ApiUrl("answers/executors"))
      .then((res) => res.json())
      .then((json) => {
        success(json.items);
      });
  }

  getReasons(params, success) {
    fetch(ApiUrl("dictionaries/reasons", { all: 1 }))
      .then((res) => res.json())
      .then((json) => {
        success(json.items);
      });
  }

  getEmployees(params, success) {
    fetch(ApiUrl("dictionaries/employees", { all: 1 }))
      .then((res) => res.json())
      .then((json) => {
        success(json.items);
      });
  }

  getFines(params, success) {
    fetch(ApiUrl("dictionaries/fines/fine-categories"))
      .then((res) => res.json())
      .then((json) => {
        let groups = json.items.filter((g) => g.items.length);
        groups.forEach((g) => {
          g.title = g.name;
          g.id = null;
          g.category = true;
          g.items = g.items.map(item => {
            return {
              ...item,
              text: item.title
            }
          })
        });
        success(groups);
      });
  }

  getCompensations(params, success) {
    fetch(ApiUrl("dictionaries/compensations", { all: 1 }))
      .then((res) => res.json())
      .then((json) => {
        success(json.items);
      });
  }

  getFields(formType) {
    return fetch(
      ApiUrl("request-project-field/index", {
        projectId: this.projectId,
        type_form: formType,
      })
    )
      .then((res) => res.json())
      .then((json) => json.items);
  }

  getParams() {
    let form = ko.toJS(this.form);
    let params = {
      status: form.status,
      moderator_id: form.moderator,
      executor_id: form.executor,
      process_up: formatClientDateToServer(form.processUp),
      delayed_up: formatClientDateToServer(form.delayedUp),
      reason_id: form.reason,
      fine_id: form.fine,
      fine_amount: form.fineAmount,
      compensation_id: form.compensation,
      employee_id: form.employee,
      comment: form.comment,
      executor_comment: form.executorComment,
      files: form.files.map(f => f.id)
    };

    this.processingFields().forEach((f) => {
      params[f.name] = f.getValue();
    });

    return params;
  }

  submit() {
    this.isSubmitted(true);
    if (!this.form.isValid()) return;
    if (this.processingFields().some((f) => !f.isValid())) return;

    this.isSubmitted(false);
    let params = this.getParams();
    this.answer
      .update(params)
      .then(() => {
        this.showSuccessMessage(true);

      })
      .catch((err) => {
        console.error(err);
        this.showSuccessMessage(true);
      });
  }

  onRender() {
    this.selectedFine.subscribe((v) => {
      if (v) {
        if (v.id == this.savedFine()) return;
        this.savedFine(v.id);
        this.form().fineAmount(v.amount);
      } else {
        this.savedFine(null);
        this.form().fineAmount("");
      }
    });
  }
}
