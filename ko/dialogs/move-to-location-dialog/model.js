import { DialogWrapper } from 'Dialogs/wrapper';
import { flattenList } from '@/utils/list/flatten-list';
import { useList } from '@/utils/list/use-list';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.disabledLevel = params.disabledLevel;

    this.listRendered = ko.observable(false);

    this.title = params.title || 'Переместить объект в папку';
    const { items, dispose, loading } = useList({
      items: params.locations,
      handleItems: (items) => {
        return flattenList(items);
      }
    });

    this.loading = loading;
    this.pending = ko.observable(false);
    this.location = ko.observable(params.location || 0);
    this.error = ko.observable(null);

    this.query = ko.observable('');
    this.formattedQuery = ko.pureComputed(() => {
      return this.query().trim().toLowerCase();
    });

    this.locations = ko.computed(() => {
      let q = this.formattedQuery();
      if (!q) return items();
      return items().filter((i) => i.text.includes(q));
    });

    this.subscriptions.push(this.location.subscribe((v) => this.error(null)));
  }

  submit() {
    this.pending(true);
    let data = {
      locationId: this.location(),
      onError: (error) => {
        this.error(error);
        this.pending(false);
        console.error(error);
      }
    };
    this.emitEvent('submit', data);
  }
}
