<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    <span data-bind="text: $parent.title"></span>
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">
      <fc-input
        params="value: query, placeholder: 'Поиск по названию', icon: 'search', clearable: true"
      ></fc-input>
    </div>

    <div class="form-group mb-0 mb-md-4">
      <fc-scroll class="move-to-location-dialog__scroll">
        <!-- ko if: loading() || !listRendered() -->
        <fc-spinner class="f-color-primary" params="size: 'sm'"></fc-spinner>
        <!-- /ko -->

        <!-- ko if: !loading() -->
        <fc-list
          style="display: none"
          params="options: locations, value: location, disabledLevel: disabledLevel"
          data-bind="event: {
                  select: function(_, e, option) {
                    console.log('select', option)
                    location(option.id) },
                  }, descendantsComplete: function(el) {
                    listRendered(true);
                    el.style.display = '';
                  }"
          class="fc-select-list"
        >
          <template data-slot="item">
            <span data-bind="text: text"></span>
          </template>

          <template data-slot="empty">
            <div class="fc-select-list__empty">Ничего не найдено</div>
          </template>
        </fc-list>
        <!-- /ko -->
      </fc-scroll>

      <fc-error params="show: error, text: error"></fc-error>
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <fc-button
        params="color: 'primary', inverse: true, click: function() { $dialog.hide('close') }, label: 'Отменить'"
      ></fc-button>
      <fc-button
        params="click: function() { submit() }, label: 'Переместить', pending: pending"
      ></fc-button>
    </div>
  </div>
</foquz-dialog>
