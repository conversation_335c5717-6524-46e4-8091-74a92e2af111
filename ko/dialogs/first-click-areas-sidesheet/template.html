<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__body">
    <div class="first-click-areas-content">
      <!-- Image with click areas on the left -->
      <div class="first-click-areas-image-panel">
        <div class="first-click-areas-image-panel__image-info-bar">
          <div class="first-click-areas-image-panel__image-info-bar-item">
          <span class="first-click-areas-image-panel__image-info-bar-label">Размер изображения:</span>
          <span class="first-click-areas-image-panel__image-info-bar-value">
            <span class="first-click-areas-image-panel__image-info-bar-value-width" data-bind="text: originalImageDimensions().width"></span>
            <span class="first-click-areas-image-panel__image-info-bar-value-separator">x</span>
            <span class="first-click-areas-image-panel__image-info-bar-value-height" data-bind="text: originalImageDimensions().height"></span>
            <span class="first-click-areas-image-panel__image-info-bar-value-unit">px</span>
          </span>
          </div>
          <div class="first-click-areas-image-panel__image-info-bar-item">
            <span class="first-click-areas-image-panel__image-info-bar-label">Масштаб:</span>
            <span class="first-click-areas-image-panel__image-info-bar-value bold" data-bind="text: imageScalePercent() + '%'"></span>
          </div>
        </div>
        <div class="image-container">
          <!-- ko if: imageUrl() -->
          <image-click-areas
            params="
            imageUrl: imageUrl,
            areas: areas,
            onAreasChange: onAreasChange.bind($data),
            selectedAreaObservable: selectedArea,
            augmentAreaWithPixelComputeds: augmentAreaWithPixelComputeds.bind($data),
            originalImageDimensions: originalImageDimensions,
            displayedImageDimensionsOutput: childDisplayedImageDimensions
          "
          ></image-click-areas>
          <!-- /ko -->

          <!-- ko if: !imageUrl() -->
          <div class="no-image-placeholder">
            <div class="no-image-icon">
              <svg-icon params="name: 'image', size: 48"></svg-icon>
            </div>
            <p>Изображение не загружено</p>
          </div>
          <!-- /ko -->
        </div>
      </div>

      <!-- Areas list and properties on the right -->
      <div
        class="first-click-areas-properties-panel"
        data-bind="fScrollbar: scrollableProperties"
      >
        <div class="first-click-areas-properties-panel__inner">
          <div class="first-click-areas-properties-panel__header">
            <!-- Informational text -->
            <div class="f-color-service f-fs-1 mb-15p">
              В статистике будет указано количество кликов по добавленным
              областям
            </div>

            <!-- Primary Add Area Button -->
            <button
              type="button"
              class="f-btn f-btn-success f-btn-text add-area-btn"
              data-bind="click: addArea"
            >
              <span class="f-btn-prepend">
                <foquz-icon
                  params="icon: 'plus'"
                  class="f-icon f-icon--plus"
                ></foquz-icon>
              </span>
              <span>Добавить область</span>
            </button>
          </div>

          <div class="first-click-areas-properties-panel__body">

            <!-- ko if: areas().length > 0 -->
            <div class="first-click-areas-properties-panel__body-items">
              <!-- ko foreach: {
                data: areas,
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
              <div
                class="first-click-areas-properties-panel__body-item"
                data-bind="css: { 'selected': $parent.selectedArea() === $data }"
              >
                <!-- Main content area -->
                <div
                  class="first-click-areas-properties-panel__body-item-content"
                  data-bind="click: function() { $parent.selectArea($data); }"
                >
                  <!-- First row: Textarea with counter -->
                  <div class="first-click-areas-properties-panel__body-item-content-name">
                    <fc-textarea
                      params="
                        value: name,
                        height: 48,
                        maxlength: 250,
                        counter: true,
                        placeholder: 'Область ' + ($index() + 1),
                        events: { focus: function() { $parent.selectArea($data); } }
                      "
                    ></fc-textarea>
                  </div>

                  <!-- Second row: Dimension inputs -->
                  <div class="first-click-areas-properties-panel__body-item-content-dimensions">
                    <div class="first-click-areas-properties-panel__body-item-content-dimensions-inputs">
                      <fc-input
                        params="
                          value: pixelWidth,
                          placeholder: '50',
                          mask: { alias: 'numeric', digits: 0, max: $parent.originalImageDimensions().width, min: 10, allowMinus: false, rightAlign: false, SetMaxOnOverflow: true },
                          events: { focus: function() { $parent.selectArea($data); } }
                        "
                        class="dimension-input"
                      ></fc-input>
                      <span class="first-click-areas-properties-panel__body-item-content-dimensions-separator">
                        <span class="first-click-areas-properties-panel__body-item-content-dimensions-separator-text">x</span>
                      </span>
                      <fc-input
                        params="
                      value: pixelHeight,
                      placeholder: '50',
                      mask: { alias: 'numeric', digits: 0, max: $parent.originalImageDimensions().height, min: 10, allowMinus: false, rightAlign: false, SetMaxOnOverflow: true },
                      events: { focus: function() { $parent.selectArea($data); } }
                    "
                        class="dimension-input"
                      ></fc-input>
                    </div>
                  </div>
                </div>

                <!-- Delete Button -->
                <div class="first-click-areas-properties-panel__body-item-content-actions">
                  <button
                    type="button"
                    class="f-btn f-btn-icon f-btn-danger f-btn-text f-color-service"
                    data-bind="click: function(data, event) { $parent.removeArea.bind($parent, $data)(); event.stopPropagation(); }"
                  >
                    <foquz-icon
                      params="icon: 'bin'"
                      class="f-icon"
                    ></foquz-icon>
                  </button>
                </div>
              </div>
              <!-- /ko -->
            </div>

            <!-- Footer -->
            <div class="first-click-areas-properties-panel__body-items-footer">
              <button
                type="button"
                class="f-btn f-btn-success f-btn-text add-area-btn secondary mt-15p"
                data-bind="click: addArea, visible: areas().length > 0"
              >
                <span class="f-btn-prepend">
                  <foquz-icon
                    params="icon: 'plus'"
                    class="f-icon f-icon--plus"
                  ></foquz-icon>
                </span>
                <span>Добавить область</span>
              </button>
            </div>

            <!-- /ko -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-secondary"
        data-bind="click: cancelChanges"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Отменить
      </button>

      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="click: saveAreas"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'check'"></svg-icon>
        </span>
        Применить
      </button>
    </div>
  </div>
</sidesheet>
