@import 'Style/colors'; // Changed to use the Style alias

.first-click-areas-sidesheet {
  .foquz-dialog__body {
    padding: 33px 30px 0 30px;
  }
  .sidesheet-foquz__dialog {
    padding-top: 0;
  }
  .foquz-dialog__container {
    padding-left: 60px;
  }

  @media (max-width: 1199px) {
    .foquz-dialog__container {
      padding-left: 0;
    }
  }
}

.first-click-areas-content {
  display: flex;
  height: 100%;
  gap: 30px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
  }
}

.first-click-areas-image-panel {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex-grow: 1;
  flex-shrink: 1;
  
  &__image-info-bar {
    margin-bottom: 15px;
    font-size: 15px;
    line-height: 1.2;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: 10px;

    &-item {
      display: inline-block;
    }

    &-label {
      color: @f-color-service;
    }

    &-value {
      color: @f-color-text;

      &-width,
      &-height {
        font-weight: 700;
      }
    }
  }
  
  .image-container {
    flex: 1;
    padding-bottom: 20px;
  }
  
  .image-controls {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    
    .f-btn {
      padding: 6px 12px;
    }

    @media (max-width: 768px) {
      flex-wrap: wrap;
    }
  }
}

.first-click-areas-properties-panel {
  width: 255px;
  padding-left: 10px;
  margin-right: -20px;
  display: flex;
  flex-direction: column;
  max-height: calc(100% - 20px);
  flex: 0 0 auto;

  &__header {
    margin-bottom: 16px;
  }

  &__inner {
    padding-right: 20px;
  }

  @media (max-width: 768px) {
    min-width: 0;
    max-height: 100%;
  }
}

.first-click-areas-properties-panel__body {
  flex: 1;
}

.first-click-areas-properties-panel__body-items {
  display: flex;
  flex-direction: column;
}

.first-click-areas-properties-panel__body-item {
  display: flex;
  align-items: flex-start;
  padding-top: 25px;
  padding-bottom: 25px;
  position: relative;
  border-top: 1px solid #E7EBED;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: -10px;
    width: calc(100% + 20px);
    height: 100%;
    background-color: #F2F5F6;
    border-radius: 9px;
    opacity: 0;
    transition: opacity 0.2s;
    z-index: -1;
  }

  &:last-child {
    border-bottom: 1px solid #E7EBED;
  }

  &:hover {
    &:before {
      opacity: 0.4;
    }
  }

  &.selected {
    &:before {
      opacity: 1;
    }

    .fc-textarea__count {
      color: #73808D;
    }
  }

  &-content {
    flex: 1;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 0;

    &-name {
      position: relative;

      .fc-textarea__field {
        min-height: 48px;
        padding-right: 42px;
      }

      .fc-textarea__count {
        bottom: 50%;
        transform: translateY(50%);
      }
    }

    &-dimensions {
      &-inputs {
        display: flex;
        align-items: center;

        .dimension-input {
          width: 82px;
          text-align: center;
        }
      }

      &-separator {
        flex: 0 0 auto;
        font-weight: 400;
        font-size: 16px;
        line-height: 1.1;
        height: 48px;
        width: 30px;
        color: #2E2F31;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  &-content-actions {
    flex-shrink: 0;
    margin-top: -8px;
    margin-right: -7px;
    margin-left: 6px;

    
    .f-btn.f-btn-icon {
      padding: 0;
      min-width: auto;
      height: 32px;
      width: 32px;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .first-click-areas-sidesheet {
    .foquz-dialog__content {
      width: 95vw;
      height: 95vh;
    }
  }

  .first-click-areas-properties-panel__body-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;

    &-content {
      width: 100%;
    }

    &-dimensions-inputs {
      justify-content: center;
    }
  }
}
