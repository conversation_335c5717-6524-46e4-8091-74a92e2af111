import { DialogWrapper } from "Dialogs/wrapper";
import "Components/image-click-areas";

export class ViewModel extends DialogWrapper {
  /**
   * Конструктор модели представления для диалога областей первого клика
   * @param {Object} params - Параметры инициализации
   * @param {string} params.imageUrl - URL изображения
   * @param {Function} params.onSave - Функция обратного вызова для сохранения
   * @param {Array} params.areas - Массив существующих областей
   * @param {Object} [params.passedOriginalImageDimensions] - Optional pre-loaded dimensions {width, height}
   * @param {HTMLElement} element - DOM элемент диалога
   */
  constructor(params, element) {
    super(params, element);

    this.imageUrl = params.imageUrl;
    this.onSave = params.onSave;
    this.selectedArea = ko.observable(null);

    // Use passed dimensions if valid, otherwise initialize to 0 and load
    const passedDims = ko.unwrap(params.passedOriginalImageDimensions);
    if (passedDims && passedDims.width > 0 && passedDims.height > 0) {
      this.originalImageDimensions = ko.observable({ width: passedDims.width, height: passedDims.height });
    } else {
      this.originalImageDimensions = ko.observable({ width: 0, height: 0 });
      this.loadImageDimensions(); // Load only if not passed or invalid
    }

    this.childDisplayedImageDimensions = ko.observable({ width: 0, height: 0 }); // Added for scale calculation
    this.scrollableProperties = ko.observable();

    this.setupAreaManagement(); // This might need to be after dimensions are potentially loaded if not passed

    /**
     * Дополняет область дополнительными свойствами:
     * - pixelWidth - ширина области в пикселях
     * - pixelHeight - высота области в пикселях
     * - isSelected - выбрана ли область
     * - clickCount - количество кликов на область
     * - name - имя области
     * - x - позиция области по горизонтали
     * - y - позиция области по вертикали
     * @param {Object} area - Область
     * @returns {Object} Область с дополнительными свойствами
     */
    this.augmentAreaWithPixelComputeds = (area) => {
      if (!area) return area;
      const self = this;

      console.log("тест-первого-клика augmentAreaWithPixelComputeds", area);

      area.pixelWidth = ko
        .computed({
          read: function () {
            const original = self.originalImageDimensions();
            const shouldReturnDefaultValue =
              !area || !ko.isObservable(area.width) || !original || original.width === 0;

            if (shouldReturnDefaultValue) {
              return 50;
            }

            console.log("тест-первого-клика ko.unwrap(area.width)", ko.unwrap(area.width));
            if (ko.unwrap(area.width) >= 1) {
              console.log("тест-первого-клика original.width!", original.width);
              return original.width;
            }

            return Math.round(ko.unwrap(area.width) * original.width);
          },
          write: function (value) {
            const original = self.originalImageDimensions();
            const shouldReturnDefaultValue =
              !area || !ko.isObservable(area.width) || !original || original.width === 0;

            if (shouldReturnDefaultValue) {
              return;
            }

            const numValue = parseInt(value);
            if (!isNaN(numValue) && numValue >= 1) {
              const maxWidth = Math.min(numValue, original.width);

              const currentX = ko.unwrap(area.x) || 0;
              const currentXPixels = currentX * original.width;
              const newWidthPixels = maxWidth;

              // Проверяем, выходит ли (текущая позиция + новая ширина) за границы
              if (currentXPixels + newWidthPixels > original.width) {
                // Корректируем позицию x, чтобы область оставалась в пределах границ
                const newXPixels = Math.max(0, original.width - newWidthPixels);
                area.x(newXPixels / original.width);
              }

              area.width(maxWidth / original.width);
            } else if (value === "" || value === null) {
              area.width(50 / original.width);
            }
          },
        })
        .extend({ notify: "always" });

      area.pixelHeight = ko
        .computed({
          read: function () {
            const original = self.originalImageDimensions();
            if (!area || !ko.isObservable(area.height) || !original || original.height === 0) {
              return 50;
            }

            if (ko.unwrap(area.height) >= 1) {
              return original.height;
            }

            return Math.round(ko.unwrap(area.height) * original.height);
          },
          write: function (value) {
            const original = self.originalImageDimensions();
            if (!area || !ko.isObservable(area.height) || !original || original.height <= 0) {
              return;
            }
            const numValue = parseInt(value);
            if (!isNaN(numValue) && numValue >= 1) {
              const maxHeight = Math.min(numValue, original.height);

              const currentY = ko.unwrap(area.y) || 0;
              const currentYPixels = currentY * original.height;
              const newHeightPixels = maxHeight;

              // Проверяем, выходит ли (текущая позиция + новая высота) за границы
              if (currentYPixels + newHeightPixels > original.height) {
                // Корректируем позицию y, чтобы область оставалась в пределах границ
                const newYPixels = Math.max(0, original.height - newHeightPixels);
                area.y(newYPixels / original.height);
              }

              area.height(maxHeight / original.height);
            } else if (value === "" || value === null) {
              area.height(50 / original.height);
            }
          },
        })
        .extend({ notify: "always" });

      // Ensure pixelWidth and pixelHeight are defined before creating displayName
      if (!area.pixelWidth) area.pixelWidth = ko.observable(50);
      if (!area.pixelHeight) area.pixelHeight = ko.observable(50);

      area.displayName = ko.computed(() => {
        const name = ko.unwrap(area.name);
        if (name && name.trim() !== '') {
          return name;
        }
        const areaIndex = this.areas().indexOf(area) + 1;
        return `Область ${areaIndex}`;
      });

      return area;
    };

    // Инициализируем начальные области, которые передаются в диалог
    const initialAreas = (params.areas || [])
      .map((areaData) => {
        try {
          const newArea = {
            id: areaData.id || this.generateAreaId(),
            name: ko.isObservable(areaData.name)
              ? areaData.name
              : ko.observable(areaData.name || ''),
            x: ko.isObservable(areaData.x) ? areaData.x : ko.observable(areaData.x || 0.1),
            y: ko.isObservable(areaData.y) ? areaData.y : ko.observable(areaData.y || 0.1),
            width: ko.isObservable(areaData.width)
              ? areaData.width
              : ko.observable(areaData.width || 0.2),
            height: ko.isObservable(areaData.height)
              ? areaData.height
              : ko.observable(areaData.height || 0.2),
            isSelected: ko.isObservable(areaData.isSelected)
              ? areaData.isSelected
              : ko.observable(areaData.isSelected || false),
            clickCount: ko.isObservable(areaData.clickCount)
              ? areaData.clickCount
              : ko.observable(areaData.clickCount || 0),
          };
          return this.augmentAreaWithPixelComputeds(newArea);
        } catch (e) {
          console.error("Error creating area:", e, areaData);
          return null;
        }
      })
      .filter((area) => area !== null);

    // Сохраняем исходное состояние областей (на момент открытия диалога)
    this.areas = ko.observableArray(initialAreas);
    this.originalAreasJSON = JSON.stringify(ko.toJS(this.areas()));

    // loadImageDimensions is now called conditionally in constructor
    // If dimensions were passed and valid, this.originalImageDimensions is already set.

    this.imageScalePercent = ko.computed(() => {
      const original = this.originalImageDimensions();
      const displayed = this.childDisplayedImageDimensions();
      if (original && original.width > 0 && displayed && displayed.width > 0) {
        return Math.round((displayed.width / original.width) * 100);
      }
      return 0; // Default scale if dimensions are not available
    });
  }

  /**
   * Загружает размеры изображения
   * Создает новый объект Image и устанавливает обработчик onload
   * для получения полного размера изображения
   */
  loadImageDimensions() {
    if (!this.imageUrl()) return;

    const img = new Image();
    img.onload = () => {
      this.originalImageDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };
    img.src = ko.unwrap(this.imageUrl);
  }

  /**
   * Настраивает управление областями
   * Подписывается на изменения URL изображения и обновляет размеры при необходимости
   */
  setupAreaManagement() {
    // Следим за изменениями URL изображения
    if (ko.isObservable(this.imageUrl)) {
      this.imageUrl.subscribe(() => {
        this.loadImageDimensions();
      });
    }
  }

  /**
   * Обработчик изменения областей
   * Синхронизирует состояние областей и выделения между родительским и дочерним компонентами
   */
  onAreasChange() {
    this.areas.valueHasMutated();
    const currentSelection = this.selectedArea();
    if (currentSelection && this.areas.indexOf(currentSelection) === -1) {
      const stillExistsById = this.areas().find(
        (a) => a.id && currentSelection.id && ko.unwrap(a.id) === ko.unwrap(currentSelection.id)
      );
      if (stillExistsById) {
        this.selectedArea(stillExistsById);
      } else {
        this.selectedArea(this.areas().length > 0 ? this.areas()[0] : null);
      }
    }
  }

  /**
   * Добавляет новую область
   * Создает новую область с параметрами по умолчанию и добавляет ее в массив областей
   */
  addArea() {
    const newAreaRaw = {
      id: this.generateAreaId(),
      name: ko.observable(''),
      x: ko.observable(0.1),
      y: ko.observable(0.1),
      width: ko.observable(0.2),
      height: ko.observable(0.2),
      isSelected: ko.observable(false),
      clickCount: ko.observable(0),
    };
    const newArea = this.augmentAreaWithPixelComputeds(newAreaRaw);
    this.areas.push(newArea);
    this.selectedArea(newArea);
  }

  /**
   * Удаляет указанную область
   * @param {Object} area - Область для удаления
   */
  removeArea(area) {
    this.areas.remove(area);
    if (this.selectedArea() === area) {
      this.selectedArea(null);
    }
  }

  /**
   * Удаляет текущую выбранную область
   */
  removeSelectedArea() {
    if (this.selectedArea()) {
      this.removeArea(this.selectedArea());
    }
  }

  /**
   * Получает ширину области в пикселях
   * @param {Object} area - Область
   * @returns {number} Ширина в пикселях
   */
  getPixelWidth(area) {
    const original = this.originalImageDimensions();
    if (original.width === 0) return 50; // Значение по умолчанию
    return Math.round((ko.unwrap(area.width) || 0) * original.width);
  }

  /**
   * Получает высоту области в пикселях
   * @param {Object} area - Область
   * @returns {number} Высота в пикселях
   */
  getPixelHeight(area) {
    const original = this.originalImageDimensions();
    if (original.height === 0) return 50; // Значение по умолчанию
    return Math.round((ko.unwrap(area.height) || 0) * original.height);
  }

  /**
   * Устанавливает ширину области в пикселях
   * @param {Object} area - Область
   * @param {number} pixelWidth - Ширина в пикселях
   */
  setPixelWidth(area, pixelWidth) {
    const original = this.originalImageDimensions();
    if (original.width > 0) {
      area.width(pixelWidth / original.width);
    }
  }

  /**
   * Устанавливает высоту области в пикселях
   * @param {Object} area - Область
   * @param {number} pixelHeight - Высота в пикселях
   */
  setPixelHeight(area, pixelHeight) {
    const original = this.originalImageDimensions();
    if (original.height > 0) {
      area.height(pixelHeight / original.height);
    }
  }

  /**
   * Генерирует уникальный идентификатор для области
   * @returns {string} Уникальный идентификатор
   */
  generateAreaId() {
    return "area_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Получает информацию об области для отображения
   * @param {Object} area - Область
   * @returns {string} Строка с информацией о позиции и размере области
   */
  getAreaDisplayInfo(area) {
    const x = Math.round((ko.unwrap(area.x) || 0) * 100);
    const y = Math.round((ko.unwrap(area.y) || 0) * 100);
    const width = Math.round((ko.unwrap(area.width) || 0) * 100);
    const height = Math.round((ko.unwrap(area.height) || 0) * 100);

    return `${x}%, ${y}% (${width}% × ${height}%)`;
  }

  /**
   * Выбирает указанную область
   * @param {Object} areaToSelect - Область для выбора
   */
  selectArea(areaToSelect) {
    if (this.selectedArea() !== areaToSelect) {
      this.selectedArea(areaToSelect);
    }
  }

  /**
   * Сохраняет области
   * Вызывает функцию обратного вызова onSave и закрывает диалог
   */
  saveAreas() {
    if (typeof this.onSave === "function") {
      const areasData = ko.toJS(this.areas);
      this.onSave(areasData);
    }
    this.originalAreasJSON = JSON.stringify(ko.toJS(this.areas()));
    this.hide();
  }

  /**
   * Отменяет изменения
   * Восстанавливает области из исходного состояния и закрывает диалог
   */
  cancelChanges() {
    const restoredAreasRaw = JSON.parse(this.originalAreasJSON);
    this.areas(
      restoredAreasRaw.map((areaData) => {
        const newArea = {
          id: areaData.id,
          name: ko.observable(areaData.name),
          x: ko.observable(areaData.x),
          y: ko.observable(areaData.y),
          width: ko.observable(areaData.width),
          height: ko.observable(areaData.height),
          isSelected: ko.observable(areaData.isSelected || false),
          clickCount: ko.observable(areaData.clickCount || 0),
        };
        return this.augmentAreaWithPixelComputeds(newArea);
      })
    );
    const currentSelectedId = this.selectedArea() ? ko.unwrap(this.selectedArea().id) : null;
    const newSelection =
      this.areas().find((a) => ko.unwrap(a.id) === currentSelectedId) ||
      (this.areas().length > 0 ? this.areas()[0] : null);
    this.selectedArea(newSelection);
    this.hide();
  }

  /**
   * Очищает все области
   * Удаляет все области и сбрасывает выделение
   */
  clearAllAreas() {
    this.areas.removeAll();
    this.selectedArea(null);
  }
}
