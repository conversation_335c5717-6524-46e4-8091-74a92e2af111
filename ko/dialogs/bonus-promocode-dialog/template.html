<foquz-dialog params="ref: modal, dialogWrapper: $component">

  <foquz-dialog-header params="empty: success">
    <!-- ko text: $parent.success() ? '' : 'Ввести промокод' -->
    <!-- /ko -->
  </foquz-dialog-header>

  <div class="foquz-dialog__body" data-bind="descendantsComplete: function() {
    onRender();
  }">
    <!-- ko ifnot: success -->
    <fc-input
              params="ref: input, value: promocode, invalid: formControlErrorStateMatcher(promocode), maxlength: 50, placeholder: 'Промокод'"
              data-bind="event: {
                enter: function() { submit() }
              }">
    </fc-input>
    <fc-error params="show: formControlErrorStateMatcher(promocode), text: promocode.error"></fc-error>
    <!-- /ko -->

    <!-- ko if: success -->
    <div>Промокод применён успешно.</div>
    <div data-bind="html: successText"></div>
    <!-- /ko -->
  </div>


  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <!-- ko ifnot: success -->
      <fc-button params="inverse: true, color: 'primary', click: function() { hide() }, label: 'Отменить'"></fc-button>
      <fc-button params="color: 'primary', click: function() { submit() }, label: 'Применить'">
      </fc-button>
      <!-- /ko -->

      <!-- ko if: success -->
      <fc-button params="mode: 'text', color: 'primary', click: function() { hide() }, label: 'Закрыть'"></fc-button>
      <!-- /ko -->
    </div>
  </div>


</foquz-dialog>
