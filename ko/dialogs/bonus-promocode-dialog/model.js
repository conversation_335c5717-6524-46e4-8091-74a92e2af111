import { DialogWrapper } from 'Dialogs/wrapper';
import { declOfNum } from 'Utils/string/decl-of-num';
import { ApiUrl } from 'Utils/url/api-url';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.input = ko.observable(null);

    this.pending = ko.observable(false);
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.serverError = ko.observable('');

    this.promocode = ko.observable('').extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: {
        validator: () => false,
        onlyIf: () => this.serverError(),
        message: () => this.serverError()
      }
    });

    this.promocode.subscribe((v) => this.serverError(''));

    this.success = ko.observable(false);
    this.bonuses = ko.observable(0);
    this.successText = ko.pureComputed(() => {
      if (!this.success()) return '';
      let bonuses = this.bonuses();

      return declOfNum(bonuses, [
        `Добавлен <span class="f-color-success font-weight-500">${bonuses}</span> бонусный ответ.`,
        `Добавлено <span class="f-color-success font-weight-500">${bonuses}</span> бонусных ответа.`,
        `Добавлено <span class="f-color-success font-weight-500">${bonuses}</span> бонусных ответов.`
      ]);
    });
  }

  submit() {
    this.isSubmitted(true);

    if (!this.promocode.isValid()) return;

    this.pending(true);

    $.ajax({
      url: ApiUrl('bonus-accounts/create', {
        promoCode: this.promocode().trim()
      }),
      success: (response) => {
        this.pending(false);
        if (response.success) {
          let model = response.model;


          this.bonuses(model.bonuses);
          this.success(true);
          this.isSubmitted(false);

          this.emitEvent('add.promocode', response.model);
        } else {
          let errors = response.errors;
          if (errors) {
            this.serverError(errors.promoCode || errors.coupon_id);
          }
        }

      },
      error: (response) => {
        this.pending(false);
        let errors = response.responseJSON.errors;

        if (errors) {
          this.serverError(errors.promoCode || errors.coupon_id);
        } else {
          console.error(response.responseJSON);
        }
      }
    });
  }

  onRender() {
    setTimeout(() => {
      this.input().focus();
    });
  }
}
