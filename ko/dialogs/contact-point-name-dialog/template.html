<foquz-dialog class="foquz-dialog--contact-point-name" params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>Сохранить вопрос как точку контакта</foquz-dialog-header>

  <div class="foquz-dialog__body">
    <label
      class="form-label"
      data-bind="text: 'Название'"
    ></label>
    <fc-input
      params="
        value: name,
        placeholder: 'Введите название точки контакта',
        counter: true,
        maxlength: 50,
      "
      class="fc-input fc-input--counter"
    ></fc-input>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="
          click: function() {
            cancelNameEdit();
          }"
      >
        Отменить
      </button>
      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="
          click: function() {
            saveName();
          },
          attr: {
            disabled: name() == ''
          },
        "
      >
        Сохранить
      </button>
    </div>
  </div>
</foquz-dialog>
