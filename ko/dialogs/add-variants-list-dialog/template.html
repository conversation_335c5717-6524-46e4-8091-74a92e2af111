<foquz-dialog
  class="foquz-dialog--add-variants"
  params="
    ref: modal,
    dialogWrapper: $component,
  "
>
  <foquz-dialog-header>
    <!-- ko text: $parent.headerText --><!-- /ko -->
  </foquz-dialog-header>
  <div class="foquz-dialog__body">
    <!-- ko ifnot: mode === 'dictionary' || hideInputTypeSwitcher || questionMode === 'cpoint' -->
    <!-- ko if: hasDictionary -->
    <div class="form-group">
      <radio-group
        class="poll-question-form__source"
        params="
          options: [
            {
              value: 'text',
              label: 'Ввод списка вручную',
            },
            {
              value: 'dictionary',
              label: 'Выбор из справочника',
            },
          ],
          value: inputType,
        "
      ></radio-group>
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <!-- ko if: (mode === 'dictionary' || inputType() === 'dictionary') && !dictionaryLoaded() -->
    <fc-spinner class="f-color-primary"></fc-spinner>
    <!-- /ko -->
    <!-- ko if: (mode === 'dictionary' || inputType() === 'dictionary') && dictionaryLoaded -->
    <!-- ko if: isDictionaryEmpty() -->
    <p>
      Указанный в настройках опроса справочник «<!-- ko text: questionController.dictionary().name --><!-- /ko -->» пуст. Нужно <a href="/foquz/settings?tab=settings&setting=collections" target="_blank">добавить значения в справочник</a> или выбрать другой справочник в <a href="/foquz/foquz-poll/settings?id=${POLL.id}" target="_blank">настройках опроса</a>
    </p>
    <!-- /ko -->
    <!-- ko ifnot: isDictionaryEmpty() -->
    <p class="mb-25p">
      Используется справочник «<a href="/foquz/settings?tab=settings&setting=collections" target="_blank" data-bind="text: questionController.dictionary().name"></a>»
    </p>
    <div data-bind="nativeScrollbar" style="max-height: 517px">
      <fc-checked-tree class="mb-4" params="tree: checkedTree"></fc-checked-tree>
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <!-- ko ifnot: mode === 'dictionary' || inputType() === 'dictionary' -->
    <label class="form-label" data-bind="html: subHeaderText"></label>
    <textarea
      data-bind="
        autosizeTextarea,
        minHeight: 196,
        maxHeight: 479,
        textInput: newDetails,
        attr: {
          placeholder: '',
        },
      "
      class="form-control"
    ></textarea>
    <!-- ko ifnot: disableReplaceDetails -->
    <fc-check
      params="
        type: 'checkbox',
        checked: replaceDetails,
        label: 'Заменить текущий список вариантов ответов',
      "
      class="fc-check fc-check--checkbox"
    ></fc-check>
    <!-- /ko -->
    <!-- /ko -->
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="
          click: function() {
            cancelAddVariants();
          }"
      >
        Отменить
      </button>
      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="
          click: function() {
            confirmAddVariants();
          },
          attr: {
            disabled: newDetails() == ''
          },
        "
      >
        Добавить
      </button>
    </div>
  </div>
</foquz-dialog>
