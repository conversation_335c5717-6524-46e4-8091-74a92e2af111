import { get as _get } from "lodash";
import { DialogWrapper } from "Dialogs/wrapper";
import { DialogsModule } from "@/utils/dialogs-module";
import { FvmCheckedTree } from "@/presentation/viewModels/fvm-checked-tree";


export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.hasAnswers = params.hasAnswers;
    this.newDetails = ko.observable('');
    this.replaceDetails = ko.observable(false);
    this.headerText = params.headerText || 'Добавление вариантов ответов списком';
    this.subHeaderText = params.subHeaderText || 'Каждый вариант ответа в отдельной строке';
    this.disableReplaceDetails = params.disableReplaceDetails;
    this.mode = params.mode;
    this.questionController = params.questionController;
    this.inputType = ko.observable('text');
    this.checked = params.checked;
    this.isDictionaryEmpty = ko.observable(false);
    this.checkedTree = null;
    this.dictionaryLoaded = ko.observable(ko.isObservable(this.questionController.dictionaryLoaded) ? this.questionController.dictionaryLoaded() : this.questionController.dictionaryLoaded);
    this.hideInputTypeSwitcher = params.hideInputTypeSwitcher;
    this.questionMode = params.questionMode;
    this.hasDictionary = params.hasDictionary;

    if (this.mode === 'dictionary') {
      this.prepareCheckedTree();
    }

    this.inputType.subscribe(v => {
      if (v === 'dictionary' && !this.checkedTree) {
        this.prepareCheckedTree();
      }
    });

    DialogsModule(this);
  }

  async prepareCheckedTree() {
    if (!this.dictionaryLoaded()) {
      await this.questionController.loadDictionary();
    }
    this.isDictionaryEmpty(false);
    if (!_get(this.questionController.dictionary(), 'elements.length')) {
      this.isDictionaryEmpty(true);
      return;
    }
    const tree = this.questionController.dictionary().elements;
    this.checkedTree = new FvmCheckedTree(
      tree.map(el => {
        const prepareItem = (item) => {
          return {
            id: `${item.id}`,
            category: item.type === 'category',
            data: {
              name: item.title,
              isCategory: item.type === 'category',
              category: item.type === 'category',
            },
            children: (item.elements || []).map(child => prepareItem(child)),
          };
        };
        return prepareItem(el);
      })
    );
    if (this.checked) {
      this.checkedTree.setValue(this.checked);
    }
    this.checkedTree.value.subscribe(v => {
      this.newDetails(v);
    });
    this.dictionaryLoaded(true);
  }

  confirmAddVariants(confirmed) {
    if (this.hasAnswers && this.replaceDetails() && !confirmed) {
      this.confirm({
        title: 'Обновление списка',
        text: 'По этому вопросу уже собирается статистика. При обновлении списка варианты, которых нет в списке, будут удалены без возможности восстановления.',
        confirm: 'Обновить',
      }).then(() => {
        this.confirmAddVariants(true);
      });
      return;
    }

    const newDetails = this.newDetails();
    const replaceDetails = this.replaceDetails() ? 1 : 0;
    this.emitEvent(
      'submit',
      {
        newDetails,
        replaceDetails,
        inputType: this.inputType(),
      });
    this.hide();
  }

  cancelAddVariants() {
    this.hide();
  }
}
