.question-type-dialog {
  .foquz-dialog__container {
    width: 600px;
  }
  .inter-screens {
    border-bottom: 1px solid #e7ebed;
    margin-bottom: 15px;
  }
  .type-item {
    vertical-align: top;
    min-height: 26px;
    padding-top: 7.5px;
    padding-bottom: 7.5px;
    padding-left: 10px;
    padding-right: 10px;
    display: flex;
    break-inside: avoid;
    border-radius: 4px;
    cursor: pointer;


    &:not(.selected):not(.disabled):hover {
      background: #f1f5f6;
    }

    &__content {
      min-height: 24px;
      padding-top: 4px;
      display: flex;
      flex-direction: column;
    }

    &__icon {
      color: var(--f-color-primary);
      margin-right: 15px;
    }

    &__description {
      font-size: 12px;
      color: var(--f-color-service);
      margin-top: 4px;
    }

    &.selected {
      cursor: default;
      .type-item__icon {
        color: #A6B1BC;
      }
    }

    &.disabled {
      opacity: 0.5;
      cursor: default;
    }
  }
  .columns {
    column-count: 2;
    column-gap: 30px;
  }

  .type-item[data-type="media-variants"] {
    break-after: column;
  }
}
