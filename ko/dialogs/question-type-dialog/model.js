import { DialogWrapper } from "Dialogs/wrapper";
import * as types from "Data/question-types";
import { Translator } from "@/utils/translate";
import { getTypeName } from "Data/question-types";
import "./no-filials-dialog";
import "./no-collections-dialog";
import {
  CLASSIFIER_QUESTION,
  FILIALS_QUESTION,
  RATE_QUESTION,
} from "../../data/question-types";
import { DialogsModule } from "@/utils/dialogs-module";

const QuestionTranslator = Translator("question");

const descriptions = {
  nps: "Стандартный, Рейтинг для вариантов",
  scale: "Стандартная, шкала для вариантов",
};

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);

    const {
      onSelect,
      selected,
      disableStartScreen,
      disableFilials,
      disableClassifier,
    } = params;

    this.types = types.typesSet.map((type) => ({
      id: type.id,
      type: getTypeName(type.id),
      description: descriptions[getTypeName(type.id)],
      text: type.label,
      disabled:
        (type.id === FILIALS_QUESTION && disableFilials) ||
        (type.id === CLASSIFIER_QUESTION && disableClassifier),
      hidden: selected != +RATE_QUESTION && type.id === RATE_QUESTION,
    }));

    this.disableFilials = disableFilials;
    this.disableClassifier = disableClassifier;

    this.startScreen = {
      id: "start",
      type: "start",
      disabled: disableStartScreen,
      text: QuestionTranslator.t("Стартовый экран"),
    };
    this.endScreen = {
      id: "end",
      type: "end",
      text: QuestionTranslator.t("Конечный экран"),
    };
    this.fiveSecondTest = {
      id: "five-second-test",
      type: "five-second-test",
      text: QuestionTranslator.t("Тест 5 секунд"),
    };
    this.options = [this.startScreen, this.endScreen, ...this.types, this.fiveSecondTest];

    this.selected = selected;
    this.onSelect = onSelect;
  }

  openNoFilialsDialog() {
    this.openDialog({
      name: "no-filials-dialog",
    });
  }

  openNoCollectionsDialog() {
    this.openDialog({
      name: "no-collections-dialog",
    });
  }

  selectType(type) {
    if (type.id === FILIALS_QUESTION) {
      if (this.disableFilials) {
        this.openNoFilialsDialog();
        return;
      }
    }
    if (type.id === CLASSIFIER_QUESTION) {
      if (this.disableClassifier) {
        this.openNoCollectionsDialog();
        return;
      }
    }
    if (type.id === "start" && this.disableStartScreen) {
      return;
    }
    if (typeof this.onSelect === "function") this.onSelect(type.id);
    this.hide();
  }
}
