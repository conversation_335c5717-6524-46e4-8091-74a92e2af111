<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header> Тип вопроса </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="columns inter-screens">
      <div
        class="type-item"
        data-bind="click: function() { selectType(startScreen) }, css: {
          disabled: startScreen.disabled,
        }"
      >
        <span
          class="type-item__icon f-icon f-icon-question-type"
          data-bind="class: 'f-icon-question-type--start'"
        >
          <svg>
            <use
              data-bind="attr: {
                href: '#foquz-icon-question-start'
            }"
            />
          </svg>
        </span>
        Стартовый экран
      </div>
      <div
        class="type-item"
        data-bind="click: function() { selectType(endScreen) }"
      >
        <span
          class="type-item__icon f-icon f-icon-question-type"
          data-bind="class: 'f-icon-question-type--end'"
        >
          <svg>
            <use
              data-bind="attr: {
              href: '#foquz-icon-question-end'
          }"
            />
          </svg>
        </span>
        Конечный экран
      </div>
    </div>

    <div class="columns question-types">
      <!-- ko foreach: { data: types, as: 'item' } -->
      <div
        class="type-item"
        data-bind="click: function() { $parent.selectType(item) }, css: {
          disabled: item.disabled,
          selected: $parent.selected == item.id
        }, attr: {
          'data-type': item.type
        }, visible: !item.hidden"
      >
        <span
          class="type-item__icon f-icon f-icon-question-type"
          data-bind="class: 'f-icon-question-type--' + item.type"
        >
          <svg>
            <use
              data-bind="attr: {
                href: '#foquz-icon-question-' + item.type
            }"
            />
          </svg>
        </span>
        <div class="type-item__content">
          <div class="type-item__name" data-bind="text: item.text"></div>

          <!-- ko if: item.description -->
          <div
            class="type-item__description"
            data-bind="text: item.description"
          ></div>
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->
      <div
      class="type-item"
      data-bind="click: function() { selectType(fiveSecondTest) }"
    >
      <span
        class="type-item__icon f-icon f-icon-question-type"
        data-bind="class: 'f-icon-question-type--five-second-test'"
      >
        <svg>
          <use
            data-bind="attr: {
            href: '#foquz-icon-question-five-second-test'
        }"
          />
        </svg>
        </span>
        Тест 5 секунд
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link px-2"
        data-bind="
                click: function() {
                  hide();
                }"
      >
        Закрыть
      </button>
    </div>
  </div>
</foquz-dialog>
