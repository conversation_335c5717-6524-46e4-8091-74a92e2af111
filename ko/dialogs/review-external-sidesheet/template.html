<sidesheet params="ref: modal, dialogWrapper: $component">
  <!-- ko if: initializing() || loading() -->
  <spinner></spinner>
  <!-- /ko -->

  <!-- ko if: !initializing() && !loading() -->
  <div
    class="sidesheet__status-line"
    data-bind="css: 'processing-status--' + review.processingStatus()"
  ></div>

  <nav class="foquz-dialog__tabs nav nav-tabs review-details-modal-nav">
    <a
      class="nav-item nav-link nav-item--order active overflow-hidden"
      data-bind="attr: { id: 'nav-poll-tab-' + id, href: '#nav-poll-' + id, 'aria-controls': 'nav-poll-' + id }, event: {
        'show.bs.tab': function() {
          closeHistory();
          return true;
        }
        }"
      data-toggle="tab"
      role="tab"
      aria-selected="true"
    >
      <div class="d-flex d-md-none w-100">
        <!-- ko if: review.order && review.order.number -->
        <span
          class="review-details-modal-nav__order flex-grow-1 mt-1"
          data-bind="tooltip, text: '#' + review.order.number, tooltipText: _t('Номер заказа')"
        >
        </span>
        <!-- /ko -->
        <!-- ko ifnot: review.order && review.order.number -->
        <span
          class="review-details-modal-nav__order"
          data-bind="text: _t('answers', 'Анкета')"
        >
        </span>
        <!-- /ko -->

        <!-- ko if: review.complaint !== null -->
        <i
          class="review-details-modal-nav__complaint-indicator"
          data-bind="tooltip, tooltipText: _t('Есть жалоба')"
        ></i>
        <!-- /ko -->

        <!-- ko if: review.ratingQuestionsCount() > 0 -->
        <i
          class="review-details-modal-nav__rating-indicator"
          data-bind="
        css: 'review-details-modal-nav__rating-indicator--value_' + review.getQuestionsAverageRating(),
        attr: { title: review.getQuestionsAverageRatingLabel() }, tooltip"
        ></i>
        <!-- /ko -->
      </div>

      <div class="d-none d-md-flex overflow-hidden w-100">
        <div
          class="d-inline-flex flex-grow-1 align-items-center overflow-hidden"
        >
          <!-- ko if: review.isAuto -->
          <i
            class="review-details-modal-nav__icon icon icon-automatic"
            data-bind="tooltip, tooltipText: _t('Автоматический')"
            data-placement="top"
          ></i>

          <!-- /ko -->
          <!-- ko ifnot: review.isAuto -->
          <i
            class="review-details-modal-nav__icon icon icon-manual"
            data-bind="tooltip, tooltipText: _t('Ручной')"
            data-placement="top"
          ></i>
          <!-- /ko -->

          <div class="review-details-modal-nav__poll-name ml-2 mt-1">
            <span
              data-bind="text: review.pollName,
            attr: { title: review.pollName }, tooltip"
            ></span>
          </div>
        </div>
        <!-- ko if: review.order && review.order.number -->
        <span
          class="review-details-modal-nav__order mt-1"
          data-bind="tooltip,  tooltipText: _t('Номер заказа'), text: '#' + review.order.number"
        >
        </span>
        <!-- /ko -->
        <!-- ko if: review.complaint !== null -->
        <i
          class="review-details-modal-nav__complaint-indicator d-inline-block"
          data-bind="tooltip,  tooltipText: _t('Есть жалоба')"
        ></i>
        <!-- /ko -->
        <!-- ko if: review.ratingQuestionsCount() > 0 -->
        <i
          class="review-details-modal-nav__rating-indicator d-inline-block"
          data-bind="
          css: 'review-details-modal-nav__rating-indicator--value_' + review.getQuestionsAverageRating(),
          attr: { title: review.getQuestionsAverageRatingLabel() }, tooltip"
        ></i>
        <!-- /ko -->
      </div>
    </a>
  </nav>

  <div class="tab-content review-details-modal__tab-content foquz-dialog__body">
    <div
      class="tab-pane show active h-100"
      data-bind="attr: {
        id: 'nav-poll-' + id,
        'aria-labelledby': 'nav-poll-tab-' + id
      }"
      role="tabpanel"
    >
      <div class="foquz-dialog__scroll pt-4" data-bind="nativeScrollbar">
        <div class="container">
          <review-external-data
            params="review: review, hideClientData: hideClientData"
          ></review-external-data>
        </div>

        <div class="review-details__questions review-questions">
          <div class="container">
            <!-- ko if: review.withPoints() && review.processingId -->
            <div
              class="f-color-text mb-4 review-details__points"
              data-bind="html: getReviewPointsHtml(review)"
            ></div>
            <!-- /ko -->
            <div class="review-questions__header">
              <div class="review-questions__title">
                <!-- ko text: _t('Ответы') -->
                <!-- /ko -->
                <span
                  class="review-questions__answers-count text-nowrap"
                  data-bind="text: _t('main', '{num1} из {num2}', {
                  num1: review.questionsWithAnswer(),
                  num2: review.questions().length
                })"
                >
                </span>
                <!-- ko let: { randomSum: (review.randomOrder ? 1 : 0) + (review.pagesRandomOrder ? 2 : 0), randomTexts: {
                  1: _t('answers', 'Включен случайный порядок страниц'),
                  2: _t('answers', 'Для страниц включен случайный порядок вопросов'),
                  3: _t('answers', 'Включен случайный порядок страниц, для страниц включен случайный порядок вопросов')
                } } -->
                <!-- ko if: randomSum > 0 -->

                <span
                  class="d-inline-block pl-5p"
                  style="vertical-align: top; font-size: 0"
                  data-bind="tooltip,
                tooltipText: randomTexts[randomSum]"
                >
                  <svg-icon
                    params="name: 'shuffle'"
                    class="f-color-light svg-icon--sm"
                  ></svg-icon>
                </span>
                <!-- /ko -->
                <!-- /ko -->
              </div>
              <!-- ko if: review.link -->
              <fc-button
                params="
                  label: 'Прохождение анкеты',
                  mode: 'text',
                  color: 'primary',
                  linkMode: true,
                  linkAttrs: {
                    href: review.link,
                    target: '_blank',
                  },
                "
              ></fc-button>
              <!-- /ko -->
              <div class="spacer"></div>
            </div>
            <review-external-questions params="review: review"></review-external-questions>
          </div>
        </div>
        <!-- ko if: review.complaint !== null -->
        <div class="container">
          <div class="review-details-modal__complaint review-complaint">
            <span
              class="review-complaint__title"
              data-bind="text: _t('Жалоба')"
            ></span>
            <!-- ko if: review.complaint.text !== null -->
            <span
              class="review-complaint__text"
              data-bind="text: review.complaint.text"
            ></span>
            <!-- /ko -->
            <!-- ko if: review.complaint.photoUrls.length > 0 -->
            <div class="review-complaint__photos">
              <!-- ko foreach: review.complaint.photoUrls -->
              <img
                class="review-complaint__photo"
                data-bind="
                                attr: { src: $data },

                                fancybox: {
                                  urls: $parent.review.complaint.photoUrls,
                                  caption: $parent.getComplaintFancyboxCaption.bind($parent),
                                  index: $index()
                                }
                            "
              />
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
        </div>
        <!-- /ko -->
      </div>
    </div>
    <div
      class="tab-pane pt-20p pt-md-30p h-100"
      data-bind="attr: {
        id: 'nav-history-' + id,
        'aria-labelledby': 'nav-history-tab-' + id
      }"
      role="tabpanel"
    >
      <foquz-client-history
        params="ref: history"
        data-bind="event: {
            reviewClick: function(_, e, reviewId) { openReview(reviewId) }
          }"
      ></foquz-client-history>
    </div>
  </div>
  <!-- /ko -->
</sidesheet>

<template id="statistics-details-modal-dialog-slide-template">
  <!-- ko template: { afterRender: onInit } -->
  <!-- ko template: { nodes: $componentTemplateNodes } -->
  <!-- /ko -->

  <!-- ko if: $parent.file_text -->
  <span
    class="statistics__details-modal-dialog-slider-index"
    data-bind="text: $parent.file_text"
  ></span>
  <!-- /ko -->

  <div class="statistics__details-modal-dialog-slider-paginator">
    <button
      class="statistics__details-modal-dialog-slider-paginator-button statistics__details-modal-dialog-slider-paginator-button--prev"
      type="button"
    ></button>

    <button
      class="statistics__details-modal-dialog-slider-paginator-button statistics__details-modal-dialog-slider-paginator-button--next"
      type="button"
    ></button>

    <div class="statistics__details-modal-dialog-slider-counter">
      <!-- ko text: index + 1 -->
      <!-- /ko -->/
      <!-- ko text: count -->
      <!-- /ko -->
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="statistics-details-modal-dialog-slider-template">
  <!-- ko template: { afterRender: onInit } -->
  <div
    class="swiper-wrapper"
    data-bind="descendantsComplete: $component.onChildrenInit.bind($component)"
  >
    <!-- ko template: { nodes: $componentTemplateNodes } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
</template>
