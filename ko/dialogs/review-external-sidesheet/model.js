import { get as _get } from "lodash";

import { DialogsModule } from "@/utils/dialogs-module";
import { DialogWrapper } from "Dialogs/wrapper";
import { Review } from "Models/review";
import { ApiUrl } from "Utils/url/api-url";

let nextId = 0;

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    DialogsModule(this);

    this.initializing = ko.observable(true);
    this.loading = ko.observable(true);

    this.blocked = true; // ToDo

    this.id = nextId++;
    this.showSaveMessage = ko.observable(false);

    this.review = null;
    this.executorMode = params.executorMode;
    this.hideClientData = params.hideClientData;
    this.enableActions = params.enableActions;

    this.history = ko.observable(null);
    this.historyOpened = ko.observable(false);

    this.processingForm = ko.observable(null);

    this.answers = params.answers;
    this.reviewId = params.reviewId;
    this.statsLinkId = _get(window, 'POLL.statsLink.link', '').replace(/.*stats\/(\w*)/, '$1');

    this.loadReview(params.reviewId).then((review) => {
      this.review = review;
      console.log('FoquzComponent DialogWrapper', this.review)
      this.review.isRequestProcessingEnabled =
        params.isRequestProcessingEnabled;
      this.review.answerId = params.pollId;

      this.init();
      this.loading(false);
    });
  }

  getReviewPointsHtml(review) {
    return `<span class="f-color-service">${_t("Набрано баллов")}: </span>
    ${_t("main", "{num1} из {num2}", {
      num1: `<span class="bold">${review.points().answer_points}</span>`,
      num2: `<span class="bold">${review.points().points_max}</span>`,
    })},
    <span>${review.points().percent}</span>%`;
  }

  openReview(reviewId) {
    this.dialogRoot.add({
      name: "review-sidesheet",
      params: {
        hideClientData: this.hideClientData,
        reviewId,
        executorMode: this.executorMode,
      },
    });
  }

  openHistory() {
    this.historyOpened(true);
    this.history().load(this.review.contactId);
  }

  closeHistory() {
    this.historyOpened(false);
    this.history().reset();
  }

  onElementRender() {
    this.initializing(false);
  }

  loadReview(reviewId) {
    let review = Review.get(reviewId);
    if (review) {
      return Promise.resolve(review);
    }
    return new Promise((res) => {
      $.ajax({
        url: ApiUrl(
          "answers/view",
          {
            dropToken: true,
            id: reviewId,
            'link-key': this.statsLinkId,
          },
        ),
        success: (response) => {
          response.questions.forEach((el, index) => {
            // ToDo рефакторинг
            el.setVariants = _get(this, "answers[index].setVariants", false);
          });
          res(new Review(response));
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      });
    });
  }

  init() {
    this.getComplaintFancyboxCaption = function () {
      const passedAt = this.review.passedAt;
      const text = this.review.complaint.text;
      return (
        `<div class="review-complaint__fancybox-description">
                        <div class="review-complaint__fancybox-description-passed-at">${passedAt}</div>` +
        (text !== null
          ? `<div class="review-complaint__fancybox-description-text">${text}</div>`
          : "") +
        `</div>`
      );
    };
  }

  submit() {
    this.processingForm().submit();
  }

  onSubmit(data) {
    this.emitEvent("update", data);
    this.showSaveMessage(true);
    setTimeout(() => {
      this.showSaveMessage(false);
    }, 4000);
  }

  cancel() {
    this.showSaveMessage(false);
    this.processingForm().cancel();
  }

  async deleteReview() {
    this.confirm({
      title: "Удаление ответов",
      text: "Ответы будут удалены без возможности восстановления. <b>Изменится статистика опроса</b>.",
      confirm: "Удалить",
      mode: "danger",
    }).then(async () => {
      const res = await fetch(
        `/foquz/api/answers/delete?id=${this.reviewId}&access-token=${APIConfig.apiKey}`,
        { method: "DELETE" }
      );
      const data = await res.json();
      this.emitEvent("update", { action: "delete", reviewId: this.reviewId });
      this.hide();
    });
  }

  printReview() {
    let { href } = window.location;
    href = `/foquz/foquz-poll/print-answer?id=${this.reviewId}`;
    window.open(href, "_blank");    
  }
}
