@import 'Style/breakpoints';
@import 'Style/colors';

.review-sidesheet {
  .review-details-modal-nav:after {
    display: none;
  }
  .foquz-client-history {
  }

  .foquz-dialog__close {
    .mobile-and-tablet({
      background: #dfe3e5;
    });

  }

  .processing-form {
    padding: 0 !important;
  }

  .swiper-container {
    margin-left: -20px;
    margin-right: -20px;
  }

  .swiper-wrapper {
    padding-left: 20px;
  }

  .tab-inner {
    padding: 0 !important;
  }

  .swiper-container {
    margin-left: 0px;
    margin-right: 0px;
  }
  .swiper-wrapper {
    padding-left: 0px;
  }

  .review-questions__header {
    display: flex;
    align-items: flex-end;
    position: relative;
    z-index: 3;
    order: -1;
    margin-bottom: 10px;
    width: fit-content;

    .review-questions__title {
      display: none;
    }

    .fc-btn {
      span.fc-btn-b__label {
        font-size: 13px;
        font-weight: 500;
        line-height: 1.1;
      }
    }

    @media screen and (min-width: 768px) {
      order: unset;
      margin-bottom: unset;

      .review-questions__title {
        display: block;
        margin-right: 15px;
        line-height: 1.1;
      }
    }
  }

  .review-questins__title {
    pointer-events: all;
  }

  .mobile-and-tablet({
    .os-scrollbar {
      &-vertical {
        display: none;
      }
    }
  });

  .review-details__questions {
    .container {
      display: flex;
      flex-direction: column;
    }
  }

  .only-mobile({
    .review-details-modal__info {
      margin-left: -15px;
      margin-right: -15px;
    }

    .swiper-container {
      margin-left: -15px;
      margin-right: -15px;
    }
    .swiper-wrapper {
      padding-left: 15px;
    }

    .call-button {
      margin-left: -15px;
      margin-right: -15px;
      width: auto;
    }

    .review-question {
      margin-left: -15px;
      margin-right: -15px;
    }
  });
}

.review-details-modal .review-dish-rating-question__table-wrapper {
  .only-mobile({
    max-height: none;
  });
}
