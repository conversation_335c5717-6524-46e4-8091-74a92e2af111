@import 'Style/colors';
@import 'Style/breakpoints';

.ui-tooltip {
  display: none!important;
}

.review-data {
  .review-date {
    .svg-icon {
      flex-shrink: 0;
      margin-right: 8px;
      vertical-align: middle;
    }
    & > span {
      display: flex;
      align-items: center;
    }
  }
  .client-info {
    .badge {
      align-self: flex-start;
      margin-right: 12px;
    }
  }
  .review-notifications__list {
    line-height: 1;
  }
  .review-lang {
    font-size: 12px;
    font-weight: 500;
    color: #73808D;
  }
  .only-mobile({
    .review-date {
      flex-grow: 1;
      flex-basis: 25%;
      align-self: flex-start;

      .svg-icon {
        margin-bottom: 4px;
      }
      .date {
        display: flex;
        flex-direction: column;
      }
    }
    .review-date > span {
      flex-direction: column;
      align-items: flex-start;
    }
    .badge {
      margin-top: 2px;
    }

  });

  .review-details-modal__info-block--dates {
    padding-top: 10px;
    padding-bottom: 10px;

    .review-date {
      white-space: nowrap;
    }
  }
  .review-details-modal__info-block--notifications {
    &.compact {
      flex-grow: 0;
      min-width: 95px;
    }
  }
}
