import { ViewModel } from '../model';
import html from './template.html';
import './style.less';


import '@/presentation/views/fc-client-fields'

ko.components.register('review-external-data', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('review-data');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
