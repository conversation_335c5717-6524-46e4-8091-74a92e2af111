<template id="review-channel-dropdown-template">
  <!-- ko ifnot: answer.answerChannel -->
  <div style="height: 32px"></div>
  <!-- /ko -->
  <!-- ko if: answer.answerChannel -->
  <!-- ko let: {
    answerChannelType: answer.getChannelType(answer.answerChannel.channel_name),
  } -->

  <!-- ko ifnot: answerChannelType -->
  <div
    class="f-color-text d-flex align-items-center"
    style="min-height: 32px; line-height: 1;"
    data-bind="text: answer.answerChannel.channel_name"
  ></div>
  <!-- /ko -->

  <!-- ko if: answerChannelType -->
  <div class="dropdown">
    <div class="" data-toggle="dropdown">
      <span
        data-bind="
          tooltip, tooltipText: _t('Канал связи') + ': ' + answer.answerChannel.channel_name
        "
        class="d-inline-block"
      >
        <svg-icon
          params="name: 'channel-' + answerChannelType"
          data-bind="css: 'f-color-channel-' + answerChannelType"
          class="svg-icon--lg"
        ></svg-icon>
      </span>
    </div>

    <div
      class="dropdown-menu dropdown-menu-left mailings__details-modal-dialog-table-sending-list-dropdown-menu"
      style="margin-top: 10px"
      data-bind="css: {
      'dropdown-menu-left': !answer.clientName
    }, style: {

      'margin-left': answer.clientName ? '-20px' : '2px'
    }"
    >
      <div
        class="mailings__details-modal-dialog-table-sending-list-table-wrapper"
      >
        <div data-bind="fScrollbar">
          <table
            class="table foq-table mailings__details-modal-dialog-table-sending-list-table"
          >
            <thead>
              <tr>
                <th>
                  <!-- ko text: _t('Отправлена') -->
                  <!-- /ko -->
                </th>
                <th>
                  <!-- ko text: _t('Каналы') -->
                  <!-- /ko -->
                </th>
                <th
                  class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-repeats-head-cell"
                >
                  <!-- ko text: _t('Повторы') -->
                  <!-- /ko -->
                </th>
                <th
                  class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-response-head-cell"
                >
                  <!-- ko text: _t('Ответ') -->
                  <!-- /ko -->
                </th>
              </tr>
            </thead>

            <tbody>
              <!-- ko foreach: answer.getChannelsStats() -->
              <tr class="font-weight-normal">
                <td data-bind="text: sended"></td>
                <td>
                  <div class="d-flex align-items-center">
                    <span
                      class="f-icon f-icon-channel mr-2"
                      data-bind="css: 'f-icon-channel--' + type"
                    >
                      <svg>
                        <use
                          data-bind="attr: {
                        href: '#channel-icon-' + type
                      }"
                        ></use>
                      </svg>
                    </span>
                    <!-- ko text: name -->
                    <!-- /ko -->
                  </div>
                </td>
                <td
                  class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-repeats-cell"
                >
                  <div class="d-flex align-items-center justify-content-center">
                    <span
                      class="f-icon f-icon-channel f-icon-channel--repeats"
                      style="margin-right: 20px"
                    >
                      <svg>
                        <use href="#channel-icon-repeats"></use>
                      </svg>
                    </span>
                    <!-- ko text: repeats -->
                    <!-- /ko -->
                  </div>
                </td>
                <td
                  class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-response-cell"
                  data-bind="text: hasResponse ? 'есть' : '—'"
                ></td>
              </tr>
              <!-- /ko -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <!-- /ko -->

  <!-- /ko -->
  <!-- /ko -->
</template>

<!-- Название опроса -->
<div class="align-items-center d-flex d-md-none pb-15p">
  <div class="d-inline-flex align-items-center overflow-hidden">
    <!-- ko if: review.isAuto -->
    <i
      class="review-details-modal-nav__icon icon icon-automatic"
      data-bind="tooltip, tooltipText: _t('Автоматический')"
      data-placement="top"
    ></i>

    <!-- /ko -->
    <!-- ko ifnot: review.isAuto -->
    <i
      class="review-details-modal-nav__icon icon icon-manual"
      data-bind="tooltip, tooltipText: _t('Ручной')"
      data-placement="top"
    ></i>
    <!-- /ko -->

    <div class="review-details-modal-nav__poll-name ml-2">
      <span
        data-bind="text: review.pollName,
      attr: { title: review.pollName }, tooltip"
      ></span>
    </div>
  </div>
</div>


<div class="review-details-modal__info d-none d-md-flex">
  <!-- ko if: review.clientName -->
  

  <!-- Данные клиента -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--client" 
  >
    <div class="client-info">
      <!-- ko ifnot: hideClientData -->
      <div class="client-info__title d-flex aling-items-center">
        <span
          class="client-info__name"
          data-bind="text: review.client.name"
        ></span>
        <span
          class="ml-2 badge badge-active client-info__order-count"
          data-bind="tooltip, text: review.client.ordersCount, tooltipText: _t('answers', 'Количество заказов')"
        ></span>
      </div>

      <!-- ko if: review.client.phone || review.client.systemFields.email -->
      <div class="client-info__phone mt-2 f-fs-2">
        <!-- ko if: review.client.phone -->
        <span
          class="font-weight-700"
          data-bind="text: review.client.phone + (review.client.systemFields.email ? ',' : '')"
        >
        </span>
        <!-- /ko -->

        <!-- ko if: review.client.systemFields.email -->
        <span data-bind="text: review.client.systemFields.email"></span>
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- /ko -->

      <!-- ko if: review.filial -->
      <div class="service-text bold mt-2" data-bind="text: review.filial"></div>
      <!-- /ko -->

      <fc-client-fields class="mt-10p" params="client: review.client, hideClientData: hideClientData"></fc-client-fields>
    </div>
  </div>

  <!-- /Данные клиента -->


  <!-- /ko -->

  <!-- ko if: !review.isAuto && review.filial -->
  <!-- Данные клиента -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--client justify-content-start"
    
  >
    <div class="client-info">
      <div class="service-text bold" data-bind="text: review.filial"></div>
    </div>
  </div>
  <!-- /Данные клиента -->
  <!-- /ko -->

  <!-- ko if: review.client.name && review.client.phone -->
  <!-- ko ifnot: hideClientData -->
  <!-- Позвонить клиенту -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--client--call"
  >
    <a
      class="btn call-block"
      data-bind="attr: { href: 'tel:' + review.client.phone }, tooltip, tooltipText: _t('answers',  'Позвонить клиенту')"
    ></a>
  </div>
  <!-- /Позвонить клиенту -->
  <!-- /ko -->
  <!-- /ko -->

  <!-- Канал связи -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--notifications justify-content-start"
    data-bind="css: {
      'compact': hideClientData
    }"
  >
    <div class="review-notifications">
      <div class="review-notifications__list mt-0">
        <!-- ko template: {
          name: 'review-channel-dropdown-template',
          data: {
            answer: review
          }
        } -->
        <!-- /ko -->
        <!-- ko if: review.device -->
        <span
          data-bind="tooltip, tooltipText: _t('Устройство') + ': ' + review.deviceName"
          class="f-color-service ml-3"
        >
          <svg-icon
            params="name: 'viewport-' + review.device"
            class="svg-icon--lg"
          ></svg-icon>
        </span>
        <!-- /ko   -->
      </div>
    </div>
    <!-- ko if: review.langText -->
    <div
      class="review-lang mt-1"
      data-bind="text: review.langText"
    ></div>
    <!-- /ko -->
  </div>
  <!-- /Канал связи -->
  <!-- Даты -->
  <div
    class="review-details-modal__info-block review-details-modal__info-block--dates"
  >
    <div class="review-date">
      <span
        data-bind="tooltip, tooltipText: _t('answers', 'Дата создания опроса')"
      >
        <svg-icon
          params="name: 'review-created'"
          class="svg-icon--sm f-color-service"
        ></svg-icon>

        <span
          class="date"
          data-bind="html: formatDate(review.createdAt)"
        ></span>
      </span>
    </div>
    <div class="review-date">
      <span data-bind="tooltip, tooltipText: _t('answers', 'Дата прохождения опроса')">
        <svg-icon 
          params="name: 'review-passed'" 
          class="svg-icon--sm"
        ></svg-icon>
        <div>
          <span 
            class="date" 
            data-bind="html: formatDate(review.started)"
          ></span>
          <!-- ko if: review.finished.length === 5 -->
            - <span class="date" data-bind="text: review.finished"></span>
          <!-- /ko -->
          <!-- ko ifnot: review.finished.length === 5 -->
            <br><span class="date" data-bind="text: review.finished"></span>
          <!-- /ko -->
        </div>
      </span>
    </div>
    <div class="review-date">
      <span data-bind="tooltip, tooltipText: _t('answers', 'Дата обработки')">
        <svg-icon
          params="name: 'review-processed'"
          class="svg-icon--sm f-color-service"
        ></svg-icon>
        <span
          class="date"
          data-bind="html: formatDate(review.processingUpdatedAt)"
        ></span>
      </span>
    </div>
    
    <div class="review-date">
      <span data-bind="tooltip, tooltipText: review.tooltip">
        <svg-icon params="name: 'review-elapsed'" class="svg-icon--sm"></svg-icon>
        <span>
          <span data-bind="text: review.answerTime"></span>
          <!-- ko if: review.timeToPass -->
          <span
            class="font-weight-normal"
            data-bind="text: ' / ' + review.timeToPass"
          ></span>
          <!-- /ko -->
        </span>
      </span>
    </div>
    
  </div>
  <!-- /Даты -->
</div>


<div class="review-details-modal__info d-md-none">
  <div
    class="review-details-modal__info-block review-details-modal__info-block--full"
  >
    <div class="d-flex align-items-center">
      <div class="client-info flex-grow-1">
        <!-- ko ifnot: hideClientData -->
        <div class="client-info__title d-flex aling-items-center">
          <span
            class="client-info__name"
            data-bind="text: review.client.name"
          ></span>
          <!-- ko if: review.client.name || review.client.ordersCount > 0 -->
          <span
            class="ml-2 badge badge-active client-info__order-count"
            data-bind="tooltip, text: review.client.ordersCount, tooltipText: _t('answers', 'Количество заказов')"
          ></span>
          <!-- /ko -->
        </div>
        <!-- /ko -->
        <!-- ko if: review.filial -->
        <div
          class="service-text bold mt-2"
          data-bind="text: review.filial"
        ></div>
        <!-- /ko -->
      </div>
      <div>
        <div class="d-flex align-items-center align-self-start">
          <!-- ko template: {
              name: 'review-channel-dropdown-template',
              data: {
                answer: review
              }
            } -->
          <!-- /ko -->
          <!-- ko if: review.device -->
          <span
            data-bind="tooltip, tooltipText: _t('Устройство') + ': ' + review.deviceName"
            class="f-color-service ml-3"
          >
            <svg-icon
              params="name: 'viewport-' + review.device"
              class="svg-icon--lg"
            ></svg-icon>
          </span>
          <!-- /ko   -->
        </div>
        <!-- ko if: review.langText -->
        <div
          class="review-lang mt-1"
          data-bind="text: review.langText"
        ></div>
        <!-- /ko -->
      </div>
    </div>

    <!-- ko ifnot: hideClientData -->
    <!-- ko if: review.client.systemFields.email -->
    <div
      class="client-info__phone mt-1"
      data-bind="text: review.client.systemFields.email"
    ></div>
    <!-- /ko -->
    <!-- /ko -->

    <fc-client-fields
      class="mt-15p mb-15p"
      params="client: review.client, hideClientData: hideClientData"
    ></fc-client-fields>

    <div class="d-flex justify-content-between mt-0 review-dates">
      <div class="review-date">
        <span
          data-bind="tooltip, tooltipText: _t('answers', 'Дата создания опроса')"
          class="d-flex"
        >
          <svg-icon
            params="name: 'review-created'"
            class="svg-icon--sm"
          ></svg-icon>
          <span>
            <span
              class="date"
              data-bind="html: formatDate(review.createdAt)"
            ></span>
          </span>
        </span>
      </div>
      <div class="review-date">
        <span data-bind="tooltip, tooltipText: _t('answers', 'Дата прохождения опроса')">
          <svg-icon 
            params="name: 'review-passed'" 
            class="svg-icon--sm"
          ></svg-icon>
          <div>
            <span 
              class="date" 
              data-bind="html: formatDate(review.started)"
            ></span>
            <!-- ko if: review.finished.length === 5 -->
              -<span class="date" data-bind="text: review.finished"></span>
            <!-- /ko -->
            <!-- ko ifnot: review.finished.length === 5 -->
              <br><span class="date" data-bind="text: review.finished"></span>
            <!-- /ko -->
          </div>
        </span>
      </div>
      <div class="review-date">
        <span data-bind="tooltip, tooltipText: _t('answers', 'Дата обработки')">
          <svg-icon
            params="name: 'review-processed'"
            class="svg-icon--sm"
          ></svg-icon>
          <span
            class="date"
            data-bind="html: formatDate(review.processingUpdatedAt)"
          ></span>
        </span>
      </div>
      <div class="review-date" data-bind="log: 'review-elapsed'">
        <span data-bind="tooltip, tooltipText: review.tooltip">
          <svg-icon params="name: 'review-elapsed'" class="svg-icon--sm"></svg-icon>
          <span>
            <span data-bind="text: review.answerTime"></span>
            <!-- ko if: review.timeToPass -->
            <span
              class="font-weight-normal"
              data-bind="text: ' / ' + review.timeToPass"
            ></span>
            <!-- /ko -->
          </span>
        </span>
      </div>
    </div>
  </div>
</div>
<!-- ko if: review.clientName && review.clientPhone -->
<div class="d-md-none">
  <!-- Позвонить клиенту -->
  <a
    class="f-bg-success call-button"
    data-bind="attr: { href: 'tel:' + review.clientPhone }, tooltip, tooltipText: _t('Позвонить клиенту')"
  >
    <span class="call-block"></span>
    <span data-bind="text: review.clientPhone"></span>
  </a>
  <!-- /Позвонить клиенту -->
</div>
<!-- /ko -->
