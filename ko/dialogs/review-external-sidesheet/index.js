import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/client-history';

import 'Legacy/blocks/components/editor/basic';
import 'Components/editor/tinymce';

import './components/review-external-data';
import './components/review-external-questions';

ko.components.register('review-external-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('review-sidesheet');
      element.classList.add('review-sidesheet--responsive');
      element.classList.add('review-details-modal');

      return new ViewModel(params, element);
    }
  },
  template: html
});
