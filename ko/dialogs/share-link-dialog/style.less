@import 'Style/breakpoints';

.share-link-dialog {
  .foquz-dialog__container {
    width: 600px;
  }
  &__content {
    display: flex;
    margin-bottom: 25px;
  }

  &__link {
    flex-grow: 1;
    margin-right: 12px;
  }

  &__rights {
    width: 90px;
  }

  .select2 {
    &-selection__rendered {
      text-overflow: clip !important;
    }
  }

  &__feedback {
    color: #73808D;
    font-size: 12px;
    display: flex;
    gap: 10px;
    margin-top: 24px;
    opacity: 0;
    pointer-events: none;
    transition: 0.2s ease-out opacity;

    &_active {
      opacity: 1;
      pointer-events: auto;
    }
  }

  .only-mobile({
    &__content {
      flex-direction: column;
    }
    &__rights {
      width: 130px;
    }

  });
}

.share-link-dialog-dropdown {
  min-width: 200px !important;
  transform: translateX(-110px);

  .only-mobile({
    transform: none;

    &:before {
      right: auto;
      left: 92px;
    }
  })
}
