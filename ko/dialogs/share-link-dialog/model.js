import { DialogWrapper } from 'Dialogs/wrapper';

const LEVELS = {
  read: 1,
  block: 3
};


export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.pollId = params.pollId;
    this.rightLevel = ko.observable('');
    this.showAnswers = ko.observable(false);
    this.link = ko.observable('');

    this.tooltip = ko.observable(null);

    this.getLink().then((link) => {
      this.setData(link);
      this.subscriptions.push(
        this.rightLevel.subscribe((v) => this.updateRights()),
        this.showAnswers.subscribe((v) => this.updateShowAnswers())
      );
    });
  }

  setData(data) {
    if (!data) return;

    this.rightLevel(data.right_level);
    this.showAnswers(!!data.show_answers);
    this.link(data.link);
  }

  getLink() {
    return new Promise((res) => {
      $.ajax({
        url: `${APIConfig.baseApiUrlPath}poll/generate-stats-link?id=${this.pollId}&access-token=${APIConfig.apiKey}`,
        method: 'POST',
        success: (response) => {
          res(response.item);
        }
      });
    });
  }

  updateRights() {
    $.ajax({
      url: `${APIConfig.baseApiUrlPath}poll/update-stats-link?id=${this.pollId}&access-token=${APIConfig.apiKey}`,
      method: 'POST',
      data: {
        right_level: this.rightLevel(),
        show_answers: this.showAnswers() ? 1 : 0
      },
      success: (response) => {
        this.setData(response.item);
      }
    });
  }

  updateShowAnswers() {
    $.ajax({
      url: `${APIConfig.baseApiUrlPath}poll/update-stats-link?id=${this.pollId}&access-token=${APIConfig.apiKey}`,
      method: 'POST',
      data: {
        right_level: this.rightLevel(),
        show_answers: this.showAnswers() ? 1 : 0
      },
      success: (response) => {
        this.setData(response.item);
      }
    });
  }

  iconTemplate(state) {
    let span = $('<span></span>');
    if (state.id == LEVELS.read) {
      span.html(`<svg width="22" height="15"><use href="#foquz-icon-eye-light"></use></svg>`);
    } else if (state.id == LEVELS.block) {
      span.html(`<svg width="20" height="17"><use href="#foquz-icon-eye-slash-light"></use></svg>`);
    }

    return span;
  }
}
