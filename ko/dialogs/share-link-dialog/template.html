<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header>
    Поделиться ссылкой
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="share-link-dialog__content">
      <div class="share-link-dialog__link">
        <label class="form-label">
          Ссылка
          <button class="btn-question"
                  data-bind="tooltip"
                  title="Ссылка"></button>
        </label>
        <copy-input params="value: link, tooltip: 'Скопировать ссылку', mode: 'link'"></copy-input>

      </div>
      <div class="ghost-label share-link-dialog__rights">
        <div class="select-wrapper"
             data-bind="tooltip, tooltipText: rightLevel() == '1' ? 'Просмотр доступен' : 'Просмотр отменен'">
          <select data-bind="
            value: rightLevel,
            valueAllowUnset: true,
            select2: {
              containerCssClass: 'form-control',
              wrapperCssClass: 'select2-container--form-control foquz-select2',
              templateSelection: iconTemplate,
              dropdownCssClass: 'share-link-dialog-dropdown',
            }">
            <option value="1">Просмотр доступен</option>
            <option value="3">Просмотр отменен</option>
          </select>
        </div>
      </div>
    </div>

    <div>
      <fc-switch params="checked: showAnswers, label: 'Разрешить просмотр ответов'"></fc-switch>
      <div class="share-link-dialog__feedback" data-bind="css: {'share-link-dialog__feedback_active' : showAnswers() }">
        <fc-icon params="name: 'exclamation-circle-light', color: '#f96261', size: 14"></fc-icon>
        Ссылка на статистику с ответами может содержать персональные данные респондентов.
      </div>
    </div>
  </div>


  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn d-none d-md-flex"
              data-bind="
                click: function() {
                  $dialog.hide('close');
                }">Готово</button>

      <button type="button"
              class="f-btn d-md-none"
              data-bind="
                  click: function() {
                    $dialog.hide('close');
                  }">Готово</button>

    </div>
  </div>

</foquz-dialog>
