<sidesheet params="ref: modal, dialogWrapper: $component">
  <!-- ko let: { $ctx: $data } -->
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">Создать купон многоразового использования</h2>
    </div>
  </div>

  <div class="foquz-dialog__body"
       data-bind="using: formData">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">

      <div class="container reusable-coupon-form">


        <div class="row">
          <div class="col col-12 col-lg-9">
            <div class="form-group">
              <fc-label params="forId: 'new-reusable-coupon-name', text: 'Название акции',
                hint: 'Название акции'"></fc-label>
              <fc-input id="new-reusable-coupon-name"
                        params="value: poolName,
                      invalid: $ctx.formControlErrorStateMatcher(poolName),
                      valid: $ctx.formControlSuccessStateMatcher(poolName),
                      maxlength: 250,
                      counter: true,"></fc-input>
              <fc-error params="text: poolName.error, show: $ctx.formControlErrorStateMatcher(poolName)"></fc-error>
            </div>
          </div>
        </div>


        <div class="row">
          <div class="col col-12 col-lg-3">
            <div class="form-group">
              <fc-label params="forId: 'new-reusable-coupon-code', text: 'Промокод',
                hint: 'Промокод'"></fc-label>
              <fc-input id="new-reusable-coupon-code"
                        params="value: poolCode,
                      invalid: $ctx.formControlErrorStateMatcher(poolCode),
                      valid: $ctx.formControlSuccessStateMatcher(poolCode),
                      maxlength: 50,
                      counter: true"></fc-input>
              <fc-error params="text: poolCode.error, show: $ctx.formControlErrorStateMatcher(poolCode)"></fc-error>
            </div>
          </div>



          <div class="col col-12 col-lg-3">
            <div class="form-group">
              <fc-label params="text: 'Срок действия',
                hint: 'Срок действия'"></fc-label>
              <div class="input-group date-input-group"
                   data-bind="dateInputGroup">
                <!-- ko let: { autosizeInput: ko.observable(null) } -->
                <input class="form-control"
                       placeholder="00.00.0000"
                       style="min-width: 80px"
                       data-bind="value: availableTo,
                         mask, maskPattern: '00.00.0000',
                         autosizeInput: autosizeInput,
                         periodPicker,
                         periodPickerArrowPosition: { anchor: 'right', offset: -10 },
                         periodPickerSingle: true,
                         periodPickerShowDropdowns: true,
                         periodPickerApply: function () {
                           autosizeInput().update();
                         },
                         css: {
                          'is-invalid': $ctx.formControlErrorStateMatcher(availableTo),
                          'is-valid': $ctx.formControlSuccessStateMatcher(availableTo),
                         }">
                <!-- /ko -->
                <i class="date-input-group__icon"></i>
              </div>
              <!-- ko template: {
                  foreach: $ctx.formControlErrorStateMatcher(availableTo),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200),
                } -->
              <div class="form-error"
                   data-bind="text: $parent.availableTo.error()"></div>
              <!-- /ko -->
            </div>
          </div>
          <div class="col col-12 col-lg-3">
            <div class="form-group">
              <fc-label params="text: 'Отправлять уведомления', hint: 'Отправлять уведомления'"></fc-label>
              <!-- ko if: $ctx.usersDirectory.loaded -->
              <div class="select2-wrapper"
                   data-bind="css: {
                  'is-invalid': $ctx.formControlErrorStateMatcher(notificationUserId),
                   'is-valid': $ctx.formControlSuccessStateMatcher(notificationUserId),
                  }">
                <select data-bind="
                        value: notificationUserId,
                        lazySelect2: {
                          containerCssClass: 'form-control',
                            wrapperCssClass: 'select2-container--form-control',
                            minimumResultsForSearch: 0,
                            allowClear: true,
                            templateResult: window.select2templates.user.result,
                        }
                      "
                        data-placeholder="Выберите пользователя">
                  <option></option>
                  <!-- ko foreach: $ctx.usersDirectory.data -->
                  <option data-bind="text: name, value: id, attr: {
                      'data-userpic': avatar
                    }"></option>
                  <!-- /ko -->
                </select>
                <!-- ko template: {
                    foreach: $ctx.formControlErrorStateMatcher(notificationUserId),
                    afterAdd: fadeAfterAddFactory(200),
                    beforeRemove: fadeBeforeRemoveFactory(200),
                  } -->
                <div class="form-error"
                     data-bind="text: $parent.notificationUserId.error()"></div>
                <!-- /ko -->
              </div>
              <!-- /ko -->
            </div>
          </div>
        </div>

        <div class="form-group pool-modal__group">
          <div class="form-check">
            <input type="checkbox"
                   class="form-check-input"
                   id="notify-when-coupon-expires-checkbox-input"
                   data-bind="checked: notifуWhenCouponExpires">
            <label class="form-check-label"
                   for="notify-when-coupon-expires-checkbox-input">
              <!-- ko if: notifуWhenCouponExpires -->
              Сообщить, что срок годности истекает через:
              <!-- /ko -->
              <!-- ko ifnot: notifуWhenCouponExpires -->
              Сообщить, что срок годности истекает через, дней
              <!-- /ko -->
            </label>
          </div>
          <!-- ko template: {
              foreach: templateIf(notifуWhenCouponExpires(), $data),
              afterAdd: slideAfterAddFactory(200),
              beforeRemove: slideBeforeRemoveFactory(200)
            } -->
          <div class="pool-modal__count-group">
            <div class="d-flex align-items-center">
              <input class="form-control"
                     data-bind="
                  textInput: daysCount,
                  numericField,
                  css: {
                  'is-invalid': $ctx.formControlErrorStateMatcher(daysCount),
                  'is-valid': $ctx.formControlSuccessStateMatcher(daysCount),
              }">
              <span class="f-fs-3">дней</span>
            </div>
            <validation-feedback params="show: $ctx.formControlErrorStateMatcher(daysCount), text: daysCount.error">
            </validation-feedback>
          </div>
          <!-- /ko -->
        </div>


        <div class="form-group pool-modal__group">
          <div class="form-check">
            <input type="checkbox"
                   class="form-check-input"
                   id="stop-sending-checkbox-input"
                   data-bind="checked: stopSending">
            <label class="form-check-label"
                   for="stop-sending-checkbox-input">Останавливать рассылку опроса, если истек срок действия</label>
          </div>
        </div>

      </div>
    </div>


  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">

      <button type="button"
              class="f-btn"
              data-bind="click: function() { $dialog.hide(); }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Отменить
      </button>


      <button type="submit"
              class="f-btn f-btn-success"
              data-bind="click: function() { submit(); }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        Сохранить
      </button>
    </div>

    <success-message params="show: isSavedMessageShown"></success-message>
  </div>

  <!-- /ko -->
</sidesheet>
