import { DialogWrapper } from 'Dialogs/wrapper';
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.usersDirectory = new Directory('user?activity=1&role=foquz_admin');
    this.usersDirectory.load();

    this._onSubmit = params.onSubmit;

    this.isSubmitted = ko.observable(false);
    this.submitting = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.isSavedMessageShown = ko.observable(false);

    this.formData = ko.validatedObservable({
      poolName: ko.observable(''),
      availableTo: ko.observable(''),
      notificationUserId: ko.observable(''),
      notifуWhenCouponsLessThanValue: ko.observable(false),
      couponsCount: ko.observable(''),
      notifуWhenCouponExpires: ko.observable(false),
      daysCount: ko.observable(''),
      stopSending: ko.observable(false)
    });

    this.serverErrors = {
      poolName: ko.observable(''),
      availableTo: ko.observable(''),
      notificationUserId: ko.observable(''),
      notifуWhenCouponsLessThanValue: ko.observable(''),
      couponsCount: ko.observable(''),
      notifуWhenCouponExpires: ko.observable(''),
      daysCount: ko.observable(''),
      stopSending: ko.observable('')
    };

    this.setValidationRules();
  }

  setValidationRules() {
    Object.keys(this.serverErrors).forEach((key) => {
      this.formData()[key].subscribe((_) => {
        this.serverErrors[key]('');
      });
    });

    this.formData().poolName.extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: {
        validator: () => false,
        message: () => this.serverErrors.poolName(),
        onlyIf: () => this.serverErrors.poolName()
      }
    });
    this.formData().availableTo.extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.formData().notifуWhenCouponExpires()
      },
      validation: [
        {
          validator: (v) => {
            if (!v) return true;
            return moment(v, 'DD.MM.YYYY').format() !== 'Invalid date';
          },
          message: 'Неверный формат параметра «Срок действия»'
        },
        {
          validator: () => false,
          message: () => this.serverErrors.availableTo(),
          onlyIf: () => this.serverErrors.availableTo()
        }
      ]
    });

    this.formData().notificationUserId.extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => {
          return (
            this.formData().notifуWhenCouponExpires() ||
            this.formData().notifуWhenCouponsLessThanValue() ||
            this.formData().stopSending()
          );
        }
      },
      validation: {
        validator: () => false,
        message: () => this.serverErrors.notificationUserId(),
        onlyIf: () => this.serverErrors.notificationUserId()
      }
    });

    this.formData().couponsCount.extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.formData().notifуWhenCouponsLessThanValue()
      },
      validation: {
        validator: () => false,
        message: () => this.serverErrors.couponsCount(),
        onlyIf: () => this.serverErrors.couponsCount()
      }
    });

    this.formData().daysCount.extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.formData().notifуWhenCouponExpires()
      },
      validation: {
        validator: () => false,
        message: () => this.serverErrors.daysCount(),
        onlyIf: () => this.serverErrors.daysCount()
      }
    });
  }

  getQueryParams() {
    const formData = this.formData();
    const params = {
      title: formData.poolName(),
      available_to: formData.availableTo(),
      notification_user_id: formData.notificationUserId(),
      notif_coupon_less_than: formData.notifуWhenCouponsLessThanValue() ? 1 : 0,
      coupon_less_than_value: formData.couponsCount(),
      notif_available_to_end: formData.notifуWhenCouponExpires() ? 1 : 0,
      count_days: formData.daysCount(),
      stop_pool_send: formData.stopSending() ? 1 : 0,
      type: 'pool'
    };

    if (params.available_to != null && params.available_to != '') {
      params.available_to = moment(formData.availableTo(), 'DD.MM.YYYY').format(
        'YYYY-MM-DD'
      );
    }

    return params;
  }

  _save() {
    const params = this.getQueryParams();
    this.submitting(true);
    return new Promise((resolve, reject) => {
      $.ajax({
        method: 'POST',
        url: `${APIConfig.baseApiUrlPath}discount-pool/create?access-token=${APIConfig.apiKey}`,
        data: params,
        success: (data) => {
          resolve(data);
        },
        error: (response) => {
          reject(response);
        },
        complete: () => {
          this.submitting(false);
        }
      });
    });
  }

  submit() {
    this.isSubmitted(true);

    if (this.formData.isValid()) {
      this._save()
        .then((data) => {
          this.isSavedMessageShown(true);
          this._onSubmit(data.pool);
          this.hide();
        })
        .catch((response) => {
          console.error(response);

          const data = response.responseJSON;
          const errors = data.errors || {};
          if (errors) {
            [
              ['title', 'poolName'],
              ['available_to', 'availableTo'],
              ['notification_user_id', 'notificationUserId'],
              ['coupon_less_than_value', 'couponsCount'],
              ['count_days', 'daysCount']
            ].forEach((pair) => {
              const error = errors[pair[0]];
              if (error) {
                if (Array.isArray(error)) {
                  console.log('this', this);
                  this.serverErrors[pair[1]](error[0]);
                }
              }
            });
          }
        });
    }
  }
}
