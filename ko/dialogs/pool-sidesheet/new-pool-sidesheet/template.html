<sidesheet params="ref: modal, dialogWrapper: $component">
  <!-- ko let: { $ctx: $data } -->
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">Создать пул</h2>
    </div>
  </div>

  <div class="foquz-dialog__body"
       data-bind="using: $ctx.formData">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">

      <div class="container">
        <div class="row">
          <div class="col col-12 col-lg-6">
            <div class="form-group">
              <label class="form-label"
                     for="new-pool-name">Название пула</label>
              <button type="button"
                      class="btn-question"
                      title="Название пула"
                      data-bind="tooltip"></button>
              <input class="form-control"
                     id="new-pool-name"
                     data-bind="textInput: poolName, css: {
                       'is-invalid': $ctx.formControlErrorStateMatcher(poolName),
                       'is-valid': $ctx.formControlSuccessStateMatcher(poolName),
                     }">
              <!-- ko template: {
                  foreach: $ctx.formControlErrorStateMatcher(poolName),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200),
                } -->
              <div class="form-error"
                   data-bind="text: $parent.poolName.error()"></div>
              <!-- /ko -->
            </div>
            <div class="row">
              <div class="col col-12 col-md-6">
                <div class="form-group">
                  <label class="form-label">Срок действия</label>
                  <div class="input-group date-input-group"
                       data-bind="dateInputGroup">
                    <!-- ko let: { autosizeInput: ko.observable(null) } -->
                    <input class="form-control"
                           placeholder="00.00.0000"
                           style="min-width: 80px"
                           data-bind="value: availableTo,
                             mask, maskPattern: '00.00.0000',
                             autosizeInput: autosizeInput,
                             periodPicker,
                             periodPickerArrowPosition: { anchor: 'right', offset: -10 },
                             periodPickerSingle: true,
                             periodPickerShowDropdowns: true,
                             periodPickerApply: function () {
                               autosizeInput().update();
                             },
                             css: {
                              'is-invalid': $ctx.formControlErrorStateMatcher(availableTo),
                              'is-valid': $ctx.formControlSuccessStateMatcher(availableTo),
                             }">
                    <!-- /ko -->
                    <i class="date-input-group__icon"></i>
                  </div>
                  <!-- ko template: {
                      foreach: $ctx.formControlErrorStateMatcher(availableTo),
                      afterAdd: fadeAfterAddFactory(200),
                      beforeRemove: fadeBeforeRemoveFactory(200),
                    } -->
                  <div class="form-error"
                       data-bind="text: $parent.availableTo.error()"></div>
                  <!-- /ko -->
                </div>
              </div>
              <div class="col col-12 col-md-6">
                <div class="form-group">
                  <label class="form-label">Отправлять уведомления</label>
                  <!-- ko if: $ctx.usersDirectory.loaded -->
                  <div class="select2-wrapper"
                       data-bind="css: {
                      'is-invalid': $ctx.formControlErrorStateMatcher(notificationUserId),
                       'is-valid': $ctx.formControlSuccessStateMatcher(notificationUserId),
                      }">
                    <select data-bind="
                            value: notificationUserId,
                            lazySelect2: {
                              containerCssClass: 'form-control',
                                wrapperCssClass: 'select2-container--form-control',
                                minimumResultsForSearch: 0,
                                allowClear: true,
                                templateResult: window.select2templates.user.result,
                            }
                          "
                            data-placeholder="Выберите пользователя">
                      <option></option>
                      <!-- ko foreach: $ctx.usersDirectory.data -->
                      <option data-bind="text: name, value: id, attr: {
                          'data-userpic': avatar
                        }"></option>
                      <!-- /ko -->
                    </select>
                    <!-- ko template: {
                        foreach: $ctx.formControlErrorStateMatcher(notificationUserId),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200),
                      } -->
                    <div class="form-error"
                         data-bind="text: $parent.notificationUserId.error()"></div>
                    <!-- /ko -->
                  </div>
                  <!-- /ko -->
                </div>
              </div>
            </div>
          </div>
          <div class="col col-12 col-lg-6">
            <div class="row new-pool__checkboxes">
              <div class="col col-12 col-sm-6">
                <div class="form-group pool-modal__group">
                  <div class="form-check">
                    <input type="checkbox"
                           class="form-check-input"
                           id="notify-when-coupons-less-than-value-checkbox-input"
                           data-bind="checked: notifуWhenCouponsLessThanValue">
                    <label class="form-check-label"
                           for="notify-when-coupons-less-than-value-checkbox-input">
                      <!-- ko if: notifуWhenCouponsLessThanValue -->
                      Сообщить, что промокодов остается меньше:
                      <!-- /ko -->
                      <!-- ko ifnot: notifуWhenCouponsLessThanValue -->
                      Сообщить, что промокодов остается меньше
                      <!-- /ko -->
                    </label>
                  </div>
                  <!-- ko template: {
                      foreach: templateIf(notifуWhenCouponsLessThanValue(), $data),
                      afterAdd: slideAfterAddFactory(200),
                      beforeRemove: slideBeforeRemoveFactory(200)
                    } -->
                  <div class="pool-modal__count-group">
                    <div class="d-flex align-items-center">
                      <input class="form-control"
                             data-bind="
                          textInput: couponsCount,
                          numericField,
                          css: {
                          'is-invalid': $ctx.formControlErrorStateMatcher(couponsCount),
                          'is-valid': $ctx.formControlSuccessStateMatcher(couponsCount),
                      }">
                      <span>штук</span>
                    </div>
                    <!-- ko template: {
                        foreach: $ctx.formControlErrorStateMatcher(couponsCount),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                      } -->
                    <div class="form-error"
                         data-bind="text: $parent.couponsCount.error"></div>
                    <!-- /ko -->
                  </div>
                  <!-- /ko -->
                </div>
                <div class="form-group pool-modal__group">
                  <div class="form-check">
                    <input type="checkbox"
                           class="form-check-input"
                           id="stop-sending-checkbox-input"
                           data-bind="checked: stopSending">
                    <label class="form-check-label"
                           for="stop-sending-checkbox-input">Останавливать рассылку опроса, если закончились промокоды
                      или
                      истек срок действия</label>
                  </div>
                </div>
              </div>
              <div class="col col-12 col-sm-6">
                <div class="form-group pool-modal__group">
                  <div class="form-check">
                    <input type="checkbox"
                           class="form-check-input"
                           id="notify-when-coupon-expires-checkbox-input"
                           data-bind="checked: notifуWhenCouponExpires">
                    <label class="form-check-label"
                           for="notify-when-coupon-expires-checkbox-input">
                      <!-- ko if: notifуWhenCouponExpires -->
                      Сообщить, что срок годности истекает через:
                      <!-- /ko -->
                      <!-- ko ifnot: notifуWhenCouponExpires -->
                      Сообщить, что срок годности истекает через, дней
                      <!-- /ko -->
                    </label>
                  </div>
                  <!-- ko template: {
                      foreach: templateIf(notifуWhenCouponExpires(), $data),
                      afterAdd: slideAfterAddFactory(200),
                      beforeRemove: slideBeforeRemoveFactory(200)
                    } -->
                  <div class="pool-modal__count-group">
                    <div class="d-flex align-items-center">
                      <input class="form-control"
                             data-bind="
                          textInput: daysCount,
                          numericField,
                          css: {
                          'is-invalid': $ctx.formControlErrorStateMatcher(daysCount),
                          'is-valid': $ctx.formControlSuccessStateMatcher(daysCount),
                      }">
                      <span>дней</span>
                    </div>
                    <validation-feedback params="show: $ctx.formControlErrorStateMatcher(daysCount)">
                      <!-- ko text: $ctx.daysCount.error -->
                      <!-- /ko -->
                    </validation-feedback>
                  </div>
                  <!-- /ko -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">

      <button type="button"
              class="f-btn"
              data-bind="click: function() { $dialog.hide(); }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Отменить
      </button>


      <button type="submit"
              class="f-btn f-btn-success"
              data-bind="click: function() { submit(); }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        Сохранить
      </button>
    </div>

    <success-message params="show: isSavedMessageShown"></success-message>
  </div>

  <!-- /ko -->
</sidesheet>
