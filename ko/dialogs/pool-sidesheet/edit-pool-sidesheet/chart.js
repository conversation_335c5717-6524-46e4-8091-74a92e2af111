import 'Components/highchart/empty/';

ko.components.register('pool-stats-charts', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass(['pool-stats-charts']);

      const viewModel = new (function () {
        this.columnChartId = 'pool-stats-columns';
        this.pieChartId = 'pool-stats-pie';
        this.funnelChartId = 'pool-stats-funnel';

        this.columnChart = null;
        this.pieChart = null;
        this.funnelChart = null;

        this.stats = params.stats;

        this.colors = [
          '#2D99FF', // total
          '#8400FF', // sended
          '#4346D3', // used
          '#16CEB9', // answered
          '#CAAD46' // available
        ];

        this.mode = ko.observable(0);

        this.formatPercentage = function (value) {
          return (value * 100).toFixed(1).replace('.', ',') + '%';
        };

        let used = parseInt(this.stats().used);
        let sent = parseInt(this.stats().sended);

        this.sales = Math.floor((used / sent) * 1000) / 10 || 0;

        this.series = {
          column: () => {
            return [
              {
                name: 'Отправлено',
                y: sent,
                color: viewModel.colors[1]
              },
              {
                name: 'Использовано',
                y: used,
                color: viewModel.colors[2]
              },
              {
                name: 'Ответов получено',
                y: parseInt(this.stats().countAnswersWithCodes),
                color: viewModel.colors[3]
              },
              {
                name: 'Доступно',
                y: parseInt(this.stats().available),
                color: viewModel.colors[4]
              }
            ];
          },
          pie: () => {
            return [
              {
                name: 'Отправлено',
                y: parseInt(this.stats().sended),
                color: viewModel.colors[1]
              },
              {
                name: 'Использовано',
                y: parseInt(this.stats().used),
                color: viewModel.colors[2]
              },
              {
                name: 'Ответов получено',
                y: parseInt(this.stats().countAnswersWithCodes),
                color: viewModel.colors[3]
              },
              {
                name: 'Осталось',
                y: parseInt(this.stats().available),
                color: viewModel.colors[4]
              }
            ];
          },
          funnel: () => {
            return [
              {
                name: 'Всего',
                y: parseInt(this.stats().total),
                color: viewModel.colors[0]
              },
              {
                name: 'Отправлено',
                y: parseInt(this.stats().sended),
                color: viewModel.colors[1]
              },
              {
                name: 'Использовано',
                y: parseInt(this.stats().used),
                color: viewModel.colors[2]
              },
              {
                name: 'Ответов получено',
                y: parseInt(this.stats().countAnswersWithCodes),
                color: viewModel.colors[3]
              },
              {
                name: 'Осталось',
                y: parseInt(this.stats().available),
                color: viewModel.colors[4]
              }
            ];
          }
        };

        this.update = () => {
          this.columnChart.series[0].setData(this.series.column());
          this.pieChart.series[0].setData(this.series.pie());
          this.funnelChart.series[0].setData(this.series.funnel());

          this.columnChart.yAxis[0].update({
            max: parseInt(this.stats().total)
          });
          this.columnChart.yAxis[1].update();
        };
        this.stats.subscribe((v) => {
          this.update();
        });
      })();

      viewModel.onInit = function () {
        const stats = viewModel.stats();
        console.log('stats', stats);
        viewModel.columnChart = Highcharts.chart(viewModel.columnChartId, {
          title: {
            text: '',
            style: {
              display: 'none'
            }
          },
          credits: {
            enabled: false
          },
          legend: {
            enabled: false
          },
          chart: {
            type: 'column',
            height: 350
          },
          xAxis: {
            categories: [
              'Отправлено',
              'Использовано',
              'Ответов получено',
              'Доступно'
            ],
            lineColor: 'red',
            lineWidth: 0,

            offset: 40
          },
          yAxis: [
            {
              title: {
                text: '',
                style: {
                  display: 'none'
                }
              },
              max: parseInt(stats.total),
              endOnTick: false,
              opposite: true
            },
            {
              title: {
                text: '',
                style: {
                  display: 'none'
                }
              },
              max: 100,
              endOnTick: false,
              labels: {
                formatter: function () {
                  let pr = this.value / stats.total;
                  pr = Math.floor(pr * 10000) / 100;
                  return pr + '%';
                  const max = this.axis.linkedParent.dataMax,
                    min = this.axis.linkedParent.dataMin,
                    range = max - min;
                  const procent = Math.floor(
                    ((this.value - min) / range) * 100
                  );
                  console.log('axis', this.axis, 'procent', procent);

                  return (procent || 0) + '%';
                }
              },
              linkedTo: 0
            }
          ],
          series: [
            {
              yAxis: 0,
              name: '',
              data: viewModel.series.column()
            }
          ]
        });

        viewModel.pieChart = Highcharts.chart(viewModel.pieChartId, {
          title: {
            text: '',
            style: {
              display: 'none'
            }
          },
          credits: {
            enabled: false
          },
          legend: {
            enabled: false
          },
          chart: {
            type: 'pie'
          },
          plotOptions: {
            area: {
              clip: false
            },
            pie: {
              clip: false,
              innerSize: '60%',
              center: ['50%', '50%'],
              size: '100%',
              dataLabels: {
                enabled: true,
                format:
                  '{point.name}:<br><b>{point.percentage:.1f}</b>%<br><b>{point.y}</b>',
                filter: {
                  operator: '>',
                  property: 'y',
                  value: 0
                },
                overflow: 'allow',

                connectorWidth: 0,
                style: {
                  fontWeight: 'normal'
                }
              }
            }
          },
          xAxis: {
            labels: {
              enabled: false
            }
          },
          tooltip: {
            headerFormat: null,
            pointFormat:
              '<span style="color:{point.color}">●</span> {point.name}: <b>{point.y}</b><br/>'
          },
          series: [
            {
              name: '',
              data: viewModel.series.pie()
            }
          ]
        });

        viewModel.funnelChart = Highcharts.chart(viewModel.funnelChartId, {
          title: {
            text: '',
            style: {
              display: 'none'
            }
          },
          credits: {
            enabled: false
          },
          legend: {
            enabled: false
          },
          chart: {
            type: 'funnel',
            margin: [0, 0, 0, 0]
          },

          plotOptions: {
            series: {
              dataLabels: {
                enabled: true,
                format: '<b>{point.name}</b> ({point.y:,.0f})',
                softConnector: true
              },
              center: ['50%', '50%'],
              neckWidth: '70',
              neckHeight: '25%',
              width: '80%'
            },
            funnel: {
              height: 220,
              width: 330
            }
          },
          series: [
            {
              name: '',
              data: viewModel.series.funnel()
            }
          ]
        });
      };

      return viewModel;
    }
  },
  template: {
    element: 'pool-stats-charts-template'
  }
});
