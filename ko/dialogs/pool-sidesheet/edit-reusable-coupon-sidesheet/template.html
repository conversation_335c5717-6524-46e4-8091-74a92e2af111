<sidesheet params="ref: modal, dialogWrapper: $component">
  <!-- ko let: {$ctx: $data} -->
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">
        <!-- ko if: !isPoolNameEditing() -->
        <!-- ko text: poolName -->
        <!-- /ko -->
        <!-- /ko -->

        <!-- ko if: isPoolNameEditing() -->
        <header-name-form
          params="value: poolName(),
            save: $ctx.savePoolName.bind($ctx),
            onChange: function() {
              poolNameError('');
            },
            cancel: function() {
              isPoolNameEditing(false);
            },"
        >
        </header-name-form>
        <!-- /ko -->

        <!-- ko if: !isPoolNameEditing() && !loading() -->
        <button
          class="btn btn-icon btn-default btn-default btn-icon-edit ml-2"
          type="button"
          title="Редактировать название"
          data-bind="click: function() { isPoolNameEditing(true); }, tooltip"
        ></button>
        <!-- /ko -->

        <!-- ko template: {
          foreach: templateIf(poolNameError(), $data),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
        <div class="form-error" data-bind="text: poolNameError"></div>
        <!-- /ko -->
      </h2>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container pb-4">
        <!-- ko template: {
            foreach: templateIf(!loading(), $data),
            afterAdd: fadeAfterAddFactory(200, 200)
          } -->

        <!-- ko if: $data.pool.mailings().length || $data.pool.polls().length -->
        <div class="mb-4 f-fs-1">
          <!-- ko if: $data.pool.polls().length -->
          <div>
            <span class="f-color-service">Пул использован в опросах:</span>
            <span data-bind="html: $data.pollsString"></span>
          </div>
          <!-- /ko -->
          <!-- ko if: $data.pool.mailings().length -->
          <div>
            <span class="f-color-service">Пул использован в рассылках:</span>
            <span data-bind="html: $data.mailingsString"></span>
          </div>
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <div class="row" data-bind="using: formData">
          <div class="col col-12 col-md-6" data-bind="log: 'chart'">
            <h2 class="f-h2 mb-0 pb-0">Промокоды</h2>
            <div
              data-bind="text: $ctx.period"
              class="f-color-service f-fs-1-5 mb-4 mt-5p"
            ></div>

            <!-- ko if: $ctx.promocodesLoading -->
            <div
              style="height: 400px"
              class="d-flex align-items-center justify-content-center"
            >
              <spinner></spinner>
            </div>
            <!-- /ko -->

            <!-- ko ifnot: $ctx.promocodesLoading -->

            <!-- ko component: {
              name: 'reusable-coupon-stats-charts',
              params: {
                stats: $ctx.stats
              }
            } -->
            <!-- /ko -->
            <!-- /ko -->
          </div>

          <div class="col col-12 col-md-6">
            <h2 class="f-h2">Настройки</h2>
            <div class="row">
              <div class="col col-12 col-xl-6">
                <div class="form-group">
                  <fc-label
                    params="forId: 'new-reusable-coupon-code', text: 'Промокод',
                    hint: 'Промокод'"
                  ></fc-label>
                  <fc-input
                    id="new-reusable-coupon-code"
                    params="value: poolCode,
                          invalid: $ctx.formControlErrorStateMatcher(poolCode),
                          valid: $ctx.formControlSuccessStateMatcher(poolCode),
                          maxlength: 50,
                          counter: true"
                  ></fc-input>
                  <fc-error
                    params="text: poolCode.error, show: $ctx.formControlErrorStateMatcher(poolCode)"
                  ></fc-error>
                </div>
              </div>
              <div class="col col-12 col-xl-6">
                <div class="form-group">
                  <fc-label
                    params="forId: 'new-reusable-coupon-code', text: 'Срок действия',
                    hint: 'Срок действия'"
                  ></fc-label>

                  <div
                    class="input-group date-input-group"
                    data-bind="dateInputGroup"
                  >
                    <!-- ko let: { autosizeInput: ko.observable(null) } -->
                    <input
                      class="form-control"
                      placeholder="00.00.0000"
                      style="min-width: 80px"
                      data-bind="value: availableTo,
                           mask, maskPattern: '00.00.0000',
                           autosizeInput: autosizeInput,
                           periodPicker,
                           periodPickerArrowPosition: { anchor: 'right', offset: -10 },
                           periodPickerSingle: true,
                           periodPickerShowDropdowns: true,
                           periodPickerApply: function () {
                             autosizeInput().update();
                           },
                           css: {
                            'is-invalid': $ctx.formControlErrorStateMatcher(availableTo),
                            'is-valid': $ctx.formControlSuccessStateMatcher(availableTo),
                           }"
                    />
                    <!-- /ko -->
                    <i class="date-input-group__icon"></i>
                  </div>

                  <!-- ko template: {
                foreach: $ctx.formControlErrorStateMatcher(availableTo),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200),
               } -->
                  <div
                    class="form-error"
                    data-bind="text: $parent.availableTo.error()"
                  ></div>
                  <!-- /ko -->
                </div>
              </div>

              <div class="col col-12 col-xl-6">
                <div class="form-group">
                  <fc-label params="text: 'Отправлять уведомления', hint: 'Отправлять уведомления'"></fc-label>

                  <!-- ko if: $ctx.usersDirectory.loaded -->
                  <div
                    class="select2-wrapper"
                    data-bind="css: {
                      'is-invalid': $ctx.formControlErrorStateMatcher(notificationUserId),
                       'is-valid': $ctx.formControlSuccessStateMatcher(notificationUserId),
                    }"
                  >
                    <select
                      data-bind="
                            value: notificationUserId,
                            valueAllowUnset: true,
                            lazySelect2: {
                              containerCssClass: 'form-control',
                                wrapperCssClass: 'select2-container--form-control',
                                minimumResultsForSearch: 0,
                                allowClear: true,
                                templateResult: window.select2templates.user.result,

                            }
                          "
                      data-placeholder="Выберите пользователя"
                    >
                      <option></option>
                      <!-- ko foreach: $ctx.usersDirectory.data -->
                      <option
                        data-bind="text: name, value: id, attr: {
                          'data-userpic': avatar
                        }"
                      ></option>
                      <!-- /ko -->
                    </select>

                    <!-- ko template: {
                  foreach: $ctx.formControlErrorStateMatcher(notificationUserId),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200),
                } -->
                    <div
                      class="form-error"
                      data-bind="text: $parent.notificationUserId.error()"
                    ></div>
                    <!-- /ko -->
                  </div>
                  <!-- /ko -->
                </div>
              </div>
            </div>

            <div class="edit-pool__checkboxes">
              <div class="form-group pool-modal__groupp">
                <div class="form-check">
                  <input
                    type="checkbox"
                    class="form-check-input"
                    id="notify-when-coupon-expires-checkbox-input"
                    data-bind="checked: notifуWhenCouponExpires"
                  />
                  <label
                    class="form-check-label"
                    for="notify-when-coupon-expires-checkbox-input"
                  >
                    <!-- ko if: notifуWhenCouponExpires -->
                    Сообщить, что срок годности истекает через:
                    <!-- /ko -->

                    <!-- ko ifnot: notifуWhenCouponExpires -->
                    Сообщить, что срок годности истекает через, дней
                    <!-- /ko -->
                  </label>
                </div>

                <!-- ko template: {
                  foreach: templateIf(notifуWhenCouponExpires(), $data),
                  afterAdd: slideAfterAddFactory(200),
                  beforeRemove: slideBeforeRemoveFactory(200)
                } -->
                <div class="pool-modal__count-group">
                  <div class="d-flex align-items-center">
                    <input
                      class="form-control"
                      data-bind="
                        textInput: daysCount,
                        numericField,
                        css: {
                        'is-invalid': $ctx.formControlErrorStateMatcher(daysCount),
                        'is-valid': $ctx.formControlSuccessStateMatcher(daysCount),
                    }"
                    />
                    <span class="f-fs-3">дней</span>
                  </div>
                  <!-- ko template: {
                  foreach: $ctx.formControlErrorStateMatcher(daysCount),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
                  <div
                    class="form-error"
                    data-bind="text: $parent.daysCount.error"
                  ></div>
                  <!-- /ko -->
                </div>

                <!-- /ko -->
              </div>

              <div class="form-group pool-modal__groupp">
                <div class="form-check">
                  <input
                    type="checkbox"
                    class="form-check-input"
                    id="stop-sending-checkbox-input"
                    data-bind="checked: stopSending"
                  />
                  <label
                    class="form-check-label"
                    for="stop-sending-checkbox-input"
                    >Останавливать рассылку опроса, если истек срок
                    действия</label
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="pb-4">
          <pool-promocodes-table
            params="pool: pool, dialogsContainer: dialogRoot, type: 'reusable'"
            data-bind="event: {
            update: function(_, event, params) {
              console.log('update', event, params)
              updateChart(params);
            }
          }"
            class="pool-modal__codes"
          ></pool-promocodes-table>
        </div>

        <!-- /ko -->

        <!-- ko template: {
            foreach: templateIf(loading(), $data),
            afterAdd: fadeAfterAddFactory(200, 200)
          } -->
        <div class="spinner-block">
          <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() { $dialog.hide(); }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'bin'"></svg-icon>
        </span>
        Отменить
      </button>

      <button
        type="submit"
        class="f-btn f-btn-success"
        data-bind="click: function() { submit(); }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'save'"></svg-icon>
        </span>
        Сохранить
      </button>
    </div>

    <success-message params="show: isSavedMessageShown"></success-message>
  </div>

  <foquz-modals-container params="modals: modals"></foquz-modals-container>
  <!-- /ko -->
</sidesheet>

<template id="reusable-coupon-stats-charts-template">
  <!-- ko template: { afterRender: onInit } -->
  <!-- ko ifnot: stats().total > 0 -->
  <empty-highchart class="mb-4"></empty-highchart>
  <!-- /ko -->
  <div class="mb-5 pool-charts" data-bind="visible: stats().total > 0">
    <div
      class=""
      data-bind="attr: { id: columnChartId }"
      style="height: 400px; width: 100%"
    ></div>

    <div class="sales">
      <div class="sales__value" data-bind="text: sales + '%'"></div>
      <div class="sales__text">Продажи=Использовано/Отправлено
        <question-button params="text: 'Продажи=Использовано/Отправлено'"></question-button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>
