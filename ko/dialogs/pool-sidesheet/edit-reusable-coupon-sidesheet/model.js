import { DialogWrapper } from 'Dialogs/wrapper';
import 'Modals/add-promocodes-modal';
import 'Components/pool-promocodes-table';
import { ApiUrl } from 'Utils/url/api-url';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.modals = ko.observableArray([]);
    this.element = element;

    element.classList.add('edit-reusable-coupon-sidesheet');

    this.pool = params.pool;
    this.modalContainer = params.modalContainer;

    this.promocodesLoading = ko.observable(true);
    this.period = ko.observable('За все время');
    this.stats = ko.observable(null);

    this.usersDirectory = new Directory('user?activity=1&role=foquz_admin');
    this.usersDirectory.load();

    this.loading = ko.observable(true);

    this.mailingsString = this.pool
      .mailings()
      .map((m) => {
        if (!m.isArchived) {
          let url = '/foquz/mailings/update?id=' + m.id;
          return `<a href="${url}" target="_blank">${m.title}</a>`;
        } else {
          return `<span>${m.title}</span>`;
        }
      })
      .join(', ');

    this.pollsString = this.pool
      .polls()
      .map((p) => {
        if (p.status == 0 && !p.deleted) {
          let url = '/foquz/foquz-poll/settings?id=' + p.id;
          return `<a href="${url}" target="_blank">${p.name}</a>`;
        } else {
          return `<span>${p.name}</span>`;
        }
      })
      .join(', ');

    this.poolName = ko.observable('');
    this.isPoolNameEditing = ko.observable(false);
    this.poolNameError = ko.observable('');

    this.formData = ko.validatedObservable({
      availableTo: ko.observable(''),
      poolCode: ko.observable(''),
      notificationUserId: ko.observable(''),
      notifуWhenCouponExpires: ko.observable(false),
      daysCount: ko.observable(''),
      stopSending: ko.observable(false)
    });

    this.serverErrors = {
      availableTo: ko.observable(''),
      poolCode: ko.observable(''),
      notificationUserId: ko.observable(''),
      notifуWhenCouponExpires: ko.observable(''),
      daysCount: ko.observable(''),
      stopSending: ko.observable('')
    };

    this.setValidation();
    this.updateChart({});
  }

  updateChart(params) {
    this.promocodesLoading(true);
    this.period(params.period || 'За все время');
    return new Promise((res) => {
      $.ajax({
        url: ApiUrl('discount-pool/plot', { id: this.pool.id }),
        data: params.filters,
        success: (response) => {
          this.stats(response);

          this.promocodesLoading(false);
        }
      });
    });
  }

  onRender() {
    super.onRender();
  }

  savePoolName(newName) {
    this.pool
      .updateName(newName)
      .then((res) => {
        this.poolName(this.pool.poolName());
        this.isPoolNameEditing(false);
      })
      .catch((error) => {
        this.poolNameError(error);
      });
  }

  setValidation() {
    console.log('SET VALIDATION');
    Object.keys(this.serverErrors).forEach((key) => {
      this.formData()[key].subscribe((_) => {
        this.serverErrors[key]('');
      });
    });

    this.formData().poolCode.extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: {
        validator: () => false,
        message: () => this.serverErrors.poolCode(),
        onlyIf: () => this.serverErrors.poolCode()
      }
    });

    this.formData().availableTo.extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.formData().notifуWhenCouponExpires()
      },
      validation: [
        {
          validator: (v) => {
            if (!v) return true;
            return moment(v, 'DD.MM.YYYY').format() !== 'Invalid date';
          },
          message: 'Неверный формат параметра «Срок действия»'
        },
        {
          validator: () => false,
          message: () => this.serverErrors.availableTo(),
          onlyIf: () => this.serverErrors.availableTo()
        }
      ]
    });

    this.formData().notificationUserId.extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => {
          return (
            this.formData().notifуWhenCouponExpires() ||
            this.formData().stopSending()
          );
        }
      },
      validation: {
        validator: () => false,
        message: () => this.serverErrors.notificationUserId(),
        onlyIf: () => this.serverErrors.notificationUserId()
      }
    });

    this.formData().daysCount.extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.formData().notifуWhenCouponExpires()
      },
      validation: {
        validator: () => false,
        message: () => this.serverErrors.daysCount(),
        onlyIf: () => this.serverErrors.daysCount()
      }
    });

    this.pool.load().then(() => {
      this._setPoolData();
      this.loading(false);
    });

    this.isSubmitted = ko.observable(false);
    this.submitting = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.isSavedMessageShown = ko.observable(false);
  }

  _setPoolData() {
    // console.log('SET DATA', ko.toJS(this.pool));
    this.poolName(this.pool.poolName());
    this.formData().poolCode(this.pool.poolCode() || '');
    this.formData().availableTo(this.pool.availableTo());
    this.formData().notificationUserId(this.pool.notificationUserId());
    this.formData().notifуWhenCouponExpires(
      this.pool.notifуWhenCouponExpires()
    );
    this.formData().daysCount(this.pool.daysCount());
    this.formData().stopSending(this.pool.stopSending());
  }

  _save() {
    const params = ko.toJS(this.formData);
    return this.pool.update(params);
  }

  submit() {
    this.isSubmitted(true);

    if (this.formData.isValid()) {
      this.submitting(true);
      this._save()
        .then((data) => {
          this.submitting(false);
          this.isSavedMessageShown(true);
        })
        .catch((errors) => {
          this.submitting(false);
          if (errors) {
            [
              ['available_to', 'availableTo'],
              ['pool_code', 'code'],
              ['notification_user_id', 'notificationUserId'],
              ['count_days', 'daysCount']
            ].forEach((pair) => {
              const error = errors[pair[0]];
              if (error) {
                if (Array.isArray(error)) {
                  this.serverErrors[pair[1]](error[0]);
                }
              }
            });
          }
        });
    }
  }
}
