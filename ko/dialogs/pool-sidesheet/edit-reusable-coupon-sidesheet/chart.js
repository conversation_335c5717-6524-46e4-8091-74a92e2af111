import 'Components/highchart/empty/';

ko.components.register('reusable-coupon-stats-charts', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass(['pool-stats-charts']);

      const viewModel = new (function () {
        this.columnChartId = 'pool-stats-columns';

        this.columnChart = null;

        this.stats = params.stats;

        this.colors = [
          '#2D99FF', // total
          '#8400FF', // sended
          '#4346D3', // used
          '#16CEB9', // answered
          '#CAAD46' // available
        ];

        this.formatPercentage = function (value) {
          return (value * 100).toFixed(1).replace('.', ',') + '%';
        };

        let used = parseInt(this.stats().used);
        let sent = parseInt(this.stats().total);

        this.sales = Math.floor((used / sent) * 1000) / 10 || 0;

        this.series = {
          column: () => {
            return [
              {
                name: 'Отправлено',
                // y: parseInt(this.stats().sended),
                y: sent,
                color: viewModel.colors[1]
              },
              {
                name: 'Ответов получено',
                y: parseInt(this.stats().countAnswersWithCodes),
                color: viewModel.colors[3]
              },
              {
                name: 'Использовано',
                y: used,
                color: viewModel.colors[2]
              }

              // {
              //   name: 'Доступно',
              //   y: parseInt(this.stats().available),
              //   color: viewModel.colors[4]
              // }
            ];
          }
        };

        this.update = () => {
          this.columnChart.series[0].setData(this.series.column());

          this.columnChart.yAxis[0].update({
            max: parseInt(this.stats().total)
          });
          this.columnChart.yAxis[1].update();
        };
        this.stats.subscribe((v) => {
          this.update();
        });
      })();

      viewModel.onInit = function () {
        const stats = viewModel.stats();
        viewModel.columnChart = Highcharts.chart(viewModel.columnChartId, {
          title: {
            text: '',
            style: {
              display: 'none'
            }
          },
          credits: {
            enabled: false
          },
          legend: {
            enabled: false
          },
          chart: {
            type: 'column',
            height: 350
          },
          xAxis: {
            categories: [
              'Отправлено',
              'Ответов получено',
              'Использовано',

            ],
            lineColor: 'red',
            lineWidth: 0,

            offset: 40
          },
          yAxis: [
            {
              title: {
                text: '',
                style: {
                  display: 'none'
                }
              },
              max: parseInt(stats.total),
              endOnTick: false,
              opposite: true
            },
            {
              title: {
                text: '',
                style: {
                  display: 'none'
                }
              },
              max: 100,
              endOnTick: false,
              labels: {
                formatter: function () {
                  let pr = this.value / stats.total;
                  pr = Math.floor(pr * 10000) / 100;
                  return pr + '%';
                  const max = this.axis.linkedParent.dataMax,
                    min = this.axis.linkedParent.dataMin,
                    range = max - min;
                  const procent = Math.floor(
                    ((this.value - min) / range) * 100
                  );
                  console.log('axis', this.axis, 'procent', procent);

                  return (procent || 0) + '%';
                }
              },
              linkedTo: 0
            }
          ],
          series: [
            {
              yAxis: 0,
              name: '',
              data: viewModel.series.column()
            }
          ]
        });
      };

      return viewModel;
    }
  },
  template: {
    element: 'reusable-coupon-stats-charts-template'
  }
});
