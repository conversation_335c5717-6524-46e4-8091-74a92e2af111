import { FComponent } from 'Components/f-component';
import { formatClientDateToServer } from 'Utils/date/format';
import { ApiUrl } from 'Utils/url/api-url';
import { dateValidator } from 'Utils/validation/date';
import { periodValidator } from 'Utils/validation/period';

export class ViewModel extends FComponent {
  constructor(params, element) {
    super(params, element);

    this.refs = {
      description: ko.observable(null)
    }

    this.coupon = params.coupon;

    this.withPromoName = params.withPromoName;
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );

    this.promocodeError = ko.observable('');

    let type = ko.observable('limited');
    let validFrom = ko.observable('');
    let validTo = ko.observable('');

    let dValidator = dateValidator();
    let pValidator = periodValidator();

    let period = ko.observable('').extend({
      validation: {
        validator: () => {
          let from = validFrom();
          let to = validTo();

          if (!from || !to) return true;

          if (!dValidator(from)) return true;
          if (!dValidator(to)) return true;

          let period = `${from}-${to}`;
          return pValidator(period);
        },
        message: 'Некорректный интервал'
      }
    });

    validFrom.extend({
      validation: [
        {
          validator: dValidator,
          message: 'Некорректный формат'
        }
      ]
    });

    validTo.extend({
      validation: [
        {
          validator: dValidator,
          message: 'Некорректный формат'
        }
      ]
    });

    this.form = ko.validatedObservable(
      {
        promoName: ko.observable('').extend({
          required: {
            message: 'Обязательное поле',
            onlyIf: () => this.withPromoName
          }
        }),
        promoCode: ko.observable('').extend({
          required: {
            message: 'Обязательное поле'
          },
          validation: {
            validator: () => false,
            onlyIf: () => this.promocodeError(),
            message: () => this.promocodeError()
          }
        }),
        type,
        maxUsageCount: ko.observable('').extend({
          required: {
            message: 'Обязательное поле',
            onlyIf: () => type() === 'limited'
          }
        }),
        answersCount: ko.observable('').extend({
          required: {
            message: 'Обязательное поле'
          }
        }),
        validFrom,
        validTo,
        period,
        isActive: ko.observable(false),
        description: ko.observable('')
      },
      { live: true, deep: true }
    );

    this.form().promoCode.subscribe((v) => {
      this.promocodeError('');
    });

    if (this.coupon) this.setData(this.coupon);
  }

  setData(coupon) {
    this.form().promoName(coupon.promoName());
    this.form().promoCode(coupon.promoCode());
    this.form().type(coupon.type());
    this.form().maxUsageCount(coupon.maxUsageCount());
    this.form().answersCount(coupon.answersCount());
    this.form().validFrom(coupon.validFrom());
    this.form().validTo(coupon.validTo());
    this.form().isActive(coupon.isActive());
    this.form().description(coupon.description());
  }

  getData() {
    let data = ko.toJS(this.form);

    return {
      promo_name: data.promoName,
      promo_code: data.promoCode,
      type: data.type,
      max_usage_count: data.maxUsageCount,
      bonuses: data.answersCount,
      valid_from_at: formatClientDateToServer(data.validFrom),
      valid_to_at: formatClientDateToServer(data.validTo),
      is_active: data.isActive ? 1 : 0,
      description: data.description
    };
  }

  submit() {
    this.isSubmitted(true);

    if (!this.form.isValid()) return;

    let url = this.coupon
      ? ApiUrl('coupons/update', { id: this.coupon.id })
      : ApiUrl('coupons/create');
    let method = this.coupon ? 'PUT' : 'POST';
    $.ajax({
      url,
      data: this.getData(),
      method,
      success: (response) => {
        if (this.coupon) this.coupon.setData(response.model);
        this.emitEvent('submit', response.model);
        this.isSubmitted(false);
      },
      error: (response) => {
        let errors = response.responseJSON.errors;

        console.log(errors);

        if (errors.promo_code) this.promocodeError(errors.promo_code[0]);
        else console.error(response.responseJSON);
      }
    });
  }

  update() {
    this.refs.description().update()
  }
}
