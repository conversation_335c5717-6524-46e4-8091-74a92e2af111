<!-- ko using: form -->
<!-- ko let: { formControlErrorStateMatcher: $parent.formControlErrorStateMatcher } -->
<div class="row">
  <!-- ko if: $parent.withPromoName -->
  <div class="col-9 mb-25p">
    <fc-label params="text: 'Название акции', required: true"></fc-label>
    <fc-input
              params="counter: true, maxlength: 250, value: promoName, invalid: $parent.formControlErrorStateMatcher(promoName)">
    </fc-input>
    <fc-error params="show: formControlErrorStateMatcher(promoName), text: promoName.error"></fc-error>
  </div>
  <!-- /ko -->

  <div class="col-3 mb-25p">
    <fc-label params="text: 'Промокод', hint: 'Промокод', required: true"></fc-label>
    <fc-input
              params="counter: true, maxlength: 50, value: promoCode, invalid: $parent.formControlErrorStateMatcher(promoCode)">
    </fc-input>
    <fc-error params="show: formControlErrorStateMatcher(promoCode), text: promoCode.error"></fc-error>
  </div>
</div>

<div class="row">
  <div class="col-6 mb-25p">
    <fc-label params="text: 'Тип купона', hint: 'Тип купона'"></fc-label>
    <fc-select params="options: [
      { id: 'one-use', text: 'Разовый' },
      { id: 'limited', text: 'Многоразовый' },
      { id: 'unlimited', text: 'Бесконечный' },
    ], value: type"></fc-select>
  </div>
  <!-- ko if: type() === 'limited' -->
  <div class="col-3 mb-25p">
    <fc-label params="text: 'Кол-во применений', hint: 'Кол-во применений', required: true"></fc-label>
    <fc-input class="text-center"
              params="value: maxUsageCount, mask: 'numbers', maxlength: 10, invalid: $parent.formControlErrorStateMatcher(maxUsageCount)">
    </fc-input>
    <fc-error params="show: formControlErrorStateMatcher(maxUsageCount), text: maxUsageCount.error"></fc-error>
  </div>
  <!-- /ko -->
  <div class="col-3 mb-25p">
    <fc-label params="text: 'Кол-во доп. ответов', hint: 'Кол-во доп. ответов', required: true"></fc-label>
    <fc-input class="text-center"
              params="value: answersCount, mask: 'numbers', maxlength: 10, invalid: $parent.formControlErrorStateMatcher(answersCount)">
    </fc-input>
    <fc-error params="show: formControlErrorStateMatcher(answersCount), text: answersCount.error"></fc-error>
  </div>
</div>

<div class="row">
  <div class="col-6 mb-25p">
    <fc-label params="hint: 'Действует, с–по'">
      <template data-slot="text">
        Действует, <span class="font-weight-normal"> с–по</span>
      </template>
    </fc-label>
    <div class="coupon-form-period">
      <div class="coupon-form-period__wrapper">
        <div>
          <fc-calendar params="value: validFrom, invalid: formControlErrorStateMatcher(validFrom)() || formControlErrorStateMatcher(period)()"></fc-calendar>
          <!-- ko if: formControlErrorStateMatcher(validFrom) -->
          <fc-error params="show: formControlErrorStateMatcher(validFrom), text: validFrom.error"></fc-error>
          <!-- /ko -->
        </div>
        <span class="coupon-form-period__separator">—</span>
        <div>
          <fc-calendar params="value: validTo, invalid: formControlErrorStateMatcher(validTo)() || formControlErrorStateMatcher(period)()"></fc-calendar>
          <!-- ko if: formControlErrorStateMatcher(validTo) -->
          <fc-error params="show: formControlErrorStateMatcher(validTo), text: validTo.error"></fc-error>
          <!-- /ko -->
        </div>
      </div>
      <!-- ko if: formControlErrorStateMatcher(period) -->
      <fc-error params="show: formControlErrorStateMatcher(period), text: period.error"></fc-error>
      <!-- /ko -->
    </div>
  </div>
  <div class="col-6 mb-25p">
    <fc-label></fc-label>
    <div class="fc-input-aligner">
      <fc-switch params="type: 'checkbox', checked: isActive, label: 'Активен'"></fc-switch>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-12">
    <fc-label params="text: 'Описание'"></fc-label>
    <fc-textarea params="ref: $parent.refs.description, value: description, counter: true, maxlength: 500"></fc-textarea>
  </div>
</div>

<!-- /ko -->
<!-- /ko -->
