import { InteractiveTable } from 'Models/interactive-table';
import { ApiUrl } from 'Utils/url/api-url';
import { SortModel } from 'Models/sort';
import { CouponCode } from './code';

export class CouponCodes extends InteractiveTable {
  constructor(couponId) {
    super();
    this.couponId = couponId;
  }

  get orderParamName() {
    return 'sort';
  }

  getSort() {
    return new SortModel('created_at', true);
  }

  get searchParamName() {
    return 'search';
  }

  getSearch() {
    return this._createSearch(
      'created_at',
      'companyName',
      'createdByName',
    );
  }


  load() {
    if (!this.beforeLoad()) return;

    $.ajax({
      url: ApiUrl('bonus-accounts', { coupon_id: this.couponId }),
      data: this.getParams(),
      success: (response) => {
        this.afterLoad(response.items.map(i => new CouponCode(i)));
      },
      error: (response) => {
        console.error(response.responseJSON);
        this.onError();
      }
    });
  }
}
