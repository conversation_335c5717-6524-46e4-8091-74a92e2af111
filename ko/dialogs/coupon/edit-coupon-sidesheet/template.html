<sidesheet params="ref: modal, dialogWrapper: $component">

  <div class="foquz-dialog__header">
    <div class="container">
      <fc-name-form params="name: coupon.promoName"
                    data-bind="event: {
        'name.change': function(_, e, newName) {
          changeName(newName)
        }
      }"></fc-name-form>
    </div>
  </div>

  <div class="foquz-dialog__body coupon-data">
    <div class="foquz-dialog__scroll"
         data-bind="nativeScrollbar">
      <fc-stats-container params="padding: 15"
                          class="coupon-data__stats">
        <fc-stats-item params="value: coupon.answersCount, label: 'Кол-во ответов', separator: true"></fc-stats-item>

        <!-- ko if: coupon.isOneUse -->
        <fc-stats-item params="value: 'Разовый', label: 'Применений', separator: true"></fc-stats-item>
        <!-- /ko -->

        <!-- ko if: coupon.isLimited -->
        <fc-stats-item params="value: coupon.maxUsageCount, label: 'Применений', separator: true"></fc-stats-item>
        <!-- /ko -->

        <fc-stats-item params="value: coupon.usageCount, label: 'Использовано', color: 'primary', separator: true">
        </fc-stats-item>

        <!-- ko ifnot: coupon.isUnlimited -->
        <fc-stats-item params="value: coupon.remains, label: 'Осталось', color: 'violet'"></fc-stats-item>
        <!-- /ko -->

        <fc-stats-item params="text: coupon.createdAt, label: 'Создан'"></fc-stats-item>

        <fc-stats-item params="label: 'Период действия'">
          <template data-slot="text">
            <fc-date-text params="from: coupon.validFrom, to: coupon.validTo"></fc-date-text>
          </template>
        </fc-stats-item>

        <fc-stats-item params="label: 'Статус'">
          <template data-slot="text">
            <!-- ko using: status -->
            <span class="bold"
                  data-bind="text: text, css: className"></span>

            <!-- ko if: $data.reason -->
            <span class="f-color-service">(<span data-bind="text: reason"></span>)</span>
            <!-- /ko -->

            <!-- /ko -->
          </template>
        </fc-stats-item>



      </fc-stats-container>
      <div class="container">

        <div class="coupon-data__settings mb-40p"
             data-bind="let: { open: ko.observable(false)}">
          <div class="mb-20p d-flex align-items-center cursor-pointer"
               data-bind="click: function() {
            open(!open())
          }">
            <h2 class="f-h2 pb-0 mr-10p">Настройки</h2>
            <fc-opener params="open: open"
                       class="f-color-service"></fc-opener>
          </div>


          <div data-bind="slide: open, onSlide: function() {
            form().update()
          }">
            <coupon-form params="ref: form, coupon: coupon"
                         data-bind="event: {
              submit: function() { afterSubmit() }
            }"></coupon-form>
          </div>


        </div>

        <div class="coupon-data__items">
          <h2 class="f-h2">Использованные купоны
            <!-- ko if: codes.items().length -->
            <span class="f-color-service font-weight-normal"
                  data-bind="text: codes.items().length"></span>
            <!-- /ko -->
          </h2>

          <interactive-table params="table: codes, emptyText: 'Купон пока не использовался', hideBeforeInit: true">
            <table class="table f-table f-table--dense f-table--searchable">
              <thead>
                <tr>
                  <th>
                    <table-head-cell params="table: $data, name: 'created_at'">Дата использования</table-head-cell>
                  </th>
                  <th>
                    <table-head-cell params="table: $data, name: 'companyName'">Компания</table-head-cell>
                  </th>
                  <th>
                    <table-head-cell params="table: $data, name: 'createdByName'">Пользователь</table-head-cell>
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- ko foreach: items -->
                <tr>
                  <td>
                    <span data-bind="text: usedDate"></span>
                    <span data-bind="text: usedTime"></span>
                  </td>
                  <td>
                    <a target="_blank" data-bind="text: companyName, attr: {
                      href: '/foquz/company/update?id=' + companyId
                    }"></a>
                  </td>
                  <td>
                    <a target="_blank" data-bind="text: userName, attr: {
                      href: '/foquz/users?nameFilter=' + userName
                    }"></a>
                  </td>
                </tr>
                <!-- /ko -->
              </tbody>
            </table>
          </interactive-table>


        </div>



      </div>

    </div>
  </div>

  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn"
              data-bind="click: function() {
                $dialog.hide();
              }">
        <foquz-icon params="icon: 'bin'"
                    class="f-btn-prepend"></foquz-icon>
        <span data-bind="text: _t('Отменить')"></span>
      </button>
      <button type="submit"
              class="f-btn f-btn-success"
              data-bind="click: function() { submit() }">
        <foquz-icon params="icon: 'save'"
                    class="f-btn-prepend"></foquz-icon>
        <span data-bind="text: _t('Сохранить')"></span>
      </button>
    </div>
  </div>

</sidesheet>
