import { DialogWrapper } from 'Dialogs/wrapper';
import { ApiUrl } from 'Utils/url/api-url';
import { CouponCodes } from './codes';
export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.coupon = params.coupon;
    this.form = ko.observable(null);


    this.status = ko.computed(() => {
      if (!this.coupon.isActive()) {
        return {
          text: 'Неактивен',
          className: 'f-color-service'
        };
      }
      let isExpired = this.coupon.isExpired();
      let isUsageEnded = this.coupon.isUsageEnded();
      if (isUsageEnded) {
        return {
          text: 'Завершен',
          reason: 'закончилось кол-во применений',
          className: 'f-color-danger'
        };
      }

      if (isExpired) {
        return {
          text: 'Завершен',
          reason: 'период действия истёк',
          className: 'f-color-danger'
        };
      }

      return {
        text: 'Активен',
        className: 'f-color-blue'
      };
    });

    this.codes = new CouponCodes(this.coupon.id);
    this.codes.load();
  }

  submit() {
    let form = this.form();
    form.submit();
  }

  afterSubmit() {
    this.emitEvent('submit');
    this.hide('submit');
  }

  changeName(newName) {
    this.update({ promo_name: newName });
  }

  update(data) {
    $.ajax({
      method: 'PUT',
      url: ApiUrl('coupons/update', { id: this.coupon.id }),
      data,
      success: (response) => {
        this.coupon.setData(response.model);
      },
      error: (response) => {
        console.error(response.responseJSON);
      }
    });
  }
}
