import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import '../coupon-form';

ko.components.register('edit-coupon-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('coupon-sidesheet');
      element.classList.add('edit-coupon-sidesheet');

      return new ViewModel(params, element);
    }
  },
  template: html
});
