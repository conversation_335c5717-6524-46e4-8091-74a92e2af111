import { DialogWrapper } from 'Dialogs/wrapper';

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.query = ko.observable('');
    this.items = params.items.map((i) => {
      return {
        ...i,
        checked: ko.observable(false)
      };
    });

    this.filteredItems = ko.pureComputed(() => {
      let q = this.query().trim().toUpperCase();
      if (!q) return this.items;
      return this.items.filter((i) => {
        return i.name.toUpperCase().includes(q);
      });
    });
  }

  add() {
    let checked = this.items.filter((i) => i.checked());
    this.emitEvent('add', { filters: checked });
    this.hide();
  }
}
