<foquz-dialog params="ref: modal, dialogWrapper: $component">


  <foquz-dialog-header>
    Добавить фильтр
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <div class="form-group">

      <search-field params="value: query, allowClear: true">
      </search-field>

    </div>

    <div class="foquz-dialog__scroll">
      <div data-bind="nativeScrollbar">
        <!-- ko ifnot: filteredItems().length -->
        <div class="f-color-service text-center pb-4">Ничего не найдено</div>
        <!-- /ko -->

        <!-- ko foreach: filteredItems -->
        <input-checkbox params="checked: checked" class="mb-3">
        <!-- ko text: $parent.name-->
        <!-- /ko -->
            </input-checkbox>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <!-- ko if: filteredItems().length -->
  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link px-2"
              data-bind="
                click: function() {
                  hide();
                }">
        Отменить
      </button>
      <button type="button"
              class="f-btn f-btn-success"
              data-bind="
                click: function() {
                  add();
                }">
        Добавить
      </button>
    </div>
  </div>
  <!-- /ko -->

</foquz-dialog>
