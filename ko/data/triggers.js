export const ORDER_MADE_TRIGGER = '1';
export const ORDER_DELIVERED_TRIGGER = '2';
export const DAYS_WITHOUT_ORDER_TRIGGER = '3';
export const BIRTHDAY_TRIGGER = '4';
export const NO_ORDER_AS_USUAL_TRIGGER = '5';
export const ORDERS_TRIGGER = '6';

export const triggerNames = {
  [ORDER_MADE_TRIGGER]: 'Заказ оформлен',
  [ORDER_DELIVERED_TRIGGER]: 'Заказ доставлен',
  [DAYS_WITHOUT_ORDER_TRIGGER]: 'Не было заказа, дней',
  [BIRTHDAY_TRIGGER]: 'День рождения',
  [NO_ORDER_AS_USUAL_TRIGGER]: 'Не сделал заказ как обычно',
  [ORDERS_TRIGGER]: 'Заказы'
};

export const triggerTypes = {
  [ORDER_MADE_TRIGGER]: 'ORDER_MADE_TRIGGER',
  [ORDER_DELIVERED_TRIGGER]: 'ORDER_DELIVERED_TRIGGER',
  [DAYS_WITHOUT_ORDER_TRIGGER]: 'DAYS_WITHOUT_ORDER_TRIGGER',
  [BIRTHDAY_TRIGGER]: 'BIRTHDAY_TRIGGER',
  [NO_ORDER_AS_USUAL_TRIGGER]: 'NO_ORDER_AS_USUAL_TRIGGER',
  [ORDERS_TRIGGER]: 'ORDERS_TRIGGER'
};

export const POLL_TRIGGERS = [
  ORDER_MADE_TRIGGER,
  ORDER_DELIVERED_TRIGGER,
  DAYS_WITHOUT_ORDER_TRIGGER,
  NO_ORDER_AS_USUAL_TRIGGER,
  ORDERS_TRIGGER
].map((t) => {
  return {
    id: t,
    name: triggerNames[t]
  };
});

export const MAILING_TRIGGERS = [
  ORDER_MADE_TRIGGER,
  ORDER_DELIVERED_TRIGGER,
  DAYS_WITHOUT_ORDER_TRIGGER,
  BIRTHDAY_TRIGGER,
  NO_ORDER_AS_USUAL_TRIGGER,
  ORDERS_TRIGGER
].map((t) => {
  return {
    id: t,
    name: triggerNames[t]
  };
});

export const orderTriggers = [ORDER_MADE_TRIGGER, ORDER_DELIVERED_TRIGGER];

export const isOrderTrigger = (trigger) => {
  return trigger == ORDER_MADE_TRIGGER || trigger == ORDER_DELIVERED_TRIGGER;
};
