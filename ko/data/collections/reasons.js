export const ReasonsCollection = {
  name: 'Типы причин',
  description:
    'Справочник «Типы причин» используется для обработки плохих анкет в разделе <a class="font-weight-500" target="_blank" href="/foquz/answers">Ответы</a>',
  serverName: 'Типы причин',
  serverId: 'reasons',
  itemServerId: 'reason',
  titleField: 'title',
  texts: {
    newItem: 'Новый тип причины',
    itemName: 'Название',
    addItem: 'Добавить тип причины',
    editItem: 'Редактировать тип причины',
    removeItemTitle: 'Удаление типа причины',
    removeItemMessage:
      'Тип причины будет удален без возможности восстановления',
    emptyMessage: `Справочник «Типы причин» пока пустой.<br>
    Для добавления причины нажмите кнопку <span class="bold">«Новый тип причины»</span>.`,
    itemPlaceholder: 'Введите название'
  }
};
