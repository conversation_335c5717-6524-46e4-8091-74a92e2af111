export const ThemesCollection = {
  name: 'Темы виджета обратной связи',
  description:
    'Справочник «Темы виджета обратной связи» используется в <a class="font-weight-500" target="_blank" href="/foquz/widget-preferences">Виджете</a>',
  serverName: 'Темы виджета обратной связи',
  serverId: 'themes',
  itemServerId: 'theme',
  titleField: 'theme',
  texts: {
    newItem: 'Новая тема',
    itemName: 'Название',
    addItem: 'Добавить тему',
    editItem: 'Редактировать тему',
    removeItemTitle: 'Удаление темы',
    removeItemMessage: 'Тема будет удалена без возможности восстановления',
    emptyMessage: `Справочник «Темы виджета обратной связи» пока пустой.<br>
    Для добавления темы нажмите кнопку <span class="bold">«Новая тема»</span>.`,
    itemPlaceholder: 'Введите название'
  }
};
