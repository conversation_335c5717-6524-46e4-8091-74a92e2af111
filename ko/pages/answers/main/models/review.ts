import {
  Feedback<PERSON><PERSON>ie<PERSON>,
  PollReview,
  Review,
  ReviewProcessing,
  ReviewTypes,
  ReviewVars,
} from "./review.types";
import {
  serverDateStringToClientDateString,
  serverDateStringToClientDateTimeString,
} from "@/utils/date/formats";
import { FilialDataModel } from "@/entities/models/filial";
import { ChannelModel } from "@/entities/structures/channel";
import { LanguageModel } from "@/entities/structures/language";
import { ReviewAnswerModel } from "@/entities/structures/review-answer";
import { UserModel } from "../../../../entities/structures/user/index";
import { ReviewComplaintModel } from "@/entities/structures/review-complaint";
import { RequestReview } from "./review.types";
import { ClientModel } from "@/entities/models/client";
import { Client } from "@/entities/models/client/types";
import { OrderModel } from "@/entities/structures/order";
import { ProcessingStatuses } from "@/constants/processing/processingStatuses";
import { ProcessingStatusNames } from "../../../../constants/processing/processingStatuses";
import { DeviceNames } from "@/constants/devices";
import { RequestThemeModel } from "@/entities/structures/request-theme";
import { ChannelStatuses } from "@/entities/structures/channel/types";

const { computed, observable } = ko;

const Types = {
  Опросы: ReviewTypes.Poll,
  Заявки: ReviewTypes.Request,
  "Обратная связь": ReviewTypes.Feedback,
};

function getProcessingData(data: ReviewVars): ReviewProcessing | null {
  const {
    processingStatus,
    processing_id,
    processingTimeAt,
    moderator,
    executor,
  } = data;

  if (!processing_id) return null;

  let reviewData: ReviewProcessing = {
    status: processingStatus,
    statusName: ProcessingStatusNames[processingStatus],
    time: processingTimeAt,
    moderator: moderator ? UserModel(moderator) : null,
    executor: executor ? UserModel(executor) : null,
  };

  return reviewData;
}

function getClientData(data: ReviewVars): Client | null {
  if (!("clientName" in data)) {
    return null;
  }

  const {
    clientPhone,
    clientName,
    clientEmail,
    clientGender,
    clientBirthday,
    clientFilials,
    clientAdded,
    clientUpdated,
    clientTags,
    clientComputedFields,
    clientAdditionalFields,
  } = data;

  const model = ClientModel({
    phone: clientPhone,
    name: clientName,
    email: clientEmail,
    gender: clientGender,
    birthday: clientBirthday,
    filials: clientFilials,
    addedAt: clientAdded,
    updatedAt: clientUpdated,
    tags: clientTags,
    computedFields: clientComputedFields,
    additionalFields: clientAdditionalFields,
    apiFields: null,
  });

  return model;
}

function getPollReviewData(data: ReviewVars): PollReview {
  const {
    isAuto,
    channel,
    device,
    language,
    answers,
    complaint,
    points,
    order,
    sends
  } = data;
  let withPoints = false;
  let reviewPoints = null;
  if (points && points.points_max) {
    withPoints = true;
    reviewPoints = {
      value: points.answer_points,
      percent: points.percent,
      max: points.points_max,
    };
  }

  const channels = [];
  const channelsStats = {};

  if (sends) {
    sends.forEach(channelData => {
      const channelModel = ChannelModel(channelData);

      if (!channelsStats[channelModel.name]) {
        let channel = {
          name: channelModel.name,
          sended: null,
          repeats: 0,
          hasResponse: false,
          type: channelModel.type,
        };
        channelsStats[channelModel.name] = channel;
        channels.push(channel);
      }

      let channel = channelsStats[channelModel.name];
      channel.sended = channelModel.sended;
      channel.repeats++;
      if (channelModel.status === ChannelStatuses.responded) channel.hasResponse = true;

    })
  }


  const reviewData: PollReview = {
    isAuto,
    channel: channel ? ChannelModel(channel) : null,
    sends: channels,
    device,
    deviceName: DeviceNames[device] || "",
    language: language ? LanguageModel(language) : null,
    answers: (answers || []).map((answer) => ReviewAnswerModel(answer)),
    complaint: complaint ? ReviewComplaintModel(complaint) : null,
    withPoints,
    points: reviewPoints,
    order: order ? OrderModel(order) : null,
  };

  if (points) {
    const { answer_points, percent, points_max } = points;
    reviewData.points = {
      value: answer_points,
      percent,
      max: points_max,
    };
  }

  return reviewData;
}

function getRequestReviewData(data: ReviewVars): RequestReview {
  const { priority, requestTheme, project } = data;

  return {
    priority,
    project: {
      id: `${project.id}`,
      name: project.name,
    },
    requestTheme: requestTheme ? RequestThemeModel(requestTheme) : null,
  };
}

function getFeedbackReviewData(data: ReviewVars): FeedbackReview {
  return {
    feedbackTheme: data.feedbackTheme,
  };
}

export function ReviewModel(data: ReviewVars): Review {
  const {
    id,
    entityId,
    hash,
    view,
    passed_at,
    name,
    filial,
    processing_id,
    answerTags,
    comment,
  } = data;

  const type = Types[view];

  let reviewData: Review = {
    id: `${id}`,
    entityId: `${entityId}`,
    hash,
    type,
    name,
    answerTags: ko.observableArray(answerTags),
    filial: filial ? FilialDataModel(filial) : null,
    passedAt: serverDateStringToClientDateTimeString(passed_at),
    client: getClientData(data),
    processingEnabled: true,
    processingId: processing_id,
    processing: observable(getProcessingData(data)),
    comments: Array.isArray(comment) ? comment : comment ? [comment] : [],
  };

  if ("processingEnabled" in data)
    reviewData.processingEnabled = data.processingEnabled;

  if (type === ReviewTypes.Poll) {
    reviewData = {
      ...reviewData,
      ...getPollReviewData(data),
    };
  } else if (type === ReviewTypes.Request) {
    reviewData = {
      ...reviewData,
      ...getRequestReviewData(data),
    };
  } else if (type === ReviewTypes.Feedback) {
    reviewData = {
      ...reviewData,
      ...getFeedbackReviewData(data),
    };
  }

  return reviewData;
}
