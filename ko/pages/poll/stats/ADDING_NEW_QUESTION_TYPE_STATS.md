# Adding Statistics for New Question Types

This guide provides comprehensive instructions for implementing statistics visualization for new question types in the Foquz poll system.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Step-by-Step Implementation](#step-by-step-implementation)
4. [Data Structure](#data-structure)
5. [Chart Types](#chart-types)
6. [Modal Dialogs](#modal-dialogs)
7. [Testing](#testing)
8. [Example: First Click Test](#example-first-click-test)

## Overview

The statistics system in Foquz displays aggregated response data for each question type. Each question type requires:

- A model class extending the base `Question` class
- A component for rendering the statistics UI
- A template for the HTML structure
- Integration with the question factory

## Architecture

```
ko/pages/poll/stats/
├── question-types/
│   ├── index.js                    # Question factory
│   ├── models/
│   │   ├── question.js            # Base Question class
│   │   └── component.js           # Base component class
│   └── [question-type]/
│       ├── index.js               # Question model
│       ├── component.js           # UI component
│       ├── template.php           # HTML template
│       └── style.less             # Styles (optional)
├── types/
│   └── variants.js                # Type definitions
└── template.php                   # Main template loader
```

## Step-by-Step Implementation

### 1. Create Question Type Directory

Create a new directory in `ko/pages/poll/stats/question-types/` with your question type name:

```bash
mkdir ko/pages/poll/stats/question-types/first-click-test
```

### 2. Create the Question Model (index.js)

Create `index.js` that extends the base `Question` class:

```javascript
import { Question } from "../models/question";
import "./component";
import "./style.less"; // if you have custom styles

export class FirstClickTestQuestion extends Question {
  constructor(questionData, ctx) {
    super(questionData, ctx);

    // Extract question-specific data
    [
      "statistics",
      "clickAreas",
      "imageUrl",
      // ... other properties
    ].forEach((key) => {
      this[key] = questionData[key];
    });

    // Initialize computed values
    this.totalClicks = this.statistics.totalClicks || 0;
    this.uniqueUsers = this.statistics.uniqueUsers || 0;
    
    // Process click areas with statistics
    this.processedAreas = this.processClickAreas();
    
    // Set up observables for UI interactions
    this.selectedArea = ko.observable(null);
    this.showHeatmap = ko.observable(true);
  }

  // Process click areas with their statistics
  processClickAreas() {
    return this.clickAreas.map(area => ({
      ...area,
      clicks: this.statistics.areaClicks[area.id] || 0,
      percentage: this.totalClicks > 0 
        ? ((this.statistics.areaClicks[area.id] || 0) / this.totalClicks * 100).toFixed(1)
        : 0
    }));
  }

  // Required: Define chart data getters
  get columnChartData() {
    return this.processedAreas.map((area, index) => ({
      name: area.name,
      data: [area.clicks],
      color: this.colors[index % this.colors.length]
    }));
  }

  get pieChartData() {
    return [{
      data: this.processedAreas.map(area => ({
        name: area.name,
        y: area.clicks
      }))
    }];
  }

  // Required: Define colors for charts
  get colors() {
    return [
      "#3F51B5", "#536DFE", "#82B1FF", "#84ffff",
      "#aadbff", "#bdb2ff", "#ff9dd8", "#ffbdb4"
    ];
  }

  // Required: Get total response count
  get totalCount() {
    return this.uniqueUsers;
  }

  // Handle area click for detailed view
  openAreaModal(area) {
    super._openVariantModalDialog({
      variant: area,
      params: {
        field: "click_area",
        value: area.id,
      },
      title: `Клиенты, кликнувшие на область "${area.name}"`
    });
  }

  // Open modal for all responses
  openAllClicksModal() {
    this.ctx.openStatsModal("stats-clicks-sidesheet", {
      question: this,
      title: "Все клики"
    });
  }
}
```

### 3. Create the Component (component.js)

Create the KnockoutJS component for rendering:

```javascript
import { QuestionStats } from '../models/component';

class ViewModel extends QuestionStats {
  constructor(params) {
    super({
      name: 'first-click-test',
      defaultChart: 'heatmap', // or 'column', 'pie'
      charts: ['heatmap', 'column', 'pie'],
      question: params.question,
    });

    this.imageUrl = this.question.imageUrl;
    this.processedAreas = this.question.processedAreas;
    this.showOverlay = ko.observable(true);
    
    // Heatmap specific properties
    this.heatmapData = this.generateHeatmapData();
  }

  generateHeatmapData() {
    // Generate heatmap visualization data
    return this.question.statistics.clickPoints || [];
  }

  toggleOverlay() {
    this.showOverlay(!this.showOverlay());
  }

  onAreaClick(area) {
    this.question.openAreaModal(area);
  }

  onAllClicksClick() {
    this.question.openAllClicksModal();
  }

  // Override chart initialization if needed
  initColumnChart() {
    return super.initColumnChart({
      tooltip: {
        headerFormat: undefined,
      },
      plotOptions: {
        column: {
          dataLabels: {
            enabled: true
          }
        }
      }
    });
  }

  onInit() {
    // Initialize charts based on available types
    if (this.hasColumnChart) this.initColumnChart();
    if (this.hasPieChart) this.initPieChart();
    // Initialize custom visualizations (e.g., heatmap)
    if (this.mode() === 'heatmap') this.initHeatmap();
  }

  initHeatmap() {
    // Custom heatmap initialization
    // This would render click points over the image
  }
}

// Register the component
ko.components.register('first-click-test-question-stats', {
  viewModel: ViewModel,
  template: {
    element: 'first-click-test-question-stats-template'
  }
});
```

### 4. Create the Template (template.php)

Create the HTML template for the statistics view:

```php
<template id="first-click-test-question-stats-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  
  <div class="poll-stats poll-stats--first-click-test">
    <!-- Summary Statistics -->
    <div class="d-flex flex-wrap mt-10p mt-md-20p f-fs-2 stats-summary">
      <div class="mr-4 mb-md-3 mb-1">
        <span class="f-color-service">Всего кликов: </span>
        <span class="bold" data-bind="text: question.totalClicks"></span>
      </div>
      <div class="mr-4 mb-md-3 mb-1">
        <span class="f-color-service">Уникальных пользователей: </span>
        <span class="bold" data-bind="text: question.uniqueUsers"></span>
      </div>
      <div class="mb-md-3 mb-1">
        <span class="f-color-service">Среднее время до клика: </span>
        <span class="bold" data-bind="text: question.statistics.avgTimeToClick + ' сек'"></span>
      </div>
    </div>

    <!-- Visualization Container -->
    <div class="question-statistics__variant-statistics">
      <!-- Chart Mode Toggle -->
      <div class="question-statistics__variant-statistics-chart-wrapper">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <div class="question-statistics__variant-statistics-mode-toggle">
            <button class="btn btn-icon btn-icon--simple" 
                    title="Тепловая карта"
                    data-bind="click: function() { mode('heatmap'); }, 
                               css: { 'active': mode() === 'heatmap' }">
              <svg-icon params="name: 'heatmap'"></svg-icon>
            </button>
            <button class="btn btn-icon btn-icon--simple" 
                    title="Столбчатая диаграмма"
                    data-bind="click: function() { mode('column'); }, 
                               css: { 'active': mode() === 'column' }">
              <svg-icon params="name: 'chart-column'"></svg-icon>
            </button>
            <button class="btn btn-icon btn-icon--simple" 
                    title="Круговая диаграмма"
                    data-bind="click: function() { mode('pie'); }, 
                               css: { 'active': mode() === 'pie' }">
              <svg-icon params="name: 'chart-pie'"></svg-icon>
            </button>
          </div>

          <!-- Toggle overlay button -->
          <!-- ko if: mode() === 'heatmap' -->
          <fc-button params="
            color: 'primary', 
            mode: 'text', 
            label: showOverlay() ? 'Скрыть области' : 'Показать области',
            click: toggleOverlay
          "></fc-button>
          <!-- /ko -->
        </div>

        <!-- Heatmap View -->
        <div class="heatmap-container" data-bind="slide: mode() === 'heatmap'">
          <div class="image-wrapper position-relative">
            <img data-bind="attr: { src: imageUrl }" class="img-fluid" />
            
            <!-- Click areas overlay -->
            <!-- ko if: showOverlay -->
            <div class="click-areas-overlay">
              <!-- ko foreach: processedAreas -->
              <div class="click-area" 
                   data-bind="
                     style: {
                       left: x + '%',
                       top: y + '%',
                       width: width + '%',
                       height: height + '%'
                     },
                     click: function() { $parent.onAreaClick($data); },
                     attr: { title: name + ': ' + clicks + ' кликов (' + percentage + '%)' }
                   ">
                <div class="area-label" data-bind="text: percentage + '%'"></div>
              </div>
              <!-- /ko -->
            </div>
            <!-- /ko -->
            
            <!-- Heatmap points -->
            <canvas class="heatmap-canvas" data-bind="attr: { id: 'heatmap-' + $component.id }"></canvas>
          </div>
        </div>

        <!-- Column Chart -->
        <div class="question-statistics__variant-statistics-chart" 
             data-bind="slide: mode() === 'column', 
                        attr: { id: chartIds.column }">
        </div>

        <!-- Pie Chart -->
        <div class="question-statistics__variant-statistics-chart" 
             data-bind="slide: mode() === 'pie', 
                        attr: { id: chartIds.pie }">
        </div>
      </div>

      <!-- Areas Table -->
      <div class="question-statistics__variant-statistics-legend poll-stats-legend">
        <table class="table foq-table mb-0">
          <thead class="position-sticky sticky-top bg-white">
            <tr>
              <th>Область</th>
              <th class="text-right">Количество кликов</th>
              <th class="text-right">Процент</th>
              <th class="text-right">Среднее время</th>
            </tr>
          </thead>
          <tbody>
            <!-- ko foreach: processedAreas -->
            <tr class="cursor-pointer" data-bind="click: function() { $parent.onAreaClick($data); }">
              <td>
                <div class="d-flex align-items-center">
                  <div class="color-indicator mr-2" 
                       data-bind="style: { backgroundColor: $parent.question.colors[$index()] }">
                  </div>
                  <span data-bind="text: name"></span>
                </div>
              </td>
              <td class="text-right" data-bind="text: clicks"></td>
              <td class="text-right" data-bind="text: percentage + '%'"></td>
              <td class="text-right" data-bind="text: avgTime ? avgTime + ' сек' : '-'"></td>
            </tr>
            <!-- /ko -->
            
            <!-- Outside clicks row -->
            <!-- ko if: question.statistics.outsideClicks > 0 -->
            <tr class="text-muted">
              <td>
                <div class="d-flex align-items-center">
                  <div class="color-indicator mr-2" style="background-color: #ccc;"></div>
                  <span>Клики вне областей</span>
                </div>
              </td>
              <td class="text-right" data-bind="text: question.statistics.outsideClicks"></td>
              <td class="text-right" data-bind="text: question.statistics.outsideClicksPercent + '%'"></td>
              <td class="text-right">-</td>
            </tr>
            <!-- /ko -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- View all clicks button -->
    <a class="no-print question-statistics__question-additional-button" 
       data-bind="click: onAllClicksClick">
      Все клики
    </a>
  </div>
  
  <!-- /ko -->
</template>
```

### 5. Add Styles (style.less) - Optional

Create custom styles if needed:

```less
.poll-stats--first-click-test {
  .heatmap-container {
    position: relative;
    max-width: 100%;
    margin: 20px 0;
    
    .image-wrapper {
      position: relative;
      display: inline-block;
      max-width: 100%;
    }
    
    .click-areas-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      
      .click-area {
        position: absolute;
        border: 2px solid #3F51B5;
        background-color: rgba(63, 81, 181, 0.2);
        cursor: pointer;
        pointer-events: all;
        transition: all 0.2s ease;
        
        &:hover {
          background-color: rgba(63, 81, 181, 0.4);
          border-color: #303F9F;
        }
        
        .area-label {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(255, 255, 255, 0.9);
          padding: 4px 8px;
          border-radius: 4px;
          font-weight: bold;
          font-size: 12px;
          white-space: nowrap;
        }
      }
    }
    
    .heatmap-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }
  }
  
  .stats-summary {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  .color-indicator {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    flex-shrink: 0;
  }
}
```

### 6. Register in Question Factory

Add your question type to the factory in `ko/pages/poll/stats/question-types/index.js`:

```javascript
import { FirstClickTestQuestion } from './first-click-test';

const models = {
  // ... existing models
  'first-click-test': FirstClickTestQuestion,
};
```

### 7. Include Template in Main Template File

Add your template to `ko/pages/poll/stats/template.php`:

```php
<?php include __DIR__ . '/question-types/first-click-test/template.php'; ?>
```

## Data Structure

### Question Data from Server

The server should provide question data in this format:

```javascript
{
  id: 123,
  question_id: 456,
  type: "24", // FIRST_CLICK_TEST constant
  name: "Где бы вы первым делом кликнули?",
  
  // Question-specific data
  imageUrl: "/uploads/click-test-image.jpg",
  clickAreas: [
    {
      id: "area_1",
      name: "Кнопка входа",
      x: 10,      // percentage
      y: 20,      // percentage
      width: 15,  // percentage
      height: 8   // percentage
    }
  ],
  
  // Statistics data
  statistics: {
    totalClicks: 150,
    uniqueUsers: 100,
    avgTimeToClick: 3.5,
    areaClicks: {
      "area_1": 75,
      "area_2": 50
    },
    clickPoints: [
      { x: 12, y: 22, time: 2.1 },
      { x: 13, y: 23, time: 3.5 }
    ],
    outsideClicks: 25,
    outsideClicksPercent: 16.7
  },
  
  // Standard properties
  answersCount: 100,
  points: null, // or points data if applicable
}
```

## Chart Types

### Available Chart Types

1. **Column Chart** - Bar chart showing clicks per area
2. **Pie Chart** - Distribution of clicks across areas
3. **Custom Visualizations** - Heatmaps, click maps, etc.

### Chart Configuration

Charts are configured using Highcharts. Override the init methods in your component:

```javascript
initColumnChart() {
  return super.initColumnChart({
    chart: {
      type: 'column'
    },
    xAxis: {
      categories: this.processedAreas.map(a => a.name)
    },
    yAxis: {
      title: {
        text: 'Количество кликов'
      }
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.y}</b> кликов'
    }
  });
}
```

## Modal Dialogs

### Client List Modal

To show users who selected a specific option:

```javascript
openAreaModal(area) {
  super._openVariantModalDialog({
    variant: area,
    params: {
      field: "click_area",
      value: area.id,
    },
    title: `Клиенты, кликнувшие на область "${area.name}"`
  });
}
```

### Custom Modals

Create custom modals for complex interactions:

```javascript
openHeatmapSettingsModal() {
  this.ctx.openStatsModal("stats-heatmap-settings-sidesheet", {
    question: this,
    settings: this.heatmapSettings,
    onSave: (newSettings) => {
      this.updateHeatmapSettings(newSettings);
    }
  });
}
```

## Example: First Click Test

Here's a complete example implementation for a First Click Test question type:

### Server Response Structure

```php
// In your PHP controller
$statistics = [
    'totalClicks' => $totalClicks,
    'uniqueUsers' => $uniqueUsers,
    'avgTimeToClick' => round($avgTime, 1),
    'areaClicks' => $this->getAreaClickCounts($questionId),
    'clickPoints' => $this->getClickPoints($questionId),
    'outsideClicks' => $outsideClicks,
    'outsideClicksPercent' => round($outsideClicks / $totalClicks * 100, 1)
];

$questionData = [
    'id' => $question->id,
    'type' => $question->type,
    'name' => $question->name,
    'imageUrl' => $question->getImageUrl(),
    'clickAreas' => $question->getClickAreas(),
    'statistics' => $statistics
];
```

### Key Features to Implement

1. **Heatmap Visualization** - Show click density on the image
2. **Area Statistics** - Display clicks per defined area
3. **Time Analysis** - Average time to first click
4. **Click Distribution** - Where users clicked outside defined areas
5. **Responsive Design** - Works on all screen sizes
6. **Export Options** - Allow data export for further analysis

## Best Practices

1. **Performance**
   - Lazy load heavy visualizations
   - Use pagination for large datasets
   - Cache computed values

2. **Accessibility**
   - Provide text alternatives for visual data
   - Ensure keyboard navigation works
   - Use ARIA labels appropriately

3. **User Experience**
   - Provide clear labels and tooltips
   - Make interactive elements obvious
   - Show loading states for async operations

4. **Code Organization**
   - Keep models focused on data processing
   - Put UI logic in components
   - Use CSS classes consistently

5. **Error Handling**
   - Handle missing data gracefully
   - Provide meaningful error messages
   - Fall back to simple views when advanced features fail

## Troubleshooting

### Common Issues

1. **Charts not displaying**
   - Check that chart containers have unique IDs
   - Verify data format matches Highcharts requirements
   - Ensure chart initialization happens after DOM render

2. **Modal dialogs not opening**
   - Verify the modal is registered in the dialogs system
   - Check that parameters are passed correctly
   - Look for JavaScript errors in console

3. **Data not updating**
   - Check that observables are used for dynamic data
   - Verify subscriptions are set up correctly
   - Ensure the question factory returns your class

### Debug Tips

1. Add console logs in key methods
2. Use browser dev tools to inspect component state
3. Check network requests for data format
4. Verify all files are loaded (check network tab)

## Conclusion

Implementing statistics for a new question type requires careful attention to:
- Data structure and processing
- User interface components
- Chart configurations
- Modal integrations
- Performance considerations

Follow this guide and use existing question types as references to ensure consistency across the application.