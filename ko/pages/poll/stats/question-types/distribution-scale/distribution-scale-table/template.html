<div class="question-statistics__variant-statistics-legend d-none d-md-block">
    <table
            class="table f-table question-statistics__variant-statistics-legend-table"
    >
        <thead>
        <tr>
            <th class="text-wrap" width="140">Оценки</th>
            <th class="text-wrap" width="140" align="right">
                <span class="">Ответов&nbsp;в диапазоне</span>
            </th>
            <th class="text-wrap" width="140" align="right">
                <span class="">Процент&nbsp;в диапазоне</span>
            </th>
            <th
                    class="text-wrap"
                    width="140"
                    align="right"
                    class="question-statistics__variant-statistics-legend-table-vote-count-head-cell"
            >
                <span class="">Ответов в&nbsp;оценке</span>
            </th>
            <th
                    class="text-wrap"
                    width="140"
                    align="right"
                    class="question-statistics__variant-statistics-legend-table-percentage-head-cell"
            >
                <span class="">Процент по&nbsp;оценке</span>
            </th>
        </tr>
        </thead>

        <tbody>


        <!-- ko foreach: variants -->
        <tr
                class="question-statistics__variant-statistics-legend-table-row"
                data-bind="click: function () { $component.onClick($data); }, style: {
          display: $component.fromOne && value == 0 ? 'none' : ''
          }"
        >
            <td
                    valign="middle"
                    class="py-2 question-statistics__variant-statistics-legend-table-text-cell"
                    data-bind="css: {
            'border-top-0':  $index() > 0
          }"
            >
                <div
                        class="question-statistics__variant-statistics-legend-table-text-cell-content"
                >
                    <div class="d-flex align-items-center">
                        <div class="nps-scale nps-scale--sm nps-scale--colored mr-3">
                            <div class="nps-scale__list">
                                <div
                                        class="nps-scale__item"
                                        data-bind="style: {
                    'backgroundColor': $component.scale[value]
                  }"
                                >
                                    <span data-bind="text: value"></span>
                                </div>
                            </div>
                        </div>

                        <!-- ko if: $index() == 0 || $component.fromOne && value == 1  -->
                        <div class="bold" data-bind="text: $parent.name"></div>
                        <!-- /ko -->
                    </div>
                </div>
            </td>

            <td
                    align="right"
                    valign="middle"
                    class="py-2"
                    data-bind="css: {
            'border-top-0':  $index() > 0
          }"
            >
                <!-- ko if: $index() == 0 || $component.fromOne && value == 1  -->
                <div
                        data-bind="text: $component.getGroupCount($parent.variants)"
                ></div>
                <!-- /ko -->
            </td>

            <td
                    align="right"
                    valign="middle"
                    class="py-2"
                    data-bind="css: {
            'border-top-0':  $index() > 0
          }"
            >
                <!-- ko if: $index() == 0 || $component.fromOne && value == 1  -->
                <div
                        data-bind="text: $component.getGroupPercent($parent.variants)"
                ></div>
                <!-- /ko -->
            </td>

            <td
                    valign="middle"
                    align="right"
                    class="py-2 question-statistics__variant-statistics-legend-table-vote-count-cell"
                    data-bind="text: count"
            ></td>
            <td
                    align="right"
                    valign="middle"
                    class="py-2 question-statistics__variant-statistics-legend-table-percentage-cell"
                    data-bind="text: $component.getVariantPercent($data)"
            ></td>
        </tr>
        <!-- /ko -->


        <!-- ko if: skip -->
        <tr
                class="question-statistics__variant-statistics-legend-table-row"
                data-bind="click: function () { $component.onClick('skipped'); }"
        >
            <td
                    valign="middle"
                    colspan="3"
                    class="py-2 question-statistics__variant-statistics-legend-table-text-cell"
            >
                <div
                        class="question-statistics__variant-statistics-legend-table-text-cell-content bold"
                >
                    Респондент отказался от оценки
                </div>
            </td>

            <td
                    valign="middle"
                    align="right"
                    class="py-2 question-statistics__variant-statistics-legend-table-vote-count-cell"
                    data-bind="text: skipped"
            ></td>
            <td
                    align="right"
                    valign="middle"
                    class="py-2 question-statistics__variant-statistics-legend-table-percentage-cell"
                    data-bind="text: $component.getPercent(skipped)"
            ></td>
        </tr>
        <!-- /ko -->
        </tbody>
    </table>
</div>

<div class="question-statistics__variant-statistics-legend d-md-none">
    <table
            class="table f-table question-statistics__variant-statistics-legend-table"
    >
        <thead>
        <tr>
            <th class="text-wrap" width="140">Оценки</th>
            <th class="text-wrap" width="140" align="right">
                <span class="">Ответов/%<br/>в диапазоне</span>
            </th>
            <th
                    class="text-wrap"
                    width="140"
                    align="right"
                    class="question-statistics__variant-statistics-legend-table-vote-count-head-cell"
            >
                <span class="">Ответов/%<br/>в оценке</span>
            </th>
        </tr>
        </thead>

        <tbody>
        <!-- ko foreach: [
            { name: 'Критик', variants: criticVariants },
            { name: 'Нейтрал', variants: neutralVariants },
            { name: 'Промоутер', variants: promoterVariants },
          ] -->
        <!-- ko foreach: variants -->
        <tr
                class="question-statistics__variant-statistics-legend-table-row"
                data-bind="click: function () { $component.onClick($data); }"
        >
            <td
                    valign="middle"
                    class="py-2 question-statistics__variant-statistics-legend-table-text-cell"
                    data-bind="css: {
              'border-top-0':  $index() > 0
            }"
            >
                <div
                        class="question-statistics__variant-statistics-legend-table-text-cell-content"
                >
                    <div class="d-flex align-items-center">
                        <div class="nps-scale nps-scale--sm nps-scale--colored mr-3">
                            <div class="nps-scale__list">
                                <div
                                        class="nps-scale__item"
                                        data-bind="style: {
                    'backgroundColor': $component.scale[value]
                  }"
                                >
                                    <span data-bind="text: value"></span>
                                </div>
                            </div>
                        </div>

                        <!-- ko if: $index() == 0 -->
                        <div class="bold" data-bind="text: $parent.name"></div>
                        <!-- /ko -->
                    </div>
                </div>
            </td>

            <td
                    align="right"
                    valign="middle"
                    class="py-2"
                    data-bind="css: {
            'border-top-0':  $index() > 1
          }"
            >
                <!-- ko if: $index() == 0 -->
                <span
                        data-bind="text: $component.getGroupCount($parent.variants)"
                ></span
                >/
                <span
                        data-bind="text: $component.getGroupPercent($parent.variants)"
                ></span>
                <!-- /ko -->
            </td>

            <td
                    align="right"
                    valign="middle"
                    class="py-2 question-statistics__variant-statistics-legend-table-percentage-cell"
            ></td>
        </tr>
        <!-- /ko -->
        <!-- /ko -->

        <!-- ko if: skip -->
        <tr
                class="question-statistics__variant-statistics-legend-table-row"
                data-bind="click: function () { $component.onClick('skipped'); }"
        >
            <td
                    valign="middle"
                    colspan="2"
                    class="py-2 question-statistics__variant-statistics-legend-table-text-cell"
            >
                <div
                        class="question-statistics__variant-statistics-legend-table-text-cell-content bold"
                >
                    Респондент отказался от оценки
                </div>
            </td>

            <td align="right" valign="middle" class="py-2">
                <span data-bind="text: skipped"></span>/
                <span
                        data-bind="text: $component.getPercent(skipped)"
                ></span>
            </td>
        </tr>
        <!-- /ko -->
        </tbody>
    </table>
</div>
