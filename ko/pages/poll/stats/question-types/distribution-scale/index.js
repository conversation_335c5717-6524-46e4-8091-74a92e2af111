import { Question } from "../models/question";
import '../../../../../components/chart/bar-chart';

import "./component";
import "./modals";
import './style.less';

export class DistributionScaleQuestion extends Question {
  constructor(questionData, ctx) {
    super(questionData, ctx);

    this.gallery = questionData.gallery.map((i, index) => ({
      url: i.url,
      poster: i.poster,
      description: i.description,
      position: i.position || index,
    })).sort((a, b) => a.position - b.position);

    this.skip = !!questionData.skip;
    this.questionData = questionData;

    this.skipped = Number(questionData.statistics?.skipped || 0);
    let percentSkipped = this.skipped
        ? (this.skipped / questionData.answersCount) * 100
        : 0;
    this.percentSkipped = (Number.isInteger(percentSkipped)
        ? percentSkipped
        : percentSkipped.toFixed(2)) + '%';

    this.fromOne = false;
    this.variants = [];
    this.counts = {};
    this.average = {};
    this.varStats = {};
    this.hasRemoved = false;
    this.showRemoved = ko.observable(false);
    this.scaleRatingColumns = [];

    this.scaleConfig = questionData.scaleRatingSetting;
    const scaleStart = Number(this.scaleConfig?.start ?? 1);
    const scaleEnd = Number(this.scaleConfig?.end ?? 150);

    // Собираем все статистики, включая анонимную
    const allStats = Object.entries(questionData.statistics || {})
        .filter(([key]) => key !== 'skipped')
        .map(([, stats]) => stats);

    const aggregated = {
      aggregate: {},
      'scale-ratings': {},
    };

    for (const stats of allStats) {
      // Обрабатываем aggregate
      for (const [id, data] of Object.entries(stats.aggregate || {})) {
        const target = aggregated.aggregate[id] ??= {
          min: null,
          max: null,
          summ: 0,
          medianaList: [],
          count: 0,
          avg: 0,
        };

        const count = Number(data.count || 0);
        const summ = Number(data.summ || 0);
        const min = data.min !== undefined ? Number(data.min) : null;
        const max = data.max !== undefined ? Number(data.max) : null;

        target.count += count;
        target.summ += summ;

        if (min !== null && (target.min === null || min < target.min)) {
          target.min = min;
        }

        if (max !== null && (target.max === null || max > target.max)) {
          target.max = max;
        }

        // Собираем все значения для точного расчета медианы
        if (stats.ratings?.[id]) {
          const ratings = stats.ratings[id]
              .map(Number)
              .filter(r => !isNaN(r) && r !== null);
          target.medianaList.push(...ratings);
        }
      }

      // Обрабатываем scale-ratings
      for (const [id, bins] of Object.entries(stats["scale-ratings"] || {})) {
        const target = aggregated["scale-ratings"][id] ??= {};
        for (const [key, count] of Object.entries(bins)) {
          target[key] = (target[key] || 0) + Number(count || 0);
        }
      }
    }

    // Финализируем aggregate с точным расчетом медианы
    for (const [id, data] of Object.entries(aggregated.aggregate)) {
      data.avg = data.count ? data.summ / data.count : 0;
      data.avg = Number.isInteger(data.avg) ? data.avg : +data.avg.toFixed(2);

      data.min = data.min !== null ? String(data.min) : "0";
      data.max = data.max !== null ? String(data.max) : "0";

      // Точный расчет медианы из всех значений
      if (data.medianaList.length > 0) {
        data.medianaList.sort((a, b) => a - b);

        const mid = Math.floor(data.medianaList.length / 2);
        let mediana;

        if (data.medianaList.length % 2 === 0) {
          mediana = (data.medianaList[mid - 1] + data.medianaList[mid]) / 2;
          // Для четного количества оставляем один decimal place если нужно
          mediana = mediana % 1 === 0 ? mediana : +mediana.toFixed(1);
        } else {
          mediana = data.medianaList[mid];
        }

        data.mediana = String(mediana);
      } else {
        data.mediana = "0";
      }

      delete data.medianaList;
    }

    this.statistics = aggregated;

    // Формируем варианты ответов
    this.variants = Object.values(questionData.variants || {}).map(v => {
      const aggregate = this.statistics?.aggregate?.[v.id] || {};
      const rawBins = this.statistics?.["scale-ratings"]?.[v.id] || {};

      const total = Object.values(rawBins).reduce((sum, count) => sum + count, 0);
      const scaleRatings = [];

      // Формируем диапазоны для шкалы
      for (let i = scaleStart; i <= scaleEnd; i += 15) {
        const from = i;
        const to = Math.min(i + 14, scaleEnd);
        let count = 0;

        for (let j = from; j <= to; j++) {
          count += Number(rawBins[j] || 0);
        }

        let percent = total ? (count / total) * 100 : 0;
        percent = Number.isInteger(percent) ? percent : +percent.toFixed(2);

        scaleRatings.push([`${from}-${to}`, percent + '%']);
      }

      if (!this.scaleRatingColumns.length) {
        this.scaleRatingColumns = scaleRatings.map(([label]) => label);
      }

      return {
        id: v.id,
        question: v.name,
        count: aggregate.count,
        aggregate: aggregate,
        ratings: aggregate,
        scaleRatings: scaleRatings,
        deleted: !!v.is_deleted,
      };
    });

    this.hasRemoved = this.variants.some(v => v.deleted);
    this.scaleMax = scaleEnd;
  }


  openDynamicsModal() {
    this.ctx.openDynamicsModal({
      id: this.question_id,
      type: "scale",
    });
  }

  get colors() {
    return [
      "#3F51B5", "#536DFE", "#82B1FF", "#84ffff", "#aadbff",
      "#bdb2ff", "#ff9dd8", "#ffbdb4", "#f4e4cd", "#e6ffb1",
      "#bbf9ee", "#c2cfff", "#e7b3ff",
    ];
  }

  get totalCount() {
    return 999;
  }

  openVariantModal(variant) {
    if (variant === "skipped") {
      super._openVariantModalDialog({
        variant: variant,
        params: {
          skipped: 1,
        },
        title: `Клиенты с пропуском ответа`,
      });
      return;
    }

    super._openVariantModalDialog({
      variant: variant,
      params: {
        field: "rating",
        value: variant.id,
      },
      title: `Клиенты с ответом ${variant.question}`,
    });
  }

  openVariantsModal() {
    this.ctx.openStatsModal("stats-distribution-scale-sidesheet", {
      title: this.name,
      question: this,
    });
  }
}
