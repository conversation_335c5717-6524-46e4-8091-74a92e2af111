import { DialogWrapper } from "Dialogs/wrapper";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    console.log('DialogWrapper', params);
    const { name, handleData, data, colors, getTotalAverage, questionsCount, scaleConfig, deleted } = params;
    this.name = name
    this.filials = data
    this.colors = colors
    this.handleData = handleData.bind(this);
    this.isSortingEnabled = ko.observable(false);
    this.deleted = deleted;

    this.chartConfig = ko.observable({ series: [] });
    this.totalAverage = getTotalAverage(data);
    this.handleData(data, colors, this.totalAverage, questionsCount, this.chartConfig, scaleConfig);
    this.toggleSorting = () => {
      console.log('modal this.isSortingEnabled', this.isSortingEnabled())
      this.isSortingEnabled(!this.isSortingEnabled());
      this.handleData(data, colors, this.totalAverage, questionsCount, this.chartConfig, scaleConfig);
    };
    console.log('DialogWrapper  this.config', this.config);

    this.onClick = function (...params) {
      this.hide()
      onClick(...params)
    }
    this.isSkipVariants = params.isSkipVariants;
    this.skipsCount = params.skipsCount;
    this.skipsPercent = params.skipsPercent;
  }


}
