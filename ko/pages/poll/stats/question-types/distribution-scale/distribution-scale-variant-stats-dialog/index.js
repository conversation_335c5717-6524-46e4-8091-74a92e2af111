import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('distribution-scale-variant-stats-dialog', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('distribution-scale-variant-stats-dialog');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
