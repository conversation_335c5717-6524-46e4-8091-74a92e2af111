const { ClientsModal } = require('../modals/clients-modal');

class VariantsModalModel extends ClientsModal {
  constructor(params) {
    super(params);
  }

  get apiUrl() {
    return '/foquz/foquz-poll/get-scale-answers?id=' + this.question.question_id;
  }

  getClients(response) {
    return response.comments;
  }

  get filterableFields() {
    return [
      ...super.filterableFields,
      'ratingLabel'
    ];
  }

  formatPropertiesToStrings(client) {
    let result = super.formatPropertiesToStrings(client);

    let ratingLabel = '';
    if (result.rating < 7) ratingLabel = 'Критик';
    else if (result.rating < 9) ratingLabel = 'Нейтрал';
    else ratingLabel = 'Промоутер';

    result.ratingLabel = ratingLabel;

    return result;
  }
}

ko.components.register('distribution-scale-question-variants-modal', {
  viewModel: VariantsModalModel,
  template: {
    element: 'distribution-scale-question-variants-modal-template',
  },
});
