import { QuestionStats } from "../models/component";
import "./distribution-scale-variant-stats-dialog";

let nextId = 0;

class ViewModel extends QuestionStats {
  constructor(params) {
    super({
      name: "distribution-scale",
      defaultChart: "bar",
      charts: ["bar"],
      question: params.question,
    });

    this.mode = ko.observable('bar');
    this.colors = this.question.colors;
    this.questionsCount = this.question.answersCount;

    this.id = nextId++;
    this.mediaType = this.question.chooseType;

    this.variants = this.question.variants;
    this.averageScaleValue = this.getAverageVariants(this.question.questionData.statistics, this.variants);
    this.config = this.question.scaleConfig;

    this.variantsTypes = [
      {
        value: 'min',
        label: 'Минимум'
      },
      {
        value: 'max',
        label: 'Максимум'
      },
      {
        value: 'avg',
        label: 'Среднее'
      },
      {
        value: 'mediana',
        label: 'Медиана'
      },
    ];
    this.variantsType = ko.observable('avg');

    this.variantsType.subscribe(value => {
      this.updateChart(value)
    })

    while (this.colors.length < this.variants.length) {
      this.colors.push(...this.colors)
    }
  }

  getAverageVariants(objects, names) {
    let averages = {};

    for (let object in objects) {
      if (object !== 'skipped') {
        let ratings = objects[object]['scale-ratings'];
        if (!ratings) {
          continue;
        }
        for (let id in ratings) {
          let sum = 0;
          let count = 0;
          let skipsCount = 0;
          let isSkipVariants = false;
          if (Object.keys(ratings[id]).includes('Отказался от оценки')) {
            isSkipVariants = true;
          }
          for (let rate in ratings[id]) {
            if (rate === 'Отказался от оценки') {
              skipsCount += ratings[id][rate];
              continue;
            }
            count += ratings[id][rate];
            sum += parseInt(rate) * ratings[id][rate];
          }
          let average = sum / count;

          // Если в массиве уже есть объект с таким id, обновляем его среднее значение
          if (averages[id]) {
            averages[id].value = (averages[id].value * averages[id].count + average) / (averages[id].count + 1);
            averages[id].count += 1;
            averages[id].skipsCount += skipsCount;
            averages[id].skipsPercent = count !== 0 ? 100 * (averages[id].skipsCount / (averages[id].skipsCount + count)).toFixed(1) : 0;
          } else {
            let nameObj = names.find(nameItem => nameItem.id.toString() === id);
            averages[id] = {
              value: average,
              count: 1,
              name: nameObj ? nameObj.question : 'Unknown',
              skipsCount,
              skipsPercent: count !== 0 ? 100 * (skipsCount / (skipsCount + count)).toFixed(1) : 0,
              isSkipVariants,
            };
          }
          averages[id].printedValue = averages[id].value.toFixed(1);
          if (averages[id].printedValue[averages[id].printedValue.length - 1] === '0') {
            averages[id].printedValue = averages[id].value.toFixed(0);
          }
        }
      }
    }

    return averages;
  }

  initBarChart() {
    const variants = this.variants.map(v => ({
      text: v.question,
      count: Number(v.aggregate[this.variantsType()]),
    }));

    return super.initBarChart({
      values: variants,
      tooltip: {
        headerFormat: undefined,
        pointFormat:
        '<span style="color:{point.color}">●</span> {point.name}: <b>{point.y}</b><br/>',
      },
    });
  }

  updateChart(type) {
    const variants = this.variants.map(v => ({
      text: v.question,
      count: Number(v.aggregate[type]),
    }));
    this.charts.bar.update({
      series: [{
        data: variants.map(v => {
          const data = {
            name: v.text,
            y: v.count
          };
          if (v.color) {
            data.color = v.color;
          }
          return data;
        }),
        type: 'bar',
      }],
    });
  }

  onInit() {
    this.initBarChart()
    const self = this;

    if (this.mode() === 'bar') {
      this.mode('table');
      setTimeout(() => this.mode('bar'), 150);
    }
    this.charts.bar.xAxis[0].labelGroup.element.childNodes.forEach((label, index) => {
      if (label) {
        label.style.cursor = "pointer";
        label.onclick = () => {
          const variant = this.variants[index];
          if (variant) {
            self.openVariantStats(variant, variant.question);
          }
        }
      }
    });
    this.charts.bar.series[0].points.forEach(({ graphic: { element } = {} } = {}, index) => {
      if (element) {
        element.style.cursor = "pointer";
        element.onclick = () => {
          const variant = this.variants[index];
          if (variant) {
            self.openVariantStats(variant, variant.question);
          }
        }
      }
    });
  }

  getRatingsById(id, statistics = this.question.questionData.statistics) {
    let result = [];
    for (let filial in statistics) {
      if (filial !== 'skipped') {
        if (statistics[filial]['scale-ratings'] && statistics[filial]['scale-ratings'][id]) {
          let ratings = statistics[filial]['scale-ratings'][id];
          let totalRatings = 0;
          let totalCount = 0;
          let skipsCount = 0;
          for (let rating in ratings) {
            if (rating === 'Отказался от оценки') {
              skipsCount += ratings[rating];
              continue;
            }
            totalCount += ratings[rating];
            totalRatings += Number(rating) * ratings[rating];
          }
          let averageValue = totalCount > 0 ? totalRatings / totalCount : 0;
          let percentageOfMax = totalCount > 0 ? (averageValue / Math.max(...Object.keys(ratings)) * 100) : 0;
  
          result.push({
            filialName: filial === 'anonymous' ? 'Без филиала' : filial,
            count: totalCount,
            averageValue: averageValue.toFixed(2),
            percentageOfMax: percentageOfMax.toFixed(2) + "%",
            skipsCount,
          });
        }
      }
    }
    return result;
  }

  handleData(data, colors, totalAverage, questionsCount, chartConfig, scaleConfig) {
    if (data.length === 0) {
      chartConfig({});
      return;
    }
    let filials = Object.keys(data || {}).map((filial, index) => {
      let averageValue = parseInt(data[filial].averageValue) || 0;
      let count = parseInt(data[filial].count) || 0;

      let colorIndex = index % colors.length;
      return {
        name: data[filial].filialName,
        y: averageValue,
        count: count,
        percent: averageValue,
        color: colors[colorIndex]
      };
    });

    filials.push({
      name: "Средняя оценка",
      y: parseInt(totalAverage),
      count: parseInt(questionsCount),
      percent: parseInt(totalAverage),
      color: '#00C968'
    })

    if (this.isSortingEnabled()) {
      filials.sort((a, b) => {
        if (a.name === "Средняя оценка") {
          return -1;
        }
        if (b.name === "Средняя оценка") {
          return 1;
        }
        return b.y - a.y;
      });
    } else {
      // Ваш обычный порядок сортировки
      filials.sort((a, b) => {
        if (a.name === "Средняя оценка") {
          return -1;
        }
        if (b.name === "Средняя оценка") {
          return 1;
        }
        if (a.name === "Без филиала") {
          return 1;
        }
        if (b.name === "Без филиала") {
          return -1;
        }
        return a.name.localeCompare(b.name);
      });
    }
    const isMobile = window.innerWidth <= 769;
    chartConfig({
      chart: {
        height: filials.length > 10 ? 500 : null,
      },
      exporting: {
        enabled: false // Пока отключаем кнопку-бургер
      },
      xAxis: {
        type: 'category',
        categories: filials.map((f) => f.name),
        scrollbar: {
          enabled: true,
          showFull: false
        },
        min: 0,
        max: Math.min(filials.length - 1, 25),
        labelWidth: '100%',
        labels: {
          align: isMobile ? 'left' : 'right',
          y: isMobile ? -22 : 2,
          x: isMobile ? 4 : -5,
          width: '100%',
          style: {
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            whiteSpace: isMobile ? 'nowrap' : 'normal',
          },
          formatter: function () {
            if (this.isFirst) {
              return '<span style="font-weight: bold; font-size: 14px;">' + this.value + '</span>';
            } else {
              return '<span style="font-size: 14px;">' + this.value + '</span>';
            }
          }
        }
      },
      credits: {
        enabled: false,
      },
      yAxis: {
        opposite: true,
        tickPixelInterval: 150,
        title: {
          enabled: false
        },
        min: 0,
        max: scaleConfig.end,
      },
      series: [{
        data: filials,
        dataLabels: {
          enabled: true,
          inside: false,
          rotation: 0,
          align: 'center',
          color: '#2E2F31',
          style: {
            textOutline: 'none',
            fontSize: '14px',
            fontWeight: '400',
            lineHeight: '14px'
          },
          x: 30,
          y: 0,
          formatter: function () {
            const percentage = filials[this.point.index].percent;
            const labelX = this.point.plotX; // Координата X подписи
            const labelHeight = this.point.shapeArgs.height; // Ширина столбца
            const rightEdge = this.series.chart.plotLeft + this.series.chart.plotWidth; // Правая граница графика
            if (labelX + labelHeight > rightEdge) {

              return '<span style="font-size: 14px; text-shadow: white 0px 0px 3px;">' + percentage + ' / ' + this.point.count + '</span>';
            } else {

              return '<span style="font-size: 14px;"><span style="font-weight: bold">' + percentage + '</span>' + ' / ' + this.point.count + '</span>';
            }
          }
        }
      }],
      tooltip: {
        useHTML: true,
        enabled: !isMobile,
        style: {
          fontSize: '14px',
        },
        // positioner: function (labelWidth, labelHeight, point) {
        //   return { x: point.plotX, y: point.plotY };
        // },
        positioner: function (labelWidth, labelHeight, point) {
          let tooltipX, tooltipY;
          if (point.plotX + labelWidth > this.chart.plotWidth) {
            tooltipX = point.plotX + this.chart.plotLeft - labelWidth;
          } else {
            tooltipX = point.plotX + this.chart.plotLeft;
          }
          tooltipY = point.plotY + this.chart.plotTop - labelHeight;

          const result = isMobile ? { x: tooltipX, y: tooltipY } : { x: point.plotX, y: point.plotY }
          return result
        },
        formatter: function () {
          return '<div>' + this.point.name + '</div><div><span style="font-weight: bold">' + this.point.y + '</span>' + ' / ' + this.point.count + '</div>';
        }
      },
    });
  }
  
  get colorsSet() {
    return [
      '#3F51B5',
      '#536DFE',
      '#82B1FF',
      '#84FFFF',
      '#ADD9FF',
      '#55A8E3',
      '#8E7DF4',
      '#D465A8',
      '#D98479',
      '#CCAB7C',
      '#D1D1D1',
      '#AADBFF',
      '#BDB2FF',
      '#FF9DD8',
      '#FFBDB4',
      '#F4E4CD',
      '#DEEEBB',
      '#BBF9EE',
      '#C2CFFF',
      '#E7B3FF'
    ];
  }
  
  getTotalAverage(statistics) {
    let totalSum = 0;
    let totalRatingCount = 0;

    // Проверка является ли статистика массивом
    if (Array.isArray(statistics)) {
      statistics.forEach(stat => {
        let count = parseInt(stat.count);
        let avg = parseFloat(stat.averageValue);
        totalSum += avg * count;
        totalRatingCount += count;
      });
    } else {
      for (let filialName in statistics) {
        if (filialName !== 'skipped') {
          let ratings = statistics[filialName]['scale-ratings'];
          if (!ratings) {
            continue;
          }
          if (!this.variants.length) {
            totalSum += _reduce(
              ratings,
              (acc, el, key) => {
                if (key === 'Отказался от оценки') {
                  return acc;
                }
                totalRatingCount += el;
                return acc + el * key;
              },
              0,
            );
          } else {
            for (let rate in ratings) {
              totalSum += _reduce(
                ratings[rate],
                (acc, el, key) => {
                  if (key === 'Отказался от оценки') {
                    return acc;
                  }
                  totalRatingCount += el;
                  return acc + el * key;
                },
                0,
              );
            }
          }
        }
      }
    }

    let averageValue = totalRatingCount > 0 ? totalSum / totalRatingCount : 0;

    return averageValue.toFixed(0);
  }

  openVariantStats({ id }, name) {
    this.openSidesheet({
      name: "distribution-scale-variant-stats-dialog",
      params: {
        name,
        handleData: this.handleData,
        data: this.getRatingsById(id),
        colors: this.colorsSet,
        getTotalAverage: this.getTotalAverage,
        questionsCount: this.questionsCount,
        chartConfig: this.chartConfig,
        scaleConfig: this.config,
        toggleSorting: this.toggleSorting,
        deleted: this.variants.find(obj => String(obj.id) === String(id)).deleted,
        isSkipVariants: this.averageScaleValue[id].isSkipVariants,
        skipsCount: this.averageScaleValue[id].skipsCount,
        skipsPercent: this.averageScaleValue[id].skipsPercent,
      },
    });
  }
}

ko.components.register('distribution-scale-question-stats', {
  viewModel: ViewModel,
  template: {
    element: 'distribution-scale-question-stats-template',
  },
});
