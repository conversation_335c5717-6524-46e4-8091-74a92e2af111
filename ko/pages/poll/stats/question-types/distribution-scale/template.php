<template id="distribution-scale-question-stats-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->

  <!-- ko if: $component.question.gallery.length -->
    <div
      class="f-color-text mt-3 d-flex align-items-center cursor-pointer no-print gallery-button_show"
      data-bind="
        fancyboxGalleryItem: {
          gallery: $component.question.gallery.map((i) => ({
            src: i.url,
            opts: { caption: i.description }
          })),
          noCursor: true,
          index: 0,
        }
      "
    >
      <span class="f-icon f-icon--picture f-icon-sm mr-2">
        <svg>
          <use href="#picture-icon"></use>
        </svg>
      </span>
      <span class="f-color-primary f-fs-3 bold">Галерея</span>
    </div>
  <!-- /ko -->

  <div class="question-statistics__question-variant-statistics question-statistics__variant-statistics flex-wrap">
    <div class="align-items-center justify-content-between w-100">
      <div class="question-statistics__variant-statistics-mode-toggle d-flex flex-column flex-md-row justify-content-between align-items-md-center priority-chart-toggle">
        <div class="my-25p">
          <button class="btn btn-icon btn-icon--simple question-statistics__variant-statistics-mode-button question-statistics__variant-statistics-bar-mode-button mr-2" title="Новый график" data-bind="click: function () { mode('bar'); }, css: { 'question-statistics__variant-statistics-mode-button--active': mode() === 'bar' }">
          </button>
          <button class="btn btn-icon btn-icon--simple question-statistics__variant-statistics-mode-button question-statistics__variant-statistics-table-mode-button ml-0" title="Таблица" data-bind="click: function () { mode('table'); }, css: { 'question-statistics__variant-statistics-mode-button--active': mode() === 'table' }">
          </button>
        </div>
        <!-- ko template: {
            foreach: templateIf(mode() === 'bar', $data),
            afterAdd: fadeAfterAddFactory(400),
            beforeRemove: fadeBeforeRemoveFactory(400)
        } -->
          <fc-scroll params="indicator: false">
            <div style="min-width: 420px">
              <radio-group
                params="
                  options: variantsTypes,
                  value: variantsType,
                  withoutIcon: true,
                "
              >
              </radio-group>
            </div>
          </fc-scroll>
        <!-- /ko -->
      </div>
    </div>
    <div class="question-statistics__variant-statistics-chart-wrapper priority-chart-wrapper">
      <div class="question-statistics__variant-statistics-chart question-statistics__variant-statistics-column-chart" data-bind="slide: mode() === 'bar', attr: { id: chartIds.bar }" style="height: initial">
      </div>            
    </div>
    <div class="question-statistics__variant-statistics-legend priority-statistic-legend" data-bind="slide: mode() === 'table'">
      <table class="table foq-table question-statistics__variant-statistics-legend-table">
        <thead>
          <tr>
            <th class="priority-statistic-legend-head-cell">Вариант</th>
            <!-- ko foreach: $component.question.scaleRatingColumns -->
              <th class="question-statistics__priority-statistics-legend-table-head-cell text-center">
                <!-- ko text: $data -->
                <!-- /ko -->
              </th>
            <!-- /ko -->
          </tr>
        </thead>
          <tbody>
            <!-- ko foreach: variants -->
              <tr
                class="question-statistics__priority-statistics-legend-table-row"
                data-bind="click: () => $component.openVariantStats($data, $data.question)"
              >
                <td class="question-statistics__priority-statistics-legend-table-text-cell">
                  <div class="question-statistics__priority-statistics-legend-table-text-cell-content">
                    <!-- ko text: $data.question -->
                    <!-- /ko -->
                  </div>
                </td>

                <!-- ko foreach: $data.scaleRatings -->
                  <td class="question-statistics__priority-statistics-legend-table-cell text-center">
                    <!-- ko text: $data[1] -->
                    <!-- /ko -->
                  </td>
                <!-- /ko -->
              </tr>
            <!-- /ko -->
            <!-- ko if: question.skip -->
              <tr
                class="question-statistics__priority-statistics-legend-table-row"
                data-bind="click: () => $component.question.openVariantModal('skipped')"
              >
                <td class="question-statistics__priority-statistics-legend-table-text-cell">
                  <div class="question-statistics__priority-statistics-legend-table-text-cell-content">
                    Респондент отказался от ответа
                  </div>
                </td>
                <td
                  class="question-statistics__priority-statistics-legend-table-cell"
                  data-bind="attr: { colspan: $component.question.scaleRatingColumns.length + 1 }"
                >
                  <div class="d-flex justify-content-end">
                    <div data-bind="text: question.skipped"></div>
                    <div class="bold ml-30p" data-bind="text: question.percentSkipped"></div>
                  </div>
                </td>
              </tr>
            <!-- /ko -->
          </tbody>
      </table>
    </div>
  </div>

  <div class="d-flex flex-wrap">
    <a class="no-print question-statistics__question-additional-button
      question-statistics__question-all-profiles-button mr-30p" data-bind="click: onAllVariantsClick">
      Все ответы
    </a>
  </div>
  <!-- /ko -->
</template>
