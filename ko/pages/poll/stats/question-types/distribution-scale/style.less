@import "Style/breakpoints";

.scale-variants:not(.table) {
  max-width: 550px;

  tr {
    cursor: pointer;
  }

  @media screen and (max-width: 1199.98px) {
    max-width: 100%;
  }
}

.scale-variant-stats-dialog {
  //.foquz-dialog__container {
  //  width: 600px;
  //}
}

.highcharts-exporting-group {
  right: 20px;
  top: -20px;
}

.highcharts-contextbutton {
  right: 25px;
  top: -25px;
}

.static-variants-scale-slider {
  .ui-state-disabled {
    opacity: 1;
  }

  .ui-slider {
    background-color: #D9D9D9;

    .ui-slider-range {
      background-color: #3F65F1;

    }
  }

  @media screen and (max-width: 767.98px) {
    .foquz-slider__slider {
      min-width: 60px;
    }

  }
}

.filials-chart {
  .skipped {
    padding-top: 10px;
    padding-bottom: 10px;
    border-top: 1px solid #E7EBED;
    border-bottom: 1px solid #E7EBED;
  }
}

.scale-statistics-removed {
  color: #73808d;
}

.variant-skip-values {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.variant-skip-values__title {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 130%;
  color: #73808D;
  margin-right: 10px;
}

.variant-skip-values__value {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #2E2F31;

  span:last-child {
    font-weight: bold;
  }
}

.scale-variants-item {
  display: flex;
  align-items: center;
  padding-top: 8px;
  padding-bottom: 8px;

  .scale-variants-item__text {
    flex-grow: 1;
  }

  .scale-variants-item__value {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 700;
    line-height: 15px;
    
    .foquz-slider__slider {
      min-width: 100px;
    }
  }
}
