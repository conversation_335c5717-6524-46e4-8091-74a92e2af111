# First Click Test Statistics Implementation Guide

This comprehensive guide provides detailed specifications and code examples for implementing the "first click test" statistics visualization in the FOQUZ system.

## Table of Contents

1. [Overview](#overview)
2. [Statistics Block Components](#statistics-block-components)
3. [Implementation Details](#implementation-details)
4. [Code Examples](#code-examples)
5. [Data Structure](#data-structure)
6. [Integration Steps](#integration-steps)

## Overview

The First Click Test statistics block displays aggregated click data from users who interacted with an uploaded image. It provides insights into where users clicked, how long it took them to click, and the distribution of clicks across defined areas.

## Statistics Block Components

### 1. Header Section

The header displays the question title and total response count:

```html
<div class="poll-stats__header">
  <h3 class="poll-stats__title" data-bind="text: question.name"></h3>
  <span class="poll-stats__count">Ответов: <span data-bind="text: question.totalCount"></span></span>
</div>
```

### 2. Summary Metrics Cards

Display key metrics in a card layout:

```html
<div class="d-flex flex-wrap mt-10p mt-md-20p f-fs-2 stats-summary">
  <div class="mr-4 mb-md-3 mb-1">
    <span class="f-color-service">Всего кликов: </span>
    <span class="bold" data-bind="text: question.totalClicks"></span>
  </div>
  <div class="mr-4 mb-md-3 mb-1">
    <span class="f-color-service">Среднее время выполнения, сек: </span>
    <span class="bold" data-bind="text: question.statistics.avgExecutionTime + ' сек'"></span>
  </div>
</div>
```

### 3. Tab Navigation

Toggle between different visualization modes:

```html
<div class="question-statistics__variant-statistics-mode-toggle">
  <button class="btn btn-icon btn-icon--simple" 
          title="Тепловая карта кликов"
          data-bind="click: function() { mode('heatmap'); }, 
                     css: { 'active': mode() === 'heatmap' }">
    <svg-icon params="name: 'heatmap'"></svg-icon>
  </button>
  <button class="btn btn-icon btn-icon--simple" 
          title="Время до первого клика"
          data-bind="click: function() { mode('time'); }, 
                     css: { 'active': mode() === 'time' }">
    <svg-icon params="name: 'chart-column'"></svg-icon>
  </button>
  <button class="btn btn-icon btn-icon--simple" 
          title="Распределение по зонам"
          data-bind="click: function() { mode('zones'); }, 
                     css: { 'active': mode() === 'zones' }">
    <svg-icon params="name: 'chart-pie'"></svg-icon>
  </button>
</div>
```

### 4. Click Zones Visualization (Heatmap)

The main visualization showing click density on the uploaded image:

```html
<div class="heatmap-container" data-bind="slide: mode() === 'heatmap'">
  <div class="image-wrapper position-relative">
    <!-- Base image -->
    <img data-bind="attr: { src: imageUrl }" class="img-fluid" alt="Test image" />
    
    <!-- Highcharts heatmap overlay -->
    <div class="heatmap-overlay" data-bind="attr: { id: 'heatmap-' + $component.id }"></div>
    
    <!-- Click areas overlay (optional) -->
    <!-- ko if: showAreas() -->
    <div class="click-areas-overlay">
      <!-- ko foreach: processedAreas -->
      <div class="click-area" 
           data-bind="
             style: {
               left: x + '%',
               top: y + '%',
               width: width + '%',
               height: height + '%'
             },
             click: function() { $parent.onAreaClick($data); },
             attr: { title: name + ': ' + clicks + ' кликов (' + percentage + '%)' }
           ">
        <div class="area-label" data-bind="text: percentage + '%'"></div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
  
  <!-- Toggle areas button -->
  <div class="mt-3">
    <fc-button params="
      color: 'primary', 
      mode: 'text', 
      label: showAreas() ? 'Скрыть области' : 'Показать области',
      click: toggleAreas
    "></fc-button>
  </div>
</div>
```

### 5. Time to First Click Distribution

Bar chart showing the distribution of time taken to make the first click:

```html
<div class="time-chart-container" data-bind="slide: mode() === 'time'">
  <div class="question-statistics__variant-statistics-chart" 
       data-bind="attr: { id: chartIds.timeBar }">
  </div>
</div>
```

### 6. Click Zones Table

Detailed table showing statistics for each defined area:

```html
<div class="question-statistics__variant-statistics-legend poll-stats-legend">
  <table class="table foq-table mb-0">
    <thead class="position-sticky sticky-top bg-white">
      <tr>
        <th>Точка клика</th>
        <th class="text-right">Кол-во кликов</th>
        <th class="text-right">Процент</th>
      </tr>
    </thead>
    <tbody>
      <!-- ko foreach: processedAreas -->
      <tr class="cursor-pointer" data-bind="click: function() { $parent.onAreaClick($data); }">
        <td>
          <div class="d-flex align-items-center">
            <div class="color-indicator mr-2" 
                 data-bind="style: { backgroundColor: $parent.question.colors[$index()] }">
            </div>
            <span data-bind="text: name"></span>
          </div>
        </td>
        <td class="text-right" data-bind="text: clicks"></td>
        <td class="text-right" data-bind="text: percentage + '%'"></td>
      </tr>
      <!-- /ko -->
      
      <!-- Пользовательские точки row -->
      <!-- ko if: question.statistics.userDefinedPointsClicks > 0 -->
      <tr class="text-muted">
        <td>
          <div class="d-flex align-items-center">
            <div class="color-indicator mr-2" style="background-color: #ccc;"></div>
            <span>Пользовательские точки</span>
          </div>
        </td>
        <td class="text-right" data-bind="text: question.statistics.userDefinedPointsClicks"></td>
        <td class="text-right" data-bind="text: question.statistics.userDefinedPointsPercent + '%'"></td>
      </tr>
      <!-- /ko -->

      <!-- Респондент отказался от ответа row -->
      <!-- ko if: question.statistics.skippedCount > 0 -->
      <tr class="text-muted">
        <td>
          <div class="d-flex align-items-center">
            <div class="color-indicator mr-2" style="background-color: #B0BEC5;"></div>
            <span>Респондент отказался от ответа</span>
          </div>
        </td>
        <td class="text-right" data-bind="text: question.statistics.skippedCount"></td>
        <td class="text-right" data-bind="text: question.statistics.skippedPercent + '%'"></td>
      </tr>
      <!-- /ko -->
    </tbody>
  </table>
</div>
```

## Implementation Details

### Data Structure

The server should provide data in this format:

```javascript
{
  id: 123,
  question_id: 456,
  type: "24", // FIRST_CLICK_TEST constant
  name: "Куда бы вы кликнули в этом интерфейсе?",
  answersCount: 1206,
  
  // Image data
  imageUrl: "/uploads/first-click-test/image-123.jpg",
  imageWidth: 1200,
  imageHeight: 800,
  
  // Click areas definition
  clickAreas: [
    {
      id: "area_1",
      name: "Кнопка входа",
      x: 10,      // percentage from left
      y: 20,      // percentage from top
      width: 15,  // percentage width
      height: 8   // percentage height
    },
    {
      id: "area_2",
      name: "Область клика с длинным наименованием",
      x: 30,
      y: 40,
      width: 20,
      height: 10
    }
  ],
  
  // Statistics data
  statistics: {
    totalClicks: 980,
    avgExecutionTime: 174, // seconds

    // Clicks per area
    areaClicks: {
      "area_1": 570,
      "area_2": 212
    },

    // All click points for heatmap
    clickPoints: [
      { x: 12.5, y: 22.3, time: 120 },
      { x: 13.1, y: 23.5, time: 145 },
      // ... more points
    ],

    // Time distribution data
    timeDistribution: [
      { range: "0-30 сек", count: 120 },
      { range: "30-60 сек", count: 280 },
      { range: "60-120 сек", count: 350 },
      { range: "120-180 сек", count: 180 },
      { range: "180+ сек", count: 50 }
    ],

    // Clicks in user-defined points (previously outside areas)
    userDefinedPointsClicks: 195,
    userDefinedPointsPercent: 19.5,

    // Skipped answers
    skippedCount: 3, // Example value
    skippedPercent: 0.3 // Example value
  }
}
```

### Highcharts Heatmap Configuration

The heatmap overlay requires special configuration to work with an image background:

```javascript
initHeatmap() {
  // Get image dimensions for proper scaling
  const img = new Image();
  img.onload = () => {
    const aspectRatio = img.height / img.width;
    const containerWidth = document.getElementById('heatmap-' + this.id).offsetWidth;
    const chartHeight = containerWidth * aspectRatio;
    
    this.createHeatmapChart(containerWidth, chartHeight);
  };
  img.src = this.imageUrl;
}

createHeatmapChart(width, height) {
  // Convert click points to heatmap data
  const heatmapData = this.generateHeatmapData();
  
  this.charts.heatmap = Highcharts.chart('heatmap-' + this.id, {
    chart: {
      type: 'heatmap',
      backgroundColor: 'transparent',
      width: width,
      height: height,
      margin: [0, 0, 0, 0],
      spacing: [0, 0, 0, 0]
    },
    
    title: { text: null },
    credits: { enabled: false },
    legend: { enabled: false },
    
    xAxis: {
      min: 0,
      max: 100,
      visible: false
    },
    
    yAxis: {
      min: 0,
      max: 100,
      reversed: true, // Important for correct Y positioning
      visible: false
    },
    
    colorAxis: {
      min: 0,
      minColor: 'rgba(255, 255, 255, 0)',
      maxColor: 'rgba(255, 0, 0, 0.8)',
      stops: [
        [0, 'rgba(255, 255, 255, 0)'],
        [0.1, 'rgba(255, 255, 0, 0.3)'],
        [0.5, 'rgba(255, 165, 0, 0.5)'],
        [0.9, 'rgba(255, 0, 0, 0.7)'],
        [1, 'rgba(139, 0, 0, 0.8)']
      ]
    },
    
    tooltip: {
      formatter: function() {
        return `Кликов в этой области: <b>${this.point.value}</b>`;
      }
    },
    
    series: [{
      type: 'heatmap',
      data: heatmapData,
      borderWidth: 0,
      nullColor: 'transparent',
      turboThreshold: 0,
      interpolation: true, // Enable smooth interpolation
      boostThreshold: 1,
      
      // Custom interpolation settings
      colsize: 2, // Grid cell size
      rowsize: 2
    }],
    
    plotOptions: {
      heatmap: {
        // Enable interpolation for smooth gradients
        interpolation: true
      }
    }
  });
}

generateHeatmapData() {
  // Create a grid for heatmap data
  const gridSize = 2; // 2% grid cells
  const heatmapGrid = {};
  
  // Initialize grid
  for (let x = 0; x <= 100; x += gridSize) {
    for (let y = 0; y <= 100; y += gridSize) {
      const key = `${x},${y}`;
      heatmapGrid[key] = 0;
    }
  }
  
  // Aggregate clicks into grid cells
  this.question.statistics.clickPoints.forEach(point => {
    const gridX = Math.floor(point.x / gridSize) * gridSize;
    const gridY = Math.floor(point.y / gridSize) * gridSize;
    const key = `${gridX},${gridY}`;
    
    if (heatmapGrid[key] !== undefined) {
      heatmapGrid[key]++;
    }
  });
  
  // Convert to Highcharts format
  const data = [];
  Object.keys(heatmapGrid).forEach(key => {
    const [x, y] = key.split(',').map(Number);
    if (heatmapGrid[key] > 0) {
      data.push({
        x: x,
        y: y,
        value: heatmapGrid[key]
      });
    }
  });
  
  return data;
}
```

### Time Distribution Bar Chart

Configuration for the time-to-first-click distribution:

```javascript
initTimeChart() {
  const timeData = this.question.statistics.timeDistribution;
  
  this.charts.timeBar = Highcharts.chart(this.chartIds.timeBar, {
    chart: {
      type: 'column',
      height: 300
    },
    
    title: { text: null },
    credits: { enabled: false },
    legend: { enabled: false },
    
    xAxis: {
      categories: timeData.map(item => item.range),
      title: {
        text: 'Время до первого клика'
      }
    },
    
    yAxis: {
      title: {
        text: 'Количество пользователей'
      }
    },
    
    tooltip: {
      formatter: function() {
        return `${this.x}: <b>${this.y}</b> пользователей`;
      }
    },
    
    plotOptions: {
      column: {
        dataLabels: {
          enabled: true,
          format: '{y}'
        },
        colorByPoint: true
      }
    },
    
    colors: ['#3F51B5', '#536DFE', '#82B1FF', '#84ffff', '#aadbff'],
    
    series: [{
      name: 'Пользователи',
      data: timeData.map(item => item.count)
    }]
  });
}
```

### Zone Distribution Pie Chart

Configuration for click distribution across zones:

```javascript
get pieChartData() {
  const data = this.processedAreas.map(area => ({
    name: area.name,
    y: area.clicks,
    percentage: area.percentage
  }));

  // Add user-defined points if any
  if (this.question.statistics.userDefinedPointsClicks > 0) {
    data.push({
      name: 'Пользовательские точки',
      y: this.question.statistics.userDefinedPointsClicks,
      percentage: this.question.statistics.userDefinedPointsPercent,
      color: '#DADFE3'
    });
  }

  // Add skipped answers if any
  if (this.question.statistics.skippedCount > 0) {
    data.push({
      name: 'Респондент отказался от ответа',
      y: this.question.statistics.skippedCount,
      percentage: this.question.statistics.skippedPercent,
      color: '#B0BEC5'
    });
  }

  return [{
    name: 'Распределение кликов',
    data: data
  }];
}
```

## Code Examples

### Complete Question Model Implementation

```javascript
// ko/pages/poll/stats/question-types/first-click-test/index.js

import { Question } from "../models/question";
import "./component";
import "./style.less";

export class FirstClickTestQuestion extends Question {
  constructor(questionData, ctx) {
    super(questionData, ctx);

    // Extract question-specific data
    [
      "statistics",
      "clickAreas",
      "imageUrl",
      "imageWidth",
      "imageHeight"
    ].forEach((key) => {
      this[key] = questionData[key];
    });

    // Initialize computed values
    this.totalClicks = this.statistics.totalClicks || 0;
    this.avgExecutionTime = this.statistics.avgExecutionTime || 0;

    // Process click areas with statistics
    this.processedAreas = this.processClickAreas();
    
    // Set up observables for UI interactions
    this.selectedArea = ko.observable(null);
    this.showHeatmap = ko.observable(true);
  }

  // Process click areas with their statistics
  processClickAreas() {
    return this.clickAreas.map((area, index) => ({
      ...area,
      clicks: this.statistics.areaClicks[area.id] || 0,
      percentage: this.totalClicks > 0
        ? ((this.statistics.areaClicks[area.id] || 0) / this.totalClicks * 100).toFixed(1)
        : 0,
      color: this.colors[index % this.colors.length]
    }));
  }

  // Required: Define chart data getters
  get columnChartData() {
    return this.processedAreas.map((area, index) => ({
      name: area.name,
      data: [area.clicks],
      color: area.color
    }));
  }

  get pieChartData() {
    const data = this.processedAreas.map(area => ({
      name: area.name,
      y: area.clicks,
      percentage: area.percentage
    }));

    if (this.statistics.userDefinedPointsClicks > 0) {
      data.push({
        name: 'Пользовательские точки',
        y: this.statistics.userDefinedPointsClicks,
        percentage: this.statistics.userDefinedPointsPercent,
        color: '#DADFE3'
      });
    }

    if (this.statistics.skippedCount > 0) {
      data.push({
        name: 'Респондент отказался от ответа',
        y: this.statistics.skippedCount,
        percentage: this.statistics.skippedPercent,
        color: '#B0BEC5'
      });
    }

    return [{
      name: 'Распределение кликов',
      data: data
    }];
  }

  // Required: Define colors for charts
  get colors() {
    return [
      "#3F51B5", "#536DFE", "#82B1FF", "#84ffff",
      "#aadbff", "#bdb2ff", "#ff9dd8", "#ffbdb4"
    ];
  }

  // Required: Get total response count
  get totalCount() {
    return this.answersCount;
  }

  // Handle area click for detailed view
  openAreaModal(area) {
    super._openVariantModalDialog({
      variant: area,
      params: {
        field: "click_area",
        value: area.id,
      },
      title: `Клиенты, кликнувшие на область "${area.name}"`
    });
  }

  // Open modal for all clicks
  openAllClicksModal() {
    this.ctx.openStatsModal("stats-first-click-sidesheet", {
      question: this,
      title: "Все клики"
    });
  }

  // Open heatmap details modal
  openHeatmapModal() {
    this.ctx.openStatsModal("stats-heatmap-details-sidesheet", {
      question: this,
      title: "Тепловая карта кликов"
    });
  }
}
```

### Complete Component Implementation

```javascript
// ko/pages/poll/stats/question-types/first-click-test/component.js

import { QuestionStats } from '../models/component';

class ViewModel extends QuestionStats {
  constructor(params) {
    super({
      name: 'first-click-test',
      defaultChart: 'heatmap',
      charts: ['heatmap', 'time', 'zones'],
      question: params.question,
    });

    this.imageUrl = this.question.imageUrl;
    this.processedAreas = this.question.processedAreas;
    this.showAreas = ko.observable(false);
    
    // Chart mode observable
    this.mode = ko.observable('heatmap');
    
    // Heatmap specific properties
    this.heatmapData = this.generateHeatmapData();
    this.heatmapInitialized = false;
  }

  generateHeatmapData() {
    const gridSize = 2; // 2% grid cells
    const heatmapGrid = {};
    
    // Initialize grid
    for (let x = 0; x <= 100; x += gridSize) {
      for (let y = 0; y <= 100; y += gridSize) {
        const key = `${x},${y}`;
        heatmapGrid[key] = 0;
      }
    }
    
    // Aggregate clicks into grid cells
    this.question.statistics.clickPoints.forEach(point => {
      const gridX = Math.floor(point.x / gridSize) * gridSize;
      const gridY = Math.floor(point.y / gridSize) * gridSize;
      const key = `${gridX},${gridY}`;
      
      if (heatmapGrid[key] !== undefined) {
        heatmapGrid[key]++;
      }
    });
    
    // Convert to Highcharts format
    const data = [];
    Object.keys(heatmapGrid).forEach(key => {
      const [x, y] = key.split(',').map(Number);
      if (heatmapGrid[key] > 0) {
        data.push({
          x: x,
          y: y,
          value: heatmapGrid[key]
        });
      }
    });
    
    return data;
  }

  toggleAreas() {
    this.showAreas(!this.showAreas());
  }

  onAreaClick(area) {
    this.question.openAreaModal(area);
  }

  onAllClicksClick() {
    this.question.openAllClicksModal();
  }

  initHeatmap() {
    if (this.heatmapInitialized) return;
    
    // Wait for image to load to get dimensions
    const img = new Image();
    img.onload = () => {
      const container = document.getElementById('heatmap-' + this.id);
      if (!container) return;
      
      const aspectRatio = img.height / img.width;
      const containerWidth = container.offsetWidth;
      const chartHeight = containerWidth * aspectRatio;
      
      this.createHeatmapChart(containerWidth, chartHeight);
      this.heatmapInitialized = true;
    };
    img.src = this.imageUrl;
  }

  createHeatmapChart(width, height) {
    this.charts.heatmap = Highcharts.chart('heatmap-' + this.id, {
      chart: {
        type: 'heatmap',
        backgroundColor: 'transparent',
        width: width,
        height: height,
        margin: [0, 0, 0, 0],
        spacing: [0, 0, 0, 0]
      },
      
      title: { text: null },
      credits: { enabled: false },
      legend: { enabled: false },
      
      xAxis: {
        min: 0,
        max: 100,
        visible: false
      },
      
      yAxis: {
        min: 0,
        max: 100,
        reversed: true,
        visible: false
      },
      
      colorAxis: {
        min: 0,
        minColor: 'rgba(255, 255, 255, 0)',
        maxColor: 'rgba(255, 0, 0, 0.8)',
        stops: [
          [0, 'rgba(255, 255, 255, 0)'],
          [0.1, 'rgba(255, 255, 0, 0.3)'],
          [0.5, 'rgba(255, 165, 0, 0.5)'],
          [0.9, 'rgba(255, 0, 0, 0.7)'],
          [1, 'rgba(139, 0, 0, 0.8)']
        ]
      },
      
      tooltip: {
        formatter: function() {
          return `Кликов в этой области: <b>${this.point.value}</b>`;
        }
      },
      
      series: [{
        type: 'heatmap',
        data: this.heatmapData,
        borderWidth: 0,
        nullColor: 'transparent',
        turboThreshold: 0,
        interpolation: true,
        boostThreshold: 1,
        colsize: 2,
        rowsize: 2
      }],
      
      plotOptions: {
        heatmap: {
          interpolation: true
        }
      }
    });
  }

  initTimeChart() {
    const timeData = this.question.statistics.timeDistribution;
    
    this.charts.timeBar = Highcharts.chart(this.chartIds.timeBar, {
      chart: {
        type: 'column',
        height: 300
      },
      
      title: { text: null },
      credits: { enabled: false },
      legend: { enabled: false },
      
      xAxis: {
        categories: timeData.map(item => item.range),
        title: {
          text: 'Время до первого клика'
        }
      },
      
      yAxis: {
        title: {
          text: 'Количество пользователей'
        }
      },
      
      tooltip: {
        formatter: function() {
          return `${this.x}: <b>${this.y}</b> пользователей`;
        }
      },
      
      plotOptions: {
        column: {
          dataLabels: {
            enabled: true,
            format: '{y}'
          },
          colorByPoint: true
        }
      },
      
      colors: ['#3F51B5', '#536DFE', '#82B1FF', '#84ffff', '#aadbff'],
      
      series: [{
        name: 'Пользователи',
        data: timeData.map(item => item.count)
      }]
    });
  }

  initZonesChart() {
    return super.initPieChart({
      plotOptions: {
        pie: {
          dataLabels: {
            enabled: true,
            format: '<b>{point.name}</b>: {point.percentage:.1f}%'
          }
        }
      }
    });
  }

  onInit() {
    // Initialize charts based on current mode
    this.mode.subscribe((newMode) => {
      setTimeout(() => {
        if (newMode === 'heatmap') this.initHeatmap();
        if (newMode === 'time') this.initTimeChart();
        if (newMode === 'zones') this.initZonesChart();
      }, 100);
    });
    
    // Initialize default view
    if (this.mode() === 'heatmap') this.initHeatmap();
  }
}

// Register the component
ko.components.register('first-click-test-question-stats', {
  viewModel: ViewModel,
  template: {
    element: 'first-click-test-question-stats-template'
  }
});
```

### Complete Template Implementation

```php
<!-- ko/pages/poll/stats/question-types/first-click-test/template.php -->

<template id="first-click-test-question-stats-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  
  <div class="poll-stats poll-stats--first-click-test">
    <!-- Summary Statistics -->
    <div class="d-flex flex-wrap mt-10p mt-md-20p f-fs-2 stats-summary">
      <div class="mr-4 mb-md-3 mb-1">
        <span class="f-color-service">Всего кликов: </span>
        <span class="bold" data-bind="text: question.totalClicks"></span>
      </div>
      <div class="mr-4 mb-md-3 mb-1">
        <span class="f-color-service">Среднее время выполнения, сек: </span>
        <span class="bold" data-bind="text: question.avgExecutionTime + ' сек'"></span>
      </div>
    </div>

    <!-- Visualization Container -->
    <div class="question-statistics__variant-statistics">
      <!-- Chart Mode Toggle -->
      <div class="question-statistics__variant-statistics-chart-wrapper">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <div class="question-statistics__variant-statistics-mode-toggle">
            <button class="btn btn-icon btn-icon--simple" 
                    title="Тепловая карта"
                    data-bind="click: function() { mode('heatmap'); }, 
                               css: { 'active': mode() === 'heatmap' }">
              <svg-icon params="name: 'heatmap'"></svg-icon>
            </button>
            <button class="btn btn-icon btn-icon--simple" 
                    title="Время до клика"
                    data-bind="click: function() { mode('time'); }, 
                               css: { 'active': mode() === 'time' }">
              <svg-icon params="name: 'chart-column'"></svg-icon>
            </button>
            <button class="btn btn-icon btn-icon--simple" 
                    title="Распределение по зонам"
                    data-bind="click: function() { mode('zones'); }, 
                               css: { 'active': mode() === 'zones' }">
              <svg-icon params="name: 'chart-pie'"></svg-icon>
            </button>
          </div>

          <!-- Toggle overlay button -->
          <!-- ko if: mode() === 'heatmap' -->
          <fc-button params="
            color: 'primary', 
            mode: 'text', 
            label: showAreas() ? 'Скрыть области' : 'Показать области',
            click: toggleAreas
          "></fc-button>
          <!-- /ko -->
        </div>

        <!-- Heatmap View -->
        <div class="heatmap-container" data-bind="slide: mode() === 'heatmap'">
          <div class="image-wrapper position-relative">
            <img data-bind="attr: { src: imageUrl }" class="img-fluid" />
            
            <!-- Heatmap overlay -->
            <div class="heatmap-overlay" data-bind="attr: { id: 'heatmap-' + $component.id }"></div>
            
            <!-- Click areas overlay -->
            <!-- ko if: showAreas -->
            <div class="click-areas-overlay">
              <!-- ko foreach: processedAreas -->
              <div class="click-area" 
                   data-bind="
                     style: {
                       left: x + '%',
                       top: y + '%',
                       width: width + '%',
                       height: height + '%'
                     },
                     click: function() { $parent.onAreaClick($data); },
                     attr: { title: name + ': ' + clicks + ' кликов (' + percentage + '%)' }
                   ">
                <div class="area-label" data-bind="text: percentage + '%'"></div>
              </div>
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
        </div>

        <!-- Time Chart -->
        <div class="time-chart-container" 
             data-bind="slide: mode() === 'time', 
                        attr: { id: chartIds.timeBar }">
        </div>

        <!-- Zones Pie Chart -->
        <div class="zones-chart-container" 
             data-bind="slide: mode() === 'zones', 
                        attr: { id: chartIds.pie }">
        </div>
      </div>

      <!-- Areas Table -->
      <div class="question-statistics__variant-statistics-legend poll-stats-legend">
        <table class="table foq-table mb-0">
          <thead class="position-sticky sticky-top bg-white">
            <tr>
              <th>Точка клика</th>
              <th class="text-right">Кол-во кликов</th>
              <th class="text-right">Процент</th>
            </tr>
          </thead>
          <tbody>
            <!-- ko foreach: processedAreas -->
            <tr class="cursor-pointer" data-bind="click: function() { $parent.onAreaClick($data); }">
              <td>
                <div class="d-flex align-items-center">
                  <div class="color-indicator mr-2" 
                       data-bind="style: { backgroundColor: color }">
                  </div>
                  <span data-bind="text: name"></span>
                </div>
              </td>
              <td class="text-right" data-bind="text: clicks"></td>
              <td class="text-right" data-bind="text: percentage + '%'"></td>
            </tr>
            <!-- /ko -->
            
            <!-- Пользовательские точки row -->
            <!-- ko if: question.statistics.userDefinedPointsClicks > 0 -->
            <tr class="text-muted">
              <td>
                <div class="d-flex align-items-center">
                  <div class="color-indicator mr-2" style="background-color: #ccc;"></div>
                  <span>Пользовательские точки</span>
                </div>
              </td>
              <td class="text-right" data-bind="text: question.statistics.userDefinedPointsClicks"></td>
              <td class="text-right" data-bind="text: question.statistics.userDefinedPointsPercent + '%'"></td>
            </tr>
            <!-- /ko -->

            <!-- Респондент отказался от ответа row -->
            <!-- ko if: question.statistics.skippedCount > 0 -->
            <tr class="text-muted">
              <td>
                <div class="d-flex align-items-center">
                  <div class="color-indicator mr-2" style="background-color: #B0BEC5;"></div>
                  <span>Респондент отказался от ответа</span>
                </div>
              </td>
              <td class="text-right" data-bind="text: question.statistics.skippedCount"></td>
              <td class="text-right" data-bind="text: question.statistics.skippedPercent + '%'"></td>
            </tr>
            <!-- /ko -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- View all clicks button -->
    <a class="no-print question-statistics__question-additional-button" 
       data-bind="click: onAllClicksClick">
      Все клики
    </a>
  </div>
  
  <!-- /ko -->
</template>
```

### Styles Implementation

```less
// ko/pages/poll/stats/question-types/first-click-test/style.less

.poll-stats--first-click-test {
  .stats-summary {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  .heatmap-container {
    position: relative;
    max-width: 100%;
    margin: 20px 0;
    
    .image-wrapper {
      position: relative;
      display: inline-block;
      max-width: 100%;
      width: 100%;
      
      img {
        max-width: 100%;
        height: auto;
        display: block;
      }
    }
    
    .heatmap-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }
    
    .click-areas-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      
      .click-area {
        position: absolute;
        border: 2px solid #3F51B5;
        background-color: rgba(63, 81, 181, 0.2);
        cursor: pointer;
        pointer-events: all;
        transition: all 0.2s ease;
        
        &:hover {
          background-color: rgba(63, 81, 181, 0.4);
          border-color: #303F9F;
          z-index: 10;
        }
        
        .area-label {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(255, 255, 255, 0.95);
          padding: 4px 8px;
          border-radius: 4px;
          font-weight: bold;
          font-size: 12px;
          white-space: nowrap;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
      }
    }
  }
  
  .time-chart-container,
  .zones-chart-container {
    min-height: 300px;
    margin: 20px 0;
  }
  
  .color-indicator {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    flex-shrink: 0;
  }
  
  .question-statistics__variant-statistics-mode-toggle {
    button {
      &.active {
        background-color: #e3f2fd;
        color: #1976d2;
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .stats-summary {
      > div {
        width: 50%;
        margin-right: 0 !important;
      }
    }
    
    .heatmap-container {
      .click-area {
        .area-label {
          font-size: 10px;
          padding: 2px 4px;
        }
      }
    }
  }
}
```

## Integration Steps

### 1. Register Question Type in Factory

Add to `ko/pages/poll/stats/question-types/index.js`:

```javascript
import { FirstClickTestQuestion } from './first-click-test';

const models = {
  // ... existing models
  'first-click-test': FirstClickTestQuestion,
};
```

### 2. Include Template

Add to `ko/pages/poll/stats/template.php`:

```php
<?php include __DIR__ . '/question-types/first-click-test/template.php'; ?>
```

### 3. Create Directory Structure

```
ko/pages/poll/stats/question-types/first-click-test/
├── index.js          # Question model
├── component.js      # UI component
├── template.php      # HTML template
└── style.less        # Styles
```

### 4. Export Functionality

The heatmap and charts can be exported using Highcharts' built-in export functionality:

```javascript
// Add export button to template
<button class="btn btn-sm btn-secondary" 
        data-bind="click: exportHeatmap">
  Экспорт тепловой карты
</button>

// Add method to component
exportHeatmap() {
  if (this.charts.heatmap) {
    this.charts.heatmap.exportChart({
      type: 'image/png',
      filename: 'heatmap-' + this.question.id
    });
  }
}
```

## Responsive Design Considerations

1. **Image Scaling**: The heatmap overlay scales with the image maintaining aspect ratio
2. **Mobile View**: Areas and labels adjust size for smaller screens
3. **Table Scrolling**: The areas table is scrollable on mobile devices
4. **Chart Sizing**: Charts adapt to container width

## Performance Optimizations

1. **Lazy Loading**: Charts are initialized only when their tab is selected
2. **Data Aggregation**: Click points are aggregated into grid cells for better performance
3. **Image Caching**: The base image is cached by the browser
4. **Debounced Resize**: Chart resizing is debounced for window resize events

## Accessibility Features

1. **Keyboard Navigation**: All interactive elements are keyboard accessible
2. **ARIA Labels**: Proper ARIA labels for screen readers
3. **Color Contrast**: Sufficient contrast for all text elements
4. **Focus Indicators**: Clear focus indicators for interactive elements

## Error Handling

```javascript
// Handle missing data gracefully
processClickAreas() {
  if (!this.clickAreas || !Array.isArray(this.clickAreas)) {
    console.warn('No click areas defined for question', this.id);
    return [];
  }
  
  return this.clickAreas.map((area, index) => {
    try {
      return {
        ...area,
        clicks: this.statistics?.areaClicks?.[area.id] || 0,
        // ... other processing
      };
    } catch (error) {
      console.error('Error processing area', area, error);
      return null;
    }
  }).filter(Boolean);
}
```

## Testing Checklist

- [ ] Heatmap renders correctly over the image
- [ ] Click areas are positioned accurately
- [ ] Time distribution chart displays correct data
- [ ] Zone distribution pie chart shows percentages (including user points and skipped)
- [ ] Area table displays correct columns (Точка клика, Кол-во кликов, Процент) and rows (defined areas, пользовательские точки, респондент отказался)
- [ ] Modal dialogs open with correct data
- [ ] Export functionality works for all charts
- [ ] Responsive design works on mobile devices
- [ ] Performance is acceptable with large datasets
- [ ] Error states are handled gracefully

This comprehensive guide provides all the necessary components and implementation details for the first click test statistics visualization. The implementation follows existing patterns in the FOQUZ system while introducing new visualization capabilities specific to click tracking and heatmap analysis.