<div class="view-condition__wrapper">
    <!-- ko ifnot: blocked -->
    <div class="view-condition__handle">
        <fc-drag-icon class="drag-handle ui-sortable-handle"></fc-drag-icon>
    </div>
    <!-- /ko -->
    <div class="view-condition__content">
        <div class="row">
            <div class="col-6">
                <div class="form-group">
                    <fc-label params="text: translator.t('Отображение')"></fc-label>
                    <fc-select params="options: views, value: condition.view, disabled: blocked"></fc-select>
                </div>
            </div>
            <div class="col-6">
                <div class="form-group">
                    <fc-label params="text: translator.t('Тип условия')"></fc-label>
                    <fc-select params="options: types, value: condition.type, disabled: blocked"></fc-select>
                    <!-- ko template: {
                         foreach: templateIf(isParam(), $data),
                         afterAdd: slideAfterAddFactory(400),
                         beforeRemove: slideBeforeRemoveFactory(400)
                      } -->
                    <a
                            href="https://foquz.ru/foquz/user-wiki?id=28"
                            target="_blank"
                            class="params-link"
                            data-bind="text: translator.t('Как это работает')"
                    ></a>
                    <!-- /ko -->
                </div>
            </div>
        </div>

        <!-- ko template: {
            foreach: templateIf(isFiveSecondTest(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
          } -->
        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <!-- ko ifnot: questionsFiveSecondTest.length -->
                    <div
                            class="f-color-danger d-flex align-items-center"
                            style="font-size: 13px"
                    >
                        <fc-icon
                                params="name: 'exclamation-circle-filled'"
                                class="mr-2"
                        ></fc-icon>
                        В опросе нет вопросов с типом «Тест 5 секунд». Выберите другое условие.
                    </div>
                    <!-- /ko -->

                    <!-- ko if: questionsFiveSecondTest.length -->
                    <fc-label params="text: translator.t('Вопрос')"></fc-label>
                    <fc-select
                            params="options: questionsFiveSecondTest, value: condition.question, invalid: errorMatcher(condition.question), disabled: blocked"
                    ></fc-select>
                    <fc-error
                            params="show: errorMatcher(condition.question), text: condition.question.error"
                    ></fc-error>
                    <!-- /ko -->
                </div>
            </div>  
        </div>
        <!-- /ko -->

        <!-- ko template: {
            foreach: templateIf(isQuestion(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
          } -->
        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <!-- ko ifnot: questions.length -->
                    <div
                            class="f-color-danger d-flex align-items-center"
                            style="font-size: 13px"
                    >
                        <fc-icon
                                params="name: 'exclamation-circle-filled'"
                                class="mr-2"
                        ></fc-icon>
                        Нет подходящих вопросов в опросе. Выберите другое условие.
                    </div>
                    <!-- /ko -->

                    <!-- ko if: questions.length -->
                    <fc-label params="text: translator.t('Вопрос')"></fc-label>
                    <fc-select
                            params="options: questions, value: condition.question, invalid: errorMatcher(condition.question), disabled: blocked"
                    ></fc-select>
                    <fc-error
                            params="show: errorMatcher(condition.question), text: condition.question.error"
                    ></fc-error>
                    <!-- /ko -->
                </div>

                <!-- ko template: {
                   foreach: templateIf(condition.question() && isQuestion(), $data),
                   afterAdd: slideAfterAddFactory(400),
                   beforeRemove: slideBeforeRemoveFactory(400)
                } -->
                <div
                    class="form-group view-conditions__variants"
                    data-bind="
                        css: {
                            'view-conditions__variants--blocked': blocked && !isClassifier,
                        },
                    "
                >
                    <fc-label params="text: translator.t('Варианты ответа')"></fc-label>

                    <!-- ko if: isScale -->
                    <div class="question-variants__slider__block">
                        <div class="question-variants__slider">
                            <interval-slider
                                    params="range: pointsRange, minLimit: rangeMinValue, maxLimit: rangeMaxValue, withIndicator: false"></interval-slider>
                        </div>
                        <input type="text" class="fa-text-input form-control"
                               data-bind="value: pointsRange()[0], event: { change: function() { pointsRange.valueHasMutated(); } }"/>
                        <div class="form-control__line">—</div>
                        <input type="text" class="fa-text-input form-control"
                               data-bind="value: pointsRange()[1], event: { change: function() { pointsRange.valueHasMutated(); } }"/>


                    </div>
                    <!-- /ko -->

                    <!-- ko if: isClassifier -->
                    <!-- ko if: condition.tree -->
                    <!-- ko using: condition.tree -->
                    <logic-checked-tree
                        params="
                            tree: $data,
                            blocked: $parent.blocked,
                        "
                    ></logic-checked-tree>
                    <!-- /ko -->
                    <!-- /ko -->

                    <!-- /ko -->

                    <!-- ko if: isMatrix -->
                    <!-- ko if: showMatrix -->
                    <variants-matrix
                        params="
                            matrix: variants,
                            checked: condition.variants,
                            skipped: condition.skipped,
                            question: questionData,
                        "
                    ></variants-matrix>
                    <!-- /ko -->
                    <!-- /ko -->

                    <!-- ko if: !isMatrix() && !isClassifier() -->
                    <variants-list
                            params="variants: variants,
              checked: condition.variants, 
              logicType: logicType"
                    ></variants-list>
                    <!-- /ko -->
                    <fc-error
                            params="show: errorMatcher(condition.variants), text: condition.variants.error"
                    ></fc-error>

                </div>
                <!-- /ko -->
            </div>
        </div>
        <!-- /ko -->
        <!-- ko template: {
             foreach: templateIf(isParam(), $data),
             afterAdd: slideAfterAddFactory(400),
             beforeRemove: slideBeforeRemoveFactory(400)
          } -->
        <div class="row">
            <div class="col-6">
                <div class="form-group">
                    <fc-label params="text: translator.t('Параметр')"></fc-label>
                    <fc-input
                        params="
                            value: condition.param,
                            invalid: errorMatcher(condition.param),
                            maxlength: 500,
                            disabled: $data.blocked,
                        "
                    ></fc-input>
                    <fc-error
                        params="
                            show: errorMatcher(condition.param),
                            text: condition.param.error,
                        "
                    ></fc-error>
                </div>
            </div>
            <div class="col-6">
                <div class="form-group">
                    <fc-label params="text: translator.t('Условие')"></fc-label>
                    <fc-select
                        params="
                            options: conditions,
                            value: condition.condition,
                            disabled: $data.blocked,
                        "
                    ></fc-select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <fc-label params="text: translator.t('Значение')"></fc-label>
                    <fc-input
                        params="
                            value: condition.value,
                            invalid: errorMatcher(condition.value),
                            maxlength: 500,
                            disabled: $data.blocked,
                        "
                    ></fc-input>
                    <fc-error
                        params="
                            show: errorMatcher(condition.value),
                            text: condition.value.error
                        "
                    ></fc-error>
                </div>
            </div>
        </div>
        <!-- /ko -->
    </div>
    <!-- ko ifnot: blocked -->
    <div class="view-condition__remove">
        <fc-button
                params="color: 'danger', icon: 'times', shape: 'square', click: function() {removeCondition()}"
        ></fc-button>
    </div>
    <!-- /ko -->
</div>
