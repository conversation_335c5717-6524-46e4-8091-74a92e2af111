@import '../../../less/main.less';
@import 'Style/colors';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box; 
}

body {
  font-family: 'Roboto';
}

.support-center {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .request-form-field {
    .fc-calendar {
      max-width: 385px;
    }

    &[data-name="phone"] {
      .fc-input {
        max-width: 385px;
      }

    }
  }

  &__background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/img/themes/background4.jpg');
    background-size: cover;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.3);
    }
  }

  .fc-spinner {
    color: white;
  }

  .fc-check--checkbox {
    .fc-check__box {
      border-radius: 3px;
    }
  }

  &__page {
    flex-grow: 1;
    position: relative;
    z-index: 1;
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .inner {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  &__header {
    font-size: 20px;
    font-weight: 900;
    text-align: center;
    margin-top: 18px;
    margin-bottom: 20px;
    padding-bottom: 0;
    color: white;
  }

  &__search {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 30px;
    max-width: 570px;
    width: 100%;

    .fc-input__wrapper {
      height: 50px;
    }
  }

  &__projects {
    max-width: 1180px;
    margin: auto;
  }

  .support-projects {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  .support-project-card {
    margin-bottom: 20px;

    width: 100%;
    min-height: 150px;

    background-color: white;
    border-radius: 9px;
    padding: 25px;

    text-decoration: none;
    cursor: pointer;

    &__name {
      color: @f-color-text;
      font-size: 16px;
      font-weight: 700;
    }

    &__description {
      color: @f-color-service;
      font-size: 12px;
      margin-top: 6px;
      line-height: 1.4;
    }

    &:hover {
      box-shadow: 0px 0px 30px rgba(63, 101, 241, 0.5);
      .support-project-card__name {
        color: @f-color-primary;
      }
    }
  }

  &__footer {
    color: white;
    text-align: center;
    padding-bottom: 15px;
    margin-top: 40px;
    font-weight: 400;
    font-size: 13px;

    img {
      margin-bottom: 10px;
    }
  }

  &__empty-message {
    text-align: center;
    font-size: 16px;
    color: white;
  }

  &__project {
    background: white;
    border-radius: 9px;
    padding: 25px 20px 30px;
    margin-left: -20px;
    margin-right: -20px;
    flex-grow: 1;
  }

  .support-project {
    .breadcrumbs {
      color: @f-color-service;
      margin-bottom: 16px;
      &-item {
        font-size: 12px;
        text-decoration: none;
        &:not(:last-child):after {
          content: '/';
          margin-left: 4px;
          margin-right: 4px;
          color: @f-color-service;
        }
      }
      a.breadcrumbs-item {
        color: @f-color-primary;
      }
    }

    &__form {
      margin-top: 30px;
    }

    .fc-spinner:not(.fc-btn-b__pending) {
      color: @f-color-primary;
    }

    .support-center__empty-message {
      color: @f-color-service;
    }

    &__name {
      font-size: 19px;
      font-weight: 700;
      margin: 0;
      padding: 0;
    }

    &__description {
      font-size: 12px;
      font-weight: 400;
      margin-top: 6px;
      color: @f-color-service;
    }

    &__themes {
      margin-top: 26px;
    }

    &-theme {
      border-bottom: 1px solid @f-color-border;
      padding-top: 16px;
      padding-bottom: 16px;
      min-height: 74px;
      display: flex;
      align-items: center;
      position: relative;
      cursor: pointer;

      &__wrapper {
        display: flex;
      }

      &:first-child {
        border-top: 1px solid @f-color-border;
      }

      &__icon {
        width: 40px;
        height: 40px;
        flex-shrink: 0;
        margin-right: 26px;
        position: relative;

        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          margin: auto;
          object-fit: contain;
          object-position: center;
        }
      }

      &__name {
        font-size: 14px;
        font-weight: 700;
      }

      &__description {
        font-size: 12px;
        color: @f-color-service;
        margin-top: 4px;
      }

      &:hover {
        border-color: transparent;
      }

      &:hover:after {
        content: '';
        position: absolute;
        top: -1px;
        left: -10px;
        right: -10px;
        bottom: -1px;
        box-shadow: 0px 0px 15px rgba(63, 101, 241, 0.5);
        border-radius: 9px;
        pointer-events: none;
      }
    }

    &__actions {
      .only-mobile({
        display: flex;
        margin-left: -15px;
        margin-right: -15px;
        .fc-btn {
          flex-grow: 1;
          margin: 0 15px;
          .fc-btn-b {
            width: 100%;
          }
        }
      })
    }
  }

  &-success {
    &__title {
      font-weight: bold;
      font-size: 19px;
      margin-top: 22px;
      margin-bottom: 8px;
    }
    &__description {
      font-size: 14px;
      margin-bottom: 20px;
    }
    &__back {
      .fc-btn-b {
        font-size: 12px;
      }
    }
  }

  @media screen and (min-width: 768px) {
    &__header {
      font-size: 26px;
      margin-top: 26px;
    }

    &__search {
      margin-bottom: 50px;
    }

    &__page {
      padding-left: 25px;
      padding-right: 25px;
    }
    .support-projects {
      margin-left: -14px;
      margin-right: -14px;
    }
    .support-project-card {
      width: calc(50% - 28px);
      min-width: 345px;
      max-width: 363px;
      margin-left: 14px;
      margin-right: 14px;
      margin-bottom: 40px;
      min-height: 180px;

      &__name {
        font-size: 19px;
      }

      &__description {
        font-size: 14px;
      }
    }

    &__project {
      padding: 26px 50px 50px;
      max-width: 900px;
      width: 100%;
      margin: auto;
    }

    &__footer {
      margin-top: 50px;
    }

    .support-project {
      .breadcrumbs {
        margin-bottom: 24px;
      }

      &__name {
        font-size: 22px;
      }

      &__description {
        font-size: 14px;
      }

      &__themes {
        margin-top: 30px;
      }

      &-theme {
        &__name {
          font-size: 16px;
        }
      }
    }

    &-success {
      &__title {
        font-size: 22px;
        margin-top: 30px;
      }
      &__description {
        font-size: 16px;
        margin-bottom: 25px;
      }
      &__back {
        .fc-btn-b {
          font-size: 14px;
        }
      }
    }
  }

  @media screen and (min-width: 1440px) {
    &__page {
      padding-left: 45px;
      padding-right: 45px;
    }
    .support-projects {
      margin-left: -22.5px;
      margin-right: -22.5px;
    }
    .support-project-card {
      width: calc(100% / 3 - 45px);
      margin-left: 22.5px;
      margin-right: 22.5px;
    }

    &__project {
      padding: 26px 50px 50px;
      max-width: 900px;
      width: 100%;
      margin: auto;
    }
  }
}
