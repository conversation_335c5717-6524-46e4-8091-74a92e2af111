import { DateMoment } from "@/types";
import { Genders } from "../../../constants/genders";
import { ServerDate } from "../../../types";

export type ContactVars = {
  id: number;
  created_at: DateMoment;
  updated_at: DateMoment;
  created_by: number;
  updated_by: number;
  company_id: number;
  last_name: string;
  first_name: string;
  patronymic: string;
  phone: string;
  email: string;
  gender: Genders;
  birthday: ServerDate;
  is_subscribed: 0 | 1;
  client_id: number;
  company_client_id: null;
  mobile_device_id: null;
  filials: Array<number>;
  filialNames: {
    [key: string]: string;
  };
};

export type Contact = {
  id: string;
}