/**
 * RequestAnswer
 *
 * Анкета-заявка в разделе Заявки
 */

import { ApiUrl } from "Utils/url/api-url";
import { ReviewProcessingEvent } from "@/utils/events/review";

export class RequestAnswer {
  constructor(data) {
    this.id = data.id;
    this.createdAtDate = moment(data.created_at, "YYYY-MM-DD HH:mm").format(
      "DD.MM.YYYY"
    );
    this.createdAtTime = moment(data.created_at, "YYYY-MM-DD HH:mm").format(
      "HH:mm"
    );

    this.project = data.project;
    this.projectName = this.project.name;

    this.theme = data.theme;
    this.themeName = this.theme?.name;
    this.description = data.description;

    this.priority = ko.observable(data.priority);

    this.client = data.client;
    this.clientSystemFields = {
      name: "",
      phone: "",
      email: "",
      filials: "",
      gender: "",
      birthday: "",
      tags: "",
      ltv: "",
      lastOrderDate: "",
      addedAt: "",
      updatedAt: "",
    };

    this.clientFields = {};
    this.clientApiFields = [];

    this.ordersCount = ko.observable(0);

    if (this.client) {
      this.setClientData(data);
    }

    this.moderator = ko.observable(null);
    this.executor = ko.observable(null);

    this.moderatorName = ko.pureComputed(() => {
      return this.moderator() && this.moderator().name;
    });

    this.executorName = ko.pureComputed(() => {
      return this.executor() && this.executor().name;
    });

    this.processing = ko.observable(null);
    this.processingStatus = ko.observable(0);

    this.processingFiles = ko.observableArray([]);

    let updatedAt = data.updated_at || data.created_at;

    this.updatedAtDate = ko.observable(
      moment(updatedAt, "YYYY-MM-DD HH:mm").format("DD.MM.YYYY")
    );
    this.updatedAtTime = ko.observable(
      moment(updatedAt, "YYYY-MM-DD HH:mm").format("HH:mm")
    );

    if (data.processing) {
      this.updateProcessing(data.processing);
    }

    this.answerFields = {};

    ["name", "description", "phone", "email", "priority"].forEach((key) => {
      this.answerFields[key] = data[key];
    });

    Object.keys(data.custom_fields).forEach((key) => {
      this.answerFields[key] = data.custom_fields[key];
    });

    this.answerFields.files = data.files.map((file) => {
      return {
        id: file.id,
        src: file.filename,
      };
    });
  }

  setClientData(data) {
    let client = data.client;

    if (!client) {
      return;
    }

    this.ordersCount(client.ordersCount);

    this.clientSystemFields.name = [
      client.last_name,
      client.first_name,
      client.patronymic,
    ]
      .filter(Boolean)
      .join(" ");
    this.clientSystemFields.phone = client.phone;
    this.clientSystemFields.email = client.email;
    this.clientSystemFields.filials = (client.filials || [])
      .map((fId) => {
        const filialName = client.filialNames[fId];
        if (!filialName) return false;
        const parts = filialName.split("/");
        if (parts.length > 1) {
          const [category, name] = parts;
          return `<span class="f-color-service">${category}</span>/${name}`;
        }
        return filialName;
      })
      .filter(Boolean)
      .join(", ");
    this.clientSystemFields.gender = client.gender;
    this.clientSystemFields.birthday = client.birthday
      ? moment(client.birthday, "YYYY-MM-DD").format("DD.MM.YYYY")
      : "";
    this.clientSystemFields.tags = (client.tags || []).map((t) => t.tag);

    this.clientSystemFields.addedAt = client.created_at
      ? moment(client.created_at * 1000).format("DD.MM.YYYY")
      : "";
    this.clientSystemFields.updatedAt = client.updated_at
      ? moment(client.updated_at * 1000).format("DD.MM.YYYY")
      : "";

    if (client.computedFields) {
      this.clientSystemFields.lastOrderDate = client.computedFields
        .last_order_date
        ? moment(this.client.computedFields.last_order_date).format(
            "DD.MM.YYYY"
          )
        : "";
      this.clientSystemFields.ltv = client.computedFields.ltv_amount;
    }

    if (client.additionalFields) {
      Object.keys(client.additionalFields).forEach((key) => {
        let value = client.additionalFields[key];
        if (value) {
          this.clientFields[key] = value;
        }
      });
    }

    if (client.customFields) {
      Object.keys(client.customFields).forEach((key) => {
        this.clientApiFields.push({
          id: key,
          text: client.customFields[key],
        });
      });
    }
  }

  updateProcessing(data) {
    this.processing(data);
    this.processingStatus(data.status);
    this.moderator(data.moderator);
    this.executor(data.executor);

    this.processingFiles(
      data.processingFiles.map((file) => {
        return {
          id: file.id,
          src: file.file_path,
        };
      })
    );

    if (data.updated_at) {
      this.updatedAtDate(
        moment(data.updated_at, "YYYY-MM-DD HH:mm").format("DD.MM.YYYY")
      );
      this.updatedAtTime(
        moment(data.updated_at, "YYYY-MM-DD HH:mm").format("HH:mm")
      );
    }
  }

  update(data) {
    return new Promise((res, rej) => {
      $.ajax({
        method: "PUT",
        url: ApiUrl("request-projects-processing/update", {
          id: this.processing().id,
        }),
        data,
        success: (response) => {
          this.updateProcessing(response.model);
          ReviewProcessingEvent.emit({
            id: this.id,
            data: response.model
          })
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          rej(response.responseJSON);
        },
      });
    });
  }

  getSystemField(fieldName) {
    if (!this.client) return;
    if (fieldName === "tags") return this.tagsString;
    if (fieldName === "gender") {
      if (this.clientSystemFields.gender == 1) return "муж.";
      if (this.clientSystemFields.gender == 2) return "жен.";
      return null;
    }
    return this.clientSystemFields[fieldName];
  }

  get tagsString() {
    return this.clientSystemFields.tags.join(", ");
  }
}
