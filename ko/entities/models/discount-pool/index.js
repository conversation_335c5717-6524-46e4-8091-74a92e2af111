/**
 * @class DiscountPool
 * Пул купонов { type: 'pool' }/Многоразовый промокод { type: 'reusable' }
 *
 * isNew { Boolean } - несохраненный пул
 * poolId { string } - идентификатор пула/многоразового купона
 * poolData { Object } - данные пула
 * isPool { Boolean } - пул или многоразовый купон
 * poolName { string } - название пула/многоразового купона
 * label { string } - название для переменной в редакторе
 */

import ee from 'event-emitter';
import { POOLS_DIRECTORY } from './pools-directory';

const REUSABLE_COUPON_TYPE = 'reusable';

export class DiscountPool {
  constructor(data) {
    ee(this);
    POOLS_DIRECTORY.load(); // данные загружаются один раз

    this.isNew = ko.observable(true);
    this.poolId = ko.observable(null);

    this.poolData = ko.computed(() => {
      if (!this.poolId()) return null;
      if (!POOLS_DIRECTORY.loaded()) return null;
      return POOLS_DIRECTORY.getPoolById(this.poolId());
    });
    this.isPool = ko.computed(() => {
      if (!this.poolData()) return false;
      return this.poolData().type !== REUSABLE_COUPON_TYPE;
    });
    this.poolName = ko.computed(() => {
      if (!this.poolData()) return false;
      return this.poolData().title;
    });
    this.reusableCouponName = ko.computed(() => {
      if (!this.poolData() || this.isPool()) return '';
      return this.poolData().code;
    });

    

    /** @deprecated */
    this.label = ko.computed(() => {
      let data = this.poolData();
      if (!data) return 'Промокод';
      if (this.isPool()) {
        return 'Промокод: ' + data.title;
      }
      return 'Промокод: ' + data.code;
    });

    

    this.poolId.subscribe((_) => {
      this.isNew(false);
    });

    this.poolsList = ko.computed(() => {
      return POOLS_DIRECTORY.pools().filter(
        (p) => p.type !== REUSABLE_COUPON_TYPE
      );
    });

    this.reusableCouponsList = ko.computed(() => {
      return POOLS_DIRECTORY.pools().filter(
        (p) => p.type === REUSABLE_COUPON_TYPE
      );
    });

    // Инициализация данных пула
    if (data) {
      this.isNew(false);
      if (data.poolId) {
        this.poolId(data.poolId);
      } else {
        // if data.name
      }
    }
  }

  setPoolId(poolId) {
    this.poolId(poolId);
  }

  update(data) {
    this.setPoolId(data.poolId);
    this.emit('change');
  }

  getData() {
    return {
      isPool: this.isPool(),
      poolId: this.poolId()
    };
  }
}
