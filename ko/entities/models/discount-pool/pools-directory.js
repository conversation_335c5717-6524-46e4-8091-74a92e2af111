function POOL(data) {
  this.id = data.id;
  this.title = data.title;
  this.code = data.code;
  this.type = data.type;
  this.fullTitle = data.type === 'reusable' ? `${this.code} - ${this.title}` : this.title
}

export const POOLS_DIRECTORY = (() => {
  // список пулов и многоразовых купонов
  let pools = ko.observableArray([]);

  let loading = ko.observable(false);
  let loaded = ko.observable(false);

  let url = `${APIConfig.baseApiUrlPath}discount-pool?all=1&access-token=${APIConfig.apiKey}`;

  // загрузить список пулов и многоразовых купонов
  let load = () => {
    if (loading() || loaded()) return;
    loading(true);

    fetch(url)
      .then((res) => res.json())
      .then((json) => {
        pools(json.items.map((i) => new POOL(i)));
        loaded(true);
        loading(false);
      });
  };

  // поиск пула/многоразового купона по id
  let getPoolById = (id) => {
    if (!id) return null;
    id = '' + id;
    return pools().find((p) => p.id === id);
  };

  return {
    loaded,
    load,
    getPoolById,
    pools
  };
})();
