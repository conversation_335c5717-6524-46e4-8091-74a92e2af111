/**
 * Купон (раздел Купоны, суперадмин)
 *
 * type: one-use|limited|unlimited
 */

import moment from 'moment';
import { formatServerDateToClient } from 'Utils/date/format';

const ONE_USE = 'one-use';
const LIMITED = 'limited';
const UNLIMITED = 'unlimited';

const now = moment();

export class Coupon {
  constructor(data) {
    this.id = data.id;

    this.isActive = ko.observable(false);
    this.createdAt = moment(data.created_at, 'YYYY-MM-DD').format('DD.MM.YYYY');

    this.promoName = ko.observable('');
    this.promoCode = ko.observable('');
    this.type = ko.observable(LIMITED);
    this.description = ko.observable('');

    this.answersCount = ko.observable();
    this.maxUsageCount = ko.observable();
    this.usageCount = ko.observable();

    this.validFrom = ko.observable();
    this.validTo = ko.observable();

    /** COMPUTED */

    this.isOneUse = ko.computed(() => {
      return this.type() === ONE_USE;
    });
    this.isLimited = ko.computed(() => {
      return this.type() === LIMITED;
    });
    this.isUnlimited = ko.computed(() => {
      return this.type() === UNLIMITED;
    });

    this.remains = ko.computed(() => {
      if (this.isUnlimited()) return null;
      return this.maxUsageCount() - this.usageCount();
    });

    this.validToDate = ko.computed(() => {
      return moment(this.validTo(), 'DD.MM.YYYY').add(1, 'd');
    });

    this.isExpired = ko.computed(() => {
      if (!this.validTo()) return false;
      return this.validToDate() < now;
    });

    this.isUsageEnded = ko.computed(() => {
      if (this.isUnlimited()) return false;
      return this.usageCount() >= this.maxUsageCount();
    });

    this.typeText = ko.computed(() => {
      switch (this.type()) {
        case ONE_USE:
          return 'Разовый';
        case LIMITED:
          return 'Многоразовый';
        case UNLIMITED:
          return 'Бесконечный';
      }
    });

    this.setData(data);
  }

  setData(data) {
    let isActive = parseInt(data.is_active);
    this.isActive(!!isActive);
    this.promoName(data.promo_name);
    this.promoCode(data.promo_code);
    this.type(data.type);
    this.description(data.description);

    this.answersCount(data.bonuses);
    this.maxUsageCount(data.max_usage_count);
    this.usageCount(data.usageCount);

    let validFrom = formatServerDateToClient(data.valid_from_at);
    this.validFrom(validFrom);
    let validTo = formatServerDateToClient(data.valid_to_at);
    this.validTo(validTo);
  }
}
