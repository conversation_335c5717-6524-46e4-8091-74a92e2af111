import { RoleNames, UserRoles } from "@/constants/users/roles";
import { User, UserData } from "./types";

export function UserModel(userData: UserData): User {
  const { id, username, role, photo } = userData;

  const roleName = role[0];
  const roleData = Object.entries(RoleNames).find(
    ([id, name]) => name === roleName
  );

  return {
    id,
    username,
    name: username,
    avatar: photo.preview,
    roleName,
    role: roleData && roleData[0] as UserRoles,
  };
}
