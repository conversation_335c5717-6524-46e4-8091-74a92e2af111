import { FModel } from '@/entities/f-model';
import { VARIANTS_TYPE_SINGLE} from '../../data/variants-types';
import { CustomAnswerController } from '../custom-answer';
import { TextAnswerController } from '../text-answer';
import { VariantsController } from '../variants';

export class ClarifyingQuestionController extends FModel {
  constructor(config = {}) {
    super();

    /** SETTINGS */
    const { ...variantsConfig } = config;
    const validator = config.validator;


    /** DATA */

    this.text = ko.observable('').extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: validator && validator.onlyIf,
      }
    });
    this.variantsType = ko.observable(VARIANTS_TYPE_SINGLE);
    this.variants = new VariantsController(variantsConfig);
    this.forAllRates = ko.observable(false);
    this.customAnswerEnabled = ko.observable(false);
    this.customAnswer = new CustomAnswerController();
    this.textAnswer = new TextAnswerController();

    this.listen(this.text, this.variantsType, this.variants, this.forAllRates, this.customAnswerEnabled, this.customAnswer);

    this.createValidator(this.text, this.variants);
  }

  reset() {
    this.forAllRates(false);
  }

  update(data) {
    this.text(data.text || '');
    this.variantsType(data.variants.type || VARIANTS_TYPE_SINGLE);
    this.forAllRates(data.forAllRates);
    this.customAnswerEnabled(data.customAnswerEnabled);
    this.customAnswer.update(data.customAnswer);
    this.textAnswer.update(data.textAnswer);
  }
}
