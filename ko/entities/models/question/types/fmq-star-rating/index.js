import { FModel } from '@/entities/f-model';
import { ClarifyingQuestionController } from '../../controllers/clarifying-question';
import { CommentController } from '../../controllers/comment';
import { GalleryController } from '../../controllers/gallery';
import { StarLabel } from './label';

const defaultColor = '#F8CD1C';
const defaultCount = 5;
const defaultSize = 'md';

export class FmqStarRating extends FModel {
  constructor(config) {
    super();

    this.color = ko.observable(defaultColor);
    this.count = ko.observable(defaultCount);
    this.size = ko.observable(defaultSize);

    this.count.subscribe((v) => {
      if (v > 5 && this.size() == 'lg') {
        this.size('md');
      }
    });

    this.labels = Array(10)
      .fill()
      .map((_, i) => new StarLabel(i));

    this.visibleLabels = ko.pureComputed(() => {
      return this.labels.slice(0, this.count());
    });

    this.galleryEnabled = ko.observable(false);
    this.gallery = new GalleryController({
      api: config.api,
      freeRemove: true,
      validator: {
        onlyIf: () => this.galleryEnabled()
      }
    });

    this.clarifyingQuestionEnabled = ko.observable(false);
    this.clarifyingQuestion = new ClarifyingQuestionController({
      validator: {
        onlyIf: () => this.clarifyingQuestionEnabled()
      }
    });

    this.commentEnabled = ko.observable(false);
    this.comment = new CommentController({
      validator: {
        onlyIf: () => this.clarifyingQuestionEnabled()
      }
    });

    this.clarifyingQuestionEnabled.subscribe((v) => {
      if (v) this.commentEnabled(false);
      else this.clarifyingQuestion.reset();
    });

    this.listen(
      this.color,
      this.count,
      this.size,
      ...this.labels,
      this.galleryEnabled,
      this.gallery,
      this.clarifyingQuestionEnabled,
      this.clarifyingQuestion,
      this.commentEnabled,
      this.comment
    );

    this.createValidator(this.clarifyingQuestion, this.gallery, this.comment);
  }

  update(data) {
    let config = data.starsConfig || {};
    this.size(config.size || defaultSize);
    this.color(config.color || defaultColor);
    this.count(config.count || defaultCount);

    let labels = config.labels || [];
    this.labels.forEach((label, i) => {
      let text = labels[i];
      label.text(text || '');
    });

    this.galleryEnabled(data.galleryEnabled);
    this.gallery.update(data.gallery);

    this.clarifyingQuestionEnabled(data.clarifyingQuestionEnabled);
    this.clarifyingQuestion.update(data.clarifyingQuestion);

    this.commentEnabled(data.commentEnabled);
    this.commentEnabled.update(data.comment);
  }

  getData() {
    let data = {
      starsConfig: {
        size: this.size(),
        color: this.color(),
        count: this.count(),
        labels: this.visibleLabels().map((l) => l.text())
      },
      galleryEnabled: this.galleryEnabled(),
      gallery: this.gallery.getData(),
      clarifyingQuestionEnabled: this.clarifyingQuestionEnabled(),
      clarifyingQuestion: this.clarifyingQuestion.getData(),
      commentEnabled: this.commentEnabled(),
      comment: this.comment.getData()
    };

    return data;
  }
}
