import { FModel } from '@/entities/f-model';
import {
  INTERBLOCK_END,
  INTERBLOCK_START,
  INTERBLOCK_TEXT
} from '@/dictionaries/question/interblock-types';

import { CHANGE_UI } from '../../events';

export class FmqInterblock extends FModel {
  constructor(params) {
    super();

    this.showNumber = ko.observable(false);
    this.savedBlockType = ko.observable(INTERBLOCK_TEXT);
    this.blockType = ko.observable(INTERBLOCK_TEXT);

    this.blockType.subscribe((v) => {
      this.emit(CHANGE_UI, {
        field: 'blockType',
        from: this.savedBlockType(),
        to: v
      });
      this.savedBlockType(v);
    });

    this.showNumber.subscribe((v) => {
      this.emit(CHANGE_UI, {
        field: 'showNumber',
        value: v
      });
    });

    if (params.screenType === 'start') {
      this.blockType(INTERBLOCK_START);
    } else if (params.screentType === 'end') {
      this.blockType(INTERBLOCK_END);
    }
  }
}
