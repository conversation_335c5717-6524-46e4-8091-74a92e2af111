import { FModel } from '@/entities/f-model';
import * as types from '@/dictionaries/question/types';
import * as models from '../types';
import { CHANGE_UI } from '../events';

const QuestionModels = {
  [types.RATE_QUESTION]: models.RateQuestion,
  [types.VARIANTS_QUESTION]: models.VariantsQuestion,
  [types.TEXT_QUESTION]: models.TextQuestion,
  [types.DATE_QUESTION]: models.DateQuestion,
  [types.ADDRESS_QUESTION]: models.AddressQuestion,
  [types.FILE_QUESTION]: models.FileQuestion,
  [types.QUIZ_QUESTION]: models.QuizQuestion,
  [types.PRIORITY_QUESTION]: models.PriorityQuestion,
  [types.MEDIA_VARIANTS_QUESTION]: models.MediaVariantsQuestion,
  [types.GALLERY_QUESTION]: models.GalleryQuestion,
  [types.SMILE_QUESTION]: models.SmileQuestion,
  [types.NPS_QUESTION]: models.NPSQuestion,
  [types.MATRIX_QUESTION]: models.MatrixQuestion,
  [types.DIFF_QUESTION]: models.DiffQuestion,
  [types.STARS_QUESTION]: models.StarsQuestion,
  [types.INTER_BLOCK]: models.InterBlock,
  [types.SCALE_QUESTION]: models.ScaleQuestion
};

let unique = 1;
const answersFromFormat = 'DD.MM.YYYY HH:mm';

export class FmQuestion extends FModel {
  constructor(config = {}) {
    super();
    console.log('FmQuestion', config)
    this.unique = unique++;

    /** SETTINGS */
    this.api = config.api;

    // this.isAuto = config.isAuto;
    // this.isSystem = config.isSystem;
    // this.withPoints = config.withPoints;
    // this.isSource = config.isSource;
    // this.isUpdated = ko.observable(false);
    // this.isTmp = ko.observable(true);
    // this.countAnswers = 0;
    // this.pointId = ko.observable(null);
    // this.actionsBlocked = ko.observable(false);
    // this.logic = ko.observable(null);

    this.serverErrors = {
      alias: ko.observable(''),
      name: ko.observable(''),
      description: ko.observable('')
    };

    /** DATA */
    this.id = ko.observable(0);
    this.type = ko.observable(types.STARS_QUESTION);
    this.required = ko.observable(true);
    this.alias = ko.observable('').extend({
      required: {
        message: 'Обязательное поле'
      },
      minLength: {
        params: 2,
        message: 'Должно быть введено хотя бы 2 символа'
      },
      validation: {
        validator: () => false,
        onlyIf: () => !!this.serverErrors.alias(),
        message: () => this.serverErrors.alias()
      }
    });
    this.name = ko.observable('').extend({
      validation: {
        validator: () => false,
        onlyIf: () => !!this.serverErrors.name(),
        message: () => this.serverErrors.name()
      }
    });
    this.description = ko.observable('').extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: {
        validator: () => false,
        onlyIf: () => !!this.serverErrors.description(),
        message: () => this.serverErrors.description()
      }
    });

    /** HIDE QUESTION DATA */
    this.answersFrom = ko.observable(moment().format(answersFromFormat));
    this.dontShowIfAnswered = ko.observable(false);

    /** TYPED QUESTION */
    this.typedQuestion = ko.observable(null);
    this.onTypeChange(this.type());

    /** LISTENERS */
    (() => {
      let subscriptions = [];
      subscriptions.push(
        this.dontShowIfAnswered.subscribe((v) => {
          if (v && !this.answersFrom()) {
            this.resetAnswers();
          }
        })
      );

      ['alias', 'name', 'description'].forEach((key) => {
        subscriptions.push(
          this[key].subscribe(() => {
            this.serverErrors[key]('');
          })
        );
      });

      subscriptions.push(
        this.type.subscribe((v) => {
          this.onTypeChange(v);
        })
      );

      this.onDispose(() => subscriptions.forEach((s) => s.dispose()));
    })();
  }

  getField(fieldName) {
    if (fieldName in this) return this[fieldName];
    let typedQuestion = this.typedQuestion();
    if (fieldName in typedQuestion) return typedQuestion[fieldName];
    return null;
  }

  getQuestionModel(type, alias) {
    if (type == types.RATE_QUESTION && alias === 'Товар')
      return models.ItemsQuestion;
    return QuestionModels[type];
  }

  onTypeChange(typeId) {
    let newType = typeId;
    if (typeId === 'start' || typeId === 'end') newType = types.INTER_BLOCK;

    let model = this.getQuestionModel(typeId);
    let question = new model({ screenType: typeId, api: this.api });

    let currentQuestion = this.typedQuestion();
    if (currentQuestion && this._changeUICb) {
      currentQuestion.off(CHANGE_UI, this._changeUICb);
    }

    this._changeUICb = (data) => {
      this.emit(CHANGE_UI, data);
    };
    question.on(CHANGE_UI, this._changeUICb);

    this.typedQuestion(question);
  }

  /** HIDE QUESTIONS RESET */
  resetAnswers() {
    let now = moment().format(answersFromFormat);
    this.answersFrom(now);
  }

  update(data) {
  }
}
