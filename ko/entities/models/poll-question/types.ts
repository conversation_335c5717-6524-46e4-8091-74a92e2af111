import {
  IPollQuestionStars,
  PollQuestionStarsVars,
} from "./questionTypes/stars.types";
import {
  IPollQuestionInterscreen,
  PollQuestionInterscreenVars,
} from "./questionTypes/inter.types";
import { PollQuestionVars } from "./questionTypes/index.types";
import { ClarifyingQuestionVars } from "./clarifyingQuestion/types";
import { CommentVars } from "./comment/types";
import { TextMaskVars } from "./textFieldMask/types";
import {
  IPollQuestionVariants,
  PollQuestionVariantsVars,
} from "./questionTypes/variants.types";
import {
  IPollQuestionText,
  PollQuestionTextVars,
} from "./questionTypes/text.types";
import {
  IPollQuestionDate,
  PollQuestionDateVars,
} from "./questionTypes/date.types";
import {
  IPollQuestionAddress,
  PollQuestionAddressVars,
} from "./questionTypes/address.types";
import {
  IPollQuestionFile,
  PollQuestionFileVars,
} from "./questionTypes/file.types";
import {
  IPollQuestionQuiz,
  PollQuestionQuizVars,
} from "./questionTypes/quiz.types";
import {
  IPollQuestionPriority,
  PollQuestionPriorityVars,
} from "./questionTypes/priority.types";
import {
  IPollQuestionMediaVariants,
  PollQuestionMediaVariantsVars,
} from "./questionTypes/mediaVariants.types";
import {
  IPollQuestionGallery,
  PollQuestionGalleryVars,
} from "./questionTypes/gallery.types";
import {
  IPollQuestionSmile,
  PollQuestionSmileVars,
} from "./questionTypes/smile.types";
import {
  IPollQuestionNps,
  PollQuestionNpsVars,
} from "./questionTypes/nps.types";
import {
  IPollQuestionMatrix,
  PollQuestionMatrixVars,
} from "./questionTypes/matrix.types";
import {
  IPollQuestionStarVariants,
  PollQuestionStarVariantsVars,
} from "./questionTypes/starVariants.types";
import {
  IPollQuestionDiff,
  PollQuestionDiffVars,
} from "./questionTypes/diff.types";
import {
  IPollQuestionRate,
  PollQuestionRateVars,
} from "./questionTypes/rate.types";
import {
  IPollQuestionFilials,
  PollQuestionFilialsVars,
} from "./questionTypes/filials.types";
import {
  IPollQuestionClassifier,
  PollQuestionClassifierVars,
} from "./questionTypes/classifier.types";
import {
  IPollQuestionScale,
  PollQuestionScaleVars,
} from "./questionTypes/scale.types";

export type FullPollQuestionVars = PollQuestionStarsVars &
  PollQuestionVariantsVars &
  PollQuestionTextVars &
  PollQuestionDateVars &
  PollQuestionAddressVars &
  PollQuestionFileVars &
  PollQuestionQuizVars &
  PollQuestionPriorityVars &
  PollQuestionMediaVariantsVars &
  PollQuestionGalleryVars &
  PollQuestionSmileVars &
  PollQuestionNpsVars &
  PollQuestionMatrixVars &
  PollQuestionStarVariantsVars &
  PollQuestionDiffVars &
  PollQuestionRateVars &
  PollQuestionFilialsVars &
  PollQuestionClassifierVars &
  PollQuestionInterscreenVars &
  ClarifyingQuestionVars &
  PollQuestionScaleVars &
  CommentVars;

export type FullPollQuestion =
  | IPollQuestionInterscreen
  | IPollQuestionStars
  | IPollQuestionVariants
  | IPollQuestionText
  | IPollQuestionDate
  | IPollQuestionAddress
  | IPollQuestionFile
  | IPollQuestionQuiz
  | IPollQuestionPriority
  | IPollQuestionMediaVariants
  | IPollQuestionGallery
  | IPollQuestionSmile
  | IPollQuestionNps
  | IPollQuestionScale
  | IPollQuestionMatrix
  | IPollQuestionStarVariants
  | IPollQuestionDiff
  | IPollQuestionRate
  | IPollQuestionFilials
  | IPollQuestionClassifier;
