export enum ClarifyingQuestionTypes {
    "single" = 0,
    "multiple" = 1,
    "text" = 2,
}

export type QuestionVariantVars = {
    id: number;
    variant: string;
    position: number;
    points?: number;
    question_detail_id?: number; // id донорского варианта
    is_deleted?: boolean;
    extra_question?: number;
    need_extra?: number;
    name?: string;
    path?: string;
    variants?: any;
    type?: number;
    file_id?: number,
    file_url?: string,
    preview_url?: string,
    description: string;
    
    detail_question: string;
    variants_element_type: ClarifyingQuestionTypes;
    is_self_answer: number;
    self_variant_text: string;
    self_variant_placeholder_text: string;
    placeholder_text: string;
    main_question_type: number;
    selfVariantFile?: object
}

export type QuestionVariant = {
    id: string;
    donorId?: string;
    text: string;
    position?: number;
    points?: number;
    deleted?: boolean;
    children?: any;
    type?: number;
    file?: any;
    file_id?: number,
    file_url?: string,
    preview_url?: string,
    description?: string;

    detail_question?: string;
    variants_element_type?: ClarifyingQuestionTypes;
    is_self_answer?: number;
    self_variant_text?: string;
    placeholder_text?: string;
    main_question_type?: number;
    selfVariantFile?: object
}