import { QuestionVariantVars, QuestionVariant } from "./types";

export function PollQuestionVariant(
  data: QuestionVariantVars,
  hasChildren?: boolean,
): QuestionVariant {
 
  const {
    id,
    variant,
    points,
    position,
    question_detail_id,
    is_deleted,
    name,
    variants,
    type,
    file_id,
    file_url,
    preview_url,
    description,

    detail_question,
    variants_element_type,
    is_self_answer,
    self_variant_text,
    placeholder_text,
    main_question_type,
    selfVariantFile,
  } = data;
  const obj = {
    id: `${id}`,
    donorId: question_detail_id ? `${question_detail_id}` : "-1",
    text: name || variant,
    deleted: !!is_deleted,
    points,
    position,
    type,
    file_id,
    file_url,
    preview_url,
    description,

    detail_question,
    variants_element_type,
    is_self_answer,
    self_variant_text,
    placeholder_text,
    main_question_type,
    selfVariantFile,
  };
  if (hasChildren) {
    Object.assign(obj, { children: variants.map(el => PollQuestionVariant(el)) });
  }

  return obj;
}
