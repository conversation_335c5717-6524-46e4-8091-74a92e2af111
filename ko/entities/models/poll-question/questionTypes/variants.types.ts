import { PollQuestionVars, IPollQuestion } from "./index.types";
import { QuestionVariantVars, QuestionVariant } from "../questionVariant/types";

export interface PollQuestionVariantsVars extends PollQuestionVars {
  detail_answers: Array<QuestionVariantVars>;

  is_self_answer: number;
  self_variant_text: string;
  self_variant_description: string;
  placeholder_text: string;
  self_variant_placeholder_text: string;

  skip: number;
  skip_text: string;
  show_tooltips: number;
}

export interface IPollQuestionVariants extends IPollQuestion {
  variants: Array<QuestionVariant>;
  detailAnswers: Array<QuestionVariantVars>;

  skip: boolean;
  skipText: string;

  selfAnswer: boolean;
  selfAnswerText: string;
  selfAnswerDescription: string;
  selfAnswerPlaceholder: string;

  deleted?: boolean;
}
