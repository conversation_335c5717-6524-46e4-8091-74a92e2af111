import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionSmile, SmileVars, Smile, PollQuestionSmileVars } from "./smile.types";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";
import { PollClarifyingQuestion, PollClarifyingToEachQuestion } from "../clarifyingQuestion/index";

export function SmileItem(data: SmileVars): Smile {
  const { id, label, smile_url } = data;
  return {
    id: `${id}`,
    text: label,
    url: smile_url,
  };
}
export class PollQuestionSmile
  extends PollQuestion
  implements IPollQuestionSmile
{
  smiles: Array<Smile>;
  skip: boolean;
  skipText: string;
  clarifyingQuestion: ClarifyingQuestion;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { smiles, skip, skip_text } = questionData as PollQuestionSmileVars;
    this.clarifyingQuestion = PollClarifyingQuestion(questionData);

    this.smiles = smiles.map((smile: SmileVars): Smile => SmileItem(smile));
    this.skip = Boolean(skip);
    this.skipText = skip_text;
  }
}
