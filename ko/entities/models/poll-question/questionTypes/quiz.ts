import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import {
  IPollQuestionQuiz,
  QuizItem,
  QuizItemVars,
  PollQuestionQuizVars,
} from "./quiz.types";
import { QuestionTextFieldMask } from "../textFieldMask/index";

export function PollQuizItem(data: QuizItemVars): QuizItem {
  const {
    id,
    isRequired,
    isTextarea,
    label,
    maskType,
    maskConfig,
    placeholderText,
  } = data;

  return {
    id: `${id}`,
    required: !!isRequired,
    textarea: !!isTextarea,
    label,
    mask: QuestionTextFieldMask({
      type: maskType,
      nameMask: maskConfig,
      placeholder: placeholderText,
    }),
  };
}

export class PollQuestionQuiz
  extends PollQuestion
  implements IPollQuestionQuiz
{
  fields: Array<QuizItem>;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { quizzes } = questionData as PollQuestionQuizVars;

    this.fields = quizzes.map((item: QuizItemVars): QuizItem => {
      return PollQuizItem(item);
    });
  }
}
