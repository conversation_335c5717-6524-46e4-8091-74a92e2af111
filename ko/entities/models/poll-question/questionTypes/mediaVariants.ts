import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionMediaVariants } from "./mediaVariants.types";

export class PollQuestionMediaVariants
  extends PollQuestion
  implements IPollQuestionMediaVariants
{
  skip: boolean;
  skipText: string;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { skip, skip_text } = questionData;

    this.skip = Boolean(skip);
    this.skipText = skip_text;
  }
}
