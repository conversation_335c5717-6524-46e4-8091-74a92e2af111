import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { PollQuestionVars, IPollQuestion } from './index.types';

export interface PollQuestionCardSortingVars extends PollQuestionVars {
  cardSortingCategories: Array<QuestionVariantVars>,
  card_column_text: string,
  category_column_text: string,
  random_categories_order: 0 | 1,
  random_variants_order: 0 | 1,
  min_choose_variants: number,
  max_choose_variants: number,
  skip: 0 | 1,
  skip_text: string,
  detail_answers: Array<QuestionVariantVars>,
}

export interface IPollQuestionCardSorting extends IPollQuestion {
  categoryList: Array<QuestionVariant>;
  cardColumnText: string;
  categoryColumnText: string;
  randomCategoryOrder: boolean;
  randomCardOrder: boolean;
  minCardInCategory: number;
  maxCardInCategory: number;
  skip: boolean,
  skipText: string,
  variants: Array<QuestionVariant>;
}