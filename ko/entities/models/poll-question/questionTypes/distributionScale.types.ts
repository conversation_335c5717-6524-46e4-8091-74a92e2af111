import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { IPollQuestion, PollQuestionVars } from "./index.types";


export interface PollQuestionDistributionScaleVars extends PollQuestionVars {
  detail_answers: Array<QuestionVariantVars>;
  self_variant_text: string;
  skip: number;
  skip_text: string;
}

export interface IPollQuestionDistributionScale extends IPollQuestion {
  skip: boolean;
  skipText: string;
  indicatorText: string;
  variants: Array<QuestionVariant>;
}
