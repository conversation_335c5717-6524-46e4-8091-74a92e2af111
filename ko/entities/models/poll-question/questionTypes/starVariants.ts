import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import {
  IPollQuestionStarVariants,
  PollQuestionStarVariantsVars,
} from "./starVariants.types";
import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { PollQuestionVariant } from "../questionVariant/index";
import { StarRating } from "../starRating/types";
import { QuestionStarRating } from "../starRating";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";
import { PollClarifyingQuestion } from "../clarifyingQuestion/index";

export class PollQuestionStarVariants
  extends PollQuestion
  implements IPollQuestionStarVariants
{
  variants: Array<QuestionVariant>;
  starRating: StarRating;
  skip: boolean;
  skipText: string;
  clarifyingQuestion: ClarifyingQuestion;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const {
      detail_answers,
      starRatingOptions,

      skip,
      skip_text,
    } = questionData as PollQuestionStarVariantsVars;

    this.variants = detail_answers.filter(el => !el?.extra_question).map(
      (item: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(item);
      }
    );

    this.starRating = QuestionStarRating(starRatingOptions);
    this.skip = Boolean(skip);
    this.skipText = skip_text;

    this.clarifyingQuestion = PollClarifyingQuestion(questionData);
  }
}
