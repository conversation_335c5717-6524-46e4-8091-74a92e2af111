import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionGallery } from "./gallery.types";
export class PollQuestionGallery
  extends PollQuestion
  implements IPollQuestionGallery
{
  skip: boolean;
  skipText: string;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { skip, skip_text } = questionData;

    this.skip = Boolean(skip);
    this.skipText = skip_text;
  }
}
