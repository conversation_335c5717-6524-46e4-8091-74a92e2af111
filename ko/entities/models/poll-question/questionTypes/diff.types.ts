import { PollQuestionVars, IPollQuestion } from "./index.types";

export type DiffRowVars = {
  id: number;
  position: number;
  start_label: string;
  end_label: string;
};

export type DiffRow = {
  id: string;
  position: number;
  startLabel: string;
  endLabel: string;
};

export interface PollQuestionDiffVars extends PollQuestionVars {
  differentialRows: Array<DiffRowVars>;
  skip: number;
  skip_text: string;
}

export interface IPollQuestionDiff extends IPollQuestion {
  diffConfig: Array<DiffRow>;
  skip: boolean;
  skipText: string;
}
