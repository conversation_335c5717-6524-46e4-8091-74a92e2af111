import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { StarRating, StarRatingVars } from "../starRating/types";
import { PollQuestionVars, IPollQuestion } from "./index.types";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";

export interface PollQuestionStarVariantsVars extends PollQuestionVars {
  detail_answers: Array<QuestionVariantVars>;
  starRatingOptions: StarRatingVars;

  skip: number;
  skip_text: string;
}

export interface IPollQuestionStarVariants extends IPollQuestion {
  variants: Array<QuestionVariant>;
  starRating: StarRating;

  skip: boolean;
  skipText: string;

  clarifyingQuestion: ClarifyingQuestion;
}
