import { VariantTypes } from "@/constants/question/variantTypes";
import { PollQuestionVars, IPollQuestion } from "./index.types";

export enum ClassifierListTypes {
  Tree = "tree",
  List = "list",
}

export enum ClassifierSorts {
  Default = "default",
  Alphabetical = "alphabetter",
  Random = "random",
}

export interface PollQuestionClassifierVars extends PollQuestionVars {
  dictionary_id: number;
  detail_answers: Array<{
    id: string;
    value: string;
  }>;
  dropdown_variants: 0 | 1;
  dictionary_list_type: ClassifierListTypes;
  variants_element_type: VariantTypes;
  dictionary_sort: ClassifierSorts;
  max_choose_variants: number | null;
  skip: number;
  skip_text: string;
  show_tooltips: number
}

export interface IPollQuestionClassifier extends IPollQuestion {
  dictionaryId: string;
  dictionaryItems: Array<string>;
  dropdown: boolean;
  listType: ClassifierListTypes;
  variantType: VariantTypes;
  sort: ClassifierSorts;
  maxVariantsCount: number;
  skip: boolean;
  skipText: string;
  detailAnswers: Array<{
    id: string;
    value: string;
  }>;
  show_tooltips: number;
}
