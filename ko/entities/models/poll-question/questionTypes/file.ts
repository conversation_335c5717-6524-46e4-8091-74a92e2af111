import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionFile } from "./file.types";

export class PollQuestionFile
  extends PollQuestion
  implements IPollQuestionFile
{
  commentEnabled: boolean;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { is_self_answer } = questionData;
    this.commentEnabled = !!is_self_answer;
  }
}
