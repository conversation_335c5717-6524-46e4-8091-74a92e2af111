import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { IPollQuestion, PollQuestionVars } from "./index.types";

export type ScaleConfigVars = {
  end: number;
  foquz_question_id: number;
  id: number;
  start: number;
  step: number;
};

export interface PollQuestionScaleVars extends PollQuestionVars {
  detail_answers: Array<QuestionVariantVars>;
  scaleRatingSetting: ScaleConfigVars;
  skip: number;
  skip_text: string;
  set_variants: 0 | 1;
}

export interface IPollQuestionScale extends IPollQuestion {
  setVariants: boolean;
  skip: boolean;
  skipText: string;
  variants: Array<QuestionVariant>;
}
