import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionMatrix3D, PollQuestionMatrix3DVars } from "./matrix-3d.types";
import { QuestionVariantVars, QuestionVariant } from "../questionVariant/types";
import { PollQuestionVariant } from "../questionVariant/index";

export class PollQuestionMatrix3D
  extends PollQuestion
  implements IPollQuestionMatrix3D
{
  rows: Array<QuestionVariant>;
  cols: Array<QuestionVariant>;

  skip: boolean;
  skipText: string;

  selfAnswer: boolean;
  selfAnswerText: string;
  selectPlaceholder: string;
  show_tooltips: number;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const {
      matrixElements,
      select_placeholder_text,
      skip,
      skip_text,
      show_tooltips,
    } = questionData as PollQuestionMatrix3DVars;

    this.rows = matrixElements.rows.map(
      (item: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(item);
      }
    );
    this.cols = matrixElements.columns.map(
      (item: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(item, true);
      }
    );

    this.skip = Boolean(skip);
    this.skipText = skip_text;
    this.selectPlaceholder = select_placeholder_text;
    this.show_tooltips = show_tooltips;
  }
}
