import { PollQuestionVariant } from "../questionVariant";
import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionNps, NpsConfig, NpsConfigVars, PollQuestionNpsVars } from "./nps.types";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";
import { PollClarifyingQuestion, PollClarifyingToEachQuestion } from "../clarifyingQuestion/index";

export function formatNpsConfig(data: NpsConfigVars): NpsConfig {
  const { design, start_label, start_point_color, end_label, end_point_color } =
    data;

  return {
    design,
    startLabel: start_label,
    startColor: start_point_color,
    endLabel: end_label,
    endColor: end_point_color,
  };
}

export class PollQuestionNps extends PollQuestion implements IPollQuestionNps {
  npsConfig: NpsConfig;
  skip: boolean;
  skipText: string;
  extraQuestionType: number;
  setVariants: boolean;
  variants: Array<QuestionVariant>;
  clarifyingQuestion: ClarifyingQuestion;
  clarifyingToEachQuestion?: Array<ClarifyingQuestion>;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { npsRatingSetting, skip, skip_text, set_variants, detail_answers, extra_question_type } = questionData as PollQuestionNpsVars;


    const variants = detail_answers.filter(el => !el?.extra_question);

    this.variants = variants
      .map((item: QuestionVariantVars): QuestionVariant => PollQuestionVariant(item));

    this.npsConfig = formatNpsConfig(npsRatingSetting);
    this.skip = Boolean(skip);
    this.skipText = skip_text;
    this.extraQuestionType = extra_question_type;
    this.setVariants = !!set_variants;
    this.clarifyingQuestion = PollClarifyingQuestion(questionData);
    if (this.extraQuestionType === 3) {
      this.clarifyingToEachQuestion = variants
        .filter(({ need_extra }) => !!need_extra)
        .map((item: QuestionVariantVars): ClarifyingQuestion => PollClarifyingToEachQuestion(item, questionData));
    }
  }
}
