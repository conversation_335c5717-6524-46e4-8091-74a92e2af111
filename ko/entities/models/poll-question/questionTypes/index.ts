import { IPollQuestion } from "./index.types";
import { FullPollQuestionVars } from "../types";
import { Gallery } from "../questionGallery/types";
import { QuestionGallery } from "../questionGallery/index";
import { Comment } from "../comment/types";
import { QuestionComment } from "../comment";
export class PollQuestion implements IPollQuestion {
  id: string;
  type: string;

  isInterscreen: boolean = false;
  isInterStart: boolean = false;
  isInterEnd: boolean = false;
  isInterFiveSecondTest: boolean = false;
  isInterText: boolean = false;

  alias: string;
  name: string;
  text: string;
  description: string;

  galleryEnabled: boolean;
  gallery: Gallery;

  donorId: string;

  pointId: string;
  pointName: string;

  commentEnabled: boolean;
  comment: Comment | null;

  constructor(questionData: FullPollQuestionVars) {
    const {
      id,
      main_question_type,
      name,
      service_name,
      description_html,
      description,
      sub_description,
      enableGallery,
      gallery,
      donor,
      point_id,
      pointName,
      comment_enabled, 
    } = questionData;

    this.id = `${id}`;

    const type: string = `${main_question_type}`;
    this.type = type;

    this.name = name;
    this.alias = service_name;
    this.text = description_html || description;
    this.description = sub_description;

    this.galleryEnabled = enableGallery;
    this.gallery = QuestionGallery(gallery);

    this.donorId = donor ? `${donor}` : null;

    this.pointId = point_id ? `${point_id}` : null;
    this.pointName = pointName;

    this.commentEnabled = !!comment_enabled;
    this.comment = QuestionComment(questionData);

    console.log({ type, questionData });
  }
}
