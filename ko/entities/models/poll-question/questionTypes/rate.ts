import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionRate, RateTypes, RateViews, PollQuestionRateVars } from './rate.types';

import { ClarifyingQuestion } from "../clarifyingQuestion/types";
import { PollClarifyingQuestion } from "../clarifyingQuestion/index";
import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { PollQuestionVariant } from "../questionVariant";

export class PollQuestionRate
  extends PollQuestion
  implements IPollQuestionRate
{
  viewType: RateViews;
  ratingText: string;
  ratingType: RateTypes;
  clarifyingQuestion: ClarifyingQuestion;

  variants: Array<QuestionVariant>;

  selfAnswer: boolean;
  selfAnswerText: string;
  selfAnswerPlaceholder: string;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const {
      type,
      text,
      rating_type,

      detail_answers,
      is_self_answer,
      self_variant_text,
      placeholder_text,
    } = questionData as PollQuestionRateVars;

    this.viewType = type;
    this.ratingText = text;
    this.ratingType = rating_type;

    this.clarifyingQuestion = PollClarifyingQuestion(questionData);

    this.variants = detail_answers.map(
      (item: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(item);
      }
    );

    this.selfAnswer = !!is_self_answer;
    this.selfAnswerText = self_variant_text;
    this.selfAnswerPlaceholder = placeholder_text;
  }
}
