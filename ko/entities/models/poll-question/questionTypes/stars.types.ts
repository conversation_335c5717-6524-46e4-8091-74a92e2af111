import { PollQuestionVars, IPollQuestion } from "./index.types";
import { StarRatingVars, StarRating } from "../starRating/types";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";

export interface PollQuestionStarsVars extends PollQuestionVars {
  starRatingOptions: StarRatingVars;

  skip: number;
  skip_text: string;
}

export interface IPollQuestionStars extends IPollQuestion {
  starRating: StarRating;

  skip: boolean;
  skipText: string;

  clarifyingQuestion: ClarifyingQuestion;
}
