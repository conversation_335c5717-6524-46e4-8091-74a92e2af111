import { InterscreenTypes } from "@/constants/question/interscreenTypes";
import { Interblock, InterblockVars } from "../interblock/types";
import { InterblockImage, InterblockImageVars } from "../interblockImage/types";
import { PollQuestionVars, IPollQuestion } from "./index.types";


export interface PollQuestionInterscreenVars extends PollQuestionVars {
  intermediateBlock: InterblockVars;
  foquzQuestionEndScreenLogos: Array<InterblockImageVars>
}

export interface IPollQuestionInterscreen extends IPollQuestion {
  isInterscreen: boolean;

  isInterStart: boolean;
  isInterEnd: boolean;
  isInterFiveSecondTest: boolean;
  isInterText: boolean;

  showQuestionNumber: boolean;

  interscreenConfig: Interblock,

  images: Array<InterblockImage>
}
