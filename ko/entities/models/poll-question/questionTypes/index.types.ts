import { GalleryVars, Gallery } from "../questionGallery/types";
import { Comment } from "../comment/types";
export interface PollQuestionVars {
  id: number;
  main_question_type: number;

  point_id: number;
  pointName: string;

  name: string;
  service_name: string;
  description: string;
  sub_description: string;

  enableGallery: boolean;
  gallery: GalleryVars;
  
  donor: number;

  comment_enabled?: 1|0;
  

  /**
     *
      text,
  
      poll_id,
      poll_is_auto,
      position,
      countAnswers,
  
      is_condition,
      is_deleted,
      is_required,
      is_system,
      is_tmp,
      is_updated,
      updated_at,
      updated_by,
  
      questionLogic,
      questionViewLogic,
  
      donor_chosen,
  
      dont_show_if_answered,
  
      link_with_client_field,
      linked_client_field,
      rewrite_linked_field,

  
     */
}

export interface IPollQuestion {
  id: string;
  type: string;

  isInterscreen: boolean;
  isInterStart: boolean;
  isInterEnd: boolean;
  isInterFiveSecondTest: boolean;
  isInterText: boolean;

  alias: string;
  name: string;
  text: string;
  description: string;

  galleryEnabled: boolean;
  gallery: Gallery;

  donorId: string;

  pointId: string;
  pointName: string;

  commentEnabled: boolean;
  comment: Comment | null;
}
