import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { PollQuestionVars, IPollQuestion } from "./index.types";
import { NpsDesign } from "@/constants/question/npsDesign";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";

export type NpsConfigVars = {
  design: NpsDesign;
  start_label: string;
  start_point_color: string;
  end_label: string;
  end_point_color: string;
};

export type NpsConfig = {
  design: NpsDesign;
  startLabel: string;
  startColor: string;
  endLabel: string;
  endColor: string;
};
export interface PollQuestionNpsVars extends PollQuestionVars {
  detail_answers: Array<QuestionVariantVars>;
  npsRatingSetting: NpsConfigVars;
  skip: number;
  extra_question_type: number;
  skip_text: string;
  set_variants: 0 | 1;
}

export interface IPollQuestionNps extends IPollQuestion {
  npsConfig: NpsConfig;
  setVariants: boolean;
  skip: boolean;
  skipText: string;
  variants: Array<QuestionVariant>;
  clarifyingQuestion: ClarifyingQuestion;
  clarifyingToEachQuestion?: Array<ClarifyingQuestion>;
}
