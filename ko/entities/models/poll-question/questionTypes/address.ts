import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionAddress } from "./address.types";

export class PollQuestionAddress
  extends PollQuestion
  implements IPollQuestionAddress
{
  placeholder: string;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { placeholder_text } = questionData;
    this.placeholder = placeholder_text;
  }
}
