import { InterscreenTypes } from "@/constants/question/interscreenTypes";
import { PollQuestion } from "./index";
import { IPollQuestionInterscreen, PollQuestionInterscreenVars } from "./inter.types";
import { FullPollQuestionVars } from "../types";
import { Interblock } from "../interblock/types";
import { QuestionInterblock } from "../interblock/index";
import { InterblockImage, InterblockImageVars } from "../interblockImage/types";
import { QuestionInterblockImage } from "../interblockImage";

export class PollQuestionInterscreen
  extends PollQuestion
  implements IPollQuestionInterscreen
{
  isInterscreen: boolean;
  isInterStart: boolean;
  isInterEnd: boolean;
  isInterFiveSecondTest: boolean;
  isInterText: boolean;
  showQuestionNumber: boolean;
  interscreenConfig: Interblock;
  images: Array<InterblockImage>;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { intermediateBlock, foquzQuestionEndScreenLogos } = questionData as PollQuestionInterscreenVars;

    this.isInterscreen = true;

    this.interscreenConfig = QuestionInterblock(intermediateBlock);

    const { screenType, showQuestionNumber } = this.interscreenConfig;

    this.isInterStart = screenType === InterscreenTypes.Start;
    this.isInterEnd = screenType === InterscreenTypes.End;
    this.isInterFiveSecondTest = screenType === InterscreenTypes.FiveSecondTest;
    this.isInterText = screenType === InterscreenTypes.Text;

    this.showQuestionNumber = showQuestionNumber;

    this.images = foquzQuestionEndScreenLogos.map(
      (i: InterblockImageVars): InterblockImage => QuestionInterblockImage(i)
    );
  }
}
