import { PollQuestionVars, IPollQuestion } from "./index.types";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";

export type MatrixConfig = {
  type: string;
  rows: Array<string>;
  donorRows?: Array<string>;
  cols: Array<string>;
  points: Array<Array<string>>;
};
export interface PollQuestionMatrixVars extends PollQuestionVars {
  matrixSettings: MatrixConfig;

  skip: number;
  skip_text: string;
  select_placeholder_text: string;
}

export interface IPollQuestionMatrix extends IPollQuestion {
  matrixConfig: MatrixConfig;

  skip: boolean;
  skipText: string;

  clarifyingQuestion: ClarifyingQuestion;
  selectPlaceholder: string;
  dropdown: number;
}
