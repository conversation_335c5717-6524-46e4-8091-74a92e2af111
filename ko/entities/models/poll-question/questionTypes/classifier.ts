import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import {
  ClassifierListTypes,
  ClassifierSorts,
  IPollQuestionClassifier,
} from "./classifier.types";
import { VariantTypes } from "@/constants/question/variantTypes";
import { PollQuestionClassifierVars } from './classifier.types';

export class PollQuestionClassifier
  extends PollQuestion
  implements IPollQuestionClassifier
{
  dictionaryId: string;
  dictionaryItems: Array<string>;
  dropdown: boolean;
  listType: ClassifierListTypes;
  variantType: VariantTypes;
  sort: ClassifierSorts;
  maxVariantsCount: number;
  skip: boolean;
  skipText: string;
  show_tooltips: number;
  detailAnswers: Array<{
    id: string;
    value: string;
  }>;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const {
      dictionary_id,
      dictionary_list_type,
      dictionary_sort,
      dropdown_variants,
      detail_answers,
      variants_element_type,
      max_choose_variants,
      skip,
      skip_text,
      show_tooltips
    } = questionData as PollQuestionClassifierVars;

    this.dictionaryId = dictionary_id && `${dictionary_id}`;
    this.dictionaryItems = (detail_answers || []).map(item => item.id);
    this.detailAnswers = detail_answers;
    this.dropdown = dropdown_variants === 1;
    this.listType = dictionary_list_type || ClassifierListTypes.Tree;
    this.variantType = variants_element_type;
    this.sort = dictionary_sort || ClassifierSorts.Default;
    this.maxVariantsCount = max_choose_variants;
    this.skip = Boolean(skip);
    this.skipText = skip_text;
    this.show_tooltips = show_tooltips;
  }
}
