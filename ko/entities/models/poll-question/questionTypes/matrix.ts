import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionMatrix, MatrixConfig } from "./matrix.types";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";
import { PollClarifyingQuestion } from "../clarifyingQuestion/index";

export class PollQuestionMatrix
  extends PollQuestion
  implements IPollQuestionMatrix
{
  matrixConfig: MatrixConfig;
  skip: boolean;
  skipText: string;
  clarifyingQuestion: ClarifyingQuestion;
  dropdown: number;
  selectPlaceholder: string;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { matrixSettings, skip, skip_text, select_placeholder_text, dropdown_variants } = questionData;

    this.matrixConfig = matrixSettings;

    this.skip = Boolean(skip);
    this.skipText = skip_text;
    this.dropdown = dropdown_variants;
    this.selectPlaceholder = select_placeholder_text;

    this.clarifyingQuestion = PollClarifyingQuestion(questionData);
  }
}
