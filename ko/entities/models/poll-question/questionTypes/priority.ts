import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";
import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionPriority, PollQuestionPriorityVars } from "./priority.types";
import { PollQuestionVariant } from "../questionVariant/index";

export class PollQuestionPriority
  extends PollQuestion
  implements IPollQuestionPriority
{
  variants: Array<QuestionVariant>;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { detail_answers } = questionData as PollQuestionPriorityVars;

    this.variants = detail_answers.map(
      (item: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(item);
      }
    );
  }
}
