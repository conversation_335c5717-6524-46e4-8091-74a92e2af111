import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionRating } from "./rating.types";
import { QuestionStarRating } from "../starRating/index";
import { StarRating } from "../starRating/types";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";
import { PollClarifyingQuestion } from "../clarifyingQuestion/index";

export class PollQuestionRating
  extends PollQuestion
  implements IPollQuestionRating
{
  starRating: StarRating;
  skip: boolean;
  skipText: string;
  clarifyingQuestion: ClarifyingQuestion;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { starRatingOptions, skip, skip_text } = questionData;

    this.starRating = QuestionStarRating(starRatingOptions);
    this.skip = Boolean(skip);
    this.skipText = skip_text;

    this.clarifyingQuestion = PollClarifyingQuestion(questionData);
  }
}
