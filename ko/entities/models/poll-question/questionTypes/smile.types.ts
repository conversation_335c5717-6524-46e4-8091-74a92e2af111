import { PollQuestionVars, IPollQuestion } from "./index.types";
import { ClarifyingQuestion } from "../clarifyingQuestion/types";

export type SmileVars = {
  id: number;
  label: string;
  smile_url: string;
};

export type Smile = {
  id: string;
  text: string;
  url: string;
};
export interface PollQuestionSmileVars extends PollQuestionVars {
  smiles: Array<SmileVars>;

  skip: number;
  skip_text: string;
}

export interface IPollQuestionSmile extends IPollQuestion {
  smiles: Array<Smile>;
  clarifyingQuestion: ClarifyingQuestion;
  skip: boolean;
  skipText: string;
}
