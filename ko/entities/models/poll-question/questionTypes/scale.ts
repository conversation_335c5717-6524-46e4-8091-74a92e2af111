import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import {
  IPollQuestionScale,
  PollQuestionScaleVars,
  ScaleConfigVars
} from "./scale.types";

import { QuestionVariant, QuestionVariantVars } from "@/entities/models/poll-question/questionVariant/types";
import { PollQuestionVariant } from "@/entities/models/poll-question/questionVariant";

export class PollQuestionScale extends PollQuestion implements IPollQuestionScale {
  skip: boolean;
  skipText: string;
  setVariants: boolean;
  variants: Array<QuestionVariant>;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { skip, skip_text, set_variants, detail_answers } = questionData as PollQuestionScaleVars;

    this.variants = detail_answers.map(
      (item: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(item);
      }
    );

    this.skip = Boolean(skip);
    this.skipText = skip_text;
    this.setVariants = !!set_variants;
  }
}