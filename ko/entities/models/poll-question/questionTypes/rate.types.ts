import { PollQuestionVars, IPollQuestion } from "./index.types";

import { ClarifyingQuestion } from "../clarifyingQuestion/types";
import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";

export enum RateViews {
  Text = "text",
  Image = "image",
  Video = "video",
}

export enum RateTypes {
  Stars = 1,
  Variants = 2,
}
export interface PollQuestionRateVars extends PollQuestionVars {
  type: RateViews;

  text: string;
  rating_type: RateTypes;

  detail_answers: Array<QuestionVariantVars>;

  is_self_answer: number;
  self_variant_text: string;
  placeholder_text: string;
}

export interface IPollQuestionRate extends IPollQuestion {
  viewType: RateViews;
  ratingText: string;
  ratingType: RateTypes;
  clarifyingQuestion: ClarifyingQuestion;

  variants: Array<QuestionVariant>;

  selfAnswer: boolean;
  selfAnswerText: string;
  selfAnswerPlaceholder: string;
}
