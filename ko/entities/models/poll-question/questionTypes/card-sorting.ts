import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { PollQuestionVariant } from "../questionVariant";
import { IPollQuestionCardSorting, PollQuestionCardSortingVars } from "./card-sorting.types";
import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";

export class PollQuestionCardSorting
  extends PollQuestion
  implements IPollQuestionCardSorting
{
  categoryList: Array<QuestionVariant>;
  cardColumnText: string;
  categoryColumnText: string;
  randomCategoryOrder: boolean;
  randomCardOrder: boolean;
  minCardInCategory: number;
  maxCardInCategory: number;
  skip: boolean;
  skipText: string;
  variants: Array<QuestionVariant>;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const {
      cardSortingCategories,
      card_column_text,
      category_column_text,
      random_categories_order,
      random_variants_order,
      min_choose_variants,
      max_choose_variants,
      skip,
      skip_text,
      detail_answers,
    } = questionData as PollQuestionCardSortingVars;

    
    this.cardColumnText = card_column_text;
    this.categoryColumnText = category_column_text;
    this.randomCategoryOrder = !!random_categories_order;
    this.randomCardOrder = !!random_variants_order;
    this.minCardInCategory = min_choose_variants;
    this.maxCardInCategory = max_choose_variants;
    this.skip = !!skip;
    this.skipText = skip_text;

    const variants = detail_answers.filter(el => !el?.extra_question);
    this.variants = variants
      .map((item: QuestionVariantVars): QuestionVariant => PollQuestionVariant(item));
    this.categoryList = cardSortingCategories
      .map((item: QuestionVariantVars): QuestionVariant => PollQuestionVariant(item));
  }
}
