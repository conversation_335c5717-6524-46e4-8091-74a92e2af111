import { MaskTypes } from "@/constants/question/maskTypes";
import { PollQuestionVars, IPollQuestion } from "./index.types";
import { NameMaskVars, TextMask } from "../textFieldMask/types";

export type QuizItemVars = {
  id: number;
  isRequired: number;
  isTextarea: boolean;
  label: string;
  maskType: MaskTypes;
  maskConfig: NameMaskVars;
  placeholderText: string;
}

export type QuizItem = {
  id: string;
  required: boolean;
  textarea: boolean;
  label: string;
  mask: TextMask;
}

export interface PollQuestionQuizVars extends PollQuestionVars {
  quizzes: Array<QuizItemVars>;
}

export interface IPollQuestionQuiz extends IPollQuestion {
  fields: Array<QuizItem>;
}
