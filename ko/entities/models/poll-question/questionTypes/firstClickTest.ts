import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import {
  IPollQuestionFirstClickTest,
  PollQuestionFirstClickTestVars,
  IFirstClick,
} from "./firstClickTest.types";

export class PollQuestionFirstClickTest
  extends PollQuestion
  implements IPollQuestionFirstClickTest
{
  skip: boolean;
  skipText: string;
  firstClick: IFirstClick;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { skip, skip_text, firstClick } = questionData as PollQuestionFirstClickTestVars;

    this.skip = Boolean(skip);
    this.skipText = skip_text;
    this.firstClick = firstClick;
  }
}