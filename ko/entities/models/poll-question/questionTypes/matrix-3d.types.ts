import { PollQuestionVars, IPollQuestion } from "./index.types";
import { QuestionVariantVars, QuestionVariant } from "../questionVariant/types";

export interface PollQuestionMatrix3DVars extends PollQuestionVars {
  matrixElements: any;

  is_self_answer: number;
  self_variant_text: string;
  select_placeholder_text: string;

  skip: number;
  skip_text: string;
  show_tooltips: number
}

export interface IPollQuestionMatrix3D extends IPollQuestion {
  rows: Array<QuestionVariant>;
  cols: Array<QuestionVariant>;

  skip: boolean;
  skipText: string;

  selectPlaceholder: string;

  deleted?: boolean;
}
