import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionVariants, PollQuestionVariantsVars } from "./variants.types";
import { QuestionVariantVars, QuestionVariant } from "../questionVariant/types";
import { PollQuestionVariant } from "../questionVariant/index";

export class PollQuestionVariants
  extends PollQuestion
  implements IPollQuestionVariants
{
  variants: Array<QuestionVariant>;
  detailAnswers: Array<QuestionVariantVars>;

  skip: boolean;
  skipText: string;

  selfAnswer: boolean;
  selfAnswerText: string;
  selfAnswerDescription: string;
  selfAnswerPlaceholder: string;
  show_tooltips: number;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const {
      detail_answers,
      is_self_answer,
      self_variant_text,
      self_variant_placeholder_text,
      skip,
      skip_text,
      show_tooltips,
      self_variant_description
    } = questionData as PollQuestionVariantsVars;

    this.variants = detail_answers.map(
      (item: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(item);
      }
    );

    this.skip = Boolean(skip);
    this.skipText = skip_text;

    this.selfAnswer = !!is_self_answer;
    this.selfAnswerText = self_variant_text;
    this.selfAnswerDescription = self_variant_description;
    this.selfAnswerPlaceholder = self_variant_placeholder_text;
    this.show_tooltips = show_tooltips;
  }

  getAllVariants() {
    return [
      ...this.variants,
      this.selfAnswer && {
        id: "-1",
        text: this.selfAnswerText,
      },
    ].filter(Boolean);
  }
}
