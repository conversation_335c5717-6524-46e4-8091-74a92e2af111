import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import {
  IPollQuestionDistributionScale,
  PollQuestionDistributionScaleVars,
} from "./distributionScale.types";

import { QuestionVariant, QuestionVariantVars } from "@/entities/models/poll-question/questionVariant/types";
import { PollQuestionVariant } from "@/entities/models/poll-question/questionVariant";

export class PollQuestionDistributionScale extends PollQuestion implements IPollQuestionDistributionScale {
  skip: boolean;
  skipText: string;
  indicatorText: string;
  variants: Array<QuestionVariant>;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    console.log('questionData', questionData)
    const { skip, skip_text, detail_answers, self_variant_text } = questionData as PollQuestionDistributionScaleVars;

    this.variants = detail_answers.map(
      (item: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(item);
      }
    );

    this.skip = Boolean(skip);
    this.skipText = skip_text;
    this.indicatorText = self_variant_text;
  }
}