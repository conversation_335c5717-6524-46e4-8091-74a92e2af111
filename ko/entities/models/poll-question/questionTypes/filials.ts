import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { FilialId, FilialIdVars, IPollQuestionFilials } from "./filials.types";

export class PollQuestionFilials
  extends PollQuestion
  implements IPollQuestionFilials
{
  filials: Array<FilialId>;
  skip: boolean;
  skipText: string;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { detail_question, skip, skip_text } = questionData;

    this.skip = skip;
    this.skipText = skip_text;

    let filials = [];

    if (detail_question) {
      try {
        const parsedFilials = JSON.parse(detail_question);
        if (Array.isArray(parsedFilials)) {
          const filialsSet = new Set(parsedFilials.map((fId) => `${fId}`));
          filials = Array.from(filialsSet.values());
        }
      } catch (e) {
        console.error(e);
      }
    }

    this.filials = filials.map((fId: FilialIdVars): FilialId => {
      return `${fId}`;
    });
  }

  getAllVariants() {
    return this.filials;
  }
}
