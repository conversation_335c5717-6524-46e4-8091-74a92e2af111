import { IPollQuestion, PollQuestionVars } from "./index.types";


export interface IFirstClick {
  button_text: string;
  id: string;
}

export interface PollQuestionFirstClickTestVars extends PollQuestionVars {
  skip: number;
  skip_text: string;
  firstClick: IFirstClick;
}

export interface IPollQuestionFirstClickTest extends IPollQuestion {
  skip: boolean;
  skipText: string;
  firstClick: IFirstClick;
}
