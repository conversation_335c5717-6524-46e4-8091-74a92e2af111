import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { DiffRow, DiffRowVars, IPollQuestionDiff, PollQuestionDiffVars } from "./diff.types";

export function formatDiffRow(data: DiffRowVars): DiffRow {
  const { id, position, start_label, end_label } = data;

  return {
    id: `${id}`,
    position,
    startLabel: start_label,
    endLabel: end_label,
  };
}

export class PollQuestionDiff
  extends PollQuestion
  implements IPollQuestionDiff
{
  diffConfig: Array<DiffRow>;
  skip: boolean;
  skipText: string;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { differentialRows, skip, skip_text } = questionData as PollQuestionDiffVars;

    this.diffConfig = differentialRows.map(
      (row: DiffRowVars): DiffRow => formatDiffRow(row)
    );

    this.skip = Boolean(skip);
    this.skipText = skip_text;
  }
}
