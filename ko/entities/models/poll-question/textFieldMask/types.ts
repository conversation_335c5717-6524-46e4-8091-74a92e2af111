import { MaskTypes } from "@/constants/question/maskTypes";
import { NameField } from "@/entities/structures/name-mask/types";

export type NameMaskFieldVars = {
  visible: string;
  required: string;
  placeholderText: string;
  minLength: string;
  maxLength: string;
};

export type NameMaskField = {
  visible: boolean;
  required: boolean;
  placeholder: string;
  minLenght: number;
  maxLength: number;
};

export type NameMaskVars = NameField<NameMaskFieldVars>;

export type NameMask = NameField<NameMaskField>;

export type TextMaskVars = {
  type: MaskTypes;
  nameMask?: NameMaskVars;
  placeholder: string;
};

export type TextMask = {
  type: MaskTypes;
  hasMask: boolean;
  isNameMask: boolean;
  isDateMonthMask: boolean;
  nameMask: NameMask;
  placeholder: string;
};
