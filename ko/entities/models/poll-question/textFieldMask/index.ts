import { MaskTypes } from "@/constants/question/maskTypes";
import {
  NameMaskFieldVars,
  NameMaskField,
  TextMaskVars,
  TextMask,
} from "./types";

export function QuestionNameMaskField(data: NameMaskFieldVars): NameMaskField {
  if (!data) {
    return {
      visible: false,
      required: false,
      placeholder: "",
      minLenght: 0,
      maxLength: 250,
    };
  }

  const { visible, required, placeholderText, minLength, maxLength } = data;

  return {
    visible: visible === "1" || visible === 'true',
    required: required === "1" || required === 'true',
    placeholder: placeholderText,
    minLenght: Number(minLength),
    maxLength: Number(maxLength),
  };
}

export function QuestionTextFieldMask(data: TextMaskVars): TextMask {
  const { type, nameMask, placeholder } = data;

  const hasMask = type !== MaskTypes.NoMask;
  const isNameMask = type === MaskTypes.Name;
  const isDateMonthMask = type === MaskTypes.DateMonth;

  const params: TextMask = {
    type,
    hasMask,
    isNameMask,
    isDateMonthMask,
    placeholder: isDateMonthMask ? "" : placeholder,
    nameMask: {
      name: QuestionNameMaskField(nameMask && nameMask.name),
      surname: QuestionNameMaskField(nameMask && nameMask.surname),
      patronymic: QuestionNameMaskField(nameMask && nameMask.patronymic),
    },
  };

  return params;
}
