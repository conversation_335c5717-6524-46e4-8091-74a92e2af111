import { Interblock, InterblockVars } from "./types";

export function QuestionInterblock(data: InterblockVars): Interblock {
  if (!data) {
    console.error('QuestionInterblock: data is null or undefined');
    return {
      id: '',
      screenType: 'text' as any,
      showQuestionNumber: false,
      text: '',
      poolId: 0,
      pollButtonText: '',
      readyButtonEnabled: false,
      readyButtonText: '',
      readyButtonLink: '',
      reportButtonEnabled: false,
      reportButtonText: '',
      agreementButtonEnabled: false,
      agreementButtonText: '',
      showImageButtonText: '',
      complaintButtonEnabled: false,
      complaintButtonText: '',
      unsubscribeButtonEnabled: false,
      unsubscribeButtonText: '',
      closeButtonForWidgetEnabled: false,
      closeButtonForWidgetText: '',
      restartButtonEnabled: false,
      restartButtonText: '',
      imagesBg: '',
    };
  }
  
  const {
    id,
    screen_type,
    show_question_number,
    text,
    pool_id,
    poll_button_text,
    ready_button,
    ready_button_text,
    external_link,
    agreement,
    agreement_text,
    show_image_button_text,
    complaint_button,
    complaint_button_text,
    unsubscribe_button,
    unsubscribe_button_text,
    scores_button,
    scores_button_text,
    start_over_button,
    start_over_button_text,
    close_widget_button,
    close_widget_button_text,
    logos_backcolor,
    socNetworks,
  } = data;

  return {
    id: `${id}`,

    screenType: screen_type,
    showQuestionNumber: !!show_question_number,
    text,
    poolId: pool_id,

    pollButtonText: poll_button_text,

    readyButtonEnabled: !!ready_button,
    readyButtonText: ready_button_text,
    readyButtonLink: external_link,

    reportButtonEnabled: !!scores_button,
    reportButtonText: scores_button_text,

    agreementButtonEnabled: !!agreement,
    agreementButtonText: agreement_text,

    showImageButtonText: show_image_button_text,

    complaintButtonEnabled: !!complaint_button,
    complaintButtonText: complaint_button_text,

    unsubscribeButtonEnabled: !!unsubscribe_button,
    unsubscribeButtonText: unsubscribe_button_text,

    closeButtonForWidgetEnabled: !!close_widget_button,
    closeButtonForWidgetText: close_widget_button_text,

    restartButtonEnabled: !!start_over_button,
    restartButtonText: start_over_button_text,

    imagesBg: logos_backcolor,
  };
}
