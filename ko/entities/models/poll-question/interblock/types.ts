import { InterscreenTypes } from "@/constants/question/interscreenTypes";

export type InterblockSocialsVars = {};



export type InterblockVars = {
  id: number;

  screen_type: number;
  show_question_number: boolean;

  text: string;

  pool_id: number;

  poll_button_text: string;

  ready_button: number;
  ready_button_text: string;
  external_link: string;

  agreement: number;
  agreement_text: string;

  show_image_button_text: string;

  complaint_button: number;
  complaint_button_text: string;

  close_widget_button: number;
  close_widget_button_text: string;

  unsubscribe_button: number;
  unsubscribe_button_text: string;

  scores_button: number;
  scores_button_text: string;

  start_over_button: number;
  start_over_button_text: string;

  logos_backcolor: string;

  socNetworks: InterblockSocialsVars;
};

export type Interblock = {
  id: string;

  screenType: InterscreenTypes;
  showQuestionNumber: boolean;
  text: string;
  poolId: number;

  pollButtonText: string;

  readyButtonEnabled: boolean;
  readyButtonText: string;
  readyButtonLink: string;

  agreementButtonEnabled: boolean;
  agreementButtonText: string;

  showImageButtonText: string;

  complaintButtonEnabled: boolean;
  complaintButtonText: string;

  unsubscribeButtonEnabled: boolean;
  unsubscribeButtonText: string;

  closeButtonForWidgetEnabled: boolean;
  closeButtonForWidgetText: string;

  restartButtonEnabled: boolean;
  restartButtonText: string;

  reportButtonEnabled: boolean;
  reportButtonText: string;

  imagesBg: string;
};
