import { Constructor } from "@/types";

import { QuestionTypes } from "@/constants/question/questionTypes";

import { FullPollQuestion, FullPollQuestionVars } from "./types";
import { PollQuestion } from "./questionTypes/index";

import { PollQuestionInterscreen } from "./questionTypes/inter";
import { PollQuestionStars } from "./questionTypes/stars";
import { PollQuestionVariants } from "./questionTypes/variants";
import { PollQuestionText } from "./questionTypes/text";
import { PollQuestionDate } from "./questionTypes/date";
import { PollQuestionAddress } from "./questionTypes/address";
import { PollQuestionFile } from "./questionTypes/file";
import { PollQuestionQuiz } from "./questionTypes/quiz";
import { PollQuestionMediaVariants } from "./questionTypes/mediaVariants";
import { PollQuestionGallery } from "./questionTypes/gallery";
import { PollQuestionSmile } from "./questionTypes/smile";
import { PollQuestionNps } from "./questionTypes/nps";
import { PollQuestionMatrix } from "./questionTypes/matrix";
import { PollQuestionMatrix3D } from "./questionTypes/matrix-3d";
import { PollQuestionStarVariants } from "./questionTypes/starVariants";
import { PollQuestionRate } from "./questionTypes/rate";
import { PollQuestionPriority } from "./questionTypes/priority";
import { PollQuestionDiff } from "./questionTypes/diff";
import { PollQuestionRating } from "./questionTypes/rating";
import { PollQuestionFilials } from "./questionTypes/filials";
import { PollQuestionClassifier } from "./questionTypes/classifier";
import { PollQuestionScale } from "./questionTypes/scale";
import { PollQuestionDistributionScale } from "./questionTypes/distributionScale";
import { PollQuestionCardSorting } from "./questionTypes/card-sorting";
import { PollQuestionFirstClickTest } from "./questionTypes/firstClickTest";

const models: {
  [key: string]: Constructor<PollQuestion>;
} = {
  [QuestionTypes.Inter]: PollQuestionInterscreen,
  [QuestionTypes.Stars]: PollQuestionStars,
  [QuestionTypes.Variants]: PollQuestionVariants,
  [QuestionTypes.Text]: PollQuestionText,
  [QuestionTypes.Date]: PollQuestionDate,
  [QuestionTypes.Address]: PollQuestionAddress,
  [QuestionTypes.File]: PollQuestionFile,
  [QuestionTypes.Quiz]: PollQuestionQuiz,
  [QuestionTypes.Priority]: PollQuestionPriority,
  [QuestionTypes.MediaVariants]: PollQuestionMediaVariants,
  [QuestionTypes.Gallery]: PollQuestionGallery,
  [QuestionTypes.Smile]: PollQuestionSmile,
  [QuestionTypes.Nps]: PollQuestionNps,
  [QuestionTypes.Matrix]: PollQuestionMatrix,
  [QuestionTypes.Matrix3D]: PollQuestionMatrix3D,
  [QuestionTypes.Diff]: PollQuestionDiff,
  [QuestionTypes.StarVariants]: PollQuestionStarVariants,
  [QuestionTypes.Rate]: PollQuestionRate,
  [QuestionTypes.Rating]: PollQuestionRating,
  [QuestionTypes.Filials]: PollQuestionFilials,
  [QuestionTypes.Classifier]: PollQuestionClassifier,
  [QuestionTypes.Scale]: PollQuestionScale,
  [QuestionTypes.DistributionScale]: PollQuestionDistributionScale,
  [QuestionTypes.CardSorting]: PollQuestionCardSorting,
  [QuestionTypes.FirstClickTest]: PollQuestionFirstClickTest,
};

export function PollQuestionFactory(
  questionData: FullPollQuestionVars
): FullPollQuestion {
  const { main_question_type } = questionData;

  const type: string = String(main_question_type);

  const model = models[type];

  if (model) return new model(questionData);
  return null;
}
