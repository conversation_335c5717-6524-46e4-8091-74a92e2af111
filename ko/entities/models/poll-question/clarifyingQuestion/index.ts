import { QuestionTypes } from "@/constants/question/questionTypes";
import {
  ClarifyingQuestionVars,
  ClarifyingQuestion,
  ClarifyingQuestionTypes,
} from "./types";
import { QuestionVariantVars, QuestionVariant } from '../questionVariant/types';
import { PollQuestionVariant } from "../questionVariant";

function getVariants(data: ClarifyingQuestionVars) {
  const type = `${data.main_question_type}`;
  if (type === QuestionTypes.StarVariants || type === QuestionTypes.Nps) {
    return data.detail_answers.filter(el => el?.extra_question);
  }
  
  return data.detail_answers;
}

export function PollClarifyingQuestion(
  data: ClarifyingQuestionVars
): ClarifyingQuestion {
  const {
    detail_question,
    variants_element_type,
    is_self_answer,
    self_variant_text,
    placeholder_text,
    selfVariantFile
  } = data;

  const pollClarifyingQuestion = {
    enabled: !!detail_question,
    text: detail_question,
    type: variants_element_type,
    isTextAnswer: variants_element_type === ClarifyingQuestionTypes.text,
    textAnswerPlaceholder: placeholder_text,
    variants: getVariants(data).map((variant: QuestionVariantVars): QuestionVariant => {
        return PollQuestionVariant(variant);
    }),
    selfAnswer: !!is_self_answer,
    selfAnswerText: self_variant_text,
    selfAnswerPlaceholder: placeholder_text,
    selfVariantFile: selfVariantFile
  };

  console.log('clarifyingQuestion pollClarifyingQuestion', pollClarifyingQuestion)

  return pollClarifyingQuestion;
}

export function PollClarifyingToEachQuestion(
  data: QuestionVariantVars,
  question: ClarifyingQuestionVars

): ClarifyingQuestion {
  const {
    id,
    detail_question,
    variants_element_type,
    is_self_answer,
    self_variant_text,
    self_variant_placeholder_text,
    placeholder_text,
    selfVariantFile
  } = data;

  const pollClarifyingQuestion = {
    enabled: !!detail_question,
    text: detail_question,
    donorId: id,
    type: variants_element_type,
    isTextAnswer: variants_element_type === ClarifyingQuestionTypes.text,
    textAnswerPlaceholder: placeholder_text,
    variants: getVariants(question)
      .filter(el => id === el?.question_detail_id)
      .map((variant: QuestionVariantVars): QuestionVariant => PollQuestionVariant(variant)),
    selfAnswer: !!is_self_answer,
    selfAnswerText: self_variant_text,
    selfAnswerPlaceholder: self_variant_placeholder_text,
    selfVariantFile: selfVariantFile
  };

  return pollClarifyingQuestion;
}