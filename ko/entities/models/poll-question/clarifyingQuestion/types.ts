import { QuestionVariant, QuestionVariantVars } from "../questionVariant/types";

export enum ClarifyingQuestionTypes {
  "single" = 0,
  "multiple" = 1,
  "text" = 2,
}

export type ClarifyingQuestionVars = {
  detail_question: string;
  detail_answers: Array<QuestionVariantVars>;
  variants_element_type: ClarifyingQuestionTypes;
  is_self_answer: number;
  self_variant_text: string;
  self_variant_placeholder_text: string;
  placeholder_text: string;
  main_question_type: number;
  selfVariantFile?: object
};

export type ClarifyingQuestion = {
  donorId?: number;
  enabled: boolean;
  text: string;
  type: ClarifyingQuestionTypes;
  isTextAnswer: boolean;
  textAnswerPlaceholder: string;
  variants: Array<QuestionVariant>;
  selfAnswer: boolean;
  selfAnswerText: string;
  selfAnswerPlaceholder: string;
  required?: number;
  selfVariantFile?: object
};
