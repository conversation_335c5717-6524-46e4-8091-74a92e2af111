import { InterblockImage, InterblockImageVars } from "./types";
export function QuestionInterblockImage(
  data: InterblockImageVars
): InterblockImage {
  if (!data) {
    console.error('QuestionInterblockImage: data is null or undefined');
    return {
      id: '',
      description: '',
      url: '',
    };
  }
  
  const { id, description, logo, external_logo } = data;

  return {
    id: `${id}`,
    description,
    url: logo || external_logo,
  };
}
