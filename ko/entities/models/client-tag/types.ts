import { DateMoment } from "@/types";

export type ClientTagConditionVars = {
    tag_id: number;
    condition_id: string;
    value: {
        sourceType: string;
        value: string;
    }
}

export type ClientTagCondition = {

}

export type ClientTagVars = {
    id: number;
    tag: string;
    auto_add: 0 | 1;
    company_id: number;
    conditions: Array<ClientTagConditionVars>,
    contacts_count: string;
    created_at: DateMoment;
    created_by: number;
    updated_at: DateMoment;
    updated_by: number;
}

export type ClientTag = {
    id: string;
    name: string;
}