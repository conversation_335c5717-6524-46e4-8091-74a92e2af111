import {
  DAYS_WITHOUT_ORDER_TRIGGER,
  NO_ORDER_AS_USUAL_TRIGGER,
  ORDERS_TRIGGER,
  ORDER_MADE_TRIGGER
} from '@/dictionaries/trigger';
import { timeValidator } from '@/utils/validation/time';
import { FModel } from '@/entities/f-model';
import { Translator } from '@/utils/translate';
import { FmRange } from '@/entities/structures/fm-range';

export class FmTrigger extends FModel {
  constructor() {
    super();

    this.translator = Translator('trigger');
    this.validationTranslator = Translator('validation');

    this.trigger = ko.observable(ORDER_MADE_TRIGGER).extend({
      required: {
        message: () => this.validationTranslator.t('Обязательное поле')()
      }
    });

    this._isOrderTrigger = ko.computed(() => {
      return !!triggers[this.trigger()].order;
    });

    // Заказ оформлен
    // Заказ доставлен
    this.this.orderTime = ko.observable('').extend({
      required: {
        message: () => this.validationTranslator.t('Обязательное поле')(),
        onlyIf: () => this._isOrderTrigger()
      }
    });

    // Не было заказа дней
    // Не сделал заказ как обычно
    this.days = ko.observable('').extend({
      required: {
        message: () => this.validationTranslator.t('Обязательное поле')(),
        onlyIf: () => {
          let trigger = this.trigger();
          return (
            trigger === DAYS_WITHOUT_ORDER_TRIGGER ||
            trigger === NO_ORDER_AS_USUAL_TRIGGER
          );
        }
      }
    });

    // Все, кроме Заказ оформлен и Заказ доставлен
    this.sendTime = ko.observable('').extend({
      validation: {
        validator: timeValidator(),
        message: () => this.validationTranslator.t('Некорректный формат')(),
        onlyIf: () => {
          return !this.isOrderTrigger();
        }
      }
    });

    // Заказы
    this.ordersCount = ko.observable('').extend({
      required: {
        message: () => this.validationTranslator.t('Обязательное поле')(),
        onlyIf: () => {
          let trigger = this.trigger();
          return trigger === ORDERS_TRIGGER;
        }
      }
    });

    // Не сделал заказ как обычно
    this.ordersCountRange = new FmRange({ from: 2, to: 10 });
    this.ordersCountRange.from.extend({
      required: {
        message: () => this.validationTranslator.t('Обязательное поле')(),
        onlyIf: () => {
          let trigger = this.trigger();
          return trigger === NO_ORDER_AS_USUAL_TRIGGER;
        }
      },
      validation: [
        {
          validator: (v) => {
            return parseInt(v) >= 2;
          },
          message: 'Минимальное количество заказов должно быть больше 1',
          onlyIf: () => {
            let trigger = this.trigger();
            return trigger === NO_ORDER_AS_USUAL_TRIGGER;
          }
        },
        {
          validator: (v) => false,
          message: 'Некорректный диапазон',
          onlyIf: () =>
            this.trigger() === NO_ORDER_AS_USUAL_TRIGGER &&
            this.ordersCountRange.rangeError()
        }
      ]
    });
    this.ordersCountRange.to.extend({
      required: {
        message: () => this.validationTranslator.t('Обязательное поле')(),
        onlyIf: () => {
          let trigger = this.trigger();
          return trigger === NO_ORDER_AS_USUAL_TRIGGER;
        }
      },
      validation: [
        {
          validator: (v) => {
            return parseInt(v) >= 2;
          },
          message: 'Минимальное количество заказов должно быть больше 1',
          onlyIf: () => {
            let trigger = this.trigger();
            return trigger === NO_ORDER_AS_USUAL_TRIGGER;
          }
        },
        {
          validator: (v) => false,
          message: 'Некорректный диапазон',
          onlyIf: () =>
            this.trigger().type === 'NO_ORDER_AS_USUAL_TRIGGER' &&
            this.ordersCountRange.rangeError()
        }
      ]
    });

    // Не сделал заказ как обычно
    this.coefficient = ko.observable(1.5).extend({
      required: {
        message: 'Обязательное поле',
        onlyIf: () => this.trigger() === NO_ORDER_AS_USUAL_TRIGGER
      },
      validation: {
        validator: (v) => {
          let value = parseFloat(v);
          return value >= 1;
        },
        onlyIf: () => this.trigger() === NO_ORDER_AS_USUAL_TRIGGER,
        message: 'Коэффициент должен быть не менее 1'
      }
    });

    this.listen(
      this.trigger,
      this.orderTime,
      this.days,
      this.ordersCountRange,
      this.sendTime,
      this.ordersCountRange,
      this.coefficient
    );
    this.createValidator(
      this.trigger,
      this.orderTime,
      this.days,
      this.ordersCountRange.from,
      this.ordersCountRange.to,
      this.sendTime,
      this.ordersCountRange,
      this.coefficient
    );

    this.trigger.subscribe((v) => this.reset());
  }

  update(data) {
    let trigger = data.trigger || ORDER_MADE_TRIGGER;
    this.trigger('' + trigger);

    switch (trigger) {
      case ORDER_MADE_TRIGGER:
      case ORDER_DELIVERED_TRIGGER:
        this.time(data.orderTime);
        break;
      case DAYS_WITHOUT_ORDER_TRIGGER:
        let days = parseInt(data.days);
        this.days(days);
        this.sendTime(data.sendTime);
        break;
      case NO_ORDER_AS_USUAL_TRIGGER:
        this.days(data.days);
        this.ordersCountRange.from(data.ordersCountFrom);
        this.ordersCountRange.to(data.ordersCountTo);
        this.sendTime(data.sendTime);
        this.coefficient(data.coefficient);
        break;
      case ORDERS_TRIGGER:
        let orders = parseInt(data.ordersCount);
        this.ordersCount(orders);
        this.sendTime(data.sendTime);
        break;
    }
  }

  getData() {}
}

export class Trigger {
  getData() {
    let trigger = this.trigger();

    if (trigger == ORDER_MADE_TRIGGER || trigger == ORDER_DELIVERED_TRIGGER) {
      return {
        trigger,
        [this.triggerTimeField]: this.time(),
        [this.triggerDaysField]: '',
        send_time: ''
      };
    }

    if (trigger == DAYS_WITHOUT_ORDER_TRIGGER) {
      return {
        trigger,
        [this.triggerTimeField]: '',
        [this.triggerDaysField]: this.days(),
        send_time: this.sendTime()
      };
    }

    if (trigger == NO_ORDER_AS_USUAL_TRIGGER) {
      return {
        trigger,
        [this.triggerTimeField]: '',
        [this.triggerDaysField]: '',
        send_time: '',
        triggerSetting: {
          days: this.days(),
          min_orders: this.minOrders(),
          max_orders: this.maxOrders(),
          time: this.sendTime(),
          coefficient: this.coefficient()
        }
      };
    }

    if (trigger == ORDERS_TRIGGER) {
      return {
        trigger,
        send_time: this.sendTime(),
        [this.triggerOrdersField]: this.orders()
      };
    }

    return {
      trigger
    };
  }

  reset() {
    this.time('');
    this.days('');
    this.orders('');
    this.sendTime('');
    this.maxOrders(10);
    this.minOrders(2);
    this.coefficient(1.5);
  }
}
