import { ServerDateTime, ClientDate } from '../../../types';

export type FeedbackThemeVars = {
    id: number;
    company_id: number;
    theme: string;
    color: string;
    icon: string;
    deleted: boolean | null;
    created_at: ServerDateTime;
    button_text: string;
    hasFeedbacks: boolean;
}

export type FeedbackTheme = {
    id: string;
    companyId: string;
    theme: string;
    color: string;
    icon: string;
    deleted: boolean;
    createdAt: ClientDate;
    buttonText: string;
    hasFeedbacks: boolean;
}