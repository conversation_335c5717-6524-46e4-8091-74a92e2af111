import { FeedbackTheme, FeedbackThemeVars } from "./types";
import { serverDateStringToClientDateString } from "@/utils/date/formats";

export function FeedbackThemeModel(data: FeedbackThemeVars): FeedbackTheme {
  const {
    button_text,
    color,
    company_id,
    created_at,
    deleted,
    hasFeedbacks,
    icon,
    id,
    theme,
  } = data;
  return {
    id: `${id}`,
    companyId: `${company_id}`,
    theme,
    color,
    icon,
    createdAt: serverDateStringToClientDateString(created_at),
    buttonText: button_text,
    deleted: !!deleted,
    hasFeedbacks,
  };
}
