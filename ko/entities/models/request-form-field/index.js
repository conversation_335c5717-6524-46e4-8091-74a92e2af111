/**
 * RequestFormField
 *
 * Модель поля формы заявки/формы обработки заявки (раздел Заявки)
 *
 * Кастомные поля (данные):
 *
 * id
 * is_active
 * is_required
 * is_system
 * name
 * label
 * description
 * placeholder
 * rank
 * type_form: processing|order
 * type_field: text|select|date
 * text_field_type: one-line|multiline
 * text_field_min_length
 * text_field_max_length
 * select_field_type: single|multiple
 * select_options[{ id, name, text }]
 *
 * Системные поля (типы):
 * type_field: file
 */

import { ApiUrl } from "Utils/url/api-url";
import { PHONE_REGEX2 } from "Utils/regex/phone";

export class RequestFormField {
  constructor(data) {
    this.id = data.id;

    this.isSystem = data.is_system;
    this.isRequired = data.is_required;
    this.isActive = data.is_active;

    this.name = data.name;
    this.label = data.label;
    this.description = data.description;

    this.type = data.type_field;

    this.textType = data.text_field_type;
    this._placeholder = data.placeholder;
    this.minLength = data.text_field_min_length;
    this.maxLength = data.text_field_max_length;

    this.selectType = data.select_field_type;
    this.options = data.select_options;

    this.value = ko.observable("").extend({
      required: {
        message: "Обязательное поле",
        onlyIf: () => this.isSingle && this.isRequired,
      },
      minLength: {
        params: this.minLength,
        message: "Не менее " + this.minLength + " символов",
        onlyIf: () => this.minLength && this.isText,
      },
      validation: [
        {
          validator: (v) => {
            if (!v) return true;
            return moment(v, "DD.MM.YYYY").isValid();
          },
          message: "Некорректный формат",
          onlyIf: () => this.isDate,
        },
        {
          validator: (v) => {
            if (!v) return true;
            return PHONE_REGEX2.test(v);
          },
          message: "Некорректный формат",
          onlyIf: () => this.isPhone,
        },
        {
          validator: (v) => {
            if (!v) return true;
            return validator.isEmail(v);
          },
          message: "Некорректный формат",
          onlyIf: () => this.isEmail,
        },
      ],
    });
    this.values = ko.observableArray([]).extend({
      validation: {
        validator: (v) => {
          return v.length;
        },
        message: "Обязательное поле",
        onlyIf: () => this.isMultiple && this.isRequired,
      },
    });
    this.files = ko.observableArray([]).extend({
      validation: {
        validator: (v) => {
          return v.length;
        },
        message: "Обязательное поле",
        onlyIf: () => this.isFile && this.isRequired,
      },
    });

    this.selectConfig = {
      minimumResultsForSearch: this.name === "priority" ? 10 : 0,
      containerCssClass: "form-control",
      wrapperCssClass: "select2-container--form-control",
      placeholder: this.placeholder,
      allowClear: true,
    };

    this.getClients = ({ q }, success) => {
      let term = (q || "").toLowerCase();
      let url = ApiUrl("answers/client-search", { q });
      fetch(url)
        .then((res) => res.json())
        .then((json) => {
          success(json.items);
        });
    };

    this.isValid = ko.computed(() => {
      return (
        this.value.isValid() && this.values.isValid() && this.files.isValid()
      );
    });
  }

  get isText() {
    return this.type === "text";
  }

  get isInput() {
    return this.isText && this.textType === "one-line";
  }

  get isTextarea() {
    return this.isText && this.textType === "multiline";
  }

  get isFile() {
    return this.type === "file";
  }

  get isSelect() {
    return this.type === "select";
  }

  get isMultiple() {
    return this.type === "select" && this.selectType === "multiple";
  }

  get isSingle() {
    return (
      this.type === "text" ||
      this.type === "date" ||
      (this.type === "select" && this.selectType === "single")
    );
  }

  get isDate() {
    return this.type === "date";
  }

  get isPhone() {
    return this.isInput && this.name === "phone";
  }

  get isEmail() {
    return this.isInput && this.name === "email";
  }

  get isClient() {
    return this.isSelect && this.name === "client_id";
  }

  get mask() {
    if (this.isPhone) return "+7 (999) 999 - 9999";

    return null;
  }

  get placeholder() {
    if (this.isPhone) return "+7 (999) 999 - 9999";
    if (this.name === "priority") return "Выберите приоритет заявки";
    if (this.isClient) return "Выберите клиента";
    return this._placeholder;
  }

  reset() {
    this.value("");
    this.values([]);
    this.files([]);
  }

  getValue() {
    if (this.isClient) return this.value() && this.value().id;
    if (this.isFile) return this.files();
    if (this.isMultiple) return this.values();
    if (this.isDate) {
      let date = this.value();
      if (!date) return "";
      return moment(date, "DD.MM.YYYY").format("YYYY-MM-DD HH:mm:ss");
    }
    return this.value();
  }

  setValue(value) {
    if (this.isFile) this.files(value);
    else if (this.isMultiple) this.values(value);
    else if (this.isDate) {
      let date = moment(value, "YYYY-MM-DD").format("DD.MM.YYYY");
      this.value(date);
    } else this.value(value);
  }
}
