import {
  serverDateStringToClientDateString,
  serverDateStringToClientDateTimeString,
} from "@/utils/date/formats";
import { FileDataModel } from "../file";
import { RequestProcessingVars } from "./request.types";
import { Processing, ProcessingEntityTypes } from "@/entities/models/processing/types";

export function RequestProcessingModel(
  data: RequestProcessingVars
): Processing | null {
  if (!data) return null;

  const {
    id,
    created_at,

    status,

    authorName,
    author_id,
    authorPicture,
    authorUsername,

    moderator,

    executor,

    process_up,
    delayed_up,
    resolved_at,
    updated_at,

    reason,
    employee,
    fine,
    compensation,

    comment,
    executor_comment,

    custom_fields,

    processingFiles,

    notification_script_id,
    notificationScript,
    answerNotifications,
  } = data;

  return {
    entityType: ProcessingEntityTypes.Request,
    
    id: `${id}`,
    createdAt: serverDateStringToClientDateTimeString(created_at),
    resolvedAt: serverDateStringToClientDateTimeString(resolved_at),
    updatedAt: serverDateStringToClientDateTimeString(updated_at),

    status: status,

    authorId: author_id ? `${author_id}` : "",
    authorName: authorName || "",
    authorAvatar: authorPicture || "",
    authorLogin: authorUsername || "",

    moderatorId: (moderator && `${moderator.id}`) || "",
    moderatorName: (moderator && moderator.name) || "",
    moderatorAvatar: (moderator && moderator.avatar) || "",

    executorId: (executor && `${executor.id}`) || "",
    executorName: (executor && executor.name) || "",

    processUp: serverDateStringToClientDateString(process_up),
    delayedUp: serverDateStringToClientDateString(delayed_up),

    reasonId: (reason && `${reason.id}`) || "",
    reasonName: (reason && reason.title) || "",

    employeeId: (employee && `${employee.id}`) || "",
    employeeName: employee?.name || "",

    fineType: fine && fine.type,
    fineId: (fine && `${fine.id}`) || "",
    fineName: (fine && fine.title) || "",
    fineAmount: fine && fine.amount,

    compensationId: (compensation && `${compensation.id}`) || "",
    compensationName: (compensation && compensation.title) || "",

    comment: comment || "",
    executorComment: executor_comment || "",

    customFields: custom_fields,

    files: (processingFiles || []).map((file) => FileDataModel(file)),

    notificationScriptId: notification_script_id
      ? `${notification_script_id}`
      : "",
    notificationScript: notificationScript,
    answerNotifications: answerNotifications,

    companyFeedbackId: "",
  };
}
