import {
  serverDateStringToClientDateString,
  serverDateStringToClientDateTimeString,
} from "@/utils/date/formats";
import { FileDataModel } from "../file";
import { Processing, ProcessingEntityTypes, ProcessingVars } from "./types";

export function ProcessingModel(data: ProcessingVars): Processing | null {
  if (!data) return null;

  const {
    id,
    created_at,

    status,

    author_id,
    author,

    moderator_id,
    moderator,

    executor_id,
    executor,

    process_up,
    delayed_up,
    resolved_at,
    updated_at,

    reason_id,
    reasonName,

    employee_id,
    employeeName,

    fineType,
    fine_id,
    fineName,
    fine_amount,

    compensation_id,
    compensationName,

    notification_script_id,
    notificationScript,
    answerNotifications,

    comment,
    executor_comment,

    company_feedback_id,

    custom_fields,

    processingFiles,

    poll_answer_id,
    request_project_answer_id,
  } = data;

  return {
    entityType: ProcessingEntityTypes.Poll,

    id: `${id}`,
    createdAt: serverDateStringToClientDateTimeString(created_at),
    resolvedAt: serverDateStringToClientDateTimeString(resolved_at),
    updatedAt: serverDateStringToClientDateTimeString(updated_at),

    status: status,

    authorId: author_id ? `${author_id}` : "",
    authorName: author?.name || "",
    authorAvatar: author?.avatar || "",
    authorLogin: author?.username || "",

    moderatorId: moderator_id ? `${moderator_id}` : "",
    moderatorName: moderator?.name || "",
    moderatorAvatar: moderator?.avatar || "",

    executorId: executor_id ? `${executor_id}` : "",
    executorName: executor?.name || "",

    processUp: serverDateStringToClientDateString(process_up),
    delayedUp: serverDateStringToClientDateString(delayed_up),

    reasonId: reason_id ? `${reason_id}` : "",
    reasonName: reasonName || "",

    employeeId: employee_id ? `${employee_id}` : "",
    employeeName: employeeName || "",

    fineType: fineType,
    fineId: fine_id ? `${fine_id}` : "",
    fineName: fineName || "",
    fineAmount: fine_amount,

    compensationId: compensation_id ? `${compensation_id}` : "",
    compensationName: compensationName || "",

    notificationScriptId: notification_script_id
      ? `${notification_script_id}`
      : "",
    notificationScript: notificationScript,
    answerNotifications: answerNotifications,

    comment: comment || "",
    executorComment: executor_comment || "",

    companyFeedbackId: company_feedback_id ? `${company_feedback_id}` : "",

    customFields: custom_fields,

    files: (processingFiles || []).map((file) => FileDataModel(file)),

    pollAnswerId: poll_answer_id ? `${poll_answer_id}` : "",
    requestProjectAnswerId: request_project_answer_id
      ? `${request_project_answer_id}`
      : "",
  };
}
