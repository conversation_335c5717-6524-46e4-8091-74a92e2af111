import { ClientDate, ClientDateTime, ServerDate, ServerDateTime } from "@/types";
import { FileData, FileDataVars } from '@/entities/models/file/types';
import { ProcessingStatuses } from "@/constants/processing/processingStatuses";
import { FineTypes } from "@/constants/fine";
import { ProcessingNotification, ProcessingNotificationScript, ProcessingNotificationVars } from "./types";

export type RequestProcessingVars = {
  id: string;

  created_at: ServerDateTime;
  resolved_at: ServerDateTime | null;
  updated_at: ServerDateTime | null;

  status: ProcessingStatuses;

  authorName: string;
  author_id: number;
  authorPicture: string;
  authorUsername?: string;

  moderator: {
    id: number;
    name: string;
    avatar: string;
  } | null;
  executor: {
    id: number;
    name: string;
    avatar: string;
  } | null;

  process_up: ServerDate | null;
  delayed_up: ServerDateTime;

  reason: {
    id: number;
    title: string;
  } | null;

  employee: {
    id: number;
    name: string;
  } | null;

  fine: {
    type: FineTypes,
    id: number;
    title: string;
    amount: number;
  } | null;

  compensation: {
    id: number;
    title: string;
  } | null;

  notification_script_id: number | null;
  notificationScript: ProcessingNotificationScript | null;
  answerNotifications: Array<ProcessingNotificationVars>;

  comment: string;
  executor_comment: string | null;
 
  custom_fields: any;
  
  processingFiles: Array<FileDataVars>;
};

