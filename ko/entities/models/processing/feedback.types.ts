
import { FineTypes } from "@/constants/fine";
import { ProcessingStatuses } from "@/constants/processing/processingStatuses";
import { ClientDate, ClientDateTime, ServerDateTime } from "@/types";
import { FileData, FileDataVars } from "../file/types";
import { ProcessingNotification, ProcessingNotificationScript, ProcessingNotificationVars } from "./types";



export interface FeedbackProcessingVars {
  id: number;

  created_at: ServerDateTime;
  resolved_at: ServerDateTime | null;
  updated_at: ServerDateTime | null;

  author_id: number;
  author: {
    name: string | null;
    username: string | null;
    avatar: string | null;
  },

  status: ProcessingStatuses;
  statusName: number;

  moderator_id: number | null;
  moderator: {
    name: string | null;
    username: string | null;
    avatar: string | null;
  },

  executor_id: number | null;
  executor: {
    name: string | null;
    username: string | null;
    avatar: string | null;
  },

  process_up: ServerDateTime | null;
  delayed_up: ServerDateTime | null;

  reason_id: number | null;
  reasonName: string | null;

  employee_id: number | null;
  employeeName: string | null;

  fineType: FineTypes | null;
  fine_id: number | null;
  fineName: string | null;
  fine_amount: number | null;

  compensation_id: number | null;
  compensationName: string | null;

  notification_script_id: number | null;
  notificationScript: ProcessingNotificationScript | null;
  answerNotifications: Array<ProcessingNotificationVars>;

  comment: string | null;
  executor_comment: string | null;

  company_feedback_id: number | null;

  custom_fields: null;

  processingFiles: Array<FileDataVars>;

  poll_answer_id?: number;
  request_project_answer_id?: number;
}

