import { ChannelNames } from "@/constants/channels";
import { FineTypes } from "@/constants/fine";
import { ProcessingStatuses } from "@/constants/processing/processingStatuses";
import { ClientDate, ClientDateTime, ServerDateTime } from "@/types";
import { FileData, FileDataVars } from "../file/types";

export enum ProcessingEntityTypes {
  Poll,
  Feedback,
  Request,
}

export type ProcessingNotificationScript = {
  id: number;
  name: string;
  deleted: 0 | 1;
};

export type ProcessingNotificationVars = {
  id: number;
  answer_processing_id: number;
  channel_name: ChannelNames;
  name: string;
  pool_id: number;
  sender: string;
  sender_name: string;
  subject: string;
  text: string;
};

export type ProcessingNotification = {
  id: number;
  answer_processing_id: number;
  channel_name: ChannelNames;
  name: string;
  pool_id: number;
  sender: string;
  sender_name: string;
  subject: string;
  text: string;
};

export interface ProcessingVars {
  id: number;

  created_at: ServerDateTime;
  resolved_at: ServerDateTime | null;
  updated_at: ServerDateTime | null;

  status: ProcessingStatuses;
  statusName: number;

  author_id: number;
  author: {
    name: string | null;
    username: string | null;
    avatar: string | null;
  },

  moderator_id: number | null;
  moderator: {
    name: string | null;
    username: string | null;
    avatar: string | null;
  },

  executor_id: number | null;
  executor: {
    name: string | null;
    username: string | null;
    avatar: string | null;
  },

  process_up: ServerDateTime | null;
  delayed_up: ServerDateTime | null;

  reason_id: number | null;
  reasonName: string | null;

  employee_id: number | null;
  employeeName: string | null;

  fineType: FineTypes | null;
  fine_id: number | null;
  fineName: string | null;
  fine_amount: number | null;

  compensation_id: number | null;
  compensationName: string | null;

  notification_script_id: number | null;
  notificationScript: ProcessingNotificationScript | null;
  answerNotifications: Array<ProcessingNotificationVars>;

  comment: string | null;
  executor_comment: string | null;

  company_feedback_id: number | null;

  custom_fields: null;

  processingFiles: Array<FileDataVars>;

  poll_answer_id?: number;
  request_project_answer_id?: number;
}

export interface Processing {
  entityType: ProcessingEntityTypes;
  
  id: string;

  createdAt: ClientDateTime;
  resolvedAt: ClientDateTime;
  updatedAt: ClientDateTime;

  status: ProcessingStatuses;

  authorName: string;
  authorId: string;
  authorAvatar: string;
  authorLogin: string;

  moderatorId: string;
  moderatorName: string | null;
  moderatorAvatar?: string;

  executorId: string;
  executorName: string | null;

  processUp: ClientDate | null;
  delayedUp: ClientDate | null;

  reasonId: string;
  reasonName: string | null;

  employeeId: string;
  employeeName: string | null;

  fineType: FineTypes;
  fineId: string;
  fineName: string;
  fineAmount: number | null;

  compensationId: string;
  compensationName: string;

  notificationScriptId: string;
  notificationScript: ProcessingNotificationScript | null;
  answerNotifications: Array<ProcessingNotification>;

  comment: string | null;
  executorComment: string | null;

  companyFeedbackId: string;

  customFields: null;

  files: Array<FileData>;

  pollAnswerId?: string;
  requestProjectAnswerId?: string;
}
