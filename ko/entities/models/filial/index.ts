import { FilialEntity, FilialEntityVars } from "./types";
import { serverDateStringToClientDateString } from "@/utils/date/formats";

export function FilialDataModel(data: FilialEntityVars): FilialEntity {
  const {
    id,
    name,
    logo,
    address,
    is_active,
    category_id,
    category_name,
    company_id,
    created_at,
    google_coordinates,
    google_location,
    yandex_link,
    crm_id,
    iiko_id,
  } = data;

  return {
    id: `${id}`,
    name,
    logo,
    address,
    active: is_active === 1,
    categoryId: category_id ? `${category_id}` : "",
    categoryName: category_name || "",
    companyId: `${company_id}`,
    createdAt: serverDateStringToClientDateString(created_at),
    googleLocation: google_location || "",
    googleCoords: google_coordinates || "",
    yandexLink: yandex_link || "",
    crmId: crm_id ? `${crm_id}` : "",
    iikoId: iiko_id ? `${iiko_id}` : "",
  };
}
