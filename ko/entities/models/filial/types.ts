import { ServerDate, ClientDate } from "../../../types";
export type FilialEntityVars = {
  id: number;
  name: string;
  logo: string;
  category_id: number | null;
  category_name: string | null;
  company_id: number;
  address: string | null;
  created_at: ServerDate;
  crm_id: number | null;
  google_coordinates: string | null;
  google_location: string | null;
  yandex_link: string | null;
  iiko_id: string | null;
  is_active: 1 | 0;
};

export type FilialEntity = {
  id: string;
  name: string;
  logo: string;
  categoryId: string;
  categoryName: string;
  companyId: string;
  address: string;
  createdAt: ClientDate;
  crmId: string;
  googleCoords: string;
  googleLocation: string;
  yandexLink: string;
  iikoId: string;
  active: boolean;
};
