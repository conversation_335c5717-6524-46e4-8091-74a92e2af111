import { isOrderTrigger } from "../../../data/triggers";
import { PollLang } from "./poll-lang";
import { PollPage } from "./poll-page";
import { Poll, PollVars } from "./types";
import { LANGS_ORDER } from "@/constants/langs";

export function PollModel(data: PollVars): Poll {
  const {
    id,
    foquzPollLangs = [],
    displaySetting,
    displayPages,
    is_auto,
    is_tmp,
    is_published,
    name,
    trigger,
    point_system,
  } = data;

  const langs = foquzPollLangs.map((lang) => PollLang(lang));
  langs.sort((a, b) => {
    const aIndex = LANGS_ORDER.indexOf(a.id);
    const bIndex = LANGS_ORDER.indexOf(b.id);
    return aIndex - bIndex;
  });
  const defaultLang = langs.find((lang) => lang.isDefaultLang);

  const isAuto = !!is_auto;

  const hasOrder = isAuto && isOrderTrigger(trigger);

  return {
    id: `${id}`,
    name,

    isAuto,
    isPublished: !!is_published,

    hasOrder,
    withPoints: !!point_system,

    langs,
    defaultLang,

    pages: (displayPages || []).map((page) => PollPage(page)),
  };
}
