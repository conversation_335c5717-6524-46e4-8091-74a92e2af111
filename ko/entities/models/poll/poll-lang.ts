import { LANGS_DATA } from "@/constants/langs";
import { LangSettings, LangSettingsVars } from "./types";

export function PollLang(data: LangSettingsVars): LangSettings {
  const {
    id,
    poll_lang_id,
    default: isDefault,
    back_text,
    next_text,
    finish_text,
    unrequired_text,
  } = data;

  const langData = LANGS_DATA[poll_lang_id];

  return {
    id: poll_lang_id,
    pollLangId: id,
    isDefaultLang: !!isDefault,
    langName: langData.name,
    langShortName: langData.shortCode,
    backButtonText: back_text,
    forwardButtonText: next_text,
    finishButtonText: finish_text,
    optionalText: unrequired_text,
  };
}
