import { PollTriggers } from "@/constants/poll/triggers";

export type LangSettingsVars = {
  poll_lang_id: number;
  id: number;
  default: number;
  back_text: string;
  next_text: string;
  finish_text: string;
  unrequired_text: string;
};

export type LangSettings = {
  id: number;
  pollLangId: number;
  isDefaultLang: boolean;
  backButtonText: string;
  forwardButtonText: string;
  finishButtonText: string;
  optionalText: string;
  langName: string;
  langShortName: string;
};

export type QuestionsPageVars = {
  id: number;
  name: string;
  order: number;
  questions: Array<{ id: string }>;
  random_order: number;
};

export type QuestionsPage = {
  id: string;
  name: string;
  position: number;
  questions: Array<{ id: string }>;
  randomOrder: boolean;
};

export type PollVars = {
  id: number;
  foquzPollLangs: Array<LangSettingsVars>;
  displaySetting: {};
  displayPages: Array<QuestionsPageVars>;
  is_auto: number;
  is_tmp: number;
  is_published: number;
  name: string;
  trigger: PollTriggers;
  point_system: number;
};

export type Poll = {
  id: string;
  name: string;

  isAuto: boolean;
  isPublished: boolean;

  hasOrder: boolean;
  withPoints: boolean;

  langs: Array<LangSettings>;
  defaultLang: LangSettings;

  pages: Array<QuestionsPage>;
};
