const ORDER_TYPES = {
  1: 'Доставка',
  2: 'Самовывоз',
  3: 'Зал'
};

const ORDER_SOURCES = {
  1: 'Сайт',
  2: 'Приложение',
  3: 'Телефон'
};

export class ContactPointData {
  constructor(data) {
    this.id = data.id;
    this.name = ko.observable(data.name);
    this.alias = ko.observable(data.alias);
    this.description = ko.observable(data.description);

    this.createdAt = '12.08.2021';

    this.polls = (data.surveys || []);
    this.conditions = data.conditions.map((c) => {
      let orderTypeName = c.orderType && ORDER_TYPES[c.orderType];
      let orderSourceName = c.orderSource && ORDER_SOURCES[c.orderSource];

      let title = [
        orderTypeName && `тип заказа «${orderTypeName}»`,
        orderSourceName && `способ оформления «${orderSourceName}»`
      ]
        .filter(Boolean)
        .join(' и ');

      if (title) {
        title = title[0].toUpperCase() + title.slice(1);
      }

      return {
        ...c,
        orderTypeName,
        orderSourceName,
        title
      };
    });
    this.businessTypes = data.businessTypes;

    this.folder = ko.observable(null);

    this.$displayName = ko.computed(() => {
      let name = this.name().trim();
      if (name) return name;
      return this.alias();
    });
  }
}
