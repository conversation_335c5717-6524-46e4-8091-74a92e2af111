import { dateToString } from '@/utils/date/date-to-string';

export class ContactPoint {
  constructor(data) {
    this.id = data.id;

    this.isFolder = data.is_folder;
    let createdAt = new Date(data.created_at);
    this.createdAt = dateToString(createdAt, 'DD.MM.YYYY');
    this.name = ko.observable(data.name);
    this.folder = ko.observable(data.folder);

    this.folders = ko.computed(() => {
      let folder = this.folder();
      if (!folder) return [];

      let currentFolder = folder.id;
      let folders = [];

      while (folder) {
        folders.push({
          id: folder.id,
          name: folder.name,
          current: folder.id === currentFolder.id
        });
        folder = folder.folder;
      }

      return folders.reverse();
    });

    // cp
    this.isSystem = data.is_system;
    this.polls = data.surveys;
    this.conditions = data.typeRelations;
    this.businessTypes = data.businessTypes;

    // folder
    this.childrenCount = ko.observable(parseInt(data.countItemsFirstLevel));
    this.pointsCount = ko.observable(parseInt(data.countItems));
  }

  update(data) {
    this.name(data.name);
    this.folder(data.folder);
    this.childrenCount(parseInt(data.countItemsFirstLevel));
    this.pointsCount(parseInt(data.countItems));
  }
}
