import { serverDateStringToClientDateTimeString } from "@/utils/date/formats";
import { ClientModel } from "../client";
import { ContactModel } from "../contact";
import { ProcessingModel } from "../processing/poll";
import { FeedbackReview, FeedbackReviewVars } from "./types";

export function FeedbackReviewModel(data: FeedbackReviewVars): FeedbackReview {
  const {
    id,
    created_at,

    contact_id,
    processing,

    client,
    formattedPhone,
    text,
    decodedFiles,

    clientPhone,
    clientName,
    ordersCount,
    clientEmail,
    clientGender,
    clientBirthday,
    clientFilials,
    clientUpdated,
    clientAdded,
    clientTags,
    clientComputedFields,
    clientAdditionalFields,
  } = data;

  return {
    id: `${id}`,
    createdAt: serverDateStringToClientDateTimeString(created_at),

    contactId: `${contact_id}`,
    processing: ProcessingModel(processing),

    client: ClientModel({
      phone: clientPhone,
      name: client<PERSON><PERSON>,
      ordersCount,
      email: clientEmail,
      gender: clientGender,
      birthday: clientBirthday,
      filials: clientFilials,
      addedAt: clientAdded,
      updatedAt: clientUpdated,
      tags: clientTags,
      computedFields: clientComputedFields,
      additionalFields: clientAdditionalFields,
      apiFields: null,
    }),

    message: {
      name: client || "",
      phone: formattedPhone || "",
      text: text || "",
      files: (decodedFiles || []).map((file) => `/${file}`),
    },
  };
}
