import { ClientDateTime, ServerDate, ServerDateTime } from "@/types";
import { FeedbackThemeVars } from "@/entities/models/feedback-theme/types";
import { Contact, ContactVars } from "@/entities/models/contact/types";
import { Processing, ProcessingVars } from "@/entities/models/processing/types";
import {
  Client,
  ClientComputedFieldsVars,
} from "@/entities/models/client/types";
import { FilialEntityVars } from "@/entities/models/filial/types";
import { ClientTagVars } from "@/entities/models/client-tag/types";
import { Genders } from "@/constants/genders";

interface FeedbackReviewClientVars {
  ordersCount: string;
  clientAdded: ServerDate;
  clientAdditionalFields: {
    [key: string]: string;
  };
  clientBirthday: ServerDate | null;
  clientComputedFields: ClientComputedFieldsVars | null;
  clientCustomerId: number | null;
  clientEmail: string;
  clientFilials: Array<FilialEntityVars>;
  clientGender: Genders;
  clientName: string;
  clientPhone: string;
  clientTags: Array<ClientTagVars>;
  clientUpdated: ServerDate;
}

interface FeedbackReviewMessageVars {
  name: string;
  surname: string;
  client: string;
  phone: string;
  formattedPhone: string;
  email: string;
  text: string;
  files: string | null;
  decodedFiles: Array<string> | null;
}

export interface FeedbackReviewMessage {
  name: string;
  phone: string;
  text: string;
  files: Array<string>;
}

export type FeedbackReviewVars = FeedbackReviewMessageVars &
  FeedbackReviewClientVars & {
    id: number;
    company_id: number;
    created_at: ServerDateTime;

    contact_id: number;

    ip_address: string;
    os: string;
    useragent: string;

    theme: number;
    feedbackTheme: FeedbackThemeVars;

    filial_id: number;
    filialName: string;

    processing: ProcessingVars;
  };

export type FeedbackReview = {
  id: string;
  createdAt: ClientDateTime;

  contactId: string;
  client: Client;
  processing: Processing;

  message: FeedbackReviewMessage;
};
