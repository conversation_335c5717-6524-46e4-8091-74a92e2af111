import { FileData, FileDataVars } from "./types";
import { serverDateStringToClientDateTimeString } from "@/utils/date/formats";

export function FileDataModel(data: FileDataVars): FileData {
  const { id, file_path, created_at, poster_url } = data;

  let url = file_path;

  return {
    id: `${id}`,
    url,
    createdAt: serverDateStringToClientDateTimeString(created_at),
    poster: poster_url || url,
  };
}
