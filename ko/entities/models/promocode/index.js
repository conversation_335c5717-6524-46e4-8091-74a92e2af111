/**
 * Промокод на дополнительные ответы для компании
 */

import { formatServerDateToClient } from 'Utils/date/format';

export class Promocode {
  constructor(data) {
    this.promocode = data.promo_code;
    this.usedAt = formatServerDateToClient(data.created_at);

    let user = data.createdBy || {};
    this.userName = user.name;
    this.userAvatar = user.avatar;
    this.bonuses = data.bonuses;

    this.coupon = data.coupon;

    let company = data.company || {};
    this.companyName = company.name;
    this.companyId = company.id;
  }
}
