import { ClientVars, Client } from "./types";
import { serverDateStringToClientDateString } from "@/utils/date/formats";
import { FilialDataModel } from "@/entities/models/filial";
import { ClientTagModel } from "@/entities/models/client-tag/index";

export function ClientModel(data: ClientVars): Client {
  const {
    phone,
    name,
    ordersCount,
    email,
    gender,
    birthday,
    filials,
    addedAt,
    updatedAt,
    tags,
    computedFields,
    additionalFields,
    apiFields,
  } = data;

  

  let ltv = null;
  let lastOrderDate = "";

  if (computedFields) {
    ltv = computedFields.ltv_amount;
    lastOrderDate = serverDateStringToClientDateString(
      computedFields.last_order_date
    );
  }

  return {
    name,
    phone,
    ordersCount: Number(ordersCount),
    
    systemFields: {
      email,
      gender,
      birthday: serverDateStringToClientDateString(birthday),
      filials: (filials || []).map(FilialDataModel),
      addedAt: serverDateStringToClientDateString(addedAt),
      updatedAt: serverDateStringToClientDateString(updatedAt),
      tags: tags.map(ClientTagModel),
      ltv,
      lastOrderDate,
    },

    additionalFields: Object.entries(additionalFields || {}).map(
      ([id, value]) => ({ id, value })
    ).filter(field => !!field.value),
    
    apiFields: Object.entries(apiFields || {}).map(([id, value]) => ({
      id,
      value,
    })),
  };
}
