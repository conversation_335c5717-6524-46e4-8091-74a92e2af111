import { ClientDate } from "@/types";
import { Genders } from "@/constants/genders";
import { FilialEntity, FilialEntityVars } from "../filial/types";
import { ClientTag, ClientTagVars } from "../client-tag/types";
import { ServerDate } from "../../../types";

export type ClientComputedFieldsVars = {
  ltv_amount: number;
  last_order_date: ServerDate;
  [key: string]: any
}

export type ClientSystemFields = {
  email: string;
  gender: Genders | null;
  birthday: ClientDate;
  filials: Array<FilialEntity>;
  lastOrderDate: string;
  ltv: number | null;
  addedAt: ClientDate;
  updatedAt: ClientDate;
  tags: Array<ClientTag>;
};

export type ClientAdditionalFields = Array<{
  id: string;
  value: string;
}>;

export type ClientApiFields = Array<{
  id: string;
  value: string;
}>;

export type ClientVars = {
  phone: string;
  name: string;
  ordersCount?: string;
  email: string;
  gender: Genders | null;
  birthday: ServerDate;
  filials: Array<FilialEntityVars>;
  addedAt: ServerDate;
  updatedAt: ServerDate;
  tags: Array<ClientTagVars>;
  computedFields: ClientComputedFieldsVars;
  additionalFields: {
    [key: string]: string;
  };
  apiFields: {
    [key: string]: string;
  };
};

export type Client = {
  name: string;
  ordersCount: number;
  phone: string;

  systemFields: ClientSystemFields;
  additionalFields: ClientAdditionalFields;
  apiFields: ClientApiFields;
};
