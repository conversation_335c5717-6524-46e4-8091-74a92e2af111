import { ClientDate, ServerDateTime } from "@/types";

export enum ChannelStatuses {
  responded = "2",
}

export type ChannelVars = {
  id: number;
  contact_id: null;
  answer_id: number;
  channel_id: null;
  repeat_id: null;
  status: string;
  key: string;
  sended: ServerDateTime;
  channel_name: string;
};

export type Channel = {
  type: string;
  sended: ClientDate;
  name: string;
  status: string;
};
