import { ChannelVars, Channel } from "./types";
import { serverDateStringToClientDateString } from '../../../utils/date/formats';

function getChannelType(channelName: string) {
  switch (channelName) {
    case 'Email':
      return 'email';
    case 'SMS':
      return 'sms';
    case 'Telegram':
      return 'telegram';
    case 'Viber':
      return 'viber';
    case 'Push':
      return 'push';
    case 'Ссылка':
      return 'link';
    default:
      return null;
  }
}

export function ChannelModel(data: ChannelVars): Channel {
  const {
    id,
    contact_id,
    answer_id,
    channel_id,
    repeat_id,
    status,
    key,
    sended,
    channel_name,
  } = data;

  return {
    type: getChannelType(channel_name),
    name: channel_name,
    sended: serverDateStringToClientDateString(sended),
    status
  };
}
