/**
 * Дата + время
 */

import moment from 'moment';
import { dateValidator } from '@/utils/validation/date';
import { timeValidator } from '@/utils/validation/time';
import { FModel } from '../f-model';
import { Translator } from '@/utils/translate';

export class FmTimePoint extends FModel {
  constructor() {
    super();

    this.translator = Translator('validation')

    this.time = ko.observable('').extend({
      validation: [
        {
          validator: timeValidator(),
          message: () => this.translator.t('Неверный формат')()
        }
      ]
    });
    this.date = ko.observable('').extend({
      required: {
        message:  () => this.translator.t('Обязательное поле')(),
        onlyIf: () => {
          return this.time() && this.time() !== '00:00';
        }
      },
      validation: [
        {
          validator: dateValidator(),
          message: () => this.translator.t('Неверный формат')()
        }
      ]
    });

    this.listen(this.time, this.date);
    this.createValidator(this.time, this.date);
  }

  update(data) {
    if (data) {
      let date = moment(data);
      this.date(date.format('DD.MM.YYYY'));
      this.time(date.format('HH:mm'));
    } else {
      this.date('');
      this.time('');
    }
  }

  hasValue() {
    return this.date();
  }

  getDate() {
    return moment(this.date(), 'DD.MM.YYYY');
  }

  getTime() {
    return moment(this.time(), 'HH:mm');
  }

  getValue() {
    if (!this.date()) return '';
    let date = moment(`${this.date()} ${this.time()}`, 'DD.MM.YYYY HH:mm');
    return date.toString();
  }
}
