import { DateMoment, ServerDateTime } from '@/types';

export type UserVars = {
    id: number;
    username: string;
    auth_key: string;
    password_hash: string;
    confirmation_token: string;
    status: number;
    superadmin: 0 | 1;
    created_at: DateMoment;
    updated_at: DateMoment;
    registration_ip: string;
    bind_to_ip: string;
    email: string;
    email_confirmed: 0 | 1;
    name: string;
    avatar: string;
    access_token: string;
    quick_help_showed: 0 | 1;
    phone: string;
    mobile_quick_help_showed: 0 | 1;
    language_id: number;
    zapier_access: ServerDateTime;
    clients_section: 0 | 1;
    photo: string;
}
export type User = {
    id: string;
    name: string;
    photo: string;
}