import { FModel } from '@/entities/f-model';

import { FmQuestion } from '@/entities/models/question/fm-question';
import { CHANGE_UI } from '@/entities/models/question/events';

export class FmQuestionForm extends FModel {
  constructor(config = {}) {
    super();

    this.errorMatcher = config.errorMatcher;
    this.successMatcher = config.successMatcher;

    this.question = new FmQuestion({
      api: {
        imageFile: this.getDownloadImageFromFileAction,
        videoFile: this.getDownloadVideoFromFileAction,
        videoYoutube: this.getDownloadVideoFromYoutubeAction,
        link: this.getDownloadMediaByLinkAction,
        label: this.getChangeMediaLabelAction
      },
      ...config
    });
    this.question.on(CHANGE_UI, (data) => {
      this.emit(CHANGE_UI, data);
    });

    this.listen(this.question);
    this.createValidator(this.question);
  }

  /** SETTINGS */

  get isAuto() {
    return false;
  }

  /** ACTIONS */

  getDownloadImageFromFileAction() {
    return '';
  }

  getDownloadVideoFromFileAction() {
    return '';
  }

  getDownloadVideoFromYoutubeAction() {
    return '';
  }

  getDownloadMediaByLinkAction() {
    return '';
  }

  getChangeMediaLabelAction() {
    return '';
  }

  /** API */

  setQuestion(data) {}

  getData() {}
}
