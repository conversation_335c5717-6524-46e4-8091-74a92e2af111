import { FmQuestionForm } from '../form';
import { ApiUrl } from '@/utils/url/api-url';
import { convertForClient } from './converters/convert-for-client';


export class ContactPointForm extends FmQuestionForm {
  constructor(config) {
    super(config);
    this.sourceQuestionId = ko.observable(config.sourceQuestionId);

    this.isSystem = ko.observable(false);
  }

  get isAuto() {
    return true;
  }

  getDownloadImageFromFileAction(id) {
    return ApiUrl(
      'questions/image-upload',
      id ? { id: this.sourceQuestionId() } : undefined
    );
  }

  getDownloadVideoFromFileAction(id) {
    return ApiUrl(
      'questions/video-upload',
      id ? { id: this.sourceQuestionId() } : undefined
    );
  }

  getDownloadVideoFromYoutubeAction(id) {
    return ApiUrl(
      'questions/upload-youtube',
      id ? { id: this.sourceQuestionId() } : undefined
    );
  }

  getDownloadMediaByLinkAction(id) {
    return ApiUrl(
      'questions/upload-by-link',
      id ? { id: this.sourceQuestionId() } : undefined
    );
  }

  getChangeMediaLabelAction(mediaId) {
    return ApiUrl('questions/change-label', { id: mediaId });
  }

  /** API */

  setQuestion(data) {
    let questionData = convertForClient(data);
    this.question.update(questionData);

    this.isSystem(!!data.isSystem)
  }
}
