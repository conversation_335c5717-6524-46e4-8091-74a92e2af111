import { BirthdayFilterTypes } from "@/entities/structures/birthday-filter/types";

import { periodValidator } from "@/utils/validation/period";
import { dateValidator } from "@/utils/validation/date";
import { ClientDate } from "@/types";
import { PeriodPickerValue } from "@/entities/structures/period-picker-value";
import { CompoundValue } from "@/entities/structures/compound-value";
import { ERROR_TEXT } from "../../../constants/validators/errors";

const { observable } = ko;

export class BirthdayFilterModel extends CompoundValue {
  type: KnockoutObservable<BirthdayFilterTypes>;
  date: KnockoutObservable<ClientDate>;
  period: KnockoutObservable<PeriodPickerValue>;
  age: KnockoutObservable<string>;
  month: KnockoutObservable<number>;
  day: KnockoutObservable<string>;

  constructor() {
    super();

    this.type = observable<BirthdayFilterTypes>(null).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });
    this.date = observable<ClientDate>("").extend({
      required: {
        message: ERROR_TEXT.required,
        onlyIf: () =>
          [BirthdayFilterTypes.Date, BirthdayFilterTypes.YearsOnDate].includes(
            this.type()
          ),
      },
      validation: {
        validator: dateValidator(),
        message: ERROR_TEXT.format,
      },
    });
    this.period = observable<PeriodPickerValue | null>(null).extend({
      required: {
        message: ERROR_TEXT.required,
        onlyIf: () => this.type() === BirthdayFilterTypes.Period,
      },
      validation: {
        validator: periodValidator(),
        message: ERROR_TEXT.format,
      },
    });
    this.age = observable<string>("").extend({
      required: {
        message: ERROR_TEXT.required,
        onlyIf: () => this.type() === BirthdayFilterTypes.YearsOnDate,
      },
    });
    this.month = observable<number>(1).extend({
      required: {
        message: ERROR_TEXT.required,
        onlyIf: () =>
          [BirthdayFilterTypes.Month, BirthdayFilterTypes.DayMonth].includes(
            this.type()
          ),
      },
    });
    this.day = observable<string>("").extend({
      required: {
        onlyIf: () => this.type() == BirthdayFilterTypes.DayMonth,
        message: ERROR_TEXT.required,
      },
      validation: {
        validator: (v) => {
          if (!v) return true;
          const num = parseInt(v);
          return num <= 31;
        },
        message: ERROR_TEXT.format,
      },
    });

    const sb = this.type.subscribe(() => {
      this.date("");
      this.period(null);
      this.age("");
      this.month(1);
      this.day("");
    });

    this._addDisposeCb(() => sb.dispose());

    this._subscribeForChanges(
      this.type,
      this.date,
      this.period,
      this.age,
      this.month,
      this.day
    );
  }

  isEmpty(): boolean {
    switch (this.type()) {
      case BirthdayFilterTypes.Date:
        return !this.date();
      case BirthdayFilterTypes.Period:
        return !this.period();
      case BirthdayFilterTypes.YearsOnDate:
        return !this.date() || !this.age();
      case BirthdayFilterTypes.Month:
        return !this.month();
      case BirthdayFilterTypes.DayMonth:
        return !this.day() || !this.month();
      default:
        return true;
    }
  }

  isValid() {
    return [
      this.type,
      this.date,
      this.period,
      this.age,
      this.month,
      this.day,
    ].every((field) => field.isValid());
  }

  setValue(value) {
    this.type(value.type);
    this.date(value.date || "");
    this.period(value.period || null);
    this.age(value.age || "");
    this.month(value.month || 1);
    this.day(value.day || "");
  }

  getValue() {
    const value = {
      type: this.type(),
      date: null,
      period: null,
      age: null,
      month: null,
      day: null,
    };

    switch (this.type()) {
      case BirthdayFilterTypes.Date:
        value.date = this.date();
        break;
      case BirthdayFilterTypes.Period:
        value.period = this.period();
        break;
      case BirthdayFilterTypes.YearsOnDate:
        value.date = this.date();
        value.age = this.age();
        break;
      case BirthdayFilterTypes.Month:
        value.month = this.month();
        break;
      case BirthdayFilterTypes.DayMonth:
        value.day = this.day();
        value.month = this.month();
        break;
    }

    return value;
  }
}
