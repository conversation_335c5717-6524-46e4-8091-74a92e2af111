import { EventEmitter, ee } from "@/utils/event-emitter";

export const COMPOUND_VALUE_CHANGE_EVENT = "compoundValue/change";

export class CompoundValue {
  _emitter: EventEmitter;
  _disposeCbs: Array<() => void> = [];

  constructor() {
    this._emitter = ee();
  }

  _emitChangeEvent() {
    this._emitter.emit(COMPOUND_VALUE_CHANGE_EVENT);
  }

  _subscribeForChanges(
    ...observables: Array<{
      subscribe: (cb: () => void) => KnockoutSubscription;
    }>
  ) {
    observables.forEach((observable) => {
      if (typeof observable.subscribe === "function") {
        const sb = observable.subscribe(() => {
          this._emitChangeEvent();
        });
        if (sb && sb.dispose) {
          this._addDisposeCb(() => sb.dispose());
        }
      }
    });
  }

  _addDisposeCb(cb: () => void) {
    this._disposeCbs.push(cb);
  }

  subscribe(cb: () => void): KnockoutSubscription {
    this._emitter.on(COMPOUND_VALUE_CHANGE_EVENT, cb);

    return {
      dispose: () => this._emitter.off(COMPOUND_VALUE_CHANGE_EVENT, cb),
    };
  }

  dispose() {
    this._disposeCbs.forEach((cb) => {
      if (typeof cb === "function") cb();
    });
    this._emitter.off();
  }

  getValue() {
    return null;
  }

  setValue(value) {
  }

  isValid() {
    return true;
  }

  isEmpty() {
    return false;
  }
}
