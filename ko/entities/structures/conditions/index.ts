import { ee } from "@/utils/event-emitter";
import { ContactCondition, ContactConditions } from "./conditions.types";
import { FILTERS_CHANGE_EVENT } from "./events";
import { ContactConditionModel } from "./condition";

const { observable, observableArray } = ko;

export function ContactConditionsModel(): ContactConditions {
  const emitter = ee();

  const list = observableArray<ContactCondition>([]);

  const isValid = observable(true);
  const isEmpty = observable(true);

  const _onInnerChange = () => {
    isValid(!list().some((condition) => !condition.isValid()));
    isEmpty(!list().some((condition) => !condition.isEmpty()));
    emitter.emit(FILTERS_CHANGE_EVENT);
  };

  const getValue = () => {
    console.log('getValue', list());
    return list()
      .filter((condition) => !condition.isEmpty() && condition.isValid())
      .map((condition) => condition.getValue());
  };

  return {
    list,
    isEmpty,
    isValid,

    getValue,
    setValue: (initialSettings) => {
      if (!Array.isArray(initialSettings)) {
        return;
      }
      initialSettings.forEach((setting) => {
        const condition = ContactConditionModel();
        condition.onChange(() => _onInnerChange());
        condition.setValue(setting);
        list.push(condition);
      });
      _onInnerChange();
    },
    onChange: (cb: () => void) => {
      emitter.on(FILTERS_CHANGE_EVENT, cb);
    },
    dispose: () => {
      list().forEach((condition) => condition.dispose());
      emitter.off();
    },

    addCondition() {
      const condition = ContactConditionModel();
      condition.onChange(() => _onInnerChange());
      list.push(condition);
      _onInnerChange();
    },

    removeCondition(condition: ContactCondition) {
      list.remove(condition);
      _onInnerChange();
    },
  };
}
