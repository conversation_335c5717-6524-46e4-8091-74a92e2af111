import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class OrderTimeFilter extends CompoundValue {
  times: KnockoutObservableArray<string>;

  constructor() {
    super();

    this.times = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });

    this._subscribeForChanges(this.times);
  }

  isEmpty(): boolean {
    return this.times().length === 0;
  }

  isValid() {
    return this.times.isValid();
  }

  setValue(value) {
    this.times(value)
  }

  getValue() {
    return this.times();
  }
}
