import { CompoundValue } from "@/entities/structures/compound-value";
import { NumberRangeModel } from "@/entities/structures/range/number-range";
import { Range } from "@/entities/structures/range/types";

export class RevenueForMonthFilter extends CompoundValue {
  from: KnockoutObservable<number>;
  to: KnockoutObservable<number>;
  range: Range;

  constructor() {
    super();

    this.from = ko.observable(0);
    this.to = ko.observable(0);

    this.range = NumberRangeModel(this.from, this.to, {
      required: true
    });

    this._subscribeForChanges(this.from, this.to);
  }

  isValid() {
    return this.range.isValid();
  }

  setValue(value) {
    this.from(value[0]);
    this.to(value[1]);
  }

  getValue() {
    return [this.from(), this.to()];
  }
}
