import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class ContactDataFilter extends CompoundValue {
  filled: KnockoutObservable<boolean>;
  fields: KnockoutObservableArray<string>;

  constructor() {
    super();
    this.fields = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });
    this.filled = ko.observable(true);

    this._subscribeForChanges(this.fields, this.filled);
  }

  isEmpty() {
    return this.fields().length === 0;
  }

  isValid() {
    return this.fields.isValid();
  }

  setValue(value) {
    this.fields(value.fields);
    this.filled(value.filled);
  }

  getValue() {
    return {
      fields: this.fields(),
      filled: this.filled(),
    };
  }
}
