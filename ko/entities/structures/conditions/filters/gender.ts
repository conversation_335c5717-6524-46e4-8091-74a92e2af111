import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class GenderFilter extends CompoundValue {
  genders: KnockoutObservableArray<number>;

  constructor() {
    super();
    this.genders = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });
    // this.genders.subscribe((v) => {
    //   console.log('gender subscribe v', v);
    // })
    this._subscribeForChanges(this.genders);
  }

  isEmpty() {
    return this.genders().length === 0;
  }

  isValid() {
    return this.genders.isValid();
  }

  setValue(value) {
    return this.genders(value);
  }

  getValue() {
    return this.genders();
  }
}
