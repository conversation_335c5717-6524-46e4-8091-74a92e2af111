import { CompoundValue } from "@/entities/structures/compound-value";
import { DateWithCriteaFilterModel } from "@/entities/structures/date-with-criteria-filter";

export class LastOrderDateFilter extends CompoundValue {
  orderDate: DateWithCriteaFilterModel;

  constructor() {
    super();

    this.orderDate = new DateWithCriteaFilterModel();
    this._subscribeForChanges(this.orderDate);
    this._addDisposeCb(() => this.orderDate.dispose());
  }

  isEmpty(): boolean {
    return this.orderDate.isEmpty();
  }

  isValid() {
    return this.orderDate.isValid();
  }

  setValue(value) {
    this.orderDate.setValue(value)
  }

  getValue() {
    return this.orderDate.getValue();
  }
}
