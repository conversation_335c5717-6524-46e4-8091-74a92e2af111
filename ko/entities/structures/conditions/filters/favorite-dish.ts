import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class FavoriteDishFilter extends CompoundValue {
  dishes: KnockoutObservableArray<string>;
  minPrice: KnockoutObservable<string>;

  constructor() {
    super();

    this.minPrice = ko.observable("");

    this.dishes = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
        onlyIf: () => !this.minPrice(),
      },
    });

    this._subscribeForChanges(this.dishes, this.minPrice);
  }

  isEmpty(): boolean {
    if (this.minPrice()) return false;
    return this.dishes().length === 0;
  }

  isValid(): boolean {
    return this.dishes.isValid();
  }

  setValue(value) {
    this.dishes(value.dishes);
    this.minPrice(value.minPrice);
  }

  getValue() {
    return {
      dishes: this.dishes(),
      minPrice: this.minPrice(),
    };
  }
}
