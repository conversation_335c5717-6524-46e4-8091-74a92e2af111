import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class OrderDaysFilter extends CompoundValue {
  days: KnockoutObservableArray<string>;

  constructor() {
    super();

    this.days = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });

    this._subscribeForChanges(this.days);
  }

  isEmpty(): boolean {
    return this.days().length === 0;
  }

  isValid() {
    return this.days.isValid();
  }

  setValue(value) {
    this.days(value)
  }

  getValue() {
    return this.days();
  }
}
