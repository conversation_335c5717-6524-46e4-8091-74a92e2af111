import { OrderHistoryTypes } from "@/constants/order/order-history";
import { OrderSourceTypes } from "@/constants/order/source-types";
import { CompoundValue } from "@/entities/structures/compound-value";

export class SourceTypeFilter extends CompoundValue {
  sourceType: KnockoutObservable<OrderSourceTypes>;
  history: KnockoutObservable<OrderHistoryTypes>;

  constructor() {
    super();

    this.history = ko.observable(OrderHistoryTypes.AtLeastOnce);
    this.sourceType = ko.observable(OrderSourceTypes.Site);

    this._subscribeForChanges(this.history, this.sourceType);
  }

  setValue(value) {
    this.history(value.history);
    this.sourceType(value.sourceType);
  }

  getValue() {
    return {
      history: this.history(),
      sourceType: this.sourceType(),
    };
  }
}
