import { CompoundValue } from "@/entities/structures/compound-value";

export class PollsParticipationFilter extends CompoundValue {
  from: KnockoutObservable<number>;
  to: KnockoutObservable<number>;

  constructor() {
    super();

    this.from = ko.observable(0);
    this.to = ko.observable(100);

    this._subscribeForChanges(this.from, this.to);
  }

  setValue(value) {
    this.from(value[0]);
    this.to(value[1]);
  }

  getValue() {
    return [this.from(), this.to()];
  }
}
