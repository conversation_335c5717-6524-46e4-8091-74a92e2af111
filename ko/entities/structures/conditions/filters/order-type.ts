import { OrderHistoryTypes } from "@/constants/order/order-history";
import { OrderTypes } from "@/constants/order/order-types";
import { CompoundValue } from "@/entities/structures/compound-value";

export class OrderTypeFilter extends CompoundValue {
  orderType: KnockoutObservable<OrderTypes>;
  history: KnockoutObservable<OrderHistoryTypes>;

  constructor() {
    super();

    this.history = ko.observable(OrderHistoryTypes.AtLeastOnce);
    this.orderType = ko.observable(OrderTypes.Delivery);

    this._subscribeForChanges(this.orderType, this.history);
  }

  setValue(value) {
    this.orderType(value.orderType);
    this.history(value.history);
  }

  getValue() {
    return {
      orderType: this.orderType(),
      history: this.history(),
    };
  }
}
