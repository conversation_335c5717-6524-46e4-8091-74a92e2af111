import { PushTokenStates } from "@/constants/client/push-token-state";
import { CompoundValue } from "@/entities/structures/compound-value";

export class PushTokenFilter extends CompoundValue {
  state: KnockoutObservable<PushTokenStates>;

  constructor() {
    super();

    this.state = ko.observable(PushTokenStates.Active);

    this._subscribeForChanges(this.state);
  }

  isEmpty(): boolean {
    return !this.state();
  }

  isValid() {
    return true;
  }

  setValue(value) {
    this.state(value);
  }

  getValue() {
    return this.state();
  }
}
