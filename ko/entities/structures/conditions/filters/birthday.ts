import { BirthdayFilterModel } from "../../birthday-filter";
import { CompoundValue } from "@/entities/structures/compound-value";

export class BirthdayFilter extends CompoundValue {
  value: BirthdayFilterModel;

  constructor() {
    super();
    this.value = new BirthdayFilterModel();

    this._subscribeForChanges(this.value);
    this._addDisposeCb(() => this.value.dispose());
  }

  isEmpty() {
    return this.value.isEmpty();
  }

  isValid() {
    return this.value.isValid();
  }

  setValue(value) {
    this.value.setValue(value);
  }

  getValue() {
    return this.value.getValue();
  }
}
