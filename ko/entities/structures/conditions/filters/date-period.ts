import { DatePeriodFilterModel } from "@/entities/structures/date-period-filter";
import { CompoundValue } from "@/entities/structures/compound-value";

export class DatePeriodFilter extends CompoundValue {
  value: DatePeriodFilterModel;

  constructor() {
    super();
    this.value = new DatePeriodFilterModel();

    this._subscribeForChanges(this.value);
    this._addDisposeCb(() => this.value.dispose());
  }

  isEmpty() {
    return !this.value.type();
  }

  isValid() {
    return this.value.isValid();
  }

  setValue(value) {
    this.value.setValue(value);
  }

  getValue() {
    return this.value.getValue();
  }
}
