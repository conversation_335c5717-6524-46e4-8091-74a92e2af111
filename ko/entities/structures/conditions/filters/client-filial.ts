import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class ClientFilialFilter extends CompoundValue {
  filials: KnockoutObservableArray<number | string>;
  except: KnockoutObservable<boolean>;

  constructor() {
    super();
    this.filials = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
      }
    });
    this.except = ko.observable(false);

    const cb = this.filials.subscribe((v) => {
      if (!v.length) this.except(false);
    });

    this._subscribeForChanges(this.filials, this.except);
    this._addDisposeCb(() => cb.dispose());
  }

  isEmpty() {
    return this.filials().length === 0;
  }

  isValid() {
    return this.filials.isValid();
  }

  setValue(value) {
    this.filials(value.filials)
    this.except(value.except)
  }

  getValue() {
    return {
      filials: this.filials(),
      except: this.except(),
    };
  }
}
