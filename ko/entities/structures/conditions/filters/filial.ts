import { OrderHistoryTypes } from "@/constants/order/order-history";
import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class FilialFilter extends CompoundValue {
  filials: KnockoutObservableArray<string>;
  except: KnockoutObservable<boolean>;
  history: KnockoutObservable<OrderHistoryTypes>;

  constructor() {
    super();

    this.filials = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });
    this.except = ko.observable(false);
    this.history = ko.observable(OrderHistoryTypes.AtLeastOnce);

    this._subscribeForChanges(this.filials, this.except, this.history);

    this.filials.subscribe((v) => {
      if (!v.length) this.except(false);
    });
  }

  isEmpty(): boolean {
    return this.filials().length === 0;
  }

  isValid() {
    return this.filials.isValid();
  }

  setValue(value) {
    this.filials(value.filials);
    this.except(value.except);
    this.history(value.history);
  }

  getValue() {
    return {
      filials: this.filials(),
      except: this.except(),
      history: this.history(),
    };
  }
}
