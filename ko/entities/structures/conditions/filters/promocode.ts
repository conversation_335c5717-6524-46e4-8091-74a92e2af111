import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class PromocodeFilter extends CompoundValue {
  pools: KnockoutObservableArray<string>;
  used: KnockoutObservable<boolean>;

  constructor() {
    super();

    this.pools = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });
    this.used = ko.observable(true);

    this._subscribeForChanges(this.pools, this.used);
  }

  isEmpty(): boolean {
    return this.pools().length === 0;
  }

  isValid() {
    return this.pools.isValid();
  }

  setValue(value) {
    this.pools(value.pools);
    this.used(value.used);
  }

  getValue() {
    return {
      pools: this.pools(),
      used: this.used(),
    };
  }
}
