import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class TagsFilter extends CompoundValue {
  tags: KnockoutObservableArray<string | number>;

  constructor() {
    super();
    this.tags = ko.observableArray([]).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });

    this._subscribeForChanges(this.tags);
  }

  isEmpty() {
    return this.tags().length === 0;
  }

  isValid() {
    return this.tags.isValid();
  }

  setValue(value) {
    this.tags(value);
  }

  getValue() {
    return this.tags();
  }
}
