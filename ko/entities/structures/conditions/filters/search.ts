import { ERROR_TEXT } from "@/constants/validators/errors";
import { CompoundValue } from "@/entities/structures/compound-value";

export class SearchFilter extends CompoundValue {
  value: KnockoutObservable<string>;

  constructor() {
    super();
    this.value = ko.observable("").extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });

    this._subscribeForChanges(this.value);
  }

  isEmpty() {
    return this.value().trim().length === 0;
  }

  isValid() {
    return this.value.isValid();
  }

  setValue(value) {
    this.value(value);
  }

  getValue() {
    return this.value().trim();
  }
}
