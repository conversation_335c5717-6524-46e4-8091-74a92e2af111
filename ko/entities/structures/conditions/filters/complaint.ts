import { CompoundValue } from "@/entities/structures/compound-value";
import { DateWithCriteaFilterModel } from "@/entities/structures/date-with-criteria-filter";

export class ComplaintFilter extends CompoundValue {
  orderDate: DateWithCriteaFilterModel;
  complaint: KnockoutObservable<boolean>;

  constructor() {
    super();

    this.orderDate = new DateWithCriteaFilterModel();
    this.complaint = ko.observable(true);

    this._subscribeForChanges(this.orderDate, this.complaint);
    this._addDisposeCb(() => this.orderDate.dispose());
  }

  isValid(): boolean {
    return this.orderDate.isValid();
  }

  setValue(value) {
    if (value) {
      if (value.hasOwnProperty('complaint')) {
        this.complaint(value.complaint);
      }

      const orderDate = {
        criteria: value.criteria,
        date: value.date,
        period: value.period,
        from: value.from,
        to: value.to
      };

      this.orderDate.setValue(orderDate);
    }
  }

  getValue() {
    return {
      ...this.orderDate.getValue(),
      complaint: this.complaint(),
    };
  }
}
