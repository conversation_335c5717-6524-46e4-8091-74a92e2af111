import { SearchFilter } from "./filters/search";
import { GenderFilter } from "./filters/gender";
import { ContactFilterType } from "./contact-filter-types";
import { DatePeriodFilter } from "./filters/date-period";
import { BirthdayFilter } from "./filters/birthday";
import { ClientFilialFilter } from "./filters/client-filial";
import { ContactDataFilter } from "./filters/contact-data";
import { TagsFilter } from "./filters/tags";
import { LastOrderDateFilter } from "./filters/last-order-date";
import { ComplaintFilter } from "./filters/complaint";
import { AvgOrdersPerYearFilter } from "./filters/avg-orders-per-year";
import { AvgOrdersPerMonthFilter } from "./filters/avg-orders-per-month";
import { RevenueForYearFilter } from "./filters/revenue-for-year";
import { RevenueForMonthFilter } from "./filters/revenue-for-month";
import { AvgCheckFilter } from "./filters/avg-check";
import { OrderDaysFilter } from "./filters/order-days";
import { OrderTimeFilter } from "./filters/order-time";
import { FavoriteDishFilter } from "./filters/favorite-dish";
import { PollsParticipationFilter } from "./filters/polls-participation";
import { OrderTypeFilter } from "./filters/order-type";
import { SourceTypeFilter } from "./filters/source-type";
import { FilialFilter } from "./filters/filial";
import { UsedPromocodesPercentFilter } from "./filters/used-promocodes-percent";
import { PromocodeFilter } from "./filters/promocode";
import { PushTokenFilter } from "./filters/push-token";

const FILTERS = {
  [ContactFilterType.Search]: SearchFilter,
  [ContactFilterType.Gender]: GenderFilter,
  [ContactFilterType.CreatedAt]: DatePeriodFilter,
  [ContactFilterType.UpdatedAt]: DatePeriodFilter,
  [ContactFilterType.Birthday]: BirthdayFilter,
  [ContactFilterType.ClientFilial]: ClientFilialFilter,
  [ContactFilterType.ContactData]: ContactDataFilter,
  [ContactFilterType.TagsInclude]: TagsFilter,
  [ContactFilterType.TagsExclude]: TagsFilter,
  [ContactFilterType.PushToken]: SearchFilter,
  [ContactFilterType.LastOrderDate]: LastOrderDateFilter,
  [ContactFilterType.Complaint]: ComplaintFilter,
  [ContactFilterType.AvgOrdersNumberPerYear]: AvgOrdersPerYearFilter,
  [ContactFilterType.AvgOrdersNumberPerMonth]: AvgOrdersPerMonthFilter,
  [ContactFilterType.RevenueForYear]: RevenueForYearFilter,
  [ContactFilterType.RevenueForMonth]: RevenueForMonthFilter,
  [ContactFilterType.AvgCheck]: AvgCheckFilter,
  [ContactFilterType.OrderDays]: OrderDaysFilter,
  [ContactFilterType.OrderTime]: OrderTimeFilter,
  [ContactFilterType.FavoriteDish]: FavoriteDishFilter,
  [ContactFilterType.PollsParticipation]: PollsParticipationFilter,
  [ContactFilterType.OrderType]: OrderTypeFilter,
  [ContactFilterType.SourceType]: SourceTypeFilter,
  [ContactFilterType.Filial]: FilialFilter,
  [ContactFilterType.UsedPromocodesPercent]: UsedPromocodesPercentFilter,
  [ContactFilterType.Promocode]: PromocodeFilter,
  [ContactFilterType.PushToken]: PushTokenFilter,
};

export function ContactFiltersFactory(type) {
  const Model = FILTERS[type];
  if (Model) return new Model();
  return null;
}
