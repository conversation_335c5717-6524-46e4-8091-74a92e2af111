export enum ContactFilterType {
  Search = "contact",
  Gender = "gender",
  Birthday = "birthday",
  CreatedAt = "createdAt",
  UpdatedAt = "updatedAt",
  ClientFilial = "clientFilials",
  ContactData = "contact-data",
  TagsInclude = "tags",
  TagsExclude = "tagsExcept",
  PushToken = "token",

  LastOrderDate = "last-order-date",
  Complaint = "complaint-in-the-order",
  AvgOrdersNumberPerYear = "avg-year-count-orders",
  AvgOrdersNumberPerMonth = "avg-month-count-orders",
  RevenueForYear = "order-amount-from-client-by-year",
  RevenueForMonth = "order-amount-from-client-by-month",
  AvgCheck = "avg-check-customer-orders",
  OrderDays = "main-orders-days",
  OrderTime = "favorite-order-time",
  FavoriteDish = "favorite-dish",
  PollsParticipation = "polls-participation",
  OrderType = "order-type",
  SourceType = "source-type",
  Filial = "filial",
  UsedPromocodesPercent = "codes-percent",
  Promocode = "promocode",
}
