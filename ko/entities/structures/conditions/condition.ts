import { ContactCondition, ContactFilter } from "./conditions.types";
import { ContactFiltersFactory } from "./filters-factory";
import { ee } from "@/utils/event-emitter";
import { FILTERS_CHANGE_EVENT } from "./events";
import { FILTER_COMPONENTS } from "@/presentation/views/fc-conditions/contact-condition/filter-components";

const { observable } = ko;

export function ContactConditionModel(): ContactCondition {
  const emitter = ee();

  const type = observable(null).extend({
    required: {
      message: "Обязательное поле",
    },
  });
  const filter = observable<ContactFilter | null>(null);

  type.subscribe((v) => {
    const currentFilter = filter();
    if (currentFilter) {
      currentFilter.dispose();
    }

    console.log("TYPE", v);

    if (v) {
      const filterModel = ContactFiltersFactory(v);
      filter(filterModel);
      filterModel.subscribe(() => {
        emitter.emit(FILTERS_CHANGE_EVENT);
      }, err => {
        console.error("Error in subscribe:", err);
      });
    } else {
      filter(null);
    }

    emitter.emit(FILTERS_CHANGE_EVENT);
  }, err => {
    console.error("Error in type.subscribe:", err);
  });

  return {
    type,
    filter,

    isEmpty: () => {
      if (!type()) return true;
      return filter()?.isEmpty();
    },
    isValid: () => {
      if (!type.isValid()) return false;
      return filter()?.isValid();
    },

    getValue() {
      return {
        type: type(),
        value: filter()?.getValue(),
      };
    },
    setValue(value) {
      if (value) {
        setTimeout(() => {
          type(value.type);
        });
      }
    },

    onChange: (cb: () => void) => {
      console.log('onChange condition', filter(), cb);

      emitter.on(FILTERS_CHANGE_EVENT, cb);
    },
    dispose() {
      emitter.off();
    },
  };
}

