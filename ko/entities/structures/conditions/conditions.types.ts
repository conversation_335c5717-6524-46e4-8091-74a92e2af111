import { BirthdayFilterValue } from "../birthday-filter/types";
import { DatePeriodFilterValue } from "../date-period-filter/types";
import { DateWithCriteriaFilterValue } from "../date-with-criteria-filter/types";
import { ContactFilterType } from "./contact-filter-types";
import { OrderHistoryTypes } from "@/constants/order/order-history";
import { OrderTypes } from "@/constants/order/order-types";
import { OrderSourceTypes } from "@/constants/order/source-types";
import { PushTokenStates } from "@/constants/client/push-token-state";

type CallbackFn = () => void;

export type ContactFilterStringValue = string;
export type ContactFilterRangeValue = [number, number];
export type ContactFilterArrayValue = Array<string>;
export type ContactFilterTokenValue = PushTokenStates;
export type ContactFilterBirthdayValue = BirthdayFilterValue;
export type ContactFilterOrderDateValue = DateWithCriteriaFilterValue & {
  complaint?: 0 | 1;
};
export type ContactFilterDatePeriodValue = DatePeriodFilterValue;
export type ContactFilterFilialsValue = {
  filials: Array<string>;
  except: 0 | 1;
  history?: OrderHistoryTypes;
};
export type ContactFilterContactFieldsValue = {
  fields: Array<number | string>;
  filled: 0 | 1;
};
export type ContactFilterDishValue = {
  dishes: Array<string>;
  minPrice: string;
};
export type ContactFilterOrderValue = {
  history: OrderHistoryTypes;
  orderType?: OrderTypes;
  sourceType?: OrderSourceTypes;
};
export type ContactFilterPromocodeValue = {
  pools: Array<string>;
  used: 0 | 1;
};

export type ContactFilterValue =
  | ContactFilterStringValue
  | ContactFilterRangeValue
  | ContactFilterArrayValue
  | ContactFilterBirthdayValue
  | ContactFilterOrderDateValue
  | ContactFilterDatePeriodValue
  | ContactFilterFilialsValue
  | ContactFilterDishValue
  | ContactFilterOrderValue
  | ContactFilterPromocodeValue
  | ContactFilterContactFieldsValue
  | ContactFilterTokenValue;

export type ContactConditionValue = {
  type: ContactFilterType;
  value: ContactFilterValue;
};

export interface ContactFilter {
  isValid: () => boolean;
  isEmpty: () => boolean;

  getValue: () => ContactFilterValue;
  setValue: any;

  onChange: (cb: CallbackFn) => void;
  dispose: () => void;
}

export interface ContactCondition {
  type: KnockoutObservable<ContactFilterType>;
  filter: KnockoutObservable<ContactFilter | null>;

  isValid: () => boolean;
  isEmpty: () => boolean;

  getValue: () => ContactConditionValue;
  setValue: (value: any) => void;

  onChange: (cb: CallbackFn) => void;
  dispose: () => void;
}

export type ContactConditions = {
  list: KnockoutObservableArray<ContactCondition>;

  isValid: KnockoutObservable<boolean>;
  isEmpty: KnockoutObservable<boolean>;

  addCondition: () => void;
  removeCondition: (condition: ContactCondition) => void;

  getValue: () => Array<ContactConditionValue>;
  setValue: (values: Array<any>) => void;

  onChange: (cb: CallbackFn) => void;
  dispose: () => void;
};
