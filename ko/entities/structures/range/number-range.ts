import { ERROR_TEXT } from "@/constants/validators/errors";
import { RangeConfig, Range } from "./types";
const { computed, toJS } = ko;

export function NumberRangeModel(
  from: KnockoutObservable<number>,
  to: KnockoutObservable<number>,
  config?: RangeConfig
): Range {
  const state = computed(() => {
    const _from = from();
    const _to = to();

    let isValid = true;
    let isFromValid = true;
    let isToValid = true;
    let error = null;
    let fromError = null;
    let toError = null;

    const hasFromValue = _from || _from == 0;
    const hasToValue = _to || _to == 0;

    if (config.required && (!hasFromValue || !hasToValue)) {
      if (!hasFromValue) {
        isFromValid = false;
        fromError = config?.requiredError || ERROR_TEXT.required;
      }
      if (!hasToValue) {
        isToValid = false;
        toError = config?.requiredError || ERROR_TEXT.required;
      }

      isValid = isFromValid && isToValid;
      error = config?.requiredError || ERROR_TEXT.required;
    } else if (_from > _to) {
      isValid = false;
      isFromValid = false;
      isToValid = false;
      error = config.rangeError || ERROR_TEXT.range;
    }

    return {
      isValid,
      isFromValid,
      isToValid,
      error: isValid ? null : error,
      fromError: isValid ? null : fromError,
      toError: isValid ? null : toError,
    };
  });

  return {
    isValid: computed(() => state().isValid),
    isFromValid: computed(() => state().isFromValid),
    isToValid: computed(() => state().isToValid),
    error: computed(() => state().error),
    fromError: computed(() => state().fromError),
    toError: computed(() => state().toError),
  };
}
