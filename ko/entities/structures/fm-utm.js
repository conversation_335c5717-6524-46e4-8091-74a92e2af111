import { FModel } from '@/entities/f-model';

export class FmUTM extends FModel {
  constructor() {
    super();

    this.source = ko.observable('');
    this.medium = ko.observable('');
    this.campaign = ko.observable('');

    this.listen(this.source, this.medium, this.campaign);
  }

  update(data) {
    this.source(data.source || '');
    this.medium(data.medium || '');
    this.campaign(data.campaign || '');
  }

  getData() {
    return {
      source: this.source(),
      medium: this.medium(),
      campaing: this.campaign()
    };
  }
}
