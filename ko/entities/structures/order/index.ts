import { DeliveryTypes, SourceTypes } from "@/constants/order";
import { OrderVars, Order } from "./types";
import { serverDateStringToClientDateTimeString } from "@/utils/date/formats";

export function OrderModel(data: OrderVars): Order {
  const {
    created_time,
    address,
    delivery_type,
    id,
    source_type,
    sum,
  } = data;

  return {
    createdAt: serverDateStringToClientDateTimeString(created_time),
    deliveryAddress: address,
    deliveryType: `${delivery_type}` as DeliveryTypes,
    number: id,
    sourceType: `${source_type}` as SourceTypes,
    sum,
  };
}
