import { DeliveryTypes, SourceTypes } from "@/constants/order";
import { ClientDateTime } from "@/types"

export type OrderVars = {
    created_time: ClientDateTime;
    address: string | null;
    delivery_type: DeliveryTypes;
    id: number;
    source_type: SourceTypes;
    sum: string;
}

export type Order = {
    createdAt: ClientDateTime;
    deliveryAddress: string | null;
    deliveryType: DeliveryTypes;
    number: number;
    sourceType: SourceTypes;
    sum: string;
}