import { FModel } from '../f-model';
import { Translator } from '@/utils/translate';

export class FmRange extends FModel {
  constructor(config = {}) {
    super();

    this.validationTranslator = Translator('validation');

    this.from = ko.observable(config.from || null);
    this.to = ko.observable(config.to || null);

    this.rangeError = ko.computed(() => {
      let from = this.from();
      let to = this.to();

      if (from == null) return;
      if (to == null) return;

      if (parseInt(from) > parseInt(to)) return true;
    });

    this.listen(this.from, this.to);
  }
}
