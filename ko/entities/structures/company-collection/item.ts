import {
  CompanyCollectionItemVars,
  CompanyCollectionItem,
  CompanyCollectionItemTypes,
} from "./types";

export function CompanyCollectionItemModel(
  data: Partial<CompanyCollectionItemVars>
): CompanyCollectionItem {
  const {
    id,
    title = "",
    description = "",
    type = CompanyCollectionItemTypes.Element,
    parent_id = 0,
    position = 0,
    deleted = 0,
    elements,
  } = data;

  return {
    id: `${id}`,
    name: title,
    description,
    category: type === CompanyCollectionItemTypes.Category,
    parentId: parent_id ? `${parent_id}` : null,
    position,
    deleted: deleted === 1,
    children: (elements || []).map((element) => CompanyCollectionItemModel(element)),
  };
}
