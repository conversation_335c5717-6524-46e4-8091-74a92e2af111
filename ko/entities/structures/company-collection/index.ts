import { CompanyCollectionVars, CompanyCollection } from "./types";

export function CompanyCollectionModel(
  data: Partial<CompanyCollectionVars>
): CompanyCollection {
  const {
    id,
    name = "",
    count = 0,
    description = "",
    system = 0,
    is_active = 1,
    used = 0,
  } = data;

  return {
    id: `${id}`,
    name: name,
    count: Number(count),
    description,
    system: system === 1,
    active: is_active === 1,
    canRemove: used === 0,
  };
}
