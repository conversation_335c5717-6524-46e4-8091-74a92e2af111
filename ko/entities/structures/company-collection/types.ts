import { ServerDateTime } from "@/types";

export type CompanyCollectionVars = {
  id: string | number;
  name: string;
  description: string;
  count: number | string;
  is_active: 0 | 1;
  system: 0 | 1;
  used: 0 | 1;
};

export type CompanyCollection = {
  id: string;
  name: string;
  description: string;
  count: number;
  canRemove: boolean;
  active: boolean;
  system: boolean;
};

export enum CompanyCollectionItemTypes {
  Element = "element",
  Category = "category",
}

export type CompanyCollectionItemVars = {
  id: number;
  dictionary_id: number;
  parent_id: number | string;

  title: string;
  description: string | null;

  position: number;
  type: CompanyCollectionItemTypes;

  deleted: 0 | 1;

  created_at: ServerDateTime;
  updated_at: ServerDateTime;

  elements?: Array<CompanyCollectionItemVars>
};

export type CompanyCollectionItem = {
  id: string;
  name: string;
  description: string;
  category: boolean;
  position: number;
  deleted: boolean;
  parentId: string;
  children: Array<CompanyCollectionItem>
};
