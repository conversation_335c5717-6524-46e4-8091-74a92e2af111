import { ClientDate } from "@/types";
import { PeriodPickerValue } from '@/entities/structures/period-picker-value';

export enum DateWithCriteriaFilterTypes {
  Days = "1",
  Period = "2",
  BeforeDate = "3",
  AfterDate = "4",
  Date = "5",
}


export type DateWithCriteriaFilterValue = {
  criteria: DateWithCriteriaFilterTypes;
  date: ClientDate | null;
  period: PeriodPickerValue | null;
  from: number;
  to: number;
}