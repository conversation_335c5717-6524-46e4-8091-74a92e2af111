import { ee } from "@/utils/event-emitter";
import { periodValidator } from "@/utils/validation/period";
import { DateWithCriteriaFilterTypes } from "./types";
import { CompoundValue } from "@/entities/structures/compound-value";
import { ClientDate } from "@/types";
import { dateValidator } from "@/utils/validation/date";
import { PeriodPickerValue } from "@/presentation/components/fc-form/fc-period-picker/types";
import { Range } from "@/entities/structures/range/types";
import { NumberRangeModel } from "../range/number-range";
import { ERROR_TEXT } from '@/constants/validators/errors';

const { observable } = ko;

export class DateWithCriteaFilterModel extends CompoundValue {
  criteria: KnockoutObservable<DateWithCriteriaFilterTypes>;
  date: KnockoutObservable<ClientDate>;
  period: KnockoutObservable<PeriodPickerValue>;
  from: KnockoutObservable<number>;
  to: KnockoutObservable<number>;
  range: Range;

  constructor() {
    super();

    this.criteria = observable(DateWithCriteriaFilterTypes.Days).extend({
      required: {
        message: ERROR_TEXT.required,
      },
    });
    this.date = ko.observable("").extend({
      required: {
        message: ERROR_TEXT.required,
        onlyIf: () =>
          [
            DateWithCriteriaFilterTypes.Date,
            DateWithCriteriaFilterTypes.BeforeDate,
            DateWithCriteriaFilterTypes.AfterDate,
          ].includes(this.criteria()),
      },
      validation: {
        validator: dateValidator(),
        message: ERROR_TEXT.format,
        onlyIf: () =>
          [
            DateWithCriteriaFilterTypes.Date,
            DateWithCriteriaFilterTypes.BeforeDate,
            DateWithCriteriaFilterTypes.AfterDate,
          ].includes(this.criteria()),
      },
    });
    this.period = ko.observable(null).extend({
      validation: [
        {
          validator: (v) => {
            return v && v.from && v.to;
          },
          message: ERROR_TEXT.required,
          onlyIf: () => this.criteria() === DateWithCriteriaFilterTypes.Period,
        },
        {
          validator: periodValidator(),
          message: ERROR_TEXT.format,
          onlyIf: () => this.criteria() === DateWithCriteriaFilterTypes.Period,
        },
      ],
    });
    this.from = ko.observable(0);
    this.to = ko.observable(0);
    this.range = NumberRangeModel(this.from, this.to, {
      required: true
    });

    this._subscribeForChanges(
      this.criteria,
      this.date,
      this.period,
      this.from,
      this.to
    );
    const sb = this.criteria.subscribe(() => {
      this.date("");
      this.period(null);
      this.from(0);
      this.to(0);
    });
    this._addDisposeCb(() => sb.dispose());
  }

  isEmpty() {
    switch (this.criteria()) {
      case DateWithCriteriaFilterTypes.Days:
        return false;
      case DateWithCriteriaFilterTypes.Date:
      case DateWithCriteriaFilterTypes.BeforeDate:
      case DateWithCriteriaFilterTypes.AfterDate:
        return !this.date();
      case DateWithCriteriaFilterTypes.Period:
        const _period = this.period();
        return !_period || (!_period.from && !_period.to);

      default:
        return true;
    }
  }

  isValid() {
    return [
      this.criteria,
      this.period,
      this.date,
      this.range,
    ].every((field) => field.isValid());
  }

  setValue(value) {
    this.criteria(value.criteria || DateWithCriteriaFilterTypes.Days);
    this.date(value.date || "");
    this.period(value.period || null);
    if (value.from !== undefined && value.to !== undefined) {
      this.from(value.from);
      this.to(value.to);
    }
  }


  getValue() {
    const value = {
      criteria: this.criteria(),
      date: null,
      period: null,
      from: null,
      to: null,
    };

    switch (this.criteria()) {
      case DateWithCriteriaFilterTypes.Days:
        value.from = this.from();
        value.to = this.to();
        break;
      case DateWithCriteriaFilterTypes.Date:
      case DateWithCriteriaFilterTypes.BeforeDate:
      case DateWithCriteriaFilterTypes.AfterDate:
        value.date = this.date();
        break;
      case DateWithCriteriaFilterTypes.Period:
        value.period = this.period();
        break;
    }

    return value;
  }
}
