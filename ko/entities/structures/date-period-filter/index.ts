import { CompoundValue } from "@/entities/structures/compound-value";
import { DatePeriodFilterTypes } from "./types";
import { PeriodPickerValue } from "../period-picker-value";
import { ClientDate } from "@/types";
import { dateValidator } from "@/utils/validation/date";
import { periodValidator } from "@/utils/validation/period";

export class DatePeriodFilterModel extends CompoundValue {
  type: KnockoutObservable<DatePeriodFilterTypes>;
  date: KnockoutObservable<ClientDate>;
  period: KnockoutObservable<PeriodPickerValue>;

  constructor() {
    super();

    this.type = ko.observable(DatePeriodFilterTypes.Date).extend({
      required: {
        message: "Обязательное поле",
      },
    });
    this.date = ko.observable("").extend({
      required: {
        message: "Обязательное поле",
        onlyIf: () => this.type() === DatePeriodFilterTypes.Date,
      },
      validation: {
        validator: dateValidator(),
        message: "Некорректный формат",
        onlyIf: () => this.type() === DatePeriodFilterTypes.Date,
      },
    });
    this.period = ko.observable(null).extend({
      required: {
        message: "Обязательное поле",
        onlyIf: () => this.type() === DatePeriodFilterTypes.Period,
      },
      validation: {
        validator: periodValidator(),
        message: "Некорректный формат",
        onlyIf: () => this.type() === DatePeriodFilterTypes.Period,
      },
    });

    const sb = this.type.subscribe(() => {
      this.date("");
      this.period(null);
    });

    this._addDisposeCb(() => sb.dispose());
    this._subscribeForChanges(this.type, this.date, this.period);
  }

  isEmpty() {
    switch (this.type()) {
      case DatePeriodFilterTypes.Date:
        return !this.date();
      case DatePeriodFilterTypes.Period:
        return !this.period();
      default:
        return true;
    }
  }

  isValid() {
    return [this.type, this.date, this.period].every((field) =>
      field.isValid()
    );
  }

  setValue(value) {
    if (value) {
      if (value.hasOwnProperty('type')) {
        this.type(value.type);
      }

      switch (value.type) {
        case DatePeriodFilterTypes.Date:
          if (value.hasOwnProperty('date')) {
            this.date(value.date);
          }
          break;

        case DatePeriodFilterTypes.Period:
          if (value.hasOwnProperty('period')) {
            this.period(value.period);
          }
          break;
      }
    }
  }

  getValue() {
    const value = {
      type: this.type(),
      date: null,
      period: null,
    };

    switch (this.type()) {
      case DatePeriodFilterTypes.Date:
        value.date = this.date();
        break;
      case DatePeriodFilterTypes.Period:
        value.period = this.period();
        break;
    }

    return value;
  }
}
