import { QuestionTypes } from "@/constants/question/questionTypes";
import { ServerDateTime } from "@/types";


export type ReviewAnswerVars = {
  question: {
    name: string;
  };
  rating: number | null;
  type: number;
  ratingType: number;
  smileType: null;
  smilesCount: number | null;
  starCount: number | null;
  answered: boolean;
  comment: string | null;
  self_answer: string | null;
  created_at: ServerDateTime;
  skipped: 0 | 1;
  variant_skipped: 0 | 1;
  smiles: Array<{
    id: number;
    foquz_question_id: number;
    smile_url: string;
    label: string;
  }>;
  extra: any
};

export type ReviewAnswer = {
  type: QuestionTypes,
  questionName: string;
  isRating: boolean;
  answered: boolean;
  skipped: boolean;
  variantSkipped: boolean;
  rating: number | null;
  ratingScale: [number, number];
  ratingView: string;
  normalizedRating: number | null;
  comment: string;
  self_answer: string;
  icon: string;
  extra: {any}
}