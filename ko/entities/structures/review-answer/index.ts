import { QuestionTypes } from "@/constants/question/questionTypes";
import { ReviewAnswer, ReviewAnswerVars } from "./types";
import { getRatingPoint } from "@/utils/rating/get-rating-point";

function getReviewAnswerRating(
  questionType: QuestionTypes,
  rating: number | null,
  ratingType: number
): number | null {
  switch (questionType) {
    case QuestionTypes.Diff:
    case QuestionTypes.Gallery:
    case QuestionTypes.Smile:
    case QuestionTypes.Rating:
    case QuestionTypes.StarVariants:
    case QuestionTypes.Stars:
      return rating || null;
    case QuestionTypes.Nps:
      return rating;
    case QuestionTypes.Scale:
      return rating;
    case QuestionTypes.Rate:
      if (ratingType == 2) return null;
      return rating || null;
    default:
      return null;
  }
}

export function getReviewAnswerRatingScale(
  questionType: QuestionTypes,
  starCount: number,
  smileCount
): [number, number] {
  switch (questionType) {
    case QuestionTypes.Nps:
      return [0, 10];
    case QuestionTypes.Rating:
    case QuestionTypes.Stars:
    case QuestionTypes.StarVariants:
      return [1, starCount || 5];
    case QuestionTypes.Smile:
      return [1, smileCount || 2];
    case QuestionTypes.Scale:
      return [0, 1000];
    default:
      return [1, 5];
  }
}

export function ReviewAnswerModel(data: ReviewAnswerVars): ReviewAnswer {
  const {
    type,
    ratingType,
    answered,
    rating,
    comment,
    skipped,
    variant_skipped,
    starCount,
    smiles,
    smilesCount,
    smileType,
    question,
    self_answer,
    extra
  } = data;

  const questionType = `${type}` as QuestionTypes;
  // console.log('ReviewAnswerModel', data);
  let ratingValue = null;
  if (!skipped) {
    ratingValue = getReviewAnswerRating(questionType, rating, ratingType);
  }

  let ratingScale = getReviewAnswerRatingScale(
    questionType,
    starCount,
    smilesCount
  );

  let ratingView = "default";
  let icon = '';
  if (questionType === QuestionTypes.Smile) {
    ratingView = smileType;
    if (smiles?.length) icon = smiles[0].smile_url;
  }

  let normalizedRating =
    ratingValue !== null
      ? getRatingPoint(ratingValue, ratingScale[0], ratingScale[1])
      : null;

  return {
    type: `${type}` as QuestionTypes,
    questionName: question.name || '',

    isRating: rating !== null,
    ratingScale,
    ratingView,
    icon,

    answered,
    skipped: skipped == 1,
    variantSkipped: variant_skipped == 1,
    rating: ratingValue,
    comment: skipped ? '' : comment,
    self_answer: skipped ? '' : self_answer,
    normalizedRating,
    extra
  };
}
