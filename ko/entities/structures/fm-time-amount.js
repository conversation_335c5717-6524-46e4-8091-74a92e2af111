import { FModel } from "../f-model";
import { timeValidator } from "Utils/validation/time";

export class FmTimeAmount extends FModel {
  constructor(config = {}) {
    super();

    let validator = config.validator || {};
    
    this.days = ko.observable(null);
    this.time = ko.observable(null).extend({
      validation: {
        validator: timeValidator(),
        onlyIf: validator.onlyIf,
        message: "Некорректное значение",
      },
    });

    if (config.value) {
      this.value = value;
    }
    this.listen(this.days, this.time);
    this.createValidator(this.days, this.time);
  }

  get value() {
    return this.getMinutes();
  }

  set value(value) {
    let [days, time] = this.getTimeComponents(value);
    this.days(days);
    this.time(time);
  }

  getTimeComponents(min) {
    if (!min) return ["", ""];

    let days = Math.floor(min / 60 / 24);
    min = min % (60 * 24);
    let hours = Math.floor(min / 60);
    min = min % 60;

    return [days || "", this.getTimeString(hours, min)];
  }

  getTimeString(h, m) {
    if (!h && !m) return "";
    return `${("" + h).padStart(2, 0)}:${("" + m).padStart(2, 0)}`;
  }

  getTimeFromString(t) {
    if (!t) return 0;

    t = t.replace(/\N/g, "0");
    let [h, m] = t.split(":");

    h = parseInt(h) || 0;
    m = parseInt(m) || 0;

    return (parseInt(h) || 0) * 60 + (parseInt(m) || 0);
  }

  getMinutes() {
    let days = this.days();
    let time = this.getTimeFromString(this.time());

    return days * 24 * 60 + time;
  }
}
