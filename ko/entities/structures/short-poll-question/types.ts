import { InterscreenTypes } from "@/constants/question/interscreenTypes";
import { QuestionTypes } from "@/constants/question/questionTypes";

export type ShortPollQuestionVars = {
    id: string;
    main_question_type: QuestionTypes;
    questionName: string;
    pointName: string;
    is_required: '0' | '1';
    screen_type: `${InterscreenTypes}`;
    show_question_number: string;
    donor: string;
}

export type ShortPollQuestion = {
  id: string;
  type: QuestionTypes;
  interscreenType: InterscreenTypes;
  showQuestionNumber: boolean;
  name: string;
  contactPoint: string;
  required: boolean;
  donor: string;
};
