import { ShortPollQuestion, ShortPollQuestionVars } from "./types";
export function ShortPollQuestionModel(
  data: ShortPollQuestionVars
): ShortPollQuestion {
  const {
    id,
    is_required,
    main_question_type,
    donor,
    pointName,
    questionName,
    screen_type,
    show_question_number,
  } = data;

  return {
    id,
    type: main_question_type,
    interscreenType: screen_type ? Number(screen_type) : null,
    showQuestionNumber: show_question_number === "1",
    name: questionName,
    contactPoint: pointName,
    required: is_required === "1",
    donor,
  };
}
