/**
 * Кастомная knockout-модель
 *
 */

import ee from 'event-emitter';
export class FModel {
  constructor() {
    ee(this);

    this.subsriptors = [];
    this.disposeCbs = [];
  }

  get value() {
    return null;
  }

  // валидация нескольких полей
  createValidator(...fields) {
    let validatedObject = {};
    let idx = 1;
    fields.forEach((field) => (validatedObject[idx] = field));

    let validator = ko.validatedObservable(validatedObject, {
      live: true,
      deep: true
    });
    this.isValid = validator.isValid;
    this.error = ko.computed(() => {
      return validator.errors()[0];
    });
  }

  onDispose(fn) {
    this.disposeCbs.push(fn);
  }

  // удаление модели
  dispose() {
    this.onDispose.forEach((fn) => fn());
  }

  // отслеживание изменения observable-полей
  listen(...fields) {
    fields.forEach((field) => {
      let s = field.subscribe((_) => {
        this.onChange();
      });
      this.onDispose(() => s.dispose());
    });
  }

  // Подписка на изменения значения
  subscribe(fn) {
    this.subsriptors.push(fn);
    return {
      dispose: () => {
        this.subscriptors = this.subscriptors.filter((cb) => cb !== fn);
      }
    };
  }

  // выполняется при измении модели
  onChange() {
    this.subsriptors.forEach((fn) => fn(this.value));
  }
}
