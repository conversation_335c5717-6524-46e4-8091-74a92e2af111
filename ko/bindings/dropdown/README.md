# Выпадающий блок

![](./screenshots/1.png)

## В верстке

Первый способ: именованный шаблон.

```
<template id="template-1">
  Dropdown content
</template>

<div data-bind="dropdown: 'template-1'">
  Click me
</div>
```

Второй способ: анонимный шаблон внутри блока с привязкой.

```
<div data-bind="dropdown">
  <button data-dropdown-target>Click me</button>

  <template>
    Dropdown content
  </template>
</div>
```

Настройки:

```
<div data-bind="dropdown, dropdownOptions: {}">
...
</div>
```

* `dropdownOptions` - настройки для плагина tippy
* `dropdownMode` - готовый пресет
  * `button`

## Кнопка с выпадающим меню (mode=button)

![](./screenshots/2.png)

* Ширина меню равна ширине кнопки
* Выравнивание по правому краю

```
<button class="f-btn" type="button" data-bind="dropdown, dropdownMode: 'button'">
  Действия
  <span class="f-btn-append-section">
      <foquz-icon params="icon: 'arrow-bottom'"></foquz-icon>
  </span>

  <template>
    <div class="tippy-list">
      <a class="tippy-list__item">Копировать</a>
      <a class="tippy-list__item">Удалить</a>
    </div>
  </template>
</button>
```

## Привязка контекста

Для привязки данных в шаблоне используется текущий контекст элемента с биндингом.

## Плагины

- [tippy.js](https://atomiks.github.io/tippyjs/)
