@import 'Style/colors';
@import 'Style/content';

.foquz-markdown-editor {
  .kv-md-header {
    .btn {
      background-color: white;
      border: 1px solid #73808d!important;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 0;
      padding-bottom: 0;

      .glyphicon {
        display: block;
        line-height: 1;
        height: 16px;
      }
    }
    .btn-group {
      margin-top: 2px;
      margin-bottom: 2px;
      .btn:not(:first-child) {
        margin-left: -1px;
      }
    }
    .float-right {
      margin-bottom: 10px;
    }
  }

  &.is-invalid {
    .kv-md-editor {
      border-color: @f-color-danger;
    }
  }

  &.is-valid {
    .kv-md-editor {
      border-color: @f-color-success;
    }
  }

  .kv-md-preview {
    .content();
  }
}
