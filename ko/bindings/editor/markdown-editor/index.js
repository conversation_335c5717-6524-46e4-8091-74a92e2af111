import './style';

ko.bindingHandlers.markdownEditor = {
  init: function (
    element,
    valueAccessor,
    allBindings,
    viewModel,
    bindingContext,
  ) {
    element.classList.add('foquz-markdown-editor');
    let field = element.querySelector('textarea');
    ko.applyBindingsToNode(field, { textInput: valueAccessor() });

    ko.utils.domNodeDisposal.addDisposeCallback(element, function() {

    });


  },
  update: function (
    element,
    valueAccessor,
    allBindings,
    viewModel,
    bindingContext,
  ) {

  }
}
