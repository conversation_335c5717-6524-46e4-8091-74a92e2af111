import { CKEditorInstance } from "Utils/editor/ckeditor";

let unique = 1;

ko.bindingHandlers.foquzCkeditor = {
  init: function (element, valueAccessor, allBindings) {
    const modelValue = valueAccessor();
    const editorSettings = allBindings().foquzCkeditorSettings || {};
    const editorInstance = allBindings.get("foquzCkeditorInstance");
    const editorConfig = {};

    let id = "foquz-ckeditor-" + unique++;
    element.id = id;
    element.name = id;

    if (editorSettings.blankExternalLinks) {
      editorConfig.link = {
        decorators: {
          blankExternalLinks: {
            mode: 'automatic',
            callback: url => /^(https?:)?\/\//.test( url ),
            attributes: {
              target: '_blank',
              rel: 'noopener'
            }
          }
        }
      };
    }

    if (editorSettings.toolbar) {
      editorConfig.toolbar = editorSettings.toolbar;
    }

    if (editorSettings.placeholder) {
      editorConfig.placeholder = editorSettings.placeholder;
    }

    if (editorSettings.options) {
      Object.entries(editorSettings.options || {}).forEach(([name, value]) => {
        editorConfig[name] = { options: value };
      })
    }

    ClassicEditor.create(element, editorConfig)
      .then((editor) => {
        if (editorSettings.readOnly) {
          editor.isReadOnly = true;
        }

        let instance = new CKEditorInstance(editor);

        instance.on("change", () => {
          modelValue(instance.getData());
        });

        let value = ko.utils.unwrapObservable(modelValue) || "";
        instance.setData(value);

        if (ko.isWriteableObservable(editorInstance)) {
          editorInstance(instance);
        }

        element.ckeditor = instance;

        if (editorSettings.execute && !value) {
          Object.entries(editorSettings.execute || {}).forEach(([name, value]) => {
            editor.execute(name, { value } );
          });
        }
      })
      .catch((error) => {
        console.error(error);
      });

    // ko.utils.domNodeDisposal.addDisposeCallback(element, function () {
    //   editor.updateElement();
    //   editor.destroy();

    //   if (ko.isWriteableObservable(editorInstance)) {
    //     editorInstance(null);
    //   }
    // });
  },

  update: function (element, valueAccessor, allBindingsAccessor, context) {
    var newValue = ko.utils.unwrapObservable(valueAccessor());
    let editor = element.ckeditor;
    if (editor && editor.getData() != newValue) {
      editor.setData(newValue);
    }
  },
};
