import { createFileInput } from 'Utils/file-loader/create-file-input';

window.translator.load().then(() => {
  ko.bindingHandlers.tinymce = {
    init: function (element, valueAccessor, allBindings) {
      const $element = $(element);
      $element.addClass(['editor', 'tinymce-wrapper']);

      const $editorField = $element.find('.editor__field');

      const isEmptyObservable =
        allBindings.get('tinymceIsEmpty') || ko.observable(false);

      const instanceObservable = allBindings.get('tinymceInstance');

      const readOnly = allBindings.get('readOnly');

      let lang = null;
      let langUrl = null;

      if (window.translator.getLang() == 'ru-RU') {
        lang = 'ru';
        langUrl = '/tinymce/tinymce-languages/ru.js';
      }

      tinymce.init({
        target: $editorField.get()[0],

        readonly: readOnly,

        language: lang,
        language_url: langUrl,

        branding: false,
        statusbar: false,

        height: 300,
        resize: true,
        noneditable_noneditable_class: 'mceNonEditable',
        content_css: [
          '/tinymce/settings/tinymce-content.css',
          '//fonts.googleapis.com/css?family=Roboto:300,400,500,700,900'
        ],

        plugins:
          'print preview fullpage paste importcss searchreplace autolink directionality code visualblocks visualchars fullscreen image link media template codesample table charmap hr pagebreak nonbreaking anchor toc insertdatetime advlist lists wordcount imagetools textpattern noneditable help charmap quickbars emoticons',

        imagetools_cors_hosts: ['picsum.photos'],
        image_advtab: true,
        importcss_append: true,
        image_caption: true,

        menubar: 'file edit view insert format tools table help',
        menu: {
          file: {
            title: 'File',
            items: 'newdocument restoredraft | preview | print '
          },
          edit: {
            title: 'Edit',
            items:
              'undo redo | cut copy paste pastetext | selectall | searchreplace'
          },
          view: {
            title: 'View',
            items:
              'code | visualaid visualchars visualblocks | spellchecker | preview fullscreen'
          },
          insert: {
            title: 'Insert',
            items:
              'image link codesample inserttable | charmap emoticons hr | anchor toc | insertdatetime'
          },
          format: {
            title: 'Format',
            items:
              'bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | forecolor backcolor | removeformat'
          },
          tools: {
            title: 'Tools',
            items: 'spellchecker spellcheckerlanguage | code wordcount'
          },
          table: {
            title: 'Table',
            items: 'inserttable | cell row column | tableprops deletetable'
          },
          help: { title: 'Help', items: 'help' }
        },

        toolbar:
          'undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | alignleft aligncenter alignright alignjustify | outdent indent |  numlist bullist | forecolor backcolor removeformat | charmap emoticons | fullscreen  preview save print | insertfile image link anchor codesample | ltr rtl',
        toolbar_sticky: true,
        toolbar_drawer: 'sliding',

        contextmenu: readOnly ? false : 'link image imagetools table',

        quickbars_selection_toolbar: readOnly
          ? false
          : 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
        fontsize_formats: '8px 10px 12px 14px 18px 24px 36px',
        file_picker_callback: function (callback, value, meta) {
          if (meta.filetype == 'image') {
            createFileInput(
              ({ file }) => {
                var reader = new FileReader();
                reader.onload = function (e) {
                  callback(e.target.result, {
                    alt: ''
                  });
                };
                reader.readAsDataURL(file);
              },
              { open: true, accept: 'image/*' }
            );
          }
        },
        // if (meta.filetype === 'file') {
        //   callback(defaultLogo, { text: 'Сервис опросов' });
        // }
        // if (meta.filetype === 'image') {
        //   callback(defaultLogo, { alt: 'Сервис опросов' });
        // }
        // if (meta.filetype === 'media') {
        //   callback('movie.mp4', {
        //     source2: 'alt.ogg',
        //     poster: defaultLogo
        //   });
        // }
        // },

        extended_valid_elements: 'button[class|contenteditable]',
        custom_elements: 'button',
        valid_children: '+body[style],+head[style]',

        force_br_newlines: true,
        force_p_newlines: false,
        forced_root_block: '',

        convert_urls: false,

        setup: (editor) => {
          if (instanceObservable && ko.isObservable(instanceObservable)) {
            instanceObservable(editor);
          }
          element._tinymceInstance = editor;

          editor.on('init', () => {
            let content = valueAccessor()();
            editor.setContent(content);
            isEmptyObservable(window.utils.string.isHTMLEmpty(content));
          });

          editor.on('input change', () => {
            let content = editor.getContent();
            valueAccessor()(content);
            isEmptyObservable(window.utils.string.isHTMLEmpty(content));
          });

          valueAccessor().subscribe((value) => {
            if (value !== editor.getContent()) {
              editor.setContent(value);
              isEmptyObservable(window.utils.string.isHTMLEmpty(value));
            }
          });

          $element.on('click', '[data-variable]', (event) => {
            const value = $(event.target).data('value');
            editor.execCommand('mceInsertContent', false, value);
          });

          $element.on('set.variable', (event, data) => {
            editor.execCommand('mceInsertContent', false, data);
          });
        }
      });

      window.test = tinymce;
    },
    update: function (element, valueAccessor) {}
  };
});
