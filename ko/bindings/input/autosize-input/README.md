# Поле ввода с автоматически изменяющейся шириной

![](./screenshots/1.png)
![](./screenshots/2.png)

## В верстке

```
<input data-bind="autosizeInput">

<!-- ko let: { input: ko.observable(null) } -->
<input data-bind="autosizeInput: input">
<!-- /ko -->
```

## Параметры

### Экземпляр autosizeInput

Привязка возвращает экземпляр `autosizeInput`.

Также может быть получен из `$(element).data('autosizeInputInstance')`.

**Методы**

- `update()`
- `getOptions()`

**Свойства**

- `_input`
- `_mirror`
- `_options`

## Плагины

- [jQuery autosizeInput](/ko/settings/jquery/autosize-input.js)
