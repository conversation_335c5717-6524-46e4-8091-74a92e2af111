# Слайдер выбора значения/интервала

![](./screenshots/1.png)
![](./screenshots/2.png)

## В верстке

Выбор одного значения

```
<!-- ko let: { value: ko.observable(5)} -->
<div
  data-bind="slider,
    sliderMin: 0,
    sliderMax: 100,
    sliderValue: value,
    disabled: true"></div>
<!-- /ko -->
```

Выбор интервала значений

```
<!-- ko let: { range: ko.observable([5, 10])} -->
<div
  data-bind="slider,
    sliderMin: 0,
    sliderMax: 100,
    sliderRange: true,
    sliderValue: value,
    disabled: true"></div>
<!-- /ko -->
```

## Плагины

* [jQuery U<PERSON> slider](https://jqueryui.com/slider/)
