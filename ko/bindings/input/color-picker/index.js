import "Settings/color-picker";
const Picker = window.ColorPicker.Default;

ko.bindingHandlers.colorPicker = {
  update: function (
    element,
    valueAccessor,
    allBindings,
    viewModel,
    bindingContext
  ) {
    let $container = $(element);
    let $pickerAnchor = $("input.form-control", $container);
    let config = valueAccessor() || {};
    let isHexFormat = config.hexOnly || $pickerAnchor.data("is-hex");
    let typeFormat = $pickerAnchor.data("color-format") || config.colorFormat || "rgb";
    const disabled = $($pickerAnchor.get(0)).attr("disabled");

    $pickerAnchor.css({
      opacity: 0,
      position: "absolute",
      right: "0",
      width: "50%",
      "z-index": 3,
    });
    let $pickerLabel = $(".js-color-preview", $container);
    $pickerLabel.on("click", (event) => {
      event.preventDefault();
      $pickerAnchor.trigger("focus");
    });

    let $hiddenPickerInput = $('<input class="form-control hidden-input"/>');

    $container.append($hiddenPickerInput);
    $hiddenPickerInput.on("focus", (event) => {
      event.preventDefault();
      $pickerAnchor.trigger("focus");
    });

    let colorPicker = new Picker($pickerAnchor.get(0), {
      format: typeFormat,
      history: {
        hidden: true,
      },
    });

    document.getElementById(colorPicker.id).style.zIndex = 5000;

    // function adjustPickerPosition() {
    //   setTimeout(() => {
    //     const widthInput = $hiddenPickerInput.outerWidth();
    //     const pickerElement = document.getElementById(colorPicker.id);
    //     pickerElement.style.left = `${
    //       parseInt(pickerElement.style.left) - widthInput / 2 - 23
    //     }px`;
    //   }, 0);
    // }

    // $pickerAnchor.on("focus", adjustPickerPosition);

    // window.addEventListener("scroll", adjustPickerPosition);

    colorPicker.on(
      "change",
      window.utils.debounce((color) => {
        updatePickerView(color);
      }, 10)
    );

    function updatePickerView(colorData) {
      let rgbaColor = colorData.rgba;
      $pickerLabel.css("background-color", colorData[typeFormat]);
      $hiddenPickerInput.val(colorData[typeFormat]);
      $pickerAnchor.val(colorData[typeFormat]);
      $pickerAnchor.trigger("change");
      let checkerboardElements = $pickerLabel.find(".checkerboard");
      let rgbaRegex = /rgba?\((\d+), (\d+), (\d+), ([\d.]+)\)/;
      let match = rgbaColor.match(rgbaRegex);

      if (match) {
        let r = parseInt(match[1]);
        let g = parseInt(match[2]);
        let b = parseInt(match[3]);
        let a = parseFloat(match[4]);

        // Определение, является ли цвет близким к белому
        let isNearWhite = r > 240 && g > 240 && b > 240;

        checkerboardElements.each(function (index) {
          let opacity = a;
          opacity = opacity === 1 ? 1 : opacity + 0.3;
          let newColor =
            isNearWhite && a < 1
              ? `rgba(186, 186, 186, ${opacity})`
              : `rgba(${r}, ${g}, ${b}, ${opacity})`;
          $(this).css("background-color", newColor);
        });
      }
    }

    function onInputUpdate() {
      let color = $pickerAnchor.get(0).value;
      colorPicker.setColor(color);
      updatePickerView(colorPicker.currentColor);
    }

    setTimeout(() => {
      onInputUpdate();
    }, 1);

    $pickerAnchor.on("redraw", function () {
      setTimeout(function () {
        onInputUpdate();
      }, 100);
    });

    // if ($pickerAnchor.data('is-hex')) {
    //     $pickerAnchor.on('blur', function() {
    //         var re = /^#([0-9a-f]{3}){1,2}$/i;

    //         if (!re.test(String($pickerAnchor.val()).toLowerCase())) {
    //             $pickerAnchor.val('#000000');
    //         }
    //     });
    // }
  },
};
