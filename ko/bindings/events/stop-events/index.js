ko.bindingHandlers.stopEvents = {
  init: function (element, valueAccessor) {
    let eventsList = valueAccessor() || [
      'click',
      'input',
      'change',
      'mousedown'
    ];
    const stopFoo = (e) => e.stopPropagation();
    $(element).on(eventsList.join(' '), stopFoo);

    ko.utils.domNodeDisposal.addDisposeCallback(element, function () {
      element.removeEventListener('keydown', stopFoo);
    });
  }
};
