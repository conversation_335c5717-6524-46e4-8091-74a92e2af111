# Элемент fancybox-галереи

При клике открывается fancybox-галерея

## В верстке

```
<div data-bind="fancyboxGalleryItem: {
  gallery: [
    { src: 'img1.png', opts: { caption: 'Описание1' } },
    { src: 'img2.png', opts: { caption: 'Описание2' } }
  ],
  index: 0
}">

</div>
```

## Параметры

**gallery**

Массив изображений/видео для галереи

**index**

Индекс текущего элемента, с которого нужно начать показ галереи
