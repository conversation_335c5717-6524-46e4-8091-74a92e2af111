# Привязка DOM-элемента

Записывает текущий DOM-элемент в observable для дальнейших манипуляций.

## В верстке

```
<!-- ko let: { wrapper: ko.observable(null) } -->
<div data-bind="element: wrapper">
  <button data-bind="click: function() {
    wrapper().style.background = 'green';
  }"></button>
</div>
<!-- /ko -->
```

```
<!-- ko let: { elements: ko.observableArray([])} -->
<div data-bind="element: elements">element 1</div>
<div data-bind="element: elements">element 2</div>
<div data-bind="element: elements, elementIndex: 1">element 3</div>
<!-- /ko -->
```
