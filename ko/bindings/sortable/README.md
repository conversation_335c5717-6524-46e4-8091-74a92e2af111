# JQuery UI Sortable для Knockout

## В верстке

```
<!-- ko let: { variants: ko.observableArray([
    { text: 'variant1' },
    { text: 'variant2' },
    { text: 'variant3' },
  ]) } -->
  <div class="fsortable: { data: variants, afterAdd: fadeAfterAddFactory(400) }">
    <div data-bind="text: $data.text"></div>
  </div>
<!-- /ko -->
```

## Плагины

* [jQuery UI Sortable](https://jqueryui.com/sortable/)
