@import 'Style/colors';

.ui-sortable {
  &-helper {
    .hide-on-dragging {
      display: none;
    }
  }

  .drag-handle {
    color: @f-color-light;
    cursor: grab;
    svg {
      width: 8px;
      height: 20px;
    }
  }

  .ui-sortable-helper {
    z-index: 20000!important;
    .drag-handle {
      cursor: grabbing;
    }
  }

  &.styled {
    .ui-sortable-helper {
      background: #ffffff;
      box-shadow: 0px 6px 15px rgba(63, 101, 241, 0.3);

      .drag-handle {
        color: @f-color-primary;
      }
    }
    .ui-sortable-placeholder {
      background-color: #9bb0fb;
      visibility: visible !important;
      opacity: 1 !important;
      * {
        display: none !important;
      }
    }

  }
}
