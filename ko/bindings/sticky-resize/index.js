ko.bindingHandlers.stickyResize = {
  init: function (
    element,
    valueAccessor,
    allBindings,
    viewModel,
    bindingContext,
  ) {

    let config = valueAccessor() || {};
    let parentSelector = config.parent || 'body';
    let parent = $(element).parents(parentSelector).get(0);

    element.style.position = 'fixed';

    let windowHeight = ko.observable(document.body.offsetHeight);
    let parentTopOffset = ko.observable(0);
    let parentBottomOffset = ko.observable(0);
    let parentLeftOffset = ko.observable(0);

    parentTopOffset.subscribe((v) => {
      element.classList.toggle('stuck', v < 0);
      element.style.top = Math.max(0, v) + 'px'
    });

    parentBottomOffset.subscribe((v) => {
      element.style.bottom = Math.max(0, v) + 'px'
    });

    parentLeftOffset.subscribe(v => {
      element.style.left = v + 'px';
    })

    let calc = () => {
      let rect = parent.getBoundingClientRect();
      parentTopOffset(rect.top);
      parentBottomOffset(windowHeight() - rect.bottom);
      parentLeftOffset(rect.left);
    };

    $(window)
      .resize(() => {
        windowHeight(document.body.offsetHeight);
        $(window).scroll();
      })
      .scroll(() => {
        calc();
      });

    calc();


    ko.utils.domNodeDisposal.addDisposeCallback(element, function() {

    });


  },
  update: function (
    element,
    valueAccessor,
    allBindings,
    viewModel,
    bindingContext,
  ) {

  }
}
