@import 'Style/breakpoints';

.foquz-client-history {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &__nav {
    .nav-item {
      &__icon {
        display: none;
      }
      &__name {
        display: inline;
      }

      .mobile-and-tablet({
        &__icon {
          display: inline;
          font-size: 0;
        }
        &__name {
          display: none;
        }
      });
    }
  }

  .interactive-table__table {
    min-width: 100%;
    padding-left: 30px;
    padding-right: 30px;

    .mobile-and-tablet({
      padding-left: 15px;
    padding-right: 15px;
    });
  }
  .review-history {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .history-tab-content {
      flex-grow: 1;
    }

    &__scroll {
      height: 100%;
      padding-right: 16px;

      .os-scrollbar-vertical {
        transform: translateX(-10px);
      }
    }
  }

  .os-scrollbar {
    z-index: 20;
  }

  .mobile-and-tablet({
    .review-history {
      &__scroll {
        padding-right: 0;
      }
    }
  });

  .only-mobile({
    .nav-tabs {
      .nav-item {
        padding-left: 4px;
        padding-right: 4px;
        justify-content: center;
        align-items: center;

        &[data-tab="reviews"] {
          &__icon {
            margin-top: 2px;
          }
        }
      }
      .review-details-modal__history-nav-item-count {
        margin-left: 8px;
      }
    }
    .interactive-table__table {
      padding-left: 0!important;
      padding-right: 0!important;
    }

  });
}
