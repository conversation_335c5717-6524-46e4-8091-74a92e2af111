<div class="review-details__history review-history d-flex flex-column"
     data-bind="let: { prefix: 'client-history-' + unique + '-'}">


  <div class="container">
    <nav class="nav nav-tabs review-details-modal__history-nav mb-4 flex-shrink-0 foquz-client-history__nav">
      <!-- ko foreach: { data: tabNames, as: 'tab' } -->
      <a class="nav-item nav-link review-details-modal__history-nav-item disabled"
         data-bind="
         element: $parent.tabs.refs[tab],
         attr: {
           'data-tab': tab,
           id: prefix + 'nav-' + tab,
           href: '#' + prefix + '-' + tab,
         }"
         data-toggle="tab"
         role="tab"
         aria-selected="false">


        <span class="nav-item__name">
          <!-- ko text: $parent.tabLabels[tab] -->
          <!-- /ko -->
        </span>

        <span class="nav-item__icon">
          <svg-icon params="name: 'history-' + tab"
                    class="svg-icon--lg"></svg-icon>
        </span>


        <span class="review-details-modal__history-nav-item-count"
              data-bind="text: $parent.total[tab]"></span>
      </a>
      <!-- /ko -->
    </nav>
  </div>

  <!-- ko if: loading -->
  <spinner></spinner>
  <!-- /ko -->


  <!-- ko if: !loading() && !hasResults() -->
  <div class="p-4 text-center f-color-service" data-bind="text: _t('Ничего не найдено')">

  </div>
  <!-- /ko -->

  <div class="flex-grow-1 overflow-hidden">
    <div class="review-history__scroll"
         data-bind="nativeScrollbar">
      <div class="container pb-5">
        <div class="tab-content statistics__details-modal-dialog-history-tab-content">
          <div class="tab-pane statistics__details-modal-dialog-history-tab-pane"
               role="tabpanel"
               data-bind="attr: {
                id: prefix + '-orders',
              }">
            <div class="full-width">
              <div data-bind="nativeScrollbar"
                   class="w-100">
                <!-- ko if: total.orders -->
                <history-orders params="ref: refs.orders, clientId: clientId"></history-orders>
                <!-- /ko -->
                <!-- ko ifnot: total.orders -->
                <div class="text-center f-color-service p-4" data-bind="text: _t('answers', 'Нет заказов')">

                </div>
                <!-- /ko -->
              </div>
            </div>
          </div>
          <div class="tab-pane statistics__details-modal-dialog-history-tab-pane"
               role="tabpanel"
               data-bind="attr: {
                id: prefix + '-events',
              }">
            <div class="full-width">
              <div data-bind="nativeScrollbar"
                   class="w-100">
                <!-- ko if: total.events -->
                <history-events params="ref: refs.events, clientId: clientId"></history-events>
                <!-- /ko -->
                <!-- ko ifnot: total.events -->
                <div class="text-center f-color-service p-4" data-bind="text: _t('answers', 'Нет событий')">
                </div>
                <!-- /ko -->
              </div>
            </div>
          </div>
          <div class="tab-pane statistics__details-modal-dialog-history-tab-pane"
               role="tabpanel"
               data-bind="attr: {
                id: prefix + '-compensations',
              }">
            <div class="full-width">
              <div data-bind="nativeScrollbar"
                   class="w-100">
                <!-- ko if: total.compensations -->
                <history-compensations params="ref: refs.compensations, clientId: clientId"></history-compensations>
                <!-- /ko -->
                <!-- ko ifnot: total.compensations -->
                <div class="text-center f-color-service p-4"  data-bind="text: _t('answers', 'Нет компенсаций')">
                </div>
                <!-- /ko -->
              </div>
            </div>
          </div>
          <div class="tab-pane statistics__details-modal-dialog-history-tab-pane"
               role="tabpanel"
               data-bind="attr: {
                id: prefix + '-reviews',
              }">
            <div class="full-width">
              <div data-bind="nativeScrollbar"
                   class="w-100">
                <!-- ko if: total.reviews -->
                <history-reviews params="ref: refs.reviews, clientId: clientId"
                                 data-bind="event: {
                      orderClick: function(orderId) { openOrder(orderId); }
                    }"></history-reviews>
                <!-- /ko -->
                <!-- ko ifnot: total.reviews -->
                <div class="text-center f-color-service p-4" data-bind="text: _t('answers', 'Нет отзывов')">
                </div>
                <!-- /ko -->
              </div>
            </div>
          </div>
          <div class="tab-pane statistics__details-modal-dialog-history-tab-pane"
               role="tabpanel"
               data-bind="attr: {
                id: prefix + '-messages',
              }">
            <div class="full-width">
              <div class="container ">
                <div class="f-color-service f-fs-1 mb-3" data-bind="text: _t('answers', 'В этой вкладке отображаются сообщения клиента, которые он отправил по форме обратной связи')">

                </div>
              </div>
              <div data-bind="nativeScrollbar"
                   class="w-100">
                <history-messages params="ref: refs.messages, clientId: clientId"></history-messages>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>

</div>
