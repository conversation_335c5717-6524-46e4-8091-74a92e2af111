import { FoquzComponent } from 'Models/foquz-component';
import { Tabs } from 'Utils/tabs';
import { ApiUrl } from 'Utils/url/api-url';

let unique = 1;
export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.unique = unique++;
    this.loading = ko.observable(true);

    this.clientId = ko.observable(params.clientId);
    this.loaded = ko.observable(false);

    this.activeTab = ko.observable('');

    let tabs = this.tabNames;

    this.tabs = new Tabs(...tabs);
    this.refs = {};
    this.total = {};

    this.activeTab = ko.observable('');

    this.tabs.on('toggle', (tabName) => {
      this.activeTab(tabName);

      let ref = this.refs[tabName]();
      if (ref) ref.reset();
    });

    tabs.forEach((tabName) => {
      this.refs[tabName] = ko.observable(null);
      this.total[tabName] = ko.observable(0);
    });

    this.hasResults = ko.pureComputed(() => {
      return tabs.some((tab) => this.total[tab]() > 0);
    });
  }

  get tabNames() {
    return ['orders', 'events', 'reviews', 'compensations', 'messages'];
  }

  get tabLabels() {
    return {
      orders: _t('answers', 'Заказы'),
      events: _t('answers', 'События'),
      reviews: _t('answers', 'Отзывы'),
      compensations: _t('answers', 'Компенсации'),
      messages: _t('answers', 'Сообщения')
    };
  }

  forEachRef(cb) {
    this.tabNames.forEach(tab => {
      let ref = this.refs[tab]();
      if (ref) cb(ref)
    })
  }

  loadTotal() {
    return new Promise((res) => {
      $.ajax({
        url: ApiUrl('contact/history-stats', { contactId: this.clientId() }),
        success: (response) => {
          this.tabNames.forEach((tab) => {
            this.total[tab](response[tab] || 0);
          });
          res();
        }
      });
    });
  }

  load(clientId) {
    if (!clientId) {
      this.loading(false);
      return;
    }

    this.clientId(clientId);

    this.loadTotal().then(() => {
      this.loading(false);
      if (this.hasResults()) {
        this.tabs.enableAll();
        this.tabs.open('orders');
      }
    });
  }

  onElementRender() {
    this.tabs.bind();
  }

  openOrder(orderId) {
    this.tabs.open('orders');
    this.refs.orders().openOrder(orderId);
  }

  reset() {
    this.tabs.closeAll();

  }

  dispose() {
    this.tabs.dispose();
  }
}
