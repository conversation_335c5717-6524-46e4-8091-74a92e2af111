@import 'Style/breakpoints';

.history-orders {
  display: block;

  .order-list {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    z-index: 100;
    width: 100vw;

    .table {
      table-layout: fixed;
      width: 100%;
    }



    .ps__rail-y {
      opacity: 0;
    }

    th {
      min-width: auto;
    }

    td {
      border-left: 0;
      border-right: 0;
    }


    .review-details-modal__history-orders-table-info-block-item-label,
    .review-details-modal__history-orders-table-info-block-item-value-row {
      font-size: 12px!important;
    }
  }

  .fixed-table {
    th {
      min-width: 100px;
    }
    td {
      min-width: calc(100vw - 115px);
    }
  }

  .only-tablet({
    th, td {
      padding-right: 8px;
      padding-left: 8px;
    }
    .order-number-cell {
      padding-right: 5px!important;
    }
  });

}
