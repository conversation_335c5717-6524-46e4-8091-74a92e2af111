import { InteractiveTable } from 'Models/interactive-table';
import { ApiUrl } from 'Utils/url/api-url';
import { HistoryOrder } from './order';

export class HistoryOrdersTable extends InteractiveTable {
  constructor() {
    super();

    this.clientId = null;
  }

  getParams() {
    let params = super.getParams();
    params.contactId = this.clientId;
    return params;
  }

  load() {
    return new Promise((res, rej) => {
      if (!this.beforeLoad()) {
        res();
        return;
      }

      this.xhr = $.ajax({
        url: ApiUrl('contact/orders'),
        data: this.getParams(),
        success: (response) => {
          let orders = response.orders || [];
          this.afterLoad(orders.map(o => new HistoryOrder(o)));
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.onError();
          rej();
        }
      });
    })

  }
}
