<!-- ko let: { $ordersTab: $component } -->
<media-query params="query: 'tablet+'">
  <interactive-table params="table: $ordersTab.table">
    <table class="table foq-table f-table">

      <col class="outer-cell">
      <col class="toggler-cell">
      <col class="order-number-cell">
      <col class="order-date-cell">
      <col class="order-sum-cell">
      <col class="order-discount-cell">
      <col class="order-type-cell">
      <col class="filial-cell">
      <col class="proccess-cell">
      <col class="complaint-cell">
      <col class="sended-cell">
      <col class="passed-cell">
      <col class="outer-cell">

      <thead>
        <tr>
          <th class="foq-table__outer-element-head-cell"></th>
          <!-- Стрелка -->
          <th></th>
          <th data-bind="text: _t('Номер заказа')"></th>
          <th data-bind="text: _t('Дата и время заказа')"></th>
          <th alint="right" data-bind="text: _t('Сумма заказа')"></th>
          <th data-bind="text: _t('Скидка/промокод')"></th>
          <th data-bind="text: _t('Тип заказа')"></th>
          <th data-bind="text: _t('Филиал')"></th>
          <th data-bind="text: _t('answers', 'Обработал')"></th>
          <!-- Жалоба -->
          <th></th>
          <!-- Отправлено -->
          <th></th>
          <!-- Пройдено -->
          <th></th>
          <th class="foq-table__outer-element-head-cell"></th>
        </tr>
      </thead>

      <tbody>
        <!-- ko foreach: items -->
        <tr class="review-details-modal__history-orders-table-row"
            data-bind="
                      attr: { id: 'history-orders-table-row-' + $data.id },
                      css: { 'review-details-modal__history-orders-table-row--open': $ordersTab.activeOrder() === $data.id }
                  ">
          <td class="foq-table__outer-element-cell"></td>
          <td class="review-details-modal__history-orders-table-arrow-cell">
            <i class="review-details-modal__history-orders-table-arrow"
               data-bind="click: function () {
                        if ($ordersTab.activeOrder() === $data.id) $ordersTab.activeOrder(null);
                        else $ordersTab.activeOrder($data.id);
                        }">
            </i>
          </td>
          <td class="review-details-modal__history-orders-table-id-cell"
              data-bind="click: function () { $ordersTab.activeOrder(id); }">
            <a class="review-details-modal__history-orders-table-id"
               data-bind="text: '#' + orderNumber"
               href="#">
            </a>
          </td>
          <td>
            <span class="review-details-modal__history-orders-table-date">
              <span data-bind="text: createdAtDate"></span><span data-bind="text: createdAtTime"></span>
            </span>
          </td>
          <td class="review-details-modal__history-orders-table-price-cell">
            <!-- ko text: price -->
            <!-- /ko -->
            <i class="far fa-ruble-sign review-details-modal__history-orders-table-price-ruble-sign"></i>
          </td>
          <td>
            <!-- ko if: discount -->
            <!-- ko text: discount -->
            <!-- /ko -->
            <i class="far fa-ruble-sign review-details-modal__history-orders-table-price-ruble-sign"></i>
            <!-- /ko -->

            <!-- ko ifnot: discount -->–<!-- /ko -->
          </td>
          <td data-bind="text: orderType"></td>
          <td data-bind="text: filialName"></td>
          <td data-bind="text: processedBy"></td>
          <!-- Жалоба -->
          <td data-bind="">
            <!-- ko if: complaint -->
            <i class="icon icon-complaint"
               title="Есть жалоба"
               data-bind="tooltip"></i>
            <!-- /ko -->
          </td>
          <!-- Отправлено -->
          <td class="review-details-modal__history-orders-table-sending-indicator-cell">
            <!-- ko if: isSurveySent -->
            <i class="review-details-modal__history-orders-table-sending-indicator"
               data-bind="tooltip, tooltipText: _t('answers', 'Отправлен опрос {survey}', {
                 survey: '&#34;' + surveyName + '&#34;'
               })"></i>
            <!-- /ko -->
          </td>
          <!-- Пройдено -->
          <td class="review-details-modal__history-orders-table-feedback-indicator-cell">
            <!-- ko if: isSurveyPassed -->
            <i class="review-details-modal__history-orders-table-feedback-indicator"
               data-bind="tooltip, tooltipText: _t('answers', 'Опрос {survey} пройден {time}', {
                survey: '&#34;' + surveyName + '&#34;',
                time: surveyPassedAt
               })"></i>
            <!-- /ko -->
          </td>
          <td class="foq-table__outer-element-cell"></td>
        </tr>

        <tr class="review-details-modal__history-orders-table-content-row">
          <td class="foq-table__outer-element-cell"></td>
          <td colspan="11" class="py-0">
            <div class="review-details-modal__history-orders-table-content pt-0"
                 data-bind="slide: $ordersTab.activeOrder() === id">
              <div class="row">

                <div class="col-xs-8 col-sm-8 col-md-8 col-lg-8">
                  <table
                         class="table foq-table review-details-modal__history-orders-table-content-table review-details-modal__history-orders-table-order-list-table">
                    <thead>
                      <tr>
                        <th data-bind="text: _t('answers', 'Состав заказа')"></th>
                        <th></th>
                        <th class="review-details-modal__history-orders-table-order-list-table-price-head-cell">
                          <!-- ko text: orderData.total_amount -->
                          <!-- /ko -->
                          <i
                             class="far fa-ruble-sign review-details-modal__history-orders-table-order-list-table-price-ruble-sign"></i>
                        </th>
                      </tr>
                    </thead>

                    <tbody>
                      <!-- ko foreach: orderData.dishes -->
                      <tr>
                        <td data-bind="text: name"></td>
                        <td class="review-details-modal__history-orders-table-order-list-table-quantity-cell">
                          х
                          <!-- ko text: quantity -->
                          <!-- /ko -->
                        </td>
                        <td class="review-details-modal__history-orders-table-order-list-table-price-cell">
                          <!-- ko text: amount -->
                          <!-- /ko -->
                          <i
                             class="far fa-ruble-sign review-details-modal__history-orders-table-order-list-table-price-ruble-sign"></i>
                        </td>
                      </tr>

                      <!-- /ko -->
                    </tbody>
                  </table>

                  <!-- ko if: orderData.delivery -->
                  <div class="review-details-modal__history-orders-table-info-block">
                    <div class="review-details-modal__history-orders-table-info-block-item">
                      <span class="review-details-modal__history-orders-table-info-block-item-label" data-bind="text: _t('answers', 'Водитель')">

                      </span>

                      <span class="review-details-modal__history-orders-table-info-block-item-value-row">
                        <!-- ko text: orderData.delivery.driver -->
                        <!-- /ko -->
                      </span>
                    </div>

                    <div class="review-details-modal__history-orders-table-info-block-item">
                      <span class="review-details-modal__history-orders-table-info-block-item-label" data-bind="text: _t('answers', 'Доставка')">

                      </span>

                      <span class="review-details-modal__history-orders-table-info-block-item-value-row">
                        <!-- ko text: createdAtDate -->
                        <!-- /ko -->
                        <!-- ko text: createdAtTime -->
                        <!-- /ko -->
                      </span>

                      <span class="review-details-modal__history-orders-table-info-block-item-value-row">
                        <!-- ko text: orderData.delivery.address -->
                        <!-- /ko -->
                      </span>
                    </div>
                  </div>
                  <!-- /ko -->
                </div>
                <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
                  <!-- ko if: orderData.delivery -->
                  <table class="table foq-table review-details-modal__history-orders-table-content-table review-details-modal__history-orders-table-history-table"
                         data-bind="using: orderData.delivery">
                    <thead>
                      <tr>
                        <th class="text-nowrap" data-bind="text: _t('answers', 'История изменений')"></th>
                        <th></th>
                      </tr>
                    </thead>

                    <tbody>
                      <tr>
                        <td data-bind="text: _t('answers', 'Создан')"></td>
                        <td class="review-details-modal__history-orders-table-history-table-time-cell">
                          <!-- ko text: created -->
                          <!-- /ko -->
                        </td>
                      </tr>

                      <tr>
                        <td data-bind="text: _t('answers', 'Подтверждён')"></td>
                        <td class="review-details-modal__history-orders-table-history-table-time-cell">
                          <!-- ko text: accepted -->
                          <!-- /ko -->
                        </td>
                      </tr>

                      <tr>
                        <td data-bind="text: _t('answers', 'Распечатан')"></td>
                        <td class="review-details-modal__history-orders-table-history-table-time-cell">
                          <!-- ko text: printed -->
                          <!-- /ko -->
                        </td>
                      </tr>

                      <tr>
                        <td data-bind="text: _t('answers', 'Оплачен')"></td>
                        <td class="review-details-modal__history-orders-table-history-table-time-cell">
                          <!-- ko text: payed -->
                          <!-- /ko -->
                        </td>
                      </tr>

                      <tr>
                        <td data-bind="text: _t('answers', 'Отправлен')"></td>
                        <td class="review-details-modal__history-orders-table-history-table-time-cell">
                          <!-- ko text: sended -->
                          <!-- /ko -->
                        </td>
                      </tr>

                      <tr>
                        <td data-bind="text: _t('answers', 'Доставлен')"></td>
                        <td class="review-details-modal__history-orders-table-history-table-time-cell">
                          <!-- ko text: delivered -->
                          <!-- /ko -->
                        </td>
                      </tr>
                    </tbody>
                  </table>

                  <!-- /ko -->
                </div>
              </div>
            </div>
          </td>
          <td class="foq-table__outer-element-cell"></td>
        </tr>
        <!-- /ko -->
      </tbody>
    </table>
  </interactive-table>
</media-query>

<media-query params="query: 'mobile'">
  <interactive-table params="table: $ordersTab.table, horizontal: true">
    <table class="fixed-table f-color-text">
      <tbody>
        <tr>
          <th class="f-border-top text-nowrap" data-bind="text: _t('Номер заказа')"></th>
          <!-- ko foreach: items -->
          <td class="f-border-top review-details-modal__history-orders-table-id-cell"
              data-bind="click: function () { $ordersTab.activeOrder(id); }">
            <a class="review-details-modal__history-orders-table-id"
               data-bind="text: '#' + orderNumber"
               href="#">
            </a>
          </td>
          <!-- /ko -->
        </tr>

        <tr>
          <th data-bind="text: _t('Дата/время заказа')"></th>
          <!-- ko foreach: items -->
          <td>
            <span class="review-details-modal__history-orders-table-date">
              <span data-bind="text: createdAtDate"></span><span data-bind="text: createdAtTime"></span>
            </span>
          </td>
          <!-- /ko -->
        </tr>

        <tr>
          <th data-bind="text: _t('Сумма заказа')"></th>
          <!-- ko foreach: items -->
          <td class="review-details-modal__history-orders-table-price-cell bold">
            <!-- ko text: price -->
            <!-- /ko -->
            <i class="far fa-ruble-sign review-details-modal__history-orders-table-price-ruble-sign"></i>
          </td>
          <!-- /ko -->
        </tr>

        <tr>
          <th data-bind="text: _t('Скидка/промокод')"></th>
          <!-- ko foreach: items -->
          <td>
            <!-- ko if: discount -->
            <!-- ko text: discount -->
            <!-- /ko -->
            <i class="far fa-ruble-sign review-details-modal__history-orders-table-price-ruble-sign"></i>
            <!-- /ko -->

            <!-- ko ifnot: discount -->–<!-- /ko -->
          </td>
          <!-- /ko -->
        </tr>

        <tr>
          <th data-bind="text: _t('Тип заказа')"></th>
          <!-- ko foreach: items -->
          <td data-bind="text: orderType"></td>

          <!-- /ko -->
        </tr>
        <tr>
          <th data-bind="text: _t('Филиал')"></th>
          <!-- ko foreach: items -->
          <td data-bind="text: filialName"></td>
          <!-- /ko -->
        </tr>
        <tr>
          <th data-bind="text: _t('answers', 'Обработал')"></th>
          <!-- ko foreach: items -->
          <td class="border-top-0"
              data-bind="text: processedBy"></td>
          <!-- /ko -->
        </tr>
        <tr>
          <th></th>
          <!-- ko foreach: items -->
          <td>
            <div class="d-flex mb-3 align-items-center">
              <!-- ko if: complaint -->
              <i class="icon icon-complaint mr-3"
                 data-bind="tooltip, tooltipText: _t('Есть жалоба')"></i>
              <!-- /ko -->
              <!-- ko if: isSurveySent -->
              <i class="mr-3 review-details-modal__history-orders-table-sending-indicator"
                 data-bind="tooltip, tooltipText: _t('answers', 'Отправлен опрос {survey}', {
                   survey: '&#34;' + surveyName + '&#34;'
                 })"></i>
              <!-- /ko -->
              <!-- ko if: isSurveyPassed -->
              <i class="review-details-modal__history-orders-table-feedback-indicator"
                 data-bind=" tooltip, tooltipText: _t('answers', 'Опрос {survey} пройден {time}', {
                   survey: '&#34;' + surveyName + '&#34;',
                   time: surveyPassedAt
                 })"></i>
              <!-- /ko -->
            </div>
            <a href="javascript: void(0)"
               data-bind="click: function () {
                $ordersTab.activeOrder($data.id);
                },text: _t('Подробнее')"></a>

            <!-- ko if: $ordersTab.activeOrder() == $data.id -->
            <div class="order-list">

              <div data-bind="fScrollbar: { onlyY: true, gradient: true }" style="padding-bottom: 28px">
                <div class="d-flex justify-content-between align-items-center mt-20p mb-10p px-15p">
                  <span class="font-weight-700 f-color-primary f-fs-3"
                        data-bind="text: '#' + $data.id"></span>

                  <span style="font-size: 24px"
                        class="cursor-pointer f-color-service"
                        data-bind="click: function() {
                      $ordersTab.activeOrder(null);
                      }">&times;</span>
                </div>

                <table
                       class="table foq-table review-details-modal__history-orders-table-content-table review-details-modal__history-orders-table-order-list-table">
                  <thead>
                    <tr>
                      <th colspan="2"><span class="f-fs-2" data-bind="text: _t('answers', 'Состав заказа')"></span></th>

                      <th class="review-details-modal__history-orders-table-order-list-table-price-head-cell">
                        <span class="f-fs-2">
                          <!-- ko text: orderData.total_amount -->
                          <!-- /ko -->
                          <i
                             class="far fa-ruble-sign review-details-modal__history-orders-table-order-list-table-price-ruble-sign"></i>
                        </span>
                      </th>
                    </tr>
                  </thead>

                  <tbody>
                    <!-- ko foreach: orderData.dishes -->
                    <tr>
                      <td data-bind="text: name"></td>
                      <td class="review-details-modal__history-orders-table-order-list-table-quantity-cell">
                        <span class="text-nowrap" data-bind="text: 'x ' + quantity"></span>
                      </td>
                      <td class="review-details-modal__history-orders-table-order-list-table-price-cell font-weight-500">
                        <!-- ko text: amount -->
                        <!-- /ko -->
                        <i
                           class="far fa-ruble-sign review-details-modal__history-orders-table-order-list-table-price-ruble-sign"></i>
                      </td>
                    </tr>

                    <!-- /ko -->
                  </tbody>
                </table>

                <!-- ko if: orderData.delivery -->
                <table class="table foq-table review-details-modal__history-orders-table-content-table review-details-modal__history-orders-table-history-table"
                       data-bind="using: orderData.delivery">
                  <thead>
                    <tr>
                      <th colspan="2"><span class="f-fs-2" data-bind="text: _t('answers', 'История изменений')"></span></th>

                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td data-bind="text: _t('answers', 'Создан')"></td>
                      <td class="review-details-modal__history-orders-table-history-table-time-cell">
                        <!-- ko text: created -->
                        <!-- /ko -->
                      </td>
                    </tr>

                    <tr>
                      <td data-bind="text: _t('answers', 'Подтверждён')"></td>
                      <td class="review-details-modal__history-orders-table-history-table-time-cell">
                        <!-- ko text: accepted -->
                        <!-- /ko -->
                      </td>
                    </tr>

                    <tr>
                      <td data-bind="text: _t('answers', 'Распечатан')"></td>
                      <td class="review-details-modal__history-orders-table-history-table-time-cell">
                        <!-- ko text: printed -->
                        <!-- /ko -->
                      </td>
                    </tr>

                    <tr>
                      <td data-bind="text: _t('answers', 'Оплачен')"></td>
                      <td class="review-details-modal__history-orders-table-history-table-time-cell">
                        <!-- ko text: payed -->
                        <!-- /ko -->
                      </td>
                    </tr>

                    <tr>
                      <td data-bind="text: _t('answers', 'Отправлен')"></td>
                      <td class="review-details-modal__history-orders-table-history-table-time-cell">
                        <!-- ko text: sended -->
                        <!-- /ko -->
                      </td>
                    </tr>

                    <tr>
                      <td data-bind="text: _t('answers', 'Доставлен')"></td>
                      <td class="review-details-modal__history-orders-table-history-table-time-cell">
                        <!-- ko text: delivered -->
                        <!-- /ko -->
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div class="px-15p f-fs-1" style="line-height: 1.4; ">
                  <div class="review-details-modal__history-orders-table-info-block-item">
                    <span class="review-details-modal__history-orders-table-info-block-item-label" data-bind="text: _t('answers', 'Водитель')">

                    </span>

                    <span class="review-details-modal__history-orders-table-info-block-item-value-row">
                      <!-- ko text: orderData.delivery.driver -->
                      <!-- /ko -->
                    </span>
                  </div>

                  <div class="review-details-modal__history-orders-table-info-block-item mt-15p">
                    <span class="review-details-modal__history-orders-table-info-block-item-label" data-bind="text: _t('answers', 'Доставка')">

                    </span>

                    <span class="review-details-modal__history-orders-table-info-block-item-value-row">
                      <!-- ko text: createdAtTime -->
                      <!-- /ko -->

                      <!-- ko text: createdAtDate -->
                      <!-- /ko -->
                    </span>

                    <span class="review-details-modal__history-orders-table-info-block-item-value-row">
                      <!-- ko text: orderData.delivery.address -->
                      <!-- /ko -->
                    </span>
                  </div>
                </div>
                <!-- /ko -->
              </div>
            </div>
            <!-- /ko -->
          </td>
          <!-- /ko -->
        </tr>
      </tbody>
    </table>
  </interactive-table>
</media-query>
<!-- /ko -->
