import { FoquzComponent } from 'Models/foquz-component';
import { DialogsModule } from 'Utils/dialogs-module';
export class HistoryTab extends FoquzComponent {
  constructor(table, params, element) {
    super(params, element);

    DialogsModule(this);

    this.table = table;

    this.clientId = params.clientId;
    this.table.clientId = this.clientId();

    this.clientId.subscribe(v => {
      this.table.clientId = v;
    })
  }

  reset() {
    this.table.reset();
  }
}
