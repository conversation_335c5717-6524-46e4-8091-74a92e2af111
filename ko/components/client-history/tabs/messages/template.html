<!-- ko let: { $messagesTab: $component } -->
<media-query params="query: 'tablet+'">
  <interactive-table params="table: $messagesTab.table, notFoundText: _t('answers', 'Сообщений пока нет')">

    <table class="table foq-table f-table f-table--searchable f-table-dense">
      <thead>
        <tr>
          <th width="120">
            <table-head-cell params="table: $data, name: 'created'">
              <!-- ko text: _t('Отправлено') -->
              <!-- /ko -->
            </table-head-cell>
          </th>

          <th width="180">
            <table-head-cell params="table: $data, name: 'filial'">
              <!-- ko text: _t('Филиал') -->
              <!-- /ko -->
            </table-head-cell>
          </th>

          <th width="180">
            <table-head-cell params="table: $data, name: 'theme'">
              <!-- ko text: _t('answers', 'Тема сообщения') -->
              <!-- /ko -->
            </table-head-cell>
          </th>

          <th>
            <table-head-cell params="table: $data, name: 'text'">
              <!-- ko text: _t('answers', 'Сообщение') -->
              <!-- /ko -->
            </table-head-cell>
          </th>
        </tr>
      </thead>
      <tbody>
        <!-- ko foreach: items -->
        <tr class="review-details-modal__history-orders-table-row">
          <td class="sm semibold"
              valign="middle"
              data-bind="text: createdAt"></td>

          <td class="sm semibold"
              valign="middle"
              data-bind="text: filial"></td>

          <td class="sm semibold"
              valign="middle">
            <!-- ko if: theme -->
            <span data-bind="text: theme.name, style: {
              color: theme.color
            }"></span>
            <!-- /ko -->
          </td>

          <td class="sm"
              valign="middle">
            <div data-bind="text: text"
                 class="f-fs-2"></div>
            <!-- ko if: files.length -->
            <attachments params="files: files"
                         class="mt-5p"></attachments>
            <!-- /ko -->
          </td>
        </tr>
        <!-- /ko -->
      </tbody>
    </table>

  </interactive-table>
</media-query>

<media-query params="query: 'mobile'">
  <interactive-table params="table: $messagesTab.table, horizontal: true"
                     class="history-messages-table history-messages-table--mobile">
    <table class="fixed-table f-color-text">
      <tr>
        <th width="120"
            class="f-border-top"><span class="font-weight-700" data-bind="text: _t('Отправлено')"></span></th>
        <!-- ko foreach: items -->
        <td valign="middle"
            class="f-border-top"
            data-bind="text: createdAt">
        </td>
        <!-- /ko -->
      </tr>

      <tr>
        <th><span class="font-weight-700" data-bind="text: _t('Филиал')"></span></th>
        <!-- ko foreach: items -->
        <td valign="middle"
            data-bind="text: filial">
        </td>
        <!-- /ko -->
      </tr>


      <tr>
        <th><span class="font-weight-700" data-bind="text: _t('answers', 'Тема сообщения')"></span></th>
        <!-- ko foreach: items -->
        <td valign="middle">
          <!-- ko if: theme -->
          <span data-bind="text: theme.name, style: {
          color: theme.color
        }"></span>
          <!-- /ko -->
        </td>
        <!-- /ko -->
      </tr>

      <tr>
        <th valign="top"><span class="font-weight-700" data-bind="text: _t('answers', 'Сообщение')"></span></th>
        <!-- ko foreach: items -->
        <td valign="top">

          <!-- ko if: files.length -->
          <attachments params="files: files"
                       class="mb-5p"></attachments>
          <!-- /ko -->

          <div data-bind="text: text"
               class="f-fs-2"></div>
        </td>
        <!-- /ko -->
      </tr>
    </table>
  </interactive-table>
</media-query>

<!-- /ko -->
