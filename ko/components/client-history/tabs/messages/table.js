import { InteractiveTable } from 'Models/interactive-table';
import { ApiUrl } from 'Utils/url/api-url';
import { FeedbackReview } from 'Models/feedback-review';
import { SortModel } from 'Models/sort';

export class HistoryMessagesTable extends InteractiveTable {
  constructor() {
    super();

    this.clientId = null;
  }

  get searchParamName() {
    return 'search';
  }

  getSearch() {
    return this._createSearch('created', 'theme', 'filial', 'text');
  }

  getSort() {
    return new SortModel('created', true);
  }

  getParams() {
    let params = super.getParams();
    params.contact_id = this.clientId;
    return params;
  }

  load() {
    return new Promise((res, rej) => {
      if (!this.beforeLoad()) {
        res();
        return;
      }

      this.xhr = $.ajax({
        url: ApiUrl('company-feedback'),
        data: this.getParams(),
        success: (response) => {
          let messages = response.items || [];
          this.afterLoad(messages.map((o) => new FeedbackReview(o)));
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.onError();
          rej();
        }
      });
    });
  }
}
