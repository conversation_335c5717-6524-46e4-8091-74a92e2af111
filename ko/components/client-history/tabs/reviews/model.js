import { HistoryTab } from '../history-tab';
import { HistoryReviewsTable } from './table';
import 'Dialogs/review-rating-dialog';
import 'Dialogs/review-sidesheet';

export class ViewModel extends HistoryTab {
  constructor(params, element) {
    super(new HistoryReviewsTable(), params, element);

    this.activeOrder = ko.observable(null);
  }

  onReviewClick(review) {
    this.emitEvent('reviewClick', review.id);
  }

  onOrderClick(order) {
    this.emitEvent('orderClick', order.id);
  }

  openComments(review) {
    this.openDialog({
      name: 'review-rating-dialog',
      params: {
        answers: review.ratingAnswers
      }
    });
  }

  openDetails(review) {
    this.openSidesheet({
      name: 'review-sidesheet',
      params: {
        reviewId: review.id,
        executorMode: false
      },
      events: {
        update: (data) => {
          review.update(data);
        },
      }
    });
  }
}
