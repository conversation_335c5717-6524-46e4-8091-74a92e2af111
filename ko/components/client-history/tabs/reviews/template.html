<!-- ko let: { $reviewsTab: $component } -->

<media-query params="query: 'tablet+'">
  <interactive-table params="table: $reviewsTab.table">

    <table class="table f-table foq-table">
      <thead>
        <tr>
          <th data-bind="text: _t('Опрос')"></th>
          <th data-bind="text: _t('Отправлен')"></th>
          <th data-bind="text: _t('Пройден')"></th>
          <th data-bind="text: _t('Оценка')"></th>
          <th data-bind="text: _t('Заказ')"></th>
          <th></th>
          <th></th>
          <th></th>
          <th></th>
        </tr>
      </thead>

      <tbody>
        <!-- ko foreach: items -->
        <tr class="cursor-pointer">
          <td data-bind="click: function() { $reviewsTab.onReviewClick($data) }">
            <a class="review-details-modal__history-reviews-table-name"
               data-bind="text: surveyName"
               href="#">

            </a>
            <!-- ko if: byAPI -->
            <span class="f-color-service ml-2 cursor-pointer"
                  style="border: 2px solid; font-size: 10px; border-radius: 2px; padding: 1px 2px; vertical-align: middle; display: inline-block; font-weight: 900"
                  data-bind="tooltip, tooltipText: _t('answers', 'Анкета, отправленная по API')">API</span>
            <!-- /ko -->
          </td>
          <td data-bind="text: sentAt || '—'"></td>
          <td data-bind="text: passedAt || '—'"></td>
          <td class="review-details-modal__history-reviews-table-rating-cell">

            <!-- ko if: $data.ratingAnswers.length -->
            <div class="points points--row"
                 data-bind="click: function(_, event) {
                                event.stopPropagation();
                                $reviewsTab.openComments($data);
                              }">
              <div class="points-wrapper"
                   data-bind="hoverScroll">

                <!-- ko foreach: { data: $data.sortedAnswersWithRating, as: 'answer' }-->

                <rating-point params="value: answer.rating, point: answer.ratingPoint, type: answer.ratingView, comment: answer.comment"
                              data-bind="attr: {
                      title: answer.comment ? question.name + '<br>' + answer.comment : question.name
                    }"
                              data-html="true"
                              data-boundary="window"
                              data-toggle="tooltip"></rating-point>
                <!-- /ko -->

              </div>
            </div>

            <!-- /ko -->
          </td>
          <td>
            <!-- ko if: orderId -->
            <a class="review-details-modal__history-reviews-table-id"
               href="#"
               data-bind="text: '#' + orderId,
                click: function (_, event) {
                    event.stopPropagation();
                    $reviewsTab.onOrderClick(orderId);
                  }">
            </a>
            <!-- /ko -->

          </td>
          <td class="review-details-modal__history-reviews-table-price-cell">

            <!-- ko if: orderId -->

            <!-- ko text: price -->
            <!-- /ko --> <i class="far fa-ruble-sign review-details-modal__history-reviews-table-price-ruble-sign"></i>

            <!-- /ko -->

          </td>
          <td>

            <!-- ko text: processedBy -->
            <!-- /ko -->

          </td>
          <td class="review-details-modal__history-reviews-table-complaint-indicator-cell">
            <!-- ko if: complaint !== null -->
            <i class="review-details-modal__history-reviews-table-complaint-indicator"
               title="Есть жалоба"
               data-bind="tooltip">
            </i>
            <!-- /ko -->
          </td>
          <td class="review-details-modal__history-reviews-table-compensation-indicator-cell">
            <!-- ko if: $data.compensation -->
            <i class="review-details-modal__history-reviews-table-compensation-indicator"
               data-bind="tooltip, tooltipText: _t('answers', 'Клиенту была предложена компенсация {compensation}', {
                compensation: '&#34;' + compensation.name + '&#34;'
               })">
            </i>
            <!-- /ko -->
          </td>
        </tr>
        <!-- /ko -->

      </tbody>
    </table>

  </interactive-table>
</media-query>

<media-query params="query: 'mobile'">
  <interactive-table params="table: $reviewsTab.table, horizontal: true">
    <table class="fixed-table f-color-text">
      <tr>
        <th width="80"
            class="f-border-top"><span class="font-weight-700"
                data-bind="text: _t('Опрос')"></span></th>
        <!-- ko foreach: items -->
        <td class="f-border-top"
            data-bind="click: function() { $reviewsTab.openDetails($data); }">
          <a class="review-details-modal__history-reviews-table-name"
             data-bind="text: surveyName"
             href="#">
          </a>
        </td>
        <!-- /ko -->
      </tr>
      <tr>
        <th><span class="font-weight-700"
                data-bind="text: _t('Пройден')"></span></th>
        <!-- ko foreach: items -->
        <td data-bind="text: passedAt"></td>
        <!-- /ko -->
      </tr>
      <tr>
        <th><span class="font-weight-700"
                data-bind="text: _t('Оценка')"></span></th>
        <!-- ko foreach: items -->
        <td>
          <!-- ko if: $data.ratingAnswersCount() > 0 -->
          <div class="points points--row"
               data-bind="click: function(_, event) {
                          event.stopPropagation();
                          $reviewsTab.openComments($data);
                        }">
            <div class="points-wrapper"
                 data-bind="hoverScroll">

              <!-- ko foreach: $data.getSortedRatingAnswers() -->
              <div class="point"
                   data-bind="text: rating, class: 'point--' + rating, css: {
                            'point--comment': comment
                          }, attr: {
                              title: comment ? questionName + '<br>' + comment : questionName
                            }, tooltip,"
                   data-html="true"
                   data-boundary="window"></div>
              <!-- /ko -->
            </div>
          </div>

          <!-- /ko -->
        </td>
        <!-- /ko -->
      </tr>
      <tr>
        <th><span class="font-weight-700"
                data-bind="text: _t('Заказ')"></span></th>
        <!-- ko foreach: items -->
        <td>
          <!-- ko if: orderId -->
          <a class="review-details-modal__history-reviews-table-id"
             href="#"
             data-bind="text: '#' + orderId, click: function (_, event) { event.stopPropagation(); $component.history.openOrder(orderId);  }">
          </a>
          <span class="font-weight-500 text-nowrap">
            <!-- ko text: price -->
            <!-- /ko --> <i class="far fa-ruble-sign review-details-modal__history-reviews-table-price-ruble-sign"></i>
          </span>
          <!-- /ko -->
        </td>
        <!-- /ko -->
      </tr>
      <tr>
        <th><span class="font-weight-700"
                data-bind="text: _t('Обработал')"></span></th>
        <!-- ko foreach: items -->
        <td>
          <!-- ko text: processedBy -->
          <!-- /ko -->
        </td>
        <!-- /ko -->
      </tr>
      <tr>
        <th style="height: 42px"></th>
        <!-- ko foreach: items -->
        <td>
          <!-- ko if: complaint !== null -->
          <i class="review-details-modal__history-reviews-table-complaint-indicator"
             title="Есть жалоба"
             data-bind="tooltip">
          </i>
          <!-- /ko -->

          <!-- ko if: $data.compensation -->
          <i class="review-details-modal__history-reviews-table-compensation-indicator"
             data-bind="tooltip, tooltipText: _t('answers', 'Клиенту была предложена компенсация {compensation}', {
              compensation: '&#34;' + compensation.name + '&#34;'
             })">
          </i>
          <!-- /ko -->
        </td>
        <!-- /ko -->
      </tr>
    </table>
  </interactive-table>
</media-query>

<!-- /ko -->
