import { InteractiveTable } from 'Models/interactive-table';
import { ApiUrl } from 'Utils/url/api-url';

import { AnsweredReview } from 'Models/review/answered-review';

export class HistoryReviewsTable extends InteractiveTable {
  constructor() {
    super();

    this.clientId = null;
  }

  getParams() {
    let params = super.getParams();
    params.contactId = this.clientId;
    return params;
  }

  load() {
    return new Promise((res, rej) => {
      if (!this.beforeLoad()) {
        res();
        return;
      }

      this.xhr = $.ajax({
        url: ApiUrl('contact/reviews'),
        data: this.getParams(),
        success: (response) => {
          let reviews = response.reviews || [];
          this.afterLoad(reviews.map(o => new AnsweredReview(o)));
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.onError();
          rej();
        }
      });
    })

  }
}
