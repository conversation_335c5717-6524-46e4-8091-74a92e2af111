import { InteractiveTable } from 'Models/interactive-table';
import { ApiUrl } from 'Utils/url/api-url';
import { HistoryCompensation } from './compensation'

export class HistoryCompensationsTable extends InteractiveTable {
  constructor() {
    super();

    this.clientId = null;
  }

  getParams() {
    let params = super.getParams();
    params.contactId = this.clientId;
    return params;
  }

  load() {
    return new Promise((res, rej) => {
      if (!this.beforeLoad()) {
        res();
        return;
      }

      this.xhr = $.ajax({
        url: ApiUrl('contact/compensations'),
        data: this.getParams(),
        success: (response) => {
          let compensations = response.compensations || [];
          this.afterLoad(
            compensations.map((c) => new HistoryCompensation(c))
          );
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.onError();
          rej();
        }
      });
    });
  }
}
