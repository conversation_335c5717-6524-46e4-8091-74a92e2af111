<!-- ko let: { $compensationsTab: $component } -->


<media-query params="query: 'tablet+'">
  <interactive-table params="table: $compensationsTab.table">
    <table class="table foq-table">
      <thead>
        <tr>
          <th data-bind="text: _t('Опрос')"></th>
          <th data-bind="text: _t('Пройден')"></th>
          <th data-bind="text: _t('answers', 'Компенсация')"></th>
          <th data-bind="text: _t('answers', 'Причина компенсации')"></th>

        </tr>
      </thead>

      <tbody>
        <!-- ko foreach: items -->
        <tr class="review-details-modal__history-compensations-table-row">
          <td data-bind="click: function() { $compensationsTab.onReviewClick($data) }">
            <a class="review-details-modal__history-compensations-table-name"
               data-bind="text: pollName"
               href="#">
            </a>
          </td>
          <td>
            <span class="review-details-modal__history-compensations-table-date">
              <span data-bind="text: passedAtDate"></span><span data-bind="text: passedAtTime"></span>
            </span>
          </td>
          <td class="review-details-modal__history-compensations-table-compensation-name-cell"
              data-bind="text: compensationName"></td>
          <td class="review-details-modal__history-compensations-table-compensation-reason-cell"
              data-bind="text: compensationReason"></td>
        </tr>
        <!-- /ko -->
      </tbody>
    </table>
  </interactive-table>
</media-query>

<media-query params="query: 'mobile'">
  <interactive-table params="table: $compensationsTab.table">
    <table class="fixed-table f-color-text"
           style="min-width: 100%">
      <tbody>
        <tr>
          <th style="width: 120px" class="f-border-top" data-bind="text: _t('Опрос')"></th>
          <!-- ko foreach: items -->
          <td class="f-border-top" data-bind="click: function() { $compensationsTab.onReviewClick($data) }">
            <a class="review-details-modal__history-compensations-table-name"
               data-bind="text: pollName"
               href="#">
            </a>
          </td>
          <!-- /ko -->
        </tr>
        <tr>
          <th data-bind="text: _t('Пройден')"></th>
          <!-- ko foreach: items -->
          <td><span data-bind="text: passedAtDate"></span><span data-bind="text: passedAtTime"></span></td>
          <!-- /ko -->
        </tr>
        <tr>
          <th data-bind="text: _t('answers', 'Компенсация')"></th>
          <!-- ko foreach: items -->
          <td data-bind="text: compensation.name"></td>
          <!-- /ko -->
        </tr>
        <tr>
          <th data-bind="text: _t('answers', 'Причина')"></th>
          <!-- ko foreach: items -->
          <td data-bind="text: compensation.reason"></td>
          <!-- /ko -->
        </tr>


      </tbody>
    </table>
  </interactive-table>
</media-query>


<!-- /ko -->
