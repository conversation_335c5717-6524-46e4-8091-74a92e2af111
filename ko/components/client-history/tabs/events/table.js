import { InteractiveTable } from 'Models/interactive-table';
import { ApiUrl } from 'Utils/url/api-url';
import { HistoryEvent } from './event';

export class HistoryEventsTable extends InteractiveTable {
  constructor() {
    super();

    this.clientId = null;

    this.filters = {
      events: ko.observableArray([]),
      resources: ko.observableArray([]),
      period: ko.observable('')
    };
  }

  getParams() {
    let params = super.getParams();
    params.contactId = this.clientId;

    let [from, to] = utils.date.formatClientPeriod(
      this.filters.period(),
      'DD.MM.YYYY'
    );
    params = {
      ...params,
      events: this.filters.events(),
      resources: this.filters.resources()
    };

    if (from && to) {
      params.from = from;
      params.to = to;
    }

    return params;
  }

  resetFilters() {
    this.filters.events([]);
    this.filters.resources([]);
    this.filters.period('');

    super.reset();
  }

  applyFilters() {
    this.reset();
  }

  load() {
    return new Promise((res, rej) => {
      if (!this.beforeLoad()) {
        res();
        return;
      }

      this.xhr = $.ajax({
        url: ApiUrl('contact/events'),
        data: this.getParams(),
        success: (response) => {
          let events = response.items || [];
          this.afterLoad(events.map((o) => new HistoryEvent(o)));
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.onError();
          rej();
        }
      });
    });
  }
}
