import { HistoryTab } from '../history-tab';
import { periodRanges } from 'Legacy/data/period-picker-ranges';
import {
  ORDER_EVENT,
  VIEW_CATEGORY_EVENT,
  VIEW_PRODUCT_EVENT,
  eventNames,
  eventsList
} from 'Legacy/data/events';
import { HistoryEventsTable } from './table';
export class ViewModel extends HistoryTab {
  constructor(params, element) {
    super(new HistoryEventsTable(), params, element);

    this.directories = {
      resources: new Directory('external-resources')
    };
    Object.keys(this.directories).forEach((key) =>
      this.directories[key].load()
    );

    this.periodPickerRanges = periodRanges;

    this.events = [...eventsList];
    this.events.sort((a, b) => {
      return a.name < b.name ? -1 : 1;
    });
  }

  getEventName(eventType) {
    return eventNames[eventType];
  }

  isProductEvent(eventType) {
    return eventType == VIEW_PRODUCT_EVENT;
  }

  isCategoryEvent(eventType) {
    return eventType == VIEW_CATEGORY_EVENT;
  }

  isOrderEvent(eventType) {
    return eventType == ORDER_EVENT;
  }
}
