<!-- ko let: { $eventsTab: $component } -->


<div class="d-flex flex-wrap align-items-center container">
  <div class="f-filters-group">
    <div class="f-filters-group__filter">
      <div class="form-group dense-form-group">
        <label class="form-label" data-bind="text: _t('answers', 'Событие')"></label>

        <select data-bind="{
                selectedOptions: table.filters.events,
                lazySelect2: {
                  wrapperCssClass: 'select2-container--form-control',
                  dropdownCssClass: 'dense-form-group__dropdown',
                  containerCss: { 'min-width': '27px' },
                  placeholder: _t('answers', 'Все события')
                }
            }"
                multiple>
          <!-- ko foreach: events -->
          <option data-bind="value: value, text: name"></option>
          <!-- /ko -->
        </select>
      </div>
    </div>

    <!-- ko if: directories.resources.loaded -->
    <div class="f-filters-group__filter">
      <div class="form-group dense-form-group">
        <label class="form-label" data-bind="text: _t('answers', 'Ресурс')"></label>

        <select data-bind="{
                selectedOptions: table.filters.resources,
                lazySelect2: {
                  wrapperCssClass: 'select2-container--form-control',
                  dropdownCssClass: 'dense-form-group__dropdown',
                  containerCss: { 'min-width': '27px' },
                  placeholder: _t('answers', 'Все ресурсы')
                }
            }"
                multiple>
          <!-- ko foreach: directories.resources.data -->
          <option data-bind="value: endpoint_id, text: title"></option>
          <!-- /ko -->
        </select>
      </div>
    </div>
    <!-- /ko -->

    <div class="f-filters-group__filter">
      <div class="form-group dense-form-group">
        <label class="form-label" data-bind="text: _t('Период')"></label>
        <period-picker params="ranges: true, allowClear: true, value: table.filters.period"></period-picker>
        

      </div>
    </div>
  </div>
  <div class="ml-auto d-flex align-items-center">
    <button class="f-btn f-btn-link"
            type="button"
            data-bind="click: function() { table.resetFilters() }, text: _t('Сбросить')"></button>
    <button class="f-btn f-btn-success f-btn-lg ml-4"
            type="button"
            data-bind="click: function() { table.applyFilters() }, text: _t('Применить')"></button>
  </div>
</div>

<hr>

<div class="mt-4">
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $eventsTab.table">
      <table class="table f-table f-table--fixed f-table--outer">
        <thead>
          <tr>
            <th class="f-table__outer-element-head-cell"></th>
            <th data-bind="text: _t('answers', 'Событие')"></th>
            <th data-bind="text: _t('answers', 'Дата/время события')"></th>
            <th data-bind="text: _t('answers', 'Ресурс')"></th>
            <th></th>
            <th class="f-table__outer-element-head-cell"></th>
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: { data: items, as: 'eventData' } -->
          <!-- ko let: { isOpened: ko.observable(false) } -->
          <tr class="f-table__row"
              data-bind="css: {
              'f-table__row--selected': isOpened
            }">
            <td class="f-table__outer-element-cell"></td>
            <td valign="middle">
              <div class="d-flex align-items-center">
                <span class="font-weight-500"
                      data-bind="text: $eventsTab.getEventName(eventData.event)">
                </span>
                <!-- ko if: $eventsTab.isOrderEvent(eventData.event) -->
                <span class="f-icon f-icon-arrow--top ml-3 f-transform--transition cursor-pointer"
                      data-bind="
                    click: function() {
                      isOpened(!isOpened())
                    },
                    css: {
                      'f-transform-rotate-180': !isOpened()
                    }
                  ">
                  <svg>
                    <use href="#arrow-top-icon"></use>
                  </svg>
                </span>
                <!-- /ko -->
              </div>
            </td>
            <td valign="middle">
              <!-- ko text: eventData.date -->
              <!-- /ko -->
            </td>
            <td valign="middle">
              <!-- ko text: eventData.resource ? eventData.resource.title : '' -->
              <!-- /ko -->
            </td>
            <td valign="middle">
              <div class="d-flex w-100 align-items-center">
                <!-- ko if: $eventsTab.isProductEvent(eventData.event) -->
                <div class="nowrap overflow-hidden overflow-ellipsis">
                  <span class="f-color-service" data-bind="text: _t('answers', 'Товар') + ':'"></span>
                  <!-- ko if: eventData.product && eventData.product.url -->
                  <a target="_blank"
                     data-bind="text: eventData.product.name,
                              attr: {
                                href: eventData.product.url
                              }"></a>
                  <!-- /ko -->
                  <!-- ko if: eventData.product && !eventData.product.url -->
                  <span data-bind="text: eventData.product.name"></span>
                  <!-- /ko -->
                </div>
                <!-- ko if: eventData.product && eventData.product.url -->
                <a class="f-icon f-icon--outer-link f-icon-sm ml-2"
                   target="_blank"
                   data-bind="
                        attr: {
                          href: eventData.product.url
                        }">
                  <svg>
                    <use href="#outer-link-icon"></use>
                  </svg>
                </a>
                <!-- /ko -->
                <!-- /ko -->
                <!-- ko if: $eventsTab.isCategoryEvent(eventData.event) -->
                <div class="nowrap overflow-hidden overflow-ellipsis">
                  <span class="f-color-service" data-bind="text: _t('answers', 'Категория') + ':'">
                  </span>
                  <!-- ko if: eventData.category && eventData.category.url -->
                  <a data-bind="text: eventData.category.name,
                            attr: {
                              href: eventData.category.url
                            }"></a>
                  <!-- /ko -->
                  <!-- ko if: eventData.category && eventData.category.url -->
                  <span data-bind="text: eventData.category.name"></span>
                  <!-- /ko -->
                </div>
                <!-- ko if: eventData.category && eventData.category.url -->
                <a class="f-icon f-icon--outer-link f-icon-sm ml-2"
                   target="_blank"
                   data-bind="
                      attr: {
                        href: eventData.category.url
                      }">
                  <svg>
                    <use href="#outer-link-icon"></use>
                  </svg>
                </a>
                <!-- /ko -->
                <!-- /ko -->
              </div>
            </td>
            <td class="f-table__outer-element-cell"></td>
          </tr>
          <!-- ko if: $eventsTab.isOrderEvent(eventData.event) -->
          <tr>
            <td class="p-0 border-0"
                colspan="6">
              <!-- ko template: {
                  foreach: templateIf(isOpened(), $data),
                  afterAdd: slideAfterAddFactory(200),
                  beforeRemove: slideBeforeRemoveFactory(200)
                } -->
              <div style=""
                   data-bind="using0: eventData.order">
                <table class="f-table table">
                  <thead>
                    <tr>
                      <th>
                        <span class="f-color-primary bold f-fs-3">
                          <!-- ko text: _t('answers', 'Заказ №') -->
                          <!-- /ko -->
                          <!-- ko text: '2345808' -->
                          <!-- /ko -->
                        </span>
                      </th>
                      <th></th>
                      <th align="right">
                        <span class="nowrap f-fs-3 font-weight-500">
                          <!-- ko text: '658' -->
                          <!-- /ko -->
                          ₽
                        </span>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td valign="middle">
                        Сливочный с копченым лососем (запеченый)
                      </td>
                      <td valign="middle">x1</td>
                      <td valign="middle"
                          align="right">
                        <span class="nowrap">458 ₽</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <interactive-table params="table: $eventsTab.table">
      <table class="table f-table f-table--fixed f-table--outer">
        <tbody>
          <!-- ko foreach: { data: items, as: 'eventData' } -->
          <!-- ko let: { isOpened: ko.observable(false) } -->
          <tr class="f-table__row"
              data-bind="css: {
          'f-table__row--selected': isOpened
         }">
            <td valign="middle"
                width="50">
              <!-- ko if: $parent.isOrderEvent(eventData.event) -->
              <span class="f-icon f-icon-arrow--top ml-3 f-transform--transition cursor-pointer"
                    data-bind="
                click: function() {
                  isOpened(!isOpened())
                },
                css: {
                  'f-transform-rotate-180': !isOpened()
                }
              ">
                <svg>
                  <use href="#arrow-top-icon"></use>
                </svg>
              </span>
              <!-- /ko -->
            </td>
            <td>
              <div class="font-weight-500 f-color-primary"
                   data-bind="text: $parent.getEventName(eventData.event)">
              </div>
              <div>
                <!-- ko text: eventData.date -->
                <!-- /ko -->
              </div>
              <div>
                <!-- ko text: eventData.resource ? eventData.resource.title : '' -->
                <!-- /ko -->
              </div>
            </td>
          </tr>
          <!-- ko if: $parent.isOrderEvent(eventData.event) -->
          <tr>
            <td class="p-0 border-0"
                colspan="2">
              <!-- ko template: {
              foreach: templateIf(isOpened(), $data),
              afterAdd: slideAfterAddFactory(200),
              beforeRemove: slideBeforeRemoveFactory(200)
            } -->
              <div style=""
                   data-bind="using0: eventData.order">
                <table class="f-table table">
                  <thead>
                    <tr>
                      <th>
                        <span class="f-color-primary bold f-fs-3">
                          <!-- ko text: _t('answers', Заказ №) -->
                          <!-- /ko -->
                          <!-- ko text: '2345808' -->
                          <!-- /ko -->
                        </span>
                      </th>
                      <th></th>
                      <th align="right">
                        <span class="nowrap f-fs-3 font-weight-500">
                          <!-- ko text: '658' -->
                          <!-- /ko -->
                          ₽
                        </span>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td valign="middle">
                        Сливочный с копченым лососем (запеченый)
                      </td>
                      <td valign="middle">x1</td>
                      <td valign="middle"
                          align="right">
                        <span class="nowrap">458 ₽</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>
</div>

<!-- /ko -->
