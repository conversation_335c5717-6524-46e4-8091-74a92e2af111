export class HistoryEvent {
  constructor(data) {
    Object.keys(data).forEach(key => {
      this[key] = data[key];
    })
    // items.forEach((item) => {
    //   const data = {
    //     ...item,
    //     pollName: item.surveyName,
    //     filial: item.filialName,
    //     order: {
    //       number: item.orderNumber,
    //       sum: item.price,
    //       id: item.orderId,
    //     },
    //   };
    //   const model = ReviewModel.get(data, that.root);
    //   that.data.push(model);
    // });
  }
}
