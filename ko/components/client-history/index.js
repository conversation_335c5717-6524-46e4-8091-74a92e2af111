import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './tabs/orders';
import './tabs/events';
import './tabs/reviews';
import './tabs/compensations';
import './tabs/messages';

ko.components.register('foquz-client-history', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('foquz-client-history');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
