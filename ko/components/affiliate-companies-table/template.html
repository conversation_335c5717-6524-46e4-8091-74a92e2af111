<interactive-table
  params="table: table, emptyText: 'Компания пока никого не привела', hideBeforeInit: true"
>
  <table class="table f-table f-table--searchable">
    <thead>
      <tr>
        <th
          data-bind="component: {
          name: 'table-head-cell',
          params: {
            sort: sort,
            sortName: 'created_at',
            placeholder: '00.00.0000',
            withSearch: true,
            searchValue: search.created_at,
            onSearch: function() { reset(); }
          }
        }"
          width="170"
        >
          <!-- ko text: _t('Дата регистрации') -->
          <!-- /ko -->
        </th>
        <th
          data-bind="component: {
          name: 'table-head-cell',
          params: {
            sort: sort,
            sortName: 'name',
            placeholder: 'Все',
            withSearch: true,
            searchValue: search.name,
            onSearch: function() { reset(); }
          }
        }"
          width="315"
        >
          Название компании
        </th>
        <th
          data-bind="component: {
          name: 'table-head-cell',
          params: {
            sort: sort,
            sortName: 'tariff',
            placeholder: 'Все',
            withSearch: true,
            searchValue: search.tariff,
            onSearch: function() { reset(); }
          }
        }"
        >
          Текущий тариф
        </th>
      </tr>
    </thead>
    <tbody>
      <!-- ko foreach: { data: items, as: 'item' } -->
      <tr class="f-fs-2">
        <td data-bind="text: item.createdAt"></td>
        <td data-bind="text: item.name"></td>
        <td>
          <a
            href="javascript:void(0)"
            data-bind="text: item.tariffName, click: function() {
            $parent.openHistory(item)
          }"
          ></a>
        </td>
      </tr>
      <!-- /ko -->
    </tbody>
  </table>
</interactive-table>

<dialogs-container params="ref: table.dialogs"></dialogs-container>
