import { formatters } from './formatters';

import { clientLinkFormatter } from './link-formatter';
import { clientMaskFormatter } from './mask-formatter';
import { questionPreviewFormatter } from './preview-formatter';

export function questionDirectPreviewFormatter(data) {

  let questionType = data.main_question_type;
  let countAnswers = data.countAnswers;
  let isAuto = data.poll_is_auto;
  let required = data.is_required == 1;
  let alias = data.service_name;
  let isSystem = data.is_system;
  let maskType = data.mask;
  let dontShowIfAnswered = data.dont_show_if_answered;

  let answersFrom = '';
  let answersFromDate = data.answers_from;
  if (answersFromDate) {
    answersFromDate = moment(answersFromDate, 'YYYY-MM-DD HH:mm');
    if (answersFromDate.isValid())
      answersFrom = answersFromDate.format('DD.MM.YYYY HH:mm');
  }

  let commonData = {
    type: questionType,
    pointId: data.point_id,
    id: data.id,
    countAnswers,
    isAuto,
    pollIsAuto: isAuto,
    required,
    alias,
    name: data.name,
    description: data.description,
    sub_description: data.sub_description,
    isSource: data.is_source, // TODO
    isUpdated: data.is_updated, // TODO
    isTmp: data.is_tmp,
    isSystem,
    dontShowIfAnswered: parseInt(dontShowIfAnswered),
    answersFrom,
  };

  let result = {
    ...commonData,
    maskType: maskType || 0,
    maskConfig: clientMaskFormatter(data.maskConfig),

    ...clientLinkFormatter(data),
  };

  result = {
    ...result,
    ...formatters[questionType].client(data)
  }
  console.log('result', result)
  result = questionPreviewFormatter(result);


  return result;
}
