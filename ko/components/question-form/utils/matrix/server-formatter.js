export default function (data) {
  const question = {
    comment_enabled: data.matrixCommentEnabled ? 1 : 0,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    placeholder_text: data.matrixCommentPlaceholder,
    comment_minlength: data.matrixCommentLengthRange[0],
    comment_maxlength: data.matrixCommentLengthRange[1],
    comment_label: data.matrixCommentLabel,
    enableGallery: data.matrixGalleryEnabled,
    max_points_calc_method: data.maxPointsCalcMethod ? 1 : 0,
    max_points_calc_recipient: data.maxPointsCalcRecipient ? 1 : 0,
    gallery: data.matrixGallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.matrixGallery.filter((v) => v.mediaId).map((v) => v.mediaId),
    matrixSettings: data.matrix,
    random_variants_order: data.randomOrder ? 1 : 0,
    dropdown_variants: data.dropdown ? 1 : 0,
    select_placeholder_text: data.selectPlaceholder,
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    skip_variant: data.skipVariant ? 1 : 0,
    multiple_choice: data.multipleChoice, 
  };

  if (data.useDonor) {
    question.donor = data.donorId;
    question.donor_chosen = data.donorVariantsType == 1 ? 1 : 0;
  } else {
    question.donor = '';
  }

  let clarifyingQuestion = data.clarifyingQuestion;
  if (clarifyingQuestion.enabled) {
    question.detail_question = clarifyingQuestion.text;
    question.for_all_rates = clarifyingQuestion.forAllRates ? 1 : 0;

    question.variants_element_type = clarifyingQuestion.variantsType;

    question.is_self_answer = clarifyingQuestion.customAnswerEnabled ? 1 : 0;
    let customAnswerRange = clarifyingQuestion.customAnswerRange;
    question.comment_minlength = customAnswerRange[0];
    question.comment_maxlength = customAnswerRange[1];
    question.placeholder_text = clarifyingQuestion.customAnswerPlaceholder;
    question.self_variant_text = clarifyingQuestion.customAnswerLabel;
    question.extra_required = clarifyingQuestion.required ? 1 : 0;
  } else {
    question.detail_question = '';
  }

  console.log(question)

  return question;
}
