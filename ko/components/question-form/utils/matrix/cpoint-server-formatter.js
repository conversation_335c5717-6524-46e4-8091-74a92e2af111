export default function (data) {
  let question = {
    commentEnabled: data.matrixCommentEnabled ? 1 : 0,
    placeholderText: data.matrixCommentPlaceholder,
    commentLengthRange: data.matrixCommentLengthRange,
    comment_label: data.matrixCommentLabel,
    enableGallery: data.matrixGalleryEnabled ? 1 : 0,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    gallery: data.matrixGallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.matrixGallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    matrixSettings: data.matrix,
    random_variants_order: data.randomOrder ? 1 : 0,
    dropdown_variants: data.dropdown ? 1 : 0,
    select_placeholder_text: data.selectPlaceholder,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    skip_variant: data.skipVariant ? 1 : 0,
    multiple_choice: data.multipleChoice,
  };
  
  let clarifyingQuestion = data.clarifyingQuestion;


  if (clarifyingQuestion.enabled) {
    question.clarifyingQuestionEnabled = true;
    question.clarifyingQuestion = clarifyingQuestion.text;
    question.forAllRates = !!clarifyingQuestion.forAllRates;

    question.clarifyingQuestionVariantsType = clarifyingQuestion.variantsType;
    question.variantsType = clarifyingQuestion.variantsType;
    question.variants = clarifyingQuestion.variants.map(v => {
      return {
        id: v.id,
        variant: v.value,
        extra_question: 1,
      }
    });
    question.customClarifyingQuestionAnswerLengthRange =
    clarifyingQuestion.customAnswerRange;
    question.placeholderText =
    clarifyingQuestion.customAnswerPlaceholder;
    question.self_variant_text = clarifyingQuestion.customAnswerLabel;

    question.customClarifyingQuestionAnswerAvailable = clarifyingQuestion.customAnswerEnabled;
    question.customVariantLengthRange = clarifyingQuestion.customAnswerRange;
    question.commentLengthRange = clarifyingQuestion.customAnswerRange;
    question.extra_required = clarifyingQuestion.required ? 1 : 0;
  }

  return question;
}
