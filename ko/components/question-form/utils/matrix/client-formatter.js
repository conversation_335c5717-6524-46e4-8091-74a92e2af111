import { get as _get } from 'lodash';

export default function(data, mode) {

  let enableComment = mode === "cpoint" ? data.commentEnabled : data.comment_enabled;
  let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: 'position' in v ? v.position : i,
    };
  });

  gallery.sort((a,b) => {
    return a.position - b.position;
  });

  let variants = (mode === "cpoint" ? data.variants : data.detail_answers)
    .filter((v) => !v.is_deleted)
    .map((v) => {
      return {
        id: v.id,
        donorId: v.question_detail_id,
        value: v.variant || v.question,
        points: v.points,
        needExtra: v.need_extra,
        extra_question: v.extra_question,
      };
    });

  let detailQuestion = data.detail_question;
  let variantsType = data.variants_element_type;
  let enableSelfAnswer = parseInt(data.is_self_answer) == 1;
  let clarifyingQuestion = {
    enabled: mode === 'cpoint' ?
      data.clarifyingQuestionEnabled :
      data.isHaveExtra,
    forAllRates: mode === 'cpoint' ? data.forAllRates : data.for_all_rates,
    text: (mode === 'cpoint' ? data.clarifyingQuestion : detailQuestion) || '',
    variantsType:
      mode === 'cpoint'
        ? parseInt(data.clarifyingQuestionVariantsType)
        : variantsType,
    variants:
      mode === 'cpoint'
        ? data.clarifyingQuestionAnswers.map((v) => {
            return {
              id: v.id,
              value: v.variant,
            };
          })
        : variants.filter(el => el.extra_question),
    customAnswerEnabled:
      mode === 'cpoint'
        ? data.customClarifyingQuestionAnswerAvailable
        : enableSelfAnswer,
    customAnswerLengthRange: commentLengthRange,
    customAnswerPlaceholder: placeholder,
    customAnswerLabel:  data.self_variant_text || '',
    required: data.extra_required,
  };

  return {
    matrixGallery: gallery,
    matrixGalleryEnabled: gallery.length > 0,

    matrixCommentEnabled: enableComment,
    matrixCommentLengthRange: commentLengthRange,
    matrixCommentPlaceholder: placeholder,
    matrixCommentLabel: data.comment_label,
    matrixCommentRequired: data.comment_required,

    matrix: data.matrixSettings,

    randomOrder: data.random_variants_order,
    dropdown:
      mode === "cpoint" ? data.dropdownVariants : data.dropdown_variants,
    selectPlaceholder: mode === "cpoint" ? data.selectPlaceholderText || '' : data.select_placeholder_text || '',

    donorId: data.donor,
    donorVariantsType: data.donor_chosen === 1 ? 1 : 0,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',
    skipVariant: data.skip_variant ? 1 : 0,
    clarifyingQuestion,
    multipleChoice: data.multiple_choice,
    maxPointsCalcMethod: data.max_points_calc_method,
    maxPointsCalcRecipient: data.max_points_calc_recipient,
  };
}
