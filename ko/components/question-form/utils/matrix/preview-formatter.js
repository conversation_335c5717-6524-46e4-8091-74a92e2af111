export default function (data, type) {
  let previewData = {
    commentEnabled: data.matrixCommentEnabled ? 1 : 0,
    isHaveComment: data.matrixCommentEnabled ? 1 : 0, 
    textFieldParam: {
      min: data.matrixCommentLengthRange[0],
      max: data.matrixCommentLengthRange[1]
    },
    placeholderText: data.matrixCommentPlaceholder,
    comment_label: data.matrixCommentLabel,
    comment_required: data.galleryCommentRequaired,

    enableGallery: data.matrixGalleryEnabled ? 1 : 0,
    gallery: data.matrixGallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description
      };
    }),

    random_variants_order: data.randomOrder ? 1 : 0,
    dropdownVariants: data.dropdown,
    selectPlaceholder: data.selectPlaceholder,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    skip_variant: data.skipVariant ? 1 : 0,
    clarifyingQuestion: data.clarifyingQuestion,
  };

  const isVuePreview = type === 'vue';

  let matrix = data.matrix;
  if (data.randomOrder) {
    let order = matrix.rows.map((_, i) => i);
    order = _.shuffle(order);

    let rows = [];
    let points = [];

    order.forEach((i) => {
      rows.push(matrix.rows[i]);
      points.push(matrix.points[i]);
    });

    matrix.rows = rows;
    matrix.points = points;
  }

  previewData.matrixSettings = matrix;

  if (isVuePreview && data.isDonorClassifierWithListType) {
    previewData.matrixSettings.rows = data.matrix.rows.map((v) => {
      const val = v || ''
      return val.split(' / ').pop()
    })
  }

  let clarifyingQuestion = data.clarifyingQuestion;
  if (clarifyingQuestion.enabled) {
    previewData.forAllRates = clarifyingQuestion.forAllRates ? 1 : 0;
    previewData.answerText = clarifyingQuestion.text;
    previewData.variantsType = clarifyingQuestion.variantsType;
    previewData.isHaveCustomField = clarifyingQuestion.customAnswerEnabled;
    let range =
      clarifyingQuestion.customAnswerRange ||
      clarifyingQuestion.customAnswerLengthRange;
    previewData.textFieldParam = {
      min: range[0],
      max: range[1],
    };
    previewData.variants = clarifyingQuestion.variants.filter((v) => v.value).map((v, i) => {
      return {
        ...v,
        id: i + 1
      }
    });
    previewData.placeholderText = clarifyingQuestion.customAnswerPlaceholder;
    previewData.self_variant_text = clarifyingQuestion.customAnswerLabel;
    previewData.isHaveExtra = true;
  }

  return previewData;
}
