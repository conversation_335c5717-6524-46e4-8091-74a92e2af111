export default function (data) {

  let question = {
    comment_enabled: data.galleryCommentEnabled ? 1 : 0,
    placeholder_text: data.galleryCommentPlaceholder,
    comment_minlength: data.galleryCommentLengthRange[0],
    comment_maxlength: data.galleryCommentLengthRange[1],
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,

    enableGallery: data.galleryEnabled,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    starOptions: data.starsConfig,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    skip_variant: data.skipVariant ? 1 : 0,
  };

  let clarifyingQuestion = data.clarifyingQuestion;

  if (clarifyingQuestion.enabled) {
    question.detail_question = clarifyingQuestion.text;
    question.for_all_rates = clarifyingQuestion.forAllRates ? 1 : 0;

    question.variants_element_type = clarifyingQuestion.variantsType;

    question.is_self_answer = clarifyingQuestion.customAnswerEnabled ? 1 : 0;
    let customAnswerRange = clarifyingQuestion.customAnswerRange;
    question.comment_minlength = customAnswerRange[0];
    question.comment_maxlength = customAnswerRange[1];
    question.placeholder_text = clarifyingQuestion.customAnswerPlaceholder;
    question.self_variant_text = clarifyingQuestion.customAnswerLabel;
    question.extra_required = clarifyingQuestion.required ? 1 : 0;
  } else {
    question.detail_question = '';
  }

  question.random_variants_order = data.randomOrder ? 1 : 0;

  if (data.useDonor) {
    question.donor = data.donorId;
    question.donor_chosen = data.donorVariantsType == 1 ? 1 : 0;
  } else {
    question.donor = null;
  }

  return question;
}
