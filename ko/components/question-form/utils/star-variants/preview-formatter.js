export default function (data, type) {
  let previewData = {
    commentEnabled: data.galleryCommentEnabled ? 1 : 0,
    isHaveComment: data.galleryCommentEnabled ? 1 : 0,
    textFieldParam: {
      min: data.galleryCommentLengthRange[0],
      max: data.galleryCommentLengthRange[1],
    },
    placeholderText: data.galleryCommentPlaceholder,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired,

    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    starRatingOptions: {
      color: data.starsConfig.color,
      size: data.starsConfig.size,
      count: data.starsConfig.count,
      labelsArray: data.starsConfig.labels,
    },

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    skip_variant: data.skipVariant ? 1 : 0,
  };

  let clarifyingQuestion = data.clarifyingQuestion;

  previewData.isHaveExtra = clarifyingQuestion.enabled;
  if (clarifyingQuestion.enabled) {
    previewData.forAllRates = clarifyingQuestion.forAllRates ? 1 : 0;
    previewData.answerText = clarifyingQuestion.text;
    previewData.variantsType = clarifyingQuestion.variantsType;
    previewData.isHaveCustomField = clarifyingQuestion.customAnswerEnabled;
    let range =
      clarifyingQuestion.customAnswerRange ||
      clarifyingQuestion.customAnswerLengthRange;
    previewData.textFieldParam = {
      min: range[0],
      max: range[1],
    };
    previewData.variants = clarifyingQuestion.variants.filter((v) => v.value).map((v, i) => {
      return {
        ...v,
        id: i + 1
      }
    });
    previewData.placeholderText = clarifyingQuestion.customAnswerPlaceholder;
    previewData.self_variant_text = clarifyingQuestion.customAnswerLabel;
    previewData.starRatingOptions.extra_question_rate_from = data.starsConfig.extra_question_rate_from;
    previewData.starRatingOptions.extra_question_rate_to = data.starsConfig.extra_question_rate_to;
    previewData.clarifyingQuestionRequired = clarifyingQuestion.required;
  }

  previewData.random_variants_order = data.randomOrder ? 1 : 0;

  const isVuePreview = type === 'vue'

  let variants = data.variants
    .map((v) => {
      const res = {
        extra_question: 0,

        //@NOTE: need_extra приходит в качестве функции, для превью на Vue
        // мы используем postMessage, а он не поддерживает функции
        need_extra: type === 'vue' ? ko.unwrap(v.needExtra) : v.needExtra,
        persistentId: v.persistentId || v.id,
        id: Math.random(1, 999),
        value: v.value,
        points: v.points,
      }

      if (isVuePreview && data.isDonorClassifierWithListType) {
        const val = ko.unwrap(v.value)
        res.value = typeof val === 'string' ? val.split(' / ').pop() : ''
      }

      return res
    })
    .filter((v) => v.value);

  if (data.randomOrder) {
    variants = _.shuffle(variants);
  }

  variants = [
    ...variants,
    ...clarifyingQuestion.variants
      .map((v) => {
        return {
          extra_question: 1,

          //@NOTE: need_extra приходит в качестве функции, для превью на Vue
          // мы используем postMessage, а он не поддерживает функции
          need_extra: type === 'vue' ? ko.unwrap(v.needExtra) : v.needExtra,
          id: Math.random(1, 999),
          persistentId: v.persistentId || v.id,
          value: v.value,
          points: v.points,
        };
      })
      .filter((v) => v.value),
  ];

  previewData.variants = variants;
  previewData.detail_answers = previewData.variants.map((v) => ({
    id: v.persistentId || v.id || unique++,
    variant: v.value,
    question: v.value,
    position: v.position,
  }));

  return previewData;
}
