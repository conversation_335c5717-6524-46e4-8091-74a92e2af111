import { get as _get } from 'lodash';

export default function (data) {

  let question = {
    commentEnabled: data.galleryCommentEnabled ? 1 : 0,
    placeholderText: data.galleryCommentPlaceholder,
    commentLengthRange: data.galleryCommentLengthRange,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    starOptions: data.starsConfig,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    skip_variant: data.skipVariant ? 1 : 0,
    variants: data.variants.map((v) => {
      return {
        id: v.id,
        variant: v.value,
        extra_question: 0,
        need_extra: _get(v, 'needExtra', () => {})() ? 1 : 0,
      };
    }),
  };

  let clarifyingQuestion = data.clarifyingQuestion;

  if (clarifyingQuestion.enabled) {
    question.clarifyingQuestionEnabled = true;
    question.clarifyingQuestion = clarifyingQuestion.text;
    question.forAllRates = !!clarifyingQuestion.forAllRates;

    question.clarifyingQuestionVariantsType = clarifyingQuestion.variantsType;
    question.variantsType = clarifyingQuestion.variantsType;
    question.variants = [
      ...question.variants,
      ...clarifyingQuestion.variants.map(v => {
        return {
          id: v.id,
          variant: v.value,
          extra_question: 1,
        }
      }),
    ];
    question.customClarifyingQuestionAnswerLengthRange =
    clarifyingQuestion.customAnswerRange;
    question.placeholderText =
    clarifyingQuestion.customAnswerPlaceholder;
    question.self_variant_text = clarifyingQuestion.customAnswerLabel;

    question.customClarifyingQuestionAnswerAvailable = clarifyingQuestion.customAnswerEnabled;
    question.customVariantLengthRange = clarifyingQuestion.customAnswerRange;
    question.commentLengthRange = clarifyingQuestion.customAnswerRange;
    question.extra_required = clarifyingQuestion.required ? 1 : 0;
  }

  question.random_variants_order = data.randomOrder ? 1 : 0;

  return question;
}
