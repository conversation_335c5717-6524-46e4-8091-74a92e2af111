export default function (data, mode) {
  let placeholder = data.placeholderText;

  let regions =
    mode === "cpoint" ? data.regionsValues : data.addressCodes.regions;
  let cities = mode === "cpoint" ? data.cityValues : data.addressCodes.cities;
  let districts =
    mode === "cpoint" ? data.districtValues : data.addressCodes.districts;
  let streets =
    mode === "cpoint" ? data.streetValues : data.addressCodes.streets;

  return {
    regions,
    cities,
    districts,
    streets,
    addressFieldPlaceholder: placeholder,
  };
}
