import { serverLinkFormatter } from '../link-formatter'

export default function (data) {
  let question = {};

  question.regionsValues = data.regions;
  question.districtValues = data.districts;
  question.cityValues = data.cities;
  question.streetValues = data.streets;

  question.placeholder_text = data.addressFieldPlaceholder;

  question = {
    ...question,
    ...serverLinkFormatter(data),
  };

  return question;
}
