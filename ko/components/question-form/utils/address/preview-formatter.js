import { previewLinkFormatter } from '../link-formatter';

export default function (data) {
  let previewData = {};

  previewData.arRegionsIDs = data.regions.map(r => r.id);
  previewData.arDistrictsIDs = data.districts.map(d => d.id);
  previewData.arCitiesIDs = data.cities.map(c => c.id);
  previewData.arStreetsIDs = data.streets.map(s => s.id);

  previewData.placeholderText = data.addressFieldPlaceholder;

  previewData = {
    ...previewData,
    ...previewLinkFormatter(data)
  };

  return previewData;
}
