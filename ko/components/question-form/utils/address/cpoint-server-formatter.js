import { cPointServerLinkFormatter } from '../link-formatter'

export default function (data) {
  let question = {};

  question.regionsValues = data.regions;
  question.districtValues = data.districts;
  question.cityValues = data.cities;
  question.streetValues = data.streets;

  question.placeholderText = data.addressFieldPlaceholder;

  question = {
    ...question,
    ...cPointServerLinkFormatter(data),
  };

  return question;
}
