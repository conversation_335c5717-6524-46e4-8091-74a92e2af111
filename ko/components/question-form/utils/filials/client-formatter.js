export default function (data, mode) {

  let enableComment =
    mode === "cpoint"
      ? data.commentEnabled
      : data.comment_enabled || data.is_self_answer;
  let commentLengthRange =
    mode === "cpoint"
      ? data.commentLengthRange
      : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;
  let listType = data.dictionary_list_type;

  let filials = [];

  const questionType = mode === "cpoint" ? data.type : data.main_question_type;
  const filialsData =
    mode === "cpoint" ? data.clarifyingQuestion : data.detail_question;

  if (questionType === 17 && filialsData) {
    try {
      const parsedFilials = JSON.parse(filialsData);
     
      if (Array.isArray(parsedFilials)) {
        const filialsSet = new Set(parsedFilials);
        filials = [...filialsSet];
      }
    } catch (e) {
      console.error(e);
    }
  }

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  let sortType = data.dictionary_sort;

  return {
    gallery: gallery,
    galleryEnabled: gallery.length > 0,

    galleryCommentEnabled: enableComment,
    galleryCommentLengthRange: commentLengthRange,
    galleryCommentPlaceholder: placeholder,
    galleryCommentLabel: data.comment_label,
    galleryCommentRequired: data.comment_required,
    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',
    sortType,

    filials: filials,

    listType: listType,

    dropdown:
      mode === "cpoint" ? data.dropdownVariants : data.dropdown_variants,
    randomOrder: data.random_variants_order,
  };
}
