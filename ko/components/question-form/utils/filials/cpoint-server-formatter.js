export default function (data) {
  let question = {
    commentEnabled: !!data.galleryCommentEnabled,
    placeholderText: data.galleryCommentPlaceholder,
    commentLengthRange: data.galleryCommentLengthRange,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),
  };

  question.filials = data.filials;

  question.dropdown_variants = data.dropdown ? 1 : 0;

  question.random_variants_order = data.randomOrder ? 1 : 0;

  return question;
}
