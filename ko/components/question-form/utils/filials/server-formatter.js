export default function (data) {

  let question = {
    dictionary_list_type: data.listType,
    comment_enabled: data.galleryCommentEnabled ? 1 : 0,
    placeholder_text: data.galleryCommentPlaceholder,
    comment_label: data.galleryCommentLabel,
    comment_minlength: data.galleryCommentLengthRange[0],
    comment_maxlength: data.galleryCommentLengthRange[1],
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.galleryEnabled,
    dictionary_sort: data.dictionarySort,
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    variants_element_type: 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),
  };

  question.filials = data.filials;

  question.dropdown_variants = data.dropdown ? 1 : 0;

  question.random_variants_order = data.randomOrder ? 1 : 0;

  return question;
}
