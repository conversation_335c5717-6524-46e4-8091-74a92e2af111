export default function (data) {
  
  let previewData = {
    commentEnabled: data.galleryCommentEnabled ? 1 : 0,
    show_tooltips: data.showTooltips ? 1 : 0,
    textFieldParam: {
      min: data.galleryCommentLengthRange[0],
      max: data.galleryCommentLengthRange[1],
    },
    placeholderText: data.galleryCommentPlaceholder,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired,

    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        url: v.url,
        persistentId: v.persistentId,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    dropdownVariants: data.dropdown,
    variantsType: data.variantType,
    max_choose_variants: data.maxVariantsCount,
    selectedDictionaryTree: getSelectedDictionaryTree(data.filialsList, data.filials),
    dictionary_list_type: data.listType,
    dictionary_sort: data.dictionarySort,
    disable_select_category: data.disableSelectCategory,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
  };

  return previewData;
}

function getSelectedDictionaryTree(filialsList, selectedFilials) {
  const dictionaryTree = {};
  let selected = [...(selectedFilials || [])];

  const isSelected = (id) => {
    const isSelected = !!selected.find(selectedId => String(id) === String(selectedId));
    if (isSelected) {
      selected = selected.filter(selectedId => String(id) !== String(selectedId))
    }
    return isSelected;
  }

  (filialsList || []).forEach(({ category, items }) => {
    const { name, id } = category;
    if (id === 0) {
      items.forEach(({ id, name }) => {
        if (!isSelected(id)) return;
    
        dictionaryTree[name] = {
          description: name,
          title: name,
          isCategory: false,
          id,
          position: 0,
          langs: null,
        };
      })
    } else {  
      const children = [];
      items.forEach(({ id, name }) => {
        if (!isSelected(id)) return;
    
        children.push({
          description: name,
          title: name,
          isCategory: false,
          id,
          position: 0,
          langs: null,
        });
      })

      if (!children.length) return;

      dictionaryTree[name] = {
        description: name,
        title: name,
        isCategory: true,
        children,
        id,
        position: 0,
        langs: null,
      };
    }
  })

  return dictionaryTree;
}

