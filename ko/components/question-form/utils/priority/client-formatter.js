export default function (data, mode) {
  let commentEnabled = mode === "cpoint" ? data.commentEnabled : data.comment_enabled || data.is_self_answer;
  let commentLengthRange = mode === "cpoint" ? data.customVariantLengthRange : [data.comment_minlength, data.comment_maxlength];
  let commentPlaceholder = data.placeholderText;

  let variants = (mode === "cpoint" ? data.variants : data.detail_answers).filter((v) => !v.is_deleted).map(
    (v) => {
      return {
        id: v.id,
        donorId: v.question_detail_id,
        value: v.variant,
        points: v.points,
        needExtra: v.need_extra,
        dictionary_element_id: v.dictionary_element_id,
      };
    }
  );

  let rightAnswer = null;
  if (data.rightAnswer) {
    rightAnswer = {
      answer: data.rightAnswer.decodedAnswer,
      points: data.rightAnswer.points,
    };
  }

  let randomOrder = false;
  if (data.random_variants_order) {
    randomOrder = data.random_variants_order;
  }

  let needReorder = false;
  if ("reorder_required" in data) {
    needReorder = data.reorder_required;
  } else {
    let prioritySettings = data.foquzQuestionPrioritySettings || {};
    needReorder = prioritySettings.reorder_required;
  }

  return {
    priorityCommentEnabled: commentEnabled,
    priorityCommentLengthRange: commentLengthRange,
    priorityCommentPlaceholder: commentPlaceholder,
    priorityCommentLabel: data.comment_label,
    priorityCommentRequired: data.comment_required,
    
    variants,
    rightAnswer,
    needReorder,
    randomOrder,

    donorId: data.donor,
    donorVariantsType: data.donor_chosen === 1 ? 1 : 0,
    maxPointsCalcMethod: data.max_points_calc_method,
  };
}
