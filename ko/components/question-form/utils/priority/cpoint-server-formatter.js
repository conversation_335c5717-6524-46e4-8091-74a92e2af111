export default function (data) {
  let question = {
    variants: data.variants.map(v => {
      return {
        id: v.id,
        variant: v.value
      }
    }),
    rightAnswer: {
      answer: data.variants.map(v => v.value),
      points: data.rightAnswerPoints
    },
    random_variants_order: data.randomOrder ? 1 : 0,
    reorder_required: data.needReorder ? 1 : 0,

    commentEnabled: data.priorityCommentEnabled ? 1 : 0,
    placeholderText: data.priorityCommentPlaceholder,
    commentLengthRange: data.priorityCommentLengthRange,
    comment_label: data.priorityCommentLabel,
    comment_required: data.priorityCommentRequired ? 1 : 0,
  };
  return question;
}
