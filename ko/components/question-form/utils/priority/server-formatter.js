export default function (data) {

  let question = {
    comment_enabled: data.priorityCommentEnabled ? 1 : 0,
    placeholder_text: data.priorityCommentPlaceholder,
    comment_minlength: data.priorityCommentLengthRange[0],
    comment_maxlength: data.priorityCommentLengthRange[1],
    comment_label: data.priorityCommentLabel,
    comment_required: data.priorityCommentRequired ? 1 : 0,
    max_points_calc_method: data.maxPointsCalcMethod ? 1 : 0,
    rightAnswer: {
      answer: data.variants.map(v => v.value),
      points: data.rightAnswerPoints
    },
    reorder_required: data.needReorder ? 1 : 0,
    random_variants_order: data.randomOrder ? 1 : 0,
  };

  if (data.useDonor) {
    question.donor = data.donorId;
    question.donor_chosen = data.donorVariantsType == 1 ? 1 : 0;
  } else {
    question.donor = '';
  }
  
  return question;
}
