let unique = 1;

export default function (data, type) {
  const isVuePreview = type === 'vue'

  let previewData = {
    reorder_required: data.needReorder ? 1 : 0,
    commentEnabled: data.priorityCommentEnabled ? 1 : 0,
    textFieldParam: {
      min: data.priorityCommentLengthRange && data.priorityCommentLengthRange[0],
      max: data.priorityCommentLengthRange && data.priorityCommentLengthRange[1],
    },
    placeholderText: data.priorityCommentPlaceholder,
    comment_label: data.priorityCommentLabel,
    comment_required: data.priorityCommentRequired,
    randomOrder: data.randomOrder,
  };

  previewData.variants = data.variants.map((v) => {
    const id = v.persistentId || v.id || unique++

    const val = v.value
    return {
      id: unique++,
      persistentId: id,
      recipientId: v.recipientId,
      value: isVuePreview && data.isDonorClassifierWithListType ? val.split(' / ').pop() : val,
    };
  });

  previewData.rightAnswer = {
    answer: data.variants.map((v) => v.value),
    points: data.rightAnswerPoints,
  };

  previewData.detail_answers = previewData.variants.map((v) => ({
    id: v.persistentId || v.id || unique++,
    variant: v.value,
    position: v.position,
  }));

  return previewData;
}
