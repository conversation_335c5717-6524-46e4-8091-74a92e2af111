export function serverLinkFormatter(data) {
  let previewData = {};
  previewData.link_with_client_field = data.linkWithClientField ? 1 : 0;
  previewData.linked_client_field = data.linkedField;
  previewData.rewrite_linked_field = data.rewriteExistLink ? 1 : 0;
  return previewData;
}

export function cPointServerLinkFormatter(data) {
  let previewData = {};
  previewData.linkWithClientField = data.linkWithClientField ? 1 : 0;
  previewData.linkedClientField = data.linkedField;
  previewData.rewriteLinkedField = data.rewriteExistLink ? 1 : 0;
  return previewData;
}

export function previewLinkFormatter(data) {
  let previewData = {};
  previewData.linkWithClientField = data.linkWithClientField;
  previewData.linkedClientField = data.linkedField;
  previewData.rewriteLinkedField = data.rewriteExistLink;
  return previewData;
}

export function clientLinkFormatter(data, mode) {
  return {
    linkWith<PERSON>lientField: parseInt(mode === "cpoint" ? data.linkWithClientField : data.link_with_client_field),
    linkedField: mode === "cpoint" ? data.linkedClientField : data.linked_client_field,
    rewriteExistLink: parseInt(mode === "cpoint" ?  data.rewriteLinkedField : data.rewrite_linked_field),
  };
}
