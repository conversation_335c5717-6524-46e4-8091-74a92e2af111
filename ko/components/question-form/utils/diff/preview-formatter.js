export default function (data) {

  let previewData = {
    isHaveComment: data.diffCommentEnabled ? 1 : 0,
    textFieldParam: {
      min: data.diffCommentLengthRange[0],
      max: data.diffCommentLengthRange[1],
    },
    placeholderText: data.diffCommentPlaceholder,
    comment_label: data.diffCommentLabel,
    comment_required: data.galleryCommentRequaired,

    enableGallery: data.diffGalleryEnabled ? 1 : 0,
    gallery: data.diffGallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    differentialRows: data.diffRows.map((row, i) => {
      return {
        ...row,
        id: i + 1,
        persistentId: row.id || row.persistentId,
      }
    }),

    semDifSetting: {
      form: data.shape,
      start_point_color: data.startColor,
      end_point_color: data.endColor,
    },

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
  };

  return previewData;
}
