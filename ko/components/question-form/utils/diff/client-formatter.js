export default function (data, mode) {
  let enableComment =
    mode === 'cpoint'
      ? data.commentEnabled
      : data.comment_enabled || data.is_self_answer;
  let commentLengthRange =
    mode === 'cpoint'
      ? data.commentLengthRange
      : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: 'position' in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  let settings = data.semDifSetting || {};

  return {
    diffGallery: gallery,
    diffGalleryEnabled: gallery.length > 0,

    diffCommentEnabled: enableComment,
    diffCommentLengthRange: commentLengthRange,
    diffCommentPlaceholder: placeholder,
    diffCommentLabel: data.comment_label,
    diffCommentRequired: data.comment_required,

    diffRows: data.differentialRows || [],

    shape: settings.form,
    startColor: settings.start_point_color,
    endColor: settings.end_point_color,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',
  };
}
