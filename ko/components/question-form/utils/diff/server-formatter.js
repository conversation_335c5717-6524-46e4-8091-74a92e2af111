export default function (data) {

  return {
    comment_enabled: data.diffCommentEnabled ? 1 : 0,
    placeholder_text: data.diffCommentPlaceholder,
    comment_minlength: data.diffCommentLengthRange[0],
    comment_maxlength: data.diffCommentLengthRange[1],
    comment_label: data.diffCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.diffGalleryEnabled,
    gallery: data.diffGallery.filter(v => v.mediaId).map((v) => {
      return {
        id: v.mediaId,
        description: v.description,
      };
    }),
    files: data.diffGallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    differentialRows: data.diffRows,
    form: data.shape,
    start_point_color: data.startColor,
    end_point_color: data.endColor,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
  };
}
