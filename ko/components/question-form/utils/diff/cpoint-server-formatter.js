export default function (data) {
  return {
    commentEnabled: data.diffCommentEnabled ? 1 : 0,
    placeholderText: data.diffCommentPlaceholder,
    commentLengthRange: data.diffCommentLengthRange,
    comment_label: data.diffCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.diffGalleryEnabled ? 1 : 0,
    gallery: data.diffGallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.diffGallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    differentialRows: data.diffRows,
    form: data.shape,
    start_point_color: data.startColor,
    end_point_color: data.endColor,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
  };
}
