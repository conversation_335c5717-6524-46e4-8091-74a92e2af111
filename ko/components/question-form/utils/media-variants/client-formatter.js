export default function(data, mode) {
  let enableComment = mode === "cpoint" ? data.commentEnabled : (data.comment_enabled || data.is_self_answer);
  let variantsType = mode === "cpoint" ? data.variantsType : data.variants_element_type;
  let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;

  let variantsMediaType = mode === "cpoint" ? data.chooseType : data.choose_type;

  let variants = data.chooseMedia.filter((v) => !v.is_deleted).map((v, i) => {
    if (variantsMediaType == 'image') {
      return {
        id: v.id,
        mediaId: v.id,
        description: v.description,
        url: mode === "cpoint" ? v.url : '/' + v.file_path,
        preview: mode === "cpoint" ? v.poster : v.preview,
        position: 'position' in v ? v.position : i,
        points: v.points
      };
    }

    if (variantsMediaType == 'video') {
      return {
        id: v.id,
        mediaId: v.id,
        description: v.description,
        preview: mode === "cpoint" ? v.poster : v.previewUrl,
        url: v.url,
        position: 'position' in v ? v.position : i,
        points: v.points,
      };
    }
  });

  variants.sort((a,b) => {
    return a.position - b.position;
  });

  return {
    mediaVariantsMediaType: variantsMediaType,
    mediaVariantsType: parseInt(variantsType),
    mediaVariants: variants,
    mediaVariantsCommentEnabled: enableComment,
    mediaVariantsCommentLengthRange: commentLengthRange,
    mediaVariantsCommentPlaceholder: placeholder,
    mediaVariantsCommentLabel: data.comment_label,
    mediaVariantsCommentRequired: data.comment_required,
    answersCountLimit: data.max_choose_variants,
    randomOrder: data.random_variants_order,
    maxPointsCalcMethod: data.max_points_calc_method,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',
  };
}
