export default function (data) {
  return {
    choose_type: data.mediaVariantsMediaType,
    variants_element_type: data.mediaVariantsType,
    comment_enabled: data.mediaVariantsCommentEnabled ? 1 : 0,
    placeholder_text: data.mediaVariantsCommentPlaceholder,
    comment_minlength: data.mediaVariantsCommentLengthRange[0],
    comment_maxlength: data.mediaVariantsCommentLengthRange[1],
    comment_label: data.mediaVariantsCommentLabel,
    comment_required: data.mediaVariantsCommentRequired ? 1 : 0,
    max_choose_variants: data.answersCountLimit,
    max_points_calc_method: data.maxPointsCalcMethod ? 1 : 0,
    variants: data.mediaVariants
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
          points: v.points
        };
      }),
    random_variants_order: data.randomOrder ? 1 : 0,
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
  };
}
