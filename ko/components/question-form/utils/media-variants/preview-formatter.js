export default function (data) {
  let previewData = {
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
  };

  previewData.textFieldParam = {
    min: data.mediaVariantsCommentLengthRange[0],
    max: data.mediaVariantsCommentLengthRange[1],
  };

  previewData.placeholderText = data.mediaVariantsCommentPlaceholder;
  previewData.isHaveComment = data.mediaVariantsCommentEnabled;
  previewData.comment_label = data.mediaVariantsCommentLabel;
  previewData.variantsType = data.mediaVariantsType;
  previewData.chooseType = data.mediaVariantsMediaType;
  previewData.max_choose_variants = data.answersCountLimit;
  previewData.comment_required = data.mediaVariantsCommentRequired;

  let variants = data.mediaVariants.map((v) => {
    return {
      id: Math.random(1, 999),
      persistentId: v.persistentId,
      url: v.url,
      preview: v.preview,
      description: v.description,
      points: v.points,
    };
  });

  previewData.random_variants_order = data.randomOrder ? 1 : 0;

  if (data.randomOrder) {
    variants = _.shuffle(variants);
  }

  previewData.chooseMedia = variants;

  return previewData;
}
