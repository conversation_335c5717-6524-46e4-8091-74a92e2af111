export default function (data) {
  return {
    chooseType: data.mediaVariantsMediaType,
    variantsType: data.mediaVariantsType,
    commentEnabled: data.mediaVariantsCommentEnabled,
    placeholderText: data.mediaVariantsCommentPlaceholder,
    commentLengthRange: data.mediaVariantsCommentLengthRange,
    comment_label: data.mediaVariantsCommentLabel,
    comment_required: data.mediaVariantsCommentRequired ? 1 : 0,
    variants: data.mediaVariants
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
          points: v.points,
        };
      }),
    files: data.mediaVariants.filter((v) => v.mediaId).map((v) => v.mediaId),
    max_choose_variants: data.answersCountLimit,
    random_variants_order: data.randomOrder ? 1 : 0,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
  };
}
