export default function (data, type) {

  let previewData = {
    id: data.id,
    
    commentEnabled: data.npsCommentEnabled ? 1 : 0,
    textFieldParam: {
      min: data.npsCommentLengthRange[0],
      max: data.npsCommentLengthRange[1],
    },
    placeholderText: data.npsCommentPlaceholder,
    comment_label: data.npsCommentLabel,
    comment_required: data.galleryCommentRequaired,

    enableGallery: data.npsGalleryEnabled ? 1 : 0,
    gallery: data.npsGallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    npsRatingSetting: {
      design: data.npsType,
      start_point_color: data.npsStartColor,
      end_point_color: data.npsEndColor,
      start_label: data.npsStartLabel,
      end_label: data.npsEndLabel,
    },

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",

    from_one: data.fromOne,

    set_variants: data.ratingType === 'variants' ? 1 : 0,

  };

  const isVuePreview = type === 'vue'

  let variants = data.variants
    .map((v) => {
      const res = v
      res.id = Math.random(1, 999)

      if (isVuePreview && res.dictionary_element_id) {
        res.id = res.dictionary_element_id
      } else if (isVuePreview && res.persistentId) {
        res.id = res.persistentId || res.id
      }

      // @NOTE: needExtra приходит в качестве функции, для превью на Vue
      // мы используем postMessage, а он не поддерживает функции
      res.needExtra = v.need_extra

      if (isVuePreview && data.isDonorClassifierWithListType) {
        const val = v.value
        res.value = typeof val === 'string' ? val.split(' / ').pop() : ''
      }

      return res
    })
    .filter((v) => v.value);

  let clarifyingQuestion = data.clarifyingQuestion;

  previewData.extra_question_type = data.extraQuestionType

  previewData.clarifyingQuestion = clarifyingQuestion

  previewData.random_variants_order = data.randomOrder ? 1 : 0;
  

  if (data.randomOrder) {
    variants = _.shuffle(variants);
  }

  previewData.variants = variants;
  previewData.detail_answers = data.variants || []

  return previewData;
}
