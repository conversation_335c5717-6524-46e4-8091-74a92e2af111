import { get as _get } from 'lodash';

export default function (data, mode) {

  let enableComment =
    mode === "cpoint"
      ? data.commentEnabled
      : data.comment_enabled;
  let commentLengthRange =
    mode === "cpoint"
      ? data.commentLengthRange
      : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  let npsSettings = data.npsRatingSetting || {};

  const clarifyingToEach = data.extra_question_type === 3;
  let variants = data.detail_answers
  variants = variants
    .filter((v) => !v.is_deleted)
    .map((v) => {
      const variant = {
        id: v.id,
        donorId: v.question_detail_id,
        value: v.variant,
        points: v.points,
        needExtra: v.need_extra,
        dictionary_element_id: v.dictionary_element_id,
        extra_question: v.extra_question,
        ...v
      };
      if (clarifyingToEach && v.need_extra) {
        variant.detail_question_options =
          variants.filter((extra) => extra.question_detail_id === v.id);
      }
      return variant;
    })
    .filter((v) => {
      if (clarifyingToEach) {
        return !v.question_detail_id
      } else {
        return true
      }
      
    });

  let detailQuestion = data.detail_question;
  let variantsType = data.variants_element_type;
  let enableSelfAnswer = parseInt(data.is_self_answer) == 1;
  let clarifyingQuestion = {
    enabled: mode === 'cpoint' ?
      data.clarifyingQuestionEnabled :
      _get(data, 'detail_question', '') !== '',
    forAllRates: mode === 'cpoint' ? data.forAllRates : data.for_all_rates,
    forRates: data.forAllRates || data.for_all_rates ? [] : [data.extra_question_rate_from, data.extra_question_rate_to],
    text: (mode === 'cpoint' ? data.clarifyingQuestion : detailQuestion) || '',
    variantsType:
      mode === 'cpoint'
        ? parseInt(data.clarifyingQuestionVariantsType)
        : variantsType,
    variants: variants.filter(el => el.extra_question),
    customAnswerEnabled:
      mode === 'cpoint'
        ? data.customClarifyingQuestionAnswerAvailable
        : enableSelfAnswer,
    customAnswerLengthRange: commentLengthRange,
    customAnswerPlaceholder: placeholder,
    customAnswerLabel:  data.self_variant_text || '',
    required: data.extra_required,
    enableFile: data.variants_with_files,
    minСhooseVariants: data.min_choose_extra_variants,
    maxСhooseVariants: data.max_choose_extra_variants,
    clarifyingQuestionIsRequired: data.extra_required
  };

  return {
    npsGallery: gallery,
    npsGalleryEnabled: gallery.length > 0,
    extraQuestionType: data.extra_question_type,

    npsCommentEnabled: enableComment,
    npsCommentLengthRange: commentLengthRange,
    npsCommentPlaceholder: placeholder,
    npsCommentLabel: data.comment_label,
    npsCommentRequired: data.comment_required,

    npsType: npsSettings.design || 1,
    npsStartColor: npsSettings.start_point_color,
    npsEndColor: npsSettings.end_point_color,
    npsStartLabel: npsSettings.start_label,
    npsEndLabel: npsSettings.end_label,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || "",

    ratingType: data.set_variants ? "variants" : "standart",

    fromOne: data.from_one,
    variants: variants,

    randomOrder: data.random_variants_order,

    donorId: data.donor,
    donorVariantsType: data.donor_chosen === 1 ? 1 : 0,
    clarifyingQuestion: clarifyingQuestion,
    selfVariantFile: data.selfVariantFile
  };
}
