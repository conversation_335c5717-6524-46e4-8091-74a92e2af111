import { formatters } from "./formatters";

const defaultData = {
  linkWithClientField: 0,
  linkedClientField: "",
  rewriteLinkedField: 0,
  text: "",
  forAllRates: false,
  clarifyingQuestionEnabled: false,
  clarifyingQuestionIsRequired: false,
  customVariantAvailable: false,
  variantsType: 0,
  dontShowIfAnswered: 0,
  answersFrom: "",

  systemType: false,
  mediaType: 1,
  images: [],
  videos: [],
  assessmentType: 0,
  maskType: 1,
  maskConfig: {
    name: {
      visible: 1,
      required: 1,
      placeholderText: "",
      minLength: 3,
      maxLength: 250,
    },
    surname: {
      visible: 1,
      required: 1,
      placeholderText: "",
      minLength: 3,
      maxLength: 250,
    },
    patronymic: {
      visible: 1,
      required: 1,
      placeholderText: "",
      minLength: 3,
      maxLength: 250,
    },
  },
  clarifyingQuestion: "",
  commentEnabled: false,
  fileCommentEnabled: false,
  clarifyingQuestionAnswers: [],
  clarifyingQuestionVariantsType: 0,
  dateType: 1,
  regionsValues: [],
  filesLength: "4",
  fileType: "0",
  districtValues: [],
  streetValues: [],
  cityValues: [],
  customClarifyingQuestionAnswers: [],
  customClarifyingQuestionAnswerAvailable: false,
  customClarifyingQuestionAnswerLengthRange: [0, 250],
  commentLengthRange: [0, 250],
  variants: [],
  mediaVariants: [],
  chooseType: "image",
  quizzes: [],
  isCategoryDishInformationEnabled: true,
  isNameDishInformationEnabled: true,
  isPortionDishInformationEnabled: true,
  minSum: "",
  rateAll: true,


  placeholderText: "",
};

export function cPointServerFormatter(data) {
  let question = {
    ...defaultData,

    id: data.id || 0,
    type: data.type,
    alias: data.alias,
    name: data.name,
    description: data.description,
    sub_description: data.subdescription,
    required: data.required,

    dontShowIfAnswered: data.dontShowIfAnswered ? 1 : 0,
    answersFrom: "",
    extra_required: data.clarifyingQuestionIsRequired ? 1 : 0,
  };

  if (data.answersFrom) {
    let answersFromDate = moment(data.answersFrom, "DD.MM.YYYY");
    if (answersFromDate.isValid()) {
      question.answersFrom = answersFromDate.format("YYYY-MM-DD");
    }
  }

  let formatter = formatters[data.type];
  question = {
    ...question,
    ...formatter.cPointServer(data),
  };

  const smileFiles = {};
  if (data.smileFiles) {
    data.smileFiles.forEach((file, i) => {
      smileFiles[`smile${i}`] = file;
    });
  }

  return {
    ...question,
    additionalFiles: {
      ...smileFiles,
    },
  };
}
