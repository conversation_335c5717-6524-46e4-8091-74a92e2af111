import { formatters } from './formatters';

import { clientLinkFormatter } from './link-formatter';
import { clientMaskFormatter } from './mask-formatter';

export function questionClientFormatter(data, mode) {
  console.log("client formatter", data);
  let questionType = mode === 'cpoint' ? data.type : data.main_question_type;
  let countAnswers =
    mode === 'cpoint' ? data.surveys.length : data.countAnswers;
  let isAuto = mode === 'cpoint' ? false : data.poll_is_auto;
  let required = mode === 'cpoint' ? data.required : data.is_required == 1;
  let alias = mode === 'cpoint' ? data.alias : data.service_name;
  let isSystem = mode === 'cpoint' ? data.isSystem : data.is_system;
  let maskType = mode === 'cpoint' ? data.maskType : data.mask;
  let dontShowIfAnswered =
    mode === 'cpoint' ? data.dontShowIfAnswered : data.dont_show_if_answered;

  let answersFrom = '';
  let answersFromDate =
    mode === 'cpoint' ? data.answersFrom : data.answers_from;
  if (answersFromDate) {
    answersFromDate = moment(answersFromDate, 'YYYY-MM-DD HH:mm');
    if (answersFromDate.isValid())
      answersFrom = answersFromDate.format('DD.MM.YYYY HH:mm');
  }

  let commonData = {
    type: questionType,
    id: data.id,
    pointId: data.point_id,
    countAnswers,
    isAuto,
    pollIsAuto: isAuto,
    required,
    alias,
    name: data.name,
    description: data.description_html || data.description,
    descriptionText: data.description,
    subdescription: data.sub_description,
    isSource: data.is_source, // TODO
    isUpdated: data.is_updated, // TODO
    isTmp: data.is_tmp,
    isSystem,
    dontShowIfAnswered: parseInt(dontShowIfAnswered),
    answersFrom,
    questionLogic: data.questionLogic,
    dictionary_element_id: data.dictionary_element_id,
    dictionary_element_name: data.dictionary_element_name,
  };

  let result = {
    ...commonData,
    maskType: maskType || 0,
    maskConfig: clientMaskFormatter(data.maskConfig, mode),

    ...clientLinkFormatter(data, mode)
  };

  // ToDo вернуть как было, если всё поломается
  result = {
    ...result,
    ...formatters[`${questionType}`].client(data, mode),
  };
  
  return result;
}
