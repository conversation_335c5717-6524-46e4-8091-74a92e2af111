import * as assessmentTypes from '../../data/assessment-types';

export default function (data) {
  let question = {};

  question.assessmentType = data.rateAssessmentType - 1;
  question.commentEnabled = 0;
  question.comment_label = data.rateCommentLabel;


  if (data.rateMediaType == 'text') {
    question.mediaType = 0;
    question.text = data.rateText;
  } else if (data.rateMediaType == 'image') {
    question.mediaType = 1;
    question.images = data.rateImages.map(i => {
      return {
        id: i.id,
        url: i.url,
        label: i.label
      }
    });
    question.files = data.rateImages.map(i => i.id);
  } else if (data.rateMediaType == 'video') {
    question.mediaType = 2;
    question.videos = data.rateVideos.map(v => {
      return {
        id: v.id,
        url: v.url,
        label: v.label,
        preview: v.preview
      }
    });
    question.files = data.rateVideos.map(v => v.id);
  }

  if (data.isItemsQuestion) {
    question.isCategoryDishInformationEnabled = data.showItemCategory;
    question.isNameDishInformationEnabled = data.showItemName;
    question.isPortionDishInformationEnabled = data.showItemPortion;
    question.rateAll = data.rateAllItems;
    question.minSum = data.itemMinPrice;
  }

  if (data.isItemsQuestion) {
    question.customVariantAvailable = data.rateCommentEnabled;
    question.commentEnabled = data.rateCommentEnabled;
    question.placeholderText = data.rateCommentPlaceholder;
    question.customVariantLengthRange = data.rateCommentLengthRange;
  } else if (data.rateAssessmentType == assessmentTypes.ASSESSMENT_TYPE_STARS) {
    question.placeholderText = '';

    if (data.rateClarifyingQuestionEnabled) {
      question.clarifyingQuestionEnabled =data.rateClarifyingQuestionEnabled;
      question.clarifyingQuestion = data.rateClarifyingQuestionText;
      question.forAllRates = !!data.rateClarifyingQuestionForAllRates;

      question.clarifyingQuestionVariantsType =
        data.rateClarifyingQuestionVariantsType;
      question.customClarifyingQuestionAnswerLengthRange =
        data.rateClarifyingQuestionCustomAnswerLengthRange;
      question.placeholderText =
        data.rateClarifyingQuestionCustomAnswerPlaceholder;
      question.self_variant_text = data.rateClarifyingQuestionCustomAnswerLabel;
      question.clarifyingQuestionAnswers = data.rateClarifyingQuestionVariants.map(v => {
        return {
          id: v.id,
          variant: v.value
        }
      });
      question.customClarifyingQuestionAnswerAvailable = data.rateClarifyingQuestionCustomAnswerEnabled;
    }

    if (data.rateCommentEnabled) {
      question.commentEnabled = true;
      question.placeholderText = data.rateCommentPlaceholder;
      question.commentLengthRange = data.rateCommentLengthRange;
    }
  } else if (data.rateAssessmentType == assessmentTypes.ASSESSMENT_TYPE_VARIANTS) {
    question.customVariantAvailable = data.rateVariantsCustomAnswerEnabled;
    question.variantsType = data.rateVariantsType;
    question.customVariantLengthRange = data.rateVariantsCustomAnswerLengthRange;
    question.placeholderText = data.rateVariantsCustomAnswerPlaceholder;
    question.self_variant_text = data.customAnswerLabel;
    question.variants = data.rateVariants.map(v => {
      return {
        id: v.id,
        variant: v.value
      }
    })
  }

  return question;
}
