import * as assessmentTypes from '../../data/assessment-types';

const defaultDishesSet = [
  {
    value: 'Жареный тофу с курицей и овощами',
    stars: 0,
    price: 90,
    category: 'Горячее',
    serving: {
      count: 2,
      weight: 100,
    },
  },
  {
    value: 'Калифорния с креветкой',
    price: 300,
    category: 'Холодные закуски',
    stars: 0,
    serving: {
      count: 10,
      weight: 400,
    },
  },
  {
    value: 'Американо',
    stars: 0,
    price: 150,
    category: 'Напитки',
    serving: {
      count: 1,
      weight: 250,
    },
  },
  {
    value: 'Жареный тофу со свининой',
    price: 200,
    category: 'Горячее',
    stars: 0,
    serving: {
      count: 2,
      weight: 500,
    },
  },
  {
    value: 'Калифорния с огурцом',
    stars: 0,
    price: 75,
    category: 'Холодные закуски',
    serving: {
      count: 1,
      weight: 900,
    },
  },
];

export default function (data) {

  let previewData = {
    comment_label: data.rateCommentLabel
  };

  previewData.answerType = 0;
  previewData.variants = [];

  if (data.rateMediaType === 'text') {
    previewData.mediaType = 0;
    previewData.questionContent = data.rateText;
  }
  if (data.rateMediaType === 'image') {
    previewData.mediaType = 1;
    previewData.questionContent = data.rateImages;
  }
  if (data.rateMediaType === 'video') {
    previewData.mediaType = 2;
    previewData.questionContent = data.rateVideos;
  }

  if (data.rateAssessmentType == assessmentTypes.ASSESSMENT_TYPE_STARS) {
    previewData.assessmentType = 0;
    previewData.stars = 4;

    if (data.rateClarifyingQuestionEnabled) {
      previewData.forAllRates = data.rateClarifyingQuestionForAllRates ? 1 : 0;
      previewData.answerText = data.rateClarifyingQuestionText;
      previewData.variantsType = data.rateClarifyingQuestionVariantsType;
      previewData.isHaveCustomField = data.rateClarifyingQuestionCustomAnswerEnabled;
      previewData.textFieldParam = {
        min: data.rateClarifyingQuestionCustomAnswerLengthRange[0],
        max: data.rateClarifyingQuestionCustomAnswerLengthRange[1],
      };
      previewData.variants = data.rateClarifyingQuestionVariants.filter(v => v.value).map((v, i) => {
        return {
          ...v,
          id: i + 1
        }
      });
      previewData.placeholderText = data.rateClarifyingQuestionCustomAnswerPlaceholder;
      previewData.self_variant_text = data.rateClarifyingQuestionCustomAnswerLabel;
    }

    if (data.rateCommentEnabled) {
      previewData.isHaveComment = true;
      previewData.answerType = 1;
      previewData.textFieldParam = {
        min: data.rateCommentLengthRange[0],
        max: data.rateCommentLengthRange[1],
      };
      previewData.placeholderText = data.rateCommentPlaceholder;
    }
  }

  if (data.rateAssessmentType == assessmentTypes.ASSESSMENT_TYPE_VARIANTS) {
    previewData.assessmentType = 1;
    previewData.isHaveCustomField = data.rateVariantsCustomAnswerEnabled;
    previewData.variantsType = data.rateVariantsType;
    previewData.textFieldParam = {
      min: data.rateVariantsCustomAnswerLengthRange[0],
      max: data.rateVariantsCustomAnswerLengthRange[1],
    };
    previewData.variants = data.rateVariants.map((v) => {
      return {
        id: Math.random(1, 999),
        value: v.value,
      };
    }).filter(v => v.value);
    previewData.placeholderText = data.rateVariantsCustomAnswerPlaceholder;
    previewData.self_variant_text = data.customAnswerLabel;
  }

  if (data.isItemsQuestion) {
    previewData.answerType = 1; // комментарий
    previewData.assessmentType = 2;
    previewData.isHaveComment = !!data.rateCommentEnabled;
    previewData.checkAll = !!data.rateAllItems;
    previewData.category = data.showItemCategory ? 1 : 0;
    previewData.b_name = data.showItemName;
    previewData.weight = data.showItemPortion;
    previewData.minDishPrice = data.itemMinPrice;
    previewData.textFieldParam = {
      min: data.rateCommentLengthRange[0],
      max: data.rateCommentLengthRange[1],
    };
    previewData.placeholderText = data.rateCommentPlaceholder;
    previewData.variants = defaultDishesSet;
  }

  return previewData;
};
