import * as assessmentTypes from "../../data/assessment-types";

export default function (data) {
  let question = {
    comment_label: data.rateCommentLabel,
  };

  question.rating_type = data.rateAssessmentType;
  question.type = data.rateMediaType;
  question.comment_enabled = 0;
  question.detail_question = "";

  if (data.rateMediaType == "text") {
    question.text = data.rateText;
  }

  if (data.isItemsQuestion) {
    question.show_category = data.showItemCategory ? 1 : 0;
    question.show_name = data.showItemName ? 1 : 0;
    question.show_portion = data.showItemPortion ? 1 : 0;
    question.value_all = data.rateAllItems ? 1 : 0;
    question.min_sum = data.itemMinPrice;
  }

  if (data.isItemsQuestion) {
    question.is_self_answer = data.rateCommentEnabled ? 1 : 0;
    question.comment_enabled = data.rateCommentEnabled ? 1 : 0;
    question.placeholder_text = data.rateCommentPlaceholder;
    question.comment_minlength = data.rateCommentLengthRange[0];
    question.comment_maxlength = data.rateCommentLengthRange[1];
  } else if (data.rateAssessmentType == assessmentTypes.ASSESSMENT_TYPE_STARS) {
    question.variants_element_type = data.rateClarifyingQuestionVariantsType;
    question.comment_minlength = data.rateCommentLengthRange[0];
    question.comment_maxlength = data.rateCommentLengthRange[1];
    question.placeholder_text = "";

    if (data.rateClarifyingQuestionEnabled) {
      question.detail_question = data.rateClarifyingQuestionText;
      question.for_all_rates = data.rateClarifyingQuestionForAllRates;
      question.is_self_answer = data.rateClarifyingQuestionCustomAnswerEnabled
        ? 1
        : 0;
      question.variants_element_type = data.rateClarifyingQuestionVariantsType;
      question.comment_minlength =
        data.rateClarifyingQuestionCustomAnswerLengthRange[0];
      question.comment_maxlength =
        data.rateClarifyingQuestionCustomAnswerLengthRange[1];
      question.placeholder_text =
        data.rateClarifyingQuestionCustomAnswerPlaceholder;
      question.self_variant_text = data.rateClarifyingQuestionCustomAnswerLabel;
    }

    if (data.rateCommentEnabled) {
      question.is_self_answer = 1;
      question.comment_enabled = 1;
      question.placeholder_text = data.rateCommentPlaceholder;
    }
  } else if (
    data.rateAssessmentType == assessmentTypes.ASSESSMENT_TYPE_VARIANTS
  ) {
    question.is_self_answer = data.rateVariantsCustomAnswerEnabled ? 1 : 0;
    question.variants_element_type = data.rateVariantsType;
    question.comment_minlength = data.rateVariantsCustomAnswerLengthRange[0];
    question.comment_maxlength = data.rateVariantsCustomAnswerLengthRange[1];
    question.placeholder_text = data.rateVariantsCustomAnswerPlaceholder;
    question.self_variant_text = data.customAnswerLabel;
  }

  return question;
}
