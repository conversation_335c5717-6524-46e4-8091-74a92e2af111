export default function(data, mode) {
  let detailQuestion = data.detail_question;
  let enableSelfAnswer = parseInt(data.is_self_answer) == 1;
  let enableComment = mode === "cpoint" ? data.commentEnabled : data.comment_enabled;
  let variantsType = data.variants_element_type;
  let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;
  let variants = (mode === "cpoint" ? data.variants : data.detail_answers).filter((v) => !v.is_deleted).map((v) => {
    return {
      id: v.id,
      value: v.variant,
    };
  });

  let dishesData = {
    showItemCategory: mode === "cpoint" ? data.isCategoryDishInformationEnabled : data.show_category,
    showItemName: mode === "cpoint" ? data.isNameDishInformationEnabled : data.show_name,
    showItemPortion: mode === "cpoint" ? data.isPortionDishInformationEnabled : data.show_portion,
    rateAllItems: mode === "cpoint" ? data.rateAll : data.value_all,
    itemMinPrice: mode === "cpoint" ? data.minSum : (data.min_sum === 0 ? '' : data.min_sum),
  }

  let mediaType = mode === "cpoint" ? data.mediaType : data.type;
  if (mediaType == 0) {
    mediaType = 'text';
  } else if (mediaType == 1) {
    mediaType = 'image';
  } else if (mediaType == 2) {
    mediaType = 'video';
  }

  let assessmentType = mode === "cpoint" ? data.assessmentType + 1 : data.rating_type;
  if (assessmentType < 1) assessmentType = 1;

  return {
    rateMediaType: mediaType,
    rateText: data.text || '',
    rateImages: data.images.map((i) => {
      return {
        id: i.id,
        url: mode === "cpoint" ? i.url : i.file_path,
        label: mode === "cpoint" ? i.label : i.file_text,
      };
    }),
    rateVideos: data.videos.map((v) => {
      return {
        id: v.id,
        url: v.url,
        label: v.label,
        preview: mode === "cpoint" ? v.poster : v.previewUrl,
      };
    }),
    rateAssessmentType: assessmentType,
    rateClarifyingQuestionEnabled: mode === "cpoint" ? data.clarifyingQuestionEnabled : !!detailQuestion,
    rateClarifyingQuestionForAllRates: mode === "cpoint" ? data.forAllRates : data.for_all_rates,
    rateClarifyingQuestionText: mode === "cpoint" ? data.clarifyingQuestion : detailQuestion,
    rateClarifyingQuestionVariantsType: mode === "cpoint" ? parseInt(data.clarifyingQuestionVariantsType) : variantsType,
    rateClarifyingQuestionVariants: mode === "cpoint" ? data.clarifyingQuestionAnswers.map(v => {
      return {
        id: v.id,
        value: v.variant
      }
    }) : variants,
    rateClarifyingQuestionCustomAnswerEnabled: mode === "cpoint" ? data.customClarifyingQuestionAnswerAvailable : enableSelfAnswer,
    rateClarifyingQuestionCustomAnswerLengthRange: commentLengthRange,
    rateClarifyingQuestionCustomAnswerPlaceholder: placeholder,

    rateCommentEnabled: enableComment,
    rateCommentLengthRange: commentLengthRange,
    rateCommentPlaceholder: placeholder,
    rateCommentLabel: data.comment_label,

    rateVariantsType: mode === "cpoint" ? parseInt(data.variantsType) : variantsType,
    rateVariants: variants,
    rateVariantsCustomAnswerEnabled: mode === "cpoint" ? data.commentEnabled : (enableSelfAnswer || enableComment),
    rateVariantsCustomAnswerLengthRange: commentLengthRange,
    rateVariantsCustomAnswerPlaceholder: placeholder,
    customAnswerLabel: data.self_variant_text || '',

    ...dishesData
  }
}
