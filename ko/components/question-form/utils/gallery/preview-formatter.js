export default function(data) {
  let previewData = {
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
  };

  previewData.textFieldParam = {
    min: data.galleryCommentLengthRange[0],
    max: data.galleryCommentLengthRange[1],
  };

  previewData.placeholderText = data.galleryCommentPlaceholder;
  previewData.isHaveComment = data.galleryCommentEnabled;
  previewData.comment_label = data.galleryCommentLabel;
  previewData.comment_required = data.galleryCommentRequaired;


  previewData.gallery = data.gallery.map((v) => {
    return {
      id: Math.random(1, 999),
      persistentId: v.persistentId || v.id,
      url: v.url,
      preview: v.preview,
      description: v.description,
      poster: v.poster || v.preview,
    };
  });

  


  return previewData;
}
