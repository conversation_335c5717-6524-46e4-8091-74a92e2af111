export default function(data, mode) {
  let enableComment = mode === "cpoint" ? data.commentEnabled : data.comment_enabled || data.is_self_answer;
  let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: 'position' in v ? v.position : i,
    };
  });

  gallery.sort((a,b) => {
    return a.position - b.position;
  });

  return {
    gallery: gallery,
    galleryCommentEnabled: enableComment,
    galleryCommentLengthRange: commentLengthRange,
    galleryCommentPlaceholder: placeholder,
    galleryCommentLabel: data.comment_label,
    galleryCommentRequired: data.comment_required,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',
  };
}
