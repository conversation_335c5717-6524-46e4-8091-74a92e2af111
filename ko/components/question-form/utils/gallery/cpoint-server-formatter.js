export default function (data) {
  return {
    commentEnabled: data.galleryCommentEnabled,
    placeholderText: data.galleryCommentPlaceholder,
    commentLengthRange: data.galleryCommentLengthRange,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
  };
}
