export default function (data) {

  return {
    comment_enabled: data.galleryCommentEnabled ? 1 : 0,
    placeholder_text: data.galleryCommentPlaceholder,
    comment_label: data.galleryCommentLabel,
    comment_minlength: data.galleryCommentLengthRange[0],
    comment_maxlength: data.galleryCommentLengthRange[1],
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
  };
}
