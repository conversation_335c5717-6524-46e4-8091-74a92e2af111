export default function (data) {
  let question = {
    comment_enabled: data.commentEnabled ? 1 : 0,
    placeholder_text: data.commentPlaceholder,
    comment_minlength: data.commentLengthRange[0],
    comment_maxlength: data.commentLengthRange[1],
    comment_label: data.commentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.galleryEnabled,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    skip_variant: data.skipVariant ? 1 : 0,

    random_variants_order: data.randomOrder ? 1 : 0,
    for_all_rates: data.requiredForAll ? 1 : 0,
    variants_element_type: data.withoutRest,
    self_variant_text: data.indicatorText,

    scaleQuestion: {
      end: data.maxValue,
    },
  };


  return question;
}
