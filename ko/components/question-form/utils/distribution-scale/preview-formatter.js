export default function (data) {
  return {
    commentEnabled: data.commentEnabled ? 1 : 0,
    placeholder_text: data.commentPlaceholder,
    textFieldParam: {
      min: data.commentLengthRange[0],
      max: data.commentLengthRange[1],
    },
    comment_label: data.commentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.galleryEnabled,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: Math.random(1, 999),
          url: v.url,
          persistentId: v.persistentId,
          preview: v.preview,
          poster: v.preview,
          description: v.description,
        };
      }),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    skip_variant: data.skipVariant ? 1 : 0,

    random_variants_order: data.randomOrder ? 1 : 0,
    requiredForAll: data.requiredForAll ? 1 : 0,
    variantsType: data.withoutRest,
    self_variant_text: data.indicatorText,

    scaleRatingSetting: {
      end: data.maxValue,
    },

    variants: data.variants
      .map((v) => {
        const res = {
          id: Math.random(1, 999),
          persistentId: v.persistentId || v.id,
          value: v.value,
          points: v.points,
        };

        return res
      })
      .filter((v) => v.value)
  };
}
