export default function (data) {
  let variants = data.detail_answers.filter(v => !v.is_deleted).map(
    (v) => {
      return {
        id: v.id,
        donorId: v.question_detail_id,
        donorVariantId: v.question_detail_id,
        value: v.variant,
        points: v.points,
        position: v.position,
        needExtra: v.need_extra,
        dictionary_element_id: v.dictionary_element_id,
      };
    }
  );

  variants.sort((a, b) => {
    return a.position - b.position;
  });

  let enableComment = data.comment_enabled || data.is_self_answer;
  let commentLengthRange = [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;


  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  return {
    gallery: gallery,
    galleryEnabled: gallery.length > 0,

    enableComment: enableComment,
    commentLengthRange: commentLength<PERSON><PERSON><PERSON>,
    placeholder: placeholder,
    commentLabel: data.comment_label,
    commentRequired: data.comment_required,


    variants: variants,
    randomOrder: data.random_variants_order,
    requiredForAll: !!data.for_all_rates,
    withoutRest: data.variants_element_type,
    maxValue: data.scaleRatingSetting?.end || 100,
    indicatorText: data.self_variant_text,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || "",
    skipVariant: data.skip_variant ? 1 : 0,
  };
};
