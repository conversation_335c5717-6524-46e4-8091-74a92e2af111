export default function (data, formData) {

  let question = {
    max_choose_variants: data.variantsAnswersCountLimit,
    enableGallery: data.galleryEnabled,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    skip_variant: data.skipVariant ? 1 : 0,
    skip_row: data.skipRow ? 1 : 0,
    skip_column: data.skipColumn ? 1 : 0,
    select_placeholder_text: data.selectPlaceholder,
    comment_enabled: data.galleryCommentEnabled ? 1 : 0,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    matrixSettings: data.matrixSettings,
    show_tooltips: data.show_tooltips ? 1 : 0,
  };

  question.rating_type = data.variantsAssessmentType;
  question.is_self_answer = data.variantsCustomAnswerEnabled ? 1 : 0;
  question.variants_element_type = data.variantsType;
  question.comment_minlength = data.galleryCommentLengthRange[0];
  question.comment_maxlength = data.galleryCommentLengthRange[1];
  question.placeholder_text = data.galleryCommentPlaceholder;
  question.self_variant_text = data.customAnswerLabel;
  question.dropdown_variants = data.dropdown ? 1 : 0;
  question.random_variants_order = data.randomOrder ? 1 : 0;
  question.donor_rows = data.donorRows || '';
  question.donor_columns = data.donorColumns || '';
  question.donor_chosen = data.donorRowsType == 1 ? 1 : 0;
  question.donor_cols_chosen = data.donorColumnsType == 1 ? 1 : 0;

  formData.FoquzQuestionMatrixElement = {
    rows: data.rowsVariants.map((el, index) => {
      return {
        id: el.id == el.donor_variant_id || el.id == el.donor_dictionary_element_id ? '0' : el.id,
        name: el.value,
        position: index,
        donor_variant_id: el.donor_variant_id,
        donor_dictionary_element_id: el.donor_variant_id,
        description: el.description
      }
    }),
    columns: data.colsVariants.map((el, index) => ({
      id: el.id == el.donor_variant_id || el.id == el.donor_dictionary_element_id ? '0' : el.id,
      name: el.value,
      position: index,
      donor_variant_id: el.donor_variant_id,
      donor_dictionary_element_id: el.donor_variant_id,
      description: el.description,
      variants: el.children.getVariants().map((el, index) => {
        return {
          id: el.id,
          name: el.value,
          position: index,
        }       
      }),
    })),
  }

  return question;
}
