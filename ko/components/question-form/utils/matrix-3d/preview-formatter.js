export default function (data, type) {
  const previewData = {
    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery.map(v => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),  
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    skip_variant: data.skipVariant ? 1 : 0,
    skip_row: data.skipRow ? 1 : 0,
    skip_column: data.skipColumn ? 1 : 0,
    variantsType: data.variantsType,
    selectPlaceholder: data.selectPlaceholder,
    commentEnabled: data.galleryCommentEnabled ? 1 : 0,
    isHaveComment: data.galleryCommentEnabled ? 1 : 0, 
    textFieldParam: {
      min: data.galleryCommentLengthRange[0],
      max: data.galleryCommentLengthRange[1]
    },
    placeholderText: data.galleryCommentPlaceholder,
    comment_label: data.galleryCommentLabel,
    matrixSettings: data.matrixSettings,
    comment_required: data.galleryCommentRequaired,
    show_tooltips: data.show_tooltips ? 1 : 0,
  };

  const isVuePreview = type === 'vue'

  // ToDo refactor
  if (data.matrixElements) {
    previewData.matrixElements = data.matrixElements;
  } else {
    previewData.matrixElements = {
      rows: data.rowsVariants.map((el, index) => {
        const row = {
          id: type === 'vue' ? el._recipientId || el.id : el.id,
          name: el.value,
          position: index,
          description: el.description
        }

        if (isVuePreview && data.isDonorRowsClassifierWithListType) {
          row.name = el.value.split(' / ').pop()
        }

        return row
      }),
      columns: data.colsVariants.map((el, index) => {
        const column = {
          id: el.id,
          name: el.value,
          position: index,
          description: el.description,
          variants: el.children ? el.children.getVariants().map((el, index) => ({
            id: el.id,
            name: el.value,
            persistentId: el.persistentId || el.id,
            position: index,
          })) : [],
        }

        if (isVuePreview && data.isDonorColumnsClassifierWithListType) {
          column.name = el.value.split(' / ').pop()
        }

        return column
      }),
    };

    const rows = previewData.matrixElements.rows || [];
    const columns = previewData.matrixElements.columns || [];
    const shouldRemoveEmptyRow = rows.length === 1 && rows[0].id === '0' && rows[0].name === '';
    const shouldRemoveEmptyColumn = columns.length === 1 && columns[0].id === '0' && columns[0].name === '';

    if (shouldRemoveEmptyRow && shouldRemoveEmptyColumn) {
      rows.pop();
      columns.pop();
    }
  }

  return previewData;
}
