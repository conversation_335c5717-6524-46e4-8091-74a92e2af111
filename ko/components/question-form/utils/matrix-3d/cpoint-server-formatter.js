export default function (data) {
  let question = {
    max_choose_variants: data.variantsAnswersCountLimit,
    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    skip_variant: data.skipVariant ? 1 : 0,
    skip_row: data.skipRow ? 1 : 0,
    skip_column: data.skipColumn ? 1 : 0,
    select_placeholder_text: data.selectPlaceholder,
    commentEnabled: data.galleryCommentEnabled ? 1 : 0,
    placeholderText: data.galleryCommentPlaceholder,
    commentLengthRange: data.galleryCommentLengthRange,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    show_tooltips: data.show_tooltips ? 1 : 0,
  };
  question.customVariantAvailable = data.variantsCustomAnswerEnabled;
  question.variantsType = data.variantsType;
  question.customVariantLengthRange = data.variantsCustomAnswerLengthRange;
  question.self_variant_text = data.customAnswerLabel;
  question.dropdown_variants = data.dropdown ? 1 : 0;
  question.random_variants_order = data.randomOrder ? 1 : 0;
  question.FoquzQuestionMatrixElement = {
    rows: data.rowsVariants.map((el, index) => ({
      id: el.id,
      name: el.value,
      position: index,
      description: el.description
    })),
    columns: data.colsVariants.map((el, index) => ({
      id: el.id,
      name: el.value,
      position: index,
      description: el.description,
      variants: el.children.getVariants().map((el, index) => ({
        id: el.id,
        name: el.value,
        position: index,
      })),
    })),
  };
  question.matrixSettings = data.matrixSettings;

  return question;
}
