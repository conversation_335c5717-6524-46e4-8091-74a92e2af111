export default function (data, mode) {

  let enableSelfAnswer = parseInt(data.is_self_answer) == 1;
  let enableComment = mode === "cpoint" ? data.commentEnabled : data.comment_enabled;
  let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
  let variantsType =
    mode === "cpoint" ? data.variantsType : data.variants_element_type;
  let placeholder = data.placeholderText;

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  return {
    gallery: gallery,
    galleryEnabled: gallery.length > 0,
    galleryCommentEnabled: enableComment,
    galleryCommentLengthRange: commentLengthRange,
    galleryCommentPlaceholder: placeholder,
    galleryCommentLabel: data.comment_label,
    galleryCommentRequired: data.comment_required,

    variantsType: parseInt(variantsType),
    matrixElements: data.matrixElements,
    matrixSettings: data.matrixSettings,
    variantsCustomAnswerEnabled:
      mode === "cpoint"
        ? data.customVariantAvailable
        : enableSelfAnswer || enableComment,
    variantsCustomAnswerLengthRange: commentLengthRange,
    variantsCustomAnswerPlaceholder: placeholder,
    customAnswerLabel: data.self_variant_text || "",
    variantsAnswersCountLimit: data.max_choose_variants,
    dropdown:
      mode === "cpoint" ? data.dropdownVariants : data.dropdown_variants,
    randomOrder: data.random_variants_order,

    donorRows: data.donor_rows,
    donorColumns: data.donor_columns,
    donorRowsType: data.donor_chosen === 1 ? 1 : 0,
    donorColumnsType: data.donor_cols_chosen === 1 ? 1 : 0,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || "",
    skipVariant: data.skip_variant ? 1 : 0,
    skipRow: data.skip_row ? 1 : 0,
    skipColumn: data.skip_column ? 1 : 0,
    selectPlaceholder: mode === "cpoint" ? data.selectPlaceholderText || '' : data.select_placeholder_text || '',
    showTooltips: data.show_tooltips,
  };
}
