export default function (data) {
  let question = {};

  question.fileCommentEnabled = data.fileCommentEnabled;
  question.comment_label = data.fileCommentLabel; 
  question.fileType = data.fileType;
  question.filesLength = data.maxFilesCount;
  question.commentLengthRange = data.fileCommentLengthRange;
  question.placeholderText = data.fileCommentPlaceholder;
  question.comment_required = data.fileCommentRequired ? 1 : 0;

  return question;
}
