export default function (data) {
  let question = {};
  const files = {
    '0': 'image',
    '1': 'video',
    '2': 'audio'
  }

  question.is_self_answer = data.fileCommentEnabled ? 1 : 0;
  question.file_types = data.fileTypes.map(i => files[i]);
  if (question.file_types.length) {
    
  }
  question.files_length = data.maxFilesCount;
  question.comment_minlength = data.fileCommentLengthRange[0];
  question.comment_maxlength = data.fileCommentLengthRange[1];
  question.placeholder_text = data.fileCommentPlaceholder;
  question.comment_label = data.fileCommentLabel;
  question.comment_required = data.fileCommentRequired ? 1 : 0;
  question.comment_enabled = data.fileCommentEnabled ? 1 : 0;

  return question;
}
