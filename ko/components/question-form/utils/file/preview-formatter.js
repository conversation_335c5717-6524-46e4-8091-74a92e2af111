export default function (data) {
  let previewData = {};
  const files = {
    '0': 'image',
    '1': 'video',
    '2': 'audio'
  }

  previewData.fileType = data.fileType;
  previewData.fileTypes = data.fileTypes.map(i => files[i]);
  previewData.filesLength = data.maxFilesCount;
  previewData.isHaveCustomField = data.fileCommentEnabled;
  previewData.textFieldParam = {
    min: data.fileCommentLengthRange[0],
    max: data.fileCommentLengthRange[1],
  };
  previewData.placeholderText = data.fileCommentPlaceholder;
  previewData.comment_label = data.fileCommentLabel;
  previewData.comment_required = data.fileCommentRequired;
  previewData.comment_enabled = data.fileCommeпtEnabled;

  return previewData;
}
