export default function(data, mode) {
  const files = {
    image: '0',
    video: '1',
    audio: '2'
  }
  let commentEnabled = mode === "cpoint" ? data.commentEnabled : parseInt(data.is_self_answer) == 1;
  let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;
  let fileTypes = mode === "cpoint" ? data.file_types.map(i => files[i]) : (data.file_types === null ? [] : data.file_types.map(i => files[i]));

  return {
    fileCommentEnabled: commentEnabled,
    fileCommentLabel: data.comment_label,
    maxFilesCount: mode === "cpoint" ? data.filesLength : data.files_length,
    fileTypes,
    fileCommentLengthRange: commentLengthRange,
    fileCommentPlaceholder: placeholder,
    fileCommentRequired: data.comment_required,
  }
}
