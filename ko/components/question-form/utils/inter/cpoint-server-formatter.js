import { interblockTypes } from "Data/interblock-types";


export default function (data) {
  let interBlock = data.interBlock;
  let texts = interBlock.texts;
  let share = interBlock.share;
  let question = {
    intermediateBlock: {
      //code: interBlock.promocode.name,
      complaint_button: interBlock.hasComplainButton ? 1 : 0,
      complaint_button_text: texts.complain,
      poll_button_text: texts.takeSurvey,
      ready_button: interBlock.hasReadyButton ? 1 : 0,
      ready_button_text: texts.ready,
      close_widget_button: interBlock.hasCloseButtonForWidget ? 1 : 0,
      close_widget_button_text: texts.closeButtonForWidget,
      start_over_button: interBlock.hasStartOverButton ? 1 : 0,
      start_over_button_text: texts.startOver,
      external_link: interBlock.readyLink,
      pool_id: interBlock.promocode.poolId,
      screen_type: interblockTypes[interBlock.type],
      show_question_number: interBlock.showNumber ? 1 : 0,
      socNetworks: {
        social_networks_enabled: interBlock.enableShare ? 1 : 0,
        ...share,
      },
      text: interBlock.text,
      unsubscribe_button: interBlock.hasUnsubscribeButton ? 1 : 0,
      unsubscribe_button_text: texts.unsubscribe,
      scores_button: interBlock.hasPointsButton ? 1 : 0,
      scores_button_text: texts.points,
      logos_backcolor: interBlock.imagesBackground,
      agreement: interBlock.agreement ? 1 : 0,
      agreement_text: interBlock.agreementText,
      show_image_button_text: interBlock.show_image_button_text,
      image_show_time: interBlock.image_show_time,
      show_bg_instruction: interBlock.show_bg_instruction ? 1 : 0
    },
    endScreenLogos: interBlock.images,
  };



  return question;
}
