import { Share } from 'Models/share';
import { getInterblockName } from '../../../../data/interblock-types';

export default function (data, mode) {
  let interBlock = data.intermediateBlock;

  if (!interBlock) return {};

  let share = interBlock.socNetworks;

  let images = (mode == 'cpoint' ? data.endScreenLogos : data.foquzQuestionEndScreenLogos) || [];

  return {
    interBlock: {
      type: getInterblockName(interBlock.screen_type),
      showNumber: interBlock.show_question_number,
      text: interBlock.text,
      hasComplainButton: !!interBlock.complaint_button,
      hasUnsubscribeButton: !!interBlock.unsubscribe_button,
      hasCloseButtonForWidget: !!interBlock.close_widget_button,
      hasReadyButton: !!interBlock.ready_button,
      hasPointsButton: !!interBlock.scores_button,
      hasStartOverButton: !!interBlock.start_over_button,
      texts: {
        complain: interBlock.complaint_button_text || '',
        unsubscribe: interBlock.unsubscribe_button_text || '',
        takeSurvey: interBlock.poll_button_text || '',
        ready: interBlock.ready_button_text || '',
        points: interBlock.scores_button_text || '',
        startOver: interBlock.start_over_button_text || '',
        closeButtonForWidget: interBlock.close_widget_button_text || '',
      },
      readyLink: interBlock.external_link,
      promocode: {
        // name: interBlock.code,
        poolId: interBlock.pool_id
      },
      enableShare: share && !!share.social_networks_enabled,
      share: Share.clientFormatter(share),
      showImages: images.length > 0,
      images: images,
      imagesBackground: interBlock.logos_backcolor,
      agreement: !!interBlock.agreement,
      agreementText: interBlock.agreement_text || '',
      show_image_button_text: interBlock.show_image_button_text || '',
      image_show_time: interBlock.image_show_time || 25,
      show_bg_instruction: !!interBlock.show_bg_instruction
    }
  };
}
