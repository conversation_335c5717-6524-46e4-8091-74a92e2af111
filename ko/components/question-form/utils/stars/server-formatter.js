export default function (data) {

  let gallery = data.galleryEnabled
    ? data.gallery
        .filter((v) => v.mediaId)
        .map((v) => {
          return {
            id: v.mediaId,
            description: v.description,
          };
        })
    : [];

  let question = {
    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery,
    files: gallery.filter((v) => v.mediaId).map((v) => v.mediaId),
    starOptions: data.starsConfig,

    detail_question: '',
    comment_enabled: 0,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',

    show_labels: data.showLabels ? 1 : 0,
    show_numbers: data.showNumbers ? 1 : 0
  };

  let clarifyingQuestion = data.clarifyingQuestion;
  let comment = data.comment;

  if (clarifyingQuestion.enabled) {
    question.detail_question = clarifyingQuestion.text;
    question.for_all_rates = clarifyingQuestion.forAllRates ? 1 : 0;

    question.variants_element_type = clarifyingQuestion.variantsType;

    question.is_self_answer = clarifyingQuestion.customAnswerEnabled ? 1 : 0;
    let customAnswerRange = clarifyingQuestion.customAnswerRange;
    question.comment_minlength = customAnswerRange[0];
    question.comment_maxlength = customAnswerRange[1];
    question.placeholder_text = clarifyingQuestion.customAnswerPlaceholder;
    question.self_variant_text = clarifyingQuestion.customAnswerLabel;
    question.extra_required = clarifyingQuestion.required ? 1 : 0;
  }

  if (data.comment.enabled) {
    question.is_self_answer = 1;
    question.comment_enabled = 1;
    
    let commentRange = comment.range;
    question.comment_minlength = commentRange[0];
    question.comment_maxlength = commentRange[1];
    question.placeholder_text = comment.placeholder;
    question.comment_label = comment.label;
    question.comment_required = data.galleryCommentRequaired ? 1 : 0;
  }

  return question;
}
