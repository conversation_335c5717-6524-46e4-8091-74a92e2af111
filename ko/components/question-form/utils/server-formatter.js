import { get as _get } from 'lodash';
import * as questionTypes from "../../../data/question-types";
import * as assessmentTypes from "../data/assessment-types";
import { formatters } from "./formatters";
import { InterblockFormatter } from "./interblock-formatter";

export function questionServerFormatter(data) {
  let question = {
    point_id: data.pointId,
    poll_id: POLL_ID,
    name: data.name,
    description: data.description,
    sub_description: data.subdescription,
    service_name: data.alias,
    main_question_type: data.type,
    is_required: data.required ? 1 : 0,

    is_self_answer: 0,

    dont_show_if_answered: data.dontShowIfAnswered ? 1 : 0,
    answers_from: "",

    donor: "",
    dictionary_element_id: data.dictionaryElementId,
  };

  if (data.answersFrom) {
    let answersFromDate = moment(data.answersFrom, "DD.MM.YYYY");
    if (answersFromDate.isValid()) {
      question.answers_from = answersFromDate.format("YYYY-MM-DD");
    }
  }

  let questionDetail = {};
  let media = [];
  let intermediateBlock = {};

  const formData = {};
  let formatter = formatters[data.type];
  question = {
    ...question,
    ...formatter.server(data, formData),
  };

  if (data.type == questionTypes.RATE_QUESTION) {
    if (data.rateMediaType == "image") {
      media = data.rateImages.map((i) => i.id);
    }

    if (data.rateMediaType == "video") {
      media = data.rateVideos.map((i) => i.id);
    }

    if (data.rateAssessmentType == assessmentTypes.ASSESSMENT_TYPE_STARS) {
      questionDetail = data.rateClarifyingQuestionVariants;
    } else if (
      data.rateAssessmentType == assessmentTypes.ASSESSMENT_TYPE_VARIANTS
    ) {
      questionDetail = data.rateVariants;
    }
  }

  if (
    data.type == questionTypes.VARIANTS_QUESTION ||
    data.type == questionTypes.PRIORITY_QUESTION ||
    data.type == questionTypes.STAR_VARIANTS_QUESTION ||
    data.type == questionTypes.NPS_QUESTION ||
    data.type == questionTypes.SCALE_QUESTION ||
    data.type == questionTypes.SMILE_QUESTION ||
    data.type == questionTypes.CLASSIFIER_QUESTION ||
    data.type == questionTypes.CARD_SORTING_QUESTION ||
    data.type == questionTypes.DISTRIBUTION_SCALE_QUESTION
  ) {
    questionDetail = data.variants;
  }

  if (
    _get(data, 'clarifyingQuestion.enabled') &&
    (
      data.type == questionTypes.STAR_VARIANTS_QUESTION ||
      data.type == questionTypes.MATRIX_QUESTION ||
      (data.type == questionTypes.SMILE_QUESTION && question.variantsType !== 2) ||
      (data.type == questionTypes.NPS_QUESTION && question.extra_question_type != 3)
    )
  ) {
    questionDetail = data.variants || [];
    questionDetail = [
      ...questionDetail,
      ...data.clarifyingQuestion.variants.map(el => {
        return {
          ...el,
          extra_question: 1,
          ...(el.id ? {} : { id: 0 }),
        };
      }),
    ];
  }

  if (
    (data.type == questionTypes.STARS_QUESTION ||
      data.type == questionTypes.RATING_QUESTION) &&
    data.clarifyingQuestion.enabled
  ) {
    questionDetail = data.clarifyingQuestion.variants;
  }

  if (data.type == questionTypes.INTER_BLOCK) {
    intermediateBlock = InterblockFormatter(data.interBlock);
  }

  const smileFiles = {};
  if (data.smileFiles) {
    data.smileFiles.forEach((file, i) => {
      smileFiles[`smile${i}`] = file;
    });
  }

  let details = questionDetail;
  if (details && Array.isArray(questionDetail)) {
    details = questionDetail
      .reduce((acc, item, i) => {
        if (!item) return acc;
        if (typeof item === "object") {
          const need_extra =
            ('needExtra' in item && ko.unwrap(item.needExtra) ? 1 : 0) || item.need_extra;
          const items = [
            {
              ...item,
              position: i + 1,
              need_extra,
              needExtra: undefined,
            },
          ];
          return [...acc, ...items];
        }
        return [...acc, item];
      }, []);
  }

  Object.assign(formData, {
    FoquzQuestion: question,
    FoquzQuestionDetail: details,
    media,
    ...intermediateBlock,
    additionalFiles: {
      ...smileFiles,
    },
  });

  return formData;
}
