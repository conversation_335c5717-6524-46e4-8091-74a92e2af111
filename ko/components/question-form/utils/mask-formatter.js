import { isTrue } from '@/utils/is-true';

function formatValue(value) {
  return isTrue(value);
}

export function serverMaskFormatter(config) {
  let maskConfig = {};
  Object.keys(config).forEach((key) => {
    let field = config[key];
    maskConfig[key] = {
      visible: formatValue(field.visible),
      required: formatValue(field.required),
      placeholderText: field.placeholder,
      minLength: field.range[0],
      maxLength: field.range[1],
    };
  });
  return maskConfig;
}

export function previewMaskFormatter(config) {
  config = config || {};
  let maskConfig = {};
  Object.keys(config).forEach((key) => {
    let field = config[key];
    maskConfig[key] = {
      visible: formatValue(field.visible),
      required: formatValue(field.required),
      placeholderText: field.placeholder,
      minLength: field.range[0],
      maxLength: field.range[1],
    };
  });
  return maskConfig;
}

export function clientMaskFormatter(config) {
  if (!config) return null;
  let maskConfig = {};
  Object.keys(config).forEach((key) => {
    let field = config[key];
    maskConfig[key] = {
      visible: formatValue(field.visible),
      required: formatValue(field.required),
      placeholder: field.placeholderText,
      range: [field.minLength, field.maxLength],
    };
  });
  return maskConfig;
}
