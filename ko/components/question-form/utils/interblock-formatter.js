import { interblockTypes } from 'Data/interblock-types';

export function InterblockFormatter(data) {
  let texts = data.texts;
  let share = data.share;
  let promocode = data.promocode;

  return {
    IntermediateBlockSetting: {
      screen_type: interblockTypes[data.type],
      show_question_number: data.showNumber ? 1 : 0,
      text: data.text,
      complaint_button: data.hasComplainButton ? 1 : 0,
      unsubscribe_button: data.hasUnsubscribeButton ? 1 : 0,
      close_widget_button: data.hasCloseButtonForWidget ? 1 : 0,
      complaint_button_text: texts.complain,
      unsubscribe_button_text: texts.unsubscribe,
      poll_button_text: texts.takeSurvey,
      close_widget_button_text: texts.closeButtonForWidget ?? '',

      ready_button: data.hasReadyButton ? 1 : 0,
      ready_button_text: texts.ready,
      start_over_button: data.hasStartOverButton ? 1 : 0,
      start_over_button_text: texts.startOver,
      external_link: data.readyLink,

      scores_button: data.hasPointsButton ? 1 : 0,
      scores_button_text: texts.points,

      code: promocode.name,
      pool_id: promocode.poolId,

      logos_backcolor: data.imagesBackground,
      agreement: data.agreement ? 1 : 0,
      agreement_text: data.agreementText,
      
      show_image_button_text: data.show_image_button_text,
      image_show_time: data.image_show_time,
      show_bg_instruction: data.show_bg_instruction ? 1 : 0
    },
    IntermediateBlockNetworks: {
      social_networks_enabled: data.enableShare ? 1 : 0,

      ...share
    },
    
  };
}
