export default function (data, type) {



  const minValue = data.scaleVariants.minValue || 0
  const maxValue = data.scaleVariants.maxValue || 100
  const stepValue = data.scaleVariants.stepValue || 10

  let previewData = {
    commentEnabled: data.scaleCommentEnabled ? 1 : 0,
    textFieldParam: {
      min: data.scaleCommentLengthRange[0],
      max: data.scaleCommentLengthRange[1],
    },
    placeholderText: data.scaleCommentPlaceholder,
    comment_label: data.scaleCommentLabel,
    comment_required: data.galleryCommentRequaired,

    enableGallery: data.scaleGalleryEnabled ? 1 : 0,
    gallery: data.scaleGallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    skip_variant: data.skipVariant ? 1 : 0,

    scaleVariants: { minValue, maxValue, stepValue },
    scaleRatingSetting: {
      end: maxValue,
      start: minValue,
      step: stepValue,
    },

    set_variants: data.scaleType === 'variants' ? 1 : 0,

  };

  const isVuePreview = type === 'vue'

  previewData.random_variants_order = data.randomOrder ? 1 : 0;
  let variants = data.variants
    .map((v) => {
      const hasElementName = v.elementName && v.elementName.length
      const res = {
        id: Math.random(1, 999),
        persistentId: v.persistentId || v.id,
        value: v.value,
        points: v.points,
      };

      if (isVuePreview && data.isDonorClassifierWithListType) {
        res.value = v.value.split('/').pop()
      }

      return res
    })
    .filter((v) => v.value);

  if (data.randomOrder) {
    variants = _.shuffle(variants);
  }

  previewData.variants = variants;

  return previewData;
}
