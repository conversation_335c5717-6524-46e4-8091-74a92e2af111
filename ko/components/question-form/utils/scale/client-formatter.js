export default function (data, mode) {

  let start = 0
  let end = 100
  let step = 10
  if (data.scaleRatingSetting) {
    start = data.scaleRatingSetting.start || 0
    end = data.scaleRatingSetting.end || 100
    step = data.scaleRatingSetting.step || 10
  } else if (data.scaleSetting) {
    start = data.scaleSetting.start || 0
    end = data.scaleSetting.end || 100
    step = data.scaleSetting.step || 10
  }

  let variants = (mode === "cpoint" ? data.variants : data.detail_answers).filter(v => !v.is_deleted).map(
    (v) => {
      return {
        id: v.id,
        donorId: v.question_detail_id,
        donorVariantId: v.question_detail_id,
        value: v.variant,
        points: v.points,
        position: v.position,
        needExtra: v.need_extra,
        dictionary_element_id: v.dictionary_element_id,
      };
    }
  );

  variants.sort((a, b) => {
    return a.position - b.position;
  });

  let enableComment = mode === "cpoint" ? data.commentEnabled : data.comment_enabled || data.is_self_answer;
  let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;


  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  return {
    scaleGallery: gallery,
    scaleGalleryEnabled: gallery.length > 0,

    scaleCommentEnabled: enableComment,
    scaleCommentLengthRange: commentLengthRange,
    scaleCommentPlaceholder: placeholder,
    scaleCommentLabel: data.comment_label,
    scaleCommentRequired: data.comment_required,

    scaleType: data.set_variants ? "variants" : "standart",
    scaleVariants: {
      minValue: start, maxValue: end, stepValue: step
    },

    variants: variants,
    randomOrder: data.random_variants_order,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || "",
    skipVariant: data.skip_variant ? 1 : 0,

    donorId: data.donor,
    donorVariantsType: data.donor_chosen === 1 ? 1 : 0
  };
};
