export default function (data) {
  const minValue = data.scaleVariants.minValue || 0;
  const maxValue = data.scaleVariants.maxValue || 100;
  const stepValue = data.scaleVariants.stepValue || 10;

  let question = {
    commentEnabled: data.scaleCommentEnabled ? 1 : 0,
    placeholderText: data.scaleCommentPlaceholder,
    commentLengthRange: data.scaleCommentLengthRange,
    comment_label: data.scaleCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.scaleGalleryEnabled,
    gallery: data.scaleGallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.scaleGallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    skip_variant: data.skipVariant ? 1 : 0,

    set_variants: data.scaleType === "variants" ? 1 : 0,

    scaleQuestion: {
      start: minValue,
      end: maxValue,
      step: stepValue,
    },

  };
  question.variants = data.variants.map((v) => {
    return {
      id: v.id,
      variant: v.value,
      points: v.points
    };
  });
  question.random_variants_order = data.randomOrder ? 1 : 0;

  return question;
}
