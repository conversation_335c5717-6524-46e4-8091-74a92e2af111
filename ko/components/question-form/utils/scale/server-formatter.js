export default function (data) {

  const minValue = data.scaleVariants.minValue || 0;
  const maxValue = data.scaleVariants.maxValue || 100;
  const stepValue = data.scaleVariants.stepValue || 10;

  let question = {
    comment_enabled: data.scaleCommentEnabled ? 1 : 0,
    placeholder_text: data.scaleCommentPlaceholder,
    comment_minlength: data.scaleCommentLengthRange[0],
    comment_maxlength: data.scaleCommentLengthRange[1],
    comment_label: data.scaleCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.scaleGalleryEnabled,
    gallery: data.scaleGallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    skip_variant: data.skipVariant ? 1 : 0,

    set_variants: data.scaleType === "variants" ? 1 : 0,

    scaleQuestion: {
      start: minValue,
      end: maxValue,
      step: stepValue,
    },
  };

  question.random_variants_order = data.randomOrder ? 1 : 0;

  if (data.useDonor) {
    question.donor = data.donorId;
    question.donor_chosen = data.donorVariantsType == 1 ? 1 : 0;
  } else {
    question.donor = null;
  }
  return question;
}
