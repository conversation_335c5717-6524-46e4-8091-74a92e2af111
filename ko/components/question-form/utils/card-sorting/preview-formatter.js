export default function (data, formData) {
  let previewData = {
    comment_enabled: data.commentEnabled ? 1 : 0,
    comment_required: data.commentIsRequired ? 1 : 0,
    placeholder_text: data.galleryCommentPlaceholder,
    comment_label: data.galleryCommentLabel,
    comment_minlength: data.galleryCommentLengthRange[0],
    comment_maxlength: data.galleryCommentLengthRange[1],
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    card_column_text: data.cardColumnText,
    category_column_text: data.categoryColumnText,
    random_variants_order: data.randomCardOrder ? 1 : 0,
    random_categories_order: data.randomCategoryOrder ? 1 : 0,
    min_choose_variants: data.minCardInCategory,
    max_choose_variants: data.maxCardInCategory,
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    
    enableGallery: data.galleryEnabled,
    gallery: data.gallery.map((v) => {
      return {
        id: Math.random(1, 999),
        url: v.url,
        persistentId: v.persistentId,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),
  };

  previewData.variants = data.variants
    .reduce((acc, {id, value}, i) => {
      const items = [
        {
          id,
          value,
          position: i + 1,
        },
      ];
      return [...acc, ...items];
    }, []);

  previewData.cardSortingCategories = data.categoryList
    .reduce((acc, {id, value}, i) => {
      const items = [
        {
          id,
          value,
          position: i + 1,
        },
      ];
      return [...acc, ...items];
    }, []);

  return previewData;
}
