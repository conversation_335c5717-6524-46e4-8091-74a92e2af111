export default function (data, mode) {
  let categoryList = data.cardSortingCategories
    .filter((v) => !v.is_deleted)
    .map((v) => {
      if (v.comment_required) {
        requiredComments.push(v.id)
      }

      if (v.type == 1) {
        emptyVariants.push(v.id)
      }

      return {
        id: v.id,
        donorId: v.question_detail_id,
        value: v.name,
        points: v.without_points ? '-' : v.points,
        needExtra: v.need_extra,
        dictionary_element_id: v.dictionary_element_id,
        type: v.type,
        file_url: v.file_url,
        preview_url: v.preview_url !== '/uploads/' ? v.preview_url : '/img/audio-file-back.png',
        file_id: v.file_id,
        description: v.description,
        position: v.position,
      };
    })
    .sort((a, b) => {
      return a.position - b.position;
    });

  let variants = data.detail_answers
    .filter((v) => !v.is_deleted)
    .map((v) => {
      if (v.comment_required) {
        requiredComments.push(v.id)
      }

      if (v.type == 1) {
        emptyVariants.push(v.id)
      }

      return {
        id: v.id,
        donorId: v.question_detail_id,
        value: v.variant,
        points: v.without_points ? '-' : v.points,
        needExtra: v.need_extra,
        dictionary_element_id: v.dictionary_element_id,
        type: v.type,
        file_url: v.file_url,
        preview_url: v.preview_url !== '/uploads/' ? v.preview_url : '/img/audio-file-back.png',
        file_id: v.file_id,
        description: v.description,
        position: v.position,
      };
    });

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  return {
    gallery: gallery,
    galleryEnabled: gallery.length > 0,

    commentEnabled: !!data.comment_enabled,
    commentIsRequired: !!data.comment_required,
    commentPlaceholder: data.placeholderText,
    commentLengthRange: [data.comment_minlength, data.comment_maxlength],
    commentLabel: data.comment_label,

    galleryCommentEnabled: !!data.comment_enabled || !!data.is_self_answer,
    galleryCommentLengthRange: [data.comment_minlength, data.comment_maxlength],
    galleryCommentPlaceholder: data.placeholderText,
    galleryCommentLabel: data.comment_label,
    galleryCommentRequired: data.comment_required,
    
    skip: !!data.skip,
    skipText: data.skip_text || '',
    maxCardInCategory: data.max_choose_variants,
    minCardInCategory: data.min_choose_variants,
    cardColumnText: data.card_column_text,
    categoryColumnText: data.category_column_text,
    randomCategoryOrder: !!data.random_categories_order,
    randomCardOrder: !!data.random_variants_order,
    categoryList,
    variants,
  };
}
