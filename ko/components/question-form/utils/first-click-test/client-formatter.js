export default function (data, mode) {
  console.log("1-click-test: client-formatter start", data);

  // Parse click areas
  let clickAreas = [];
  // The server now sends areas in firstClickArea
  const serverAreas = data.firstClickArea || data.click_areas; // Keep data.click_areas for backward compatibility if any old data source uses it

  if (serverAreas) {
    try {
      const areas = typeof serverAreas === 'string'
        ? JSON.parse(serverAreas) // Should not be a string based on Postman, but good to be safe
        : serverAreas;

      if (Array.isArray(areas)) {
        clickAreas = areas.map(area => ({
          id: area.id, // Keep the ID from the server
          name: area.name || "",
          x: parseFloat(area.x_coord) || 0, // Map from x_coord
          y: parseFloat(area.y_coord) || 0, // Map from y_coord
          width: parseFloat(area.width) || 0,
          height: parseFloat(area.height) || 0,
          // clickCount might come from stats, not directly from question data
        }));
      }
    } catch (e) {
      console.warn("Failed to parse click areas:", e);
      clickAreas = [];
    }
  }

  // Parse gallery data
  let gallery = [];
  if (data.gallery && Array.isArray(data.gallery)) {
    gallery = data.gallery.map((v, i) => {
      return {
        id: v.id,
        mediaId: v.id,
        description: v.description || "",
        preview: v.poster || v.url,
        url: v.url,
        src: v.src,
        position: 'position' in v ? v.position : i,
      };
    });
  }

  const firstClick = data.firstClick || {};

  const result = {
    // Gallery data (inherited from GalleryQuestion)
    gallery: gallery,

    // Legacy image data (for backward compatibility)
    imageId: data.image_file_id || null,
    imageUrl: data.image_url || "",
    imagePreview: data.image_url || "",
    clickAreas: clickAreas,

    // First Click Test settings
    mobileDisplay: firstClick.mobile_view === 1 ? "height" : "width",
    minClicks: parseInt(firstClick.min_click) || 1,
    maxClicks: parseInt(firstClick.max_click) || null,
    displayTime: parseInt(firstClick.show_time) || null,
    buttonText: firstClick.button_text || "",
    allowClickCancel: firstClick.allow_cancel_click === 1 ? true : false,

    skip: data.skip || false,
    skipText: data.skip_text || "",

    galleryCommentEnabled: data.comment_enabled || false,
    galleryCommentLabel: data.comment_label || "",
    galleryCommentPlaceholder: data.placeholder_text || "",
    galleryCommentLengthRange: [data.comment_minlength || 0, data.comment_maxlength || 1000],
    galleryCommentRequired: data.comment_required || false,
  };

  console.log("1-click-test: client-formatter", result);

  return result;
}
