export default function (data) {
  let question = {};

  // Gallery data (inherited from GalleryQuestion)
  question.galleryEnabled = data.gallery && data.gallery.length > 0;
  question.gallery = data.gallery
    .filter((v) => v.mediaId)
    .map((v) => {
      return {
        id: v.mediaId,
        description: v.description || "",
      };
    });
  question.files = data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId);

  // Legacy image data (for backward compatibility)
  question.imageId = data.imageId || null;
  question.imageUrl = data.imageUrl || "";

  // Click areas
  question.clickAreas = data.clickAreas || [];

  // First Click Test settings
  question.mobileDisplay = data.mobileDisplay || "width";
  question.minClicks = parseInt(data.minClicks) || 1;
  question.maxClicks = data.maxClicks ? parseInt(data.maxClicks) : null;
  question.displayTime = data.displayTime ? parseInt(data.displayTime) : null;
  question.buttonText = data.buttonText || "";
  question.allowClickCancel = data.allowClickCancel || false;

  // Standard options - use standard property names
  question.skip = data.skip ? 1 : 0;
  question.skipText = data.skipText || '';
  
  // Comment options from GalleryQuestion
  question.commentEnabled = data.galleryCommentEnabled || false;
  question.placeholderText = data.galleryCommentPlaceholder || '';
  question.commentLengthRange = data.galleryCommentLengthRange || [0, 1000];
  question.comment_label = data.galleryCommentLabel || '';
  question.comment_required = data.galleryCommentRequaired ? 1 : 0;

  return question;
}
