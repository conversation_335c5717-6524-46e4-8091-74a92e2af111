export default function (data) {
  let question = {};

  question.enableGallery = data.gallery && data.gallery.length > 0 ? 1 : 0;
  question.gallery = data.gallery
    .filter((v) => v.mediaId)
    .map((v) => {
      return {
        id: v.mediaId,
        description: v.description || "",
      };
    });

  if (question.gallery && question.gallery.length > 0) {
    // leave only first image
    question.gallery = question.gallery.slice(0, 1);
  }

  if (data.clickAreas && data.clickAreas.length > 0) {
    question.firstClickArea = data.clickAreas.map((area, index) => {
      const serverId =
        typeof area.id === "string" && area.id.startsWith("area_") ? 0 : area.id || 0;
      return {
        id: serverId,
        name: area.name || `Область ${index + 1}`,
        x_coord: area.x || 0,
        y_coord: area.y || 0,
        width: area.width || 0,
        height: area.height || 0,
      };
    });
  } else {
    question.firstClickArea = [];
  }

  question.firstClick = {};

  question.firstClick.mobile_view = data.mobileDisplay === "height" ? "1" : "0";
  question.firstClick.min_click = parseInt(data.minClicks) || 1;
  question.firstClick.max_click = data.maxClicks ? parseInt(data.maxClicks) : "";
  question.firstClick.show_time = data.displayTime ? parseInt(data.displayTime) : "";
  question.firstClick.button_text = data.buttonText || "";
  question.firstClick.allow_cancel_click = data.allowClickCancel ? "1" : "0";

  question.skip = data.skip ? 1 : 0;
  question.skip_text = data.skipText || "";

  question.comment_enabled = data.galleryCommentEnabled ? 1 : 0;
  question.placeholder_text = data.galleryCommentPlaceholder || "";
  question.comment_label = data.galleryCommentLabel || "";
  question.comment_minlength = data.galleryCommentLengthRange
    ? data.galleryCommentLengthRange[0]
    : 0;
  question.comment_maxlength = data.galleryCommentLengthRange
    ? data.galleryCommentLengthRange[1]
    : 1000;
  question.comment_required = data.galleryCommentRequaired ? 1 : 0;

  Object.keys(question).forEach((key) => {
    if (question[key] === "" && key !== "button_text" && key !== "skip_text") {
      question[key] = null;
    }
  });

  console.log("DEBUG: question", question);

  return question;
}
