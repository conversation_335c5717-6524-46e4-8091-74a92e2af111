export default function (data, type) {
  let previewData = {
    max_choose_variants: data.variantsAnswersCountLimit,
    min_choose_variants: data.variantsAnswersCountLimitMin,

    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
    self_variant_nothing: data.selfVariantNothing ? 1 : 0,
    show_tooltips: data.show_tooltips ? 1 : 0,
  };


  previewData.textFieldParam = {
    min: data.variantsCustomAnswerLengthRange[0],
    max: data.variantsCustomAnswerLengthRange[1],
  };
  previewData.placeholderText = data.galleryCommentPlaceholder;
  previewData.self_variant_text = data.customAnswerLabel;
  previewData.variants_with_files = data.enableFile ? 1 : 0;
  previewData.self_variant_description = data.selfVariantDescription;

  previewData.isHaveCustomField = data.variantsCustomAnswerEnabled;
  previewData.variantsType = data.variantsType;

  previewData.dropdownVariants = data.dropdown;

  previewData.random_variants_order = data.randomOrder ? 1 : 0;

  const requiredComments = data.requiredComments || []

  let filteredVariants = data.variants.map((v) => {

    // @NOTE: id варианта-реципиента. Для опции "Комментарий обязателен для вариантов:"
    // нужно проверять оригинальный id варианта, а сейчас в `id` лежит id варианта-донора
    const recipientId = v.recipientId || v.persistentId || v.id
    

    const isVuePreview = type === 'vue'

    const res = {
      id: Math.random(1, 999),
      persistentId: v.persistentId || v.id,
      value: v.value,
      points: v.points,
      random_exclusion: v.random_exclusion,
      type: data.emptyVariants.includes(v.id) ? 1 : 0,
      description: v.description || '',
      comment_required: requiredComments.includes(recipientId),
    }

    if (isVuePreview && !res.persistentId) {
      res.id = 'is_self_answer'
      res.persistentId = 'is_self_answer'
    }

    if (isVuePreview && data.isDonorClassifierWithListType) {
      const val = v.value
      res.value = typeof val === 'string' ? val.split(' / ').pop() : ''
    }

    if (data.enableFile || data.donorEnableFile) {
      res.file = v.file
      res.file_id = v.file?.id || null;
      res.file_url= v.file?.fileUrl || null;
      res.preview_url= v.file?.previewUrl || null;
    }
    return res;
  })

  if (type !== 'vue') {
    filteredVariants = filteredVariants.filter(v => (v.value && v.value.length) || v.type === 1);
  }

  previewData.detail_answers = filteredVariants;
  previewData.comment_enabled = data.galleryCommentEnabled ? 1 : 0;
  previewData.placeholder_text = data.galleryCommentPlaceholder;
  previewData.comment_minlength = data.galleryCommentLengthRange ? data.galleryCommentLengthRange[0] : data.variantsCommentLengthRange[0];
  previewData.comment_maxlength = data.galleryCommentLengthRange ? data.galleryCommentLengthRange[1] : data.variantsCommentLengthRange[1];
  previewData.comment_label = data.galleryCommentLabel;
  previewData.comment_required = data.galleryCommentRequaired ? 1 : 0;
  previewData.selfVariantPlaceholderText = data.variantsCustomAnswerPlaceholder
  previewData.previewQuestions = window.QUESTIONS
  previewData.selfVariantCommentRequired = !data.selfVariantCommentRequired

  if (type === 'vue') {
    previewData.selfVariantCommentRequired = requiredComments.includes('is-self-answer')
    previewData.variants_with_files = (data.enableFile || data.donorEnableFile) ? 1 : 0;
  }

  if (data.useDonor) {
    previewData.donor = data.donorId;
  } else {
    previewData.donor = null;
  }

  return previewData;
}
