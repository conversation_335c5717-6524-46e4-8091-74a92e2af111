export default function (data, mode) {

  let enableSelfAnswer = parseInt(data.is_self_answer) == 1;
  let enableComment =
    mode === 'cpoint'
      ? data.commentEnabled
      : data.comment_enabled;
  let commentLengthRange =
    mode === 'cpoint'
      ? data.commentLengthRange
      : [data.comment_minlength, data.comment_maxlength];
  let commentPlaceholder = data.placeholderText;
  let variantsType =
    mode === "cpoint" ? data.variantsType : data.variants_element_type;
  let selfVariantLengthRange =
    mode === "cpoint"
      ? data.selfVariantLengthRange
      : [data.self_variant_minlength, data.self_variant_maxlength];
  let placeholder = data.selfVariantPlaceholderText;

  let requiredComments = []
  let emptyVariants = []

  if (data.self_variant_comment_required) {
    requiredComments.push('is-self-answer')
  }

  let variants = (mode === "cpoint" ? data.variants : data.detail_answers)
    .filter((v) => !v.is_deleted)
    .map((v) => {
      if (v.comment_required) {
        requiredComments.push(v.id)
      }

      if (v.type == 1) {
        emptyVariants.push(v.id)
      }

      return {
        id: v.id,
        donorId: v.question_detail_id,
        value: v.variant,
        points: v.without_points ? '-' : v.points,
        needExtra: v.need_extra,
        dictionary_element_id: v.dictionary_element_id,
        type: v.type,
        file_url: v.file_url,
        preview_url: v.preview_url !== '/uploads/' ? v.preview_url : '/img/audio-file-back.png',
        file_id: v.file_id,
        description: v.description,
        random_exclusion: v.random_exclusion,
      };
    });

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  return {
    gallery: gallery,
    testField: 1,
    galleryEnabled: gallery.length > 0,
    variantsCommentEnabled: enableComment,
    variantsCommentLengthRange: commentLengthRange,
    variantsCommentPlaceholder: commentPlaceholder,
    variantsCommentLabel: data.comment_label,
    variantsCommentRequired: data.comment_required,
    maxPointsCalcMethod: data.max_points_calc_method,
    enableFile: !!data.variants_with_files,
    requiredComments: requiredComments,
    emptyVariants: emptyVariants,


    variantsType: parseInt(variantsType),
    variants: variants,
    variantsCustomAnswerEnabled:
      mode === "cpoint"
        ? data.customVariantAvailable
        : enableSelfAnswer,
    variantsCustomAnswerLengthRange: selfVariantLengthRange,
    variantsCustomAnswerPlaceholder: placeholder,
    customAnswerLabel: data.self_variant_text || "",
    variantsAnswersCountLimit: data.max_choose_variants,
    variantsAnswersCountLimitMin: data.min_choose_variants,
    dropdown:
      mode === "cpoint" ? data.dropdownVariants : data.dropdown_variants,
    randomOrder: data.random_variants_order,

    donorId: data.donor,
    donorVariantsType: data.donor_chosen === 1 ? 1 : 0,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || "",
    selfVariantNothing: data.self_variant_nothing,
    showTooltips: data.show_tooltips,
    selfVariantDescription: data.self_variant_description,
  };
}
