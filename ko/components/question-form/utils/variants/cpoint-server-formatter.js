export default function (data) {

  let question = {
    max_choose_variants: data.variantsAnswersCountLimit,
    min_choose_variants: data.variantsAnswersCountLimitMin,
    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),
    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    self_variant_comment_required: data.requiredComments.includes("is-self-answer") && data.galleryCommentEnabled ? 1 : 0,
    self_variant_nothing: data.selfVariantNothing ? 1 : 0,
  };

  question.customVariantAvailable = data.variantsCustomAnswerEnabled;
  question.variantsType = data.variantsType;
  question.selfVariantLengthRange = data.variantsCustomAnswerLengthRange;
  question.self_variant_placeholder_text = data.variantsCustomAnswerPlaceholder;
  question.self_variant_text = data.customAnswerLabel;
  question.self_variant_description = data.selfVariantDescription;
  
  question.variants = data.variants.map((v) => {
    return {
      id: v.id,
      variant: v.value,
      points: v.points,
      type: data.emptyVariants.includes(v.id) ? 1 : 0,
      description: v.description,
    };
  });

  if (!data.comment.required && data.requiredComments.length) {

    question.variants.map(i => {
      if (i.type !== 1) {
        i.comment_required = data.requiredComments.includes(i.id) ? 1 : 0
      } else {
        i.comment_required = data.requiredComments.includes('0') ? 1 : 0
      }
      
      if (typeof i.id === 'string' && i.id.includes('new')) {
        i.id = '0'
      }
      
      
      
      return i
    })
  }

  question.dropdown_variants = data.dropdown ? 1 : 0;

  question.random_variants_order = data.randomOrder ? 1 : 0;
  question.show_tooltips = data.show_tooltips ? 1 : 0;

  let comment = data.comment;

  if (comment.enabled) {
    question.commentEnabled = true;
    question.placeholderText = comment.placeholder;
    question.commentLengthRange = comment.range;
    question.comment_label = comment.label;
    question.comment_required = comment.required ? 1 : 0;
  }

  return question;
}
