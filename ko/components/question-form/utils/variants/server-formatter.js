export default function (data) {

  let question = {
    max_choose_variants: data.variantsAnswersCountLimit,
    min_choose_variants: data.variantsAnswersCountLimitMin,
    self_variant_comment_required: data.requiredComments.includes("is-self-answer") && data.galleryCommentEnabled ? 1 : 0,
    self_variant_nothing: data.selfVariantNothing ? 1 : 0,

    enableGallery: data.galleryEnabled,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
    show_tooltips: data.show_tooltips ? 1 : 0,
  };

  if (data.enableFile) {
    data.variants.map(i => {
      i.file_id = i.file?.id || null
      return i
    })
  }

  data.variants.map(i => {
    if (typeof i.id === 'string' && i.id.includes('new')) {
      i.id = '0'
    }
    const normalizedId = i.id !== '0' ? i.id : i.persistentId;

    i.type = data.emptyVariants.includes(normalizedId) ? 1 : 0
    return i
  })

  if (!data.comment.required && data.requiredComments.length && data.galleryCommentEnabled) {
    data.variants.map(i => {
      const finalId = i.persistentId || i.id;
      i.comment_required = data.requiredComments.includes(finalId) ? 1 : 0

      if (typeof i.id === 'string' && i.id.includes('new')) {
        i.id = '0'
      }
      
      
      
      return i
    })
  }


  question.variants_with_files = data.enableFile ? 1 : 0;
  question.rating_type = data.variantsAssessmentType;
  question.is_self_answer = data.variantsCustomAnswerEnabled ? 1 : 0;
  question.variants_element_type = data.variantsType;
  question.self_variant_minlength = data.variantsCustomAnswerLengthRange[0];
  question.self_variant_maxlength = data.variantsCustomAnswerLengthRange[1];
  question.self_variant_placeholder_text = data.variantsCustomAnswerPlaceholder;
  question.self_variant_text = data.customAnswerLabel;
  question.self_variant_description = data.selfVariantDescription;

  question.comment_enabled = data.galleryCommentEnabled ? 1 : 0;
  question.placeholder_text = data.galleryCommentPlaceholder;
  question.comment_minlength = data.galleryCommentLengthRange[0];
  question.comment_maxlength = data.galleryCommentLengthRange[1];
  question.comment_label = data.galleryCommentLabel;
  question.comment_required = data.galleryCommentRequaired ? 1 : 0;

  question.dropdown_variants = data.dropdown ? 1 : 0;

  question.random_variants_order = data.randomOrder ? 1 : 0;
  question.max_points_calc_method = data.maxPointsCalcMethod ? 1 : 0;

  if (data.useDonor) {
    question.donor = data.donorId;
    question.donor_chosen = data.donorVariantsType == 1 ? 1 : 0;
  } else {
    question.donor = null;
  }

  return question;
}
