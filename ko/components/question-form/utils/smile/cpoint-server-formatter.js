export default function (data) {
  return {
    commentEnabled: data.smileCommentEnabled ? 1 : 0,
    placeholderText: data.smileCommentPlaceholder,
    commentLengthRange: data.smileCommentLengthRange,
    comment_label: data.smileCommentLabel,
    enableGallery: data.smileGalleryEnabled ? 1 : 0,
    comment_required: data.smileCommentRequired ? 1 : 0,
    gallery: data.smileGallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.smileGallery.filter((v) => v.mediaId).map((v) => v.mediaId),
    smileType: data.smileType,
    smilesCount: data.smilesCount,
    smiles: data.smiles.map((smile) => {
      const { previewUrl, ...params } = smile;
      return params;
    }),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',

    show_labels: data.showLabels ? 1 : 0
  };
}
