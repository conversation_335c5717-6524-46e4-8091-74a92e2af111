export default function(data, mode) {

  let enableComment = mode === "cpoint" ? data.commentEnabled : data.comment_enabled || data.is_self_answer;
  let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: 'position' in v ? v.position : i,
    };
  });

  gallery.sort((a,b) => {
    return a.position - b.position;
  });

  let smileType = mode === "cpoint" ? data.smileType : data.smile_type;
  if (!smileType) smileType == 'heart';

  let variants = data.detail_answers
  variants = variants
    .filter((v) => !v.is_deleted)
    .map((v) => {
      const variant = {
        id: v.id,
        donorId: v.question_detail_id,
        value: v.variant,
        points: v.points,
        needExtra: v.need_extra,
        dictionary_element_id: v.dictionary_element_id,
        extra_question: v.extra_question,
        ...v
      };
      return variant;
    });
  let clarifyingQuestion = {
    enabled: !!data.detail_question,
    forAllRates: data.for_all_rates,
    forRates: data.for_all_rates ? [] : [data.extra_question_rate_from, data.extra_question_rate_to],
    text: data.detail_question,
    customAnswerEnabled: !!data.is_self_answer,
    customAnswerLengthRange: [data.comment_minlength, data.comment_maxlength],
    customAnswerPlaceholder: data.placeholderText,
    customAnswerLabel:  data.self_variant_text || '',
    required: data.extra_required,
    enableFile: data.variants_with_files,
    minСhooseVariants: data.min_choose_extra_variants,
    maxСhooseVariants: data.max_choose_extra_variants,
    clarifyingQuestionIsRequired: data.extra_required,
    variantsType: data.variants_element_type,
    variants: variants.filter(el => el.extra_question),
  };

  return {
    smileGallery: gallery,
    smileGalleryEnabled: gallery.length > 0,

    smileCommentEnabled: enableComment,
    smileCommentLengthRange: commentLengthRange,
    smileCommentPlaceholder: placeholder,
    smileCommentLabel: data.comment_label,
    smileCommentRequired: data.comment_required,

    smiles: data.smiles,
    smileType: smileType,
    smilesCount: data.smiles.length,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',
    clarifyingQuestion: clarifyingQuestion,
    showLabels: data.show_labels ? 1 : 0,
    selfVariantFile: data.selfVariantFile
  };
}
