export default function (data) {
  console.log('4771 sf', data)
  const clarifyingQuestion = data.clarifyingQuestion
  const question = {
    comment_enabled: data.smileCommentEnabled ? 1 : 0,
    placeholder_text: data.smileCommentPlaceholder,
    comment_minlength: data.smileCommentLengthRange[0],
    comment_maxlength: data.smileCommentLengthRange[1],
    comment_label: data.smileCommentLabel,
    comment_required: data.smileCommentRequired ? 1 : 0,
    enableGallery: data.smileGalleryEnabled,
    gallery: data.smileGallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.smileGallery.filter((v) => v.mediaId).map((v) => v.mediaId),
    smile_type: data.smileType,
    smiles_count: data.smilesCount,
    smiles: data.smiles.map((smile) => {
      const { previewUrl, ...params } = smile;
      return params;
    }),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",

    show_labels: data.showLabels ? 1 : 0,
  };
  if (clarifyingQuestion.enabled) {
    question.extra_required = clarifyingQuestion.clarifyingQuestionIsRequired ? 1 : 0;
    question.detail_question = clarifyingQuestion.text;
    question.for_all_rates = clarifyingQuestion.forAllRates ? 1 : 0;
    question.variants_with_files = clarifyingQuestion.enableFile ? 1 : 0;
    question.self_variant_file_id = clarifyingQuestion.customAnswerFile?.id || null
    question.min_choose_extra_variants = clarifyingQuestion.minСhooseVariants || ''
    question.max_choose_extra_variants = clarifyingQuestion.maxСhooseVariants || ''

    if (!question.for_all_rates) {
      const [from, to] = clarifyingQuestion.forRates
      question.extra_question_rate_from = from || 0
      question.extra_question_rate_to = to || 10
    }

    question.variants_element_type = clarifyingQuestion.variantsType;

    question.is_self_answer = clarifyingQuestion.customAnswerEnabled ? 1 : 0;
    let customAnswerRange = clarifyingQuestion.customAnswerRange;
    question.comment_minlength = customAnswerRange[0];
    question.comment_maxlength = customAnswerRange[1];
    question.placeholder_text = clarifyingQuestion.customAnswerPlaceholder;
    question.self_variant_text = clarifyingQuestion.customAnswerLabel;
    question.extra_required = clarifyingQuestion.required ? 1 : 0;
  } else {
    question.detail_question = '';
  }

  return question;
}
