export default function (data, type) {
  const isVuePreview = type === 'vue'

  let previewData = {
    commentEnabled: data.smileCommentEnabled ? 1 : 0,
    textFieldParam: {
      min: data.smileCommentLengthRange[0],
      max: data.smileCommentLengthRange[1],
    },
    placeholderText: data.smileCommentPlaceholder,
    comment_label: data.smileCommentLabel,
    comment_required: data.smileCommentRequired,

    enableGallery: data.smileGalleryEnabled ? 1 : 0,
    gallery: data.smileGallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    smile_type: data.smileType || data.smile_type,
    smiles: data.smiles.map((v, index) => {
      return {
        id: v.id || index + 1,
        label: v.label,
        url: v.url || v.smile_url || v.previewUrl,
        smile_url: v.url || v.smile_url || v.previewUrl,
      };
    }),

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",

    show_labels: data.showLabels ? 1 : 0,
  };

  let variants = data?.clarifyingQuestion?.variants || []
    .map((v) => {
      const res = v
      res.id = Math.random(1, 999)

      if (isVuePreview && res.dictionary_element_id) {
        res.id = res.dictionary_element_id
      } else if (isVuePreview && res.persistentId) {
        res.id = res.persistentId || res.id
      }

      // @NOTE: needExtra приходит в качестве функции, для превью на Vue
      // мы используем postMessage, а он не поддерживает функции
      res.needExtra = v.need_extra

      return res
    })
    .filter((v) => v.value);

  previewData.clarifyingQuestion = data.clarifyingQuestion
  previewData.variants = variants;

  return previewData;
}
