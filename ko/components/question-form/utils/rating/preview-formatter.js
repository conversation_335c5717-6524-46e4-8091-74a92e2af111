export default function (data) {
  const { count } = data.starsConfig;
  let previewData = {
    stars: count - 1,

    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        persistentId: v.persistentId,
        url: v.url,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    starRatingOptions: {
      color: data.starsConfig.color,
      count,
      labelsArray: data.starsConfig.labels,
    },

    variants: [],

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
  };

  let clarifyingQuestion = data.clarifyingQuestion;
  let comment = data.comment;

  previewData.clarifyingQuestionEnabled = !!clarifyingQuestion.enabled;

  if (clarifyingQuestion.enabled) {
    previewData.forAllRates = clarifyingQuestion.forAllRates ? 1 : 0;
    previewData.answerText = clarifyingQuestion.text;
    previewData.variantsType = clarifyingQuestion.variantsType;
    previewData.isHaveCustomField = clarifyingQuestion.customAnswerEnabled;
    let range =
      clarifyingQuestion.customAnswerRange ||
      clarifyingQuestion.customAnswerLengthRange;
    previewData.textFieldParam = {
      min: range[0],
      max: range[1],
    };
    previewData.variants = clarifyingQuestion.variants.filter((v) => v.value).map((v, i) => {
      return {
        ...v,
        id: i + 1
      }
    });
    previewData.placeholderText = clarifyingQuestion.customAnswerPlaceholder;
    previewData.self_variant_text = clarifyingQuestion.customAnswerLabel;
    previewData.starRatingOptions.extra_question_rate_from = data.starsConfig.extra_question_rate_from;
    previewData.starRatingOptions.extra_question_rate_to = data.starsConfig.extra_question_rate_to;
    previewData.clarifyingQuestionRequired = !!clarifyingQuestion.required;
  }

  if (comment.enabled) {
    previewData.isHaveComment = true;
    let range = comment.range;
    previewData.textFieldParam = {
      min: range[0],
      max: range[1],
    };
    previewData.placeholderText = comment.placeholder;
    previewData.comment_label = comment.label;
    previewData.comment_required = comment.required;
  }

  return previewData;
}
