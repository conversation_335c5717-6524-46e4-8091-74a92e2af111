export default function (data) {

  const {
    dictionaryId,
    dictionaryItems,
    dropdown,
    listType,
    variantType,
    dictionarySort,
    maxVariantsCount,
    showTooltips,
    disableSelectCategory
  } = data;


  let question = {
    disable_select_category: disableSelectCategory ? 1 : 0,
    show_tooltips: showTooltips ? 1 : 0,
    commentEnabled: !!data.galleryCommentEnabled,
    placeholderText: data.galleryCommentPlaceholder,
    commentLengthRange: data.galleryCommentLengthRange,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    dictionary_id: dictionaryId,
    dictionaries: dictionaryItems,
    dictionary_list_type: listType,
    variantsType: variantType,
    dictionary_sort: dictionarySort,
    max_choose_variants: maxVariantsCount,
    dropdown_variants: dropdown ? 1 : 0,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
  };



  return question;
}
