export default function (data) {
  const {
    dictionaryId,
    dictionaryItems,
    dropdown,
    listType,
    variantType,
    dictionarySort,
    maxVariantsCount,
    showTooltips,
    disableSelectCategory
  } = data;

  let question = {
    disable_select_category: disableSelectCategory ? 1 : 0,
    show_tooltips: showTooltips ? 1 : 0,
    comment_enabled: data.galleryCommentEnabled ? 1 : 0,
    placeholder_text: data.galleryCommentPlaceholder,
    comment_label: data.galleryCommentLabel,
    comment_minlength: data.galleryCommentLengthRange[0],
    comment_maxlength: data.galleryCommentLengthRange[1],
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    enableGallery: data.galleryEnabled,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),

    dictionary_id: dictionaryId,
    dictionaries: dictionaryItems,
    dictionary_list_type: listType,
    variants_element_type: variantType,
    dictionary_sort: dictionarySort,
    max_choose_variants: maxVariantsCount,
    dropdown_variants: dropdown ? 1 : 0,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',
  };

  return question;
}
