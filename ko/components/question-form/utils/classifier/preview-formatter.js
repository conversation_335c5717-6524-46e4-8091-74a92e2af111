export default function (data) {
  
  let previewData = {
    commentEnabled: data.galleryCommentEnabled ? 1 : 0,
    show_tooltips: data.showTooltips ? 1 : 0,
    textFieldParam: {
      min: data.galleryCommentLengthRange[0],
      max: data.galleryCommentLengthRange[1],
    },
    placeholderText: data.galleryCommentPlaceholder,
    comment_label: data.galleryCommentLabel,
    comment_required: data.galleryCommentRequaired,

    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery.map((v, index) => {
      return {
        id: Math.random(1, 999),
        url: v.url,
        persistentId: v.persistentId,
        preview: v.preview,
        poster: v.preview,
        description: v.description,
      };
    }),

    dropdownVariants: data.dropdown,
    variantsType: data.variantType,
    max_choose_variants: data.maxVariantsCount,
    dictionary_id: data.dictionaryId,
    variants: (data.dictionaryItems || []).map((id) => ({ id })),
    selectedDictionaryTree: data.selectedDictionaryTree || {},
    dictionary_list_type: data.listType,
    dictionary_sort: data.dictionarySort,
    disable_select_category: data.disableSelectCategory,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",
  };

  return previewData;
}
