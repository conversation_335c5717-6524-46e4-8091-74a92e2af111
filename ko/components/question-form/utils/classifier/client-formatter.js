export default function (data) {

  let enableComment = data.comment_enabled || data.is_self_answer;
  let commentLengthRange = [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;

  let dictionaryId = data.dictionary_id ? `${data.dictionary_id}` : "";
  let dictionaryItems = (data.detail_answers || []).map((item) => item.id);
  let listType = data.dictionary_list_type;
  let variantType = data.variants_element_type;
  let sortType = data.dictionary_sort;

  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  let variants = data.detail_answers
    .filter((v) => !v.is_deleted)
    .map((v) => {
      return {
        id: v.id,
        value: v.path,
        description: v.description
      };
    });

  return {
    gallery: gallery,
    galleryEnabled: gallery.length > 0,

    galleryCommentEnabled: enableComment,
    galleryCommentLengthRange: commentLengthRange,
    galleryCommentPlaceholder: placeholder,
    galleryCommentLabel: data.comment_label,
    galleryCommentRequired: data.comment_required,

    dictionaryId,
    dictionaryItems,
    dropdown: data.dropdown_variants,
    listType,
    variantType: parseInt(variantType),
    sortType,
    maxVariantsCount: data.max_choose_variants,
    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',
    showTooltips: data.show_tooltips,
    disableSelectCategory: data.disable_select_category,
    variants
  };
}
