import { formatters } from "./formatters";

/**
 * @description Форматирует данные для превью вопроса
 * @param {object} data - Данные вопроса
 * @param {'default' | 'vue'} type - Тип превью. По умолчанию 'default'
 * Для превью на Vue добавляется дополнительная обработка и добавление некоторых полей
 * @returns {object}
 */
export function questionPreviewFormatter(data, type = 'default') {
  console.log('questionPreviewFormatter', data, type)

  let previewData = {
    type: parseInt(data.type),
  };
  ["alias", "name", "description", "id"].forEach((field) => {
    previewData[field] = data[field];
  });

  previewData.description_html = data.description;
  previewData.sub_description = data.subdescription;
  previewData.isRequired = data.required;
  previewData.showQuestion = true;
  previewData.pointId = data.pointId;

  let formatter = formatters[data.type];
  previewData = {
    ...previewData,
    ...formatter.preview(data, type),
  };

  return previewData;
}
