import { clientMaskFormatter } from "../mask-formatter";

export default function(data, mode) {
  return {
    quizzes: data.quizzes.map(q => {
      let isTextarea = mode === "cpoint" ? parseInt(q.variantsType) === 1 : q.isTextarea;

      return {
        id: q.id,
        label: mode === "cpoint" ? q.name : q.label,
        required: mode === "cpoint" ? q.required : q.isRequired,
        isTextarea,
        maskType: q.maskType || 0,
        maskConfig: clientMaskFormatter(q.maskConfig, mode),
        placeholder: q.placeholderText,
        lengthRange: mode === "cpoint" ?
          isTextarea ?
            q.customVariantLengthRange :
            q.customVariantLengthRangeShort :
          [q.textFieldParam.min, q.textFieldParam.max],
        linkWithClientField: parseInt(q.linkWithClientField),
        linkedField: q.linkedClientField,
        rewriteExistLink: parseInt(q.rewriteLinkedField),
      };
    }),
  };
}
