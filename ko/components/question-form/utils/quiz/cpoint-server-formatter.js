import { serverMaskFormatter } from '../mask-formatter';
import { cPointServerLinkFormatter } from '../link-formatter';

export default function (data) {
  let question = {};
  question.quizzes = data.quizzes.map((q) => {
    const result = {
      id: q.id,
      name: q.label,
      required: q.required,
      maskType: q.maskType,
      maskConfig: serverMaskFormatter(q.maskConfig),
      variantsType: q.isTextarea ? 1 : 0,
      ...cPointServerLinkFormatter(q),
      placeholderText: q.placeholder || '',
    };
    if (q.isTextarea) {
      result.customVariantLengthRange = q.lengthRange;
    } else {
      result.customVariantLengthRangeShort = q.lengthRange;
    }
    return result;
  });

  return question;
}
