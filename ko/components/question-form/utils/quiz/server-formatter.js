import { serverMaskFormatter } from '../mask-formatter';
import { serverLinkFormatter } from '../link-formatter';

export default function (data) {
  let question = {};

  question.quizzes = data.quizzes.map((q) => {
    return {
      id: q.id,
      name: q.label,
      is_required: q.required ? 1 : 0,
      mask_type: q.maskType,
      mask_config: serverMaskFormatter(q.maskConfig),
      variants_type: q.isTextarea ? 1 : 0,
      comment_minlength: q.lengthRange[0],
      comment_maxlength: q.lengthRange[1],
      ...serverLinkFormatter(q),
      placeholder_text: q.placeholder || '',
    };
  });

  return question;
}
