import { previewMaskFormatter } from '../mask-formatter';
import { previewLinkFormatter } from '../link-formatter';

let unique = 1;

export default function (data) {
  let previewData = {};


  previewData.values = data.quizzes.map((q) => {
    return {
      id: unique++,
      persistentId: q.persistentId,
      label: q.label,
      isRequired: q.required,
      maskType: q.maskType,
      maskConfig: previewMaskFormatter(q.maskConfig),
      isTextarea: q.isTextarea,
      textFieldParam: {
        min: q.lengthRange[0],
        max: q.lengthRange[1],
      },
      placeholderText: q.placeholder,
      ...previewLinkFormatter(q),
    };
  });

  console.log({ data, previewData })

  return previewData;
}
