<!-- ko let: { $ctx: $component }-->

<!-- ko if: isSortable && !isFullBlocked -->
<div
  class="question-form-variant__drag-handle sortable-handle hide-on-dragging"
>
  <i class="icon icon-drag-arrow"></i>
</div>
<!-- /ko -->

<div class="question-form-variant__content hide-on-dragging">
  <!-- ko let: { fileInput: ko.observable(null) } -->
  <input
    type="file"
    data-bind="
          element: fileInput,
          attr: {
            accept: variant.accept,
          },
          event: {
              change: function (_, event) {
                  const file = event.target.files[0];
                  variant.addByFile(file);
                  event.target.value = '';
              }
          }"
    hidden
  />

  <!-- ko ifnot: variant.isSelected -->
  <div>
    <button
      class="f-btn"
      data-bind="click: function () { $(fileInput()).trigger('click'); }"
    >
      <span class="f-btn-prepend">
        <svg-icon params="name: 'clip'"></svg-icon>
      </span>
      <span data-bind="text: $component.translator.t('С компьютера')"></span>
    </button>

    <!-- ko if: variant.mediaType() == 'video' -->
    <button
      class="ml-2 btn btn-default btn-with-icon btn-upload-link survey-question__media-form-control-actions-item"
      data-bind="
      click: function () { variant.addByYoutubeLink(); }"
      type="button"
    >
      <span
        data-bind="text: $component.translator.t('По ссылке на Youtube')"
      ></span>
    </button>
    <!-- /ko -->
  </div>

  <validation-feedback
    params="show: !variant.fileSize.isValid(), text: variant.fileSize.error"
  ></validation-feedback>
  <!-- /ko -->

  <!-- ko if: variant.isSelected -->
  <div class="d-flex">
    <div class="media-preview mr-4">
      <!-- ko if: variant.isLoading -->
      <div class="media-preview__loader"></div>
      <!-- /ko -->

      <!-- ko if: variant.isLoaded -->
      <img
        data-bind="
        attr: { src: variant.getPreview() },
        fancyboxGalleryItem: {
          gallery: $component.getGalleryConfig(),
          index: $index()
        }"
      />

      <!-- ko ifnot: $component.isFullBlocked -->
      <button
        class="media-preview__remove"
        data-bind="click: function (_, e) {
        variant.removeMedia($data);
      }, attr: { title: $component.translator.t('Удалить')}"
        type="button"
      >
        <i class="fas fa-times"></i>
      </button>
      <!-- /ko -->
      <!-- /ko -->

      <!-- ko if: !variant.isLoaded() && !variant.isLoading() -->

      <div
        class="media-preview__load"
        data-bind="click: function () { $(fileInput()).trigger('click'); }"
      >
        <!-- ko if: variant.type == 'image' -->
        <i class="i-icon-photo"></i>
        <!-- /ko -->
        <!-- ko if: variant.type == 'video' -->
        <i class="i-icon-video"></i>
        <!-- /ko -->
        <!-- ko if: variant.type == 'mix' -->
        <i class="i-icon-photovideo"></i>
        <!-- /ko -->
      </div>

      <!-- /ko -->
    </div>

    <div class="question-form-media-variant__description">
      <foquz-chars-counter
        style="height: 100%"
        params="value: variant.description, max: 500"
      >
        <textarea
          class="form-control"
          data-bind="
            textInput: variant.description,
            autosizeTextarea, minHeight: 94, disable: $ctx.isFullBlocked"
        ></textarea>
      </foquz-chars-counter>
    </div>

    <!-- ko if: withPoints -->
    <input
      type="text"
      class="question-form-variant__points form-control"
      maxlength="9"
      data-bind="textInput: variant.points, onlyNumbers: { sign: true }, disable: $ctx.isFullBlocked, attr: {
             placeholder: $component.translator.t('0 баллов')
           }"
    />
    <!-- /ko -->
  </div>
  <!-- /ko -->

  <validation-feedback
    params="show: $component.formControlErrorStateMatcher(variant.url), text: variant.url.error()"
  >
  </validation-feedback>
  <!-- /ko -->
</div>

<!-- ko template: {
    foreach: templateIf(!isFullBlocked && canRemove() && (!isBlocked || variant.id == '0'), $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->

<button
  type="submit"
  class="question-form-variant__remove hide-on-dragging f-btn f-btn-danger f-btn--square"
  data-bind="click: function() { onRemove() }, attr: {
          title: $component.translator.t('Удалить')
        }"
>
  <foquz-icon params="icon: 'times'" class="f-icon-sm"></foquz-icon>
</button>

<!-- /ko -->
<!-- /ko -->
