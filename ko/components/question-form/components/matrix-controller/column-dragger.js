function createColumnGhost(columnId, columnHeader, helper) {
  let ghostTable = document.createElement("table");
  ghostTable.classList.add("matrix-ghost");
  ghostTable.classList.add("matrix-table");
  ghostTable.classList.add("matrix-ghost-column");

  let srcStyle = document.defaultView.getComputedStyle(columnHeader);
  ghostTable.style.width = srcStyle.getPropertyValue("width");

  let ghostTHead = document.createElement("thead");
  let ghostHeader = columnHeader.cloneNode(true);
  ghostTHead.appendChild(ghostHeader);
  ghostTable.appendChild(ghostTHead);

  var ghostTBody = document.createElement("tbody");

  helper.columnsTableRows.forEach((row) => {
    let ghostRow = document.createElement("tr");
    let column = helper.getColumnInRow(columnId, row);
    let ghostColumn = column.cloneNode(true);
    ghostRow.appendChild(ghostColumn);
    ghostTBody.appendChild(ghostRow);
  });

  ghostTable.appendChild(ghostTBody);

  ghostTable.style.position = "absolute";
  ghostTable.style.top = "-1500px";
  ghostTable.style.background = "white";

  document.body.appendChild(ghostTable);

  return ghostTable;
}

// Начало перетаскивания
function onDragStart(event, helper) {
  let columnHeader = event.currentTarget;
  event.dataTransfer.effectAllowed = "move";

  // ID перетаскиваемой колонки
  let columnId = helper.getColumnId(columnHeader);
  event.dataTransfer.setData("columnId", columnId);

  // Копия колонки, которая будет около курсора
  let columnGhost = createColumnGhost(columnId, columnHeader, helper);
  event.dataTransfer.setDragImage(columnGhost, 0, 0);

  // Отметить заголовок колонки
  columnHeader.classList.add("matrix-column-header--dragging");

  // Отметить все ячейки перетаскиваемой колонки
  helper.columnsTableRows.forEach((row) => {
    let srcCol = helper.getColumnInRow(columnId, row);
    srcCol.classList.add("matrix-column-cell--dragging");
  });
}

// Прохождение курсора над заголовком колонки
function onDragOver(event, helper) {
  // Разрешить вставку
  if (event.preventDefault) {
    event.preventDefault();
  }

  // Заголовок перемещаемой колонки
  const activeColumnHeader = document.querySelector(
    ".matrix-column-header--dragging"
  );

  // Заголовок, над которым проходит курсор
  const targetColumnHeader = event.currentTarget;

  if (activeColumnHeader == targetColumnHeader) {
    return;
  }

  // Определить место для вставки перетаскиваемого заголовка (зависит от позиции курсора)
  const targetCoords = targetColumnHeader.getBoundingClientRect();
  const targetCenter = targetCoords.x + targetCoords.width / 2;
  const cursorPosition = event.clientX;

  let targetColumnId = helper.getColumnId(targetColumnHeader);
  let activeColumnId = helper.getColumnId(activeColumnHeader);

  if (cursorPosition >= targetCenter) {
    if (activeColumnHeader === targetColumnHeader.nextElementSibling) return;

    $(activeColumnHeader).insertAfter(targetColumnHeader);

    helper.columnsTableRows.forEach((row) => {
      let srcCol = helper.getColumnInRow(activeColumnId, row);

      if (activeColumnId !== targetColumnId) {
        let destCol = helper.getColumnInRow(targetColumnId, row);
        $(srcCol).insertAfter(destCol);
      }
    });
  } else {
    if (activeColumnHeader === targetColumnHeader.previousElementSibling)
      return;
    $(activeColumnHeader).insertBefore(targetColumnHeader);
    helper.columnsTableRows.forEach((row) => {
      let srcCol = helper.getColumnInRow(activeColumnId, row);

      if (activeColumnId !== targetColumnId) {
        let destCol = helper.getColumnInRow(targetColumnId, row);
        $(srcCol).insertBefore(destCol);
      }
    });
  }

  event.dataTransfer.dropEffect = "move";
}

// Перетаскиваемый элемент отпущен
function onDrop(event, helper) {
  if (event.preventDefault) {
    event.preventDefault();
  }
  if (event.stopPropagation) {
    event.stopPropagation();
  }

  // // ID перетаскиваемой колонки
  // let srcColumnId = event.dataTransfer.getData('columnId');

  // // Копия перетаскиваемого заголовка
  // const activeElement = document.querySelector('.matrix-column--dragging');

  // // Определение места вставки ячеек перетаскиваемой колонки
  // let element = activeElement.previousElementSibling;
  // let position = 'after';

  // if (!element) {
  //   position = 'before';
  //   element = activeElement.nextElementSibling;
  // }

  // const destColumnId = helper.getColumnId(element);

  // helper.columnsTableRows.forEach((row) => {
  //   let srcCol = helper.getColumnInRow(srcColumnId, row);

  //   if (destColumnId !== srcColumnId) {
  //     let destCol = helper.getColumnInRow(destColumnId, row);
  //     if (position == 'before') $(srcCol).insertBefore(destCol);
  //     else $(srcCol).insertAfter(destCol);
  //   }

  //   srcCol.style.display = '';
  // });

  // activeElement.removeAttribute('rowspan');
  // activeElement.classList.remove('matrix-column--dragging');
}
function updatePoints(newPoints) {
  let rows = document.querySelectorAll(".matrix-table__row");
  rows.forEach((row, rowIndex) => {
    let inputs = row.querySelectorAll('input[type="text"]');
    inputs.forEach((input, colIndex) => {
      if (newPoints[rowIndex]) {
        input.value = newPoints[rowIndex][colIndex];
      }
    });
  });
}
ko.bindingHandlers.matrixColumnHeader = {
  init: (el, valueAccessor) => {
    let matrix = valueAccessor();
    let helper = matrix.domHelper;

    el.addEventListener(
      "dragstart",
      (event) => onDragStart(event, helper),
      false
    );

    el.addEventListener(
      "dragover",
      (event) => {
        onDragOver(event, helper);
        return false;
      },
      false
    );

    el.addEventListener(
      "drop",
      (event) => {
        onDrop(event, helper);
      },
      false
    );

    el.addEventListener(
      "dragend",
      (event) => {
        const newPoints = matrix.resortColumns();
        updatePoints(newPoints);
        $(".matrix-column-header--dragging").removeClass(
          "matrix-column-header--dragging"
        );
        $(".matrix-column-cell--dragging").removeClass(
          "matrix-column-cell--dragging"
        );

        $(".matrix-ghost").remove();
      },
      false
    );
  },
};
