<!-- ko let: { $translator: $component.translator }-->
<!-- ko if: withVariantsTypeSelect -->
<radio-group params="options: $component.variantsTypes, value: $component.variantsType, disabled: $component.isBlocked || $component.isFullBlocked" data-bind="click: function() {
  if ($component.isBlocked && !$component.isFullBlocked) $component.question.tryChangeBlockedParam();
  else return true;
}" class="mb-4"></radio-group>
<!-- /ko -->

<!-- ko if: $component.canUseFile && $component.variantsType() !== 2 -->
<div class="form-group">
  <div>
      <fc-switch params="checked: $component.enableFile, 
          disabled: $component.isFullBlocked,
          label: 'Варианты ответов с изображениями/видео (плиткой)',
          hint: 'Варианты ответов с изображениями/видео (плиткой)'"></fc-switch>
  </div>
</div>
<!-- /ko -->

<!-- ko if: $component.variantsType() == 2 -->
<div>
  <!-- ko component: {
    name: 'text-field',
    params: {
      controller: textAnswerField,
      disabled: $component.isFullBlocked
    }
  } -->
  <!-- /ko -->
</div>
<!-- /ko -->

<!-- ko ifnot: $component.variantsType() == 2 -->
<div
  class="survey-question__variants-control variants-controller"
  data-bind="css: $component.className"
>
  <!-- ko if: withDropdownOption -->
  <div class="mb-20p">
    <foquz-checkbox params="checked: dropdown, disabled: $component.isFullBlocked">
      <span data-bind="text: $translator.t('Варианты ответов выпадающим списком')"></span>
    </foquz-checkbox>
  </div>
  <!-- /ko -->
  

  <!-- ko if: withRandomOption -->
  <div class="mb-20p">
    <foquz-checkbox params="checked: randomOrder, disabled: $component.isFullBlocked">
      <span data-bind="text: $translator.t('Случайный порядок вариантов')"></span> <question-button
                       params="text: $translator.t('Варианты ответов для каждого респондента при прохождении будут предложены в случайном порядке')">
      </question-button>
    </foquz-checkbox>
  </div>
  <!-- /ko -->

  <div class="survey-question__variants-control-list variants-controller__list survey-question__variants-control-list-content" data-bind="nativeScrollbar">

    <!-- ko if: variants().length -->
    <div
      class="variants-controller__sortable-container"
      data-bind="
        sortable,
        sortableItem: '.variants-controller__variant',
        sortableHandle: '.sortable-handle',
        onSort: $component.onSort.bind($component)
      "
    >
      <!-- ko foreach: {
        data: variants,
        as: 'variant',
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: $component.beforeRemove.bind($component)
      } -->
      <question-form-variant
        class="variants-controller__variant survey-question__variants-control-list-item"
        params="variant: variant,
          focused: $index() === $component.focused(),
          isSortable: !$component.isFullBlocked && (!variant.sortable || variant.sortable()) ,
          formControlErrorStateMatcher: $component.formControlErrorStateMatcher,
          formControlSuccessStateMatcher: $component.formControlSuccessStateMatcher,
          canRemove: $component.canRemoveSingleVariant || $component.variants().length > 1 && !$component.isFullBlocked,
          isBlocked: $component.isBlocked,
          isFullBlocked: $component.isFullBlocked,
          enableFile: $component.enableFile,
          onRemove: function() { $component.removeVariant(variant); },
          onEnter: function() {
            if ($component.isVariantsValid()) {
              var variant = $component.addVariant($index() + 1);
              $component.focused($index() + 1);
            }
          },
          withPoints: $component.withPoints
        "
      ></question-form-variant>
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- ko if: withAddVariantButton -->
    <!-- ko ifnot: $component.isFullBlocked -->
    <button
      class="survey-question__variants-control-add-button variants-controller__add-button"
      data-bind="
        click: () => addVariant(),
        attr: { disabled: !$component.isVariantsValid() },
      "
    >
      <span class="survey-question__variants-control-add-button-icon"></span>
      <span data-bind="text: $translator.t('Добавить вариант')"></span>
    </button>
    <!-- /ko -->
    <!-- /ko -->


  </div>
</div>

<!-- ko if: withAnswersCountLimit -->
<!-- ko template: {
  foreach: templateIf($component.isMultiple(), $data),
  afterAdd: fadeAfterAddFactory(400),
  beforeRemove: fadeBeforeRemoveFactory(400)
} -->
<div class="form-group mt-4">
  <label class="form-label" data-bind="text: $translator.t('Max кол-во выбранных ответов')"></label>
  <div class="row">
    <div class="col-12 col-md-10 col-lg-6">
      <input type="text"
             class="form-control"
             data-bind="intervalInput: {min: answersCountLimitMin, max: answersCountLimitMax}, textInput: answersCountLimit, disable: $component.isFullBlocked">
    </div>
  </div>


</div>

<!-- /ko -->
<!-- /ko -->

<!-- ko if: withCustomAnswer -->
<switch class="mt-4"
        params="checked: customAnswerEnabled, disabled: $component.isBlocked || $component.isFullBlocked"
        data-bind="click: function() {
          if ($component.isFullBlocked) return false;
          if ($component.isBlocked) {
            $component.question.tryChangeBlockedParam();
          } else return true;
        }">
  <div for="rate-clarifying-question-custom-answer"
       class="f-check-label" data-bind="text: $translator.t('Свой вариант (произвольное поле)')"></div>
</switch>

<!-- ko template: {
      foreach: templateIf(customAnswerEnabled(), $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
<div class="mt-4">
  <!-- ko component: {
    name: 'text-field',
    params: {
      controller: customAnswerField,
      disabled: isFullBlocked,
      enableFile: $component.enableFile
    }
  } -->
  <!-- /ko -->
</div>
<!-- /ko -->

<!-- /ko -->
<!-- /ko -->
<!-- /ko -->
