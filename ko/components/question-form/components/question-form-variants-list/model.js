import {
  VARIANTS_TYPE_MULTIPLE,
  VARIANTS_TYPE_SINGLE,
  VARIANTS_TYPE_TEXT
} from '../../data/variant-types';

import { Translator } from '@/utils/translate';

export class ViewModel {
  constructor({
    controller,
    question,
    canUseFile,
    enableFile,
    withPoints,
    withTextAnswer,
    textAnswerField,
    withVariantsTypeSelect,
    variantsType,
    withDropdownOption,
    dropdown,
    withRandomOption,
    randomOrder,
    canRemoveSingleVariant,
    removeVariant,
    isVariantsValid,
    addVariant,
    withAddVariantButton,
    variantsCount,
    withAnswersCountLimit,
    answersCountLimitMin,
    answersCountLimitMax,
    answersCountLimit,
    withCustomAnswer,
    customAnswerEnabled,
    customAnswerField,
    variants,
  }, element) {
    if (controller) {
      this.controller = controller;
      this.question = controller.question;
      this.canUseFile = controller.config.canUseFile;
      this.withPoints = controller.config.withPoints;
      this.withTextAnswer = controller.config.withTextAnswer;
      this.textAnswerField = controller.textAnswerField;
      this.withVariantsTypeSelect = controller.config.withVariantsTypeSelect;
      this.withVariantsTypeSelect = controller.config.withVariantsTypeSelect;
      this.variantsType = controller.variantsType;
      this.withDropdownOption = controller.config.withDropdownOption;
      this.dropdown = controller.dropdown;
      this.withRandomOption = controller.config.withRandomOption;
      this.randomOrder = controller.randomOrder;
      this.canRemoveSingleVariant = controller.config.canRemoveSingleVariant;
      this.removeVariant = controller.removeVariant;
      this.isVariantsValid = controller.isVariantsValid;
      this.addVariant = controller.addVariant;
      this.withAddVariantButton = controller.config.withAddVariantButton;
      this.variantsCount = controller.variantsCount;
      this.withAnswersCountLimit = controller.config.withAnswersCountLimit;
      this.answersCountLimitMin = controller.answersCountLimitMin;
      this.answersCountLimitMax = controller.answersCountLimitMax;
      this.answersCountLimit = controller.answersCountLimit;
      this.withCustomAnswer = controller.config.withCustomAnswer;
      this.customAnswerEnabled = controller.customAnswerEnabled;
      this.customAnswerField = controller.customAnswerField;
      this.variants = controller.variants;
      this.enableFile = this.question.enableFile;
    } else {
      this.question = question;
      this.canUseFile = canUseFile;
      this.withPoints = withPoints;
      this.withTextAnswer = withTextAnswer;
      this.textAnswerField = textAnswerField;
      this.withVariantsTypeSelect = withVariantsTypeSelect;
      this.withVariantsTypeSelect = withVariantsTypeSelect;
      this.variantsType = variantsType;
      this.withDropdownOption = withDropdownOption;
      this.dropdown = dropdown;
      this.withRandomOption = withRandomOption;
      this.randomOrder = randomOrder;
      this.canRemoveSingleVariant = canRemoveSingleVariant;
      this.removeVariant = removeVariant;
      this.isVariantsValid = isVariantsValid;
      this.addVariant = addVariant;
      this.withAddVariantButton = withAddVariantButton;
      this.variantsCount = variantsCount;
      this.withAnswersCountLimit = withAnswersCountLimit;
      this.answersCountLimitMin = answersCountLimitMin;
      this.answersCountLimitMax = answersCountLimitMax;
      this.answersCountLimit = answersCountLimit;
      this.withCustomAnswer = withCustomAnswer;
      this.customAnswerEnabled = customAnswerEnabled;
      this.customAnswerField = customAnswerField;
      this.variants = variants;
      this.enableFile = enableFile;
    }

    this.translator = Translator('question');

    this.focused = ko.observable('');

    this.sorting = ko.observable(false);
    this.isBlocked = this.question.isBlocked();
    this.isFullBlocked = this.question.isFullBlocked;

    this.formControlErrorStateMatcher =
      this.question.controller.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher =
      this.question.controller.formControlSuccessStateMatcher;

    this.hideCountError = ko.observable(false);

    this.className = '';

    this.isMultiple = ko.pureComputed(() => {
      return this.variantsType() == VARIANTS_TYPE_MULTIPLE;
    });
  }

  get variantsTypes() {
    const variantsTypes = [
      {
        id: VARIANTS_TYPE_SINGLE,
        value: VARIANTS_TYPE_SINGLE,
        icon: 'input-radio',
        label: this.translator.t('Один ответ')()
      },
      {
        id: VARIANTS_TYPE_MULTIPLE,
        value: VARIANTS_TYPE_MULTIPLE,
        icon: 'input-checkbox',
        label: this.translator.t('Несколько ответов')()
      },
    ];
    
    if (this.withTextAnswer) {
      variantsTypes.push({
        id: VARIANTS_TYPE_TEXT,
        value: VARIANTS_TYPE_TEXT,
        icon: 'input-text',
        label: this.translator.t('Текстовый ответ')()
      });
    }

    return variantsTypes;
  }

  onSort(event) {
    let variants = this.variants;
    this.sorting(true);
    const movedItem = variants()[event.data.oldIndex];
    variants.remove(movedItem);
    variants.splice(event.data.newIndex, 0, movedItem);
    this.sorting(false);
  }

  beforeRemove(element) {
    const duration = this.sorting() ? 0 : 200;
    return $(element)
      .delay(0)
      .fadeOut(duration, function () {
        return $(element).remove();
      });
  }

  afterAdd(element) {
    const duration = this.sorting() ? 0 : 200;
    return $(element).hide().delay(0).fadeIn(duration);
  }
}
