import { Translator } from '@/utils/translate';
import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";

export class ViewModel {
  constructor(params, element) {
    this.translator = Translator('question')
    this.subscriptions = [];
    this.loaderError = ko.observable(false)
    this.variant = params.variant;
    this.isSortable = params.isSortable;
    this.canRemove = params.canRemove;
    this.isBlocked = params.isBlocked;
    this.isFullBlocked = params.isFullBlocked;
    this.enableFile = params.enableFile;
    this.withPoints = params.withPoints;
    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher = params.formControlSuccessStateMatcher;
    this.onRemove = params.onRemove;
    this.onEnter = () => {
      if (this.isFullBlocked) return;
      params.onEnter();
    };
    this.focused = params.focused;

    this.loaderErrorText = ko.observable('Файл должен быть меньше 5 Мб')
    const loaderConfig = {
      presets: ["videoVar","imageWithSvg"],    
    };

    this.loader = new FoquzMultipleLoader(loaderConfig)

    this.loader.on("foquz.file.loader.errors.limit", (param) => {
      this.loaderError(true)
    })

    this.loader.on("select", ({ file }) => {
      

      this.loaderError(false)
      
      var fd = new FormData();
      fd.append("file", file);
      var self = this;
      var url = "";

      $.ajax({
        url: `/foquz/api/questions/upload-detail-file?access-token=${window.ACCESS_TOKEN}&detail_id=${this.variant.id}`,
        type: "post",
        data: fd,
        contentType: false,
        processData: false,
        success: function (response) {
          if (response.file.previewUrl == '/uploads/') {
            response.file.previewUrl = '/img/audio-file-back.png'
          }
          self.variant.file(response.file)
          if (!self.variant.value().length) {
            self.variant.value(response.file.origin_name.replace(/\.[^/.]+$/, ""))
          }
          
          
        },
      });
    });

    console.log('parma', params.variant, params.canRemove(), params.isBlocked, params.isFullBlocked);
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
