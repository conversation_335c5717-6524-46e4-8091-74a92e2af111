
<!-- ko if: isSortable -->
<div class="question-form-variant__drag-handle sortable-handle hide-on-dragging"  data-bind="css: {'align-items-start': enableFile}">
  <i class="icon icon-drag-arrow"></i>
</div>
<!-- /ko -->

<div class="question-form-variant__content hide-on-dragging" data-bind="descendantsComplete: function(el) {
  if (focused()) {
    setTimeout(function() {
      $(el).find('input').focus();
    }, 0)
  }
}">
  <div
    class="question-form-variant__fields"
    data-bind="css: {'align-items-start': enableFile}"
  >
    <!-- ko if: enableFile -->
      <!-- ko if: variant.file() -->
      <file-loader-preview
        class="file-loader-preview--lg my-0 mr-15p my-0"
        params="
          loading: false,
          file: variant.file(),
          preview: variant.file().previewUrl,
          disabled: !!window.CURRENT_USER?.watcher,
          onRemove: () => { 
            if (!window.CURRENT_USER?.watcher) {
              variant.file(null)
            }
          }
        "
      ></file-loader-preview>
      <!-- /ko -->
      <!-- ko if: !variant.file() -->
        <div
          class="survey-question__media-form-control-actions my-0 mr-15p"
          data-bind="css:{'disabled': false}"
        >
          <!-- ko let: { fileInput: ko.observable(null) } -->
            <div class="survey__images-form-control-content m-0">
              <div
                data-bind="click: function (_,e) {$component.loader.open();}" 
                class="survey__images-form-control-add-button survey__images-form-control-add-button_big m-0"
              >
                <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 9.43839L9.96279 17.4644C8.97817 18.4476 7.64274 19 6.25028 19C4.85782 19 3.52239 18.4476 2.53777 17.4644C1.55315 16.4811 1 15.1476 1 13.7571C1 12.3665 1.55315 11.033 2.53777 10.0497L10.575 2.02375C11.2314 1.36825 12.1217 1 13.05 1C13.9783 1 14.8686 1.36825 15.525 2.02375C16.1814 2.67924 16.5502 3.56829 16.5502 4.4953C16.5502 5.42231 16.1814 6.31135 15.525 6.96685L7.47904 14.9928C7.15083 15.3206 6.70569 15.5047 6.24154 15.5047C5.77738 15.5047 5.33224 15.3206 5.00403 14.9928C4.67583 14.6651 4.49144 14.2206 4.49144 13.7571C4.49144 13.2935 4.67583 12.849 5.00403 12.5213L12.429 5.11537" stroke="#73808D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span
                  class="survey__images-form-control-add-button-label"
                  data-bind="text: 'Загрузить изображение/видео до 5 Мб'"
                ></span>
              </div>
            </div>
            <!-- ko if: loaderError() -->
              <div class="mt-10p pt-15p file-loader-error position-relative">
                <div class="position-absolute file-loader-error-text" data-bind="text: loaderErrorText"></div>
              </div>
            <!-- /ko -->
            <!-- ko if: !loaderError() && !variant.file() -->
              <div class="mt-10p pt-15p file-loader-error position-relative">
                <div
                  class="position-absolute file-loader-error-text"
                  data-bind="text: 'Обязательное поле'"
                ></div>
              </div>
            <!-- /ko -->
          <!-- /ko -->
        </div>
      <!-- /ko -->
    <!-- /ko -->
    <div class="flex-grow-1">
      <div class="flex-grow-1 d-flex align-center">
        <foquz-chars-counter
          class="question-form-variant__value"
          params="value: variant.value, max: 250"
        >
          <input
            class="form-control"
            data-bind="
              textInput: $parent.variant.value,
              css: {
                  'is-invalid': $parent.formControlErrorStateMatcher($parent.variant.value),
                  'is-valid': $parent.formControlSuccessStateMatcher($parent.variant.value)
              },
              onEnter: $parent.onEnter, disable: $parent.isFullBlocked
            "
          >
        </foquz-chars-counter>
    
        <!-- ko if: withPoints -->
          <input
            type="text"
            class="question-form-variant__points form-control"
            maxlength="9"
            data-bind="
              textInput: variant.points,
              onlyNumbers: { sign: true },
              disable: isFullBlocked,
              attr: {placeholder: $component.translator.t('0 баллов')}
            "
          >
        <!-- /ko -->
      </div>
      <validation-feedback
        params="
          text: variant.value.error,
          show: formControlErrorStateMatcher(variant.value)
        "
      ></validation-feedback>
    </div>
    
  </div>
</div>

<!-- ko template: {
    foreach: templateIf(!isFullBlocked && canRemove() && (!isBlocked || variant.id == '0'), $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
} -->
  <button
    type="submit"
    class="question-form-variant__remove hide-on-dragging f-btn f-btn-danger f-btn--square"
    data-bind="
      click: function() { onRemove() },
      attr: {title: $component.translator.t('Удалить')},
      css: {'mt-0': enableFile}"
  >
    <foquz-icon params="icon: 'times'" class="f-icon-sm"></foquz-icon>
  </button>
<!-- /ko -->
