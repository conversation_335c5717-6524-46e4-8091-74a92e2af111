import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('question-form-variant', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);
      element.classList.add('question-form-variant')
      return new ViewModel(params, element);
    },
  },
  template: html,
});
