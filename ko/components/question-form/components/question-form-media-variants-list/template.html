<!-- ko let: { $translator: translator }-->
<!-- ko if: controller.config.withVariantsTypeSelect -->
<div
  class="hat-radio-group hat-radio-group--dense survey-question__variants-type-selector"
>
  <!-- ko foreach: $component.variantsTypes -->
  <div
    class="hat-radio-group__radio"
    data-bind="let: { inputId: 'survey-question-variants-type-selector-' + id}"
  >
    <input
      class="hat-radio-group__radio-input"
      name="variants-type"
      type="radio"
      data-bind="value: id, checked: $component.controller.variantsType, attr: { id: inputId, }, disable: $component.isBlocked || $component.isFullBlocked"
    />
    <label
      class="hat-radio-group__radio-label"
      data-bind="attr: { for: inputId },
    click: function() {
       if ($component.isFullBlocked) return false;
      if ($component.isBlocked) $component.question.tryChangeBlockedParam();
      else return true;
    }"
    >
      <i
        class="survey-question__variants-type-selector-value-icon"
        data-bind="class: 'survey-question__variants-type-selector-value-' + icon + '-icon'"
      ></i>
      <span data-bind="text: label"></span>
    </label>
  </div>
  <!-- /ko -->
</div>
<!-- /ko -->

<hr class="mx-0 mt-0 mb-4">

<div
  data-bind="dnd: function(files) {
    controller.loader.loadFiles(files);
    }, dndDisabled: $component.isFullBlocked"
>
  <div
    class="survey-question__variants-control variants-controller"
    data-bind="css: $component.className"
  >
    <div
      class="survey-question__variants-control-list variants-controller__list survey-question__variants-control-list-content"
      data-bind="nativeScrollbar"
    >
      <!-- ko if: controller.variants().length -->
      <div
        class="variants-controller__sortable-container"
        data-bind="
          sortable,
          sortableItem: '.variants-controller__variant',
          sortableHandle: '.sortable-handle',
          onSort: $component.onSort.bind($component)"
      >
        <!-- ko foreach: {
          data: controller.variants,
          as: 'variant',
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: $component.beforeRemove.bind($component)
        } -->
        <question-form-media-variant
          class="variants-controller__variant survey-question__variants-control-list-item"
          params="variant: variant,
                                     getGalleryConfig: $component.getGalleryConfig.bind($component),
          isSortable: !variant.sortable || variant.sortable(),
          formControlErrorStateMatcher: $component.formControlErrorStateMatcher,
          formControlSuccessStateMatcher: $component.formControlSuccessStateMatcher,
          canRemove: $component.controller.config.canRemoveSingleVariant || $component.controller.variants().length > 1,
          isBlocked: $component.isBlocked,
          isFullBlocked: $component.isFullBlocked,
          onRemove: function() { $component.controller.removeVariant(variant); },
          withPoints: $component.withPoints"
        >
        </question-form-media-variant>
        <!-- /ko -->
      </div>
      <!-- /ko -->

      <validation-feedback
        params="show: $component.formControlErrorStateMatcher(controller.variantsCount), text: controller.variantsCount.error()"
      >
      </validation-feedback>

      <!-- ko foreach: controller.loader.errors -->
      <file-loader-error params="error: $data"></file-loader-error>
      <!-- /ko -->
    </div>
    <!-- ko ifnot: $component.isFullBlocked -->
    <div class="my-4">
      <button
        class="f-btn"
        data-bind="click: function() {
          controller.loader.open()
        }"
      >
        <span class="f-btn-prepend">
          <svg-icon params="name: 'clip'"></svg-icon>
        </span>
        <span data-bind="text: $translator.t('С компьютера')"></span>
      </button>

      <!-- ko if: controller.mediaType == 'video' || controller.mediaType == 'mix' -->
      <button
        class="ml-2 btn btn-default btn-with-icon btn-upload-link survey-question__media-form-control-actions-item"
        data-bind="
      click: addLinkVariant"
        type="button"
      >
        <span data-bind="text: $translator.t('По ссылке')"></span>
      </button>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>

  <!-- ko if: controller.config.withAnswersCountLimit -->
  <!-- ko template: {
    foreach: templateIf($component.isMultiple(), $data),
    afterAdd: fadeAfterAddFactory(400),
    beforeRemove: fadeBeforeRemoveFactory(400)
  } -->
  <div class="form-group">
    <label
      class="form-label"
      data-bind="text: $translator.t('Max кол-во выбранных ответов')"
    ></label>
    <div class="row">
      <div class="col-12 col-md-10 col-lg-6">
        <input
          type="text"
          class="form-control"
          data-bind="intervalInput: {min: controller.answersCountLimitMin, max: controller.answersCountLimitMax}, textInput: controller.answersCountLimit, disable: $component.isFullBlocked"
        />
      </div>
    </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko if: controller.config.withCustomAnswer -->
  <div class="f-check my-4">
    <input
      type="checkbox"
      class="f-check-input"
      data-bind="checked: controller.customAnswerEnabled, disabled: $component.isBlocked"
      id="rate-clarifying-question-custom-answer"
    />
    <label
      for="rate-clarifying-question-custom-answer"
      class="f-check-label"
      data-bind="click: function() {
             if ($component.isFullBlocked) return false;
      if ($component.isBlocked) {
        $component.question.tryChangeBlockedParam();
      } else return true;
    }"
      ><span data-bind="text: t('Свой вариант (произвольное поле)')"></span
    ></label>
  </div>
  <!-- ko template: {
      foreach: templateIf(controller.customAnswerEnabled(), $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
  <div class="mt-4">
    <!-- ko component: {
          name: 'text-field',
          params: {
            controller: controller.customAnswerField,
            disabled: $component.isFullBlocked
          }
        } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <dnd-cover
    params="type: controller.mediaType, mode: 'multiple'"
    class="my-n4 mx-n3"
    data-bind="css: {
               'dense': controller.variants().length < 2
             }"
  ></dnd-cover>
</div>
<!-- ko if: $component.withPoints -->
<div class="mb-20p">
  <foquz-checkbox params="checked: $component.question.maxPointsCalcMethod, disabled: $component.isFullBlocked">
      <span data-bind="text: $translator.t('Учитывать в итоговой сумме баллов, если вопрос не отображался для респондента')"></span>
      <question-button params="text: $translator.t('Настройки отображения вопроса можно настроить в логике опроса. По умолчанию все скрытые логикой отображения вопросы учитываются в итоговом подсчете баллов')">
      </question-button>
  </foquz-checkbox>
</div>
<!-- /ko -->

<!-- ko if: controller.config.withRandomOption -->
<div class="mb-30p">
  <foquz-checkbox
    params="checked: controller.randomOrder, disabled: $component.isFullBlocked"
  >
    <span
      data-bind="text: $translator.t('Случайный порядок вариантов')"
    ></span>
    <question-button
      params="text: $translator.t('Варианты ответов для каждого респондента при прохождении будут предложены в случайном порядке')"
    ></question-button>
  </foquz-checkbox>
</div>
<!-- /ko -->

<hr class="mx-0 mb-4">
<!-- /ko -->
