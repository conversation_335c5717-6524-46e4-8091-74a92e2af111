import { createFileInput } from 'Utils/file-loader/create-file-input';
import { MEDIA_VARIANT_TYPE_MIX } from '../../data/media-variants-types';
import * as Parent from '../question-form-variants-list/model';
import { Translator } from '@/utils/translate';
const QuestionTranslator = Translator('question');
export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
    this.translator = QuestionTranslator;
    this.className = 'media-variants-controller';
    this.disabled = params.disabled;

    if (this.controller.mediaType !== MEDIA_VARIANT_TYPE_MIX) {
      this.hideCountError = ko.pureComputed(() => {
        return !this.controller.countedVariants().length;
      });
    } else {
      this.hideCountError = ko.observable(true);
    }
  }

  getGalleryConfig() {
    return this.controller.variants().map((v) => {
      return {
        src: v.getUrl(),
        opts: {
          caption: v.description()
        }
      };
    });
  }

  addFileVariant() {
    let variant = this.controller.createVariant();
    createFileInput(
      ({ file }) => {
        if (file) variant.addByFile(file);
        this.controller.variants.push(variant);
      },
      { accept: variant.accept, open: true }
    );
  }

  addLinkVariant() {
    let variant = this.controller.createVariant();
    variant.addByYoutubeLink().then(() => {
      this.controller.variants.push(variant);
    });
  }
}
