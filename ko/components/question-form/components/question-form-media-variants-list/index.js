import { ViewModel } from './model';
import html from './template.html';
import './style';

ko.components.register('question-form-media-variants-list', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('question-form-variants-list')
      element.classList.add('question-form-media-variants-list')
      return new ViewModel(params, element);
    },
  },
  template: html
});
