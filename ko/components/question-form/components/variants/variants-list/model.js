import * as VariantItem from "../variant-item";
import { registerComponent } from "@/utils/engine/register-component";

registerComponent("fc-variant-item", VariantItem);

export default function (params, element) {

  const sorting = ko.observable(false);
  const viewModel = {};
  viewModel.tooltipPlaceholder = params.tooltipPlaceholder
  viewModel.showTooltips = params.showTooltips ? params.showTooltips : ko.observable(false)
  viewModel.useTooltips = params.useTooltips ? params.useTooltips : false
  viewModel.enableFile = params.enableFile
  viewModel.randomOrder = ko.isObservable(params.randomOrder) ? params.randomOrder : ko.observable(false)
  viewModel.translator = params.questionTranslator;
  viewModel.variantsList = params.variantsList;
  viewModel.variants = params.variantsList.list;
  viewModel.isBlocked = params.isBlocked;
  viewModel.isFullBlocked = params.isFullBlocked;
  viewModel.formControlErrorStateMatcher = params.formControlErrorStateMatcher;
  viewModel.formControlSuccessStateMatcher = params.formControlSuccessStateMatcher;
  viewModel.canRemoveSingleVariant = params.canRemoveSingleVariant;
  viewModel.isValid = params.variantsList.isValid;
  viewModel.isVariantsValid = params.variantsList.isVariantsValid;
  viewModel.withPoints = params.withPoints;
  viewModel.hasChildren = params.hasChildren;
  viewModel.hasChildren = params.hasChildren;
  viewModel.variantsListId = ko.observable(params.id || `variants-list-${Math.floor(Math.random() * 1000)}`);
  viewModel.focused = ko.observable("");
  viewModel.canRemove = ko.computed(() => {
    if (ko.toJS(params.isFullBlocked) || ko.toJS(params.isBlocked)) return false;
    return params.variantsList.list().length > 1;
  });
  
  viewModel.onSort = (variantsList, event) => {
    sorting(true);
    variantsList.move(event.data.oldIndex, event.data.newIndex);
    sorting(false);
  },
  viewModel.beforeRemove = element => {
    const duration = sorting() ? 0 : 200;
    return $(element)
      .delay(0)
      .fadeOut(duration, function () {
        return $(element).remove();
      });
  },
  viewModel.beforeRemove = element => {
    const duration = sorting() ? 0 : 200;
    return $(element)
      .delay(0)
      .fadeOut(duration, function () {
        return $(element).remove();
      });
  },
  viewModel.afterAdd = element => {
    const duration = sorting() ? 0 : 200;
    return $(element).hide().delay(0).fadeIn(duration);
  },
  viewModel.addVariant = () => {
    params.variantsList.addVariant(undefined, true, params.useTooltips);
    viewModel.focused(params.variantsList.list().length - 1);
  },
  viewModel.removeVariant = variant => {
    if (typeof params.onRemove === "function") {
      params.onRemove(variant, () => {
        params.variantsList.removeVariant(variant);
      });
    } else params.variantsList.removeVariant(variant);
  };

  return viewModel;
}
