<div class="survey-question__variants-control-list-content" data-bind="nativeScrollbar">
  <!-- ko if: variants().length -->
  <div
    class="variants-controller__sortable-container"
    data-bind="
      sortable,
      sortableItem: '.variants-controller__variant--parent',
      sortableHandle: '.variants-controller__variant--parent .sortable-handle',
      onSort: $component.onSort.bind($component, variantsList),
      attr: {
        id: variantsListId(),
      },
    "
  >
    <!-- ko foreach: {
      data: variants,
      as: 'variant',
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: $component.beforeRemove.bind($component)
    } -->
    <fc-variant-item
      class="variants-controller__variant variants-controller__variant--parent"
      params="
        variant: variant,
        showTooltips: $component.showTooltips,
        useTooltips: $component.useTooltips,
        tooltipPlaceholder: $component.tooltipPlaceholder,
        focused: $index() === $component.focused(),
        isSortable: !$component.isFullBlocked && (!variant.sortable || variant.sortable()) ,
        formControlErrorStateMatcher: $component.formControlErrorStateMatcher,
        formControlSuccessStateMatcher: $component.formControlSuccessStateMatcher,
        canRemove: $component.canRemove,
        isBlocked: $component.isBlocked,
        enableFile: $component.enableFile,
        isFullBlocked: $component.isFullBlocked,
        onRemove: function() { $component.removeVariant(variant); },
        onEnter: function() {
          if ($component.isVariantsValid()) {
            var variant = $component.addVariant($index() + 1);
            $component.focused($index() + 1);
          }
        },
        withPoints: $component.withPoints,
        randomOrder: $component.randomOrder,
        index: $index(),
      "
    ></fc-variant-item>


    <!-- ko if: $component.hasChildren -->
    <div
      class="variants-controller__sortable-container variants-controller__children-container"
      data-bind="
        sortable,
        sortableItem: '.variants-controller__variant--child',
        sortableHandle: '.variants-controller__variant--child .sortable-handle',
        onSort: $component.onSort.bind($component, variant.children),
      "
    >
      <label class="form-label" data-bind="text: $translator.t('Ответы')"></label>
      <!-- ko foreach: {
        data: variant.children.list,
        as: 'childVariant',
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: $component.beforeRemove.bind($component)
      } -->
      <fc-variant-item
        class="variants-controller__variant variants-controller__variant--child"
        params="
          variant: childVariant,
          focused: $index() === $component.focused(),
          isSortable: !$component.isFullBlocked && (!childVariant.sortable || childVariant.sortable()) ,
          formControlErrorStateMatcher: $component.formControlErrorStateMatcher,
          formControlSuccessStateMatcher: $component.formControlSuccessStateMatcher,
          canRemove: variant.children.list().length > 1,
          isBlocked: false,
          isFullBlocked: $component.isFullBlocked,
          onRemove: function() { variant.children.removeVariant(childVariant); },
          onEnter: function() {
            if ($component.isVariantsValid()) {
              var childVariant = variant.children.addVariant($index() + 1);
              $component.focused($index() + 1);
            }
          },
          withPoints: $component.withPoints,
          randomOrder: $component.randomOrder,
          index: $index(),
        "
      ></fc-variant-item>
      <!-- /ko -->
      <!-- ko ifnot: $component.isFullBlocked -->
      <div class="survey-question__variants-child-actions">
        <fc-button
          params="
            label: 'Добавить ответ',
            color: 'success',
            icon: 'plus',
            inverse: true,
            disabled: !variant.children.isValid(),
            click: function() {
              variant.children.addVariant();
              $component.focused(variant.children.list().length - 1);
            },
          "
        ></fc-button>
        <fc-button
          params="
            label: 'Добавить списком',
            color: 'success',
            icon: 'plus',
            inverse: true,
            disabled: false,
            click: function() {
              question.addVariantsFromList(variant.children, 'answers');
            },
          "
        ></fc-button>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
</div>
