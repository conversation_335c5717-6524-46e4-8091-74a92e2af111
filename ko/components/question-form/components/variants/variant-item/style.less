.lock-shuffle {
  color: #73808D;
  transition: .2s;
  &:hover {
    color: #2E2F31;
  }
  &.locked {
    color: #F96261;
  }
  &[disabled] {
    color: #73808D;
  }
  &[disabled].locked {
    color: #F96261;
  }
}

.fc-variant-item {
  display: flex;
  padding-top: 7px;
  padding-bottom: 7px;
  margin-left: -10px;
  margin-right: -10px;
  padding-left: 10px;
  padding-right: 10px;
  flex-wrap: wrap;

  &__tooltip {
    width: 100%;
    overflow: hidden;
    &-input {
      width: 100%;
      flex-shrink: 0;
      display: flex;
    }
    &-value {
      font-family: Roboto;
      font-size: 12px;
      font-weight: 400;
      line-height: 14.4px;
      color: #73808D;
    }
    &-helper {
      cursor: pointer;
      height: 48px;
      display: flex;
      align-items: center;
    }
  }

  &__drag-handle {
    flex-shrink: 0;
    margin-right: 15px;
    height: 48px;
    display: flex;
    align-items: center;
    cursor: move;
    &.hidden_ {
      opacity: 0;
      cursor: auto;
      height: 0;
    }
    &_file {
      height: 18px;
    }
  }

  &:only-child {
    .fc-variant-item__drag-handle {
      display: none;
    }
  }

  &__content {
    flex-grow: 1;
  }

  &__fields {
    width: 100%;
    display: flex;
    align-items: center;
  }

  &__value {
    flex-grow: 1;
  }

  &__points {
    width: 100px;
    margin-left: 15px;
    padding: 0 15px;
    text-align: center;
  }

  &__remove {
    flex-shrink: 0;
  }

  &__index {
    color: #73808D;
    display: flex;
    align-items: center;
    height: 48px;
    font-size: 12px;
    line-height: 14px;
    margin-right: 10px;
    &.hidden_ {
      opacity: 0;
    }
    &_file {
      height: 18px;
    }
  }
  .file-loader-error {
    color: #F96261;
    font-family: Roboto;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 120%; /* 14.4px */
    &-text {
      top: 0;
      width: max-content;
    }
  }
}
