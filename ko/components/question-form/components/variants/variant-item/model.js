import { questionTranslator } from "@/components/question-form/translator";
import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";

const { computed } = ko;

export default function (params, element) {
  element.classList.add('fc-variant-item')

  const {
    variant,
    isSortable,
    canRemove,
    isBlocked,
    isFullBlocked,
    withPoints,
    formControlErrorStateMatcher,
    formControlSuccessStateMatcher,
    onRemove,
    onEnter,
    focused,
    index,
    enableFile,
    showTooltips,
    useTooltips,
    tooltipPlaceholder,
    donor
  } = params;

  const randomOrder = ko.isObservable(params.randomOrder) ? params.randomOrder : ko.observable(false)
  const randomExclusion = ko.isObservable(variant.randomExclusion) ? variant.randomExclusion : ko.observable(false)

  const loaderError = ko.observable(false)
  const loaderErrorText = ko.observable('Файл должен быть меньше 5 Мб')
  const loaderConfig = {
    presets: ["videoVar","image"],    
  };

  const loader = new FoquzMultipleLoader(loaderConfig)

  loader.on("foquz.file.loader.errors.limit", (param) => {
    loaderError(true)
  })

  loader.on("select", ({ file }) => {
    

    loaderError(false)
    
    var fd = new FormData();
    fd.append("file", file);
    var self = this;
    var url = "";

    $.ajax({
      url: `/foquz/api/questions/upload-detail-file?access-token=${window.ACCESS_TOKEN}&detail_id=${variant.id}`,
      type: "post",
      data: fd,
      contentType: false,
      processData: false,
      success: function (response) {
        if (response.file.previewUrl == '/uploads/') {
          response.file.previewUrl = '/img/audio-file-back.png'
        }
        variant.file(response.file)
        if (!variant.value().length) {
          variant.value(response.file.origin_name.replace(/\.[^/.]+$/, ""))
        }
        
        
      },
    });
  });


  return {
    translator: questionTranslator,
    variant,
    isSortable,
    showTooltips,
    canRemove: computed(() => {
      if (ko.toJS(isFullBlocked)) return false;
      if (!canRemove?.()) return false;
      if (!ko.toJS(isBlocked)) return true;
      return variant.id === '0';
    }),
    isBlocked,
    isFullBlocked,
    withPoints,
    randomOrder,
    randomExclusion,
    formControlErrorStateMatcher,
    formControlSuccessStateMatcher,
    donor,
    onRemove,
    enableFile,
    loaderError,
    loaderErrorText,
    onEnter() {
      if (isFullBlocked) return;
      onEnter();
    },
    focused,
    index,
    loader,
    useTooltips,
    tooltipPlaceholder
    
  };
}
