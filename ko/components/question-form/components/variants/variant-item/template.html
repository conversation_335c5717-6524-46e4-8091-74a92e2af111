<div class="d-flex align-items-start">
  <!-- ko if: donor -->
    <div
      class="fc-variant-item__index"
      data-bind="
        text: index() + 1,
        css: {'fc-variant-item__index_file': enableFile}
      "
    ></div>
  <!-- /ko -->
  <!-- ko if: isSortable -->
    <div
      class="fc-variant-item__drag-handle sortable-handle hide-on-dragging"
      data-bind="css: {'fc-variant-item__drag-handle_file': enableFile}"
    >
      <i class="icon icon-drag-arrow"></i>
    </div>
  <!-- /ko -->
  <!-- ko template: {
    foreach: templateIf(enableFile?.(), $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
    <!-- ko if: variant.file() -->
      <file-loader-preview
        class="file-loader-preview--lg my-0 mr-15p my-0"
        params="
          loading: false,
          file: variant.file(),
          preview: variant.file().previewUrl,
          onRemove: () => variant.file(null),
        "
      ></file-loader-preview>
    <!-- /ko -->
    <!-- ko if: !variant.file() -->
      <div class="survey-question__media-form-control-actions my-0 mr-15p">
        <!-- ko let: { fileInput: ko.observable(null) } -->
          <div class="survey__images-form-control-content m-0">
            <div
              data-bind="click: (_,e) => $component.loader.open()"
              class="survey__images-form-control-add-button survey__images-form-control-add-button_big m-0"
            >
              <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 9.43839L9.96279 17.4644C8.97817 18.4476 7.64274 19 6.25028 19C4.85782 19 3.52239 18.4476 2.53777 17.4644C1.55315 16.4811 1 15.1476 1 13.7571C1 12.3665 1.55315 11.033 2.53777 10.0497L10.575 2.02375C11.2314 1.36825 12.1217 1 13.05 1C13.9783 1 14.8686 1.36825 15.525 2.02375C16.1814 2.67924 16.5502 3.56829 16.5502 4.4953C16.5502 5.42231 16.1814 6.31135 15.525 6.96685L7.47904 14.9928C7.15083 15.3206 6.70569 15.5047 6.24154 15.5047C5.77738 15.5047 5.33224 15.3206 5.00403 14.9928C4.67583 14.6651 4.49144 14.2206 4.49144 13.7571C4.49144 13.2935 4.67583 12.849 5.00403 12.5213L12.429 5.11537" stroke="#73808D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span
                class="survey__images-form-control-add-button-label"
                data-bind="text: 'Загрузить изображение/видео до 5 Мб'"
              ></span>
            </div>
          </div>
          <!-- ko if: loaderError() -->
            <div class="mt-10p pt-15p file-loader-error position-relative">
              <div class="position-absolute file-loader-error-text" data-bind="text: loaderErrorText"></div>
            </div>
          <!-- /ko -->
          <!-- ko if: !loaderError() && !variant.file() -->
            <div class="mt-10p pt-15p file-loader-error position-relative">
              <div class="position-absolute file-loader-error-text" data-bind="text: 'Обязательное поле'"></div>
            </div>
          <!-- /ko -->
        <!-- /ko -->
      </div>
    <!-- /ko -->
  <!-- /ko -->
</div>
<div class="flex-grow-1">
  <div class="d-flex align-items-start">
    <div
      class="fc-variant-item__content hide-on-dragging"
      data-bind="
        descendantsComplete: (el) => {
          if (focused?.()) {
            setTimeout(function() {
              $(el).find('input').focus();
            }, 0)
          }
        }
      "
    >
      <div class="fc-variant-item__fields">
        <foquz-chars-counter
          class="fc-variant-item__value"
          params="
            value: variant.value,
            max: 250,
          "
          data-bind="
            css: {
              'foquz-chars-counter--hide-counter': variant.dictionaryElementId,
            },
          "
        >
          <input
            class="form-control"
            data-bind="
              textInput: $parent.variant.value,
              css: {
                'is-invalid': $parent.enableFile && $parent.enableFile?.()
                  ? false
                  : $parent.formControlErrorStateMatcher?.($parent.variant.value),
                'is-valid': $parent.formControlSuccessStateMatcher?.($parent.variant.value),
              },
              onEnter: $parent.onEnter,
              disable: $parent.donor || $parent.isFullBlocked || $parent.variant.dictionaryElementId,
            "
          />
          <!-- ko if: $parent.variant.dictionaryElementId -->
            <div class="fc-input__icons">
              <div
                data-bind="
                  component: {
                    name: 'fc-icon',
                    params: {
                      name: 'dictionary-connection',
                      color: 'light',
                    },
                  },
                  tooltip,
                  tooltipText: `Связка варианта с элементом справочника «${question.controller.dictionary().name}»`,
                "
                class="fc-input__append-icon"
              ></div>
            </div>
          <!-- /ko -->
        </foquz-chars-counter>

        <!-- ko if: withPoints -->
          <input
            type="text"
            class="fc-variant-item__points form-control"
            maxlength="9"
            data-bind="
              textInput: variant.points,
              onlyNumbers: {
                sign: true,
              },
              disable: isFullBlocked,
              attr: {
                placeholder: $component.translator.t('0 баллов'),
              },
            "
          />
        <!-- /ko -->
      </div>
      <!-- ko if: enableFile == undefined || !enableFile?.()-->
        <validation-feedback
          params="text: variant.value.error, show: formControlErrorStateMatcher?.(variant.value)"
        ></validation-feedback>
      <!-- /ko -->
    </div>
  </div>
  <!-- ko template: {
    foreach: templateIf($component.useTooltips && $component.useTooltips(), $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
    <div class="d-flex fc-variant-item__tooltip">
      <!-- ko template: {
        foreach: templateIf($component.showTooltips(), $data),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
        <div class="pt-15p fc-variant-item__tooltip-input">
          <fc-input
            class="ml-15p flex-grow-1"
            params="
              value: variant.description,
              maxlength: 500,
              disabled: isFullBlocked,
              placeholder: $component.tooltipPlaceholder,
            "
          ></fc-input>
          <!-- ko template: {
            foreach: templateIf(donor && variant.modified, $data),
            afterAdd: fadeAfterAddFactory(200),
            beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
            <div
              class="ml-15p fc-variant-item__tooltip-helper"
              data-bind="tooltip, tooltipPlacement: 'top', tooltipText: 'Текст подсказки для элемента изменён'"
            >
              <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_9511_30996)">
                  <path d="M12 11C11.5833 11.4167 10.8405 12.1369 10.2564 12.5449C9.6723 12.9529 9.00964 13.2392 8.30935 13.3863C7.60906 13.5333 6.88603 13.5378 6.18495 13.3997C5.48386 13.2615 4.81962 12.9835 4.23328 12.5828C3.64694 12.1822 3.15095 11.6675 2.77598 11.0704C2.40101 10.4734 2.15503 9.80673 2.05323 9.11166C1.95143 8.4166 1.99597 7.7079 2.18412 7.02936C2.37227 6.35083 2.62784 5.79988 3.14709 5.16669C3.69571 4.4977 4.25405 4.10898 4.8943 3.80456M12 11V13.0468M12 11H10M12 5.5L9 8.5L6 9.5L7 6.5L10 3.5M14 3.5L12 1.5" stroke="#73808D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </g>
                <defs>
                  <clipPath id="clip0_9511_30996">
                    <rect width="15" height="15" fill="white" transform="translate(0 0.5)"/>
                  </clipPath>
                </defs>
              </svg>
            </div>
          <!-- /ko -->
        </div>
      <!-- /ko -->
      <!-- ko template: {
        foreach: templateIf(!$component.showTooltips(), $data),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
        <div
          class="fc-variant-item__tooltip-value pt-5p"
          data-bind="text: variant.description && variant.description() && variant.description().length ? variant.description() : '–'"
        ></div>
      <!-- /ko -->
    </div>
  <!-- /ko -->
</div>

<!-- ko template: {
  foreach: templateIf(randomOrder(), $data),
  afterAdd: fadeAfterAddFactory(200),
  beforeRemove: fadeBeforeRemoveFactory(200)
} -->
  <div class="ml-15p">
    <button
      type="button"
      class="lock-shuffle f-btn f-btn-text"
      data-bind="
        click: () => randomExclusion(!randomExclusion()),
        css: { locked: randomExclusion() },
        attr: { disabled: !!isFullBlocked },
        tooltip,
        tooltipText: randomExclusion()
          ? 'Расположение варианта зафиксировано'
          : 'Зафиксировать расположение варианта'
      "
    >
      <fc-icon params="name: 'lock-shuffle', width: 16, height: 20" class="f-icon-sm"></fc-icon>
    </button>
  </div>
<!-- /ko -->
<!-- ko template: {
  foreach: templateIf(!donor && canRemove?.(), $data),
  afterAdd: fadeAfterAddFactory(200),
  beforeRemove: fadeBeforeRemoveFactory(200)
} -->
  <button
    type="submit"
    class="fc-variant-item__remove hide-on-dragging f-btn f-btn-danger f-btn--square ml-15p"
    data-bind="
      click: () => onRemove(),
      attr: {
        title: $component.translator.t('Удалить'),
        disabled: !!isFullBlocked,
      },
    "
  >
    <foquz-icon params="icon: 'times'" class="f-icon-sm"></foquz-icon>
  </button>
<!-- /ko -->
