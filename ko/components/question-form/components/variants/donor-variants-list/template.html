<!-- ko if: variants() && variants().length -->
  <!-- ko if: !$component.sorting() -->
    <div
      class="donor-variants-list__wrapper"
      data-bind="
        sortable,
        sortableItem: '.donor-variant',
        sortableHandle: '.sortable-handle',
        onSort: $component.onSort.bind($component, variants),
      "
    >
      <!-- ko foreach: {
        data: variants,
        as: 'variant',
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: $component.beforeRemove.bind($component),
      } -->
        <fc-variant-item
          class="variants-controller__variant variants-controller__variant--child"
          params="
            variant: variant,
            showTooltips: $component.showTooltips,
            useTooltips: $component.useTooltips,
            isSortable: !$component.isFullBlocked && (!variant.sortable || variant.sortable()),
            canRemove: ko.observable(false),
            enableFile: variant.file() &&  variant.file().file_id && $parents[1].type() !== '21' && $parents[1].type() !== '12' && $parents[1].type() !== '8' && $parents[1].type() !== '20' && $parents[1].type() !== '7',
            isBlocked: false,
            isFullBlocked: $component.isFullBlocked,
            randomOrder: $component.randomOrder,
            withPoints: $component.withPoints,
            donor: true,
            index: $index(),
          "
        ></fc-variant-item>
        <!-- ko if: $component.hasChildren && variant.children -->
          <div
            class="variants-controller__sortable-container variants-controller__children-container"
            data-bind="
              sortable,
              sortableItem: '.variants-controller__variant--child',
              sortableHandle: '.variants-controller__variant--child .sortable-handle',
              onSort: $component.onSort.bind($component, variant.children.list),
            "
          >
            <label class="form-label" data-bind="text: $translator.t('Ответы')"></label>
            <!-- ko foreach: {
              data: variant.children.list,
              as: 'childVariant',
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: $component.beforeRemove.bind($component)
            } -->
            <fc-variant-item
              class="variants-controller__variant variants-controller__variant--child"
              params="
                variant: childVariant,
                focused: $index() === $component.focused(),
                isSortable: !$component.isFullBlocked && (!childVariant.sortable || childVariant.sortable()),
                formControlErrorStateMatcher: $component.formControlErrorStateMatcher,
                formControlSuccessStateMatcher: $component.formControlSuccessStateMatcher,
                canRemove: variant.children.list().length > 1,
                isBlocked: false,
                isFullBlocked: $component.isFullBlocked,
                randomOrder: $component.randomOrder,
                onRemove: function() { variant.children.removeVariant(childVariant); },
                onEnter: function() {
                  if ($component.isVariantsValid()) {
                    var childVariant = variant.children.addVariant($index() + 1);
                    $component.focused($index() + 1);
                  }
                },
                withPoints: $component.withPoints,
                index: $index(),
              "
            ></fc-variant-item>
            <!-- /ko -->
            <!-- ko ifnot: $component.isFullBlocked -->
            <div class="survey-question__variants-child-actions">
              <fc-button
                params="
                  label: 'Добавить ответ',
                  color: 'success',
                  icon: 'plus',
                  inverse: true,
                  click: function() {
                    variant.children.addVariant();
                    $component.focused(variant.children.list().length - 1);
                  },
                "
              ></fc-button>
              <fc-button
                params="
                  label: 'Добавить списком',
                  color: 'success',
                  icon: 'plus',
                  inverse: true,
                  click: function() {
                    question.addVariantsFromList(variant.children, 'answers');
                  },
                "
              ></fc-button>
            </div>
            <!-- /ko -->
          </div>
        <!-- /ko -->
      <!-- /ko -->
    </div>
  <!-- /ko -->
<!-- /ko -->
