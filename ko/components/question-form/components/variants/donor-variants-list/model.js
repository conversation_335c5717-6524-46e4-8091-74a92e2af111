import { questionTranslator } from "@/components/question-form/translator";

const { observable, computed } = ko;

export default function (params, element) {
  element.classList.add("fc-donor-variants-list");
  const sorting = observable(false);
  const viewModel = {};
  
  viewModel.sorting = observable(false);
  viewModel.translator = params.questionTranslator;
  viewModel.hasChildren = params.hasChildren;
  viewModel.isFullBlocked = params.isFullBlocked;
  viewModel.variants = params.variants;
  viewModel.randomOrder = ko.isObservable(params.randomOrder) ? params.randomOrder : ko.observable(false)
  viewModel.withPoints = params.withPoints;
  viewModel.useTooltips = params.useTooltips;
  viewModel.showTooltips = params.showTooltips ? params.showTooltips : ko.observable(false)
  viewModel.formControlErrorStateMatcher = params.formControlErrorStateMatcher;
  viewModel.formControlSuccessStateMatcher = params.formControlSuccessStateMatcher;
  viewModel.tooltipPlaceholder = params.tooltipPlaceholder || '';
  viewModel.isVariantsValid = ko.observable(true);
  viewModel.focused = ko.observable("");
  viewModel.onSort = (list, event) => {
    sorting(true);
    viewModel.sorting(true)
    const { oldIndex, newIndex } = event.data;
    const movedItem = list()[oldIndex];
    list.remove(movedItem)
    list.splice(newIndex, 0, movedItem);
    sorting(false);
    viewModel.sorting(false)
    $('body').removeClass('body--dragging')
  };
  viewModel.beforeRemove = element => {
    const duration = sorting() ? 0 : 200;
    return $(element)
      .delay(0)
      .fadeOut(duration, function () {
        return $(element).remove();
      });
  };
  viewModel.afterAdd = element => {
    const duration = sorting() ? 0 : 200;
    return $(element).hide().delay(0).fadeIn(duration);
  };
  viewModel.addVariant = () => {
    params.variantsList.addVariant();
    viewModel.focused(params.variantsList.list().length - 1);
  },
  viewModel.removeVariant = variant => {
    if (typeof params.onRemove === "function") {
      params.onRemove(variant, () => {
        params.variantsList.removeVariant(variant);
      });
    } else params.variantsList.removeVariant(variant);
  };
  return viewModel;
}
