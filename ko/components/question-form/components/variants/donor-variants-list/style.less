.fc-donor-variants-list {
  margin-top: -7.5px;
  margin-bottom: -7.5px;

  .fc-variant-item__tooltip-helper {
    cursor: pointer;
  }

  .donor-variant {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;

    padding-top: 7.5px;
    padding-bottom: 7.5px;
    margin-left: -10px;
    padding-left: 10px;
    margin-right: -10px;
    padding-right: 10px;
    &.with-file {
      align-items: flex-start;
    }

    .sortable-handle {
      padding-right: 15px;
      cursor: move;
    }

    &__value {
      flex-grow: 1;
      height: 48px;
      display: flex;
      align-items: center;
      padding: 5px 15px;
      background: #f2f5f6;
      border: 1px solid #cfd8dc;
      border-radius: 4px;
      color: #73808d;
      font-size: 16px;
    }

    &__points {
      width: 100px;
      margin-left: 15px;
      flex-shrink: 0;
    }

    .fc-input__icons {
      right: 10px;
    }
  }
}
