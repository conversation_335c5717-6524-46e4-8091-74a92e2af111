import { questionTranslator } from "@/components/question-form/translator";

export default function (params, element) {
  const { value, disabled } = params;

  const types = [
    {
      id: 'selected',
      value: 1,
      label: questionTranslator.t("Выбранные варианты")(),
    },
    {
      id: 'unselected',
      value: 0,
      label: questionTranslator.t("Невыбранные варианты")(),
    },
  ].filter(Boolean);

  return {
    types,
    value,
    disabled
  }
}
