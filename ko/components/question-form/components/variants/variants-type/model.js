import {
  VARIANTS_TYPE_MULTIPLE,
  VARIANTS_TYPE_SINGLE,
} from "@/components/question-form/data/variant-types";

import { questionTranslator } from "@/components/question-form/translator";

export default function (params, element) {
  const { value, disabled } = params;

  const types = [
    {
      id: VARIANTS_TYPE_SINGLE,
      value: VARIANTS_TYPE_SINGLE,
      icon: "input-radio",
      label: questionTranslator.t("Выбор одного варианта")(),
    },
    {
      id: VARIANTS_TYPE_MULTIPLE,
      value: VARIANTS_TYPE_MULTIPLE,
      icon: "input-checkbox",
      label: questionTranslator.t("Выбор нескольких вариантов")(),
    },
  ].filter(Boolean);

  return {
    types,
    value,
    disabled
  }
}
