@import "Style/colors";

.fc-type-select {
  display: block;
  border: 1px solid #cfd8dc;
  border-radius: 4px;
  cursor: pointer;

  &__wrapper {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 10px 20px 10px 15px;
  }
  &__result {
    flex-grow: 1;
    display: flex;
    align-items: center;
    font-size: 16px;
  }
  &__toggler {
    flex-shrink: 0;
  }
  &__icon {
    margin-right: 15px;
    flex-shrink: 0;

    svg {
      color: #a6b1bc;
    }
  }

  &.disabled {
    cursor: default;

    .fc-type-select__wrapper {
      background-color: #f2f5f6;
    }

    .fc-type-select__icon {
      opacity: 0.5;
    }

    .fc-type-select__name {
      color: var(--f-color-service);
    }
  }
}
