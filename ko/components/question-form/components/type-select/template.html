<div
  class="fc-type-select__wrapper"
  data-bind="click: function() { openTypesDialog() }"
>
  <div class="fc-type-select__result">
    <span
      class="fc-type-select__icon f-icon f-icon-question-type"
      data-bind="class: 'f-icon-question-type--' + typeName()"
    >
      <svg>
        <use
          data-bind="attr: {
                  href: '#foquz-icon-question-' + typeName()
              }"
        />
      </svg>
    </span>

    <div class="fc-type-select__name">
      <div class="name" data-bind="text: typeLabel"></div>
      
    </div>
  </div>
  <div class="fc-type-select__toggler">
    <fc-icon
      params="name: 'chevron-bottom', color: 'secondary', size: 12"
    ></fc-icon>
  </div>
</div>
