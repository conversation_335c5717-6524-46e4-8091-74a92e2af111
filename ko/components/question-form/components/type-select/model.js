import { FComponent } from "Components/f-component";
import { Translator } from "@/utils/translate";
import { getTypeLabel, getTypeName } from "Data/question-types";
import "@/dialogs/question-type-dialog";
import { DialogsModule } from "@/utils/dialogs-module";
import {  CLASSIFIER_QUESTION, FILIALS_QUESTION } from "Data/question-types";

const QuestionTranslator = Translator("question");

export class ViewModel extends FComponent {
  constructor(params, element, nodes) {
    super(params, element, nodes);

    DialogsModule(this);

    element.classList.add("fc-type-select");

    this.translator = QuestionTranslator;

    const {
      value,
      disabled,
      onSelect,
      disableStartScreen,
      disableFilials,
      disableClassifier,
    } = params;

    this.rawValue = value;
    this.value = ko.computed(() => {
      const type = value();
      return type === FILIALS_QUESTION
        ? String(CLASSIFIER_QUESTION)
        : type;
    });
    
    this.disabled = disabled;
    this.onSelect = onSelect;
    this.disableStartScreen = disableStartScreen;
    this.disableFilials = disableFilials;
    this.disableClassifier = disableClassifier;

    this.typeLabel = ko.computed(() => {
      const type = this.value();
      if (type === "start") return QuestionTranslator.t("Стартовый экран");
      if (type === "end") return QuestionTranslator.t("Конечный экран");
      if (type === "five-second-test") return QuestionTranslator.t("Тест 5 секунд");
      return getTypeLabel(type);
    });

    this.typeName = ko.computed(() => {
      const type = this.value();
      if (type === "start") return "start";
      if (type === "end") return "end";
      if (type === "five-second-test") return "five-second-test";
      return getTypeName(type);
    });

    ko.applyBindingsToNode(element, {
      css: {
        disabled: this.disabled,
      },
    });
  }

  openTypesDialog() {
    if (ko.toJS(this.disabled)) return;

    this.openDialog({
      name: "question-type-dialog",
      params: {
        selected: this.value(),
        disableStartScreen: this.disableStartScreen(),
        disableFilials: ko.toJS(this.disableFilials),
        disableClassifier: ko.toJS(this.disableClassifier),
        onSelect: (typeId) => {
          if (typeId === "start" && this.disableStartScreen()) return;
          if (this.value() == typeId) return;

          this.rawValue(typeId);
          if (typeof this.onSelect === "function") this.onSelect(typeId);
        },
      },
    });
  }
}
