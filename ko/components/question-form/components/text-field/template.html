<!-- ko let: { $ctx: $component }-->
<div data-bind="using: controller"
     class="row">
  <!-- ko if: withLabel -->
  <div class="col-12" data-bind="css: {'d-flex mb-15p': $component.enableFile}">
    <!-- ko if: $component.enableFile -->
    <!-- ko if: $component.file && $component.file() -->
    <file-loader-preview class="file-loader-preview--lg my-0 mr-15p my-0" params="loading: false, file: $component.file(), preview: $component.file().previewUrl,
    onRemove: function() { 
      $component.file(null)
    }">

    </file-loader-preview>
    <!-- /ko -->
    <!-- ko if: !$component.file || !$component.file() -->
    <div class="survey-question__media-form-control-actions my-0 mr-15p" data-bind="css:{
    'disabled': false
    }">
    <!-- ko let: { fileInput: ko.observable(null) } -->
    <div class="survey__images-form-control-content m-0">

        <div data-bind="click: function (_,e) {
            
        $component.loader.open();
    }" class="survey__images-form-control-add-button survey__images-form-control-add-button_big m-0">

            <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 9.43839L9.96279 17.4644C8.97817 18.4476 7.64274 19 6.25028 19C4.85782 19 3.52239 18.4476 2.53777 17.4644C1.55315 16.4811 1 15.1476 1 13.7571C1 12.3665 1.55315 11.033 2.53777 10.0497L10.575 2.02375C11.2314 1.36825 12.1217 1 13.05 1C13.9783 1 14.8686 1.36825 15.525 2.02375C16.1814 2.67924 16.5502 3.56829 16.5502 4.4953C16.5502 5.42231 16.1814 6.31135 15.525 6.96685L7.47904 14.9928C7.15083 15.3206 6.70569 15.5047 6.24154 15.5047C5.77738 15.5047 5.33224 15.3206 5.00403 14.9928C4.67583 14.6651 4.49144 14.2206 4.49144 13.7571C4.49144 13.2935 4.67583 12.849 5.00403 12.5213L12.429 5.11537" stroke="#73808D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>


            <span class="survey__images-form-control-add-button-label" data-bind="text: 'Загрузить изображение/видео до 5 Мб'">

                
            </span>

        </div>

    </div>
    <!-- ko if: $component.loaderError() -->
    <div class="mt-10p pt-15p file-loader-error position-relative">
      <div class="position-absolute file-loader-error-text" data-bind="text: $component.loaderErrorText"></div>
    </div>
    <!-- /ko -->
    <!-- ko if: !$component.loaderError() && !$component.file() -->
    <div class="mt-10p pt-15p file-loader-error position-relative">
      <div class="position-absolute file-loader-error-text" data-bind="text: 'Обязательное поле'"></div>
    </div>
    <!-- /ko -->

    <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <div class="form-group w-100">
      
      <foquz-chars-counter params="value: label, max: 250">
        <input type="text"
               class="form-control"
               data-bind="textInput: $parent.label, attr: {
          placeholder: $parent.defaultLabel
        }, disable: $ctx.disabled">
      </foquz-chars-counter>
      <!-- ko if: $parent.useTooltip -->
      <!-- ko template: {
          foreach: templateIf($parent.showTooltip(), $data),
          afterAdd: slideAfterAddFactory(200),
          beforeRemove: slideBeforeRemoveFactory(200)
        } -->
        <div class="mt-15p ml-15p">
            <input type="text"
                   class="form-control"
                   data-bind="textInput: $ctx.tooltip, attr: {
              placeholder: 'Подсказка для своего варианта', maxlength: 500
            }, disable: $ctx.disabled">
        </div>
        <!-- /ko -->
        <!-- ko if: !$parent.showTooltip() -->
        <div class="fc-variant-item__tooltip-value mt-5p" data-bind="text: ($ctx.tooltip && $ctx.tooltip() && $ctx.tooltip().length) ? $ctx.tooltip() : '–'"></div>
        <!-- /ko -->
      <!-- /ko -->
    </div>
    
  </div>
  <!-- /ko -->

  <!-- ko if: $component.showAdditional -->

  <div class="col-12 col-md-6">
    <!-- ko template: {
      name: 'placeholder-template',
      data: {
        placeholder: placeholder,
        disabled: $ctx.disabled,
        translator: $component.translator,
      }
    } -->
    <!-- /ko -->
  </div>

  

  <div class="col-12 col-md-6">
    <div class="form-group">
      <label class="form-label"
             data-bind="text: $component.intervalText"></label>

      <div class="mt-10p ml-2">
        <!-- ko component: {
          name: 'interval-slider',
          params: {
            minLimit: 0,
            maxLimit: 3000,
            range: range,
            disabled: $ctx.disabled
          }
        } -->
        <!-- /ko -->
      </div>

    </div>
  </div>

  <!-- /ko -->

</div>
<!-- /ko -->
