import html from './template.html';
import './style.less';

import { Translator } from '@/utils/translate';
import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";

const QuestionTranslator = Translator('question');

ko.components.register('text-field', {
  viewModel: function (params) {
    this.controller = params.controller;
    this.translator = QuestionTranslator;
    this.useTooltip = params.useTooltip
    this.tooltip = params.tooltip
    this.showTooltip = params.showTooltip
    this.enableFile = params.enableFile

    this.loaderError = ko.observable(false)
    this.file = this.controller.file || ko.observable(null)

    this.loaderErrorText = ko.observable('Файл должен быть меньше 5 Мб')
    const loaderConfig = {
      presets: ["videoVar","image"],    
    };

    this.loader = new FoquzMultipleLoader(loaderConfig)

    this.loader.on("foquz.file.loader.errors.limit", (param) => {
      this.loaderError(true)
    })

    this.loader.on("select", ({ file }) => {
      

      this.loaderError(false)
      
      var fd = new FormData();
      fd.append("file", file);
      var self = this;
      var url = "";

      $.ajax({
        url: `/foquz/api/questions/upload-self-variant-file?access-token=${window.ACCESS_TOKEN}`,
        type: "post",
        data: fd,
        contentType: false,
        processData: false,
        success: function (response) {
          if (response.file.previewUrl == '/uploads/') {
            response.file.previewUrl = '/img/audio-file-back.png'
          }
          self.file(response.file)
          if (!self.controller.label().length) {
            self.controller.label(response.file.origin_name.replace(/\.[^/.]+$/, ""))
          }
          
          
        },
      });
    });
    

    

    this.showAdditional = true;

    if (params.controller.hideDetails) {
      this.showAdditional = false;
    }

    this.intervalText =
      params.intervalText || QuestionTranslator.t('Кол-во символов в ответе');
    this.disabled = params.disabled;
  },
  template: html
});
