import '../../components/text-field';

class TextField {
  constructor(config = {}) {
    this.withLabel = config.withLabel;
    this.defaultLabel = config.defaultLabel;
    this.label = ko.observable('');

    this.placeholder = ko.observable('');
    this.placeholderLimit = 60;
    this.range = ko.observableArray([0, 250]);
    this.minLimit = 0;
    this.maxLimit = 500;
    this.hideDetails = config.hideDetails

    this.file = config.file


    this.isChanged = ko.observable(false).extend({ notify: 'always' });
    this.required = ko.observable(false);

    [
      this.placeholder,
      this.range,
      this.label,
    ].forEach((f) => {
      f.subscribe((v) => this.isChanged(true));
    });

    if (ko.isObservable(config.file)) {
      config.file.subscribe((v) => this.isChanged(true))
    }
  }

  updateData(data) {
    if (data.range) this.range(data.range);
    this.placeholder(data.placeholder || '');
    let label = (data.label && String(data.label)) || '';
    this.label(label.trim());
    this.required(data.required)
  }

  updateReuired(val) {
    this.required(val)
  }

  getData() {
    return {
      placeholder: this.placeholder(),
      range: this.range(),
      label: this.label().trim(),
      required: this.required(),
      file: this.file
    };
  }
}

export default TextField;
