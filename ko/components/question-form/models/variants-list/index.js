import { get as _get } from "lodash";

import {
  questionTranslator,
  validationTranslator,
} from "@/components/question-form/translator";
import { VariantItemModel } from "./variant-item";

const { observable, observableArray, computed } = ko;

export function VariantsListModel({
  min = 0,
  hasChildren = false,
  useTooltips,
  clarifyingToEach,
  clarifyingQuestionEnabled,
  fromOne,
} = {}) {
  const isChanged = observable(false).extend({
    notify: "always",
  });

  const list = observableArray([]);
  list.subscribe((v) => isChanged(true));

  const count = observable().extend({
    validation: {
      validator: () => {
        return list().length >= min;
      },
      message: () => {
        let options = window.utils.declOfNum(min, [
          "вариант",
          "варианта",
          "вариантов",
        ]);

        return validationTranslator.t("Нужно добавить хотя бы {options}", {
          options: validationTranslator.t(`{count} ${options}`, {
            count: min,
          })(),
        })();
      },
    },
  });

  const isVariantsValid = computed(() => {
    const isVariantsValid = !list().some((v) => !v || !v.value || !v.value.isValid());
    return isVariantsValid;
  })


  const isValid = computed(() => {
    if (!count.isValid()) return false;
     let isValid = getInvalidIndex(list()) < 0;
    return isValid;
  });

  const isValidWithFile = computed(() => {
    if (!count.isValid()) return false;
    return !list().some((v) => !v || !v.file());
  });

  function getVariantModel(data) {  
    const model = new VariantItemModel({
      clarifyingToEach,
      clarifyingQuestionEnabled,
      fromOne,
      ...data
    });
    if (hasChildren) {
      if (!model.children) {
        model.children = new VariantsListModel();
      }
      if (_get(data, 'variants.length')) {
        model.children.update(data.variants);
      }
    }
    
    model.subscriptions = [
      model.value.subscribe(() => isChanged(true)),
      model.points.subscribe(() => isChanged(true)),
      model.needExtra.subscribe(() => isChanged(true)),

      model.isChanged.subscribe(() => {
        isChanged(true)
      }),
    ];
    if (model.children && model.children.isChanged) {
      model.subscriptions.push(model.children.isChanged.subscribe(() => isChanged(true)))
    }
    if (model.description) {
      model.subscriptions.push(model.description.subscribe(() => isChanged(true)))
    }
    return model;
  }

  function getInvalidIndex(list) {
    return list.findIndex((v) => {
      let isValid = true;
      isValid = v && v.isValid && v.isValid();
      isValid = v.children
        ? isValid && !v.children.list().some((c) => !(c && c.isValid && c.isValid()))
        : isValid;
      return !isValid;
    });
  }

  function addVariant(index, random, tooltip) {
    
    const model = getVariantModel({random, useTooltip: tooltip});
    if (hasChildren && !model.children) {
      model.children = new VariantsListModel();
    }
    if (index === undefined) {
      list.push(model);
    } else {
      list.splice(index, 0, model);
    }
    return model;
  }

  function updateEmptyVariantLabel(params, index) {
    const model = getVariantModel({ value: params.label, type: 1, ...params });
    this.list()[index] = model
    this.isChanged(true)

  }

  function addEmptyVariant(params = {}) {
    const model = getVariantModel({ value: params.label, type: 1, ...params });
    
    if (list().some(item => item.type === 1)) {
      list(list().filter(item => item.type !== 1))
    } else {
      list.push(model);
    }
  }

  function addExternalVariant(name, index, params = {}) {
    const model = getVariantModel({ value: name, ...params });

    if (index === undefined) {
      list.push(model);
    } else {
      list.splice(index, 0, model);
    }

    return model;
  }

  function removeVariant(variant) {
    if (!variant) {
      return;
    }
    if (variant.subscriptions && variant.subscriptions.length) {
      variant.subscriptions.forEach((s) => {
        if (typeof s?.dispose === 'function') {
          s.dispose();
        }
      });
    }
    list.remove(variant);
  }

  function getVariants() {
    return list().map((v) => v.getData());
  }

  const getVariantsComputed = ko.computed(() => {
    return list().map((v) => v.getData());
  });

  function update(variants, { noModel, tooltip, noSort = false } = {}) {
    
    if (!noSort) {
      variants.sort((a, b) => {
        if ((a.position || a.position === 0) && (b.position || b.position === 0)) {
          return +a.position - +b.position;
        } else {
          return 0;
        }
      });
    }
    if (!variants) {
      list([]);
    } else if (noModel) {
      list(variants);

      // @NOTE: добавляем подписки на изменения подсказок (тултипов) вариантов
      addSubscriptions(list);
    } else {
      list(variants.map((v) => {
        if (tooltip) {
          v.useTooltip = true
        }
        const variant = getVariantModel(v)
        if (v.modified) {
          variant.modified = true
        }

        return variant
      }));
    }
    if (!list().length) {
      addVariant(undefined, undefined, tooltip);
    }
  }

  /**
   * Добавляет подписки на изменения подсказок (тултипов) вариантов
   * @param {*} list (observableArray) список вариантов
   */
  function addSubscriptions(list) {
    const unwrappedList = ko.unwrap(list);
    if (Array.isArray(unwrappedList)) {
      unwrappedList.forEach((v) => {
        if (typeof v?.description === 'function') {
          v.description.subscribe((v) => {
            isChanged(true);
          });
        }
      });
    }
  }

  function move(oldIndex, newIndex) {
    const movedItem = list()[oldIndex];

    list.remove(movedItem);
    list.splice(newIndex, 0, movedItem);
  }

  addVariant(undefined, undefined, useTooltips);

  return {
    list,
    isChanged,
    isValid,
    isVariantsValid,
    count,
    getInvalidIndex,
    addVariant,
    addExternalVariant,
    removeVariant,
    getVariants,
    getVariantsComputed,
    update,
    move,
    addEmptyVariant,
    updateEmptyVariantLabel,
    isValidWithFile
  };
}
