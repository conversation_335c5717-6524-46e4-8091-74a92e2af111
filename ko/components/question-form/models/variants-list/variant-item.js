import { bindAll } from 'lodash';
import { AnswersLimit } from "../types/variants/answers-limit";
import TextField from '../text-field';
import { Translator } from '@/utils/translate';
import { validationTranslator } from "@/components/question-form/translator";

const QuestionTranslator = Translator('question');
const ValidationTranslator = Translator("validation");

const { observable, observableArray, computed, isObservable } = ko;

export function VariantItemModel(data) {
  return new _VariantItemModel(data);
}

// Generate a unique ID by combining timestamp and random number
const generateUniqueId = () => `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

class _VariantItemModel {
  constructor({
    id,
    random,
    points = '',
    name,
    value = '',
    description = '',
    dictionary_element_id = null,
    donor_variant_id = null,
    donorId = null,
    _recipientId = null,
    type = 0,
    file = null,
    needExtra,

    clarifyingToEach,
    clarifyingQuestionEnabled,
    fromOne,

    selfVariantFile = null,
    detail_question = '',
    text_variant_minlength = 0,
    text_variant_maxlength = 250,
    placeholder_text = '',
    variants_with_files = 0,
    is_self_answer = 0,
    self_variant_minlength = 0,
    self_variant_maxlength = 250,
    self_variant_text = '',
    self_variant_placeholder_text = '',
    variants_element_type = 0,
    for_all_rates = 1,
    extra_required = 1,
    extra_question_rate_from = 0,
    extra_question_rate_to = 10,
    min_choose_extra_variants = '',
    max_choose_extra_variants = '',
    detail_question_options = [],
    random_exclusion = 0,
  } = {}) {
    this.id = id || (random ? "new" + Math.random() : '0');

    // @NOTE: Получаем идентификатор, который не будет возвращать '0'
    this.persistentId = (id && id !== '0') ? id : generateUniqueId();
    this.dictionary_element_id = dictionary_element_id;
    this.donor_variant_id = donor_variant_id;
    this.donorId = donorId;

    /**
     * @type { number | string | null }
     * @description
     * Айдишник варианта-реципиента.
     * Сейчас, если включена опция "Донор-Реципиент", то в `id` лежит айдишник варианта-донора
     * В некоторых случая нужно получать айдишник варианта-реципиента.
     */
    this._recipientId = _recipientId;
    this.type = type;

    this.clarifyingToEach = clarifyingToEach;
    this.fromOne = fromOne;
    this.isChanged = ko.observable(false).extend({
      notify: 'always'
    });
    this.points = observable(points);
    this.randomExclusion = observable(!!random_exclusion);
    this.value = observable(name || value || '').extend({
      required: {
        message: () => validationTranslator.t("Обязательное поле")(),
      },
    });

    if (this.clarifyingToEach && isObservable(this.clarifyingToEach) && this.clarifyingToEach()) {
      needExtra = this.value()
        ? !!needExtra
        : false;
      this.clarifyingToEach.subscribe((flag) => {
        this.needExtra(!flag)
      });
    } else {
      needExtra = needExtra === undefined
        ? true
        : !!needExtra;
      if (clarifyingQuestionEnabled && isObservable(clarifyingQuestionEnabled) && !clarifyingQuestionEnabled()) {
        needExtra = true;
      }
    }

    this.needExtra = observable(needExtra);

    this.file = observable(file);

    this.selfVariantFile = observable(null);
    if (selfVariantFile) {
      this.selfVariantFile({
        id: selfVariantFile.file_id,
        fileUrl: selfVariantFile.file_url,
        previewUrl: selfVariantFile.preview_url
      })
    }
    this.description = observable(description)
  
    this.clarifyingQuestionText = observable(detail_question || '').extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
      },
    });;
    this.enableFile = observable(!!variants_with_files);
    this.variantsType = observable(variants_element_type || 0);
    this.clarifyingQuestionForAllRates = observable(!!for_all_rates);
    this.clarifyingQuestionIsRequired = observable(!!extra_required);
    this.clarifyingQuestionForRates = observableArray([
      extra_question_rate_from || 0,
      extra_question_rate_to || 10,
    ]);
    if (this.fromOne && isObservable(this.fromOne)) {
      this.fromOne.subscribe((v) => {
        this.startRate = v ? 1 : 0
          let [start, end] = this.clarifyingQuestionForRates()
          if (start == 0 || start == 1) {
            start = v ? 1 : 0
            this.clarifyingQuestionForRates([start, end])
          }
      })
    }
    this.customAnswerEnabled = observable(!!is_self_answer);

    this.variants = observableArray([]);
    if (Array.isArray(detail_question_options) && detail_question_options.length) {
      detail_question_options
        .forEach((variant) => this.addVariant(null, variant));
    } else {
      this.addVariant();
    }
  
    this.answersCountLimit = AnswersLimit({
      required: this.clarifyingQuestionIsRequired,
      list: this.variants,
    });
    this.answersCountLimit.value(max_choose_extra_variants || '');
  
    this.answersCountLimitMin = AnswersLimit({
      required: this.clarifyingQuestionIsRequired,
      list: this.variants,
      maxVal: this.answersCountLimit.value
    });
    this.answersCountLimitMin.value(min_choose_extra_variants || '');
  
    this.textAnswerPlaceholder = observable(placeholder_text || '');
    this.textAnswerRange = ko.observableArray([text_variant_minlength || 0, text_variant_maxlength || 250]);
    this.textAnswerField = new TextField({});
    this.textAnswerField.updateData({
      range: this.textAnswerRange(),
      placeholder: this.textAnswerPlaceholder(),
    });
  
    this.customAnswerText = observable(self_variant_text || '');
    this.customAnswerPlaceholder = observable(self_variant_placeholder_text || '');
    this.customAnswerRange = ko.observableArray([self_variant_minlength || 0, self_variant_maxlength || 250]);
    this.customAnswerField = new TextField({
      withLabel: true,
      defaultLabel: QuestionTranslator.t('Свой вариант'),
      file: this.selfVariantFile
    });
    this.customAnswerField.updateData({
      range: this.customAnswerRange(),
      placeholder: this.customAnswerPlaceholder(),
      label: this.customAnswerText(),
    });

    bindAll(this, [
      'addVariant',
      'removeVariant',
      'getData',
      '_createVariantModel',
    ]);

    [
      this.variantsType,
      this.customAnswerEnabled,
      this.variants,
      this.answersCountLimitMin.value,
      this.answersCountLimit.value,
      this.customAnswerField.isChanged,
      this.textAnswerField.isChanged,
      this.file,
      this.selfVariantFile,
      this.clarifyingQuestionText,
      this.clarifyingQuestionForRates,
      this.answersCountLimitMin?.value,
      this.answersCountLimit?.value,
      this.clarifyingQuestionIsRequired,
      this.clarifyingQuestionForAllRates,
      this.enableFile,
      this.randomExclusion,
    ]
    .forEach((f) => f && f.subscribe((v) => this.isChanged(true)));

    this.isVariantsValid = computed(() => {
      if (!this.needExtra()) return true;
      if (!this.clarifyingQuestionText.isValid()) return false;
      if (this.variantsType() == 2) return true;
      if (this.enableFile && this.enableFile()) {
        if (this.customAnswerEnabled()) {
          let fieldData = this.customAnswerField.getData();
          return !this.variants().some((v) => !v.file()) && fieldData.file();
        } else {
          return !this.variants().some((v) => !v.file());
        }
      } else {
        return !this.variants().some((v) => !v.value.isValid())
      }
    })

    this.isValid = computed(() => {
      if (!this.value()) return false;
      if (this.clarifyingToEach && this.clarifyingToEach()) {
        return this.isVariantsValid();
      } else {
        return true;
      }
    });
  }

  _createVariantModel(variantData) {
    return new VariantModel(
      variantData,
      this.enableFile,
      () => this.isChanged(true),
    );
  }

  addVariant(index, data) {
    let variant = this._createVariantModel(data);

    if (index || index === 0) {
      this.variants.splice(index, 0, variant);
    } else {
      this.variants.push(variant);
    }

    return variant;
  }

  /**
   * Удаляет вариант из списка
   * И отписывается от его изменений
   * @param { VariantModel } variant
   */
  removeVariant(variant) {
    if (variant.subscriptions) {
      variant.subscriptions.forEach((s) => s.dispose());
    }
    this.variants.remove(variant);
  }
  
  getData() {
    const customAnswer = this.customAnswerField.getData();
    const textVariant = this.textAnswerField.getData();
    const data = {
      file: this.file() || null,
      id: this.id,
      random_exclusion: this.randomExclusion() ? 1 : 0,
      persistentId: this.persistentId,
      value: this.value() ? this.value().trim() : '',
      description: this.description ? this.description() : undefined,
      points: this.points() || '',
      needExtra: this.needExtra,
      need_extra: this.needExtra() ? 1 : 0,
      children: this.children,
      dictionary_element_id: this.dictionary_element_id,
      donor_variant_id: this.donor_variant_id,
      donorId: this.donorId,
      _recipientId: this._recipientId,
      type: this.type,
      detail_question: this.clarifyingQuestionText(),

      variants_with_files: this.enableFile() ? 1 : 0,
      
      variants_element_type: this.variantsType(),

      for_all_rates: this.clarifyingQuestionForAllRates() ? 1 : 0,
      extra_required: this.clarifyingQuestionIsRequired() ? 1 : 0,

      extra_question_rate_from: this.clarifyingQuestionForRates()[0],
      extra_question_rate_to: this.clarifyingQuestionForRates()[1],

      min_choose_extra_variants: this.answersCountLimitMin.value(),
      max_choose_extra_variants: this.answersCountLimit.value(),

      is_self_answer: this.customAnswerEnabled() ? 1 : 0,
      self_variant_minlength: customAnswer.range[0],
      self_variant_maxlength: customAnswer.range[1],
      self_variant_text: customAnswer.label,
      self_variant_placeholder_text: customAnswer.placeholder,

      text_variant_minlength: textVariant.range[0],
      text_variant_maxlength: textVariant.range[1],
      placeholder_text: textVariant.placeholder,

      self_variant_file_id: (customAnswer.file && customAnswer.file()?.id) ? customAnswer.file().id : null,
      selfVariantFile: (customAnswer.file && customAnswer.file()?.id)
        ? { file_id: customAnswer.file().id, file_url: customAnswer.file().fileUrl, preview_url: customAnswer.file().previewUrl }
        : null,
    };

    if (this.clarifyingToEach && this.clarifyingToEach() && this.needExtra()) {
      data.detail_question_options = this.variants().map((variant) => variant.getData())
    }

    return data;
  }
}

class VariantModel {
  /**
   * @constructor
   * @param { VariantModel } data
   */
  constructor({
    id = '0',
    file = '',
    points = '',
    value = '',
    variant = '',
    file_id,
    file_url,
    preview_url,
    extra_question,
  } = {}, fileEnabled, isChanged) {
    this.id = id;
    this.file = ko.observable(file);
    this.fileEnabled = fileEnabled
    this.simple = file === undefined
    
    if (file_id) {
      this.file({id: file_id, fileUrl: file_url, previewUrl: preview_url})
    }
    this.points = ko.observable(points);
    this.value = ko.observable(value || variant).extend({
      required: {
        message: () => ValidationTranslator.t('Обязательное поле')(),
        onlyIf: () => !(this.fileEnabled && this.fileEnabled()),
      }
    });
    this.extra_question = extra_question;
    this.subscriptions = [
      this.value.subscribe((v) => isChanged()),
      this.points.subscribe((v) => isChanged()),
      this.file.subscribe((v) => isChanged())
    ];
  }

  /**
   * @returns { VariantData }
   */
  getData() {
    const fileId = this.file() ? this.file().id : null
    return {
      id: this.id,
      value: this.value().trim(),
      points: this.points(),
      extra_question: this.extra_question,
      file: this.file(),
      file_id: fileId,
      donorId: this.donorId,
    };
  }
}