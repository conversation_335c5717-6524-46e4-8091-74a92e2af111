import { SCALE_QUESTION } from "Data/question-types";
import { Translator } from "@/utils/translate";
import GalleryQuestion from "../gallery";
import { VariantsListModel } from "Components/question-form/models/variants-list";
import { Donor } from "Components/question-form/models/donor";

const QuestionTranslator = Translator("question");
const ValidationTranslator = Translator("validation");

class ScaleQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    super(controller, config);
    this.type = SCALE_QUESTION;
    this.translator = QuestionTranslator;

    this.scaleType = ko.observable("standart");

    this.galleryEnabled = ko.observable(false);

    this.randomOrder = ko.observable(false);

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");
    this.skipVariant = ko.observable(false);

    this.variantsList = VariantsListModel();

    // donor
    this.donor = Donor(controller.donors);
    this.blockRecipients = controller.blockRecipients;
    this.disableDonors = ko.computed(() => {
      if (this.isFullBlocked) return true;
      if (ko.toJS(this.isBlocked)) return true;
      if (this.blockRecipients) return true;
      if (!this.donor.donorsList().length) return true;
      return false;
    });
    this.donorVariants = ko.observableArray([]);

    this.donor.useDonor.subscribe((v) => {
      if (v) this.variantsList.update([]);
    });
    this.donor.donorId.subscribe((v) => {
      const donorVariants = this.donor.getDonorVariants(v);
      this.donorVariants(donorVariants);
    });

    ko.validation.rules['isGreaterThan'] = {
      validator: function (val, otherVal) {
        return parseFloat(val) > parseFloat(otherVal);
      },
      message: 'Поле должно быть больше чем {0}'
    };
    ko.validation.rules['isLessThan'] = {
      validator: function (val, otherVal) {
        return parseFloat(val) < parseFloat(otherVal);
      },
      message: 'Поле должно быть меньше чем {0}'
    };
    ko.validation.rules['stepValue'] = {
      validator: function (val, params) {
        let min = ko.utils.unwrapObservable(params[0]);
        let max = ko.utils.unwrapObservable(params[1]);
        return parseFloat(val) > 0 && parseFloat(val) <= max - min;
      }
    };
    ko.validation.registerExtenders();

    this.minValue = ko.observable(0).extend({
      number: {
        message: () => ValidationTranslator.t("Введите число")(),
      }, min: {
        params: 0, message: () => ValidationTranslator.t("Целые положительные числа или 0")(),
      },
    });
    this.maxValue = ko.observable(100).extend({
      number: {
        message: () => ValidationTranslator.t("Введите число")(),
      }, min: {
        params: 1, message: () => ValidationTranslator.t("Значение должно быть больше 0")(),
      },
      isGreaterThan: {
        params: this.minValue,
        message: () => ValidationTranslator.t("Максимальное значение должно быть больше минимального")(),
      },
    });
    this.stepValue = ko.observable(10).extend({
      number: {
        message: () => ValidationTranslator.t("Введите число")(),
      },
      min: {
        params: 1,
        message: () => ValidationTranslator.t("Значение должно быть больше 0")(),
      },
      stepValue: {
        params: [this.minValue, this.maxValue],
        message: () => ValidationTranslator.t(`Значение шага должно быть в диапазоне 1 - ${this.maxValue() - this.minValue()}`),
      },
    });

    this.usesDictionaryElements = ko.computed(() => {
      if (this.donor.useDonor()) {
        return !!this.donorVariants().find(el => el.dictionaryElementId);
      }
      return !!this.variantsList.list().find(el => el.dictionaryElementId);
    }, this);

    this.dictionaryBadgeTooltipText = ko.computed(() => {
      if (!controller.question()) {
        return '';
      }
      return `
        ${controller.question().dictionaryElementId() ? `Связка вопроса с элементом ${controller.dictionary().name} справочника ${controller.dictionary().name}` : ''}
        ${controller.question().usesDictionaryElements() ? `${controller.question().dictionaryElementId() ? '.' : ''} В вопросе есть варианты, связанные с элементами справочника ${controller.dictionary().name}` : ''}
      `;
    }, this);

    this.commentIsRequired = ko.observable(null);

    [
      this.minValue,
      this.maxValue,
      this.stepValue,
      this.scaleType,
      this.galleryEnabled,
      this.skip,
      this.skipText,
      this.skipVariant,
      this.donorVariants,
      this.randomOrder,
      this.commentIsRequired
    ].forEach((f) => f.subscribe((v) => this.onChange()));

    this.subscriptions.push(this.variantsList.isChanged.subscribe((v) => this.onChange(true)), this.donor.isChanged.subscribe((v) => this.onChange(true)));
    this.skip.subscribe((v) => {
      if (!v) {
        this.skipVariant(v);
      }
    });
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
    //super.updateCommentRequired(this.commentIsRequired())
  }

  getLogicVariants(ids) {
    return this.variantsList
      .list()
      .filter((variant) => {
        return ids.find((id) => variant.id == id);
      })
      .map((variant) => variant.value);
  }

  onDonorsTogglerClick() {
    if (this.isFullBlocked) return false;
    if (ko.toJS(this.isBlocked)) {
      this.tryChangeBlockedParam();
    } else if (this.blockRecipients) {
      this.tryChangeBlockedParam();
    } else if (!this.donor.donorsList().length) {
      this.noDonorsInfo();
    } else return true;
  }

  addVariantsFromDictionary() {
    this.openDialog({
      name: "add-variants-list-dialog",
      params: {
        hasAnswers: parseInt(this.countAnswers) > 0,
        mode: 'dictionary',
        dictionary: this.controller.dictionary,
        checked: this.variantsList.list().filter(el => el.dictionaryElementId).map(el => `${el.dictionaryElementId}`),
        headerText: 'Добавление вариантов из справочника',
      },
      events: {
        submit: async (result) => {
          this.controller.dictionaryElements()
            .filter(el => result.newDetails.find(detail => detail == el.id))
            .forEach(el => {
              if (this.variantsList.list().find(variant => variant.dictionaryElementId == el.id)) {
                return;
              }
              this.variantsList.addExternalVariant(
                el.title,
                undefined,
                {
                  dictionary_element_id: el.id,
                }
              )
            });
        },
      },
    });
  }

  updateData(data) {
    const {
      variants, randomOrder,

      donorId, donorVariantsType,
    } = data;
    this.donor.update({ donorId, donorVariantsType });
    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.scaleCommentRequired)
    }

    data.gallery = data.scaleGallery;
    data.galleryCommentEnabled = data.scaleCommentEnabled;
    data.galleryCommentLengthRange = data.scaleCommentLengthRange;
    data.galleryCommentPlaceholder = data.scaleCommentPlaceholder;
    data.galleryCommentLabel = data.scaleCommentLabel;
    data.galleryCommentRequired = data.scaleCommentRequired;
    
    super.updateData(data);

    this.galleryEnabled(data.scaleGalleryEnabled);
    this.minValue(data.scaleVariants.minValue);
    this.maxValue(data.scaleVariants.maxValue);
    this.stepValue(data.scaleVariants.stepValue);
    this.scaleType(data.scaleType);

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
    this.skipVariant(!!data.skipVariant);

    if (donorId) {
      const donorType = window.QUESTIONS.find(i => i.id == donorId).main_question_type
      const donorVariants = this.donor.getDonorVariants(donorId);
      let currentOrder
      if (donorType !== 19) {

        currentOrder = variants.map((v) => v.donorId || "custom");
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(a.id);
          const bIndex = currentOrder.indexOf(b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      } else {
        const donorIds = donorVariants.map(i => +i.id)
        const temp_variants = variants.filter(i => donorIds.includes(i.dictionary_element_id)).sort((a, b) => a.position - b.position)
        currentOrder = temp_variants.map((v) => v.dictionary_element_id);
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(+a.id);
          const bIndex = currentOrder.indexOf(+b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      }

        
        donorVariants.forEach((variant) => {
          const data = variants.find((v) => {
            if (variant.id === "custom") return !v.donorId;
            return v.donorId === variant.id;
          });

          if (data) {
            variant.points(data.points);
          }
        });
      
      
      this.donorVariants(donorVariants);
    } else {
      this.variantsList.update(variants);
    }

    console.log(this.variantsList.list())
    console.log(this.donorVariants())

    this.randomOrder(randomOrder);
  }


  getData() {
    let data = super.getData();
    data.comment = {
      ...this.commentField.getData(),
    };

    data.scaleGalleryEnabled = this.galleryEnabled() ? 1 : 0;
    data.scaleGallery = data.scaleGalleryEnabled ? data.gallery : [];

    if (!data.scaleVariants) {
      data.scaleVariants = {}
    }

    data.scaleType = this.scaleType();
    data.randomOrder = this.randomOrder();


    data.scaleVariants.minValue = parseInt(this.minValue());
    data.scaleVariants.maxValue = parseInt(this.maxValue());
    data.scaleVariants.stepValue = parseInt(this.stepValue());

    data.scaleCommentEnabled = data.galleryCommentEnabled;
    data.scaleCommentLabel = data.galleryCommentLabel;
    data.scaleCommentLengthRange = data.galleryCommentLengthRange;
    data.scaleCommentPlaceholder = data.galleryCommentPlaceholder;
    data.scaleCommentRequired = data.galleryCommentRequired;

    data.skip = this.skip();
    data.skipText = this.skipText();
    data.skipVariant = this.skipVariant();

    if (this.donor.useDonor()) {
      const donorId = ko.unwrap(this.donor.donorId)
      const donorQestion = window.QUESTIONS.find(i => i.id == donorId)
      const isDonorClassifierWithListType = donorQestion?.main_question_type === 19 && donorQestion?.dictionary_list_type === 'list'

      data = {
        ...data, ...this.donor.getData(), variants: ko.toJS(this.donorVariants).map((v) => {
          if (v.id === "custom") {
            return {
              value: v.value, points: v.points || "",
            };
          }
          return {
            id: v.id, value: v.value, points: v.points || "",
          };
        }),
        isDonorClassifierWithListType,
      };
    } else {
      data = {
        ...data, variants: this.variantsList.getVariants(),

        donor: null,
      };
    }

    return data;
  }

  isVariantsValid() {
    return this.variantsList.isVariantsValid();
  }

  isValid() {
    if (!this.defaultValidation()) return false;
    if (!this.minValue.isValid()) return false;
    if (!this.maxValue.isValid()) return false;
    if (!this.stepValue.isValid()) return false;
    if (this.galleryEnabled()) {
      if (!super.isValid()) return false;
    }

    if (this.scaleType() === "variants") {
      if (this.donor.useDonor()) {
        return true;
      }

      return this.variantsList.isValid();
    }
    return true;
  }
}

export default ScaleQuestion;
