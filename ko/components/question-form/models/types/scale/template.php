<!-- Блок шкалы для вариантов. Выбор варианта для оценки -->
<template id="question-form-scale-variants-list">
  <fc-label params="text: 'Варианты для оценки'"></fc-label>
  <fc-variants-list params="
            variantsList: question.variantsList,
            isBlocked: question.isBlocked,
            isFullBlocked: question.isFullBlocked,
            withPoints: question.withPoints,
            canRemoveSingleVariant: false,
            formControlErrorStateMatcher: question.formControlErrorStateMatcher,
            formControlSuccessStateMatcher: question.formControlSuccessStateMatcher
        ">

  </fc-variants-list>

  <!-- ko ifnot: question.isFullBlocked -->
  <button class="mt-15p survey-question__variants-control-add-button variants-controller__add-button" data-bind="
        click: function() { question.variantsList.addVariant(); },
        attr: { disabled: !question.isVariantsValid() }
        ">
    <span class="survey-question__variants-control-add-button-icon"></span>
    <span data-bind="text: question.translator.t('Добавить вариант')"></span>
  </button>
  <!-- /ko -->
</template>

<!-- Блок с вопросом-донором -->
<template id="question-form-scale-variants-donor">
  <div>
    <div class="form-group">
      <fc-label params="text: 'Вопрос-донор', hint: 'Вопрос-донор'"></fc-label>
      <fc-select params="
                options: question.donor.donorsList,
                value: question.donor.donorId
                "></fc-select>
    </div>

    <div class="form-group">
      <fc-label params="text: 'Варианты ответов'"></fc-label>
      <fc-donor-variants-type params="
            value: question.donor.donorVariantsType,
            isFullBlocked: question.isFullBlocked,
            disabled: question.isBlocked() || question.isFullBlocked" data-bind="click: function() {
                if (question.isBlocked() && !question.isFullBlocked)
                    question.tryChangeBlockedParam();
                else return true;
            }" class="mb-4"></fc-donor-variants-type>
    </div>

    <div class="form-group">
      <fc-label params="text: 'Варианты для оценки'"></fc-label>
      <fc-donor-variants-list params="variants: question.donorVariants, withPoints: question.withPoints">
      </fc-donor-variants-list>
    </div>
  </div>
</template>

<!-- Основной блок -->
<template id="question-form-template-scale">

  <!-- ko template: {
        foreach: templateIf(question.scaleType() === 'variants', $data),
        afterAdd: slideAfterAddFactory(400),
        beforeRemove: slideBeforeRemoveFactory(400)
     } -->
  <div>
    <!-- ko ifnot: question.isAuto || question.isSystem || question.mode === 'cpoint' -->
    <div class="form-group">
      <div data-bind="
             click: function() {
               return question.onDonorsTogglerClick();
             }">
        <fc-switch params="checked: question.donor.useDonor,
                     disabled: question.disableDonors || question.isFullBlocked,
                     label: 'Использовать варианты ответов респондента из другого вопроса',
                     hint: 'Использовать варианты ответов респондента из другого вопроса'"></fc-switch>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko template: {
            foreach: templateIf(question.donor.useDonor(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
         } -->
    <!-- ko template: { name: 'question-form-star-variants-donor', } -->
    <!-- /ko -->
    <!-- /ko -->


    <!-- ko template: {
            foreach: templateIf(!question.donor.useDonor(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
         } -->
    <!-- ko template: { name: 'question-form-star-variants-list', } -->
    <!-- /ko -->
    <!-- /ko -->

    <div class="mb-20p">
      <foquz-checkbox params="checked: question.randomOrder, disabled: question.isFullBlocked">
        <span data-bind="text: question.translator.t('Случайный порядок строк')"></span>
        <question-button params="text: question.translator.t('Случайный порядок строк')">
        </question-button>
      </foquz-checkbox>
    </div>



  </div>

  <!-- /ko -->




  <div class="row">

    <div class="col-6">
      <!-- Блок ввода минимального значения -->
      <div class="form-group">
        <label class="form-label" for="min-value"><span data-bind="text: $translator.t('Мин. отметка шкалы')"></span></label>
        <input data-bind="
                textInput: question.minValue,
                disable: question.isFullBlocked,
                css: {
                'is-invalid': controller.formControlErrorStateMatcher(question.minValue),
                'is-valid': controller.formControlSuccessStateMatcher(question.minValue)
                }" type="number" class="form-control" onkeydown="return event.key == 'Backspace' || event.key == 'Delete' || (event.key >= '0' && event.key <= '9')" min="0" oninput="if(this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength); validity.valid||(value='');" step="1" id="min-value" maxLength="7" placeholder="0">
        <validation-feedback params="show: question.formControlErrorStateMatcher(question.minValue), text: question.minValue.error"></validation-feedback>

      </div>
    </div>
    <div class="col-6">
      <!-- Блок ввода максимального значения -->
      <div class="form-group">
        <label class="form-label" for="max-value"><span data-bind="text: $translator.t('Макс. отметка шкалы')"></span></label>
        <input data-bind="
                textInput: question.maxValue,
                disable: question.isFullBlocked,
                css: {
                'is-invalid': controller.formControlErrorStateMatcher(question.maxValue),
                'is-valid': controller.formControlSuccessStateMatcher(question.maxValue)
                }" type="number" onkeydown="return event.key == 'Backspace' || event.key == 'Delete' || (event.key >= '0' && event.key <= '9')" min="1" maxlength="7" oninput="if(this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength); validity.valid||(value='');" step="1" class="form-control" id="max-value" placeholder="100">
        <validation-feedback params="show: question.formControlErrorStateMatcher(question.maxValue), text: question.maxValue.error"></validation-feedback>

      </div>
    </div>
    <div class="col-6">
      <!-- Блок ввода шага ползунка -->
      <div class="form-group">
        <label class="form-label" for="step-value"><span data-bind="text: $translator.t('Шаг ползунка')"></span></label>
        <input data-bind="
             textInput: question.stepValue,
             disable: question.isFullBlocked,
             css: {
               'is-invalid': controller.formControlErrorStateMatcher(question.stepValue),
               'is-valid': controller.formControlSuccessStateMatcher(question.stepValue)
             }" type="number" class="form-control" id="step-value" maxlength="7" onkeydown="return event.key == 'Backspace' || event.key == 'Delete' || (event.key >= '0' && event.key <= '9')" min="1" oninput="if(this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength); validity.valid||(value='');" step="1" placeholder="10">
        <validation-feedback params="show: question.formControlErrorStateMatcher(question.stepValue), text: question.stepValue.error"></validation-feedback>

      </div>
    </div>

  </div>
  <hr class="mx-0">
  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск оценки'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
     foreach: templateIf(question.skip(), $data),
     afterAdd: slideAfterAddFactory(400),
     beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Не готов(а) оценить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>
    <!-- /ko -->
    <!-- ko if: question.scaleType() === 'variants' && question.skip() -->
    <div class="mb-20p">
      <foquz-checkbox
        params="
          checked: question.skipVariant,
          disabled: question.isFullBlocked,
        "
      >
        <span data-bind="text: question.translator.t('Пропуск для каждой оценки')"></span>
        <question-button params="text: question.translator.t('Пропуск для каждой оценки')">
        </question-button>
      </foquz-checkbox>
    </div>
    <!-- /ko -->
  </div>
  <hr class="mx-0">

  <!-- ko template: { name: 'gallery-question-gallery-template' } -->
  <!-- /ko -->


  <hr>
  <!-- Комментарий -->
  <div class="mt-4">
    <switch params="checked: question.commentEnabled, disabled: question.isFullBlocked"><span data-bind="text: $translator.t('Комментарий')"></span></switch>

  </div>

  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200),
      disabled: question.isFullBlocked,

  } -->
  <div>
    <div class="row pb-4">
      <div class="col">
        <div class="f-check">
          <input
            type="checkbox"
            id="comment-required"
            class="f-check-input"
            data-bind="
              checked: question.commentIsRequired,
              disable: question.isFullBlocked,
              event: { change: function() { 
                question.updateCommentRequired() } }
            "
          />
          <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
        </div>
      </div>
    </div>
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: $translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
        } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->


</template>