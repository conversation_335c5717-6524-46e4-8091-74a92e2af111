const { computed } = ko;

export function AnswersLimit({ required, list, maxVal, minVal }) {
    const min = ko.computed(() => {
        if (minVal && minVal()) {
          return minVal();
        }
        return (required && required()) ? 1 : 0;
      });
      const max = ko.computed(() => {
        return Math.min(list().length, (maxVal && maxVal()) ? maxVal() - 1 : list().length);
      });
      const value = ko.observable("");

      min.subscribe((v) => {
        if (value() !== "" && value() < v) {
          value(v);
        }
      });
      max.subscribe((v) => {
        if (value() !== "" && value() > v) {
          value(v);
        }
      });

      return {
        min, max, value
      }
}