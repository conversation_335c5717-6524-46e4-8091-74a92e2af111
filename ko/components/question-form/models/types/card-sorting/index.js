import { VariantsController } from "../../../controllers/variants-controller";
import { Translator } from "@/utils/translate";
import { declOfNum } from "@/utils/string/decl-of-num";
import GalleryQuestion from "../gallery";
import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";
import { CARD_SORTING_QUESTION } from "Data/question-types";
import { VariantsListModel } from "@/components/question-form/models/variants-list";
import { AnswersLimit } from "./answers-limit";
import TextField from "../../text-field";

const QuestionTranslator = Translator("question");
const ValidationTranslator = Translator("validation");

class CardSortingQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };
    super(controller, config);
    this.type = CARD_SORTING_QUESTION;
    
    this.categoryList = VariantsListModel();
    this.cardList = VariantsListModel();

    this.maxCardInCategory = AnswersLimit({
      list: this.cardList.list,
      minVal: ko.observable(1),
    });
    this.minCardInCategory = AnswersLimit({
      list: this.cardList.list,
      minVal: ko.observable(1),
      maxVal: this.maxCardInCategory.value
    });

    this.cardColumnText = ko.observable('Карточки');
    this.categoryColumnText = ko.observable('Категории');

    this.randomCategoryOrder = ko.observable(false);
    this.randomCardOrder = ko.observable(false);

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    this.commentEnabled = ko.observable(false);
    this.commentLabel = ko.observable('');
    this.commentIsRequired = ko.observable(false);
    this.commentField = new TextField();

    this.galleryEnabled = ko.observable(false);

    const loaderConfig = {
      presets: ["videoVar","image"],
      multiple: true,
      
    };
    this.loader = new FoquzMultipleLoader(loaderConfig)

    this.loader.on("select", ({file}) => {
      var fd = new FormData();
      for (let index = 0; index < file.length; index++) {
        const element = file[index];
        if (element.size / 1024 / 1024 < 5) {
          fd.append(`files[${index}]`, element);
        }
      }
      
      var self = this;
  
      $.ajax({
        url: `/foquz/api/questions/upload-detail-files?access-token=${window.ACCESS_TOKEN}`,
        type: "post",
        data: fd,
        contentType: false,
        processData: false,
        success: function (response) {

          response.files.forEach(i => {
            if (i.previewUrl == "/uploads/") {
              i.previewUrl = '/img/audio-file-back.png'
            }
            self.variantsList.addVariant(self.variantsList.list().length)
            const variant = self.variantsList.list()[self.variantsList.list().length - 1]
            variant.file(i)
            variant.value(i.origin_name.replace(/\.[^/.]+$/, ""))
          })
        },
      });
    });

    [
      this.cardColumnText,
      this.categoryColumnText,
      this.randomCategoryOrder,
      this.randomCardOrder,
      this.skip,
      this.skipText,
      this.commentEnabled,
      this.commentIsRequired,
      this.commentLabel,
      this.maxCardInCategory.value,
      this.minCardInCategory.value,
      this.commentField.isChanged,
      this.galleryEnabled,
      this.categoryList.isChanged,
      this.cardList.isChanged,
    ].forEach((f) => f.subscribe((v) => this.onChange()));
  }
  
  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  updateData(data) {
    data.galleryCommentEnabled = data.variantsCommentEnabled;
    data.galleryCommentLengthRange = data.variantsCommentLengthRange;
    data.galleryCommentPlaceholder = data.variantsCommentPlaceholder;
    data.galleryCommentLabel = this.commentLabel();
    data.galleryCommentRequired = data.variantsCommenRequired
    super.updateData(data);

    const {
      galleryEnabled,
      maxCardInCategory,
      minCardInCategory,
      cardColumnText,
      categoryColumnText,
      randomCategoryOrder,
      randomCardOrder,
      skip,
      skipText,
      commentLengthRange,
      commentEnabled,
      commentPlaceholder,
      commentIsRequired,
      commentLabel,
      categoryList,
      variants,
    } = data;
    
    this.galleryEnabled(galleryEnabled);

    this.categoryList.update(categoryList);
    this.cardList.update(variants);

    this.maxCardInCategory.value(maxCardInCategory);
    this.minCardInCategory.value(minCardInCategory);

    this.cardColumnText(cardColumnText);
    this.categoryColumnText(categoryColumnText);

    this.randomCategoryOrder(randomCategoryOrder);
    this.randomCardOrder(randomCardOrder);

    this.skip(skip);
    this.skipText(skipText || '');

    this.commentEnabled(commentEnabled);
    this.commentIsRequired(commentIsRequired);
    this.commentLabel(commentLabel);
    this.commentField.updateData({
      range: commentLengthRange,
      placeholder: commentPlaceholder,
      required: commentIsRequired
    });
  }

  getData() {
    const parentData = super.getData();
    const galleryEnabled = this.galleryEnabled() ? 1 : 0;
    
    let data = {
      ...parentData,
      maxCardInCategory: this.maxCardInCategory.value(),
      minCardInCategory: this.minCardInCategory.value(),
      cardColumnText: this.cardColumnText(),
      categoryColumnText: this.categoryColumnText(),
      randomCategoryOrder: this.randomCategoryOrder(),
      randomCardOrder: this.randomCardOrder(),
      commentEnabled: this.commentEnabled(),
      commentIsRequired: this.commentIsRequired(),
      commentLabel: this.commentLabel(),
      skip: this.skip(),
      skipText: this.skipText(),
      categoryList: this.categoryList.getVariants(),
      variants: this.cardList.getVariants(),
      galleryEnabled,
      gallery: galleryEnabled ? parentData.gallery : [],
    };

    return data;
  }

  isValid() {
    if (this.galleryEnabled()) {
      if (!super.isValid()) return false;
    }
    if (!this.defaultValidation()) return false;
    return this.categoryList.isValid() && this.cardList.isValid();
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}

export default CardSortingQuestion;