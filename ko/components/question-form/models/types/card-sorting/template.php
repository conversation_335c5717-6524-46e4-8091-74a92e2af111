<template id="question-form-template-card-sorting">
  <div>
    <div class="">
      <fc-label
        params="
          text: $translator.t('Наименование колонки с карточками'),
          hint: $translator.t('Из колонки с карточками респондент будет перемещать карточки в соответствующие категории')
        "
      ></fc-label>

      <div class="mb-25p">
        <fc-input
          params="
            value: question.cardColumnText,
            maxlength: 125,
            counter: true,
            disabled: question.isFullBlocked
          "
        ></fc-input>
        <div class="form-label__note mt-5p">
          Если заголовок не нужен, оставьте поле пустым
        </div>
      </div>
      
      <fc-variants-list
        params="
          variantsList: question.cardList,
          isBlocked: question.isBlocked,
          isFullBlocked: question.isFullBlocked,
          withPoints: false,
          canRemoveSingleVariant: false,
          formControlErrorStateMatcher: question.formControlErrorStateMatcher,
          formControlSuccessStateMatcher: question.formControlSuccessStateMatcher
        "
      ></fc-variants-list>

      <!-- ko ifnot: question.isFullBlocked -->
        <div class="row">
          <div class="col-12">
            <button
              class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
              data-bind="
                click: function() {
                  question.cardList.addVariant();
                },
                attr: {
                  disabled: !question.cardList.isVariantsValid(),
                },
              "
            >
              <span class="survey-question__variants-control-add-button-icon"></span>
              <span data-bind="text: question.translator.t('Добавить карточку')"></span>
            </button>
          </div>
        </div>
      <!-- /ko -->

      <!-- ko if: question.formControlErrorStateMatcher(question.cardList.count)() -->
      <validation-feedback
          params="
            show: question.formControlErrorStateMatcher(question.cardList.count),
            text: question.cardList.count.error()
          "
        ></validation-feedback>
      <!-- /ko -->

      <div class="mb-20p">
        <foquz-checkbox params="checked: question.randomCardOrder, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Случайный порядок карточек')"></span>
            <question-button params="text: question.translator.t('Варианты карточек для каждого респондента при прохождении будут предложены в случайном порядке')">
            </question-button>
        </foquz-checkbox>
      </div>
    </div>

    <hr class="mx-0 my-4">

    <div class="">
      <fc-label
        params="
          text: $translator.t('Наименование колонки с категориями'),
          hint: $translator.t('В колонку с категориями респондент будет перемещать карточки в соответствующую категорию')
        "
      ></fc-label>

      <div class="mb-25p">
        <fc-input
          params="
            value: question.categoryColumnText,
            maxlength: 125,
            counter: true,
            disabled: question.isFullBlocked
          "
        ></fc-input>
        <div class="form-label__note mt-5p">
          Если заголовок не нужен, оставьте поле пустым
        </div>
      </div>

      <fc-variants-list
        params="
          variantsList: question.categoryList,
          isBlocked: question.isBlocked,
          isFullBlocked: question.isFullBlocked,
          withPoints: false,
          canRemoveSingleVariant: false,
          formControlErrorStateMatcher: question.formControlErrorStateMatcher,
          formControlSuccessStateMatcher: question.formControlSuccessStateMatcher
        "
      ></fc-variants-list>

      <!-- ko ifnot: question.isFullBlocked -->
        <div class="row">
          <div class="col-12">
            <button
              class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
              data-bind="
                click: function() {
                  question.categoryList.addVariant();
                },
                attr: {
                  disabled: !question.categoryList.isVariantsValid(),
                },
              "
            >
              <span class="survey-question__variants-control-add-button-icon"></span>
              <span data-bind="text: question.translator.t('Добавить категорию')"></span>
            </button>
          </div>
        </div>
      <!-- /ko -->

      <!-- ko if: question.formControlErrorStateMatcher(question.categoryList.count)() -->
        <validation-feedback
          params="
            show: question.formControlErrorStateMatcher(question.categoryList.count),
            text: question.categoryList.count.error()
          "
        ></validation-feedback>
      <!-- /ko -->

      <div class="d-flex mt-4">
        <fc-variants-min-count
          class="w-100 mr-15p"
          params="
            label: 'Min кол-во карточек в категории',
            min: question.minCardInCategory.min,
            max: question.minCardInCategory.max,
            value: question.minCardInCategory.value,
            disabled: question.isFullBlocked
          "
        ></fc-variants-min-count>
        <fc-variants-max-count
          class="w-100 ml-15p"
          params="
            label: 'Max кол-во карточек в категории',
            min: question.maxCardInCategory.min,
            max: question.maxCardInCategory.max,
            value: question.maxCardInCategory.value,
            disabled: question.isFullBlocked
          "
        ></fc-variants-max-count>
      </div>

      <div class="mb-20p">
        <foquz-checkbox params="checked: question.randomCategoryOrder, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Случайный порядок категорий')"></span>
            <question-button params="text: question.translator.t('Варианты категорий для каждого респондента при прохождении будут предложены в случайном порядке')">
            </question-button>
        </foquz-checkbox>
      </div>

    </div>

    <hr class="mx-0 my-4">

    <div>
      <div class="form-group">
          <fc-switch
            params="
              checked: question.skip,
              label: $translator.t('Пропуск ответа'),
              disabled: question.isFullBlocked
            "
          ></fc-switch>
      </div>

      <!-- ko template: {
          foreach: templateIf(question.skip(), $data),
          afterAdd: slideAfterAddFactory(400),
          beforeRemove: slideBeforeRemoveFactory(400)
      } -->
        <div class="form-group">
            <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
            <fc-input
                params="
                  value: question.skipText,
                  counter: true,
                  maxlength: 125,
                  placeholder: $translator.t('Затрудняюсь ответить'),
                  disabled: question.isFullBlocked,
                "
            ></fc-input>
        </div>
      <!-- /ko -->
    </div>

    <hr class="mx-0">

    <!-- ko template: { name: 'gallery-question-gallery-template' } -->
    <!-- /ko -->

    <hr class="mx-0">

    <div class="">
      <div class="mt-4">
        <switch
          params="
            checked: question.commentEnabled,
            disabled: question.isFullBlocked
          "
        >
          <span data-bind="text: $translator.t('Комментарий')"></span>
        </switch>
      </div>

      <!-- ko template: {
          foreach: templateIf(question.commentEnabled(), $data),
          afterAdd: slideAfterAddFactory(200),
          beforeRemove: slideBeforeRemoveFactory(200)
      } -->
        <div>
          <div class="row pb-4">
              <div class="col">
                <div class="f-check">
                    <input
                      type="checkbox"
                      id="comment-required"
                      class="f-check-input"
                      data-bind="
                          checked: question.commentIsRequired,
                          disable: question.isFullBlocked,
                          event: { change: function() { 
                          question.updateCommentRequired() } }
                      "
                    />
                    <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
                </div>
              </div>
          </div>
          <div class="form-group">
            <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
            <fc-input params="value: question.commentLabel, maxlength: 125, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
          </div>

          <!-- ko component: {
            name: 'text-field',
            params: {
                controller: question.commentField,
                intervalText: $translator.t('Кол-во символов в комментарии'),
                disabled: question.isFullBlocked
            }
          } -->
          <!-- /ko -->
        </div>
      <!-- /ko -->
    </div>
  </div>
</template>