<template id="question-form-nps-variants-list">
  <fc-label params="text: 'Варианты для оценки'"></fc-label>
  <fc-variants-list params="
            variantsList: question.variantsList,
            isBlocked: question.isBlocked,
            isFullBlocked: question.isFullBlocked,
            withPoints: false,
            canRemoveSingleVariant: false,
            formControlErrorStateMatcher: question.formControlErrorStateMatcher,
            formControlSuccessStateMatcher: question.formControlSuccessStateMatcher
        ">

  </fc-variants-list>

  <!-- ko ifnot: question.isFullBlocked -->
  <button class="mt-15p survey-question__variants-control-add-button variants-controller__add-button" data-bind="
        click: function() { question.variantsList.addVariant(); },
        attr: { disabled: !question.isVariantsValid() }
        ">
    <span class="survey-question__variants-control-add-button-icon"></span>
    <span data-bind="text: question.translator.t('Добавить вариант')"></span>
  </button>
  <!-- /ko -->
</template>

<template id="question-form-nps-variants-donor" >
  <div>
    <div class="form-group">
      <fc-label params="text: 'Вопрос-донор', hint: 'Вопрос-донор'"></fc-label>
      <fc-select params="
                options: question.donor.donorsList,
                value: question.donor.donorId
                "></fc-select>
    </div>

    <div class="form-group">
      <fc-label params="text: 'Варианты ответов'"></fc-label>
      <fc-donor-variants-type params="
            value: question.donor.donorVariantsType, 
            isFullBlocked: question.isFullBlocked,
            disabled: question.isBlocked() || question.isFullBlocked" data-bind="click: function() {
                if (question.isBlocked() && !question.isFullBlocked) 
                    question.tryChangeBlockedParam();
                else return true;
            }" class="mb-4"></fc-donor-variants-type>
    </div>

    <div class="form-group">
      <fc-label params="text: 'Варианты для оценки'"></fc-label>
      <fc-donor-variants-list params="variants: question.donorVariants, withPoints: false">
      </fc-donor-variants-list>
    </div>
  </div>
</template>

<template id="question-form-template-nps">
  <!-- ko let: { $translator: question.translator } -->

  <!-- ko template: {
     foreach: templateIf(question.ratingType() === 'variants', $data),
     afterAdd: slideAfterAddFactory(400),
     beforeRemove: slideBeforeRemoveFactory(400)
  } -->
  <div data-bind="childrenComplete: () => question.onChildrenComplete($root.isSubmitted)">
    <!-- ko ifnot: question.isAuto || question.isSystem || question.mode === 'cpoint' -->
    <div class="form-group">
      <div data-bind="
          click: function() {
            return question.onDonorsTogglerClick();
          }">
        <fc-switch params="checked: question.donor.useDonor, 
                  disabled: question.disableDonors || question.isFullBlocked,
                  label: 'Использовать варианты ответов респондента из другого вопроса',
                  hint: 'Использовать варианты ответов респондента из другого вопроса'"></fc-switch>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko template: {
         foreach: templateIf(question.donor.useDonor(), $data),
         afterAdd: slideAfterAddFactory(400),
         beforeRemove: slideBeforeRemoveFactory(400)
      } -->
    <!-- ko template: { name: 'question-form-star-variants-donor', } -->
    <!-- /ko -->
    <!-- /ko -->


    <!-- ko template: {
         foreach: templateIf(!question.donor.useDonor(), $data),
         afterAdd: slideAfterAddFactory(400),
         beforeRemove: slideBeforeRemoveFactory(400)
      } -->
    <!-- ko template: { name: 'question-form-star-variants-list', } -->
    <!-- /ko -->
    <!-- /ko -->





    <div class="mb-20p">
      <foquz-checkbox params="checked: question.randomOrder, disabled: question.isFullBlocked">
        <span data-bind="text: question.translator.t('Случайный порядок строк')"></span>
        <question-button params="text: question.translator.t('Случайный порядок строк')">
        </question-button>
      </foquz-checkbox>
    </div>



  </div>

  <!-- /ko -->


  <div class="">
    <label class="form-label"><span data-bind="text: $translator.t('Дизайн')"></span>

      <button class="btn-question" tabindex="10" data-bind="tooltip, tootltipText: $translator.t('Дизайн')" title=""></button>
    </label>

    <div class="hat-radio-group survey-question__variants-type-selector">
      <!-- ko foreach: question.npsTypes -->
      <div class="hat-radio-group__radio" data-bind="let: { inputId: 'survey-question-nps-type-selector-' + id }">
        <input class="hat-radio-group__radio-input" type="radio" name="nps-type" data-bind="checked: question.npsType,
        value: id,
        attr: { id: inputId, },
        disable: question.isFullBlocked" />
        <label class="hat-radio-group__radio-label" data-bind="attr: { for: inputId },
        click: function() {
          if (question.isFullBlocked) return false;
          else return true;
        }">
          <span class="hat-radio-group__radio-indicator"></span>
          <!-- ko text: name -->
          <!-- /ko -->
        </label>
      </div>
      <!-- /ko -->
    </div>
  </div>

  <!-- ko if: question.npsType() == 3 -->
  <div class="row">
    <div class="col-12 col-md-6">
      <div class="form-group">
        <label class="form-label"><span data-bind="text: $translator.t('Начальный цвет')"></span></label>
        <div class="input-group" data-bind="colorPicker">
          <div class="input-group-prepend poll-design__color-preview js-color-preview"></div>
          <input required="" class="form-control colorpicker-anchor" data-bind="textInput: question.startColor, disable: question.isFullBlocked" data-is-hex="true" maxlength="7" style="opacity: 0; position: absolute; right: 0px; width: 50%; z-index: 3;">
          <input class="form-control hidden-input">
        </div>
      </div>
    </div>


    <div class="col-12 col-md-6">
      <div class="form-group">
        <label class="form-label"><span data-bind="text: $translator.t('Конечный цвет')"></span></label>
        <div class="input-group" data-bind="colorPicker">

          <div class="input-group-prepend poll-design__color-preview js-color-preview"></div>
          <input required="" class="form-control colorpicker-anchor" data-bind="textInput: question.endColor, disable: question.isFullBlocked" data-is-hex="true" maxlength="7" style="opacity: 0; position: absolute; right: 0px; width: 50%; z-index: 3;">
          <input class="form-control hidden-input">
        </div>
      </div>
    </div>

  </div>
  <!-- /ko -->

  <div class="form-group">
    <fc-check params="checked: question.fromOne, label: 'Начинать шкалу с единицы', hint: 'Начинать шкалу с единицы', disabled: question.isFullBlocked"></fc-check>
  </div>

  <div class="nps-scale mb-4" data-bind="css: {
    'nps-scale--colored': question.npsType() != 2,
    'nps-scale--from-one': question.fromOne,
  }">

    <div class="nps-scale__list">
      <!-- ko foreach: question.scale -->
      <div class="nps-scale__item" data-bind="style: {
            'backgroundColor': $data
          }">
        <span data-bind="text: $index()"></span>
      </div>
      <!-- /ko -->
    </div>

  </div>

  <div class="row">

    <div class="col-12 col-md-6">
      <div class="form-group">
        <label class="form-label"><span data-bind="text: $translator.t('Метка начальной точки')"></span>

          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: $translator.t('Метка начальной точки')"></button>
        </label>


        <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: question.startLabel().length">
          <input type="text" class="form-control" placeholder="" data-bind="textInput: question.startLabel, disable: question.isFullBlocked,
            attr: {
              placeholder: $translator.t('Маловероятно')
            }" maxlength="150">
          <div class="chars-counter__value"></div>
        </div>

        <div class="form-hint"><span data-bind="text: $translator.t('Если метки не нужны, оставьте поля пустыми')"></span></div>
      </div>
    </div>

    <div class="col-12 col-md-6">
      <div class="form-group">
        <label class="form-label"><span data-bind="text: $translator.t('Метка конечной точки')"></span>

          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: $translator.t('Метка конечной точки')"></button>
        </label>

        <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: question.endLabel().length">
          <input type="text" class="form-control" placeholder="" data-bind="textInput: question.endLabel, disable: question.isFullBlocked, attr: {
            placeholder: $translator.t('С большой вероятностью')
          }" maxlength="150">
          <div class="chars-counter__value"></div>
        </div>
      </div>
    </div>
  </div>

  <hr class="mx-0">

  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск оценки'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
     foreach: templateIf(question.skip(), $data),
     afterAdd: slideAfterAddFactory(400),
     beforeRemove: slideBeforeRemoveFactory(400)
  } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Не готов(а) оценить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>

    <!-- /ko -->
  </div>


  <hr class="mx-0">

  <!-- ko template: { name: 'gallery-question-gallery-template' } -->
  <!-- /ko -->
  
  <hr class="mx-0">
  
  <div class="row clarifying-answer-settings__switchers">
    <!-- ko if: question.ratingType() == 'variants' -->
      <div class="col">
        <div class="pt-1">
          <switch class="mb-0" params="
              checked: question.clarifyingQuestionEnabled,
              disabled: question.isBlocked() || question.isFullBlocked
            " data-bind="
              click: function() {
                if (question.isFullBlocked) return false;
                if (question.isBlocked()) question.tryChangeBlockedParam();
                else return true;
              }
            ">
            <span data-bind="text: $translator.t('Уточняющий вопрос')"></span>
          </switch>
        </div>
      </div>
    <!-- /ko -->
    <div class="col">
      <switch
        class="mb-0"
        params="
          checked: question.commentEnabled,
          disabled: question.isBlocked() || question.isFullBlocked"
          data-bind="
            click: () => {
              if (question.isFullBlocked) return false;
              if (question.isBlocked()) question.tryChangeBlockedParam();
              else return true;
            }
          "
      >
        <!-- ko text: $translator.t('Комментарий') -->
      </switch>
    </div>
  </div>
  <!-- ko if: question.ratingType() == 'variants' -->
    <!-- ko template: {
      foreach: question.clarifyingQuestionEnabled,
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
      <div class="row mb-25p">
        <div class="col">
          <fc-select
            params="
              options: question.clarifyingQuestionOptions,
              value: question.clarifyingQuestionType,
              disabled: question.isBlocked() || question.isFullBlocked
            "
            data-bind="
            click: () => {
              if (question.isFullBlocked) return false;
              if (question.isBlocked()) question.tryChangeBlockedParam();
              else return true;
            }
          "
          ></fc-select>
        </div>
      </div>

      <!-- ko ifnot: question.clarifyingToEach() -->
        <div class="row mb-4">
          <div class="col">
            <div class="d-flex flex-wrap">
              <fc-check
                class="mr-15p"
                params="
                  checked: question.clarifyingQuestionForAllVariants,
                  label: $translator.t('Всех вариантов'),
                  disabled: question.isFullBlocked,
                  onChange: (flag) => {
                    question.clarifyingQuestionForAllVariants(flag);
                    question.onChangeClarifyingQuestionForAllVariants(flag);
                  },
                "
              ></fc-check>
              <!-- ko if: !question.donor.useDonor() -->
              <!-- ko foreach: question.variantsList.list -->
                <fc-check
                  class="mr-15p"
                  params="
                    checked: $data.needExtra,
                    label: $index() + 1,
                    disabled: question.isFullBlocked,
                    onChange: (flag) => {
                      $data.needExtra(flag);
                      question.onChangeNeedExtra(flag); 
                    },
                  "
                ></fc-check>
              <!-- /ko -->
              <!-- /ko -->
              <!-- ko if: question.donor.useDonor() -->
              <!-- ko foreach: question.donorVariants() -->
              <fc-check
                  class="mr-15p"
                  params="
                    checked: $data.needExtra,
                    label: $index() + 1,
                    disabled: question.isFullBlocked,
                    onChange: (flag) => {
                      $data.needExtra(flag);
                      question.onChangeNeedExtra(flag); 
                    },
                  "
                ></fc-check>
              <!-- /ko -->
              <!-- /ko -->
            </div>
          </div>
        </div>
        <clarifying-question
          params="
            clarifyingQuestionVariant: question,
            question: question,
            formControlErrorStateMatcher: question.formControlErrorStateMatcher,
            formControlSuccessStateMatcher: question.formControlSuccessStateMatcher,
          "
        ></clarifying-question>
      <!-- /ko -->

      <!-- ko if: question.clarifyingToEach() -->
        <fc-label params="text: 'Настройки вопроса для варианта'"></fc-label>
        <native-scrollbar
          params="
            items: question.variantsList.list,
            getText: (item, index) => index + 1,
            getHint: (item, index) => question.getHint(item, index),
            currentIndex: question.currentClarifyingIndex,
          "
        ></native-scrollbar>

        <!-- ko foreach: {
          data: question.variantsList.list,
          as: 'clarifyingQuestionVariant', noChildContext: true
        } -->
          <!-- ko if: $index() === question.currentClarifyingIndex() -->
            <div
              class="f-fs-1 f-color-service mb-25p"
              data-bind="text: clarifyingQuestionVariant.value"
            ></div>
            <switch class="mb-25p"
              params="
                checked: clarifyingQuestionVariant.needExtra,
                disabled: question.isBlocked() || question.isFullBlocked
              "
              data-bind="click: function() {
                if (question.isFullBlocked) return false;
                if (question.isBlocked()) question.tryChangeBlockedParam();
                else return true;
              }"
            ><span data-bind="text: $translator.t('Уточняющий вопрос для этого варианта')"></span></switch>  
            <!-- ko template: {
              foreach: templateIf(clarifyingQuestionVariant.needExtra(), $data),
              afterAdd: slideAfterAddFactory(200),
              beforeRemove: slideBeforeRemoveFactory(200),
              disabled: question.isFullBlocked,
            } -->
              <clarifying-question
                params="
                  clarifyingQuestionVariant: clarifyingQuestionVariant,
                  question: question,
                  formControlErrorStateMatcher: question.formControlErrorStateMatcher,
                  formControlSuccessStateMatcher: question.formControlSuccessStateMatcher,
                "
              ></clarifying-question>
            <!-- /ko -->
          <!-- /ko -->
        <!-- /ko -->
      <!-- /ko -->
    <!-- /ko -->

  <!-- /ko -->

  <!-- Комментарий -->
  
  <!-- ko template: {
    foreach: templateIf(question.commentEnabled(), $data),
    afterAdd: slideAfterAddFactory(200),
    beforeRemove: slideBeforeRemoveFactory(200),
    disabled: question.isFullBlocked,
  } -->
  <div>
    <div class="row pb-4">
      <div class="col">
      <div class="f-check">
          <input
          type="checkbox"
          id="comment-required"
          class="f-check-input"
          data-bind="
              checked: question.commentIsRequired,
              disable: question.isFullBlocked,
              event: { change: function() { 
              question.updateCommentRequired() } }
          "
          />
          <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
      </div>
      </div>
    </div>
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
      name: 'text-field',
      params: {
        controller: question.commentField,
        intervalText: $translator.t('Кол-во символов в комментарии'),
        disabled: question.isFullBlocked
      }
    } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->

  <!-- /ko -->
</template>