<div class="row">
  <div class="col">
    <foquz-checkbox
      class="mr-15p"
      params="
        checked: question.clarifyingQuestionForAllRates,
        disabled: isFullBlocked,
      "
    >
      <!-- ko text: $translator.t('Для всех оценок') -->
    </foquz-checkbox>
  </div>
  <div class="col">
    <foquz-checkbox
      class="mr-15p"
      params="
        checked: question.clarifyingQuestionIsRequired,
        disabled: isFullBlocked,
      "
    >
      <!-- ko text: $translator.t('Обязательный') -->
    </foquz-checkbox>
  </div>
</div>
<!-- ko template: {
  foreach: templateIf(!question.clarifyingQuestionForAllRates(), $data),
  afterAdd: fadeAfterAddFactory(200),
  beforeRemove: fadeBeforeRemoveFactory(200),
} -->
  <div class="row pt-4">
    <div class="col mb-4">
      <fc-label params="text: 'Для оценок'"></fc-label>
      <fc-range class="clarifying-answer-settings__rates-range" params="
        value: question.clarifyingQuestionForRates,
        min: startRate,
        max: 10,
        tooltips: true,
        disabled: isFullBlocked,
      "></fc-range>
    </div>
  </div>
<!-- /ko -->

<div class="form-group mt-3">
  <fc-label
    params="
      text: $translator.t('Уточняющий вопрос'),
      hint: $translator.t('Можно добавить вопрос, который дополнительно будет задан респонденту для уточнения его ответа. Уточняющий вопрос должен быть всегда с вариантами ответов')
    "
  ></fc-label>
  <div
    class="chars-counter chars-counter--type_textarea"
    data-bind="
      charsCounter,
      charsCounterCount: question.clarifyingQuestionText().length
    "
  >
    <textarea
      class="form-control"
      data-bind="
        textInput: question.clarifyingQuestionText,
        css: {
          'is-invalid': formControlErrorStateMatcher(question.clarifyingQuestionText),
          'is-valid': formControlSuccessStateMatcher(question.clarifyingQuestionText)
        },
        autosizeTextarea,
        disable: isFullBlocked
      "
      maxlength="500"
    ></textarea>
    <div class="chars-counter__value"></div>
  </div>
  <!-- ko if: formControlErrorStateMatcher(question.clarifyingQuestionText) -->
    <div class="form-error" data-bind="text: question.clarifyingQuestionText.error()"></div>
  <!-- /ko -->
</div>
<div class="nps-clarifying-question">
  <div data-bind="component: {
    name: 'question-form-variants-list',
    params: questionFormVariantsListParams
  }"></div>
</div>

<!-- ko template: {
  foreach: templateIf(question.variantsType() == 1, $data),
  afterAdd: fadeAfterAddFactory(400),
  beforeRemove: fadeBeforeRemoveFactory(400)
} -->
<div class="d-flex mt-4">
  <fc-variants-min-count
    class="w-100 mr-15p"
    params="
      min: question.answersCountLimitMin.min,
      max: question.answersCountLimitMin.max,
      value: question.answersCountLimitMin.value,
      disabled: isFullBlocked
    "
  ></fc-variants-min-count>
  <fc-variants-max-count
    class="w-100 ml-15p"
    params="
      min: question.answersCountLimit.min,
      max: question.answersCountLimit.max,
      value: question.answersCountLimit.value,
      disabled: isFullBlocked
    "
  ></fc-variants-max-count>
</div>
<!-- /ko -->
