import html from './template.html';

class ViewModel {
  constructor({ clarifyingQuestionVariant: question, question: allQuestion, formControlErrorStateMatcher, formControlSuccessStateMatcher }) {
    console.log('questiongfhcfghc', question)
    
    this.isFullBlocked = allQuestion.isFullBlocked;
    this.startRate = allQuestion.startRate;
    this.clarifyingToEach = allQuestion.clarifyingToEach;
    this.formControlErrorStateMatcher = formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher = formControlSuccessStateMatcher;

    this.question = question;
    this.allQuestion = allQuestion;
    this.controller = null;


    if (this.clarifyingToEach()) {
      this.questionFormVariantsListParams = {
        question: allQuestion,
        enableFile: question.enableFile,
        canUseFile: question.variantsType !== 2,
        withTextAnswer: true,
        textAnswerField: question.textAnswerField,
        withVariantsTypeSelect: true,
        variantsType: question.variantsType,
        removeVariant: question.removeVariant,
        isVariantsValid: question.isVariantsValid,
        addVariant: question.addVariant,
        withAddVariantButton: true,
        variantsCount: question.variantsCount,
        withCustomAnswer: true,
        customAnswerEnabled: question.customAnswerEnabled,
        customAnswerField: question.customAnswerField,
        variants: question.variants,
      }
    } else {
      this.questionFormVariantsListParams = {
        controller: allQuestion.clarifyingQuestionController,
        question: allQuestion,
      }
    }
  }
}

ko.components.register('clarifying-question', {
  viewModel: ViewModel,
  template: html
});
