import "Components/input/color-picker";
import "./components/clarifying-question"

import { get as _get, uniq as _uniq } from 'lodash';

import GalleryQuestion from "../gallery";
import { NPS_QUESTION } from "Data/question-types";
import { NPSGradient } from "Legacy/utils/nps";
import { AnswersLimit } from "../variants/answers-limit";

import { Translator } from "@/utils/translate";
import { Donor } from "../../donor";
import "@/components/native-scrollbar";
import { VariantsListModel } from "Components/question-form/models/variants-list";
import { VariantsController } from "../../../controllers/variants-controller";

import "./style.less";

const QuestionTranslator = Translator("question");
const ValidationTranslator = Translator("validation");

const defaultColors = ["#F96261", "#00C968"];

const types = [
  {
    id: 1,
    name: QuestionTranslator.t("Цветной"),
  },
  {
    id: 2,
    name: QuestionTranslator.t("Черно-белый"),
  },
  {
    id: 3,
    name: QuestionTranslator.t("Свой градиент"),
  },
];

class NPSQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };

    super(controller, config);

    this.type = NPS_QUESTION;

    this.galleryEnabled = ko.observable(false);

    this.ratingType = ko.observable("standart");

    this.npsType = ko.observable(1);
    this.npsTypes = types;

    this.startColor = ko.observable(defaultColors[0]);
    this.endColor = ko.observable(defaultColors[1]);

    this.startLabel = ko.observable("");
    this.endLabel = ko.observable("");

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    this.fromOne = ko.observable(false);

    this.scale = ko.pureComputed(() => {
      if (this.npsType() == 2) {
        return [...Array(11)].map((i) => "");
      }

      return NPSGradient(this.startColor(), this.endColor());
    });

    this.clarifyingQuestionEnabled = ko.observable(false);
    this.clarifyingQuestionType = ko.observable(1);

    this.currentClarifyingIndex = ko.observable(0);
    this.clarifyingToEach = ko.computed(() => this.clarifyingQuestionType() === 3);


    this.variantsList = VariantsListModel({
      clarifyingToEach: this.clarifyingToEach,
      clarifyingQuestionEnabled: this.clarifyingQuestionEnabled,
      fromOne: this.fromOne,
    });

    // donor
    this.donor = Donor(controller.donors);
    this.blockRecipients = controller.blockRecipients;
    this.disableDonors = ko.computed(() => {
      if (this.isFullBlocked) return true;
      if (ko.toJS(this.isBlocked)) return true;
      if (this.blockRecipients) return true;
      if (!this.donor.donorsList().length) return true;
      return false;
    });
    this.donorVariants = ko.observableArray([]);

    this.donor.useDonor.subscribe((v) => {
      if (v) this.variantsList.update([]);
    });
    this.donor.donorId.subscribe((v) => {
      const donorVariants = this.donor.getDonorVariants(v);
      this.donorVariants(donorVariants);
    });

    this.enableFile = ko.observable(false);

    this.clarifyingQuestionForRates = ko.observable([0, 10]);
    this.clarifyingQuestionForAllVariants = ko.observable(true);
    this.clarifyingQuestionIsRequired = ko.observable(true)

    this.clarifyingToEach.subscribe((flag) => {
      this.clarifyingQuestionForAllVariants(!flag);
      this.onChangeClarifyingQuestionForAllVariants()
    })

    this.clarifyingQuestionController = new VariantsController(
      {
        withCustomAnswer: true,
        withVariantsTypeSelect: true,
        withAddVariantButton: true,
        addFirstVariant: true,
        variantsType: "single",
        withTextAnswer: true,
        canUseFile: true,
        fileEnabled: this.enableFile,
        isClarifyingQuestion: true
      },
      this
    );
    this.variantsType = this.clarifyingQuestionController.variantsType;

    this.changing = ko.observable(false);

    this.startRate = ko.observable(0);

    this.clarifyingQuestionText = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => this.clarifyingQuestionEnabled() && !this.clarifyingToEach(),
      },
    });

    this.clarifyingQuestionForAllRates = ko.observable(true);
    this.clarifyingQuestionOptions = [
      {id: 1, text: 'Общий для всех вариантов'},
      {id: 2, text:'Одинаковый вопрос для каждого варианта'},
      {id: 3, text:'Для каждого варианта свой вопрос'}
    ]


    this.commentIsRequired = ko.observable(null);
    

    // options
    this.randomOrder = ko.observable(false);

    // limit
    this.answersCountLimit = AnswersLimit({
      required: this.required,
      list: this.clarifyingQuestionController.variants,
    });

    this.answersCountLimitMin = AnswersLimit({
      required: this.required,
      list: this.clarifyingQuestionController.variants,
      maxVal: this.answersCountLimit.value
    });

    this.usesDictionaryElements = ko.computed(() => {
      if (this.donor.useDonor()) {
        return !!this.donorVariants().find(el => el.dictionaryElementId);
      }
      return !!this.variantsList.list().find(el => el.dictionaryElementId);
    }, this);

    this.dictionaryBadgeTooltipText = ko.computed(() => {
      if (!controller.question()) {
        return '';
      }
      return `
        ${controller.question().dictionaryElementId() ? `Связка вопроса с элементом ${controller.dictionary().name} справочника ${controller.dictionary().name}` : ''}
        ${controller.question().usesDictionaryElements() ? `${controller.question().dictionaryElementId() ? '.' : ''} В вопросе есть варианты, связанные с элементами справочника ${controller.dictionary().name}` : ''}
      `;
    }, this);

    [
      this.galleryEnabled,
      this.npsType,
      this.startColor,
      this.endColor,
      this.startLabel,
      this.endLabel,
      this.skip,
      this.skipText,
      this.fromOne,
      this.ratingType,
      this.randomOrder,
      this.donorVariants,
      this.commentIsRequired,
      this.enableFile,
      this.clarifyingQuestionIsRequired,
      this.clarifyingQuestionText,
      this.clarifyingQuestionType,
      this.clarifyingQuestionController.variants,
      this.answersCountLimitMin?.value,
      this.answersCountLimit?.value,
    ]
    .forEach((f) => f && f.subscribe((v) => this.onChange()));

    this.subscriptions.push(
      this.variantsList.isChanged.subscribe((v) => this.onChange(true)),
      this.donor.isChanged.subscribe((v) => this.onChange(true)),
      this.npsType.subscribe((v) => {
        if (v == 1) {
          this.startColor(defaultColors[0])
          this.endColor(defaultColors[1])
        }
      })
    );

    this.fromOne.subscribe((v) => {
      this.changing(true)
      this.startRate(v ? 1 : 0);
      let [start, end] = this.clarifyingQuestionForRates()
      if (start == 0 || start == 1) {
        start = v ? 1 : 0
        this.clarifyingQuestionForRates([start, end])
      }
      this.changing(false)
    })

    this.commentEnabled.subscribe((v) => {
      if (v) {
        this.clarifyingQuestionEnabled(false)
      }
    })
    this.clarifyingQuestionEnabled.subscribe((v) => {
      if (v) {
        this.commentEnabled(false)
      }
    })
    this.clarifyingQuestionController.isChanged.subscribe((v) => this.onChange())
  }

  onChildrenComplete(isSubmited) {
    if (!!this.isSubmited || !ko.isObservable(isSubmited)) return;
    this.isSubmited = isSubmited;
    this.isSubmited.subscribe((isSubmited) => {
      if (isSubmited && !this.variantsList.isValid() && this.clarifyingToEach()) {
        const index = this.variantsList.getInvalidIndex(this.variantsList.list());
        this.currentClarifyingIndex(index);
      }
    })
  }

  getHint(item, index) {
    let hintClass = '';
    if (item.needExtra()) {
      if (item.isVariantsValid()) {
        hintClass = 'nps-variants-hint_need-extra'
      } else {
        hintClass = 'nps-variants-hint_error'
      }
    }
    return `<span class="${hintClass}"></span>`;
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  getLogicVariants(ids) {
    return this.variantsList
      .list()
      .filter((variant) => {
        return ids.find((id) => variant.id == id);
      })
      .map((variant) => variant.value);
  }

  onDonorsTogglerClick() {
    if (this.isFullBlocked) return false;
    if (ko.toJS(this.isBlocked)) {
      this.tryChangeBlockedParam();
    } else if (this.blockRecipients) {
      this.tryChangeBlockedParam();
    } else if (!this.donor.donorsList().length) {
      this.noDonorsInfo();
    } else return true;
  }

  updateData(data) {
    const {
      variants,
      randomOrder,

      donorId,
      donorVariantsType,
    } = data;

    data.gallery = data.npsGallery;
    data.galleryCommentEnabled = data.npsCommentEnabled;
    data.galleryCommentLengthRange = data.npsCommentLengthRange;
    data.galleryCommentPlaceholder = data.npsCommentPlaceholder;
    data.galleryCommentLabel = data.npsCommentLabel;
    data.galleryCommentRequired = data.npsCommentRequired;

    super.updateData(data);

    this.donor.update({ donorId, donorVariantsType });

    this.galleryEnabled(data.npsGalleryEnabled);

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.npsCommentRequired)
    }

    if (data.extraQuestionType) {
      let clarifyingQuestion = data.clarifyingQuestion
      this.clarifyingQuestionEnabled(true)
      this.clarifyingQuestionType(data.extraQuestionType)
      this.clarifyingQuestionText(data.clarifyingQuestion.text)
      this.clarifyingQuestionForAllRates(data.clarifyingQuestion.forAllRates)
      this.enableFile(!!data.clarifyingQuestion.enableFile)
      this.clarifyingQuestionIsRequired(data.clarifyingQuestion.clarifyingQuestionIsRequired)
      this.answersCountLimitMin.value(data.clarifyingQuestion.minСhooseVariants)
      this.answersCountLimit.value(data.clarifyingQuestion.maxСhooseVariants)
      if (!data.clarifyingQuestion.forAllRates) {
        this.clarifyingQuestionForRates(data.clarifyingQuestion.forRates)
      }

      this.clarifyingQuestionController.updateData({
        type: clarifyingQuestion.variantsType,
        variants: clarifyingQuestion.variants,
        customAnswerEnabled: clarifyingQuestion.customAnswerEnabled,
        customAnswerRange: clarifyingQuestion.customAnswerLengthRange,
        customAnswerPlaceholder: clarifyingQuestion.customAnswerPlaceholder,
        customAnswerLabel: clarifyingQuestion.customAnswerLabel,
        selfVariantFile: data.selfVariantFile
      });
    }

    this.ratingType(data.ratingType);
    this.npsType(data.npsType);
    this.startColor(data.npsStartColor || defaultColors[0]);
    this.endColor(data.npsEndColor || defaultColors[1]);
    this.startLabel(data.npsStartLabel);
    this.endLabel(data.npsEndLabel);
    this.fromOne(!!data.fromOne);

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");

    if (donorId) {
      const donorType = window.QUESTIONS.find(i => i.id == donorId).main_question_type
      const donorVariants = this.donor.getDonorVariants(donorId);
      let currentOrder
      if (donorType !== 19) {

        currentOrder = variants.map((v) => v.donorId || "custom");
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(a.id);
          const bIndex = currentOrder.indexOf(b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      } else {
        const donorIds = donorVariants.map(i => +i.id)
        const temp_variants = variants.filter(i => donorIds.includes(i.dictionary_element_id)).sort((a, b) => a.position - b.position)
        currentOrder = temp_variants.map((v) => v.dictionary_element_id);
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(+a.id);
          const bIndex = currentOrder.indexOf(+b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      }

        
        donorVariants.forEach((variant) => {
          const data = variants.find((v) => {
            if (variant.id === "custom") return !v.donorId;
            return v.donorId === variant.id || v.dictionary_element_id == variant.id;
          });

          if (data) {
            variant.points(data.points);
            variant.needExtra(data.needExtra)
          }
        });
      
      
      this.donorVariants(donorVariants);
    } else {
      this.variantsList.update(variants.filter(i => !i.extra_question));
    }

    this.randomOrder(randomOrder);
    if (!this.donor.useDonor()) {
      if (_uniq(this.variantsList.getVariants().map(el => el.needExtra())).length === 1) {
        this.clarifyingQuestionForAllVariants(this.variantsList.getVariants()[0].needExtra());
      } else {
        this.clarifyingQuestionForAllVariants(false);
      }
    } else {
      if (_uniq(this.donorVariants().map(el => el.needExtra())).length === 1) {
        this.clarifyingQuestionForAllVariants(this.donorVariants()[0].needExtra());
      } else {
        this.clarifyingQuestionForAllVariants(false);
      }
    }
    
  }

  getData() {
    let data = super.getData();

    data.extraQuestionType = this.clarifyingQuestionEnabled()
      ? this.clarifyingQuestionType()
      : 0;

    data.clarifyingQuestion = {
      minСhooseVariants: this.answersCountLimitMin.value(),
      maxСhooseVariants: this.answersCountLimit.value(),
      enabled: this.clarifyingQuestionEnabled(),
      required: this.clarifyingQuestionIsRequired(),
      forAllRates: this.clarifyingQuestionForAllRates(),
      forRates: this.clarifyingQuestionForRates(),
      text: this.clarifyingQuestionText(),
      enableFile: this.enableFile(),

      ...this.clarifyingQuestionController.getData(),
    };

    data.npsGalleryEnabled = this.galleryEnabled() ? 1 : 0;
    data.npsGallery = data.npsGalleryEnabled ? data.gallery : [];

    data.npsCommentEnabled = data.galleryCommentEnabled;
    data.npsCommentLabel = data.galleryCommentLabel;
    data.npsCommentLengthRange = data.galleryCommentLengthRange;
    data.npsCommentPlaceholder = data.galleryCommentPlaceholder;
    data.npsCommentRequired = data.galleryCommentRequired;

    data.ratingType = this.ratingType();
    data.randomOrder = this.randomOrder();

    data.npsType = this.npsType();
    data.npsStartColor = this.startColor();
    data.npsEndColor = this.endColor();
    data.npsStartLabel = this.startLabel();
    data.npsEndLabel = this.endLabel();
    data.fromOne = this.fromOne() ? 1 : 0;

    data.skip = this.skip();
    data.skipText = this.skipText();

    if (this.donor.useDonor()) {
      const donorId = ko.unwrap(this.donor.donorId)
      const donorQestion = window.QUESTIONS.find(i => i.id == donorId)
      const isDonorClassifierWithListType = donorQestion?.main_question_type === 19 && donorQestion?.dictionary_list_type === 'list'

      data = {
        ...data,
        ...this.donor.getData(),
        variants: ko.toJS(this.donorVariants).map((v) => {
          if (v.id === "custom") {
            return {
              value: v.value,
              points: v.points || "",
              needExtra: v.needExtra,
              need_extra: v.needExtra,
              is_self_answer: 1
            };
          }
          
          return {
            id: v.id,
            value: v.value,
            points: v.points || "",
            needExtra: v.needExtra,
            need_extra: v.needExtra,
            dictionary_element_id: v.dictionary_element_id,
            persistentId: v.persistentId,
          };
        }),
        isDonorClassifierWithListType,
      };
    } else {
      data = {
        ...data,
        variants: this.variantsList.getVariants(),

        donor: null,
      };
    }
    return data;
  }

  addVariantsFromDictionary() {
    this.openDialog({
      name: "add-variants-list-dialog",
      params: {
        hasAnswers: parseInt(this.countAnswers) > 0,
        mode: 'dictionary',
        dictionary: this.controller.dictionary,
        checked: this.variantsList.list().filter(el => el.dictionaryElementId).map(el => `${el.dictionaryElementId}`),
        headerText: 'Добавление вариантов из справочника',
      },
      events: {
        submit: async (result) => {
          this.controller.dictionaryElements()
            .filter(el => result.newDetails.find(detail => detail == el.id))
            .forEach(el => {
              if (this.variantsList.list().find(variant => variant.dictionaryElementId == el.id)) {
                return;
              }
              this.variantsList.addExternalVariant(
                el.title,
                undefined,
                {
                  dictionary_element_id: el.id,
                }
              )
            });
        },
      },
    });
  }

  onChangeClarifyingQuestionForAllVariants() {
    const needExtra = this.clarifyingQuestionForAllVariants();

    const variants = this.donor.useDonor()
      ? this.donorVariants()
      : this.variantsList.getVariants();

    variants.forEach(variant => {
      variant.needExtra(needExtra);
    });
  }

  onChangeNeedExtra(flag) {
    const variants = this.donor.useDonor()
      ? this.donorVariants()
      : this.variantsList.getVariants();
    
    this.clarifyingQuestionForAllVariants(flag && !variants.find(el => !el.needExtra()));
  }

  isVariantsValid() {
    return this.variantsList.isVariantsValid();
  }

  isValid() {
    if (!this.defaultValidation()) return false;

    if (!this.clarifyingQuestionText.isValid()) return false;

    if (this.clarifyingQuestionEnabled()) {
      if (!this.clarifyingQuestionController.isValid()) return false;
    }

    if (this.galleryEnabled()) {
      return super.isValid();
    }

    if (this.ratingType() === "variants") {
      if (this.donor.useDonor()) {
        return true;
      }

      return this.variantsList.isValid();
    }

    return true;
  }
}

export default NPSQuestion;
