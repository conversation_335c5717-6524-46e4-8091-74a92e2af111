<template id="question-form-template-priority">
  <!-- ko let: { $translator: question.translator } -->

  <!-- ko ifnot: question.isAuto || question.isSystem || question.mode === 'cpoint' -->
  <div class="form-group">
    <div data-bind="
        click: function() {
          return question.onDonorsTogglerClick();
        }">
      <fc-switch params="checked: question.donor.useDonor,
                disabled: question.disableDonors || question.isFullBlocked,
                label: 'Использовать варианты ответов респондента из другого вопроса',
                hint: 'Использовать варианты ответов респондента из другого вопроса'"></fc-switch>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko if: question.donor.useDonor -->
  <div>
    <div class="form-group">
      <fc-label params="text: 'Вопрос-донор', hint: 'Вопрос-донор'"></fc-label>
      <fc-select
        params="
          options: question.donor.donorsList,
          value: question.donor.donorId,
          disabled: question.isBlocked() || question.isFullBlocked,
        "
        data-bind="
          click: function() {
            if (question.isBlocked() && !question.isFullBlocked) {
              question.tryChangeBlockedParam();
            } else {
              return true;
            }
          },
        "
      ></fc-select>
    </div>

    <div class="form-group">
      <fc-label params="text: 'Варианты ответов'"></fc-label>
      <fc-donor-variants-type params="
            value: question.donor.donorVariantsType,
            isFullBlocked: question.isFullBlocked,
            disabled: question.isBlocked() || question.isFullBlocked" data-bind="click: function() {
                if (question.isBlocked() && !question.isFullBlocked)
                    question.tryChangeBlockedParam();
                else return true;
            }" class="mb-4"></fc-donor-variants-type>
    </div>

    <!-- ko if: question.withPoints -->
    <div class="f-fs-1 f-color-service mb-2"><span data-bind="text: $translator.t('Расположите ответы в том порядке, который будет считаться правильным ответом. При прохождении опроса они будут выводится в случайном порядке. Введите количество баллов за правильный ответ.')"></span> </div>
    <!-- /ko -->

    <fc-donor-variants-list params="variants: question.donorVariants">
    </fc-donor-variants-list>
  </div>
  <!-- /ko -->

  <!-- ko ifnot: question.donor.useDonor -->
  <div>
    <label class="form-label" for="description" data-bind="text: $translator.t('Варианты ответов')"></label>

    <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Нужно указать как минимум 2 варианта ответа. При прохождении опроса изменить порядок ответов можно с помощью перетаскивания')" type="button" title="">
    </button>

    <!-- ko if: question.withPoints -->
    <div class="f-fs-1 f-color-service mb-2"><span data-bind="text: $translator.t('Расположите ответы в том порядке, который будет считаться правильным ответом. При прохождении опроса они будут выводится в случайном порядке. Введите количество баллов за правильный ответ.')"></span> </div>
    <!-- /ko -->

    <fc-variants-list
      params="
        variantsList: question.variantsList,
        isBlocked: question.isBlocked,
        isFullBlocked: question.isFullBlocked,
        withPoints: false,
        canRemoveSingleVariant: false,
        formControlErrorStateMatcher: question.formControlErrorStateMatcher,
        formControlSuccessStateMatcher: question.formControlSuccessStateMatcher
      "
    ></fc-variants-list>
    <!-- ko if: question.formControlErrorStateMatcher(question.variantsList.count)() -->
    <validation-feedback
      params="
        show: question.formControlErrorStateMatcher(question.variantsList.count),
        text: question.variantsList.count.error()
      "
    ></validation-feedback>
    <!-- /ko -->

    <!-- ko ifnot: question.isFullBlocked -->
    <div class="row">
      <div
        class="col-6"
        data-bind="
          css: {
            'col-6': !!window.POLL && window.POLL.dictionary_id,
            'col-12': !window.POLL || !window.POLL.dictionary_id,
          },
        "
      >
        <button
          class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
          data-bind="
            click: function() {
              question.variantsList.addVariant();
            },
            attr: {
              disabled: !question.isVariantsValid(),
            },
          "
        >
          <span class="survey-question__variants-control-add-button-icon"></span>
          <span data-bind="text: question.translator.t('Добавить вариант')"></span>
        </button>
      </div>
      <div class="col-6" data-bind="if: !!window.POLL && window.POLL.dictionary_id">
        <button
          class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
          data-bind="
            click: function() {
              if (controller.dictionaryConnectionSwitchDisabled()) {
                controller.onDictionaryConnectionSwitchClick(controller);
                return;
              }
              question.addVariantsFromDictionary();
            },
            css: {
              'variants-controller__add-button--blocked': controller.dictionaryConnectionSwitchDisabled(),
            },
          "
        >
          <span class="survey-question__variants-control-add-button-icon"></span>
          <span data-bind="text: question.translator.t('Добавить из справочника')"></span>
        </button>
      </div>
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->






  <!-- ko if: question.withPoints -->
  <div class="form-group">
    <label class="form-label"><span data-bind="text: $translator.t('Баллов за правильный ответ')"></span></label>

    <div class="row">
      <div class="col-12 col-md-10 col-lg-6">
        <input type="text" class="form-control text-center" data-bind="onlyNumbers: { sign: true }, textInput: question.rightAnswerPoints, disable: question.isFullBlocked" placeholder="0 баллов" maxlength="9">
      </div>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko if: question.withPoints -->
  <foquz-checkbox
    class="mb-4"
    params="
      checked: question.maxPointsCalcMethod,
      disabled: question.isFullBlocked,
    "
  >
    <span
      data-bind="text: question.translator.t('Учитывать в итоговой сумме баллов, если вопрос не отображался для респондента')"
    ></span>
    <question-button
      params="text: question.translator.t('Настройки отображения вопроса можно настроить в логике опроса. По умолчанию все скрытые логикой отображения вопросы учитываются в итоговом подсчете баллов')"
    ></question-button>
  </foquz-checkbox>
  <!-- /ko -->

  <foquz-checkbox
    class="mb-4"
    params="checked: question.needReorder, disabled: question.isFullBlocked"
  >
    <span data-bind="text: $translator.t('Обязательное перемещение вариантов')"></span>
  </foquz-checkbox>

  <foquz-checkbox
    class="mb-4"
    params="
      checked: question.randomOrder,
      disabled: question.isFullBlocked,
    "
  >
    <span
      data-bind="text: $translator.t('Случайный порядок вариантов')"
    ></span>
    <question-button
      params="text: $translator.t('Варианты ответов для каждого респондента при прохождении будут предложены в случайном порядке')"
    ></question-button>
  </foquz-checkbox>

  <hr class="mx-0">

  <!-- Комментарий -->
  <div class="mt-4">
    <switch params="checked: question.commentEnabled, disabled: question.isFullBlocked">
      <span data-bind="text: question.translator.t('Комментарий')"></span>
    </switch>
  </div>

  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div>
    <div class="row pb-4">
        <div class="col">
          <div class="f-check">
              <input
              type="checkbox"
              id="comment-required"
              class="f-check-input"
              data-bind="
                  checked: question.commentIsRequired,
                  disable: question.isFullBlocked,
                  event: { change: function() { 
                  question.updateCommentRequired() } }
              "
              />
              <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
          </div>
        </div>
    </div>
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: question.translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
    } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->

  <!-- /ko -->
</template>