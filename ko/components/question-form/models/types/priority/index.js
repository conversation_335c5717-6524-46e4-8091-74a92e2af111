import Question from "../../question";

import { PRIORITY_QUESTION } from "Data/question-types";

import { Donor } from "../../donor";
import { VariantsListModel } from "@/components/question-form/models/variants-list";
import TextField from "../../text-field";

class PriorityQuestion extends Question {
  constructor(controller, config) {
    super(controller, config);

    this.type = PRIORITY_QUESTION;

    // variants
    this.variantsList = VariantsListModel({
      min: 2,
    });
    
    // donor
    this.donor = Donor(controller.donors);
    this.blockRecipients = controller.blockRecipients;
    this.disableDonors = ko.computed(() => {
      if (this.isFullBlocked) return true;
      if (ko.toJS(this.isBlocked)) return true;
      if (this.blockRecipients) return true;
      if (!this.donor.donorsList().length) return true;
      return false;
    });
    this.donorVariants = ko.observableArray([]);

    this.donor.useDonor.subscribe((v) => {
      if (v) this.variantsList.update([]);
    });
    this.donor.donorId.subscribe((v) => {
      const donorVariants = this.donor.getDonorVariants(v);
      this.donorVariants(donorVariants);
    });

    this.maxPointsCalcMethod = ko.observable(false);

    // options
    this.rightAnswerPoints = ko.observable("");
    this.needReorder = ko.observable(false);
    this.randomOrder = ko.observable(false);

    this.commentEnabled = ko.observable(false);
    this.commentLabel = ko.observable('');
    this.commentIsRequired = ko.observable(null);
    this.commentField = new TextField();

    this.usesDictionaryElements = ko.computed(() => {
      if (this.donor.useDonor()) {
        return !!this.donorVariants().find(el => el.dictionaryElementId);
      }
      return !!this.variantsList.list().find(el => el.dictionaryElementId);
    }, this);

    this.dictionaryBadgeTooltipText = ko.computed(() => {
      if (!controller.question()) {
        return '';
      }
      return `
        ${controller.question().dictionaryElementId() ? `Связка вопроса с элементом ${controller.dictionary().name} справочника ${controller.dictionary().name}` : ''}
        ${controller.question().usesDictionaryElements() ? `${controller.question().dictionaryElementId() ? '.' : ''} В вопросе есть варианты, связанные с элементами справочника ${controller.dictionary().name}` : ''}
      `;
    }, this);

    this.subscriptions.push(
      this.maxPointsCalcMethod.subscribe((v) => this.onChange(true)),
      this.commentIsRequired.subscribe((v) => this.onChange(true)),
      this.commentEnabled.subscribe((v) => this.onChange(true)),
      this.commentLabel.subscribe((v) => this.onChange(true)),
      this.commentField.isChanged.subscribe((v) => this.onChange(true)),
      this.needReorder.subscribe((v) => this.onChange()),
      this.randomOrder.subscribe((v) => this.onChange()),
      this.donorVariants.subscribe(() => this.onChange(true)),
      this.variantsList.isChanged.subscribe((v) => this.onChange(true)),
      this.donor.isChanged.subscribe((v) => this.onChange(true))
    );
  }

  onDonorsTogglerClick() {
    if (this.isFullBlocked) return false;
    if (ko.toJS(this.isBlocked)) {
      this.tryChangeBlockedParam();
    } else if (this.blockRecipients) {
      this.tryChangeBlockedParam();
    } else if (!this.donor.donorsList().length) {
      this.noDonorsInfo();
    } else return true;
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  updateData(data) {
    super.updateData(data);

    const {
      variants,
      rightAnswer,
      needReorder,
      randomOrder,
      donorId,
      donorVariantsType,
      donorOrder = [],
      priorityCommentEnabled,
      priorityCommentLabel,
      priorityCommentLengthRange,
      priorityCommentPlaceholder,
      maxPointsCalcMethod
    } = data;

    this.donor.update({ donorId, donorVariantsType, donorOrder, variants });

    this.maxPointsCalcMethod(maxPointsCalcMethod)
    this.commentEnabled(priorityCommentEnabled);
    this.commentLabel(priorityCommentLabel || '');
    this.commentIsRequired(data.priorityCommentRequired)
    this.commentField.updateData({
      range: priorityCommentLengthRange,
      placeholder: priorityCommentPlaceholder,
      required: data.priorityCommentRequired
    });

    if (donorId) {
      const donorType = window.QUESTIONS.find(i => i.id == donorId).main_question_type
      const donorVariants = this.donor.getDonorVariants(donorId);

      let currentOrder
      if (donorType !== 19) {

        currentOrder = variants.map((v) => v.donorId || "custom");
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(a.id);
          const bIndex = currentOrder.indexOf(b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      } else {
        const donorIds = donorVariants.map(i => +i.id)
        const temp_variants = variants.filter(i => donorIds.includes(i.dictionary_element_id)).sort((a, b) => a.position - b.position)
        currentOrder = temp_variants.map((v) => v.dictionary_element_id);
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(+a.id);
          const bIndex = currentOrder.indexOf(+b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      }

        
        donorVariants.forEach((variant) => {
          const data = variants.find((v) => {
            if (variant.id === "custom") return !v.donorId;
            return v.donorId === variant.id;
          });

          if (data) {
            variant.points(data.points);
          }

          variant._recipientId = data?.id
        });
      
      this.donorVariants(donorVariants);
    } else {
      this.variantsList.update(variants);
    }

    if (rightAnswer) this.rightAnswerPoints(rightAnswer.points);
    this.needReorder(!!needReorder);
    this.randomOrder(!!randomOrder);
  }

  getData() {
    const parentData = super.getData();

    let data = {
      ...parentData,
      rightAnswerPoints: this.rightAnswerPoints(),
      needReorder: this.needReorder(),
      randomOrder: this.randomOrder(),
    };

    data.priorityCommentEnabled = this.commentEnabled();
    data.priorityCommentLabel = this.commentLabel();
    let commentFieldData = this.commentField.getData();
		data.priorityCommentLengthRange = commentFieldData.range;
		data.priorityCommentPlaceholder = commentFieldData.placeholder;
    data.priorityCommentRequired = commentFieldData.required;

    if (this.donor.useDonor()) {
      const donorId = ko.unwrap(this.donor.donorId)
      const donorQestion = window.QUESTIONS.find(i => i.id == donorId)
      const isDonorClassifierWithListType = donorQestion?.main_question_type === 19 && donorQestion?.dictionary_list_type === 'list'

      data = {
        ...data,
        ...this.donor.getData(),
        variants: ko.toJS(this.donorVariants).map((v) => {
          if (v.id === "custom") {
            return {
              value: v.value,
              points: v.points || "",
              recipientId: v._recipientId,
            };
          }
          return {
            id: v.id,
            value: v.value,
            points: v.points || "",
            recipientId: v._recipientId,
          };
        }),
        isDonorClassifierWithListType,
      };
    } else {
      data = {
        ...data,
        variants: this.variantsList.getVariants(),
        donor: null,
        maxPointsCalcMethod: this.maxPointsCalcMethod()
      };
    }

    return data;
  }

  addVariantsFromDictionary() {
    this.openDialog({
      name: "add-variants-list-dialog",
      params: {
        hasAnswers: parseInt(this.countAnswers) > 0,
        mode: 'dictionary',
        dictionary: this.controller.dictionary,
        checked: this.variantsList.list().filter(el => el.dictionaryElementId).map(el => `${el.dictionaryElementId}`),
        headerText: 'Добавление вариантов из справочника',
      },
      events: {
        submit: async (result) => {
          this.controller.dictionaryElements()
            .filter(el => result.newDetails.find(detail => detail == el.id))
            .forEach(el => {
              if (this.variantsList.list().find(variant => variant.dictionaryElementId == el.id)) {
                return;
              }
              this.variantsList.addExternalVariant(
                el.title,
                undefined,
                {
                  dictionary_element_id: el.id,
                }
              )
            });
        },
      },
    });
  }
  
  isVariantsValid() {
    return this.variantsList.isVariantsValid();
  }

  isValid() {
    if (!super.isValid()) return false;

    if (this.donor.useDonor()) {
      return true;
    }

    return this.variantsList.isValid();
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}

export default PriorityQuestion;
