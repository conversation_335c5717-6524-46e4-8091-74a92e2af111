import Question from '../../question';
import './style.less';

import { <PERSON><PERSON>ontroller } from '../../../controllers/link-controller';
import { QUIZ_QUESTION } from 'Data/question-types';

import { MaskedFieldConfig } from 'Models/masked-field-config';
import { TextFieldConfig } from 'Models/text-field-config';
import { getUnique } from '@/utils/unique';

const FIELD_TYPE_SINGLE_LINE = '0';
const FIELD_TYPE_MULTIPLE_LINE = '1';
import { Translator } from '@/utils/translate';

const QuestionTranslator = Translator('question');
const ValidationTranslator = Translator('validation')

import 'Components/input/text-field-config';
import 'Components/input/masked-field-config';


class QuizField {
  constructor(data, question) {
    this.question = question;

    this.id = ko.observable(data.id);
    this.persistentId = ko.observable(data.id !== "0" ? data.id : getUnique())
    this.required = ko.observable(data.required);

    this.required.subscribe((v) => {
      this.question.onQuizRequiredChange(v);
    });

    this.fieldType = ko.observable(
      data.isTextarea ? FIELD_TYPE_MULTIPLE_LINE : FIELD_TYPE_SINGLE_LINE
    );

    this.isMultiline = ko.pureComputed(() => {
      return this.fieldType() == FIELD_TYPE_MULTIPLE_LINE;
    });

    // TODO длина плейсхолдера может быть разной

    this.maskController = new MaskedFieldConfig();
    this.maskController.link(this.required);
    this.fieldController = new TextFieldConfig();

    if (this.isMultiline()) {
      this.fieldController.updateData({
        placeholder: data.placeholder,
        range: data.lengthRange
      });
    } else {
      this.maskController.updateData({
        type: data.maskType,
        config: data.maskConfig,
        placeholder: data.placeholder,
        range: data.lengthRange
      });
    }

    this.isMultiline.subscribe((v) => {
      this.fieldController.range([0, v ? 500 : 250]);
    });

    this.linkController = new LinkController();
    this.linkController.updateData(data);

    this.label = ko.observable(data.label).extend({
      required: {
        message: () => ValidationTranslator.t('Обязательное поле')(),
        onlyIf: () => {
          return !this.maskController.isNameMask();
        }
      }
    });

    this.isValid = ko.pureComputed(() => {
      if (!this.label.isValid()) return false;
      if (this.isMultiline()) return true;
      if (!this.maskController.isValid()) return false;
      return true;
    });

    [this.required, this.fieldType, this.label].forEach((f) =>
      f.subscribe((v) => this.question.onChange())
    );

    this.maskController.on('change', () => this.question.onChange());
    this.fieldController.on('change', () => this.question.onChange());
  }

  getData() {
    let label = this.label();
    let isTextarea = this.fieldType() == FIELD_TYPE_MULTIPLE_LINE;

    if (!isTextarea && this.maskController.isNameMask()) {
      label = QuestionTranslator.t('ФИО')();
    }

    let data = {
      id: this.id(),
      persistentId: this.persistentId(),
      isTextarea: isTextarea ? 1 : 0,
      label: label,
      required: this.required()
    };

    data = {
      ...data,
      ...this.linkController.getData()
    };

    if (isTextarea) {
      let fieldData = this.fieldController.getData();
      data.maskType = 0;
      data.maskConfig = {};
      data.placeholder = fieldData.placeholder;
      data.lengthRange = fieldData.range;
    } else {
      let maskData = this.maskController.getData();
      data.maskType = maskData.type;
      data.maskConfig = maskData.config;
      data.placeholder = maskData.placeholder;
      data.lengthRange = maskData.range;
    }

    return data;
  }
}

class QuizQuestion extends Question {
  constructor(controller, config) {
    super(controller, config);

    this.type = QUIZ_QUESTION;

    this.quizzes = ko.observableArray([]);
    this.addQuiz();

    this.isQuizzesValid = ko.pureComputed(() => {
      return !this.quizzes().some((q) => !q.isValid());
    });

    this.quizzes.subscribe((v) => this.onChange());

    let isQuestionChanged = ko.observable(false);
    let isQuizChanged = ko.observable(false);

    this.onQuizRequiredChange = (v) => {
      if (isQuestionChanged()) return;

      isQuizChanged(true);
      if (v) this.required(true);
      else {
        if (!this.quizzes().some((q) => q.required())) this.required(false);
      }
      isQuizChanged(false);
    };

    this.required.subscribe((v) => {
      if (isQuizChanged()) return;

      isQuestionChanged(true);
      this.quizzes().forEach((q) => q.required(v));
      isQuestionChanged(false);
    });
  }

  addQuiz() {
    this.quizzes.push(
      new QuizField(
        {
          isTextarea: false,
          required: true,
          label: '',
          id: '0',
          lengthRange: [0, 250],
          placeholder: ''
        },
        this
      )
    );
  }

  removeQuiz(quiz) {
    this.quizzes.remove(quiz);
  }

  updateData(data) {
    super.updateData(data);

    this.quizzes(data.quizzes.map((q) => new QuizField(q, this)));
    if (!this.quizzes().length) this.addQuiz();
  }

  getData() {
    let data = super.getData();

    data.quizzes = this.quizzes().map((q) => q.getData());

    return data;
  }

  isValid() {
    if (!super.isValid()) return false;

    return this.isQuizzesValid();
  }
}

export default QuizQuestion;

ko.components.register('quiz-question-block', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass('survey-question__quiz-control');

      const viewModel = new (function () {
        this.question = params.question;
        this.controller = this.question.controller;

        this.canAddQuiz = ko.observable(true);

        this.question.isQuizzesValid.subscribe((v) => {
          if (v) this.canAddQuiz(true);
        });

        this.formControlErrorStateMatcher = (formControl) => {
          let matcher =
            this.controller.formControlErrorStateMatcher(formControl);
          return !this.canAddQuiz() || matcher();
        };
        this.formControlSuccessStateMatcher =
          this.controller.formControlSuccessStateMatcher;

        this.fieldTypes = [
          {
            id: FIELD_TYPE_SINGLE_LINE,
            label: QuestionTranslator.t('Однострочное')(),
            icon: 'oneline'
          },
          {
            id: FIELD_TYPE_MULTIPLE_LINE,
            label: QuestionTranslator.t('Многострочное')(),
            icon: 'multiline'
          }
        ];

        this.sorting = ko.observable(false);

        this.remove = function (v) {
          this.question.removeQuiz(v);
        };

        this.addQuiz = function () {
          if (
            this.question.quizzes().length &&
            !this.question.isQuizzesValid()
          ) {
            this.canAddQuiz(false);
            return;
          }

          this.question.addQuiz();
        };

        this.isValid = ko.pureComputed(() => {
          return this.value().every((v) => v.name().length > 0);
        });

        this.onSort = (event) => {
          let quizzes = this.question.quizzes;
          this.sorting(true);
          const movedItem = quizzes()[event.data.oldIndex];
          quizzes.remove(movedItem);
          quizzes.splice(event.data.newIndex, 0, movedItem);
          this.sorting(false);
        };

        this.beforeRemove = (element) => {
          const duration = this.sorting() ? 0 : 200;
          return $(element)
            .delay(0)
            .fadeOut(duration, function () {
              return $(element).remove();
            });
        };
      })();

      viewModel.onInit = function () {};

      return viewModel;
    }
  },
  template: {
    element: 'quiz-question-block-template'
  }
});
