<template id="question-form-template-quiz">
  <!-- ko let: { $translator: question.translator } -->
  <div data-bind="component: {
        name: 'quiz-question-block',
        params: {
          question: question,
        }
    }">
  </div>

  <template id="quiz-question-block-template">
    <!-- ko template: { afterRender: onInit } -->
    <div class="survey-question__quiz-control-list">
      <div class="survey-question__quiz-control-list-content" data-bind="sortable,
          sortableItem: '.survey-question__quiz-control-list-item',
          sortableHandle: '.survey-question__quiz-title',
          onSort: onSort">
        <!-- ko foreach: {
          data: question.quizzes,
          as: 'quiz',
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: beforeRemove
        } -->
        <div class="survey-question__quiz-control-list-item">

          <div class="survey-question__quiz-title" data-bind="css: {
            'pr-0': $parent.question.isFullBlocked
          }">
            <!-- ko ifnot: $parent.question.isFullBlocked -->
            <span class="survey-question__quiz-control-list-item-drag hide-on-dragging sortable-handle">
              <i class="icon icon-drag-arrow"></i>
            </span>
            <!-- /ko -->
            <span class="survey-question__quiz-title-text hide-on-dragging" data-bind="text: $translator.t('Поле {index}', { index: ($index() + 1) })"></span>
            <div class="form-group switch-form-group survey-question__required-form-group hide-on-dragging">
              <label class="switch form-control">
                <input type="checkbox" data-bind="checked: quiz.required, disable: question.isFullBlocked">
                <span class="switch__slider"></span>
              </label>

              <label class="form-label" data-bind="css: {
                'form-label_checked': quiz.required
              }"><span data-bind="text: $translator.t('Обязательное')"></span></label>
            </div>
          </div>

          <div class="survey-question__quiz-control-list-item-content remove-in-mirror remove-on-dragging">
            <!-- ko ifnot: quiz.maskController.isNameMask -->
            <div class="form-group">

              <label class="form-label"><span data-bind="text: $translator.t('Название поля')"></span></label>

              <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Название поля, которое будет отображаться над полем ввода')" type="button" title="">
              </button>

              <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: quiz.label().length">
                <input class="form-control" data-bind="
                    textInput: quiz.label,
                    disable: question.isFullBlocked,
                    css: {
                        'is-invalid': $component.formControlErrorStateMatcher(quiz.label),
                        'is-valid': $component.formControlSuccessStateMatcher(quiz.label)
                    }
                " maxlength="60">


                <div class="chars-counter__value"></div>

              </div>

              <!-- ko if: $component.formControlErrorStateMatcher(quiz.label) -->
              <div class="form-error" data-bind="text: quiz.label.error()"></div>
              <!-- /ko -->
            </div>
            <!-- /ko -->

            <!-- ko if: quiz.maskController.noMask  -->
            <div class="hat-radio-group hat-radio-group--dense survey-question__variants-type-selector">
              <!-- ko foreach: { data: $component.fieldTypes, as: 'fieldType' } -->
              <div class="hat-radio-group__radio"
              data-bind="let: { inputId: 'survey-question-variants-type-selector-' + quizIndex() + '-' + fieldType.id }">
                <input class="hat-radio-group__radio-input" type="radio"
                data-bind="
                value: fieldType.id,
                checked: quiz.fieldType,
                disable: question.isFullBlocked,
                attr: { id: inputId, name: 'quiz-field-type' + quizIndex() }" />
                <label class="hat-radio-group__radio-label" data-bind="attr: { 'for': inputId }">
                  <i class="survey-question__variants-type-selector-value-icon"
                  data-bind="class: 'survey-question__variants-type-selector-value-' + fieldType.icon + '-icon'"></i>
                  <!-- ko text: fieldType.label -->
                  <!-- /ko -->
                </label>
              </div>
              <!-- /ko -->
            </div>
            <!-- /ko -->

            <!-- ko if: quiz.isMultiline -->
                <text-field-config params="field: quiz.fieldController, disabled: question.isFullBlocked"></text-field-config>
            <!-- /ko -->

            <!-- ko ifnot: quiz.isMultiline -->
                <masked-field-config params="mask: quiz.maskController, disabled: question.isFullBlocked"></masked-field-config>
            <!-- /ko -->

            <hr class="mt-0">

            <!-- Связь с полем -->
            <div class="link-field-wrapper pt-5p" data-bind="component: {
              name: 'link-controller-block',
              params: {
                controller: quiz.linkController,
                disabled: question.isFullBlocked || !(question.isPaidRate || question.isContactsEnabled),
              }
            }"></div>
            <!-- /Связь с полем -->


            <!-- ko template: {
              foreach: templateIf(question.quizzes().length > 1, $data),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: $component.beforeRemove
            } -->
            <!-- ko ifnot: question.isFullBlocked -->
            <!-- ko if: !question.isBlocked() || quiz.id() == '0' -->
            <button type="submit" class="btn btn-danger survey-question__variants-control-list-item-remove-button" title="" data-bind="click: function() { question.removeQuiz($data); }, attr: {
              title: _t('Удалить')
            }">
            </button>
            <!-- /ko -->
            <!-- /ko -->
            <!-- /ko -->
          </div>
        </div>
        <!-- /ko -->
      </div>

      <!-- ko ifnot: question.isFullBlocked -->
      <button class="survey-question__variants-control-add-button mb-4" data-bind="
                          click: function() { $component.addQuiz(); },
                          enable: $component.canAddQuiz
                      ">
        <span class="survey-question__variants-control-add-button-icon"></span>
        <span data-bind="text: $translator.t('Добавить поле')"></span>
      </button>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </template>
  <!-- /ko -->
</template>
