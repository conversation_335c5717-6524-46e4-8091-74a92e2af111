import Question from "../../question";
import { LinkController } from "../../../controllers/link-controller";
import { ADDRESS_QUESTION } from "Data/question-types";

import { Translator } from "@/utils/translate";

ko.bindingHandlers.surveyQuestionCallback = {
  init: function (element, valueAccessor) {
    valueAccessor()(element);
  },
};
class AddressQuestion extends Question {
  constructor(controller, config) {
    super(controller, config);

    this.type = ADDRESS_QUESTION;
    this.updating = ko.observable(false);

    this.linkController = new LinkController(this);

    this.placeholder = ko.observable("");
    this.placeholderLimit = 60;

    this.regionsValues = ko.observableArray([]);
    this.regionsValuesNames = ko.observable();

    this.districtValues = ko.observableArray([]);
    this.districtValuesNames = ko.observable();

    this.streetValues = ko.observableArray([]);
    this.streetValuesNames = ko.observable();

    this.cityValues = ko.observableArray([]);
    this.cityValuesNames = ko.observable();

    [
      this.placeholder,
      this.regionsValues,
      this.districtValues,
      this.streetValues,
      this.cityValues,
    ].forEach((f) => f.subscribe((v) => this.onChange()));
  }

  updateData(data) {
    this.updating(true);
    super.updateData(data);

    this.regionsValues(data.regions.map((v) => v.id));
    this.regionsValuesNames(
      _.chain(data.regions)
        .keyBy("id")
        .mapValues((v) => v.name)
        .value()
    );

    console.log({
      regions: this.regionsValues(),
      names: this.regionsValuesNames(),
    });

    this.districtValues(data.districts.map((v) => v.id));
    this.districtValuesNames(
      _.chain(data.districts)
        .keyBy("id")
        .mapValues((v) => v.name)
        .value()
    );

    this.streetValues(data.streets.map((v) => v.id));
    this.streetValuesNames(
      _.chain(data.streets)
        .keyBy("id")
        .mapValues((v) => v.name)
        .value()
    );

    this.cityValues(data.cities.map((v) => v.id));
    this.cityValuesNames(
      _.chain(data.cities)
        .keyBy("id")
        .mapValues((v) => v.name)
        .value()
    );

    this.placeholder(data.addressFieldPlaceholder || "");

    this.linkController.updateData(data);
    this.updating(false);
  }

  getData() {
    let data = super.getData();

    let regions = this.regionsValues();
    let districts = this.districtValues();
    let cities = this.cityValues();
    let streets = this.streetValues();

    let regionsNames = this.regionsValuesNames();
    let districtsNames = this.districtValuesNames();
    let citiesNames = this.cityValuesNames();
    let streetsNames = this.streetValuesNames();

    data.regions = regions.map((r) => {
      return {
        id: r,
        name: regionsNames[r],
      };
    });

    data.districts = districts.map((d) => {
      return {
        id: d,
        name: districtsNames[d],
      };
    });

    data.cities = cities.map((c) => {
      return {
        id: c,
        name: citiesNames[c],
      };
    });

    if (!cities.length && !districts.length) {
      data.streets = [];
    } else {
      data.streets = streets.map((s) => {
        return {
          id: s,
          name: streetsNames[s],
        };
      });
    }

    data.addressFieldPlaceholder = this.placeholder();

    data = {
      ...data,
      ...this.linkController.getData(),
    };

    return data;
  }

  kladrRequest(params) {
    var node = document.createElement("script"),
      src = "//kladr-api.ru/api.php?",
      res,
      rej,
      result = new Promise(function (resolve, reject) {
        res = resolve;
        rej = reject;
      });

    node.type = "text/javascript";
    node.id = new Date().getTime() + Math.random();
    node.onerror = function () {
      node.parentNode.removeChild(node);
      rej();
    };
    node.onload = function () {
      node.parentNode.removeChild(node);
    };

    if (typeof params == "string")
      src +=
        ["?", "&"].indexOf(params.substr(0, 1)) > -1
          ? params.substr(1)
          : params;
    else if (typeof params == "object")
      src += Object.keys(params)
        .map(function (key) {
          return key + "=" + encodeURIComponent(params[key]);
        })
        .join("&");

    var callback =
      "__jsonp_dynamic_callback_" + node.id.toString().replace(".", "");
    window[callback] = function (json) {
      res(json);

      delete window[callback];
    };

    node.src = src + (!params ? "" : "&") + "callback=" + callback;
    document.body.appendChild(node);

    return result;
  }
  transport(params, success, failure) {
    let arData = params.data,
      arResults = [],
      promises = [];

    if (arData) {
      arData.forEach((item) => {
        params.data = item;
        var $request = $.ajax(params);
        promises.push(
          $request.then(() => {
            $request.responseJSON.result.forEach((subItem) => {
              arResults.push(subItem);
            });
          })
        );
      });

      Promise.all(promises).then(() => {
        arResults.sort(function (a, b) {
          if (a.name > b.name) {
            return 1;
          }
          if (a.name < b.name) {
            return -1;
          }
          return 0;
        });

        success(arResults);
      });
    }
  }

  formatRegionsData(term, data) {
    return {
      query: term.term,
      contentType: "region",
    };
  }
  processRegionsResponse(data) {
    let result = [];

    data.result.forEach((item) => {
      if (item.id !== "Free") {
        let fullName = "";
        if (item.type !== "Город" && item.type !== "Республика") {
          fullName = item.name + " " + item.type.toLowerCase();
        } else {
          fullName = item.type + " " + item.name;
        }
        this.regionsValuesNames({
          ...this.regionsValuesNames(),
          [item.id]: fullName,
        });
        result.push(item);
      }
    });

    return {
      results: result,
    };
  }

  onRegionsChange() {
    () => {
      if (this.districtValues().length && this.districtValues().forEach) {
        let arErrors = [],
          promises = [];
        this.districtValues().forEach((item, iIndex) => {
          let regionId = item.substr(0, 2),
            value = item;
          if (this.regionsValues().length && this.regionsValues().forEach) {
            let errors = true;
            this.regionsValues().forEach((item, index) => {
              if (item.substr(0, 2) === regionId) {
                errors = false;
              }
            });
            if (errors) {
              arErrors.push(value);
            }
          }
        });
        arErrors.forEach((value, i) => {
          this.districtValues.remove(value);
        });
      }

      if (this.cityValues().length && this.cityValues().forEach) {
        let arErrors = [],
          promises = [];
        this.cityValues().forEach((item, iIndex) => {
          let regionId = item.substr(0, 2),
            value = item;
          if (this.regionsValues().length && this.regionsValues().forEach) {
            let errors = true;
            this.regionsValues().forEach((item, index) => {
              if (item.substr(0, 2) === regionId) {
                errors = false;
              }
            });
            if (errors) {
              arErrors.push(value);
            }
          }
        });
        arErrors.forEach((value, i) => {
          this.cityValues.remove(value);
        });
      }
    };
  }

  regionsCallback(element) {
    const select$ = $(element);
    this.regionsValues().forEach((v) => {
      const option = new Option(this.regionsValuesNames()[v], v, true, true);
      select$.append(option);
    });
  }

  formatDistrictsData(term, page) {
    let resp = [];
    if (this.regionsValues() && term.term) {
      this.regionsValues().forEach((region) => {
        resp.push({
          query: term.term,
          contentType: "district",
          regionId: region,
          withParent: 1,
        });
      });
    }
    return resp;
  }
  processDistrictsResponse(data) {
    let result = [];
    data.forEach((item) => {
      if (item.id !== "Free") {
        let fullName = "";
        item.value = item.id;
        fullName = item.name + " " + item.type.toLowerCase();
        this.districtValuesNames({
          ...this.districtValuesNames(),
          [item.id]: fullName,
        });
        result.push(item);
      }
    });
    return {
      results: result,
    };
  }
  districtsCallback(element) {
    const select$ = $(element);
    this.districtValues().forEach((v) => {
      const option = new Option(this.districtValuesNames()[v], v, true, true);
      select$.append(option);
    });
  }

  formatCitiesData(term, page) {
    let resp = [];
    if (this.districtValues().length) {
      this.districtValues().forEach((district) => {
        resp.push({
          query: term.term,
          contentType: "city",
          districtId: district,
          limit: 30,
          withParent: 1,
        });
      });
    } else if (this.regionsValues() && term.term) {
      this.regionsValues().forEach((region) => {
        resp.push({
          query: term.term,
          contentType: "city",
          regionId: region,
          limit: 30,
          withParent: 1,
        });
      });
    }
    return resp;
  }
  processCitiesResponse(data) {
    let result = [];
    data.forEach((item) => {
      if (item.id !== "Free") {
        let fullName = "";
        item.value = item.id;
        fullName = item.name + " " + item.type.toLowerCase();
        this.cityValuesNames({
          ...this.cityValuesNames(),
          [item.id]: fullName,
        });
        result.push(item);
      }
    });
    return {
      results: result,
    };
  }

  onCitiesChange() {
    if (this.streetValues().length && this.streetValues().forEach) {
      let arErrors = [],
        promises = [];
      this.streetValues().forEach((item, iIndex) => {
        let regionId = item.substr(0, 4),
          value = item;
        if (this.cityValues().length && this.cityValues().forEach) {
          let errors = true;
          this.cityValues().forEach((item, index) => {
            if (item.substr(0, 4) === regionId) {
              errors = false;
            }
          });
          if (errors) {
            arErrors.push(value);
          }
        }
      });
      arErrors.forEach((value, i) => {
        this.streetValues.remove(value);
      });
    }
  }

  citiesCallback(element) {
    const select$ = $(element);
    this.cityValues().forEach((v) => {
      const option = new Option(this.cityValuesNames()[v], v, true, true);
      select$.append(option);
    });
  }

  formatStreetsData(term, page) {
    let resp = [];
    if (this.cityValues() && term.term) {
      this.cityValues().forEach((region) => {
        resp.push({
          query: term.term,
          contentType: "street",
          limit: 30,
          cityId: region,
          withParent: 1,
        });
      });
    }
    return resp;
  }
  processStreetsResponse(data) {
    let result = [];
    data.forEach((item) => {
      if (item.id !== "Free") {
        let fullName = "";
        item.value = item.id;
        fullName = item.name + " " + item.type.toLowerCase();
        this.streetValuesNames({
          ...this.streetValuesNames(),
          [item.id]: fullName,
        });
        result.push(item);
      }
    });
    return {
      results: result,
    };
  }

  streetsCallback(element) {
    const select$ = $(element);
    this.streetValues().forEach((v) => {
      const option = new Option(this.streetValuesNames()[v], v, true, true);
      select$.append(option);
    });
  }

  getSuggestionsList(sQuery, sType) {
    let response;
    this.kladrRequest({
      contentType: sType,
      withParent: 0,
      query: sQuery,
    }).then(function (json) {
      //response = json;
    });
  }

  regionTemplateSelection(state) {
    if (!state.id) {
      return state.name;
    }

    const level = state.id.slice(0, 2);

    return $(
      `<strong class="survey-question__strong">${level}</strong>` +
        "<span>" +
        this.regionsValuesNames()[state.id] +
        "</span>"
    );
  }

  regionInvertTemplateSelection(state) {

    if (!state.id) {
      return state.name;
    }

    const level = state.id.slice(0, 2);

    return $(
      "<span>" +
        this.regionsValuesNames()[state.id] +
        "</span>" +
        `<strong class="survey-question__strong">${level}</strong>`
    );
  }
  districtTemplateSelection(state) {
    if (!state.id) {
      return state.name;
    }

    return $("<span>" + this.districtValuesNames()[state.id] + "</span>");
  }
  districtInvertTemplateSelection(state) {
    if (!state.id) {
      return state.name;
    }

    return $("<span>" + this.districtValuesNames()[state.id] + "</span>");
  }
  cityTemplateSelection(state) {
    if (!state.id) {
      return state.name;
    }

    return $("<span>" + this.cityValuesNames()[state.id] + "</span>");
  }
  streetTemplateSelection(state) {
    if (!state.id) {
      return state.name;
    }

    return $("<span>" + this.streetValuesNames()[state.id] + "</span>");
  }

  isValid() {
    return super.isValid();
  }
}

export default AddressQuestion;
