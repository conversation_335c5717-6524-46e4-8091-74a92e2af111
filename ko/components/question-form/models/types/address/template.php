<template id="question-form-template-address">
    <!-- ko let: {$translator: question.translator} -->
    <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Регионы')"></label>

        <button type="button" tabindex="10" class="btn-question" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно ограничить список адресов для выбора, указав регионы, районы, города и улицы')" ></button>

        <!-- ko ifnot: question.updating -->
        <select multiple="multiple"  id="regions-select2" data-bind="selectedOptions: question.regionsValues,
            valueAllowUnset: true,
            disable: question.isFullBlocked,
            select2: {
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                templateResult: question.regionTemplateSelection.bind(question),
                templateSelection: question.regionInvertTemplateSelection.bind(question),
                minimumInputLength: 1,
                ajax: {
                    url: 'https://kladr-api.ru/api.php',
                    dataType: 'jsonp',
                    processResults: question.processRegionsResponse.bind(question),
                    data: question.formatRegionsData.bind(question),
                },
                placeholder: $translator.t('Все регионы')
            },
            event: {
                change: question.onRegionsChange.bind(question)
            },
            surveyQuestionCallback: question.regionsCallback.bind(question)
            ">
           
        </select>
        <!-- /ko -->
    </div>

    <!-- ko template: {
        foreach: templateIf(question.regionsValues().length, $data),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
    <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Районы')"></label>

        <button type="button" tabindex="10" class="btn-question" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно ограничить список адресов для выбора, указав регионы, районы, города и улицы')" ></button>

         <!-- ko ifnot: question.updating -->
        <select multiple="multiple" data-bind="selectedOptions: question.districtValues,
        disable: question.isFullBlocked,
            select2: {
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                templateResult: question.districtTemplateSelection.bind(question),
                templateSelection: question.districtInvertTemplateSelection.bind(question),
                minimumInputLength: 1,
                ajax: {
                    url: 'https://kladr-api.ru/api.php',
                    dataType: 'jsonp',
                    processResults: question.processDistrictsResponse.bind(question),
                    transport: question.transport,
                    data: question.formatDistrictsData.bind(question)
                },
                placeholder: $translator.t('Все районы')
            },
            event: { change: () => {} },
            surveyQuestionCallback: question.districtsCallback.bind(question)">
            <option></option>
        </select>
        <!-- /ko -->

    </div>
    <!-- /ko -->

    <!-- ko template: {
        foreach: templateIf(question.regionsValues().length, $data),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
    <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Города/населенные пункты')"></label>

        <button type="button" tabindex="10" class="btn-question" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно ограничить список адресов для выбора, указав регионы, районы, города и улицы')" ></button>

         <!-- ko ifnot: question.updating -->
        <select multiple="multiple" data-bind="selectedOptions: question.cityValues,
        disable: question.isFullBlocked,
        select2: {
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                templateResult: question.cityTemplateSelection.bind(question),
                templateSelection: question.cityTemplateSelection.bind(question),
                minimumInputLength: 2,
                ajax: {
                    url: 'https://kladr-api.ru/api.php',
                    dataType: 'jsonp',
                    processResults: question.processCitiesResponse.bind(question),
                    transport: question.transport,
                    data: question.formatCitiesData.bind(question),
                },
                placeholder: $translator.t('Все города/населенные пункты')
            },
            event: {
                change: question.onCitiesChange.bind(question)
            },
            surveyQuestionCallback: question.citiesCallback.bind(question)">
            <option></option>
        </select>
        <!-- /ko -->

    </div>
    <!-- /ko -->

    <!-- ko template: {
        foreach: templateIf(question.cityValues().length && parseInt(question.regionsValues()) > 1, $data),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
    <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Улицы')"></label>

        <button type="button" tabindex="10" class="btn-question" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно ограничить список адресов для выбора, указав регионы, районы, города и улицы')" title=""></button>

         <!-- ko ifnot: question.updating -->
        <select multiple="multiple" data-bind="selectedOptions: question.streetValues,
        disable: question.isFullBlocked,
            select2: {
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                templateResult: question.streetTemplateSelection.bind(question),
                templateSelection: question.streetTemplateSelection.bind(question),
                minimumInputLength: 2,
                ajax: {
                    url: 'https://kladr-api.ru/api.php',
                    dataType: 'jsonp',
                    processResults: question.processStreetsResponse.bind(question),
                    transport: question.transport,
                    data: question.formatStreetsData.bind(question)
                },
                placeholder: $translator.t('Все улицы')
            },
            surveyQuestionCallback: question.streetsCallback.bind(question)">
            <option></option>
        </select>
        <!-- /ko -->

    </div>
    <!-- /ko -->

    <div class="mb-4">
        <!-- ko template: {
            name: 'placeholder-template',
            data: {
                placeholder: question.placeholder,
                disabled:  question.isFullBlocked,
                translator: question.translator,
            }
        } -->
        <!-- /ko -->
    </div>

    <hr>
    <div class="pt-5p link-field-wrapper" data-bind="component: {
        name: 'link-controller-block',
        params: {
            controller: question.linkController,
            disabled: question.isFullBlocked || !(question.isPaidRate || question.isContactsEnabled),
        }
    }"></div>
    <!-- /ko -->
</template>
