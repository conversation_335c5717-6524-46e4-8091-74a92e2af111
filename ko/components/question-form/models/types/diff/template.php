<template id="question-form-template-diff">
  <!-- ko let: { $translator: question.translator } -->
  <div>
    <div class="f-fs-1 f-color-service mb-1">
      <span data-bind="text: $translator.t('В этом варианте вопроса можно выбрать только один ответ для каждой строки')"></span>
    </div>



    <div class="mt-4">
      <label class="form-label">

        <span data-bind="text: $translator.t('Форма кнопок')"></span>
        <button class="btn-question" data-bind="tooltip, tooltipText: $translator.t('Форма кнопок')" title=""></button>
      </label>

      <radio-group params="options: [
          { value: 'rect', label: $translator.t('Прямоугольник')},
          { value: 'circle', label: $translator.t('Круг') }
        ], value: question.shape, disabled: question.isFullBlocked"></radio-group>
    </div>

    <!-- ko template: {
      foreach: templateIf(question.shape() == 'circle', $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="mt-4">
      <div class="row mb-20p">
        <div class="col-6">
          <div>
            <label class="form-label">
              <span data-bind="text: $translator.t('Начальный цвет')"></span>
            </label>

            <color-picker params="value: question.startColor,  disabled: question.isFullBlocked"></color-picker>
          </div>
        </div>

        <div class="col-6">
          <div>
            <label class="form-label">
              <span data-bind="text: $translator.t('Конечный цвет')"></span>
            </label>

            <color-picker params="value: question.endColor, disabled: question.isFullBlocked"></color-picker>
          </div>
        </div>
      </div>

      <div class="d-flex align-items-center justify-content-center diff-scale">

        <!-- ko foreach: question.gradient -->

        <div class="diff-scale__item" data-bind="style: {
          color: $data,
        }, attr: { 'data-index': $index() + 1 }"></div>

        <!-- /ko -->



      </div>
    </div>


    <!-- /ko -->




    <diff-controller-block params="controller: question.diffController" data-bind="css: {
      'hide-points': question.shape() == 'circle'
    }"></diff-controller-block>

  </div>

  <!-- ko template: { name: 'gallery-question-gallery-template' } -->
  <!-- /ko -->

  <hr class="mx-0">

  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск оценки'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
       foreach: templateIf(question.skip(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Не готов(а) оценить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>

    <!-- /ko -->
  </div>

  <hr class="mx-0">

  <!-- Комментарий -->
  <div class="mt-4">
    <switch params="checked: question.commentEnabled, disabled: question.isFullBlocked">
      <span data-bind="text: $translator.t('Комментарий')"></span>
    </switch>

  </div>



  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div>
  <div class="row pb-4">
        <div class="col">
            <div class="f-check">
            <input
                type="checkbox"
                id="comment-required"
                class="f-check-input"
                data-bind="
                checked: question.commentIsRequired,
                disable: question.isFullBlocked,
                event: { change: function() { 
                    question.updateCommentRequired() } }
                "
            />
            <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
            </div>
        </div>
        </div>
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: $translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
    } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->

  <!-- /ko -->
</template>