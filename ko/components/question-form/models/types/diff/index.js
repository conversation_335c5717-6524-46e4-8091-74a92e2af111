import GalleryQuestion from '../gallery';
import { DIFF_QUESTION } from 'Data/question-types';
import { DiffController } from '../../../controllers/diff-controller';
import 'Components/input/color-picker';
import { doubleGradientCss } from '../../../../../utils/color/gradient';
import './style.less';
import { Translator } from '@/utils/translate';

class DiffQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true
    };

    super(controller, config);

    this.type = DIFF_QUESTION;

    this.galleryEnabled = ko.observable(false);
    this.commentIsRequired = ko.observable(null);

    this.diffController = new DiffController({}, this);

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    //this.enableShapeFeature = true;

    this.shape = ko.observable('rect');
    this.startColor = ko.observable('#73808D');
    this.endColor = ko.observable('#3F65F1');

    this.gradient = ko.computed(() => {
      let gr = doubleGradientCss({
        start: this.startColor(),
        end: this.endColor(),
        neutral: '#CFD8DC',
        count: 5
      });
      return gr;
    });

    [
      this.galleryEnabled,
      this.shape,
      this.startColor,
      this.endColor,
      this.diffController.onChange,
      this.skip,
      this.skipText,
      this.commentIsRequired
    ].forEach((f) =>
      this.subscriptions.push(f.subscribe((v) => this.onChange(true)))
    );
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  updateData(data) {
    data.gallery = data.diffGallery;
    data.galleryCommentEnabled = data.diffCommentEnabled;
    data.galleryCommentLabel = data.diffCommentLabel;
    data.galleryCommentLengthRange = data.diffCommentLengthRange;
    data.galleryCommentPlaceholder = data.diffCommentPlaceholder;
    data.galleryCommentRequired = data.diffCommentRequired;

    super.updateData(data);

    this.galleryEnabled(data.diffGalleryEnabled);

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.diffCommentRequired)
    }

    this.diffController.updateData(data.diffRows);

    this.startColor(data.startColor || '#73808D');
    this.endColor(data.endColor || '#3F65F1');

    let shape = data.shape;
    if (!['rect', 'circle'].includes(shape)) shape = 'rect';
    //if (!this.enableShapeFeature) shape = 'rect';
    this.shape(shape);

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
  }

  getData() {
    let data = super.getData();

    data.diffGalleryEnabled = this.galleryEnabled() ? 1 : 0;
    data.diffGallery = data.diffGalleryEnabled ? data.gallery : [];

    data.diffCommentEnabled = data.galleryCommentEnabled;
    data.diffCommentLabel = data.galleryCommentLabel;
    data.diffCommentLengthRange = data.galleryCommentLengthRange;
    data.diffCommentPlaceholder = data.galleryCommentPlaceholder;
    data.diffCommentRequired = data.galleryCommentRequired;

    data.diffRows = this.diffController.getData();

    data.startColor = this.startColor();
    data.endColor = this.endColor();
    data.shape = this.shape();

    data.skip = this.skip();
    data.skipText = this.skipText();

    return data;
  }

  isValid() {
    if (this.galleryEnabled()) {
      return super.isValid();
    }
    return this.defaultValidation();
  }
}

export default DiffQuestion;
