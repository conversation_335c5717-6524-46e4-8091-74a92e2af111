<!-- Блок шкалы для вариантов. Выбор варианта для оценки -->
<template id="question-form-distribution-scale-variants-list">
  <fc-label params="text: 'Варианты для оценки'"></fc-label>
  <fc-variants-list
    params="
      variantsList: question.variantsList,
      isBlocked: question.isBlocked,
      isFullBlocked: question.isFullBlocked,
      withPoints: question.withPoints,
      canRemoveSingleVariant: false,
      formControlErrorStateMatcher: question.formControlErrorStateMatcher,
      formControlSuccessStateMatcher: question.formControlSuccessStateMatcher
    "
  ></fc-variants-list>

  <!-- ko ifnot: question.isFullBlocked -->
    <button
      class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
      data-bind="
        click: () => question.variantsList.addVariant(),
        attr: { disabled: !question.isVariantsValid() }
      "
    >
      <span class="survey-question__variants-control-add-button-icon"></span>
      <span data-bind="text: question.translator.t('Добавить вариант')"></span>
    </button>
  <!-- /ko -->
</template>

<!-- Основной блок -->
<template id="question-form-template-distribution-scale">
  <div>
    <!-- ko template: { name: 'question-form-star-variants-list', } -->
    <!-- /ko -->

    <div class="mb-20p">
      <foquz-checkbox params="checked: question.randomOrder, disabled: question.isFullBlocked">
        <span data-bind="text: question.translator.t('Случайный порядок вариантов')"></span>
        <question-button
          params="text: question.translator.t('Варианты для каждого респондента при прохождении будут предложены в случайном порядке')"
        ></question-button>
      </foquz-checkbox>
    </div>
    <div class="mb-20p">
      <foquz-checkbox params="checked: question.requiredForAll, disabled: question.isFullBlocked">
        <span data-bind="text: question.translator.t('Оценка обязательна для всех вариантов')"></span>
        <question-button
          params="text: question.translator.t('Респоденту в прохождении нужно для каждого варианта обязательно поставить оценку')"
        ></question-button>
      </foquz-checkbox>
    </div>
  </div>

  <div class="row">
    <div class="col-8">
      <!-- Блок ввода минимального значения -->
      <div class="form-group">
        <label class="form-label">
          <span data-bind="text: $translator.t('Распределение единиц между вариантами')"></span>
        </label>
        <radio-group
          params="
            options: [
              { value: 0, label: 'С остатком' },
              { value: 1, label: 'Без остатка',}
            ],
            disabled: question.isFullBlocked,
            value: question.withoutRest
          "
        ></radio-group>
      </div>
    </div>
    <div class="col-4">
      <!-- Блок ввода максимального значения -->
      <div class="form-group">
        <label
          class="form-label"
          for="max-value"
        >
          <span data-bind="text: $translator.t('Кол-во единиц')"></span>
        </label>
        <fc-input
          params="
            mask: 'numbers',
            value: question.maxValue,
            disabled: question.isFullBlocked,
            invalid: question.formControlErrorStateMatcher(question.maxValue),
          "
        ></fc-input>
        <validation-feedback
          params="
            show: question.formControlErrorStateMatcher(question.maxValue),
            text: question.maxValue.error
          "
        ></validation-feedback>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group">
        <label class="form-label">
          <span data-bind="text: $translator.t('Текст для индикатора распределения')"></span>
        </label>
        <fc-input
          params="
            value: question.indicatorText,
            counter: true,
            maxlength: 125,
            disabled: question.isFullBlocked,
          "
        ></fc-input>
        <div class="form-hint">Если текст не нужен, оставьте поле пустым</div>
      </div>
    </div>
  </div>
  <hr class="mx-0 mt-0">
  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск оценки'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
     foreach: templateIf(question.skip(), $data),
     afterAdd: slideAfterAddFactory(400),
     beforeRemove: slideBeforeRemoveFactory(400)
    } -->
      <div class="form-group">
        <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
        <fc-input
          params="
            value: question.skipText,
            counter: true,
            maxlength: 125,
            placeholder: $translator.t('Затрудняюсь ответить'),
            disabled: question.isFullBlocked,
          "
        ></fc-input>
      </div>
    <!-- /ko -->
  </div>
  <hr class="mx-0">

  <!-- ko template: { name: 'gallery-question-gallery-template' } -->
  <!-- /ko -->

  <hr>
  <div class="mt-4">
    <switch
      params="
        checked: question.commentEnabled,
        disabled: question.isFullBlocked
      "
    >
      <span data-bind="text: $translator.t('Комментарий')"></span>
    </switch>
  </div>

  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200),
      disabled: question.isFullBlocked,

  } -->
    <div>
      <div class="row pb-4">
        <div class="col">
          <div class="f-check">
            <input
              type="checkbox"
              id="comment-required"
              class="f-check-input"
              data-bind="
                checked: question.commentIsRequired,
                disable: question.isFullBlocked,
                event: { change: function() { 
                  question.updateCommentRequired() } }
              "
            />
            <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
          </div>
        </div>
      </div>
      <div class="form-group">
        <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
        <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
      </div>

      <!-- ko component: {
        name: 'text-field',
        params: {
          controller: question.commentField,
          intervalText: $translator.t('Кол-во символов в комментарии'),
          disabled: question.isFullBlocked,
        }
      } -->
      <!-- /ko -->
    </div>
  <!-- /ko -->
</template>