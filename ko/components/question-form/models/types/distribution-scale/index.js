import { DISTRIBUTION_SCALE_QUESTION } from "Data/question-types";
import { Translator } from "@/utils/translate";
import GalleryQuestion from "../gallery";
import { VariantsListModel } from "Components/question-form/models/variants-list";

const QuestionTranslator = Translator("question");
const ValidationTranslator = Translator("validation");

class DistributionScaleQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    super(controller, config);
    this.type = DISTRIBUTION_SCALE_QUESTION;
    this.translator = QuestionTranslator;

    this.galleryEnabled = ko.observable(false);

    this.randomOrder = ko.observable(false);
    this.requiredForAll = ko.observable(false);
    this.withoutRest = ko.observable(0);
    this.indicatorText = ko.observable('Распределено');
    this.maxValue = ko.observable(100).extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
      },
    });

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");
    this.skipVariant = ko.observable(false);

    this.variantsList = VariantsListModel();



    this.usesDictionaryElements = ko.computed(() => {
      return !!this.variantsList.list().find(el => el.dictionaryElementId);
    }, this);

    this.dictionaryBadgeTooltipText = ko.computed(() => {
      if (!controller.question()) {
        return '';
      }
      return `
        ${controller.question().dictionaryElementId() ? `Связка вопроса с элементом ${controller.dictionary().name} справочника ${controller.dictionary().name}` : ''}
        ${controller.question().usesDictionaryElements() ? `${controller.question().dictionaryElementId() ? '.' : ''} В вопросе есть варианты, связанные с элементами справочника ${controller.dictionary().name}` : ''}
      `;
    }, this);

    this.commentIsRequired = ko.observable(null);

    [
      this.maxValue,
      this.galleryEnabled,
      this.skip,
      this.skipText,
      this.skipVariant,
      this.randomOrder,
      this.requiredForAll,
      this.withoutRest,
      this.indicatorText,
      this.commentIsRequired,
      this.variantsList.isChanged,
    ].forEach((f) => f.subscribe((v) => this.onChange()));
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  getLogicVariants(ids) {
    return this.variantsList
      .list()
      .filter((variant) => {
        return ids.find((id) => variant.id == id);
      })
      .map((variant) => variant.value);
  }

  addVariantsFromDictionary() {
    this.openDialog({
      name: "add-variants-list-dialog",
      params: {
        hasAnswers: parseInt(this.countAnswers) > 0,
        mode: 'dictionary',
        dictionary: this.controller.dictionary,
        checked: this.variantsList.list().filter(el => el.dictionaryElementId).map(el => `${el.dictionaryElementId}`),
        headerText: 'Добавление вариантов из справочника',
      },
      events: {
        submit: async (result) => {
          this.controller.dictionaryElements()
            .filter(el => result.newDetails.find(detail => detail == el.id))
            .forEach(el => {
              if (this.variantsList.list().find(variant => variant.dictionaryElementId == el.id)) {
                return;
              }
              this.variantsList.addExternalVariant(
                el.title,
                undefined,
                {
                  dictionary_element_id: el.id,
                }
              )
            });
        },
      },
    });
  }

  updateData(data) {
    const {
      variants,
      randomOrder,
      requiredForAll,
      withoutRest,
      indicatorText,
      maxValue,
      enableComment,
      commentLengthRange,
      placeholder,
      commentLabel,
      commentRequired,
      gallery,
      galleryEnabled,
    } = data;

    this.commentIsRequired(!!commentRequired)
    this.galleryEnabled(galleryEnabled);

    data.gallery = gallery;
    data.galleryCommentEnabled = enableComment;
    data.galleryCommentLengthRange = commentLengthRange;
    data.galleryCommentPlaceholder = placeholder;
    data.galleryCommentLabel = commentLabel;
    data.galleryCommentRequired = commentRequired;
    
    super.updateData(data);

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
    this.skipVariant(!!data.skipVariant);

    this.variantsList.update(variants);

    this.randomOrder(randomOrder);
    this.requiredForAll(requiredForAll);
    this.withoutRest(withoutRest);
    this.indicatorText(indicatorText);
    this.maxValue(maxValue);
  }


  getData() {
    let data = super.getData();
    data.comment = {
      ...this.commentField.getData(),
    };

    data.galleryEnabled = this.galleryEnabled() ? 1 : 0;

    data.randomOrder = this.randomOrder();
    data.requiredForAll = this.requiredForAll();
    data.withoutRest = this.withoutRest();
    data.indicatorText = this.indicatorText();

    data.maxValue = parseInt(this.maxValue());

    data.commentEnabled = data.galleryCommentEnabled;
    data.commentLabel = data.galleryCommentLabel;
    data.commentLengthRange = data.galleryCommentLengthRange;
    data.commentPlaceholder = data.galleryCommentPlaceholder;
    data.commentRequired = data.galleryCommentRequired;

    data.skip = this.skip();
    data.skipText = this.skipText();
    data.skipVariant = this.skipVariant();

    data = {
      ...data,
      variants: this.variantsList.getVariants(),
    };

    return data;
  }

  isVariantsValid() {
    return this.variantsList.isVariantsValid();
  }

  isValid() {
    if (!this.defaultValidation()) return false;
    if (!this.maxValue.isValid()) return false;
    if (this.galleryEnabled()) {
      if (!super.isValid()) return false;
    }

    return this.variantsList.isValid();
  }
}

export default DistributionScaleQuestion;
