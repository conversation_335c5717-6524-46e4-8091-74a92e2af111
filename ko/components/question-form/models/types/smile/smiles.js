import { Translator } from "@/utils/translate";
import { SMILES } from "@/constants/smiles";



const QuestionTranslator = Translator("question");



function getSet(fullSet, count) {
  const setLength = fullSet.length;

  if (count === 2) {
    if (setLength === 2) return [...fullSet];
    return [fullSet[0], fullSet[4]];
  }

  if (setLength < 5) return [];

  if (count === 3) {
    return [fullSet[0], fullSet[2], fullSet[4]];
  }
  return [...fullSet];
}

function getSmile(smileConfig) {
  const { id, icons, name } = smileConfig;

  const iconsCollection = {};

  icons.forEach((url, index) => {
    const iconName = `${id}-${index}`;
    iconsCollection[iconName] = {
      name: iconName,
      url,
      label: ko.observable(null),
      file: ko.observable(null),
    };
  });

  const iconsNames = Object.keys(iconsCollection);

  const sets = [
    {
      name: QuestionTranslator.t("Два")(),
      icons: getSet(iconsNames, 2),
    },
  ];

  if (icons.length >= 3) {
    sets.push({
      name: QuestionTranslator.t("Три")(),
      icons: getSet(iconsNames, 3),
    });
  }

  if (icons.length >= 5) {
    sets.push({
      name: QuestionTranslator.t("Пять")(),
      icons: getSet(iconsNames, 5),
    });
  }

  const config = {
    id,
    name: QuestionTranslator.t(name)(),
    icons: iconsCollection,
    preview: icons[icons.length - 1],
    sets,
    defaultSetIndex: sets.length === 1 ? 0 : Math.floor(sets.length / 2),
  };

  return config;
}

export function getSmiles() {
  const smiles = [];

  SMILES.forEach((smileConfig) => {
    smiles.push(getSmile(smileConfig));
  });

  return smiles;
}

export const CUSTOM_TYPE = 'custom';

export function getCustomSmiles() {
  return getSmile({
    id: CUSTOM_TYPE,
    name: "Загрузить свои",
    icons: Array(5)
      .fill(null)
      .map(() => {
        return ko.observable(null);
      }),
  })
}