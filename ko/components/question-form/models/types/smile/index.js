import GalleryQuestion from "../gallery";
import { SMILE_QUESTION } from "Data/question-types";
import { CUSTOM_TYPE, getCustomSmiles, getSmiles } from "./smiles";
import { getFilePreview } from "@/utils/file-loader/get-preview";

import "./style.less";
import "./components/clarifying-question"
import { AnswersLimit } from "../variants/answers-limit";

import { model, template } from "./components/custom-smile";
import { registerComponent } from "@/utils/engine/register-component";
import { RootUrl } from "../../../../../utils/url/root-url";
import { VariantsController } from "../../../controllers/variants-controller";
import { VariantsListModel } from "Components/question-form/models/variants-list";

import { Translator } from "@/utils/translate";

const QuestionTranslator = Translator("question");
const ValidationTranslator = Translator("validation");

registerComponent("custom-smile", { model, template });

const SMILE_TYPES = getSmiles();
const CUSTOM_SMILES = getCustomSmiles();
const DEFAULT_SMILE_TYPE = SMILE_TYPES[0].id;

class SmileQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };

    super(controller, config);

    this.type = SMILE_QUESTION;

    this.smileTypes = SMILE_TYPES;
    this.customSmiles = CUSTOM_SMILES;

    this.smileTypes.forEach((type) => {
      Object.values(type.icons).forEach((icon) => {
        icon.label.subscribe(() => this.onChange());
      });
    });

    Object.values(this.customSmiles.icons).forEach((icon) => {
      icon.url.subscribe(() => this.onChange());
      icon.label.subscribe(() => this.onChange());
    });

    this.smileType = ko.observable(null);
    this.isCustom = ko.computed(() => {
      return this.smileType() === CUSTOM_TYPE;
    });

    this.smileTypeConfig = ko.observable({});
    this.smileSets = ko.observableArray([]);

    this.lagestSetCount = ko.computed(() => {
      return this.smileSets().reduce((a, i) => Math.max(a, i.icons.length), 0);
    });

    this.smileSet = ko.observable(0);

    this.icons = ko.observable({});
    this.iconsOrder = ko.observableArray([]);

    this.smileType.subscribe((v) => {
      this.setSmileType(v);
    });

    this.smileError = ko.observable("");

    this.smilesError = ko.computed(() => {
      if (this.smileType() !== "custom") return null;
      const icons = Object.values(this.icons());
      const withoutFile = icons.some(
        (smile) => !smile.icon.file() && !smile.icon.url()
      );

      if (withoutFile)
        return "Необходимо загрузить изображения для всех смайлов";
      return null;
    });

    this.smilesState = {
      isValid: ko.computed(() => {
        return !this.smilesError();
      }),
    };

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    this.galleryEnabled = ko.observable(false);

    this.showLabels = ko.observable(false);

    this.showSmiles = ko.observable(true);

    this.commentIsRequired = ko.observable(null);

    this.smilesGallery = ko.computed(() => {
      const order = this.iconsOrder();
      const icons = this.icons();
      return order
        .map((icon) => {
          const data = icons[icon];
          if (!data) return false;
          const url = ko.toJS(data.icon.url);
          if (!url) return false;
          return { src: url };
        })
        .filter(Boolean);
    });
    
    this.clarifyingQuestionEnabled = ko.observable(false);
    this.clarifyingQuestionText = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => this.clarifyingQuestionEnabled(),
      },
    });
    this.enableFile = ko.observable(false);
    this.fromOne = ko.observable(true);
    this.clarifyingQuestionForRates = ko.observable([1, 2]);

    this.clarifyingQuestionForAllRates = ko.observable(true);
    this.startRate = ko.observable(1);
    this.endRate = ko.observable(2);
    this.smileSet.subscribe((v) => {
      const length = v?.icons?.length
      const max = length ? length : 1;
      this.endRate(max)
      let [start, end] = this.clarifyingQuestionForRates();
      this.clarifyingQuestionForRates([Math.min(start, max), Math.min(end, max)]);
      this.setSmileSet(v);
    });
    this.clarifyingQuestionForAllRates
      .subscribe(v => this.clarifyingQuestionForRates([this.startRate(), this.endRate()]));
    this.clarifyingQuestionForAllVariants = ko.observable(true);

    this.clarifyingQuestionIsRequired = ko.observable(true);

    this.variantsList = VariantsListModel({
      clarifyingQuestionEnabled: this.clarifyingQuestionEnabled,
      fromOne: this.fromOne,
    });

    this.clarifyingQuestionController = new VariantsController(
      {
        withCustomAnswer: true,
        withVariantsTypeSelect: true,
        withAddVariantButton: true,
        addFirstVariant: true,
        variantsType: "single",
        withTextAnswer: true,
        canUseFile: true,
        fileEnabled: this.enableFile,
        isClarifyingQuestion: true,
      },
      this
    );
    this.variantsType = this.clarifyingQuestionController.variantsType;
    
    this.answersCountLimit = AnswersLimit({
      required: this.required,
      list: this.clarifyingQuestionController.variants,
    });

    this.answersCountLimitMin = AnswersLimit({
      required: this.required,
      list: this.clarifyingQuestionController.variants,
      maxVal: this.answersCountLimit.value
    });

    [
      this.galleryEnabled,
      this.smileType,
      this.smileSet,
      this.skip,
      this.skipText,
      this.showLabels,
      this.commentIsRequired,
      this.clarifyingQuestionForRates,
      this.clarifyingQuestionIsRequired,
      this.clarifyingQuestionText,
      this.clarifyingQuestionController.variants,
      this.enableFile,
      this.answersCountLimitMin?.value,
      this.answersCountLimit?.value,
    ].forEach((f) => f.subscribe((v) => this.onChange()));

    this.commentEnabled.subscribe((v) => {
      if (v) {
        this.clarifyingQuestionEnabled(false)
      }
    })
    this.clarifyingQuestionEnabled.subscribe((v) => {
      if (v) {
        this.commentEnabled(false)
      }
    })
    this.clarifyingQuestionController.isChanged.subscribe((v) => this.onChange())

    this.smileType(DEFAULT_SMILE_TYPE);
    this.clarifyingQuestionEnabled = this.clarifyingQuestionEnabled.bind(this)
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  getSmileTypeConfig(type) {
    return type === CUSTOM_TYPE
      ? this.customSmiles
      : this.smileTypes.find((smile) => smile.id == type);
  }

  setSmileType(type) {
    if (!type) {
      type = DEFAULT_SMILE_TYPE;
      this.smileType(v);
      return;
    }

    if (type !== CUSTOM_TYPE) {
      Object.values(this.customSmiles.icons).forEach((icon) => {
        icon.label("");
        icon.file("");
        icon.url("");
      });
    }

    const config = this.getSmileTypeConfig(type);
    this.smileTypeConfig = config;

    this.smileSets(config.sets);
    let defaultSetIndex = 0;
    if (config.defaultSetIndex) defaultSetIndex = config.defaultSetIndex;

    const defaultSet = config.sets[defaultSetIndex];
    this.smileSet(defaultSet);
  }

  setSmileSet(set) {
    let smileIcons = this.smileTypeConfig.icons;

    const icons = {};

    set.icons.forEach((iconId) => {
      const iconData = smileIcons[iconId];
      icons[iconId] = {
        id: 0,
        icon: iconData,
      };
    });

    this.icons(icons);
    this.iconsOrder([...set.icons]);
  }

  getLogicVariants(variants) {
    let _variants = [...variants];
    _variants.sort();

    let icons = this.icons();
    let iconsOrder = this.iconsOrder();

    return _variants
      .map((v) => {
        let iconName = iconsOrder[v - 1];
        const iconData = icons[iconName];

        if (!iconData) return false;

        return {
          url: ko.toJS(iconData.icon.url),
          name: iconData.icon.name,
          label: iconData.icon.label(),
        };
      })
      .filter(Boolean);
  }

  updateData(data) {
    const { smileType } = data;
    data.gallery = data.smileGallery;
    data.galleryCommentEnabled = data.smileCommentEnabled;
    data.galleryCommentLengthRange = data.smileCommentLengthRange;
    data.galleryCommentPlaceholder = data.smileCommentPlaceholder;
    data.galleryCommentLabel = data.smileCommentLabel
    data.galleryCommentRequired = data.smileCommenRequired

    super.updateData(data);

    this.galleryEnabled(data.smileGalleryEnabled);

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.smileCommentRequired)
    }

    this.smileType(smileType);

    let set = this.smileSets().find((s) => {
      return s.icons.length == data.smilesCount;
    });

    if (set) {
      this.smileSet(set);

      this.iconsOrder().forEach((name, index) => {
        let smileData = data.smiles[index];
        const icon = this.icons()[name];

        if (smileData && icon) {
          icon.icon.label(smileData.label);
          icon.id = smileData.id || 0;

          if (data.smileType === "custom") {
            icon.icon.url(RootUrl(smileData.smile_url));
          }
        }
      });
    }

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");

    this.showLabels(!!data.showLabels);

    if (data.clarifyingQuestion.enabled) {
      let clarifyingQuestion = data.clarifyingQuestion
      this.clarifyingQuestionEnabled(data.clarifyingQuestion.enabled)
      this.clarifyingQuestionText(data.clarifyingQuestion.text)
      this.clarifyingQuestionForAllRates(data.clarifyingQuestion.forAllRates)
      this.enableFile(!!data.clarifyingQuestion.enableFile)
      this.clarifyingQuestionIsRequired(data.clarifyingQuestion.clarifyingQuestionIsRequired)
      this.answersCountLimitMin.value(data.clarifyingQuestion.minСhooseVariants)
      this.answersCountLimit.value(data.clarifyingQuestion.maxСhooseVariants)
      if (!data.clarifyingQuestion.forAllRates) {
        this.clarifyingQuestionForRates(data.clarifyingQuestion.forRates)
      }

      this.clarifyingQuestionController.updateData({
        type: clarifyingQuestion.variantsType,
        variants: clarifyingQuestion.variants,
        customAnswerEnabled: clarifyingQuestion.customAnswerEnabled,
        customAnswerRange: clarifyingQuestion.customAnswerLengthRange,
        customAnswerPlaceholder: clarifyingQuestion.customAnswerPlaceholder,
        customAnswerLabel: clarifyingQuestion.customAnswerLabel,
        selfVariantFile: data.selfVariantFile
      });
    }
  }

  getData() {
    let data = super.getData();
    data.smileGalleryEnabled = this.galleryEnabled() ? 1 : 0;
    data.smileGallery = data.smileGalleryEnabled ? data.gallery : [];
    data.smileCommentEnabled = data.galleryCommentEnabled;
    data.smileCommentLabel = data.galleryCommentLabel;
    data.smileCommentLengthRange = data.galleryCommentLengthRange;
    data.smileCommentPlaceholder = data.galleryCommentPlaceholder;
    data.smileCommentRequired = this.commentIsRequired();
    
    data.clarifyingQuestion = {
      minСhooseVariants: this.answersCountLimitMin.value(),
      maxСhooseVariants: this.answersCountLimit.value(),
      enabled: this.clarifyingQuestionEnabled(),
      required: this.clarifyingQuestionIsRequired(),
      forAllRates: this.clarifyingQuestionForAllRates(),
      forRates: this.clarifyingQuestionForRates(),
      text: this.clarifyingQuestionText(),
      enableFile: this.enableFile(),

      ...this.clarifyingQuestionController.getData(),
    };

    data.smileType = this.smileType();
    data.smilesCount = this.icons().length;

    const smileFiles = {};

    const iconsData = this.icons();
    const icons = this.iconsOrder().map((name) => iconsData[name]);

    if (this.smileType() === "custom") {
      icons.forEach((i) => {
        const { name, file } = i.icon;
        smileFiles[name] = file();
      });
      data.smileFiles = Object.values(smileFiles);
    }

    data.smiles = icons.map((i) => {
      const { url, file, label } = i.icon;

      let data = {
        url: file() ? "" : ko.toJS(url),
        previewUrl: ko.toJS(url),
        label: label(),
      };
      if (i.id && this.smileType() !== "custom") data.id = i.id;
      return data;
    });

    data.skip = this.skip();
    data.skipText = this.skipText();
    
    data.showLabels = this.showLabels();

    return data;
  }

  setCustomSmile(smile, file) {
    if (file) {
      smile.icon.file(file);
      getFilePreview(file).then((url) => {
        smile.icon.url(url);
      });
    } else {
      smile.icon.file(null);
      smile.icon.url("");
    }
  }

  setSmileError(err) {
    this.smileError(err);
    clearTimeout(this._smileErrorTimer);
    this._smileErrorTimer = setTimeout(() => {
      this.smileError("");
    }, 3000);
  }

  onSort(e) {
    const { oldIndex, newIndex } = e;

    const { sets } = this.customSmiles;

    const currentCount = this.iconsOrder().length;
    const set = sets.find((s) => {
      return s.icons.length == currentCount;
    });

    const { icons } = set;

    const element = icons[oldIndex];
    icons.splice(oldIndex, 1);
    icons.splice(newIndex, 0, element);

    this.iconsOrder(icons);

    this.showSmiles(false);
    this.showSmiles(true);
  }

  isValid() {
    let isClarifyingQuestionValid = true;
    let smilesStateValid = true;
    let galleryValid = true;
    let defaultValid = true;

    if (this.clarifyingQuestionEnabled()) {
      isClarifyingQuestionValid = this.clarifyingQuestionController.isValid()
    }

    smilesStateValid = this.smilesState.isValid()

    if (this.galleryEnabled()) {
      galleryValid = super.isValid()
    }

    defaultValid = this.defaultValidation();
    return isClarifyingQuestionValid && smilesStateValid && galleryValid && defaultValid;
  }

  smileTypeTemplate(state) {
    if (!state.id) return state.text;

    const img = state.element.dataset.img;

    const $icon = $("<img width='24'>").attr("src", img).addClass("mr-10p");

    let span = $("<span>")
      .text(state.text)
      .addClass("d-flex align-items-center");

    span.prepend($icon);

    if (state.element.hasAttribute("data-custom")) {
      span.addClass("custom-option");
    }

    return span;
  }
}

export default SmileQuestion;
