<template id="question-form-template-smile">
  <!-- ko let: { $translator: question.translator } -->
  <div class="">
    <div class="smile-form">
      <div class="smile-form__type mb-4">

        <label class="form-label">
          <span data-bind="text: $translator.t('Вид смайлов')"></span>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title=""></button>
        </label>

        <div class="select2-wrapper" data-bind="click: function() {
          if (question.isFullBlocked) return false;
          if (question.isBlocked()) question.tryChangeBlockedParam();
          else return true;
        }">
          <select data-bind="value: question.smileType,
            valueAllowUnset: true,
            disable: question.isBlocked() || question.isFullBlocked,
            lazySelect2: {
              containerCssClass: 'form-control',
              wrapperCssClass: 'select2-container--form-control smile-type-select',
              templateResult: question.smileTypeTemplate,
              templateSelection: question.smileTypeTemplate,
            }">
            <option data-bind="value: question.customSmiles.id, text: question.customSmiles.name" data-img="/img/icons/upload.png" data-custom></option>

            <!-- ko foreach: question.smileTypes -->
            <option data-bind="value: $data.id, text: $data.name, attr: { 
              'data-img': $data.preview
            }"></option>
            <!-- /ko -->

          </select>
        </div>

      </div>

      <!-- ko if: question.smileSets().length > 1 -->
      <div class="smile-form__sets mb-4">
        <label class="form-label">
          <span data-bind="text: $translator.t('Количество')"></span>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title=""></button>
        </label>
        <div>

          <div class="f-btn-group" data-bind="click: function() {
              if (question.isBlocked()) question.tryChangeBlockedParam();
              else return true;
            }">
            <!-- ko foreach: question.smileSets -->
            <button class="f-btn" data-bind="
            text: $data.name,
            click: function() {
              question.smileSet($data);
            },
            disable: question.isBlocked() || question.isFullBlocked,
            css: {
              'active': question.smileSet() == $data
            }"></button>
            <!-- /ko -->
          </div>


        </div>
      </div>
      <!-- /ko -->

      <div class="smile-form__labels mb-4">
        <label class="form-label">
          <span data-bind="text: $translator.t('Метки')"></span>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title=""></button>
        </label>
        <!-- ko if: question.showSmiles -->
        <div class="smile-form__labels-row" data-bind="sortable,
          sortableItem: '.smile-form__label',
          sortableHandle: '.smile-form__handle',
          onSort: function(e) { question.onSort(e) }">
          <!-- ko foreach: {data: question.iconsOrder, as: 'iconName'} -->
          <div class="smile-form__label" data-bind="style: {
           'width': (100 / question.lagestSetCount()) + '%'
          }, let: { icon: question.icons()[iconName] } ">

            <!-- ko if: icon -->
            <div data-bind="log">
              <!-- ko if: question.smileType() === 'custom' -->
              <custom-smile params="
                    url: icon.icon.url,
                    index: $index(),
                    gallery: question.smilesGallery,
                    onChange: function(file) {
                      question.setCustomSmile(icon, file);
                    },
                    onError: function(err) {
                      question.setSmileError(err);
                    }
                  " class="mb-15p"></custom-smile>
              <!-- /ko -->
              <input type="text" class="form-control" data-bind="textInput: icon.icon.label, disable: question.isFullBlocked" maxlength="150">
              <!-- ko ifnot: question.smileType() === 'custom' -->
              <div class="text-center">
                <img data-bind="attr: {
                  src: icon.icon.url
                }" alt="" class="mt-2" width="33">
              </div>
              <!-- /ko -->
            </div>

            <!-- ko if: question.smileType() === 'custom' -->
            <div class="smile-form__handle d-flex justify-content-center cursor-pointer mt-15p">
              <fc-icon params="width: 24, height: 12, name: 'drag-arrow-horizontal'"></fc-icon>
            </div>
            <!-- /ko -->
            <!-- /ko -->

          </div>
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <fc-error class="mt-3" params="show:question.smileError, text: question.smileError">
        </fc-error>

        <fc-error class="mt-3" params="show: question.formControlErrorStateMatcher(question.smilesState), text: question.smilesError">
        </fc-error>

      </div>


    </div>

    <div class="smile-config">
      <div class="form-group">
        <fc-check params="
          checked: question.showLabels,
          label: 'Всегда отображать метки', 
          hint: 'Всегда отображать метки', disabled: question.isFullBlocked"></fc-check>
      </div>
    </div>
  </div>

  <hr class="mx-0">

  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск оценки'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
       foreach: templateIf(question.skip(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Не готов(а) оценить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>

    <!-- /ko -->
  </div>

  <hr class="mx-0">


  <!-- ko template: { name: 'gallery-question-gallery-template' } -->
  <!-- /ko -->

  <hr>

  <div class="row clarifying-answer-settings__switchers">
    <div class="col">
      <div class="pt-1">
        <switch class="mb-0" params="
            checked: question.clarifyingQuestionEnabled,
            disabled: question.isBlocked() || question.isFullBlocked
          " data-bind="
            click: function() {
              if (question.isFullBlocked) return false;
              if (question.isBlocked()) question.tryChangeBlockedParam();
              else return true;
            }
          ">
          <span data-bind="text: $translator.t('Уточняющий вопрос')"></span>
        </switch>
      </div>
    </div>
    <div class="col">
      <switch
        class="mb-0"
        params="
          checked: question.commentEnabled,
          disabled: question.isBlocked() || question.isFullBlocked"
          data-bind="
            click: () => {
              if (question.isFullBlocked) return false;
              if (question.isBlocked()) question.tryChangeBlockedParam();
              else return true;
            }
          "
      >
        <!-- ko text: $translator.t('Комментарий') -->
      </switch>
    </div>
  </div>

  <!-- ko template: {
    foreach: question.clarifyingQuestionEnabled,
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
    <smile-clarifying-question
      params="
        clarifyingQuestionVariant: question,
        question: question,
        formControlErrorStateMatcher: question.formControlErrorStateMatcher,
        formControlSuccessStateMatcher: question.formControlSuccessStateMatcher,
      "
    ></smile-clarifying-question>
  <!-- /ko -->

  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div>
      <div class="row pb-4">
            <div class="col">
            <div class="f-check">
                <input
                type="checkbox"
                id="comment-required"
                class="f-check-input"
                data-bind="
                    checked: question.commentIsRequired,
                    disable: question.isFullBlocked,
                    event: { change: function() { 
                    question.updateCommentRequired() } }
                "
                />
                <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
            </div>
            </div>
        </div>
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: $translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
        } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->

  <!-- /ko -->
</template>