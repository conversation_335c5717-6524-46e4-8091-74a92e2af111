import { FComponent } from "@/components/f-component";
import html from "./template.html";



export class ViewModel extends FComponent {
  constructor(params, element) {
    super(params, element);

    const { url, onChange, onError, gallery, index } = params;

    element.classList.add('fc-custom-smile');

    console.log({ params })

    this.gallery = gallery;
    this.index = index;
    this.onChange = onChange;
    this.onError = onError;
    this.url = url;
    this.input = ko.observable(null);
  }

  remove() {
    this.onChange(null);
  }

  selectFile() {
    this.input().click();
  }

  onSelect(e) {
    const files = e.target.files;
    const [file] = files;
    if (!file) {
      this.onChange(null);
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      console.log('size error')
      this.onError('Файл слишком большой. Максимальный размер изображения — 5 мб');
      return;
    }

    // check size
    console.log({ file });

    this.onChange(file);
  }
}

export const model = ViewModel;
export const template = html;
