<div class="fc-custom-smile__wrapper">
  <!-- ko if: url -->
  <div class="fc-custom-smile__view">
    <div
      class="fc-custom-smile__img"
      data-bind="fancyboxGalleryItem: {
        gallery: function() { return gallery() },
        index: index
      }"
    >
      <img
        data-bind="attr: {
            src: url
          }"
        alt=""
        class="mt-2"
      />
    </div>
  </div>
  <button
    data-bind="click: function() {remove()}"
    class="fc-custom-smile__remove"
  >
    <fc-icon params="size: 16, name: 'times-bold'"></fc-icon>
  </button>
  <!-- /ko -->

  <!-- ko ifnot: url -->

  <button
    type="button"
    class="fc-custom-smile__button"
    data-bind="click: function() {
    selectFile()
  }"
  >
    <fc-icon params="size: 20, name: 'clip-bold'" class="mb-2"></fc-icon>
    svg, png, gif, jpg, jpeg
    <br />
    изображение
  </button>

  <input
    type="file"
    accept=".svg, .png, .gif, .jpg, .jpeg"
    data-bind="element: input, event: {
      change: function(_, e) {
        onSelect(e);
      }
    }"
  />

  <!-- /ko -->
</div>
