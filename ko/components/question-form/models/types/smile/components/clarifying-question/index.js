import html from './template.html';

class ViewModel {
  constructor({ clarifyingQuestionVariant: question, question: allQuestion, formControlErrorStateMatcher, formControlSuccessStateMatcher }) {    
    this.isFullBlocked = allQuestion.isFullBlocked;
    this.startRate = allQuestion.startRate;
    this.endRate = allQuestion.endRate;
    this.formControlErrorStateMatcher = formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher = formControlSuccessStateMatcher;

    this.question = question;
    this.allQuestion = allQuestion;
    this.controller = null;

    this.questionFormVariantsListParams = {
      controller: allQuestion.clarifyingQuestionController,
      question: allQuestion,
    }
  }
}

ko.components.register('smile-clarifying-question', {
  viewModel: ViewModel,
  template: html
});
