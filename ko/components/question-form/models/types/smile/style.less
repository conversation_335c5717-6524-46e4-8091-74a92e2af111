.fc-custom-smile {
  display: block;

  &__wrapper {
    width: 105px;
    height: 105px;
    position: relative;
    margin: auto;
  }

  &__view {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-width: 54px;
      max-height: 54px;
    }
  }

  &__remove, &__button {
    border: none;
    outline: none;
    box-shadow: none;
    padding: 0;
  }

  &__remove {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: black;
    color: white;
    font-size: 0;
  }

  &__button {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #eceff1;
    color: #73808d;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
    padding: 0 10px;
  }

  input[type="file"] {
    display: none;
  }
}


.smile-type-select {
  .select2-results {
    &__option:first-child {
      position: relative;
      margin-bottom: 20px;
    
      &:after {
        content: '';
        position: absolute;
        left: 20px;
        right: 25px;
        bottom: -10px;
        border-bottom: 1px solid #E7EBED;
      }
      
    }
  }
}