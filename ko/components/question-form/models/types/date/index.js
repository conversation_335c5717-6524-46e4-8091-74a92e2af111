import Question from '../../question';
import { LinkController } from '../../../controllers/link-controller';
import { DATE_QUESTION } from 'Data/question-types';
import { timeValidator } from 'Utils/validation/time';
import { dateValidator } from 'Utils/validation/date';
import { DateMonth } from 'Models/date-month';
import 'Components/input/date-month';
import {
  formatClientDateToServer,
  formatServerDateToClient
} from 'Utils/date/format';
import { Translator } from '@/utils/translate';

import "./style.less";

const ValidationTranslator = Translator('validation')


const DATE_TYPE_ONLY_DATE = '0';
const DATE_TYPE_ONLY_TIME = '1';
const DATE_TYPE_DATE_AND_TIME = '2';

class DateQuestion extends Question {
  constructor(controller, config) {
    super(controller, config);

    this.type = DATE_QUESTION;

    this.dateType = ko.observable(DATE_TYPE_ONLY_DATE);
    this.dateTypes = [
      {
        id: DATE_TYPE_ONLY_DATE,
        icon: 'date',
        label: this.translator.t('Только дата')()
      },
      {
        id: DATE_TYPE_ONLY_TIME,
        icon: 'time',
        label: this.translator.t('Только время')()
      },
      {
        id: DATE_TYPE_DATE_AND_TIME,
        icon: 'datetime',
        label: this.translator.t('Дата и время')()
      }
    ];

    this.linkController = new LinkController(this);

    this.onlyDateMonth = ko.observable(false);

    this.rightAnswer = {
      points: ko.observable(''),
      date: ko.observable(''),
      time: ko.observable(''),
    };

    this.isDateRequired = ko.pureComputed(() => {
      if (!this.withPoints) return false;
      if (this.dateType() == DATE_TYPE_ONLY_TIME) return false;
      if (this.rightAnswer.time()) return true;
      return false;
    });

    this.rightAnswer.dateMonth = new DateMonth({
      required: this.isDateRequired,
      allowEmptyMonth: true
    })

    this.dateType.subscribe((v) => {
      this.rightAnswer.date('');
      this.rightAnswer.time('');
      this.rightAnswer.dateMonth.reset();
    });


    this.isTimeRequired = ko.pureComputed(() => {
      if (!this.withPoints) return false;
      if (this.dateType() == DATE_TYPE_ONLY_DATE) return false;
      if (this.onlyDateMonth()) return this.rightAnswer.dateMonth.hasValue();
      return this.rightAnswer.date();
    });

    this.rightAnswer.date.extend({
      required: {
        message: () => ValidationTranslator.t('Обязательное поле')(),
        onlyIf: () => {
          if (this.onlyDateMonth()) return false;
          return this.isDateRequired();
        }
      },
      validation: {
        validator: dateValidator({ format: 'DD.MM.YYYY' }),
        message: () => ValidationTranslator.t('Некорректный формат')(),
        onlyIf: () => {
          if (!this.withPoints) return false;
          if (this.dateType() == DATE_TYPE_ONLY_TIME) return false;
          if (this.onlyDateMonth()) return false;
          return true;
        }
      }
    });

    this.maxPointsCalcMethod = ko.observable(false);

    this.rightAnswer.time.extend({
      required: {
        message: () => ValidationTranslator.t('Обязательное поле')(),
        onlyIf: () => {
          return this.isTimeRequired()
        }
      },
      validation: {
        validator: timeValidator(),
        message: () => ValidationTranslator.t('Некорректный формат')(),
        onlyIf: () => {
          if (!this.withPoints) return false;
          if (this.dateType() == DATE_TYPE_ONLY_DATE) return false;
          return true;
        }
      }
    });

    [
      this.dateType,
      this.onlyDateMonth,
      this.rightAnswer.points,
      this.rightAnswer.date,
      this.rightAnswer.time,
      this.rightAnswer.dateMonth.value,
      this.maxPointsCalcMethod
    ].forEach((field) => field.subscribe((v) => this.onChange()));
  }

  updateData(data) {
    super.updateData(data);
    this.dateType('' + data.dateType);

    this.linkController.updateData(data);

    this.onlyDateMonth(data.onlyDateMonth);
    this.maxPointsCalcMethod(data.maxPointsCalcMethod)

    if (this.withPoints) {
      let rightAnswer = data.dateRightAnswer;
      if (rightAnswer) {
        if (rightAnswer.points == null) this.rightAnswer.points('');
        else this.rightAnswer.points(rightAnswer.points);

        if (data.onlyDateMonth) {
          this.rightAnswer.dateMonth.setData(rightAnswer.date);
        } else {
          this.rightAnswer.date(formatServerDateToClient(rightAnswer.date));
        }

        this.rightAnswer.time(rightAnswer.time || '');
      }
    }
  }

  getData() {
    let data = super.getData();

    data.dateType = parseInt(this.dateType());
    data.onlyDateMonth = this.onlyDateMonth();

    let rightDate;

    if (data.onlyDateMonth) {
      rightDate = this.rightAnswer.dateMonth.value();
    } else {
      rightDate = formatClientDateToServer(this.rightAnswer.date());
    }

    let rightAnswer = {
      points: this.rightAnswer.points(),
      answer: {
        date: rightDate,
        time: this.rightAnswer.time()
      }
    };

    data = {
      ...data,
      ...this.linkController.getData(),
      dateRightAnswer: rightAnswer,
      maxPointsCalcMethod: this.maxPointsCalcMethod()
    };

    return data;
  }

  isValid() {
    if (!this.rightAnswer.time.isValid()) return false;

    if (this.onlyDateMonth()) {
      if (!this.rightAnswer.dateMonth.isValid()) return false;
    } else {
      if (!this.rightAnswer.date.isValid()) return false;
    }

    return super.isValid();
  }
}

export default DateQuestion;
