<template id="question-form-template-date">
  <!-- ko let: {$translator: question.translator} -->
  <div class="hat-radio-group hat-radio-group--dense survey-question__variants-type-selector mb-4">
    <!-- ko foreach: question.dateTypes -->
    <div class="hat-radio-group__radio" data-bind="let: { inputId: 'survey-question-variants-type-selector-' + id }">
      <input
        class="hat-radio-group__radio-input"
        type="radio"
        name="date-type"
        data-bind="
          checked: question.dateType,
          value: id,
          attr: {
            id: inputId,
            disabled: question.isFullBlocked === true,
          },
      " />
      <label class="hat-radio-group__radio-label" data-bind="attr: { for: inputId },
      click: function() {
        if (question.isBlocked() && !$parent.controller.isBlocked) question.tryChangeBlockedParam();
        else return true;
      }">
        <i class="survey-question__variants-type-selector-value-icon" data-bind="class: 'survey-question__variants-type-selector-value-' + icon+ '-icon'"></i>
        <!-- ko text: label -->
        <!-- /ko -->
      </label>
    </div>
    <!-- /ko -->
  </div>

  <!-- ko if: question.dateType() == 0 || question.dateType() == 2 -->
  <div class="mb-4">
    <foquz-checkbox
      params="
        checked: question.onlyDateMonth,
        disabled: question.isFullBlocked,
      "
      data-bind="
        click: function() {
          if (question.isBlocked() && !controller.isBlocked) {
            question.tryChangeBlockedParam();
          } else { 
            return true;
          }
        },
      "
    >
      <span data-bind="text: $translator.t('Только день и месяц')"></span>
    </foquz-checkbox>
  </div>
  <!-- /ko -->

  <!-- ko if: question.withPoints -->
  <div class="row">
    <div class="col-auto">
      <div class="form-group">
        <label class="form-label"><span data-bind="text: $translator.t( 'Баллов за правильный ответ')"></span></label>
        <input type="text" style="width: 250px" class="form-control text-center" placeholder="" maxlength="9" data-bind="onlyNumbers: { sign: true }, textInput: question.rightAnswer.points, attr: {
          placeholder: $translator.t('0 баллов')
        }, disable: question.isFullBlocked">
      </div>
    </div>
    <div
      class="col-auto question-date__right-answer"
      data-bind="
        css: {
          'question-date__right-answer--blocked': question.isFullBlocked,
        },
      "
    >
      <div class="form-group">
        <label class="form-label">
          <span data-bind="text: $translator.t( 'Правильный ответ')"></span>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: $translator.t( 'Правильный ответ')" title=""></button>
        </label>

        <div class="d-flex">
          <!-- ko if: question.dateType() == 0 || question.dateType() == 2 -->

          <!-- ko if: question.onlyDateMonth -->
          <date-month params="model: question.rightAnswer.dateMonth, formControlErrorStateMatcher: question.formControlErrorStateMatcher, disabled: question.isFullBlocked" class="mr-15p"></date-month>
          <!-- /ko -->

          <!-- ko ifnot: question.onlyDateMonth -->
          <div style="width: 185px">
            <date-picker
              class="mr-3"
              params="
                value: question.rightAnswer.date,
                allowClear: true,
                disabled: question.isFullBlocked,
              "
              data-bind="
                css: {
                  'is-invalid': question.formControlErrorStateMatcher(question.rightAnswer.date),
                },
              "
            ></date-picker>
            <validation-feedback params="show: question.formControlErrorStateMatcher(question.rightAnswer.date), text: question.rightAnswer.date.error"></validation-feedback>
          </div>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko if: question.dateType() == 1 || question.dateType() == 2 -->
          <div>
            <input type="text" class="form-control text-center" data-bind="timeInput, textInput: question.rightAnswer.time, css: {
              'is-invalid': question.formControlErrorStateMatcher(question.rightAnswer.time)
            }, disable: question.isFullBlocked">

            <validation-feedback params="show: question.formControlErrorStateMatcher(question.rightAnswer.time), text: question.rightAnswer.time.error"></validation-feedback>
          </div>
          <!-- /ko -->
        </div>
      </div>
    </div>
  </div>
  <!-- ko if: question.withPoints -->
  <div class="mb-30p">
        <foquz-checkbox params="checked: question.maxPointsCalcMethod, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Учитывать в итоговой сумме баллов, если вопрос не отображался для респондента')"></span>
            <question-button params="text: question.translator.t('Настройки отображения вопроса можно настроить в логике опроса. По умолчанию все скрытые логикой отображения вопросы учитываются в итоговом подсчете баллов')">
            </question-button>
        </foquz-checkbox>
    </div>
    <!-- /ko -->
  <!-- /ko -->

  <hr class="mt-0">
  <div class="pt-5p link-field-wrapper" data-bind="component: {
      name: 'link-controller-block',
      params: {
          controller: question.linkController,
          disabled: question.isFullBlocked || !(question.isPaidRate || question.isContactsEnabled),
      }
  }"></div>
  <!-- /ko -->
</template>
