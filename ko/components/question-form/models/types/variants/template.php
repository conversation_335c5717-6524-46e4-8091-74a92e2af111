<template id="question-form-variants-list">
    <!-- ko if: question.withPoints -->
    <div class="without-points-info-text">Введите баллы для каждого варианта ответа. Чтобы вариант ответа не участвовал в итоговом подсчете баллов, оставьте в поле знак минус «-» (без кавычек).</div>
    <!-- /ko -->
    <!-- ko if: question.useTooltips -->
    <div class="mb-10p mt-15p">
    <fc-button params="size: 'auto', mode: 'text', label: question.showTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок', color: 'primary'" data-bind="click: function() {
                question.showTooltips(!question.showTooltips())
            }"></fc-button>
    </div>
    <!-- /ko -->
    <fc-variants-list
        params="
            variantsList: question.variantsList,
            isBlocked: false,
            isFullBlocked: question.isFullBlocked,
            randomOrder: question.randomOrder,
            withPoints: question.withPoints,
            enableFile: question.enableFile,
            canRemoveSingleVariant: false,
            formControlErrorStateMatcher: question.formControlErrorStateMatcher,
            formControlSuccessStateMatcher: question.formControlSuccessStateMatcher,
            showTooltips: question.showTooltips,
            useTooltips: question.useTooltips,
            tooltipPlaceholder: 'Подсказка для варианта ответа',
            onRemove: function(v, cb) {
                question.removeVariant(v, cb)
            }
        "
    ></fc-variants-list>

    <!-- ko if: question.useTooltips -->
    <div class="mb-10p mt-15p">
    <fc-button params="size: 'auto', mode: 'text', label: question.showTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок', color: 'primary'" data-bind="click: function() {
                question.showTooltips(!question.showTooltips())
            }"></fc-button>
    </div>
    <!-- /ko -->
    <!-- ko ifnot: question.isFullBlocked -->
    <div class="row">
        <div class="col-6">
            <!-- ko ifnot: question.enableFile -->
            <button
                class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
                data-bind="
                    click: function() {
                        question.variantsList.addVariant(undefined, undefined, true);
                    },
                    attr: {
                        disabled: !question.variantsList.isValid(),
                    },
                "
            >
                <span class="survey-question__variants-control-add-button-icon"></span>
                <span data-bind="text: question.translator.t('Добавить вариант')"></span>
            </button>
            <!-- /ko -->
            <!-- ko if: question.enableFile -->
            <button
                class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
                data-bind="
                    click: function() {
                        question.variantsList.addVariant(undefined, undefined, true);
                    },
                    attr: {
                        disabled: !question.variantsList.isValidWithFile(),
                    },
                "
            >
                <span class="survey-question__variants-control-add-button-icon"></span>
                <span data-bind="text: question.translator.t('Добавить вариант')"></span>
            </button>
            <!-- /ko -->
        </div>
        <div class="col-6">
            <!-- ko ifnot: question.enableFile -->
            <button
                class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
                data-bind="
                    click: function() {
                        question.addVariantsFromList();
                    },
                "
            >
                <span class="survey-question__variants-control-add-button-icon"></span>
                <span data-bind="text: question.translator.t('Добавить списком')"></span>
            </button>
            <!-- /ko -->
            <!-- ko if: question.enableFile -->
            <button
                class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
                data-bind="
                    click: function() {
                        question.loader.open()
                    },
                "
            >
                <span class="survey-question__variants-control-add-button-icon"></span>
                <span data-bind="text: question.translator.t('Загрузить файлы')"></span>
            </button>
            <!-- /ko -->
        </div>
    </div>
    <!-- /ko -->

    <switch
        class="mb-4"
        params="
            checked: question.customAnswerEnabled,
            disabled: question.isFullBlocked,
        "
    >
        <div for="rate-clarifying-question-custom-answer" class="f-check-label" data-bind="text: question.translator.t('Свой вариант (произвольное поле)')"></div>
    </switch>

    <!-- ko template: {
      foreach: templateIf(question.customAnswerEnabled(), $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
    <div class="mb-4">
        <!-- ko component: {
            name: 'text-field',
            params: {
                controller: question.customAnswerField,
                disabled: question.isFullBlocked,
                useTooltip: question.useTooltips,
                tooltip: question.selfVariantDescription,
                showTooltip: question.showTooltips
            }
        } -->
        <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- <switch
        class="mb-4"
        params="
            checked: question.emptyVariantEnabled,
            disabled: question.isFullBlocked,
        "
        data-bind="event:{change: function() {
                        question.toggleEmptyVariant()
                    }}"
    >
        <div for="rate-clarifying-question-custom-answer" class="f-check-label" data-bind="text: question.translator.t('Вариант ответа «Ничего из перечисленного»')"></div>
    </switch> -->

    <switch
        class="mb-4"
        params="
            checked: question.emptyVariantEnabled,
            disabled: question.isFullBlocked,
        "
    >
        <div class="d-flex">
        <div for="rate-clarifying-question-custom-answer" class="f-check-label" data-bind="text: question.translator.t('«Ничего из перечисленного» для вариантов')"></div>
        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: question.translator.t('Опция позволяет выбрать любые созданные варианты ответов, при нажатии на которые будут сбрасываться все отмеченные респондентом варианты')" type="button"></button>
        </div>
        
    </switch>

    <!-- ko template: {
      foreach: templateIf(question.emptyVariantEnabled(), $data),
      afterAdd: slideAfterAddFactory(400),
        beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group mb-2">
      <div class="d-flex align-items-center flex-wrap">
        <!-- ko foreach: question.variantsList.list() -->
        <div class="f-check mr-15p mb-15p">
            <input
            type="checkbox"
            id="comment-required"
            class="f-check-input"
            data-bind="
                checked: question.emptyVariants().includes(persistentId || id),
                disable: question.isFullBlocked,
                event: { change: function() { 
                question.toggleEmptyVariants(persistentId || id) } },
                attr: { id: 'variant-empty-' + (persistentId || id)}
            "
            />
            <label class="f-check-label" data-bind="text: $index() + 1, attr: { for: 'variant-empty-' + (persistentId || id)}"></label>
        </div>
        <!-- /ko -->
        <!-- ko if: question.customAnswerEnabled() -->
        <div class="f-check mr-15p mb-15p">
            <input
            type="checkbox"
            id="nothing-self-answer"
            class="f-check-input"
            data-bind="
                checked: question.selfVariantNothing,
                disable: question.isFullBlocked,
            "
            />
            <label class="f-check-label" data-bind="text: question.customAnswerField.label().length ? question.customAnswerField.label() : question.customAnswerField.defaultLabel(), attr: { for: 'nothing-self-answer'}"></label>
        </div>
        
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->

    <hr class="mx-0 mt-0">

    <!-- ko template: {
        foreach: templateIf(question.isMultiple(), $data),
        afterAdd: fadeAfterAddFactory(400),
        beforeRemove: fadeBeforeRemoveFactory(400)
    } -->
    <div class="d-flex mt-4">
        <fc-variants-min-count class="w-100 mr-15p" params="
                min: question.answersCountLimitMin.min,
                max: question.answersCountLimitMin.max,
                value: question.answersCountLimitMin.value,
                disabled: question.isFullBlocked
        "></fc-variants-min-count>
        <fc-variants-max-count class="w-100 ml-15p" params="
                min: question.answersCountLimit.min,
                max: question.answersCountLimit.max,
                value: question.answersCountLimit.value,
                disabled: question.isFullBlocked
        "></fc-variants-max-count>
    </div>
    
    <!-- /ko -->
    <!-- ko if: question.withPoints -->
    <div class="mb-20p">
        <foquz-checkbox params="checked: question.maxPointsCalcMethod, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Учитывать в итоговой сумме баллов, если вопрос не отображался для респондента')"></span>
            <question-button params="text: question.translator.t('Настройки отображения вопроса можно настроить в логике опроса. По умолчанию все скрытые логикой отображения вопросы учитываются в итоговом подсчете баллов')">
            </question-button>
        </foquz-checkbox>
    </div>
    <!-- /ko -->
    <!-- ko if: !question.donor.useDonor() -->
    <div class="mb-20p">
        <foquz-checkbox params="checked: question.dropdown, disabled: question.isFullBlocked" >
            <span data-bind="text: question.translator.t('Варианты ответов выпадающим списком')"></span>
        </foquz-checkbox>
    </div>
    <!-- /ko -->

    <div class="mb-20p">
        <foquz-checkbox params="checked: question.randomOrder, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Случайный порядок вариантов')"></span>
            <question-button params="text: question.translator.t('Варианты ответов для каждого респондента при прохождении будут предложены в случайном порядке')">
            </question-button>
        </foquz-checkbox>
        <!-- ko template: {
            foreach: templateIf(question.randomOrder(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
        } -->
        <div class="form-label__note pt-10p">
            Можно зафиксировать отдельные варианты. Они будут отображаться в прохождении на той же позиции, что и в настройке вопроса.
        </div>
        <!-- /ko -->
    </div>

</template>

<template id="question-form-variants-donor">
    <div>
        <div class="form-group">
            <fc-label params="text: 'Вопрос-донор', hint: 'Вопрос-донор'"></fc-label>
            <fc-select
                params="
                    options: question.donor.donorsList,
                    value: question.donor.donorId,
                    disabled: question.isBlocked() || question.isFullBlocked,
                "
                data-bind="
                    click: function() {
                        if (question.isBlocked() && !question.isFullBlocked) {
                        question.tryChangeBlockedParam();
                        } else {
                        return true;
                        }
                    },
                "
            ></fc-select>
        </div>

        <div class="form-group">
            <fc-label params="text: 'Варианты ответов'"></fc-label>
            <fc-donor-variants-type params="
            value: question.donor.donorVariantsType, 
            isFullBlocked: question.isFullBlocked,
            disabled: question.isBlocked() || question.isFullBlocked" data-bind="click: function() {
                if (question.isBlocked() && !question.isFullBlocked) 
                    question.tryChangeBlockedParam();
                else return true;
            }" class="mb-4"></fc-donor-variants-type>
        </div>

        <div class="form-group">
            <!-- ko if: question.useTooltips -->
                <div class="mb-10p mt-15p">
                    <fc-button
                        params="
                            size: 'auto',
                            mode: 'text',
                            label: question.showTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
                            color: 'primary'
                        "
                        data-bind="click: () => question.showTooltips(!question.showTooltips())"
                    ></fc-button>
                </div>
            <!-- /ko -->
            <fc-donor-variants-list
                params="
                    variants: question.donorVariants,
                    withPoints: question.withPoints,
                    useTooltips: question.useTooltips,
                    showTooltips: question.showTooltips,
                    tooltipPlaceholder: 'Подсказка для варианта ответа',
                    randomOrder: question.randomOrder,
                    isFullBlocked: question.isFullBlocked,
                "
            ></fc-donor-variants-list>
            <!-- ko if: question.useTooltips -->
                <div class="mb-10p mt-15p">
                    <fc-button
                        params="
                            size: 'auto',
                            mode: 'text',
                            label: question.showTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
                            color: 'primary'
                        "
                        data-bind="click: () => question.showTooltips(!question.showTooltips())"
                    ></fc-button>
                </div>
            <!-- /ko -->
        </div>


        <!-- ko template: {
            foreach: templateIf(question.isMultiple(), $data),
            afterAdd: fadeAfterAddFactory(400),
            beforeRemove: fadeBeforeRemoveFactory(400)
        } -->
        <div class="d-flex mt-4">
            <fc-variants-min-count class="w-100 mr-15p" params="
                    min: question.answersCountLimitMin.min,
                    max: question.answersCountLimit.value,
                    value: question.answersCountLimitMin.value,
                    disabled: question.isFullBlocked
            "></fc-variants-min-count>
            <fc-variants-max-count class="w-100 ml-15p" params="
                    min: question.answersCountLimit.min,
                    max: question.answersCountLimit.max,
                    value: question.answersCountLimit.value,
                    disabled: question.isFullBlocked
            "></fc-variants-max-count>
        </div>
        <!-- /ko -->

        <!-- ko if: question.withPoints -->
        <div class="mb-20p">
            <foquz-checkbox params="checked: question.maxPointsCalcMethod, disabled: question.isFullBlocked">
                <span data-bind="text: question.translator.t('Учитывать в итоговой сумме баллов, если вопрос не отображался для респондента')"></span>
                <question-button params="text: question.translator.t('Настройки отображения вопроса можно настроить в логике опроса. По умолчанию все скрытые логикой отображения вопросы учитываются в итоговом подсчете баллов')">
                </question-button>
            </foquz-checkbox>
        </div>
        <!-- /ko -->

        <div class="mb-20p">
            <foquz-checkbox params="checked: question.dropdown, disabled: question.isFullBlocked">
                <span data-bind="text: question.translator.t('Варианты ответов выпадающим списком')"></span>
            </foquz-checkbox>
        </div>

        <div class="mb-20p">
            <foquz-checkbox params="checked: question.randomOrder, disabled: question.isFullBlocked">
                <span data-bind="text: question.translator.t('Случайный порядок вариантов')"></span>
                <question-button
                    params="text: question.translator.t('Варианты ответов для каждого респондента при прохождении будут предложены в случайном порядке')"
                ></question-button>
            </foquz-checkbox>
            <!-- ko template: {
                foreach: templateIf(question.randomOrder(), $data),
                afterAdd: slideAfterAddFactory(400),
                beforeRemove: slideBeforeRemoveFactory(400)
            } -->
                <div class="form-label__note pt-10p">
                    Можно зафиксировать отдельные варианты. Они будут отображаться в прохождении на той же позиции, что и в настройке вопроса.
                </div>
            <!-- /ko -->
        </div>
    </div>
</template>

<template id="question-form-template-variants">
    <fc-variants-type params="
            value: question.variantsType, 
            disabled: question.isBlocked() || question.isFullBlocked,
        " data-bind="
            click: function() {
                if (question.isBlocked() && !question.isFullBlocked) {
                    question.tryChangeBlockedParam();
                } else {
                    return true;
                }
            },
        " class="mb-4"></fc-variants-type>

        

    <!-- ko ifnot: question.isAuto || question.isSystem || question.mode === 'cpoint' -->
    <div class="form-group">
        <div data-bind="
        click: function() {
          return question.onDonorsTogglerClick();
        }">
            <fc-switch params="checked: question.donor.useDonor, 
                disabled: question.disableDonors || question.isFullBlocked,
                label: 'Использовать варианты ответов респондента из другого вопроса',
                hint: 'Использовать варианты ответов респондента из другого вопроса'"></fc-switch>
        </div>
    </div>
    <!-- ko template: {
       foreach: templateIf(!question.donor.useDonor(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
        <div>
            <fc-switch params="checked: question.enableFile, 
                disabled: question.isFullBlocked,
                label: 'Варианты ответов с изображениями/видео (плиткой)',
                hint: 'Варианты ответов с изображениями/видео (плиткой)'"></fc-switch>
        </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <div class="form-group">
        <div>
            <fc-switch params="checked: question.useTooltips, 
                disabled: question.isFullBlocked,
                label: 'Показывать подсказки для вариантов ответов',
                hint: 'Под текстом строк и столбцов появятся дополнительные поля, в которые можно внести текст тултипов (подсказок в знаке &#34;?&#34;)'"></fc-switch>
        </div>
    </div>

    <!-- ko template: {
       foreach: templateIf(question.donor.useDonor(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <!-- ko template: { name: 'question-form-variants-donor', } -->
    <!-- /ko -->
    <!-- /ko -->


    <!-- ko template: {
       foreach: templateIf(!question.donor.useDonor(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <!-- ko template: { name: 'question-form-variants-list', } -->
    <!-- /ko -->
    <!-- /ko -->

    <hr class="mx-0 my-4">

    <div>
        <div class="form-group">
            <fc-switch params="checked: question.skip, label: $translator.t('Пропуск ответа'), disabled: question.isFullBlocked"></fc-switch>
        </div>

        <!-- ko template: {
       foreach: templateIf(question.skip(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
        <div class="form-group">
            <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
            <fc-input
                params="
                    value: question.skipText,
                    counter: true,
                    maxlength: 125,
                    placeholder: $translator.t('Затрудняюсь ответить'),
                    disabled: question.isFullBlocked,
                "
            ></fc-input>
        </div>

        <!-- /ko -->
    </div>

    <hr class="mx-0">



    <!-- ko template: { name: 'gallery-question-gallery-template' } -->
    <!-- /ko -->
    <hr class="mx-0">
    <!-- Комментарий -->
  <div class="mt-4">
    <switch params="checked: question.commentEnabled, disabled: question.isFullBlocked"><span data-bind="text: $translator.t('Комментарий')"></span></switch>

  </div>



  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div>
        <div class="row pb-4">
            <div class="col">
            <div class="f-check">
                <input
                type="checkbox"
                id="comment-required"
                class="f-check-input"
                data-bind="
                    checked: question.commentIsRequired,
                    disable: question.isFullBlocked,
                    event: { change: function() { 
                    question.updateCommentRequired() } }
                "
                />
                <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный для всех')"></label>
            </div>
            </div>
        </div>
    <!-- ko template: {
        foreach: templateIf(!question.commentIsRequired(), $data),
        afterAdd: fadeAfterAddFactory(400),
        beforeRemove: fadeBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label class="mb-15p" params="text: 'Обязательный для вариантов'" data-bind="click: function () {console.log(question.variantsList.list())}"></fc-label>
      <div class="d-flex align-items-center flex-wrap">
        <!-- ko foreach: question.variantsList.list() -->
        <div class="f-check mr-15p mb-15p">
            <input
            type="checkbox"
            id="comment-required"
            class="f-check-input"
            data-bind="
                checked: question.requiredComments().includes(persistentId || id),
                disabled: question.isFullBlocked,
                event: {
                    change: () => question.toggleReqComments(id, persistentId || null)
                },
                attr: { id: 'comment-required-' + (persistentId || id)}
            "
            />
            <label class="f-check-label" data-bind="text: $index() + 1, attr: { for: 'comment-required-' + (persistentId || id)}"></label>
        </div>
        <!-- /ko -->
        <!-- ko if: question.customAnswerEnabled() -->
        <div class="f-check mr-15p mb-15p">
            <input
            type="checkbox"
            id="comment-required-self-answer"
            class="f-check-input"
            data-bind="
                checked: question.requiredComments().includes('is-self-answer'),
                disable: question.isFullBlocked,
                event: { change: function() { 
                question.toggleReqComments('is-self-answer', 'is-self-answer')
            } },
            "
            />
            <label class="f-check-label" data-bind="text: question.customAnswerField.label().length ? question.customAnswerField.label() : question.customAnswerField.defaultLabel(), attr: { for: 'comment-required-self-answer'}"></label>
        </div>
        <!-- /ko -->
      </div>
        <button
            class="f-btn f-btn-link -mt-10p"
            type="button"
            data-bind="
                attr: {
                    disabled: (question.requiredComments().length === question.variantsList.list().length + (question.customAnswerEnabled() ? 1 : 0))
                        || !!question.isFullBlocked
                },
                click: () => question.addAllComments()
            "
        >
            Выбрать все
        </button>
    </div>
    <!-- /ko -->
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: $translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
        } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->
</template>