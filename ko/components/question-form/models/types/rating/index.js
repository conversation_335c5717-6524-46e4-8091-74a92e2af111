import "Components/input/color-picker";

import GalleryQuestion from "../gallery";
import { RATING_QUESTION } from "Data/question-types";

import { VariantsController } from "../../../controllers/variants-controller";
import { Translator } from "@/utils/translate";
import { declOfNum } from "@/utils/string/decl-of-num";

const QuestionTranslator = Translator("question");
const ValidationTranslator = Translator("validation");

const starDefaultColor = "#3F65F1";

class RatingQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };

    super(controller, config);

    this.translator = QuestionTranslator;

    this.type = RATING_QUESTION;

    this.color = ko.observable(starDefaultColor);
    this.count = ko.observable(5);

    this.count.subscribe((v) => {
      this.clarifyingQuestionForRates([1, v]);
    });

    this.labels = Array(10)
      .fill()
      .map((_, i) => {
        return {
          value: ko.observable(""),
          label: i + 1
        };
      });

    this.visibleLabels = ko.pureComputed(() => {
      return this.labels.slice(0, this.count());
    });

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    this.galleryEnabled = ko.observable(false);

    this.clarifyingQuestionEnabled = ko.observable(false);
    this.clarifyingQuestionText = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => this.clarifyingQuestionEnabled(),
      },
    });
    this.clarifyingQuestionController = new VariantsController(
      {
        withCustomAnswer: true,
        withVariantsTypeSelect: true,
        withAddVariantButton: true,
        addFirstVariant: true,
        variantsType: "single",
        withTextAnswer: true,
      },
      this
    );
    this.clarifyingQuestionForAllRates = ko.observable(0);
    this.clarifyingQuestionIsRequired = ko.observable(1);
    this.clarifyingQuestionForRates = ko.observable([1, 5]);

    this.clarifyingQuestionEnabled.subscribe((v) => {
      if (!v) this.clarifyingQuestionForAllRates(false);
      else this.commentEnabled(false);
    });
    this.commentEnabled.subscribe((v) => {
      if (v) this.clarifyingQuestionEnabled(false);
    });

    this.commentIsRequired = ko.observable(null);

    [
      this.color,
      this.count,
      ...this.labels.map((l) => l.value),
      this.galleryEnabled,
      this.clarifyingQuestionEnabled,
      this.clarifyingQuestionForAllRates,
      this.clarifyingQuestionIsRequired,
      this.clarifyingQuestionText,
      this.skip,
      this.skipText,
      this.commentIsRequired,
      this.clarifyingQuestionForRates,
    ].forEach((f) => f.subscribe((v) => this.onChange()));

    [this.clarifyingQuestionController].forEach((c) =>
      c.isChanged.subscribe((v) => this.onChange())
    );
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  getLogicVariants(variants) {
    let _variants = [...variants];
    _variants.sort();
    return _variants.map((v) => {
      const word = declOfNum(v, ["звезда", "звезды", "звезд"]);
      return `${v} ${word}`;
    });
  }

  updateData(data) {
    let comment = data.comment;


    super.updateData({
      ...data,
      galleryCommentEnabled: comment.enabled,
      galleryCommentLengthRange: comment.range,
      galleryCommentPlaceholder: comment.placeholder,
      galleryCommentLabel: comment.label,
      galleryCommentRequaired: comment.required,
    });

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.comment.required)
    }

    this.galleryEnabled(data.galleryEnabled);

    let clarifyingQuestion = data.clarifyingQuestion;

    this.clarifyingQuestionEnabled(clarifyingQuestion.enabled);
    this.clarifyingQuestionForAllRates(clarifyingQuestion.forAllRates);
    this.clarifyingQuestionIsRequired(clarifyingQuestion.required);
    this.clarifyingQuestionText(clarifyingQuestion.text || "");
    this.clarifyingQuestionController.updateData({
      type: clarifyingQuestion.variantsType,
      variants: clarifyingQuestion.variants,
      customAnswerEnabled: clarifyingQuestion.customAnswerEnabled,
      customAnswerRange: clarifyingQuestion.customAnswerLengthRange,
      customAnswerPlaceholder: clarifyingQuestion.customAnswerPlaceholder,
      customAnswerLabel: clarifyingQuestion.customAnswerLabel,
    });

    let starsConfig = data.starsConfig;
    if (starsConfig) {
      this.color(starsConfig.color || starDefaultColor);
      this.count(starsConfig.count || 5);

      let labels = starsConfig.labels || [];
      this.labels.forEach((label, index) => {
        let labelText = labels[index];
        label.value(labelText || "");
      });
      if (starsConfig.extraQuestionRateFrom && starsConfig.extraQuestionRateTo) {
        this.clarifyingQuestionForRates([starsConfig.extraQuestionRateFrom, starsConfig.extraQuestionRateTo]);
      } else {
        this.clarifyingQuestionForRates([1, starsConfig.count || 5]);
      }
    }

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");

  }

  getData() {
    let data = super.getData();

    data.galleryEnabled = this.galleryEnabled();

    data.clarifyingQuestion = {
      enabled: this.clarifyingQuestionEnabled(),
      required: this.clarifyingQuestionIsRequired(),
      forAllRates: this.clarifyingQuestionForAllRates(),
      text: this.clarifyingQuestionText(),
      ...this.clarifyingQuestionController.getData(),
    };

    data.comment = {
      enabled: this.commentEnabled(),
      ...this.commentField.getData(),
      label: this.commentLabel(),
      required: this.commentIsRequired()
    };

    data.starsConfig = {
      color: this.color(),
      count: this.count(),
      labels: this.visibleLabels().map((l) => l.value()),
    };

    data.skip = this.skip();
    data.skipText = this.skipText();

    if (
      this.clarifyingQuestionEnabled() &&
      !this.clarifyingQuestionForAllRates() &&
      this.clarifyingQuestionForRates()[0] &&
      this.clarifyingQuestionForRates()[1]
    ) {
      data.starsConfig['extra_question_rate_from'] = this.clarifyingQuestionForRates()[0];
      data.starsConfig['extra_question_rate_to'] = this.clarifyingQuestionForRates()[1];
    }

    return data;
  }

  isValid() {
    if (!this.clarifyingQuestionText.isValid()) return false;

    if (this.clarifyingQuestionEnabled()) {
      if (!this.clarifyingQuestionController.isValid()) return false;
    }

    if (this.galleryEnabled()) {
      return super.isValid();
    }

    return this.defaultValidation();
  }
}

export default RatingQuestion;
