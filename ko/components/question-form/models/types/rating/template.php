<template id="question-form-template-rating">
  <!-- ko let: { $translator: question.translator } -->

  <div class="row">
    <div class="col-6">
      <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Цвет шкалы')"></label>
        <div class="input-group" data-bind="colorPicker">
          <div class="input-group-prepend  poll-design__color-preview js-color-preview" data-bind="attr: {'data-backup': question.color}"></div>
          <input required class="form-control" data-bind="textInput: question.color, disable: question.isFullBlocked" maxlength="7" data-is-hex="true">
        </div>
      </div>
    </div>

    <div class="col-6">
      <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Размер шкалы')"></label>

        <div class="input-group slider-input-group" data-bind="click: function() {
          if (question.isFullBlocked) return false;
          if (question.isBlocked()) {
            question.tryChangeBlockedParam();
          }
          else return true;
        }">
          <div class="form-control" data-bind="slider, sliderMin: 2, sliderMax: 10, sliderValue: question.count, disabled: question.isBlocked() || question.isFullBlocked"></div>

          <div class="input-group-append" data-bind="style: {
            opacity:question.isBlocked() ? 0.5 : 1
          }">
            <span class="input-group-text" data-bind="text: question.count"></span>
          </div>
        </div>
      </div>
    </div>


    <div class="col-12">
      <div class="mb-1">
        <label class="form-label">
          <span data-bind="text: $translator.t('Метки')"></span>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: $translator.t('Метки')"></button>
        </label>

        <div class="d-flex flex-wrap mx-n1">
          <!-- ko foreach: question.visibleLabels -->
          <div class="mb-3 stars-question-label">
            <input type="text" class="form-control mb-1" data-bind="textInput: value, disable: question.isFullBlocked" maxlength="150">
            <div data-bind="text: label" class="text-center"></div>
          </div>
          <!-- /ko -->
        </div>
      </div>

    </div>
  </div>

  <hr>

  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск оценки'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
       foreach: templateIf(question.skip(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Не готов(а) оценить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>

    <!-- /ko -->
  </div>

  <hr>

  <!-- ko template: {
    name: 'gallery-question-gallery-template',
    data: {
      question: question,
    }
  } -->
  <!-- /ko -->

  <hr>

  <div class="row pb-1">
    <div class="col">
      <div class="pt-1">

        <switch class="mb-0" params="checked: question.clarifyingQuestionEnabled, disabled: question.isBlocked() || question.isFullBlocked" data-bind="click: function() {
                    if (question.isFullBlocked) return false;
                    if (question.isBlocked()) question.tryChangeBlockedParam();
                    else return true;
                }">
          <span data-bind="text: $translator.t('Уточняющий вопрос')"></span>
        </switch>
      </div>
    </div>
    <div class="col">
      <div class="pt-1">
        <switch class="mb-0" params="checked: question.commentEnabled, disabled: question.isFullBlocked || (question.isBlocked() && question.clarifyingQuestionEnabled())" data-bind="click: function() {
                    if (question.isFullBlocked) return false;
                    if (question.isBlocked()  && question.clarifyingQuestionEnabled()) question.tryChangeBlockedParam();
                    else return true;
                }">
          <span data-bind="text: $translator.t('Комментарий')"></span>
        </switch>

      </div>
    </div>
  </div>
  <!-- ko template: {
    foreach: question.clarifyingQuestionEnabled,
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <div class="row pt-4">
    <div class="col">
      <div class="f-check">
        <input type="checkbox" id="for-all-rate" class="f-check-input" data-bind="checked: question.clarifyingQuestionForAllRates, disable: question.isFullBlocked">
        <label for="for-all-rate" class="f-check-label" data-bind="text: $translator.t('Для всех оценок')"></label>
      </div>
    </div>
    <div class="col">
      <div class="f-check">
        <input type="checkbox" id="extra-required" class="f-check-input" data-bind="checked: question.clarifyingQuestionIsRequired, disable: question.isFullBlocked">
        <label for="extra-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
      </div>
    </div>
  </div>
  <!-- ko template: {
    foreach: templateIf(!question.clarifyingQuestionForAllRates(), $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <div class="row pt-4">
    <div class="col mb-4">
      <fc-label params="text: 'Для оценок'"></fc-label>
      <fc-range
        class="clarifying-answer-settings__rates-range"
        params="
          value: question.clarifyingQuestionForRates,
          min: 1,
          max: question.count,
          tooltips: true,
          disabled: question.isFullBlocked,
        "
      ></fc-range>
    </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <!-- Уточняющий вопрос -->
  <!-- ko template: {
        foreach: templateIf(question.clarifyingQuestionEnabled(), $data),
        afterAdd: slideAfterAddFactory(200, 150),
        beforeRemove: slideBeforeRemoveFactory(150)
    } -->
  <div class="form-group mt-3">
    <label class="form-label" for="clarifyingQuestion" data-bind="text: $translator.t('Уточняющий вопрос')"></label>

    <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно добавить вопрос, который дополнительно будет задан респонденту для уточнения его ответа. Уточняющий вопрос должен быть всегда с вариантами ответов')" type="button">
    </button>

    <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: question.clarifyingQuestionText().length">
      <textarea class="form-control" data-bind="
          textInput: question.clarifyingQuestionText,
          css: {
              'is-invalid': controller.formControlErrorStateMatcher(question.clarifyingQuestionText),
              'is-valid': controller.formControlSuccessStateMatcher(question.clarifyingQuestionText)
          },
          autosizeTextarea,
          disable: question.isFullBlocked" id="clarifyingQuestion" maxlength="500">
      </textarea>


      <div class="chars-counter__value"></div>

    </div>

    <!-- ko if: controller.formControlErrorStateMatcher(question.clarifyingQuestionText) -->
    <div class="form-error" data-bind="text: question.clarifyingQuestionText.error()"></div>
    <!-- /ko -->
  </div>

  <div class="">
    <question-form-variants-list params="controller: question.clarifyingQuestionController, disabled: question.isFullBlocked">
    </question-form-variants-list>
  </div>
  <!-- /ko -->
  <!-- /Уточняющий вопрос -->


  <!-- Комментарий -->
  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200, 200),
      beforeRemove: slideBeforeRemoveFactory(150)
  } -->
  <div class="row pt-4">
    <div class="col">
      <div class="f-check">
        <input
          type="checkbox"
          id="comment-required"
          class="f-check-input"
          data-bind="
            checked: question.commentIsRequired,
            disable: question.isFullBlocked,
            event: { change: function() { 
              question.updateCommentRequired() } }
          "
        />
        <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
      </div>
    </div>
  </div>
  <div class="mt-4">
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>
    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: $translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
    } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->

  <!-- /ko -->
</template>