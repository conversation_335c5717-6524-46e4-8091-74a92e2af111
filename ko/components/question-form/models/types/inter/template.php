<link rel="stylesheet" href=/usocial/uscl.css?v=7.1.5">


<template id="question-form-template-inter">
  <!-- ko let: { $translator: question.translator } -->
  <div class="form-group">
    <!-- ko if: question.blockType() == 'five-second-test' -->
      <label class="form-label"><span data-bind="text: $translator.t('Инструкция')"></span></label>

      <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Инструкция, которая отображается перед началом теста')" type="button" title="">
      </button>

      <ckeditor params="
        value: question.description,
        variables: question.variables,
        promocode: question.promocode,
        disabled: question.isFullBlocked,
        options: {
          fontSize: [8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42]
        },
        execute: { 'alignment': 'center' },
        error: controller.formControlErrorStateMatcher(question.description)() ? question.description.error : '',
      " data-bind="
        css: {
          'is-invalid': controller.formControlErrorStateMatcher(question.description)
        },
      " class="ckeditor--inter"></ckeditor>
    <!-- /ko -->
    <!-- ko if: question.blockType() != 'five-second-test' -->
      <label class="form-label"><span data-bind="text: $translator.t('Текст на странице')"></span></label>
      <ckeditor params="
        value: question.text,
        variables: question.variables,
        promocode: question.promocode,
        options: {
          fontSize: [8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42]
        },
        execute: { 'alignment': 'center' },
        disabled: question.isFullBlocked,
        error: controller.formControlErrorStateMatcher(question.text)() ? question.text.error : '',
      " data-bind="
        css: {
          'is-invalid': controller.formControlErrorStateMatcher(question.text)
        },
      " class="ckeditor--inter"></ckeditor>
    <!-- /ko -->
  </div>

  <!-- ko if: question.blockType() == 'five-second-test' -->
  <div class="form-group">
    <fc-check params="checked: question.show_bg_instruction, label: 'Показывать инструкцию на подложке', disabled: question.isFullBlocked">
    </fc-check>
  </div>
  <!-- /ko -->

  <!-- ko if: question.blockType() != 'end' && question.blockType() != 'five-second-test' -->
  <div class="form-group">
    <fc-check params="checked: question.agreement, label: 'Согласие респондента на продолжение', hint: 'Согласие респондента на продолжение', disabled: question.isFullBlocked">
    </fc-check>
  </div>

  <!-- ko template: {
     foreach: templateIf(question.agreement(), $data),
     afterAdd: slideAfterAddFactory(400),
     beforeRemove: slideBeforeRemoveFactory(400)
  } -->
  <div class="form-group">
    <fc-label params="text: 'Текст согласия'"></fc-label>
    <ckeditor params="
        value: question.agreementText,
        variables: [],
        blankExternalLinks: true,
        placeholder: 'Я согласен/согласна на обработку персональных данных',
        toolbar: ['bold', 'underline', 'italic', 'strikethrough', 'link', 'alignment:left', 'alignment:right', 'alignment:center', 'alignment:justify', 'fontSize'],
        disabled: question.isFullBlocked,
        error: controller.formControlErrorStateMatcher(question.agreementText)() ? question.agreementText.error : '',
      "
      class="ckeditor_min"
      data-bind="
        css: {
          'is-invalid': controller.formControlErrorStateMatcher(question.agreementText)
        },
      "
    >
    </ckeditor>
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko if: question.blockType()  === 'start' || question.blockType() === 'end' || question.blockType() === 'text' -->
  <hr class="mt-0 mb-4">
  <div class="switch-form-group switch-form-group--lg">
    <switch class="mb-0" params="checked: question.showImages, disabled: question.isFullBlocked"><span data-bind="text: $translator.t('Изображения')"></span></switch>
  </div>

  <div class="f-color-service f-fs-1 mb-20p mt-10p">
      <!-- ko ifnot: question.isFullBlocked -->
      <span data-bind="text: $translator.t('Добавьте изображение с вашего устройства или по ссылке')"></span>
      <!-- /ko -->
  </div>

  <!-- ko template: {
        foreach: templateIf(question.showImages(), $data),
        afterAdd: slideAfterAddFactory(400),
        beforeRemove: slideBeforeRemoveFactory(400)
      } -->

  <div data-bind="dnd: function(files) {
        question.loader.loadFile(files[0])
      }, style: {
        '--interscreen-image-bg': question.imagesBackground
      }">
    <div class="row">
      <div class="col-12 col-md-6">
        <div class="mb-4">
          <label class="form-label"><span data-bind="text: $translator.t('Цвет подложки')"></span></label>
          <color-picker params="value: question.imagesBackground, opacity: true, colorFormat: 'rgba', disabled: question.isFullBlocked"></color-picker>
        </div>
      </div>
    </div>
    <div>
      <div class="styled interscreen-images" data-bind="foquzSortable: {
            data: question.images,
            as: 'image',
            options: {
              handle: '.drag-handle',
            }
          }, attr: {
            'data-count': question.images().length
          }">
        <div>
          <interscreen-image params="image: image, formControlErrorStateMatcher: $parent.controller.formControlErrorStateMatcher, disabled: question.isFullBlocked" data-bind="event: {
            remove: function() {
              question.removeImage(image)
            }
          }" class="border-top"></interscreen-image>
        </div>
      </div>

    </div>
    <!-- ko ifnot: question.isFullBlocked -->
    <div class="border-top py-4">
      <button type="button" class="f-btn" data-bind="click: function() {
        question.selectImage();
      }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'clip'"></svg-icon>
        </span>
        <span data-bind="text: $translator.t('С компьютера')"></span>
      </button>
      <button type="button" class="f-btn" data-bind="click: function() {
        question.selectImageLink();
      }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'link'" class="svg-icon--lg"></svg-icon>
        </span>
        <span data-bind="text: $translator.t('По ссылке')"></span>
      </button>
      <div>
        <!-- ko foreach: question.loader.errors -->
        <file-loader-error params="error: $data"></file-loader-error>
        <!-- /ko -->
        <validation-feedback params="show: controller.formControlErrorStateMatcher(question.images), text: question.images.error"></validation-feedback>
      </div>
    </div>
    <!-- /ko -->
    <dnd-cover params="type: 'image', dense: !question.images().length"></dnd-cover>
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko if: question.blockType() == 'five-second-test' -->
  <div class="form-group">   
    <div class="d-flex flex-column">
      <div class="cover-image-upload-container">
        <!-- ko if: question.images().length === 0 -->
        <div class="cover-image-upload-box" data-bind="click: function() { question.selectImage(); }, dnd: function(files) { question.loadFile(files[0]); }">
          <div class="cover-image-upload-icon">
            <svg-icon params="name: 'clip'" class="svg-icon--lg"></svg-icon>
          </div>
          <div class="cover-image-upload-text">
            <span data-bind="text: $translator.t('Загрузить изображение до 5 Мб')"></span>
          </div>
        </div>
        <!-- /ko -->
        <!-- ko with: question.images()[0] -->
          <!-- ko if: fileUrl -->
          <div class="cover-image-preview-container">
            <img data-bind="attr: { src: fileUrl() }" class="cover-image-preview-img">
            <!-- ko if: !question.isFullBlocked -->
              <button class="cover-image-preview-remove" data-bind="click: function() { question.removeImage($data); }">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20Z" fill="black"/>
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.35275 9.79789L6.7105 8.15564C6.34405 7.78919 6.34405 7.19201 6.7105 6.82555C7.07695 6.4591 7.67414 6.4591 8.04059 6.82555L9.68284 8.46781L11.4065 6.74412C11.8544 6.29623 12.5602 6.29623 13.0081 6.74412C13.456 7.19201 13.456 7.89777 13.0081 8.34565L11.2844 10.0693L12.9266 11.7116C13.2931 12.078 13.2931 12.6752 12.9266 13.0417C12.5602 13.4081 11.963 13.4081 11.5965 13.0417L9.95429 11.3994L8.2306 13.1231C7.78272 13.571 7.07695 13.571 6.62907 13.1231C6.18118 12.6752 6.18118 11.9695 6.62907 11.5216L8.35275 9.79789Z" fill="white"/>
                </svg>
              </button>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        <!-- /ko -->
        <div class="cover-image-upload-description">
          <span data-bind="text: $translator.t('Загруженное изображение будет отображаться при клике на кнопку в полноэкранном режиме')"></span>
        </div>
      </div>

      <!-- ko if: question.loaderError() -->
      <div class="mt-10p pt-15p file-loader-error position-relative">
        <div class="position-absolute file-loader-error-text" data-bind="text: question.loaderErrorText"></div>
      </div>
      <!-- /ko -->
      <!-- ko ifnot: question.loaderError() -->
      <validation-feedback params="show: controller.formControlErrorStateMatcher(question.images), text: question.images.error"></validation-feedback>
      <!-- /ko -->
    </div>
  </div>

  <div class="row">
    <div class="col-6">
      <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Время показа изображения, секунд')"></label>

        <div class="input-group slider-input-group" data-bind="click: function() {
          if (question.isFullBlocked) return false;
          else return true;
        }">
          <div class="form-control" data-bind="slider, sliderMin: 5, sliderMax: 60, sliderValue: question.image_show_time, disabled: question.isBlocked() || question.isFullBlocked"></div>

          <div class="input-group-append" data-bind="style: {
            opacity:question.isBlocked() ? 0.5 : 1
          }">
            <span class="input-group-text" data-bind="text: question.image_show_time"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="form-group">
        <div class="d-flex justify-content-between">
          <label class="form-label" for="name">
            <span data-bind="text: $translator.t('Текст кнопки')"></span>
          </label>
        </div>

        <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: question.show_image_button_text().length">
          <input class="form-control" data-bind="
              textInput: question.show_image_button_text,              
              attr: {
                placeholder: $translator.t('Показать изображение'),
                disabled: !!window.CURRENT_USER.watcher,
              },
              css: {
                  'is-invalid': controller.formControlErrorStateMatcher(question.show_image_button_text),
                  'is-valid': controller.formControlSuccessStateMatcher(question.show_image_button_text),
              }" id="show_image_button_text" maxlength="30">
          <div class="chars-counter__value"></div>
        </div>

        <!-- ko if: controller.formControlErrorStateMatcher(question.show_image_button_text) -->
        <div class="form-error" data-bind="text: question.show_image_button_text.error()"></div>
        <!-- /ko -->
      </div>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko if: question.blockType() === 'text' -->
  <hr class="mt-0 mb-4">
  <!-- /ko -->
  <!-- ko template: {
    foreach: templateIf(question.blockType() == 'start' || question.blockType() == 'end', $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400)
  } -->

  <div class="border-top pt-4">

    <!-- ko template: {
        foreach: templateIf(question.blockType() == 'start', $data),
        afterAdd: slideAfterAddFactory(400),
        beforeRemove: slideBeforeRemoveFactory(400)
      } -->
    <div class="row">
      <div class="col-12 col-lg-6">


        <div class="form-group">
          <label class="form-label"><span data-bind="text: $translator.t('Текст кнопки «пройти опрос»')"></span>
          </label>
          <foquz-chars-counter params="value: question.texts.takeSurvey, max: 30">
            <input
              type="text"
              class="form-control"
              data-bind="
                textInput: question.texts.takeSurvey,
                attr: {
                  placeholder: question.defaultTexts.takeSurvey,
                },
                disable: question.isFullBlocked,
              "
            />
          </foquz-chars-counter>
        </div>


      </div>
    </div>
    <!-- /ko -->

    <div class="row">
      <!-- ko template: {
        foreach: templateIf(question.blockType() == 'end', $data),
        afterAdd: slideAfterAddFactory(400),
        beforeRemove: slideBeforeRemoveFactory(400)
      } -->
        <div class="col-12 col-lg-6">
          <div class="form-group">
            <fc-check
              params="
                checked: question.hasStartOverButton, 
                disabled: question.isFullBlocked, 
                label: $translator.t('Кнопка «Начать заново»'),
                hint: $translator.t('Кнопка «Начать заново»')
              "
            ></fc-check>
          </div>
          <!-- ko template: {
            foreach: templateIf(question.hasStartOverButton(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
          } -->
            <div class="form-group">
              <label class="form-label"><span data-bind="text: $translator.t('Текст кнопки «Начать заново»')"></span>

              </label>

              <foquz-chars-counter params="value: question.texts.startOver, max: 30">
                <input type="text" class="form-control" data-bind="textInput: question.texts.startOver, attr: { placeholder: question.defaultTexts.startOver }, disable: question.isFullBlocked">
              </foquz-chars-counter>
            </div>
          <!-- /ko -->
        </div>
      <!-- /ko -->

      <div class="col-12 col-lg-6">
        <div class="form-group">
          <fc-check
            params="
              checked: question.hasComplainButton, 
              disabled: question.isFullBlocked, 
              label: $translator.t('Ссылка «Пожаловаться»'),
              hint: $translator.t('Функция используется для добавления жалобы')
            "
          ></fc-check>
        </div>
        <!-- ko template: {
          foreach: templateIf(question.hasComplainButton(), $data),
          afterAdd: slideAfterAddFactory(400),
          beforeRemove: slideBeforeRemoveFactory(400)
        } -->
          <div class="form-group">
            <label class="form-label"><span data-bind="text: $translator.t('Текст ссылки «Пожаловаться»')"></span>
            </label>
            <foquz-chars-counter params="value: question.texts.complain, max: 30">
              <input type="text" class="form-control" data-bind="textInput: question.texts.complain, attr: { placeholder: question.defaultTexts.complain }, disable: question.isFullBlocked">
            </foquz-chars-counter>
          </div>
        <!-- /ko -->
      </div>

      <div class="col-12 col-lg-6">
        <div class="form-group">
          <fc-check
            params="
              checked: question.hasUnsubscribeButton, 
              disabled: question.isFullBlocked, 
              label: $translator.t('Ссылка «Отписаться от рассылки»'),
              hint: $translator.t('Функция используется для возможности отписаться от рассылки опросов')
            "
          ></fc-check>
        </div>
        <!-- ko template: {
          foreach: templateIf(question.hasUnsubscribeButton(), $data),
          afterAdd: slideAfterAddFactory(400),
          beforeRemove: slideBeforeRemoveFactory(400)
        } -->
          <div class="form-group">
            <label class="form-label"><span data-bind="text: $translator.t('Текст ссылки «Отписаться»')"></span>
            </label>
            <foquz-chars-counter params="value: question.texts.unsubscribe, max: 30">
              <input type="text" class="form-control" data-bind="textInput: question.texts.unsubscribe, attr: { placeholder: question.defaultTexts.unsubscribe }, disable: question.isFullBlocked">
            </foquz-chars-counter>
          </div>
        <!-- /ko -->
      </div>
      
      <!-- ko template: {
        foreach: templateIf(question.blockType() == 'end', $data),
        afterAdd: slideAfterAddFactory(400),
        beforeRemove: slideBeforeRemoveFactory(400)
      } -->
        <div class="col-12 col-lg-6">
          <div class="form-group">
            <fc-check
              params="
                checked: question.hasCloseButtonForWidget, 
                disabled: question.isFullBlocked, 
                label: $translator.t('Кнопка «Закрыть» для виджета'),
                hint: $translator.t('Кнопка отображается только для виджета опроса и закрывает его при клике')
              "
            ></fc-check>
          </div>
          <!-- ko template: {
            foreach: templateIf(question.hasCloseButtonForWidget(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
          } -->
            <div class="form-group">
              <label class="form-label"><span data-bind="text: $translator.t('Текст кнопки «Закрыть»')"></span>
              </label>
              <foquz-chars-counter params="value: question.texts.closeButtonForWidget, max: 30">
                <input
                  type="text"
                  class="form-control"
                  data-bind="
                    textInput: question.texts.closeButtonForWidget,
                    attr: { placeholder: question.defaultTexts.closeButtonForWidget },
                    disable: question.isFullBlocked,
                  "
                >
              </foquz-chars-counter>
              <div
                data-bind="text: $translator.t('Кнопка отображается только для виджета опроса и закрывает его при клике')"
                class="f-color-service f-fs-1 mb-20p mt-10p"
              ></div>
            </div>
          <!-- /ko -->
        </div>
      <!-- /ko -->
    </div>


    <!-- ko template: {
      foreach: templateIf(question.blockType() == 'end', $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400)
    } -->
      <div class="row">
        <div class="col-12">
          <div class="form-group">
            <fc-check
              params="
                checked: question.hasReadyButton, 
                disabled: question.isFullBlocked, 
                label: $translator.t('Кнопка «Готово»'),
                hint: $translator.t('Кнопка «Готово»')
              "
            ></fc-check>
          </div>
          <!-- ko template: {
              foreach: templateIf(question.hasReadyButton(), $data),
              afterAdd: slideAfterAddFactory(400),
              beforeRemove: slideBeforeRemoveFactory(400)
            } -->
          <div class="row">
            <div class="col-12 col-lg-6">
              <div class="form-group">
                <label class="form-label"><span data-bind="text: $translator.t('Текст кнопки «Готово»')"></span>
                </label>
                <foquz-chars-counter params="value: question.texts.ready, max: 30">
                  <input type="text" class="form-control" data-bind="textInput: question.texts.ready, attr: { placeholder: question.defaultTexts.ready }, disable: question.isFullBlocked">
                </foquz-chars-counter>
              </div>
            </div>
            <div class="col-12 col-lg-6">
              <div class="form-group">
                <label class="form-label"><span data-bind="text: $translator.t('Ссылка на внешний ресурс')"></span>
                  <question-button><span data-bind="text: $translator.t('Ссылка на внешний ресурс')"></span></question-button>
                </label>
                <input type="text" class="form-control" data-bind="textInput: question.readyLink, css: {
                        'is-invalid': controller.formControlErrorStateMatcher(question.readyLink)
                      }, disable: question.isFullBlocked" maxlength="2048" placeholder="http://">
                <validation-feedback params="show: controller.formControlErrorStateMatcher(question.readyLink), text: question.readyLink.error"></validation-feedback>
              </div>
            </div>
          </div>
          <!-- /ko -->
        </div>
      </div>
    <!-- /ko -->
  </div>

  <!-- ko template: {
    foreach: templateIf(question.withPoints && question.blockType() == 'end', $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400)
  } -->
    <div class="row">
      <div class="col-12 col-lg-6">
        <div class="form-group">
          <fc-check
            params="
              checked: question.hasPointsButton, 
              disabled: question.isFullBlocked, 
              label: $translator.t('Кнопка «Отчет о тестировании»'),
              hint: $translator.t('Кнопка «Отчет о тестировании»')
            "
          ></fc-check>
        </div>
        <!-- ko template: {
          foreach: templateIf(question.hasPointsButton(), $data),
          afterAdd: slideAfterAddFactory(400),
          beforeRemove: slideBeforeRemoveFactory(400)
        } -->
          <div class="form-group">
            <label class="form-label"><span data-bind="text: $translator.t('Текст кнопки «Отчет о тестировании»')"></span>

            </label>

            <foquz-chars-counter params="value: question.texts.points, max: 30">
              <input type="text" class="form-control" data-bind="textInput: question.texts.points, attr: { placeholder: question.defaultTexts.points }, disable: question.isFullBlocked">
            </foquz-chars-counter>
          </div>
        <!-- /ko -->
      </div>
    </div>
  <!-- /ko -->


  <!-- ko ifnot: question.isAuto || question.mode == 'cpoint' -->
  <hr class="mt-0 mb-4">

  <div class="form-group switch-form-group switch-form-group--lg mb-0">

    <div class="d-flex align-items-center flex-wrap">
      <switch class="mr-4" params="checked: question.enableShare, disabled: question.isFullBlocked"><span data-bind="text: $translator.t('Иконки соцсетей')"></span></switch>

      <div class="f-fs-1 f-color-service mb-4" style="padding-top: 4px; padding-bottom: 2px;"><span data-bind="text: $translator.t('Отображаются только для анонимных опросов')"></span></div>
    </div>

  </div>


  <!-- ko template: {
      foreach: templateIf(question.enableShare(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
    } -->
  <share-config params="share: question.share, disabled: question.isFullBlocked"></share-config>
  <!-- /ko -->
  <!-- /ko -->
  </div>

  <!-- /ko -->
  <!-- /ko -->

</template>