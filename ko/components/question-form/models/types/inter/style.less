.interscreen-image {
  .media-load-button {
    background-color: var(--interscreen-image-bg);
  }
}

.interscreen-images {
  .ui-sortable-helper {
    margin-left: -20px;
    margin-right: -20px;
    padding-left: 20px;
    padding-right: 20px;
  }

  &[data-count="1"] {
    .drag-handle {
      display: none;
    }
  }
}

// Стили для компонента cover-image
.cover-image-upload-container {
  display: flex;
  align-items: center;
  gap: 30px;
}

.cover-image-upload-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 5px;
  flex: 0 0 105px;
  width: 105px;
  height: 105px;
  background-color: #DADFE3;
  border-radius: 8px;
  cursor: pointer;
  padding: 0px 10px;
  transition: background-color 0.2s;
}

.cover-image-upload-icon {
  color: #6C757D;
}

.cover-image-upload-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 10px;
  color: #73808D;
  text-align: center;
}

.cover-image-upload-description {
  color: #6C757D;
  font-size: 14px;
  max-width: 600px;
}

.cover-image-preview {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
}

.cover-image-preview-container {
  position: relative;
  flex: 0 0 105px;
  width: 105px;
  height: 105px;
  border-radius: 8px;
  background-color: #f5f5f5;

  .cover-image-preview-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }

  .cover-image-preview-remove {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #000000;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;

    .svg-icon {
      color: white;
    }
  }
}
