import Question from "../../question";
import { INTER_BLOCK } from "Data/question-types";

import "Components/editor/ckeditor";
import { DiscountPool } from "@/entities/models/discount-pool";
import { Share } from "Models/share";
import { interscreenTexts } from "./default-texts";

import "Components/share-config";
import "Components/input/color-picker";
import { FoquzImageLoader } from "Models/file-loader/image-loader";
import { InterblockImage } from "./image";
import "Bindings/sortable";
import "./interscreen-image";
import "./cover-image";
import "Dialogs/link-dialog";
import { DialogsModule } from "Utils/dialogs-module";
import { Translator } from "@/utils/translate";
const ValidationTranslator = Translator("validation");
import "./style.less";
import {
  COPY_PROMOCODE_VARIABLE,
  PROMOCODE_VARIABLE,
} from "../../../../../constants/editor-variables";

export default class InterBlock extends Question {
  constructor(controller, config) {
    super(controller, config);

    DialogsModule(this);
    this.type = INTER_BLOCK;

    this.savedShowNumber = ko.observable(false);
    this.showNumber = ko.observable(false);
    this.subscriptions.push(
      this.showNumber.subscribe((v) => {
        if (this.savedShowNumber() === v) return;
        this.emit("changeShowNumber", v);
        this.savedShowNumber(v);
      })
    );

    this.savedBlockType = ko.observable("text");
    this.blockType = ko.observable("text");

    this.agreement = ko.observable(false);
    this.agreementText = ko.observable("");
    this.subscriptions.push(
      this.agreement.subscribe((v) => {
        if (!v) this.agreementText("");
      })
    );

    this.updating = ko.observable(false);

    this.subscriptions.push(
      this.blockType.subscribe((v) => {
        if (this.savedBlockType() === v) return;
        this.emit("changeScreenType", {
          from: this.savedBlockType(),
          to: v,
        });
        this.savedBlockType(v);
      })
    );

    this.disableStartScreen = this.controller.disableStartScreen;

    [this.blockType, this.showNumber].forEach((f) => {
      this.subscriptions.push(
        f.subscribe((_) => {
          this.emit("question.change:ui", {
            field: "screenType",
            data: {
              type: this.blockType(),
              showNumber: this.showNumber(),
            },
          });
        })
      );
    });

    this.subscriptions.push(
      this.blockType.subscribe((v) => {
        this.showNumber(false);
      })
    );

    this.text = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => this.blockType() == "text",
      },
    });

    let variables = [
      "name",
      PROMOCODE_VARIABLE,
      COPY_PROMOCODE_VARIABLE,
      "lang",
    ];

    if (this.controller.hasOrder || this.mode == "cpoint") {
      variables = [
        "name",
        "orderNumber",
        "orderTime",
        PROMOCODE_VARIABLE,
        COPY_PROMOCODE_VARIABLE,
        "lang",
      ];
    }

    this.variables = ko.computed(() => {
      if (this.blockType() == "end" && this.withPoints) {
        return [
          ...variables,
          "pointsTotal",
          "points",
          "pointsPercent",
          "pointsResult",
          "pointsDescription",
        ];
      }

      if (this.blockType() == "five-second-test") {
        return [
          "name"
        ];
      }

      return variables;
    });

    this.hasComplainButton = ko.observable(false);
    this.hasUnsubscribeButton = ko.observable(false);
    this.hasReadyButton = ko.observable(false);
    this.hasPointsButton = ko.observable(false);
    this.hasCloseButtonForWidget = ko.observable(false);

    this.defaultTexts = {};
    Object.entries(interscreenTexts).forEach(([key, value]) => {
      this.defaultTexts[key] = this.translator.t(value);
    });

    this.texts = {
      takeSurvey: ko.observable(""),
      unsubscribe: ko.observable(""),
      complain: ko.observable(""),
      ready: ko.observable(""),
      points: ko.observable(""),
      startOver: ko.observable(""),
      closeButtonForWidget: ko.observable(""),
    };

    this.readyLink = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => {
          return this.blockType() == "end" && this.hasReadyButton();
        },
      },
      validation: {
        validator: (v) => {
          if (!v) return true;
          return validator.isURL(v || "");
        },
        message: () => ValidationTranslator.t("Неверный формат ссылки")(),
      },
    });

    this.hasCloseButtonForWidget.subscribe((v) => {
      if (!v) {
        this.texts.closeButtonForWidget("");
      }
    });

    this.hasPointsButton.subscribe((v) => {
      if (!v) {
        this.texts.points("");
      }
    });

    this.hasReadyButton.subscribe((v) => {
      if (!v) {
        this.texts.ready("");
        this.readyLink("");
      }
    });

    this.hasStartOverButton = ko.observable(false);

    this.promocode = new DiscountPool();

    // Добавляем переменную для времени показа изображения в режиме five-second-test
    this.image_show_time = ko.observable(25);
    
    // Добавляем переменную для текста кнопки показа изображения
    this.show_image_button_text = ko.observable("");
    
    // Добавляем переменную для отображения инструкции на подложке
    this.show_bg_instruction = ko.observable(false);

    // соцсети (только для анонимных ручных опросов)
    this.enableShare = ko.observable(false);
    this.share = new Share();
    this.loader = new FoquzImageLoader();
    this.loaderError = ko.observable(false);
    this.loaderErrorText = ko.observable('Файл должен быть меньше 5 Мб');

    this.showImages = ko.observable(false);
    this.images = ko.observableArray([]).extend({
      validation: {
        validator: (v) => {
          // Для режима five-second-test нужно ровно одно изображение
          if (this.blockType() === "five-second-test") {
            return v.length === 1 && v[0].fileUrl();
          }
          // Для обычного режима нужно хотя бы одно изображение
          return v.length && v.some((i) => i.fileUrl());
        },
        onlyIf: () => {
          // Для режима five-second-test валидация всегда активна
          if (this.blockType() === "five-second-test") {
            return true;
          }
          // Для обычного режима только если включены изображения
          return this.showImages();
        },
        message: () => {
          // Разные сообщения для разных режимов
          if (this.blockType() === "five-second-test") {
            this.loaderError(false);
            return ValidationTranslator.t("Необходимо загрузить изображение")();
          }
          return ValidationTranslator.t("Необходимо добавить хотя бы одно изображение")();
        }
      },
    });
    this.showImages.subscribe((v) => {
      if (!v) this.images.removeAll();
    });
    this.imagesBackground = ko.observable("#FFFFFF");
    
    this.loader.on("select", ({ file }) => {
      this.loadFile(file);
    });

    this.loader.on("foquz.file.loader.errors.limit", (param) => {
      this.loaderError(true);
    });
    
    this.activeImage = ko.observable(null);

    this.subscriptions.push(
      this.enableShare.subscribe((v) => this.share.refresh())
    );



    [
      this.blockType,
      this.showNumber,
      this.text,
      this.hasComplainButton,
      this.hasUnsubscribeButton,
      this.hasReadyButton,
      this.hasPointsButton,
      this.hasStartOverButton,
      this.hasCloseButtonForWidget,
      this.show_image_button_text,
      this.image_show_time,
      this.show_bg_instruction,
      ...Object.values(this.texts),
      this.enableShare,
      this.readyLink,
      this.showImages,
      this.images,
      this.imagesBackground,
      this.agreement,
      this.agreementText,
    ].forEach((f) => {
      f.subscribe((v) => this.onChange());
    });

    this.share.on("change", () => {
      this.onChange();
    });
  }

  createImage() {
    let img = new InterblockImage(this.id);
    let cb = () => this.onChange();
    img.on("changeData", cb);
    let dispose = img.dispose.bind(img);
    img.dispose = () => {
      dispose();
      img.off("changeData", cb);
    };
    this.loaderError(false);
    return img;
  }

  removeImage(img) {
    img.dispose();
    this.images.remove(img);
    this.onChange();
  }

  loadFile(file) {
    let img = this.createImage();

    img
      .loadFile(file)
      .then(() => {
        this.images.push(img);
      })
      .catch((err) => {});
  }

  selectImage() {
    // Если это режим five-second-test, то ограничиваем загрузку одним изображением
    if (this.blockType() === "five-second-test" && this.images().length > 0) {
      if (confirm(this.translator.t("Вы уже загрузили изображение. Заменить его?")())) {
        this.images.removeAll();
        this.loader.open();
      }
    } else {
      this.loader.open();
    }
  }

  selectImageLink() {
    const dialog = this.openDialog({
      name: "link-dialog",
      params: {
        submit: ({ link }) => {
          return new Promise((res, rej) => {
            let _img = new Image();
            _img.onload = () => {
              res();
            };
            _img.onerror = () => {
              rej("Не удается загрузить изображение");
            };
            _img.src = link;
          });
        },
      },
      events: {
        submit: ({ link }) => {
          dialog.hide();
          
          setTimeout(() => {
            // Если это режим five-second-test, то сначала удаляем все существующие изображения
            if (this.blockType() === "five-second-test" && this.images().length > 0) {
              if (confirm(this.translator.t("Вы уже загрузили изображение. Заменить его?")())) {
                this.images.removeAll();
                
                let img = this.createImage();
                img.loadByLink(link).then(() => {
                  this.images.push(img);
                  this.onChange();
                });

                console.log("images", this.images());
              }
            } else {
              let img = this.createImage();
              img.loadByLink(link).then(() => {
                this.images.push(img);              
                this.onChange();
              });
            }
          }, 500);
        },
      },
    });
  }

  setType(type) {
    this.blockType(type);
  }

  dispose() {
    super.dispose();
    this.share.dispose();
  }

  get isInterblock() {
    return true;
  }

  get hasMainValidation() {
    return false;
  }

  get needRequiredSetting() {
    return false;
  }

  updateCommonData(data, onCreate) {
    super.updateCommonData(data);
    if (onCreate) {
      this.name("");
    }
  }

  updateData(data) {
    this.updating(true);
    super.updateData(data);

    let interBlock = data.interBlock;
    
    console.log('updateData interBlock:', interBlock);

    if (interBlock) {
      this.savedBlockType(interBlock.type);
      this.blockType(interBlock.type);
      this.showNumber(interBlock.showNumber);
      this.text(interBlock.text);
      this.hasComplainButton(interBlock.hasComplainButton);
      this.hasUnsubscribeButton(interBlock.hasUnsubscribeButton);
      this.hasReadyButton(interBlock.hasReadyButton);
      this.hasPointsButton(interBlock.hasPointsButton);
      this.hasStartOverButton(interBlock.hasStartOverButton);
      this.hasCloseButtonForWidget(interBlock.hasCloseButtonForWidget);
      this.texts.complain(interBlock.texts.complain);
      this.texts.unsubscribe(interBlock.texts.unsubscribe);
      this.texts.takeSurvey(interBlock.texts.takeSurvey);
      this.texts.ready(interBlock.texts.ready);
      this.texts.points(interBlock.texts.points);
      this.texts.startOver(interBlock.texts.startOver);
      this.texts.closeButtonForWidget(interBlock.texts.closeButtonForWidget);
      this.readyLink(interBlock.readyLink || "");
      this.agreement(interBlock.agreement);
      this.agreementText(interBlock.agreementText || "");
      
      // Загружаем время показа изображения
      if (interBlock.image_show_time !== undefined) {
        this.image_show_time(interBlock.image_show_time);
      }
      
      // Загружаем текст кнопки показа изображения
      if (interBlock.show_image_button_text !== undefined) {
        this.show_image_button_text(interBlock.show_image_button_text);
      }
      
      // Загружаем флаг отображения инструкции на подложке
      if (interBlock.show_bg_instruction !== undefined) {
        this.show_bg_instruction(interBlock.show_bg_instruction);
      }

      if (interBlock.imagesBackground !== undefined) {
        this.imagesBackground(interBlock.imagesBackground);
      }

      this.promocode.update(interBlock.promocode);

      this.enableShare(interBlock.enableShare);
      this.share.setData(interBlock.share);

      // Для режима five-second-test всегда устанавливаем showImages в true
      if (interBlock.type === "five-second-test") {
        this.showImages(true);
      } else {
        this.showImages(interBlock.showImages);
      }

      let images = [...interBlock.images];
      images.sort((a, b) => a.position - b.position);

      // Очищаем текущие изображения
      this.images.removeAll();

      // Добавляем новые изображения
      const newImages = images.map((iData) => {
        let img = this.createImage();
        img.setData(iData);
        return img;
      });
      
      this.images(newImages);
    }

    setTimeout(() => {
      this.updating(false);
      this.onChange();
    });
  }

  getData() {
    let interBlock = {
      type: this.blockType(),
      showNumber: this.showNumber(),
      text: this.text(),
      hasComplainButton: this.hasComplainButton(),
      hasUnsubscribeButton: this.hasUnsubscribeButton(),
      hasReadyButton: this.hasReadyButton(),
      hasPointsButton: this.hasPointsButton(),
      hasCloseButtonForWidget: this.hasCloseButtonForWidget(),
      hasStartOverButton: this.hasStartOverButton(),
      texts: ko.toJS(this.texts),
      readyLink: this.readyLink(),
      enableShare: this.enableShare(),
      share: Share.serverFormatter(this.share),
      promocode: this.promocode.getData(),
      showImages: this.showImages(),
      images: this.images()
        .filter((i) => i.fileUrl())
        .map((i) => i.getData()),
      imagesBackground: this.imagesBackground(),
      agreement: this.agreement(),
      agreementText: this.agreementText(),
      // Добавляем время показа изображения
      image_show_time: this.image_show_time(),
      // Добавляем текст кнопки показа изображения
      show_image_button_text: this.show_image_button_text(),
      // Добавляем флаг отображения инструкции на подложке
      show_bg_instruction: this.show_bg_instruction(),
    };
    
    // Для режима five-second-test устанавливаем showImages в true, чтобы изображения сохранились
    if (this.blockType() === "five-second-test") {
      interBlock.showImages = true;
    }

    return {
      ...super.getData(),
      interBlock,
    };
  }

  isValid() {
    // Для режима five-second-test проверяем наличие одного изображения
    if (this.blockType() === "five-second-test") {
      if (!this.images.isValid()) return false;
      if (this.images().length !== 1) return false;
      if (this.images()[0] && !this.images()[0].isValid()) return false;
    } else {
      // Для обычного режима проверяем как раньше
      if (!this.images.isValid()) return false;
      if (this.images().some((i) => !i.isValid())) return false;
    }

    return (
      this.alias.isValid() && this.text.isValid() && this.readyLink.isValid()
    );
  }
}
