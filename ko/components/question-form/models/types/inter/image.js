import { FoquzLoaderWithFile } from "Models/file-loader/loader-with-file";
import ee from "event-emitter";
import { ApiUrl } from "Utils/url/api-url";

const img_array = [];

export class InterblockImage {
  constructor(qId) {
    this.questionId = qId;
    ee(this);

    this.fileId = null;
    this.fileUrl = ko.observable();

    this.loading = ko.observable(false);

    this.naturalWidth = ko.observable(null);
    this.naturalHeight = ko.observable(null);

    this.savedWidth = ko.observable(null);
    this.savedHeight = ko.observable(null);

    this.ratio = ko.computed(() => {
      let w = this.savedWidth() || this.naturalWidth();
      let h = this.savedHeight() || this.naturalHeight();

      if (!h) return null;
      return w / h;
    });

    this.loader = new FoquzLoaderWithFile((file) => {
      if (file) {
        return this.loadFile(file);
      } else {
        this.removeFile();
        return Promise.resolve();
      }
    });

    this.link = ko.observable("").extend({
      validation: {
        validator: (v) => {
          if (!v) return true;
          return true
          return validator.isURL(v);
        },
        message: "Неверный формат",
      },
    });
    this.width = ko.observable("");
    this.height = ko.observable("");
    this.description = ko.observable("");

    this.lock = ko.observable(false);

    this.lock.subscribe((v) => {
      if (!v) return;

      this.savedWidth(this.width());
      this.savedHeight(this.height());
    });

    this.imgIndex = img_array.length;

    let img = new Image();
    img_array.push(img);
    img.onload = () => {
      this.naturalWidth(img.naturalWidth);
      this.naturalHeight(img.naturalHeight);
      // this.loading(false);
    };

    /** Сохранение пропорций */
    this.width.subscribe((v) => {
      if (!this.lock()) return;
      let ratio = this.ratio();
      if (!ratio) return;
      if (!v) {
        this.height(this.savedHeight());
      } else {
        let newHeight = Math.round(v / ratio);
        this.height(newHeight);
      }
    });

    this.height.subscribe((v) => {
      if (!this.lock()) return;
      let ratio = this.ratio();
      if (!ratio) return;
      if (!v) {
        this.width(this.savedWidth());
      } else {
        let newWidth = Math.round(v * ratio);
        this.width(newWidth);
      }
    });

    this.isValid = this.link.isValid;

    [
      this.width,
      this.height,
      this.fileUrl,
      this.link,
      this.description,
      this.fileUrl,
    ].forEach((f) => {
      f.subscribe(() => this.emit("changeData"));
    });
  }

  get img() {
    return img_array[this.imgIndex];
  }

  setFileData(data) {
    console.log('set file data', { data })
    this.fileId = data.id;
    
    this.fileUrl(data.logo || data.external_logo);
    this.loader.setFile(this.fileUrl());

    // this.loading(true);

    this.img.src = this.fileUrl();
    
  }

  loadFile(file) {
    return new Promise((res, rej) => {
      let fd = new FormData();
      fd.append("logo", file);

      $.ajax({
        url: ApiUrl("questions/upload-end-screen-logo"),
        method: "POST",
        data: fd,
        processData: false,
        contentType: false,
        success: (response) => {
          if (response.error) {
            rej(response.error);
            return;
          }
          this.setFileData(response);

          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          rej(response.responseJSON);
        },
      });
    });
  }

  loadByLink(link) {
    return new Promise((res) => {
      $.ajax({
        url: ApiUrl("questions/upload-end-screen-logo-by-link"),
        data: {
          externalLogo: link,
        },
        method: "POST",
        success: (response) => {
          this.setFileData(response.media);
         
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      });
    });
  }

  removeFile() {
    if (!this.fileId) return;
    // $.ajax({
    //   url: ApiUrl('questions/delete-end-screen-logo', { id: this.fileId })
    // });
    this.lock(false);
    this.ratio = null;
    this.fileId = null;
    this.fileUrl("");
    this.width("");
    this.height("");
    this.naturalWidth("");
    this.naturalHeight("");
    this.savedWidth("");
    this.savedHeight("");
  }

  setData(iData) {
    this.setFileData(iData);

    this.link(iData.link);
    this.width(iData.width);
    this.height(iData.height);
    this.description(iData.description);
  }

  getData() {
    if (!this.fileId) return null;
    return {
      id: this.fileId,
      link: this.link().trim(),
      width: this.width(),
      height: this.height(),
      description: this.description().trim(),
      logo: this.fileUrl(),
    };
  }

  dispose() {}
}
