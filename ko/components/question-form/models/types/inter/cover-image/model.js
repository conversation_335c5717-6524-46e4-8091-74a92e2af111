import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.image = params.image;
    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher || commonFormControlErrorStateMatcher();
    this.disabled = params.disabled;
  }

  onRemove() {
    this.emitEvent('remove')
  }
}
