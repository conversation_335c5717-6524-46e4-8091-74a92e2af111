<!-- ko ifnot: disabled -->
<div class="drag-handle">
  <svg-icon params="name: 'drag-arrow', width: 6, height: 16"></svg-icon>
</div>
<!-- /ko -->

<div class="interscreen-image__image">
  <media-load-button class="sm" params="loader: image.loader, disabled: disabled">
    <svg-icon params="name:'image-light', width: 46, height: 46"></svg-icon>
  </media-load-button>

  <file-loader-error params="error: image.loader.error"></file-loader-error>
</div>

<div class="interscreen-image__content">
  <div class="form-group">
    <label class="form-label">
      <span data-bind="text: _t('questions', 'Ссылка на внешний ресурс')"></span>
      <question-button params="text: _t('questions', 'Ссылка на внешний ресурс')"></question-button>
    </label>

    <input type="text"
           class="form-control"
           placeholder="https://..."
           data-bind="textInput: image.link, css: {
             'is-invalid': false
           }, disable: disabled">

    <validation-feedback params="show: formControlErrorStateMatcher(image.link), text: image.link.error"></validation-feedback>
  </div>

  <div class="form-group ">
    <label class="form-label">
      <span data-bind="text: _t('questions', 'Размеры')"></span>
      <span class="font-weight-normal">(<span data-bind="text: _t('questions', 'ширина x высота')"></span>)</span>
      <question-button params="text: _t('questions', 'Размеры') + ' (' +_t('questions', 'ширина x высота')+ ')'">
      </question-button>
    </label>

    <div class="interscreen-image__sizes">
      <input type="text"
             class="form-control"
             data-bind="textInput: image.width, onlyNumbers, attr: {
               placeholder: image.naturalWidth
             }, disable: disabled">

      <span class="f-fs-3">x</span>

      <input type="text"
             class="form-control"
             data-bind="textInput: image.height, onlyNumbers, attr: {
               placeholder: image.naturalHeight
             }, disable: disabled">

      <button class="button-ghost interscreen-image__lock" data-bind="click: function() {
        image.lock(!image.lock())
      }, disable: image.loading() || !image.fileUrl() || disabled">
        <!-- ko if: image.lock -->
        <svg-icon params="name: 'lock-closed', width: 18, height: 20" class="f-color-primary"></svg-icon>
        <!-- /ko -->

        <!-- ko ifnot: image.lock -->
        <svg-icon params="name: 'lock-open', width: 24, height: 20" class="f-color-service"></svg-icon>
        <!-- /ko -->
      </button>
    </div>

    <!-- <div class="f-color-service f-fs-1 mt-5p" data-bind="text: _t('questions', 'Если нужны исходные размеры изображения, оставьте в полях 0')"></div> -->
  </div>

  <div class="">
    <label class="form-label">
      <span data-bind="text: _t('questions', 'Описание изображения')"></span>
      <question-button params="text: _t('questions', 'Описание изображения')"></question-button>
    </label>

    <foquz-chars-counter params="value: image.description, max: 500">
      <textarea class="form-control" data-bind="textInput: $parent.image.description, disable: $parent.disabled, autosizeTextarea"></textarea>
    </foquz-chars-counter>
  </div>
</div>

<div class="interscreen-image__close">
  <!-- ko ifnot: disabled -->
  <button class="f-btn f-btn--square f-btn--rect f-btn-danger"
          data-bind="click: function() { onRemove(); }">
    <svg-icon params="name: 'times'" class="svg-icon--sm"></svg-icon>
  </button>
  <!-- /ko -->
</div>
