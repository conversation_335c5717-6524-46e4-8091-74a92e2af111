import { get as _get, uniq as _uniq } from 'lodash';
import GalleryQuestion from "../gallery";

import { STAR_VARIANTS_QUESTION } from "@/data/question-types";

import { Donor } from "../../donor";
import { VariantsListModel } from "@/components/question-form/models/variants-list";
import { VariantsController } from "../../../controllers/variants-controller";
import { Translator } from "@/utils/translate";

import "./style.less";

const ValidationTranslator = Translator("validation");

const starDefaultColor = "#F8CD1C";
class VariantsQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };

    super(controller, config);

    // question type
    this.type = STAR_VARIANTS_QUESTION;

    this.variantsList = VariantsListModel();
    

    // donor
    this.donor = Donor(controller.donors);
    this.blockRecipients = controller.blockRecipients;
    this.disableDonors = ko.computed(() => {
      if (this.isFullBlocked) return true;
      if (ko.toJS(this.isBlocked)) return true;
      if (this.blockRecipients) return true;
      if (!this.donor.donorsList().length) return true;
      return false;
    });
    this.donorVariants = ko.observableArray([]);
    this.donor.donorId.subscribe(v => {
      this.updateVariantsList(
        v,
        v ?
          this.donor.getDonorVariants(v)
            .map((dv, index) => {
              return {
                id: index,
                donorId: dv.id === "custom" ? null : dv.id,
                value: dv.value(),
                points: dv.points(),
                needExtra: dv.needExtra(),
                extra_question: 0,
              };
            }) :
          [],
      );
    });

    // options
    this.randomOrder = ko.observable(false);

    // stars
    this.color = ko.observable(starDefaultColor);
    this.count = ko.observable(5);

    this.size = ko.observable("md");

    this.count.subscribe((v) => {
      if (v > 5 && this.size() == "lg") {
        this.size("md");
      }
      this.clarifyingQuestionForRates([1, v]);
    });

    this.labels = Array(10)
      .fill()
      .map((_, i) => {
        let label = utils.declOfNum(i + 1, ["звезда", "звезды", "звезд"]);

        return {
          value: ko.observable(""),
          label: this.translator.t(`{count} ${label}`, {
            count: i + 1,
          }),
        };
      });

    this.visibleLabels = ko.pureComputed(() => {
      return this.labels.slice(0, this.count());
    });

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");
    this.skipVariant = ko.observable(false);

    // gallery
    this.galleryEnabled = ko.observable(false);

    this.usesDictionaryElements = ko.computed(() => {
      if (this.donor.useDonor()) {
        return !!this.donorVariants().find(el => el.dictionaryElementId);
      }
      return !!this.variantsList.list().find(el => el.dictionaryElementId);
    }, this);

    // this.dictionaryBadgeTooltipText = ko.computed(() => {
    //   if (!controller.question()) {
    //     return '';
    //   }
    //   return `
    //     ${controller.question().dictionaryElementId() ? `Связка вопроса с элементом ${controller.dictionary().name} справочника ${controller.dictionary().name}` : ''}
    //     ${controller.question().usesDictionaryElements() ? `${controller.question().dictionaryElementId() ? '.' : ''} В вопросе есть варианты, связанные с элементами справочника ${controller.dictionary().name}` : ''}
    //   `;
    // }, this);

    this.clarifyingQuestionEnabled = ko.observable(false);
    this.clarifyingQuestionText = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => this.clarifyingQuestionEnabled(),
      },
    });
    this.clarifyingQuestionForRates = ko.observable([1, 5]);
    this.clarifyingQuestionForAllVariants = ko.observable(true);
    this.clarifyingQuestionController = new VariantsController(
      {
        withCustomAnswer: true,
        withVariantsTypeSelect: true,
        withAddVariantButton: true,
        addFirstVariant: true,
        variantsType: "single",
        withTextAnswer: true,
      },
      this
    );
    this.clarifyingQuestionForAllRates = ko.observable(true);
    this.clarifyingQuestionIsRequired = ko.observable(1);

    this.clarifyingQuestionEnabled.subscribe((v) => {
      if (!v) this.clarifyingQuestionForAllRates(false);
      else this.commentEnabled(false);
    });
    this.commentEnabled.subscribe((v) => {
      if (v) this.clarifyingQuestionEnabled(false);
    });

    this.commentIsRequired = ko.observable(null);

    // subscriptions
    [
      this.galleryEnabled,
      this.clarifyingQuestionEnabled,
      this.clarifyingQuestionForAllRates,
      this.clarifyingQuestionIsRequired,
      this.clarifyingQuestionText,
      this.randomOrder,
      this.donorVariants,
      this.color,
      this.count,
      this.size,
      ...this.labels.map((l) => l.value),
      this.skip,
      this.skipText,
      this.skipVariant,
      this.commentIsRequired,
      this.clarifyingQuestionForRates,
    ].forEach((f) => {
      this.subscriptions.push(f.subscribe(() => this.onChange(true)));
    });
    this.subscriptions.push(
      this.variantsList.isChanged.subscribe((v) => this.onChange(true)),
      this.donor.isChanged.subscribe((v) => this.onChange(true))
    );
    [this.clarifyingQuestionController].forEach((c) =>
      c.isChanged.subscribe((v) => this.onChange())
    );
    this.skip.subscribe((v) => {
      if (!v) {
        this.skipVariant(v);
      }
    });
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  onDonorsTogglerClick() {
    if (this.isFullBlocked) return false;
    if (ko.toJS(this.isBlocked)) {
      this.tryChangeBlockedParam();
    } else if (this.blockRecipients) {
      this.tryChangeBlockedParam();
    } else if (!this.donor.donorsList().length) {
      this.noDonorsInfo();
    } else return true;
  }

  getLogicVariants(ids) {
    return this.variantsList
      .list()
      .filter((variant) => {
        return ids.find((id) => variant.id == id);
      })
      .map((variant) => variant.value);
  }

  updateVariantsList(donorId, variants) {
    if (donorId) {
      const donorType = window.QUESTIONS.find(i => i.id == donorId).main_question_type
      const donorVariants = this.donor.getDonorVariants(donorId);
      let currentOrder
      if (donorType !== 19) {

        currentOrder = variants.map((v) => v.donorId || "custom");
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(a.id);
          const bIndex = currentOrder.indexOf(b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      } else {
        const donorIds = donorVariants.map(i => +i.id)
        const temp_variants = variants.filter(i => donorIds.includes(i.dictionary_element_id)).sort((a, b) => a.position - b.position)
        currentOrder = temp_variants.map((v) => v.dictionary_element_id);
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(+a.id);
          const bIndex = currentOrder.indexOf(+b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      }

        
        donorVariants.forEach((variant) => {
          const data = variants.find((v) => {
            if (variant.id === "custom") return !v.donorId;
            return v.donorId === variant.id;
          });

          if (data) {
            variant.points(data.points);
          }
        });
      
      
      this.donorVariants(donorVariants);
    } else {
      
      this.variantsList.update(variants);
    } 
  }

  updateData(data) {
    super.updateData(data);

    const {
      variants,
      randomOrder,
      galleryEnabled,

      donorId,
      donorVariantsType,
      starsConfig,
    } = data;

    this.galleryEnabled(galleryEnabled);

    let clarifyingQuestion = data.clarifyingQuestion;

    this.clarifyingQuestionEnabled(clarifyingQuestion.enabled);
    this.clarifyingQuestionForAllRates(clarifyingQuestion.forAllRates);
    this.clarifyingQuestionIsRequired(clarifyingQuestion.required);
    this.clarifyingQuestionText(clarifyingQuestion.text || "");
    this.clarifyingQuestionController.updateData({
      type: clarifyingQuestion.variantsType,
      variants: clarifyingQuestion.variants,
      customAnswerEnabled: clarifyingQuestion.customAnswerEnabled,
      customAnswerRange: clarifyingQuestion.customAnswerLengthRange,
      customAnswerPlaceholder: clarifyingQuestion.customAnswerPlaceholder,
      customAnswerLabel: clarifyingQuestion.customAnswerLabel,
    });

    this.donor.update({ donorId, donorVariantsType });

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.galleryCommentRequired)
    }

    this.updateVariantsList(donorId, variants);

    if (_uniq(this.variantsList.getVariants().map(el => el.needExtra())).length === 1) {
      this.clarifyingQuestionForAllVariants(this.variantsList.getVariants()[0].needExtra());
    } else {
      this.clarifyingQuestionForAllVariants(false);
    }

    this.randomOrder(randomOrder);

    if (starsConfig) {
      this.color(starsConfig.color || starDefaultColor);
      this.count(starsConfig.count || 5);
      this.size(starsConfig.size || "md");
      if (starsConfig.extraQuestionRateFrom && starsConfig.extraQuestionRateTo) {
        this.clarifyingQuestionForRates([starsConfig.extraQuestionRateFrom, starsConfig.extraQuestionRateTo]);
      } else {
        this.clarifyingQuestionForRates([1, starsConfig.count || 5]);
      }

      let labels = starsConfig.labels || [];
      this.labels.forEach((label, index) => {
        let labelText = labels[index];
        label.value(labelText || "");
      });
    }

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
    this.skipVariant(!!data.skipVariant);
  }

  getData() {
    const parentData = super.getData();
    const galleryEnabled = this.galleryEnabled() ? 1 : 0;

    let data = {
      ...parentData,
      randomOrder: this.randomOrder(),
      galleryEnabled,
      gallery: galleryEnabled ? parentData.gallery : [],
      starsConfig: {
        color: this.color(),
        count: this.count(),
        size: this.size(),
        labels: this.visibleLabels().map((l) => l.value()),
      },
    };

    if (
      this.clarifyingQuestionEnabled() &&
      !this.clarifyingQuestionForAllRates() &&
      this.clarifyingQuestionForRates()[0] &&
      this.clarifyingQuestionForRates()[1]
    ) {
      data.starsConfig['extra_question_rate_from'] = this.clarifyingQuestionForRates()[0];
      data.starsConfig['extra_question_rate_to'] = this.clarifyingQuestionForRates()[1];
    }

    data.clarifyingQuestion = {
      enabled: this.clarifyingQuestionEnabled(),
      required: this.clarifyingQuestionIsRequired(),
      forAllRates: this.clarifyingQuestionForAllRates(),
      text: this.clarifyingQuestionText(),
      ...this.clarifyingQuestionController.getData(),
    };

    data.comment = {
      enabled: this.commentEnabled(),
      ...this.commentField.getData(),
      label: this.commentLabel(),
    };


    if (this.donor.useDonor()) {
      const donorId = ko.unwrap(this.donor.donorId)
      const donorQestion = window.QUESTIONS.find(i => i.id == donorId)
      const isDonorClassifierWithListType = donorQestion?.main_question_type === 19 && donorQestion?.dictionary_list_type === 'list'

      data = {
        ...data,
        ...this.donor.getData(),
        variants: [
          ...ko.toJS(this.donorVariants).map((v, index) => {
            if (v.id === "custom") {
              return {
                value: v.value,
                points: v.points || "",
                needExtra: _get(this.variantsList.list(), `[${index}].needExtra`, false) || v.needExtra,
              };
            }
            const res = {
              id: v.id,
              value: v.value,
              points: v.points || "",
              needExtra: v.needExtra,
            };
            return res
          }),
        ],
        isDonorClassifierWithListType,
      };
    } else {
      data = {
        ...data,
        variants: [
          ...this.variantsList.getVariants(),
        ],
        donor: null,
      };
    }

    data.skip = this.skip();
    data.skipText = this.skipText();
    data.skipVariant = this.skipVariant();

    return data;
  }

  addVariantsFromDictionary() {
    this.openDialog({
      name: "add-variants-list-dialog",
      params: {
        hasAnswers: parseInt(this.countAnswers) > 0,
        mode: 'dictionary',
        checked: this.variantsList.list().filter(el => el.dictionaryElementId).map(el => `${el.dictionaryElementId}`),
        headerText: 'Добавление вариантов из справочника',
        questionController: this.controller,
      },
      events: {
        submit: async (result) => {
          this.controller.dictionaryElements()
            .filter(el => result.newDetails.find(detail => detail == el.id))
            .forEach(el => {
              if (this.variantsList.list().find(variant => variant.dictionaryElementId == el.id)) {
                return;
              }
              this.variantsList.addExternalVariant(
                el.title,
                undefined,
                {
                  dictionary_element_id: el.id,
                }
              )
            });
        },
      },
    });
  }

  isVariantsValid() {
    return this.variantsList.isVariantsValid();
  }

  isValid() {
    if (!this.defaultValidation()) return false;

    if (!this.clarifyingQuestionText.isValid()) return false;

    if (this.clarifyingQuestionEnabled()) {
      if (!this.clarifyingQuestionController.isValid()) return false;
    }

    if (this.galleryEnabled()) {
      if (!super.isValid()) return false;
    }

    if (this.donor.useDonor()) {
      return true;
    }

    return this.variantsList.isValid();
  }

  onChangeClarifyingQuestionForAllVariants() {
    this.variantsList.getVariants().forEach(variant => {
      variant.needExtra(this.clarifyingQuestionForAllVariants());
    });
  }

  onChangeNeedExtra(data, index) {
    setTimeout(() => {
      this.onChange();
    }, 100)
    if (data.needExtra() && !this.clarifyingQuestionForAllVariants() && !this.variantsList.getVariants().find(el => !el.needExtra())) {
      this.clarifyingQuestionForAllVariants(true);
      return;
    }
    if (!data.needExtra() && this.clarifyingQuestionForAllVariants() && !this.variantsList.getVariants().find((el, idx) => idx !== index && !el.needExtra())) {
      this.clarifyingQuestionForAllVariants(false);
      return;
    }
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}

export default VariantsQuestion;
