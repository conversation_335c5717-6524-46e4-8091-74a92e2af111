<template id="question-form-template-file">
    <!-- ko let: {$translator: question.translator} -->
    <!-- Табы: фото/видео/фото и видео -->
    <!-- <div class="hat-radio-group hat-radio-group--dense survey-question__variants-type-selector" data-bind="click: function () {
        console.log(question.fileTypes)
        console.log(question.fileType())
    }">
    
    </div> -->
    <div class="row">
        <div class="col-12">
            <div class="form-group">
                <fc-label params="text:'Типы загружаемых файлов', hint: 'Типы загружаемых файлов'" ></fc-label>
                <fc-select params=" 
                options: question.fileTypes, 
                value: question.fileType_,
                disabled: false,
                placeholder: 'Все типы', 
                multiple: true, 
                "></fc-select>
            </div>
        </div>
    </div>
    <!-- /Табы: фото/видео/фото и видео -->

    <div class="form-group">
        <label class="form-label" for="name">
            <span data-bind="text: $translator.t('Максимальное кол-во файлов')"></span>

            <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно ограничить количество файлов, которое может загрузить респодент при ответе на вопрос')" type="button" title="">
            </button>
        </label>

        <div class="row">
            <div class="col-6">
                <input class="form-control" data-bind="textInput: question.maxFilesCount, disable: question.isFullBlocked" id="name" maxlength="99" max="999" minlength="0" min="1" step="1" type="number" onblur="this.value = Math.abs(parseInt(this.value) || 1)">
            </div>
        </div>
    </div>

    <hr>

    <!-- Комментарий -->
    <div class="mt-4">
        <switch params="checked: question.commentEnabled, disabled: question.isFullBlocked">
            <span data-bind="text: $translator.t('Комментарий')"></span>
        </switch>
    </div>

    <!-- ko template: {
        foreach: templateIf(question.commentEnabled(), $data),
        afterAdd: slideAfterAddFactory(200),
        beforeRemove: slideBeforeRemoveFactory(200)
    } -->
    <div>
        <div class="row pb-4">
            <div class="col">
            <div class="f-check">
                <input
                type="checkbox"
                id="comment-required"
                class="f-check-input"
                data-bind="
                    checked: question.commentIsRequired,
                    disable: question.isFullBlocked,
                    event: { change: function() { 
                    question.updateCommentRequired() } }
                "
                />
                <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
            </div>
            </div>
        </div>
        <div class="form-group">
            <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
            <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
        </div>

        <!-- ko component: {
            name: 'text-field',
            params: {
                controller: question.commentField,
                intervalText: $translator.t('Кол-во символов в комментарии'),
                disabled: question.isFullBlocked
            }
        } -->
        <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /Комментарий -->
    <!-- /ko -->
</template>