import Question from '../../question';
import TextField from '../../text-field';
import { FILE_QUESTION } from 'Data/question-types';

import { Translator } from '@/utils/translate';

const FILE_TYPE_PHOTO = '0';
const FILE_TYPE_VIDEO = '1';
const FILE_TYPE_AUDIO = '2';


const QuestionTranslator = Translator('question');

class FileQuestion extends Question {
  constructor(controller, config) {
    super(controller, config);

    this.type = FILE_QUESTION;
    this.fileType_ = ko.observableArray([])

    this.fileType = ko.observableArray([]);
    this.fileTypes = [
      {
        id: FILE_TYPE_PHOTO,
        label: QuestionTranslator.t('Изображения')(),
        text: ko.observable(QuestionTranslator.t('Изображения')()),
      },
      {
        id: FILE_TYPE_VIDEO,
        label: QuestionTranslator.t('Видео')(),
        text: ko.observable(QuestionTranslator.t('Видео')()),
      },
      {
        id: FILE_TYPE_AUDIO,
        label: QuestionTranslator.t('Аудио')(),
        text: ko.observable(QuestionTranslator.t('Аудио')()),
      }
    ];

    this.maxFilesCount = ko.observable(4);

    this.commentEnabled = ko.observable(false);
    this.commentField = new TextField();
    this.commentLabel = ko.observable('');
    this.commentIsRequired = ko.observable(null);

    [this.fileType_, this.maxFilesCount, this.commentEnabled, this.commentLabel, this.commentIsRequired].forEach((f) =>
      f.subscribe((v) => this.onChange())
    );

    this.commentField.isChanged.subscribe((v) => {
      if (v) this.onChange();
    });
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  updateData(data) {
    super.updateData(data);

    [
      'fileCommentEnabled',
      'maxFilesCount',
      'fileType',
      'fileCommentLengthRange',
      'fileCommentPlaceholder'
    ].forEach((key) => {
      if (!(key in data))
        console.warn('File Question update warning: no param', key, data);
    });

    this.commentEnabled(data.fileCommentEnabled);
    this.commentLabel(data.fileCommentLabel || '');
    this.commentIsRequired(data.fileCommentRequired)
    this.maxFilesCount(data.maxFilesCount);
    this.fileType_(data.fileTypes);
    this.commentField.updateData({
      range: data.fileCommentLengthRange,
      placeholder: data.fileCommentPlaceholder || '',
      required: data.fileCommentRequired
    });
  }

  getData() {
    let data = super.getData();

    data.fileTypes = this.fileType_();
    data.maxFilesCount = this.maxFilesCount();
    data.fileCommentEnabled = this.commentEnabled();
    data.fileCommentLabel = this.commentLabel()

    let fieldData = this.commentField.getData();
    data.fileCommentLengthRange = fieldData.range;
    data.fileCommentPlaceholder = fieldData.placeholder;
    data.fileCommentRequired = this.commentIsRequired()
    
    return data;
  }

  isValid() {
    return super.isValid();
  }
}

export default FileQuestion;
