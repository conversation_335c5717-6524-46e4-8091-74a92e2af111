.filials-select {
  &-container {
    max-height: 615px;
    overflow: hidden;
    .os-host-overflow {
      max-height: 615px;
    }
  }
  
  .category-wrapper {
    margin-bottom: 20px;
  }
  .category-wrapper:last-child {
    margin-bottom: 0;
  }
  .category-header {
    display: flex;
    align-items: center;
  }

  .category-name {
    font-size: 16px;
    font-weight: bold;
    color: var(--f-color-text);
  }
  .category-stats {
    color: var(--f-color-service);
    font-size: 15px;
    white-space: nowrap;
    margin-left: 10px;
  }

  .category-wrapper .category-items {
    margin-top: 15px;
    padding-left: 36px;
  }
  .category-items {
    .fc-check {
      margin-bottom: 15px;
    }
    .fc-check:last-child {
      margin-bottom: 0;
    }
  }
  .category-toggler {
    margin-left: 10px;
  }

  &.filials-select--disabled {
    opacity: 0.5;
  }
}

.toggle-all-switcher {
  font-size: 16px;
  font-weight: bold;
  color: var(--f-color-text);
  &-stats {
    color: var(--f-color-service);
    font-size: 15px;
    white-space: nowrap;
    margin-left: 10px;
  }
}
