export function ViewModel(params, element) {
  const { filials, loaded, value, disabled, invalid, onRemove } = params;

  const filialsWithCategory = ko.computed(() => {
    const items = filials().filter(item => item.category.id != 0);
    return items;
  })

  const filialsWithoutCategory = ko.computed(() => {
    const [items] = filials().filter(item => item.category.id == 0);
    console.log(4548, items)
    return items.items;
  })

  const _categoriesVisibility = {};

  filialsWithCategory().forEach((item) => {
    _categoriesVisibility[item.category.id] = ko.observable(false);
  });

  const categoriesVisibility = ko.observable(_categoriesVisibility);

  filialsWithCategory.subscribe((v) => {
    const visibility = categoriesVisibility();
    v.forEach((item) => {
      const prev = visibility[item.category.id];
      if (!prev) {
        visibility[item.category.id] = ko.observable(false);
      }
    });
    categoriesVisibility(visibility);
  });

  function getCategoryState(item) {
    const _value = value();
    const categoryFilials = item.items.map((f) => f.id);
    const checkedCategoryFilials = categoryFilials.filter((fId) =>
      _value.includes(fId)
    );

    return {
      checkedCount: checkedCategoryFilials.length,
      checked: categoryFilials.length === checkedCategoryFilials.length,
      partial: checkedCategoryFilials.length > 0,
    };
  }

  return {
    filials: filialsWithCategory,
    filialsWithoutCategory,
    loaded,
    disabled,
    value,
    categoriesVisibility,

    getCategoryState,

    toggleCategory(item) {
      if (ko.toJS(disabled)) return;

      let _value = value();
      const state = getCategoryState(item);
      const isChecked = state.checked;

      const cb = () => {
        const categoryFilials = item.items.map((f) => f.id);
        _value = _value.filter((id) => !categoryFilials.includes(id));

        if (!isChecked) {
          _value = [..._value, ...categoryFilials];
        }

        value(_value);
      };

      if (isChecked && typeof onRemove === "function") {
        onRemove(item.items, cb);
      } else {
        cb();
      }
    },

    toggleFilial(filial) {
      if (ko.toJS(disabled)) return;
      const isChecked = value().includes(filial.id);
      if (isChecked) {
        const cb = () => {
          const newValue = value().filter((id) => id !== filial.id);
          value(newValue);
        };

        if (typeof onRemove) {
          onRemove(filial, cb);
        } else {
          cb();
        }
      } else {
        const newValue = [...value(), filial.id];
        value(newValue);
      }
    },
  };
}
