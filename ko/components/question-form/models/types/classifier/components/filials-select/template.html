<!-- ko ifnot: loaded -->
<div>Loading...</div>
<!-- /ko -->

<!-- ko if: loaded -->
<div
  class="filials-select "
  data-bind="
    let: {
      $select: $component,
    },
    css: {
      'filials-select--disabled': disabled,
    },
  "
>
  <!-- ko foreach: { data: filials, as: 'item' } -->
  <div class="category-wrapper">
    <fc-check
      params="
            checked: $select.getCategoryState(item).checked, 
            partial: $select.getCategoryState(item).partial, 
            onChange: function() {
                $select.toggleCategory(item)
            }"
    >
      <template data-slot="label">
        <div class="category-header">
          <span
            class="category-name"
            data-bind="text: item.category.name"
          ></span>
          <span class="category-stats">
            (<span
              data-bind="text: $select.getCategoryState(item).checkedCount"
            ></span>
            из
            <span data-bind="text: item.items.length"></span>)
          </span>

          <fc-expander
            data-bind="click: function(_, e) { 
              e.preventDefault();
              e.stopPropagation();
              return false; 
            }"
            class="category-toggler"
            params="open: $select.categoriesVisibility()[item.category.id]"
          ></fc-expander>
        </div>
      </template>
    </fc-check>

    <!-- ko template: {
      foreach: templateIf($select.categoriesVisibility()[item.category.id](), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400)
    } -->

    <div class="category-items">
      <!-- ko foreach: { data: item.items, as: 'filial' } -->
      <fc-check
        params="label: filial.name, 
            checked: $select.value().includes(filial.id), 
          
            onChange: function() {
                $select.toggleFilial(filial)
            }"
      ></fc-check>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <div class="category-items pl-0">
    <!-- ko foreach: { data: filialsWithoutCategory, as: 'filial' } -->
      <fc-check
        params="
          label: filial.name, 
          checked: $select.value().includes(filial.id), 
          onChange: () => $select.toggleFilial(filial)
        "
      ></fc-check>
    <!-- /ko -->
  </div>
</div>
<!-- /ko -->

