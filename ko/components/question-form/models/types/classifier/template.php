<template id="question-form-template-classifier">

    <div class="form-group" data-bind="let: { $question: question }">
        <fc-label params="text: 'Справочник', hint: 'Справочник'"></fc-label>
        <fc-select
            params="
                options: question.dictionaries, 
                value: question.dictionaryId, 
                clearable: true,
                invalid: question.formControlErrorStateMatcher(question.dictionaryId),
                placeholder: 'Выберите справочник',
                disabled: question.isFullBlocked || (controller.recipients && controller.recipients().length),
                searchable: true
            "
            data-bind="
                click: () => {
                    if (controller.recipients && controller.recipients().length) {
                        controller.openDonorTypeModal(true);
                    }
                }
            "
        >
            <template data-slot="option">
                <span data-bind="click: () => {
                    if ($data.disabled) {
                        $question.showEmptyCollectionModal($data);
                        return true;
                    }
                }">
                    <span data-bind="text: $data.text"></span>&nbsp;
                    <span class="f-color-service">(<span data-bind="text: $data.count"></span>)</span>
                </span>
            </template>
        </fc-select>
        <fc-error
            class="mb-4"
            params="
                show: question.formControlErrorStateMatcher(question.dictionaryId),
                text: question.dictionaryId.error,
            "
        ></fc-error>
    </div>

    <!-- ko template: {
       foreach: templateIf(question.dictionaryId(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
        <div>
            <div>
                <!-- ko template: {
                    foreach: templateIf(question.hasCategories(), $data),
                    afterAdd: slideAfterAddFactory(400),
                    beforeRemove: slideBeforeRemoveFactory(400)
                } -->
                <div class="form-group">
                    <fc-radio-group
                        params="
                            options: question.listTypes,
                            value: question.listType,
                            disabled: question.isFullBlocked,
                        "
                    ></fc-radio-group>
                </div>
                <!-- /ko -->


                <div class="form-group">
                    <fc-button-group
                        params="
                            block: true,
                            size: 'xl',
                            options: question.variantTypes, 
                            value: question.variantType,  
                            disabled: !question.hasVariantTypes() || question.isFullBlocked,
                        "
                        data-bind="click: () => question.infoDisabledVariantTypes()"
                    ></fc-button-group>
                </div>

                <fc-label params="text: 'Варианты ответов'"></fc-label>

                <!-- ko if: question.itemsLoading() || (question.isFilials() && !question.filialsLoaded()) -->
                    <fc-spinner class="mb-4 f-color-primary"></fc-spinner>
                <!-- /ko -->

                <!-- ko if: !question.isFilials() && !question.itemsLoading() -->
                    <foquz-checkbox
                        params="
                            checked: !!question.dictionaryItems().length,
                            partially: question.partiallyChecked,
                            disabled: question.isFullBlocked,
                            event: {
                                input: () => question.toggleAll(),
                            },
                        "
                    >
                        <span
                            class="toggle-all-switcher"
                            data-bind="text: question.translator.t('Выбрать все')"
                        ></span>
                        <span
                            class="toggle-all-switcher-stats"
                            data-bind="text: question.translator.t(`(${question.dictionaryItems().length} из ${question.dictionaryList().length})`)"
                        ></span>
                    </foquz-checkbox>

                    <hr class="mx-0 my-15p">

                    <div data-bind="nativeScrollbar" style="max-height: 550px">
                        <fc-checked-tree
                            params="
                                tree: question.dictionaryTree,
                                isFullBlocked: question.isFullBlocked,
                                showTooltips: question.showTooltips
                            "
                        ></fc-checked-tree>
                    </div>

                    <hr class="mx-0 my-15p">

                    <fc-error
                        class="mt-n2 mb-4"
                        params="
                            show: question.formControlErrorStateMatcher(question.dictionaryItems),
                            text: question.dictionaryItems.error
                        "
                    ></fc-error>
                <!-- /ko -->
                <!-- ko if: question.isFilials() && question.filialsLoaded() -->
                    <foquz-checkbox
                        params="
                            checked: !!question.selectedFilials().length,
                            partially: question.selectedFilials().length && question.selectedFilials().length !== question.filialsList().length,
                            disabled: question.isFullBlocked,
                            event: {
                                input: () => question.toggleAll(),
                            },
                        "
                    >
                        <span
                            class="toggle-all-switcher"
                            data-bind="text: question.translator.t('Выбрать все')"
                        ></span>
                        <span
                            class="toggle-all-switcher-stats"
                            data-bind="text: question.translator.t(`(${question.selectedFilials().length} из ${question.filialsList().length})`)"
                        ></span>
                    </foquz-checkbox>

                    <hr class="mx-0 my-15p">
                    
                    <div class="survey-question__variants-control-list-content" data-bind="nativeScrollbar" style="max-height: 550px">
                        <fc-filials-select
                            params="
                                filials: question.filials, 
                                loaded: question.filialsLoaded, 
                                value: question.selectedFilials, 
                                disabled: question.isFullBlocked, 
                                invalid: question.formControlErrorStateMatcher(question.selectedFilials),
                                onRemove: (items, cb) => {
                                    question.removeVariant(items, cb)
                                }
                            "
                        ></fc-filials-select>
                    </div>

                    <hr class="mx-0 my-15p">
                <!-- /ko -->

                <div class="form-group">
                    <fc-check
                        params="
                            checked: question.dropdown, 
                            disabled: question.isFullBlocked, 
                            label: question.translator.t('Варианты ответов выпадающим списком'),
                            hint: question.translator.t('Варианты ответов выпадающим списком')
                        "
                    ></fc-check>
                </div>

                <div class="row">
                    <div class="col col-6">
                        <div class="form-group">
                            <fc-label params="text: 'Сортировка', hint: 'Сортировка'"></fc-label>
                            <fc-select
                                params="
                                    options: question.sortTypes,
                                    value: question.sort,
                                    disabled: question.isFullBlocked
                                "
                            ></fc-select>
                        </div>
                    </div>
                    <!-- ko template: {
                        foreach: templateIf(question.variantType() == 1, $data),
                        afterAdd: fadeAfterAddFactory(400),
                        beforeRemove: fadeBeforeRemoveFactory(400)
                    } -->
                        <div class="col col-6">
                            <div class="form-group">
                                <fc-label params="text: 'Max кол-во выбранных ответов'"></fc-label>
                                <fc-input
                                    params="
                                        value: question.maxVariantsCount,
                                        mask: 'numbers',
                                        disabled: question.isFullBlocked,
                                    "
                                ></fc-input>
                            </div>
                        </div>
                    <!-- /ko -->
                </div>
                <!-- ko if: !question.isFilials() -->
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <fc-check
                                params="
                                    checked: question.showTooltips, 
                                    disabled: question.isFullBlocked, 
                                    label: question.translator.t('Показывать подсказки для элементов и категорий справочника'),
                                    hint: question.translator.t('Подсказки будут отображаться сбоку от всех категорий и элементов, у которых есть описание в выбранном справочнике')
                                "
                            ></fc-check>
                            <div class="form-label mt-10p">
                                <small
                                    class="form-label__note"
                                    data-bind="text: question.translator.t('Для этого должны быть заполнены поля описания для элементов и категорий ')"
                                ></small>
                                <a
                                    href="/foquz/settings?tab=settings&channel=email&setting=collections"
                                    target="_blank"
                                    class="form-label__note_link"
                                >
                                    справочника
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /ko -->
                <!-- ko template: {
                    foreach: templateIf(question.listType() == 'tree' && question.variantType() == 1 && !question.dropdown(), $data),
                    afterAdd: slideAfterAddFactory(400),
                    beforeRemove: slideBeforeRemoveFactory(400)
                } -->
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <fc-check
                                    params="
                                        checked: question.disableSelectCategory, 
                                        disabled: question.isFullBlocked, 
                                        label: question.translator.t('Запретить выбор категории/подкатегории до разворачивания вложенных уровней'),
                                        hint: question.translator.t('Выбор целой категории/подкатегории будет доступен, когда все вложенные элементы будут развёрнуты')
                                    "
                                ></fc-check>
                                <div class="form-label mt-10p">
                                    <small
                                        class="form-label__note"
                                        data-bind="text: question.translator.t('Опция доступна только для настроек «древовидный список»/«выбор нескольких вариантов»')"
                                    ></small>
                                </div>
                            </div>
                        </div>
                    </div>
                <!-- /ko -->
            </div>
        </div>
    <!-- /ko -->

    <hr class="mt-0 mx-0">

    <div>
        <div class="form-group">
            <fc-switch
                params="
                    checked: question.skip,
                    label: $translator.t('Пропуск ответа'),
                    disabled: question.isFullBlocked
                "
            ></fc-switch>
        </div>

        <!-- ko template: {
            foreach: templateIf(question.skip(), $data),
            afterAdd: slideAfterAddFactory(400),
            beforeRemove: slideBeforeRemoveFactory(400)
        } -->
            <div class="form-group">
                <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
                <fc-input
                    params="
                        value: question.skipText,
                        counter: true,
                        maxlength: 125,
                        placeholder: $translator.t('Затрудняюсь ответить'),
                        disabled: question.isFullBlocked,
                    "
                ></fc-input>
            </div>
        <!-- /ko -->
    </div>


    <hr class="mx-0">

    <!-- ko template: { name: 'gallery-question-gallery-template' } -->
    <!-- /ko -->

    <hr>

    <!-- Комментарий -->
    <div class="mt-4">
        <switch
            params="
                checked: question.commentEnabled,
                disabled: question.isFullBlocked
            "
        >
            <span data-bind="text: $translator.t('Комментарий')"></span>
        </switch>
    </div>

    <!-- ko template: {
        foreach: templateIf(question.commentEnabled(), $data),
        afterAdd: slideAfterAddFactory(200),
        beforeRemove: slideBeforeRemoveFactory(200)
    } -->
        <div>
            <div class="row pb-4">
                <div class="col">
                    <div class="f-check">
                        <input
                            type="checkbox"
                            id="comment-required"
                            class="f-check-input"
                            data-bind="
                                checked: question.commentRequaired,
                                disable: question.isFullBlocked,
                                event: { change: function() { 
                                question.updateCommentRequired() } }
                            "
                        />
                        <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
                <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
            </div>

            <!-- ko component: {
                name: 'text-field',
                params: {
                    controller: question.commentField,
                    intervalText: $translator.t('Кол-во символов в комментарии'),
                    disabled: question.isFullBlocked
                }
            } -->
            <!-- /ko -->
        </div>
    <!-- /ko -->
    <!-- /Комментарий -->
</template>

<template id="question-form-template-filials">
    <div data-bind="template: { name: 'question-form-template-classifier' }"></div>
</template>