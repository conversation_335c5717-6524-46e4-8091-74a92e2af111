import { FIRST_CLICK_TEST } from "Data/question-types";
import { FoquzImageLoader } from "Models/file-loader/image-loader";
import GalleryQuestion from "../gallery";
import { Translator } from "@/utils/translate";

class FirstClickTestQuestion extends GalleryQuestion {
  constructor(controller, config) {
    super(controller, config);

    this.type = FIRST_CLICK_TEST;
    this.translator = Translator("question");

    this.updating = ko.observable(false);

    // Image properties
    this.imageFile = ko.observable(null);
    this.imagePreview = ko.observable("");
    this.imageSrc = ko.observable("");
    this.originalImageDimensions = ko.observable({ width: 0, height: 0 }); // Store original dimensions
    this.clickAreas = ko.observableArray([]);

    // Валидация на наличие изображения
    this.imageFile.extend({
      validation: {
        validator: (value) => {
          // Проверяем, есть ли изображение или есть варианты в галерее
          return value || this.galleryController.variants().length > 0;
        },
        message: () => {
          return this.translator.t("Необходимо загрузить изображение")();
        },
      },
    });

    this.mobileDisplay = ko.observable("width"); // "width" | "height"
    this.minClicks = ko.observable(1);
    this.maxClicks = ko.observable("");
    this.displayTime = ko.observable("");
    this.buttonText = ko.observable("");
    this.allowClickCancel = ko.observable(false);

    this.changeClickAreasButtonText = ko.pureComputed(() => {
      const areasLength = this.clickAreas().length;

      if (areasLength === 0) {
        return ko.unwrap(this.translator.t("Добавить области клика"));
      }

      return ko.unwrap(this.translator.t("Изменить области клика")) + " (" + areasLength + ")";
    });

    this.initializeImageLoader();
    this.setupValidation();
    this.setupSubscriptions();
  }

  initializeImageLoader() {
    this.imageLoader = new FoquzImageLoader(this.imagePreview, {
      presets: ["imageWithSvg"],
      errors: {
        format: "Можно загружать файлы форматов: jpg, jpeg, png, gif, svg",
        limit: "До 5 Мб",
        required: this.translator.t("Необходимо загрузить изображение")(),
      },
    });

    this.imageLoader.on("select", ({ file }) => {
      this.imageLoader.updatePreview(file);
      this.handleImageSelect(file);
    });

    this.imageLoader.on("preview", ({ url }) => {
      this.imagePreview(url);
    });

    this.imageLoader.remove = () => {
      this.removeImage();
    };
  }

  setupValidation() {
    this.minClicks.extend({
      required: {
        params: true,
        message: "Минимальное количество кликов обязательно",
      },
      min: {
        params: 1,
        message: "Минимальное количество кликов должно быть больше 0",
      },
      max: {
        params: 999,
        message: "Максимальное значение: 999",
      },
    });

    this.maxClicks.extend({
      validation: {
        validator: (value) => {
          if (!value) return true;
          const min = parseInt(this.minClicks()) || 1;
          const max = parseInt(value);
          return max >= min;
        },
        message: "Максимальное количество кликов должно быть >= минимального",
      },
    });
  }

  setupSubscriptions() {
    this.minClicks.subscribe((newMin) => {
      const max = parseInt(this.maxClicks());
      const min = parseInt(newMin);
      if (max && max < min) {
        this.maxClicks(min.toString());
      }
    });

    this.maxClicks.subscribe((newMax) => {
      const max = parseInt(newMax);
      const min = parseInt(this.minClicks()) || 1;
      if (newMax && max < min) {
        this.maxClicks(min.toString());
      }
    });

    [
      this.mobileDisplay,
      this.minClicks,
      this.maxClicks,
      this.displayTime,
      this.buttonText,
      this.allowClickCancel,
      this.skip,
      this.skipText,
      this.commentEnabled,
      this.commentLabel,
      this.commentRequaired,
    ].forEach((f) =>
      f.subscribe(() => {
        if (this.updating()) return;
        this.onChange();
      })
    );

    this.commentField.isChanged.subscribe(() => {
      if (this.updating()) return;
      this.onChange();
    });
  }

  handleImageSelect(file) {
    this.imageFile(file);
    this.clickAreas([]);

    this.loadByFileWithParams(file, { scenario: 'first_click' });

    const subscription = this.galleryController.variants.subscribe((variants) => {
      if (variants && variants.length > 0) {
        const firstVariant = variants[0];
        if (firstVariant && firstVariant.url) {
          const newImageSrc = firstVariant.url();
          this.imageSrc(newImageSrc);
          this._updateImageDimensions(newImageSrc); // Update dimensions when imageSrc changes
          subscription.dispose();
        }
      }
    });

    if (!this.updating()) {
      this.onChange();
    }
  }

  removeImage() {
    this.imageFile(null);
    this.imagePreview("");
    this.imageSrc("");
    this.originalImageDimensions({ width: 0, height: 0 }); // Reset dimensions
    this.clickAreas([]);
    this.imageLoader.preview("");

    this.galleryController.variants([]);

    if (!this.updating()) {
      this.onChange();
    }
  }

  openFullscreen() {
    if (this.imagePreview()) {
      $.fancybox.open({
        src: this.imagePreview(),
        type: "image",
      });
    }
  }

  openClickAreasDialog() {
    console.log("1-click-test: openClickAreasDialog", this.imageSrc());
    if (!this.imageSrc()) {
      return;
    }

    this.controller.openSidesheet({
      name: "first-click-areas-sidesheet",
      params: {
        imageUrl: this.imageSrc,
        // Pass the main model's loaded dimensions to the dialog
        // So dialog doesn't need to reload them, ensuring consistency.
        passedOriginalImageDimensions: this.originalImageDimensions(),
        areas: this.clickAreas().map((areaPx) => { // areaPx contains pixel values
          const dims = this.originalImageDimensions();
          let percX = 0.1;
          let percY = 0.1;
          let percWidth = 0.2;
          let percHeight = 0.2;

          if (dims.width > 0 && dims.height > 0) {
            percX = areaPx.x / dims.width;
            percY = areaPx.y / dims.height;
            percWidth = areaPx.width / dims.width;
            percHeight = areaPx.height / dims.height;
          }
          return {
            id: areaPx.id || this.generateAreaId(),
            name: areaPx.name || "Область",
            x: ko.observable(percX), // Pass percentages to dialog
            y: ko.observable(percY),
            width: ko.observable(percWidth),
            height: ko.observable(percHeight),
            isSelected: ko.observable(false), // Dialog manages its own selection state
            clickCount: ko.observable(areaPx.clickCount || 0), // clickCount is just a stat here
          };
        }),
        onSave: (updatedAreasPerc) => { // updatedAreasPerc are in percentages from dialog
          const dims = this.originalImageDimensions();
          const newPixelAreas = updatedAreasPerc.map(areaPerc => {
            let pxX = 0, pxY = 0, pxWidth = 50, pxHeight = 50;
            if (dims.width > 0 && dims.height > 0) {
              pxX = Math.round(ko.unwrap(areaPerc.x) * dims.width);
              pxY = Math.round(ko.unwrap(areaPerc.y) * dims.height);
              pxWidth = Math.round(ko.unwrap(areaPerc.width) * dims.width);
              pxHeight = Math.round(ko.unwrap(areaPerc.height) * dims.height);
            }
            return {
              id: areaPerc.id, // Preserve ID
              name: ko.unwrap(areaPerc.name),
              x: pxX,
              y: pxY,
              width: Math.max(1, pxWidth),
              height: Math.max(1, pxHeight),
              // clickCount is not typically saved back from this dialog to the question definition
            };
          });
          this.clickAreas(newPixelAreas); // Store as pixels
          this.onChange();
        },
      },
    });
  }

  generateAreaId() {
    return "area_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
  }

  _updateImageDimensions(imageUrl) {
    if (!imageUrl) {
      this.originalImageDimensions({ width: 0, height: 0 });
      return;
    }
    const img = new Image();
    img.onload = () => {
      this.originalImageDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
      console.log("1-click-test: Original dimensions updated", this.originalImageDimensions());
    };
    img.onerror = () => {
      console.warn("1-click-test: Failed to load image to get dimensions", imageUrl);
      this.originalImageDimensions({ width: 0, height: 0 });
    };
    img.src = imageUrl;
  }

  updateData(data) {
    this.updating(true);
    super.updateData(data);

    if (data.imageFile) this.imageFile(data.imageFile);
    if (data.clickAreas) this.clickAreas(data.clickAreas || []);
    if (data.gallery && data.gallery.length > 0) {
      this.imagePreview(data.gallery[0].preview);
      const newImageSrc = data.gallery[0].src;
      this.imageSrc(newImageSrc);
      this._updateImageDimensions(newImageSrc); // Update dimensions when data is updated
    } else if (data.imagePreview) { // Fallback for older data or direct imageFile
        this._updateImageDimensions(data.imagePreview);
    }


    if (data.mobileDisplay !== undefined) this.mobileDisplay(data.mobileDisplay);
    if (data.minClicks !== undefined) this.minClicks(data.minClicks);
    if (data.maxClicks !== undefined) this.maxClicks(data.maxClicks);
    if (data.displayTime !== undefined) this.displayTime(data.displayTime);
    if (data.buttonText !== undefined) this.buttonText(data.buttonText);
    if (data.allowClickCancel !== undefined) this.allowClickCancel(data.allowClickCancel);

    setTimeout(() => {
      this.updating(false);
    }, 100);
  }

  getData() {
    const data = {
      ...super.getData(),

      imageFile: this.imageFile(),
      imagePreview: this.imagePreview(),
      // this.clickAreas() now stores pixel values.
      // Server formatter expects these pixel values.
      clickAreas: this.clickAreas().map((areaPx) => {
        const areaJS = ko.toJS(areaPx); // areaPx.x, .y, .width, .height are already pixels
        return {
          id: areaJS.id,
          name: areaJS.name,
          x: Math.round(areaJS.x), // Ensure integer
          y: Math.round(areaJS.y), // Ensure integer
          width: Math.max(1, Math.round(areaJS.width)), // Ensure integer & positive (min 1px)
          height: Math.max(1, Math.round(areaJS.height)), // Ensure integer & positive (min 1px)
        };
      }),

      mobileDisplay: this.mobileDisplay(),
      minClicks: parseInt(this.minClicks()) || 1,
      maxClicks: this.maxClicks() ? parseInt(this.maxClicks()) : "",
      displayTime: this.displayTime() ? parseInt(this.displayTime()) : "",
      buttonText: this.buttonText() || "",
      allowClickCancel: this.allowClickCancel(),
    };

    return data;
  }

  isValid() {
    this.imageLoader.clearErrors();

    if (!super.isValid()) return false;

    if (!this.imageFile.isValid()) {
      this.imageLoader.error(this.translator.t("Необходимо загрузить изображение")());
      return false;
    }

    if (!this.minClicks.isValid()) return false;

    if (!this.maxClicks.isValid()) return false;

    return true;
  }

  dispose() {
    super.dispose();
  }
}

export default FirstClickTestQuestion;
