<template id="question-form-template-first-click-test">
  <div class="question-form-template-first-click-test-main-container">
    <div class="form-group question-form-template-first-click-test-main-image-container">
      <label class="form-label" for="question-form-template-first-click-test-main-image-container" data-bind="text: $translator.t('Загрузка изображения')">Загрузка изображения</label>
      <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Загрузка изображения')" type="button"></button>

      <div class="d-flex align-items-start mt-5p">
        <media-load-button params="
          loader: question.imageLoader,
          disabled: question.isFullBlocked,
          formControlErrorStateMatcher: question.formControlErrorStateMatcher(question.imageFile)
        ">
          <svg-icon params="name: 'clip'"></svg-icon>
          <div class="mt-5p" style="line-height: 1.15;">
            jpg, jpeg, png, gif, svg
            <br>
            до 5 Мб
          </div>
        </media-load-button>

        <!-- ko if: question.imagePreview() -->
        <div class="ml-30p flex-grow-1">
          <button type="button" class="f-btn f-btn--secondary f-btn--sm" data-bind="
            click: question.openClickAreasDialog.bind(question)
          ">
            <span data-bind="text: question.changeClickAreasButtonText"></span>
          </button>

          <div class="f-color-service f-fs-1 mt-10p"
            style="line-height: 1.3;"
            data-bind="
              text: question.translator.t('Отметьте области, по которым хотели бы получить статистику, либо собирайте клики и анализируйте по тепловой карте'),
              attr: {
                title: question.translator.t('Отметьте области, по которым хотели бы получить статистику, либо собирайте клики и анализируйте по тепловой карте')
              }
            "
          ></div>
        </div>
        <!-- /ko -->
      </div>

      <file-loader-error params="error: question.imageLoader.error"></file-loader-error>
      <validation-feedback params="show: controller.formControlErrorStateMatcher(question.imageFile), text: question.imageFile.error"></validation-feedback>
    </div>

    <!-- Mobile Display Settings -->
    <div class="row">
      <div class="col-md-6">
        <div class="form-group mb-0">
          <label class="form-label" for="question-form-template-first-click-test-mobile-display" data-bind="text: $translator.t('Отображение на смартфоне')">Отображение на смартфоне</label>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Отображение на смартфоне')" type="button"></button>

          <div>
            <fc-select params="
              value: question.mobileDisplay,
              options: [
              { id: 'width', text: question.translator.t('По ширине экрана') },
              { id: 'height', text: question.translator.t('По высоте экрана') }
              ],
              disabled: question.isFullBlocked
            "></fc-select>
          </div>
        </div>
      </div>
    </div>

    <hr class="mx-0">

    <!-- Click Count Settings -->
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label class="form-label" data-bind="text: question.translator.t('Min кол-во кликов')">Min кол-во кликов</label>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: question.translator.t('Минимальное количество кликов')" type="button"></button>
          <div>
            <fc-input params="
              value: question.minClicks,
              min: 1,
              max: 999,
              disabled: question.isFullBlocked,
              mask: '999',
              maxlength: 3,
            "></fc-input>
          </div>
          <validation-feedback params="show: controller.formControlErrorStateMatcher(question.minClicks), text: question.minClicks.error"></validation-feedback>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group">
          <label class="form-label" data-bind="text: question.translator.t('Max кол-во кликов')">Max кол-во кликов</label>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: question.translator.t('Максимальное количество кликов')" type="button"></button>
          <div>
            <fc-input params="
              value: question.maxClicks,
              mask: {
                alias: 'numeric',
                digits: 0,
                max: 999,
                min: question.minClicks,
                allowMinus: false,
                rightAlign: false,
                SetMaxOnOverflow: true
              },
              disabled: question.isFullBlocked,
            "></fc-input>
          </div>
          <validation-feedback params="show: controller.formControlErrorStateMatcher(question.maxClicks), text: question.maxClicks.error"></validation-feedback>
        </div>
      </div>
    </div>

    <!-- Additional Settings -->
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label class="form-label" data-bind="text: question.translator.t('Время показа изображения, секунд')">Время показа изображения, секунд</label>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: question.translator.t('Время показа (секунды)')" type="button"></button>
          <div>
            <fc-input params="
              value: question.displayTime,
              min: 1,
              max: 300,
              mask: '999',
              maxlength: 3,
              disabled: question.isFullBlocked
            "></fc-input>
          </div>
          <div class="f-color-service f-fs-1 mb-20p mt-10p" data-bind="text: question.translator.t('Если ограничение по времени не нужно, оставьте поле пустым')"></div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group">
          <label class="form-label" data-bind="text: question.translator.t('Текст кнопки')">Текст кнопки</label>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: question.translator.t('Текст кнопки')" type="button"></button>
          <div>
            <fc-input params="
              value: question.buttonText,
              placeholder: question.translator.t('Показать изображение'),
              maxlength: 30,
              disabled: question.isFullBlocked,
              counter: true
            "></fc-input>
          </div>
        </div>
      </div>
    </div>

    <!-- Advanced Options -->
    <div class="form-group">
      <fc-check params="
        type: 'checkbox',
        checked: question.allowClickCancel,
        label: question.translator.t('Возможность отменить клик'),
        hint: question.translator.t('Возможность отменить клик'),
        disabled: question.isFullBlocked
      "></fc-check>
    </div>

    <hr class="mx-0">

    <!-- Skip Option -->
    <div class="form-group">
      <fc-switch params="
        checked: question.skip,
        label: question.translator.t('Пропуск оценки'),
        disabled: question.isFullBlocked
      "></fc-switch>
    </div>

    <!-- ko template: {
      foreach: templateIf(question.skip(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <label class="form-label" data-bind="text: question.translator.t('Текст')">Текст</label>
      <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: question.translator.t('Текст')" type="button"></button>
      <div>
        <fc-input
          params="
            value: question.skipText,
            counter: true,
            maxlength: 125,
            placeholder: question.translator.t('Не готов(а) оценить'),
            disabled: question.isFullBlocked,
          "
        ></fc-input>
      </div>
    </div>
    <!-- /ko -->

    <hr>

    <!-- Comment Option -->
    <div class="form-group">
      <fc-switch params="
        checked: question.commentEnabled,
        label: question.translator.t('Поле для комментария'),
        disabled: question.isFullBlocked
        "></fc-switch>
    </div>

    <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="row">
      <div class="col">
        <div class="f-check">
          <input
            type="checkbox"
            id="comment-required"
            class="f-check-input"
            data-bind="
              checked: question.commentRequaired,
              disable: question.isFullBlocked,
              event: { change: function() {
                question.updateCommentRequired() } }
            "
          />
          <label for="comment-required" class="f-check-label" data-bind="text: question.translator.t('Обязательный')"></label>
        </div>
      </div>
    </div>
    <div class="mt-4">
      <div class="form-group">
        <label class="form-label">Наименование поля</label>
        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: 'Наименование поля'" type="button"></button>
        <div>
          <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
        </div>
      </div>

      <!-- ko component: {
          name: 'text-field',
          params: {
              controller: question.commentField,
              intervalText: question.translator.t('Кол-во символов в комментарии'),
              disabled: question.isFullBlocked
          }
      } -->
      <!-- /ko -->
    </div>
    <!-- /ko -->

  </div>
</template>
