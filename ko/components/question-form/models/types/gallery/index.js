import Question from "../../question";
import { GALLERY_QUESTION } from "Data/question-types";
import { imageExtensions, videoExtensions } from "Legacy/data/media-extensions";

import TextField from "../../text-field";
import { MediaVariantsController } from "../../../controllers/media-variants-controller";
import { MEDIA_VARIANT_TYPE_MIX } from "../../../data/media-variants-types";
import { Translator } from "@/utils/translate";

class GalleryQuestion extends Question {
  constructor(controller, config = {}) {
    super(controller, config);

    this.type = GALLERY_QUESTION;

    this.imageExtensions = imageExtensions;
    this.videoExtensions = videoExtensions;
    this.getInputAccept = () => {
      return [...this.imageExtensions, ...this.videoExtensions].join(", ");
    };

    let galleryConfig = {
      withVariantsTypeSelect: false,
      withAddVariantButton: false,
      withCustomAnswer: false,
      addFirstVariant: false,
      minVariantsCount: 1,
      minVariantsCountErrorMessage: () => {
        return this.translator.t(
          "Необходимо добавить хотя бы одно изображение или видео"
        )();
      },
      mediaType: MEDIA_VARIANT_TYPE_MIX,
    };

    let extendConfig = config.galleryConfig || {};

    this.galleryController = new MediaVariantsController(
      {
        ...galleryConfig,
        ...extendConfig,
      },
      this
    );

    this.galleryError = ko.observable(null);

    this.commentEnabled = ko.observable(false);
    this.commentField = new TextField();
    this.commentLabel = ko.observable("");
    this.commentRequaired = ko.observable(false);

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    [this.commentField, this.galleryController].forEach((c) =>
      c.isChanged.subscribe((v) => this.onChange())
    );

    [this.commentEnabled, this.commentLabel, this.skip, this.skipText, this.commentRequaired].forEach(
      (f) => f.subscribe((v) => this.onChange())
    );
  }

  updateCommentRequired(value) {
    this.commentField.updateReuired(this.commentRequaired())
  }

  loadByFile(file) {
    this.galleryError(null);
    let variant = this.galleryController.createVariant();
    variant
      .addByFile(file)
      .then(() => this.galleryController.variants.push(variant))
      .catch((error) => {
        this.galleryError(error);
      });
  }

  loadByFileWithParams(file, urlParams = {}) {
    this.galleryError(null);
    let variant = this.galleryController.createVariant();
    
    // Override the getLoadByFileApiUrl method to add custom parameters
    const originalGetLoadByFileApiUrl = variant.getLoadByFileApiUrl.bind(variant);
    variant.getLoadByFileApiUrl = () => {
      return originalGetLoadByFileApiUrl().then(url => {
        // Add custom parameters to the URL
        const urlObj = new URL(url, window.location.origin);
        Object.keys(urlParams).forEach(key => {
          urlObj.searchParams.set(key, urlParams[key]);
        });
        return urlObj.toString();
      });
    };
    
    variant
      .addByFile(file)
      .then(() => this.galleryController.variants.push(variant))
      .catch((error) => {
        this.galleryError(error);
      });
  }

  loadByLink() {
    this.galleryError(null);
    let variant = this.galleryController.createVariant();
    variant.addByLink().then(() => {
      this.galleryController.variants.push(variant);
    });
  }

  updateData(data) {
    super.updateData(data);

    let galleryData = {
      variants: data.gallery,
    };

    this.galleryController.updateData(galleryData);

    this.commentEnabled(data.galleryCommentEnabled);
    this.commentLabel(data.galleryCommentLabel || "");
    this.commentRequaired(data.galleryCommentRequired)
    this.commentField.updateData({
      range: data.galleryCommentLengthRange,
      placeholder: data.galleryCommentPlaceholder,
      required: data.galleryCommentRequired
    });

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
  }

  getData() {
    let data = super.getData();

    let variantsData = this.galleryController.getData();
    data.gallery = variantsData.variants;

    data.galleryCommentEnabled = this.commentEnabled();

    let commentFieldData = this.commentField.getData();
    data.comment = this.commentField.getData()

    data.galleryCommentLengthRange = commentFieldData.range;
    data.galleryCommentPlaceholder = commentFieldData.placeholder;
    data.galleryCommentLabel = this.commentLabel();
    data.galleryCommentRequaired = commentFieldData.required;

    data.skip = this.skip();
    data.skipText = this.skipText();

    return data;
  }

  isValid() {
    if (!super.isValid()) return false;

    return this.galleryController.isValid();
  }
}

export default GalleryQuestion;
