<template id="question-form-template-gallery">
  <div>
    <label class="form-label mb-0" for="name">
      <span data-bind="text: question.translator.t('Галерея')"></span>
      <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: question.translator.t('Можно загрузить изображение или видео с компьютера, а также добавить по ссылке. Видео можно добавить по ссылке только с сервиса Youtube. Для изображений поддерживаются форматы: PNG, JPG, JPEG, GIF. Ограничение на размер файла 5 мб. Для видео поддерживаются форматы: MP4, WEBM. Ограничение на размер файла 10 мб. Менять порядок элементов можно с помощью перетаскивания')" type="button">
      </button>
    </label>

    <div class="form-label"><small class="form-label__note" data-bind="text: question.translator.t('Добавьте в галерею изображения или видео с вашего устройства или по ссылке')"></small></div>

    <!-- ko ifnot: question.galleryController.variants().length -->
    <hr class="mb-0">
    <!-- /ko -->


    <div data-bind="dnd: function(files) {
      question.galleryController.loader.loadFiles(files)
    }, dndDisabled: question.isFullBlocked">
      <!-- ko component: {
        name: 'media-variants-controller-block',
        params: {
            controller: question.galleryController,
            disabled: question.isFullBlocked
        }
    } -->
      <!-- /ko -->


      <!-- ko ifnot: question.isFullBlocked -->
      <div class="mb-4">
        <div class="mt-4">
          <!-- ko let: { fileInput: ko.observable(null) } -->
          <input data-bind="
        element: fileInput,
        event: {
            change: function (_, event) {
                const file = event.target.files[0];
                question.loadByFile(file);
                event.target.value = '';
            }
        },
        attr: {
          accept: question.getInputAccept()
        }" type="file" hidden>

          <button class="f-btn
          survey-question__media-form-control-action" data-bind="click: function () { $(fileInput()).trigger('click'); }">
            <span class="f-btn-prepend">
              <svg-icon params="name: 'clip'"></svg-icon>
            </span>
            <span data-bind="text: question.translator.t('С компьютера')"></span>
          </button>

          <button class="ml-2 btn btn-default btn-with-icon btn-upload-link
        survey-question__media-form-control-actions-item" data-bind="
        click: function () { question.loadByLink(); }" type="button">
            <span data-bind="text: question.translator.t('По ссылке')"></span>
          </button>

          <!-- /ko -->
        </div>

        <!-- ko foreach: question.galleryController.loader.errors -->
        <file-loader-error class="mt-n3" params="error: $data"></file-loader-error>
        <!--/ko -->

        <!-- ko if: question.galleryError -->
        <div class="form-error" data-bind="text: question.galleryError"></div>
        <!-- /ko -->

        <!-- ko if: question.formControlErrorStateMatcher(question.galleryController.variantsCount) -->
        <div class="form-error" data-bind="text: question.galleryController.variantsCount.error()"></div>
        <!-- /ko -->
      </div>
      <!-- /ko -->

      <dnd-cover params="type: 'mix', mode: 'multiple', dense: question.galleryController.variants().length < 1" class="my-n4 mx-n3"></dnd-cover>
    </div>

  </div>

  <hr class="mx-0">

  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск оценки'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
       foreach: templateIf(question.skip(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Не готов(а) оценить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>

    <!-- /ko -->
  </div>

  <hr class="mx-0">


  <!-- Комментарий -->
  <div class="mt-4">
    <switch params="checked: question.commentEnabled, disabled: question.isFullBlocked">
      <span data-bind="text: question.translator.t('Комментарий')"></span>
    </switch>

  </div>


  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div>
      <div class="row pb-4">
          <div class="col">
            <div class="f-check">
                <input
                type="checkbox"
                id="comment-required"
                class="f-check-input"
                data-bind="
                    checked: question.commentRequaired,
                    disable: question.isFullBlocked,
                    event: { change: function() { 
                    question.updateCommentRequired() } }
                "
                />
                <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
            </div>
            </div>
        </div>
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: question.translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
    } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->
</template>