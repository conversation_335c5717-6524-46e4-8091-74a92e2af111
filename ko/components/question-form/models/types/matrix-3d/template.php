<template id="question-form-matrix-3d-list">
  <div class="form-group">
    <fc-switch
      params="
        checked: question.useTooltips, 
        disabled: question.isFullBlocked,
        label: 'Показывать подсказки для столбцов и строк',
        hint: 'Под текстом строк и столбцов появятся дополнительные поля, в которые можно внести текст тултипов (подсказок в знаке &#34;?&#34;)'
      "
    ></fc-switch>
  </div>
  <div id="question-form-matrix-3d-list-top"></div>
  <h2 class="f-h2">Строки</h2>
  <!-- ko template: {
    foreach: templateIf(!question.donorRows.useDonor() && question.useTooltips(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400),
  } -->
  <div class="mb-20p">
    <fc-button
      params="
        size: 'auto',
        mode: 'text',
        label: question.showRowTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
        color: 'primary'
      "
      data-bind="click: () => question.showRowTooltips(!question.showRowTooltips())"
    ></fc-button>
  </div>
  <!-- /ko -->
  <!-- ko ifnot: question.isAuto || question.isSystem || question.mode === 'cpoint' -->
  <div class="form-group" style="margin-bottom: 25px">
    <div
      data-bind="
        click: function() {
          return question.onDonorRowsTogglerClick();
        },
      ">
      <fc-switch
        params="
          checked: question.donorRows.useDonor, 
          disabled: question.disableDonorRows() || question.isFullBlocked,
          label: 'Использовать варианты ответов респондента из другого вопроса',
          hint: 'Использовать варианты ответов респондента из другого вопроса',
        "
      ></fc-switch>
    </div>
  </div>
  <!-- /ko -->
  <!-- ko template: {
    foreach: templateIf(question.donorRows.useDonor(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400),
  } -->
    <!-- ko template: {
      name: 'question-form-matrix-3d-donor',
      data: {
        ...$data,
        ...{
          donorType: 'row',
        },
      },
    } -->
    <!-- /ko -->
  <!-- /ko -->
  <!-- ko template: {
    foreach: templateIf(!question.donorRows.useDonor(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400),
  } -->
  <fc-variants-list
    params="
      variantsList: question.rowsVariantsList,
      isBlocked: question.isBlocked,
      isFullBlocked: question.isFullBlocked,
      withPoints: false,
      canRemoveSingleVariant: false,
      formControlErrorStateMatcher: question.formControlErrorStateMatcher,
      formControlSuccessStateMatcher: question.formControlSuccessStateMatcher,
      onRemove: function(v, cb) {
        question.removeVariant(v, cb);
      },
      id: `matrix-3d-rows-variants-${question.id()}`,
      showTooltips: question.showRowTooltips,
      useTooltips: question.useTooltips,
      tooltipPlaceholder: 'Подсказка для строки'
    "
  ></fc-variants-list>
  
  <!-- ko template: {
    foreach: templateIf(question.useTooltips(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400),
  } -->
  <div class="mb-10p mt-15p">
    <fc-button
      params="
        size: 'auto',
        mode: 'text',
        label: question.showRowTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
        color: 'primary'
      "
      data-bind="click: () => question.showRowTooltips(!question.showRowTooltips())"
    ></fc-button>
  </div>
  <!-- /ko -->
  <!-- ko ifnot: question.isFullBlocked -->
  <div class="row">
    <div class="col-6">
      <button
        class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
        data-bind="
          click: function() {
            const { addVariant } = ko.contextFor(document.querySelector(`#matrix-3d-rows-variants-${question.id()}`)).$data;
            addVariant();
          },
          attr: {
            disabled: !question.rowsVariantsList.isValid(),
          },
        ">
        <span class="survey-question__variants-control-add-button-icon"></span>
        <span data-bind="text: question.translator.t('Добавить строку')"></span>
      </button>
    </div>
    <div class="col-6">
      <button
        class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
        data-bind="
          click: function() { question.addVariantsFromList(question.rowsVariantsList, 'rows'); },
        "
      >
        <span class="survey-question__variants-control-add-button-icon"></span>
        <span data-bind="text: question.translator.t('Добавить списком')"></span>
      </button>
    </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->
  <div class="form-group">
    <fc-check
      params="
        checked: question.rowsAboveVariants,
        label: 'Отображать строки над вариантами',
        hint: 'Отображать строки над вариантами',
        disabled: question.isFullBlocked,
      "
    ></fc-check>
  </div>
  <hr class="mx-0">
  <h2 class="f-h2">Столбцы</h2>
  <!-- ko ifnot: question.isAuto || question.isSystem || question.mode === 'cpoint' -->
  <div class="form-group" style="margin-bottom: 18px">
    <div
      data-bind="
        click: function() {
          return question.onDonorColumnsTogglerClick();
        },
      "
    >
      <fc-switch
        params="
          checked: question.donorColumns.useDonor,
          disabled: question.disableDonorColumns() || question.isFullBlocked,
          label: 'Использовать варианты ответов респондента из другого вопроса',
          hint: 'Использовать варианты ответов респондента из другого вопроса',
        "
      ></fc-switch>
    </div>
  </div>
  <!-- /ko -->
  <!-- ko template: {
    foreach: templateIf(question.donorColumns.useDonor(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400),
  } -->
  <!-- ko template: {
    name: 'question-form-matrix-3d-donor',
    data: {
      ...$data,
      ...{
        donorType: 'column',
      },
    },
  } -->
  <!-- /ko -->
  <!-- /ko -->
  <!-- ko template: {
    foreach: templateIf(!question.donorColumns.useDonor(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400),
  } -->
  <!-- ko template: {
    foreach: templateIf(question.useTooltips(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400),
  } -->
  <div class="mb-20p">
    <fc-button
      params="
        size: 'auto',
        mode: 'text',
        label: question.showCellTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
        color: 'primary'
      "
      data-bind="click: () => question.showCellTooltips(!question.showCellTooltips())"
    ></fc-button>
  </div>
  <!-- /ko -->
  <fc-variants-list
    params="
      variantsList: question.colsVariantsList,
      isBlocked: question.isBlocked,
      isFullBlocked: question.isFullBlocked,
      withPoints: false,
      canRemoveSingleVariant: false,
      formControlErrorStateMatcher: question.formControlErrorStateMatcher,
      formControlSuccessStateMatcher: question.formControlSuccessStateMatcher,
      onRemove: function(v, cb) {
        question.removeVariant(v, cb);
      },
      hasChildren: true,
      id: `matrix-3d-columns-variants-${question.id()}`,
      showTooltips: question.showCellTooltips,
      useTooltips: question.useTooltips,
      tooltipPlaceholder: 'Подсказка для столбца'
    "
  ></fc-variants-list>
  <!-- ko template: {
    foreach: templateIf(question.useTooltips(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400),
  } -->
  <div class="mb-10p mt-15p">
    <fc-button
      params="
        size: 'auto',
        mode: 'text',
        label: question.showCellTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
        color: 'primary'
      "
      data-bind="click: () => question.showCellTooltips(!question.showCellTooltips())"
    ></fc-button>
  </div>
  <!-- /ko -->
  <!-- ko ifnot: question.isFullBlocked -->
  <div class="row">
    <div class="col-6">
      <button
        class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
        data-bind="
          click: function() {
            const { addVariant } = ko.contextFor(document.querySelector(`#matrix-3d-columns-variants-${question.id()}`)).$data;
            addVariant();
          },
          attr: { disabled: !question.colsVariantsList.isValid() },
        "
      >
        <span class="survey-question__variants-control-add-button-icon"></span>
        <span data-bind="text: question.translator.t('Добавить столбец')"></span>
      </button>
    </div>
    <div class="col-6">
      <button
        class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
        data-bind="
          click: function() { question.addVariantsFromList(question.colsVariantsList); },
        "
      >
        <span class="survey-question__variants-control-add-button-icon"></span>
        <span data-bind="text: question.translator.t('Добавить списком')"></span>
      </button>
    </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->
  <!-- ko if: question.isFullBlocked -->
  <div class="row mt-15p"></div>
  <!-- /ko -->
</template>

<template id="question-form-matrix-3d-donor">
  <div data-bind="let: { donor: $data.donorType === 'row' ? question.donorRows : question.donorColumns }">
    <div class="form-group">
      <fc-label params="text: 'Вопрос-донор', hint: 'Вопрос-донор'"></fc-label>
      <fc-select
        params="
          options: donor.donorsList,
          value: donor.donorId,
          disabled: question.isBlocked() || question.isFullBlocked,
        "
        data-bind="
          click: function() {
            if (question.isBlocked() && !question.isFullBlocked) {
              question.tryChangeBlockedParam();
            } else {
              return true;
            }
          },
        "
      ></fc-select>
    </div>
    <div class="form-group">
      <fc-label params="text: 'Варианты ответов'"></fc-label>
      <fc-donor-variants-type
        params="
          value: donor.donorVariantsType,
          isFullBlocked: question.isFullBlocked,
          disabled: question.isBlocked() || question.isFullBlocked,
        "
        data-bind="
          click: function() {
            if (question.isBlocked() && !question.isFullBlocked) {
              question.tryChangeBlockedParam();
            } else {
              return true;
            }
          },
        "
        class="mb-4"
      ></fc-donor-variants-type>
    </div>
    <div
      class="form-group"
      data-bind="let: {tooltipPlaceholder: $data.donorType === 'row' ? 'Тултип для строки' : 'Тултип для столбца', donorVariants: $data.donorType === 'row' ? question.rowsVariantsList : question.colsVariantsList, showTooltips: $data.donorType === 'row' ? question.showRowTooltips : question.showCellTooltips }"
    >     
    <!-- ko template: {
      foreach: templateIf(($data.donorType === 'row') && question.useTooltips(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400),
    } -->
    <div class="mb-10p mt-15p">
      <fc-button
        params="
          size: 'auto',
          mode: 'text',
          label: question.showRowTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
          color: 'primary'
        "
        data-bind="click: () => question.showRowTooltips(!question.showRowTooltips())"
      ></fc-button>
    </div>
    <!-- /ko -->
    <!-- ko template: {
      foreach: templateIf(($data.donorType !== 'row') && question.useTooltips(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400),
    } -->
    <div class="mb-10p mt-15p">
      <fc-button
        params="
          size: 'auto',
          mode: 'text',
          label: question.showCellTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
          color: 'primary'
        "
        data-bind="click: () => question.showCellTooltips(!question.showCellTooltips())"
      ></fc-button>
    </div>
    <!-- /ko -->
    <!-- ko if: donorVariants && donorVariants.list() -->
      <fc-donor-variants-list
        params="
          variants: donorVariants.list,
          withPoints: false,
          hasChildren: $data.donorType === 'column',
          formControlErrorStateMatcher: question.formControlErrorStateMatcher,
          formControlSuccessStateMatcher: question.formControlSuccessStateMatcher,
          isFullBlocked: question.isFullBlocked,
          useTooltips: question.useTooltips,
          showTooltips: $data.donorType === 'row'
            ? question.showRowTooltips
            : question.showCellTooltips,
          tooltipPlaceholder: tooltipPlaceholder
        "
      ></fc-donor-variants-list>
    <!-- /ko -->
    <!-- ko template: {
      foreach: templateIf(($data.donorType === 'row') && question.useTooltips(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400),
    } -->
    <div class="mb-10p mt-15p">
      <fc-button
        params="
          size: 'auto',
          mode: 'text',
          label: question.showRowTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
          color: 'primary'
        "
        data-bind="click: () => question.showRowTooltips(!question.showRowTooltips())"
      ></fc-button>
    </div>
    <!-- /ko -->
    <!-- ko template: {
      foreach: templateIf(($data.donorType !== 'row') && question.useTooltips(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400),
    } -->
    <div class="mb-10p mt-15p">
      <fc-button
        params="
          size: 'auto',
          mode: 'text',
          label: question.showCellTooltips() ? 'Свернуть настройку подсказок' : 'Развернуть настройку подсказок',
          color: 'primary'
        "
        data-bind="click: () => question.showCellTooltips(!question.showCellTooltips())"
      ></fc-button>
    </div>
    <!-- /ko -->
    </div>
  </div>
</template>

<template id="question-form-template-matrix-3d">
  <!-- ko template: { name: 'question-form-matrix-3d-list', } --><!-- /ko -->
  <fc-variants-type
    params="
      value: question.variantsType, 
      disabled: question.isBlocked() || question.isFullBlocked,
    "
    data-bind="
      click: function() {
        if (question.isBlocked() && !question.isFullBlocked) {
          question.tryChangeBlockedParam();
        } else {
          return true;
        }
      },
    "
    class="mb-4"
  ></fc-variants-type>
  <div class="form-group">
    <fc-label params="text: $translator.t('Подсказка внутри поля выбора варианта'), hint: $translator.t('Подсказка внутри поля выбора варианта')"></fc-label>
    <fc-input
      params="
        value: question.selectPlaceholder,
        counter: true,
        maxlength: 125,
        disabled: question.isFullBlocked,
      "
    ></fc-input>
  </div>
  <hr class="mx-0">
  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск ответа'), disabled: question.isFullBlocked"></fc-switch>
    </div>
    <!-- ko template: {
      foreach: templateIf(question.skip(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400),
    } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Затрудняюсь ответить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>
    <!-- /ko -->
    <!-- ko if: question.skip() -->
    <div class="mb-20p">
      <foquz-checkbox params="checked: question.skipVariant, disabled: question.isFullBlocked">
        <span data-bind="text: question.translator.t('Пропуск для каждого ответа')"></span>
        <question-button params="text: question.translator.t('Пропуск для каждого ответа')"></question-button>
      </foquz-checkbox>
    </div>
    <!-- ko template: {
      foreach: templateIf(question.skipVariant(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400),
    } -->
    <div class="mb-20p">
      <div class="row">
        <div class="col-5">
          <foquz-checkbox params="checked: question.skipRow, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Общий пропуск для строки')"></span>
            <question-button params="text: question.translator.t('Добавляет возможность пропустить каждую строку по одной кнопке')"></question-button>
          </foquz-checkbox>
        </div>
        <div class="col-6">
          <foquz-checkbox class="ml-" params="checked: question.skipColumn, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Общий пропуск для столбца')"></span>
            <question-button params="text: question.translator.t('Добавляет возможность пропустить каждый столбец по одной кнопке. Доступно только для ПК и планшетной версий')"></question-button>
          </foquz-checkbox>
        </div>
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>
  <hr class="mx-0">
  <!-- ko template: { name: 'gallery-question-gallery-template' } --><!-- /ko -->
  <hr class="mx-0">
  <div class="row clarifying-answer-settings__switchers">
    <div class="col">
      <div class="pt-1">
        <switch
          class="mb-0"
          params="
            checked: question.commentEnabled,
            disabled: question.isFullBlocked,
          "
          data-bind="
            click: function() {
              if (question.isFullBlocked) {
                return false;
              }
              return true;
            },
          "
        >
          <span data-bind="text: $translator.t('Комментарий')"></span>
        </switch>
      </div>
    </div>
  </div>
  <!-- ko template: {
    foreach: templateIf(question.commentEnabled(), $data),
    afterAdd: slideAfterAddFactory(200),
    beforeRemove: slideBeforeRemoveFactory(200),
  } -->
  <div>
      <div class="row pb-4">
            <div class="col">
            <div class="f-check">
                <input
                type="checkbox"
                id="comment-required"
                class="f-check-input"
                data-bind="
                    checked: question.commentIsRequired,
                    disable: question.isFullBlocked,
                    event: { change: function() { 
                    question.updateCommentRequired() } }
                "
                />
                <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
            </div>
            </div>
        </div>
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input
        params="
          value: question.commentLabel,
          maxlength: 120,
          counter: true,
          placeholder: 'Ваш комментарий',
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>
    <!-- ko component: {
      name: 'text-field',
      params: {
        controller: question.commentField,
        intervalText: $translator.t('Кол-во символов в комментарии'),
        disabled: question.isFullBlocked,
      }
    } --><!-- /ko -->
  </div>
  <!-- /ko -->
</template>
