import { get as _get } from "lodash";
import GalleryQuestion from "../gallery";

import { ASSESSMENT_TYPE_VARIANTS } from "@/components/question-form/data/assessment-types";
import { MATRIX_3D_QUESTION } from "@/data/question-types";
import { VARIANTS_TYPE_MULTIPLE, VARIANTS_TYPE_SINGLE } from "@/components/question-form/data/variant-types";

import { AnswersLimit } from "../variants/answers-limit";
import { Donor } from "../../donor";
import TextField from "@/components/question-form/models/text-field";
import { VariantsListModel } from "@/components/question-form/models/variants-list";
import "@/dialogs/add-variants-list-dialog";

class Matrix3DQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };



    super(controller, config);

    // question type
    this.type = MATRIX_3D_QUESTION;

    // variants
    this.defaultVariantsType = VARIANTS_TYPE_SINGLE;
    this.variantsType = ko.observable(this.defaultVariantsType);
    this.rowsVariantsList = VariantsListModel({useTooltips: true});
    this.colsVariantsList = VariantsListModel({ hasChildren: true, useTooltips: true });

    this.isMultiple = ko.computed(() => {
      return this.variantsType() == VARIANTS_TYPE_MULTIPLE;
    });

    // custom variant
    this.customAnswerEnabled = ko.observable(false);
    this.customAnswerField = new TextField({
      withLabel: true,
      defaultLabel: this.translator.t("Свой вариант"),
    });

    this.controller = controller;
    this.useTooltips = ko.observable(false);
    this.showRowTooltips = ko.observable(false);
    this.showCellTooltips = ko.observable(false);

    // donor
    this.donorRows = Donor(controller.donors);
    this.donorColumns = Donor(controller.donors, { hasChildren: true });
    this.blockRecipients = controller.blockRecipients;
    this.disableDonorRows = ko.computed(() => {
      if (this.isFullBlocked) return true;
      if (ko.toJS(this.isBlocked)) return true;
      if (this.blockRecipients) return true;
      if (!this.donorRows.donorsList().length) return true;
      return false;
    });
    this.disableDonorColumns = ko.computed(() => {
      if (this.isFullBlocked) return true;
      if (ko.toJS(this.isBlocked)) return true;
      if (this.blockRecipients) return true;
      if (!this.donorColumns.donorsList().length) return true;
      return false;
    });
    this.donorRows.useDonor.subscribe((v) => {
      // ToDo
    });
    this.donorRows.donorId.subscribe((v) => {
      const donorVariants = this.donorRows.getDonorVariants(v, {tooltip: true});
      
      this.rowsVariantsList.update(donorVariants, { noModel: true, tooltip: true });
    });
    this.donorColumns.useDonor.subscribe((v) => {
      // ToDo
    });
    this.donorColumns.donorId.subscribe((v) => {
      const donorVariants = this.donorColumns.getDonorVariants(v, {hasChildren: true, tooltip: true});

      this.colsVariantsList.update(donorVariants, { noModel: true, tooltip: true });
    });

    // options
    this.dropdown = ko.observable(false);
    this.randomOrder = ko.observable(false);

    // limit
    this.answersCountLimit = AnswersLimit({
      required: this.required,
      list: this.colsVariantsList.list,
    });

    // gallery
    this.galleryEnabled = ko.observable(false);

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");
    this.skipVariant = ko.observable(false);
    this.skipRow = ko.observable(false);
    this.skipColumn = ko.observable(false);

    this.selectPlaceholder = ko.observable('');
    this.rowsAboveVariants = ko.observable(false);

    this.commentIsRequired = ko.observable(null);

    // subscriptions
    [
      this.variantsType,
      this.customAnswerEnabled,
      this.galleryEnabled,
      this.answersCountLimit.value,
      this.dropdown,
      this.randomOrder,
      this.skip,
      this.skipText,
      this.skipVariant,
      this.skipRow,
      this.skipColumn,
      this.selectPlaceholder,
      this.rowsAboveVariants,
      this.commentIsRequired,
      this.useTooltips
    ].forEach((f) => {
      this.subscriptions.push(f.subscribe(() => this.onChange(true)));
    });
    this.subscriptions.push(
      this.rowsVariantsList.isChanged.subscribe((v) => this.onChange(true)),
      this.colsVariantsList.isChanged.subscribe((v) => this.onChange(true)),
      this.customAnswerField.isChanged.subscribe((v) => this.onChange(true)),
      this.donorRows.isChanged.subscribe((v) => this.onChange(true)),
      this.donorColumns.isChanged.subscribe((v) => this.onChange(true)),
    );
    this.skipVariant.subscribe((v) => {
      if (!v) {
        this.skipRow(v)
        this.skipColumn(v)
      }
    });
    this.skip.subscribe((v) => {
      if (!v) {
        this.skipVariant(v);
      }
    });
    this.useTooltips.subscribe((v) => {
      if (v) {
        this.showCellTooltips(true)
        this.showRowTooltips(true)
        if ($("#question-form-matrix-3d-list-top").length) {
          $([document.documentElement, document.body]).animate({
            scrollTop: $("#question-form-matrix-3d-list-top").offset().top
        }, 600);
        }
      }
    });
  }

  removeVariant(v, cb) {
    const hasAnswers = parseInt(this.countAnswers) > 1;
    const recipients = this.controller.recipients && this.controller.recipients();

    if (!hasAnswers) {
      cb();
      return;
    }

    if (recipients.length) {
      const recipientsText = recipients
        .map((q) => {
          return `<div>«${q.index}. ${q.question.descriptionText}»</div>`;
        })
        .join("");

      this.confirm({
        title: "Удаление варианта ответа",
        text: `По этому вопросу уже собирается статистика. Вариант ответа будет удалён без возможности восстановления также у вопросов-реципиентов:
        <div class="bold">${recipientsText}</div>
        `,
        mode: "danger",
        confirm: "Удалить",
      }).then(() => {
        setTimeout(() => {
          cb();
        }, 500);
      });
    } else {
      this.confirm({
        title: "Удаление варианта ответа",
        text: "По этому вопросу уже собирается статистика. Вариант ответа будет удален без возможности восстановления.",
        mode: "danger",
        confirm: "Удалить",
      }).then(() => {
        setTimeout(() => {
          cb();
        }, 500);
      });
    }
  }

  addVariantsFromList(variantsList, type) {
    const typeString = type === 'rows' ? 'строк' : 'столбцов';
    this.openDialog({
      name: "add-variants-list-dialog",
      params: {
        hasAnswers: parseInt(this.countAnswers) > 0,
        headerText: type !== 'answers' ? `Добавление значений ${typeString} списком` : undefined,
        subHeaderText: type !== 'answers' ? 'Каждый вариант в отдельной строке' : undefined,
        disableReplaceDetails: type !== 'answers',
        questionController: this.controller,
        hideInputTypeSwitcher: true,
      },
      events: {
        submit: async (result) => {
          const details = variantsList.getVariants()
            .reduce((acc, item, i) => {
              if (!item) return acc;
              if (typeof item === "object") {
                const items = [
                  {
                    ...item,
                    position: i + 1,
                    need_extra: 'needExtra' in item && item.needExtra() ? 1 : 0,
                    needExtra: undefined,
                  },
                ];
                return [...acc, ...items];
              }
              return [...acc, item];
            }, []);
          const res = await fetch(
            `/foquz/api/questions/prepare-details?access-token=${APIConfig.apiKey}`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json;charset=utf-8'
              },
              body: JSON.stringify({
                newDetails: result.newDetails,
                replaceDetails: result.replaceDetails,
                FoquzQuestionDetail: details,
              }),
            },
          );
          const data = await res.json();
          const childs = {}
          variantsList.list().forEach(v => {
            if (v.children) {
              childs[v.id+v.value()] = v.children.getVariants()
            }
          })
          console.log(childs)
          variantsList.update(
            data.detail_answers
              .filter((v) => !v.is_deleted)
              .map((v) => {
                return {
                  id: v.id,
                  donorId: v.question_detail_id,
                  value: v.variant,
                  points: v.points,
                  needExtra: v.need_extra,
                  description: v.description
                };
              }),
              {tooltip: true}
          );
          
          variantsList.list().forEach(v => {
            if (childs[v.id+v.value()]) {
              v.children.update(childs[v.id+v.value()])
            }
          })
        },
      },
    });
  }

  onDonorRowsTogglerClick() {
    if (this.isFullBlocked) return false;
    if (ko.toJS(this.isBlocked)) {
      this.tryChangeBlockedParam();
    } else if (this.blockRecipients) {
      this.tryChangeBlockedParam();
    } else if (!this.donorRows.donorsList().length) {
      this.noDonorsInfo();
    } else return true;
  }

  onDonorColumnsTogglerClick() {
    if (this.isFullBlocked) return false;
    if (ko.toJS(this.isBlocked)) {
      this.tryChangeBlockedParam();
    } else if (this.blockRecipients) {
      this.tryChangeBlockedParam();
    } else if (!this.donorColumns.donorsList().length) {
      this.noDonorsInfo();
    } else return true;
  }

  getLogicVariants(ids) {
    // ToDo
    return this.rowsVariantsList
      .list()
      .filter((variant) => {
        return ids.find((id) => variant.id == id);
      })
      .map((variant) => variant.value);
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  updateData(data) {
    super.updateData(data);

    const {
      matrixElements,
      variantsType,
      variantsCustomAnswerEnabled,
      variantsCustomAnswerLengthRange,
      variantsCustomAnswerPlaceholder,
      customAnswerLabel,
      variantsAnswersCountLimit,
      dropdown,
      randomOrder,
      galleryEnabled,
      donorRows,
      donorColumns,
      donorRowsType,
      donorColumnsType,
      showTooltips
    } = data;

    this.galleryEnabled(galleryEnabled);
    this.variantsType(variantsType);
    this.useTooltips(!!showTooltips)

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.galleryCommentRequired)
    }

    if (donorRows) {
      const donorQestion = window.QUESTIONS.find(i => i.id == donorRows);

      this.useTooltips(!!showTooltips);

      const donorType = donorQestion.main_question_type
      this.donorRows.useDonor(true);
      this.donorRows.donorId(donorRows);
      this.donorRows.donorVariantsType(donorRowsType);

      const donorVariants = this.donorRows.getDonorVariants(donorRows, {tooltip: true}).map(i => {
        const description = matrixElements.rows.find(item => 
          item.id == i.id 
          || item.donor_variant_id == i.id
          || Number.parseInt(item.donor_dictionary_element_id) == i.id
        )?.description || '';

        if (description && description.length && description !== i.description) {
          i.description(description)
          i.modified = true
        } else if (description === '' && typeof i.description === 'function') {
          i.description('')
        }

        const recipientId = matrixElements.rows.find(row => row.donor_variant_id === i.id || row.donor_dictionary_element_id === i.id)?.id
        if (recipientId) {
          i._recipientId = recipientId
        }
        
        return i
      });

      if (donorType !== 19) {
        this.rowsVariantsList.update(donorVariants, { noModel: true, tooltip: true });
      } else {
        const donorIds = donorVariants.map(i => +i.id)
        const temp_variants = matrixElements.rows
          .filter(i => donorIds.includes(i.donor_dictionary_element_id))
          .sort((a, b) => a.position - b.position);
        const currentOrder = temp_variants.map((v) => v.donor_dictionary_element_id);
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(+a.id);
          const bIndex = currentOrder.indexOf(+b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
        
        this.rowsVariantsList.update(donorVariants, { noModel: true, tooltip: true, noSort: true });
      }      
    } else {
      this.donorRows.useDonor(false)
      this.rowsVariantsList.update(matrixElements.rows, {tooltip: true});
    }
    if (donorColumns) {
      const donorQestion = window.QUESTIONS.find(i => i.id == donorColumns);

      this.useTooltips(!!showTooltips);
      const donorType = donorQestion.main_question_type;
      this.donorColumns.useDonor(true);
      this.donorColumns.donorId(donorColumns);
      this.donorColumns.donorVariantsType(donorColumnsType);

      const donorVariants = this.donorColumns
        .getDonorVariants(donorColumns, {tooltip: true})
        .map(i => {
          const description = matrixElements.columns.find(item => 
            item.id == i.id 
            || item.donor_variant_id == i.id
            || Number.parseInt(item.donor_dictionary_element_id) == i.id
          )?.description || '';

          if (description && description.length && description !== i.description) {
            i.description(description)
            i.modified = true
          } else if (description === '' && typeof i.description === 'function') {
            i.description('')
          }
          return i
        });

      if (donorType !== 19) {
        this.colsVariantsList.update(donorVariants, { noModel: true, tooltip: true });
        this.colsVariantsList.update(
          matrixElements.columns
            .sort((a, b) => a.position - b.position)
            .map(i => {
              const donorDescription = donorQestion.detail_answers
                .find(item => item.id == i.donor_dictionary_element_id || item.id == i.donor_variant_id)?.description || '';
              if (donorDescription && donorDescription.length && donorDescription !== i.description) {
                i.modified = true
              }
              
              return i
            }),
          { tooltip: true },
        );
      } else {
        this.colsVariantsList.update(donorVariants, { noModel: true, tooltip: true });
        this.colsVariantsList.update(matrixElements.columns.sort((a, b) => a.position - b.position).map(i => {

          const donorDescription = donorQestion.detail_answers
            .find(item => item.id == i.donor_dictionary_element_id|| item.id == i.donor_variant_id)?.description || '';
          if (donorDescription && donorDescription.length && donorDescription !== i.description) {
            i.modified = true
          }
          return i
        }), {tooltip: true });
      }
    } else {
      this.donorColumns.useDonor(false)
      this.colsVariantsList.update(matrixElements.columns, {tooltip: true});
    }

    this.customAnswerEnabled(variantsCustomAnswerEnabled);
    this.customAnswerField.updateData({
      range: variantsCustomAnswerLengthRange,
      placeholder: variantsCustomAnswerPlaceholder,
      label: customAnswerLabel,
    });

    this.answersCountLimit.value(variantsAnswersCountLimit);

    this.dropdown(dropdown);
    this.randomOrder(randomOrder);

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
    this.skipVariant(!!data.skipVariant);
    this.skipRow(!!data.skipRow);
    this.skipColumn(!!data.skipColumn);
    this.selectPlaceholder(data.selectPlaceholder);
    this.rowsAboveVariants(parseInt(_get(data, 'matrixSettings.rowsAboveVariants', 0)));
  }

  getData() {
    const parentData = super.getData();
    const variantsType = parseInt(this.variantsType());
    const galleryEnabled = this.galleryEnabled() ? 1 : 0;

    let data = {
      ...parentData,
      variantsAssessmentType: ASSESSMENT_TYPE_VARIANTS,
      variantsType,
      dropdown: this.dropdown(),
      randomOrder: this.randomOrder(),
      variantsAnswersCountLimit: this.answersCountLimit.value(),
      galleryEnabled,
      gallery: galleryEnabled ? parentData.gallery : [],
    };

    data.skip = this.skip();
    data.skipText = this.skipText();
    data.skipVariant = this.skipVariant();
    data.skipRow = this.skipRow();
    data.skipColumn = this.skipColumn();
    data.selectPlaceholder = this.selectPlaceholder();
    data.matrixSettings = {
      rowsAboveVariants: this.rowsAboveVariants() ? 1 : 0,
    };
    const customAnswer = this.customAnswerField.getData();
    data = {
      ...data,
      rowsVariants: this.rowsVariantsList.getVariants(),
      colsVariants: this.colsVariantsList.getVariants(),
      variantsCustomAnswerEnabled: 0,
      variantsCustomAnswerPlaceholder: customAnswer.placeholder,
      variantsCustomAnswerLengthRange: customAnswer.range,
      customAnswerLabel: customAnswer.label,
      donor: null,
    };

    if (this.donorRows.useDonor()) {
      data.donorRows = this.donorRows.donorId();
      data.donorRowsType = this.donorRows.donorVariantsType();
      const donorRowsQuestion = window.QUESTIONS.find(i => i.id == data.donorRows)
      const isDonorClassifierWithListType = donorRowsQuestion?.main_question_type === 19 && donorRowsQuestion?.dictionary_list_type === 'list'
      data.isDonorRowsClassifierWithListType = isDonorClassifierWithListType
    }
    if (this.donorColumns.useDonor()) {
      data.donorColumns = this.donorColumns.donorId();
      data.donorColumnsType = this.donorColumns.donorVariantsType();
      const donorColumnsQuestion = window.QUESTIONS.find(i => i.id == data.donorColumns)
      const isDonorClassifierWithListType = donorColumnsQuestion?.main_question_type === 19 && donorColumnsQuestion?.dictionary_list_type === 'list'
      data.isDonorColumnsClassifierWithListType = isDonorClassifierWithListType
    }

    data.show_tooltips = this.useTooltips()
    return data;
  }

  isValid() {
    if (!this.defaultValidation()) {
      return false;
    }
    if (this.galleryEnabled() && !super.isValid()) {
      return false;
    }
    return this.rowsVariantsList.isValid() && this.colsVariantsList.isValid();
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}

export default Matrix3DQuestion;
