<template id="question-form-template-items">
  <div class="form-group" data-bind="let: { mediaController: question.mediaController }, dnd: function(files) {
    question.mediaController.imageLoader.loadFiles(files);
  }, dndDisabled: question.isFullBlocked">
    <label class="form-label"><span data-bind="text: _t('question', 'Загрузка изображений')"></span></label>

    <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: _t('question', 'Загрузка изображений')" type="button" title="">
    </button>

    <div class="survey-question__media-form-control">
      <!-- ko ifnot: question.isFullBlocked -->
      <div class="survey-question__media-form-control-actions">

        <button class="btn btn-default btn-with-icon btn-upload-disk
                            survey-question__media-form-control-action" data-bind="
                            click: function () { mediaController.imageLoader.open() },
                            attr: {disabled: mediaController.isImageLoading}" type="button">
          <span data-bind="text: _t('question', 'C компьютера')"></span>
        </button>

      </div>
      <!-- /ko -->

      <div class="survey-question__media-form-control-list">
        <div class="survey-question__media-form-control-list-content">
          <!-- ko foreach: {
            data: mediaController.images,
            as: 'image',
            beforeRemove: fadeBeforeRemoveFactory(200),
            afterAdd: fadeAfterAddFactory(200, 200)
          } -->
          <file-loader-preview class="ml-10p mt-10p" params="
                        loading: image.loading,
                        preview: image.getUrl(),
                        gallery: mediaController.imageGallery,
                        index:  imageIndex(),
                        onRemove: function() {
                            mediaController.removeImage(image);
                        },
                        disabled: question.isFullBlocked">
            <!-- ko if: mediaController.images().length > 1 -->
            <input class="survey-question__media-form-control-list-item-label" data-bind="stopClick, textInput: image.label,
                                event: {
                                    change: function() { image.updateLabel() },
                                },
                                attr: { 'data-image-id': image.id },
                                disable: question.isFullBlocked" maxlength="3">
            <!-- /ko -->
          </file-loader-preview>
          <!-- /ko -->

          <!-- ko template: {
              foreach: templateIf(mediaController.isImageLoading(), $data),
              afterAdd: fadeAfterAddFactory(200, 200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
          <div class="survey-question__media-form-control-list-loading-placeholder" data-bind="attr: { placeholder: _t('question', 'Идёт загрузка данных...') }">
          </div>
          <!-- /ko -->
        </div>
      </div>
    </div>

    <!-- ko foreach: mediaController.imageLoader.errors -->
    <file-loader-error params="error: $data"></file-loader-error>
    <!-- /ko -->

    <!-- ko if: controller.formControlErrorStateMatcher(mediaController.images) -->
    <div class="form-error" data-bind="text: mediaController.images.error()"></div>
    <!-- /ko -->

    <dnd-cover class="my-n4 mx-n3 foquz-dnd-cover--sm" params="type: 'image', mode: 'multiple'" data-bind="css: {
            'dense': mediaController.images().length < 1
        }"></dnd-cover>
  </div>

  <?php /*
  <div class="form-group">
    <label class="form-label"><span data-bind="text: _t('question', 'Информация о блюде')"></span></label>
    <button type="button" class="btn-question" data-toggle="tooltip, tooltipText: _t('question', 'Информация о блюде')" data-placement="top" ></button>
    <div class="quest__checkbox-list-form-control">
      <div class="form-check">
        <input type="checkbox" class="form-check-input" id="category" data-bind="checked: question.showItemCategory, disable: question.isFullBlocked">
        <label class="form-check-label" for="category"><span data-bind="text: _t('question', 'Категория')"></span></label>
      </div>

      <div class="form-check">
        <input type="checkbox" class="form-check-input" id="b_name" data-bind="checked: question.showItemName, disable: question.isFullBlocked">
        <label class="form-check-label" for="b_name"><span data-bind="text: _t('question', 'Название')"></span></label>
      </div>

      <!-- <div class="form-check">-->
      <!-- <input type="checkbox" class="form-check-input" id="weight" data-bind="checked: weight">-->
      <!-- <label class="form-check-label" for="weight">Порция</label>-->
      <!-- </div>-->
    </div>
  </div>
  */ ?>

  <div class="form-group quest__rating-type-form-group">
    <label class="form-label"><span data-bind="text: _t('question', 'Тип оценки / выбора')"></span></label>
    <button type="button" class="btn-question" data-toggle="tooltip, tooltipText: _t('question', 'Тип оценки. Оценка может быть звёздами либо вариантами на выбор')" data-placement="top" title=""></button>
    <div class="quest__rating-type-form-control">
      <div class="btn-group btn-group-select" role="group" data-bind="click: function () {
        if (question.isFullBlocked) return false;
          question.tryChangeBlockedParam();
         }">
        <input type="text" class="form-control readonly" id="btnGroupSelect" name="" placeholder="" data-bind="value: _t('question', 'Звёзды 5' )" readonly aria-haspopup="true" aria-expanded="false" disabled>
      </div>

      <ul class="quest__form-stars">
        <li class="active"></li>
        <li class="active"></li>
        <li class="active"></li>
        <li class="active"></li>
        <li class="active"></li>
      </ul>
    </div>
  </div>

  <div class="form-group">
    <label class="form-label"><span data-bind="text: _t('question', 'Мин. сумма блюда для оценки')"></span>, <i class="fas fa-ruble-sign"></i></label>
    <button type="button" class="btn-question" data-toggle="tooltip, tooltipText: _t('question', 'Мин. сумма блюда для оценки')" data-placement="top"></button>
    <input class="form-control quest__min-sum-form-control" data-bind="textInput: question.itemMinPrice, disable: question.isFullBlocked" maxlength="99" max="999" minlength="0" min="0" step="1" type="number" onblur="this.value = Math.abs(parseInt(this.value) || 0)">
  </div>

  <div class="form-group">
    <div class="form-check survey-question__custom-variant-checkbox">
      <input type="checkbox" class="form-check-input" id="exampleCheck2" data-bind="checked: question.rateAllItems, disable: question.isFullBlocked">
      <label class="form-check-label" for="exampleCheck2" data-bind="text: _t('question', 'Оценить все сразу')"></label>
    </div>
  </div>


  <div class="form-group">
    <div class="form-check survey-question__custom-variant-checkbox">
      <input type="checkbox" class="form-check-input" data-bind="checked: question.commentEnabled, disable: question.isFullBlocked" id="survey-question-custom-variant-available-checkbox">
      <label class="form-check-label" for="survey-question-custom-variant-available-checkbox" data-bind="text: _t('question', 'Свой комментарий (произвольное поле)')"></label>
    </div>
  </div>

  <!-- Комментарий -->
  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: fadeAfterAddFactory(200),
  } -->
  <!-- ko component: {
      name: 'text-field',
      params: {
         controller: question.commentField,
         disabled: question.isFullBlocked
      }
  } -->
  <!-- /ko -->
  <!-- /ko -->
  <!-- /Комментарий -->
</template>