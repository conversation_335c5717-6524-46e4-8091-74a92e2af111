import RateQuestion from '../rate'
import { Translator } from '@/utils/translate';

class ItemsQuestion extends RateQuestion {
	constructor(controller, config) {
		super(controller, config);
		this.isItemsQuestion = true;

		this.showItemCategory = ko.observable(true);
		this.showItemName = ko.observable(true);
		this.showItemPortion = ko.observable(true);
		this.itemMinPrice = ko.observable('');
		this.rateAllItems = ko.observable(false);

		[
			this.showItemName,
			this.showItemCategory,
			this.showItemPortion,
			this.itemMinPrice,
			this.rateAllItems
		].forEach(f => f.subscribe(v => this.onChange()));
	}

	updateData(data) {
		super.updateData(data);

		this.showItemCategory(data.showItemCategory);
		this.showItemName(data.showItemName)
		this.showItemPortion(data.showItemPortion)
		this.itemMinPrice(data.itemMinPrice)
		this.rateAllItems(data.rateAllItems);
	}

	get isMediaRequired() {
		return false;
	  }

	getData() {
		let data = super.getData();

		data.isItemsQuestion = this.isItemsQuestion;

		data.showItemCategory = this.showItemCategory();
		data.showItemName = this.showItemName();
		data.showItemPortion = this.showItemPortion();
		data.itemMinPrice = this.itemMinPrice();
		data.rateAllItems = this.rateAllItems();

		return data;
	}

	isValid() {
		return super.isValid();
	}
}

export default ItemsQuestion;
