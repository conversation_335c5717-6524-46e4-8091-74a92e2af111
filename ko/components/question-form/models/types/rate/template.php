<template id="question-form-template-rate">
    <!-- ko let: { $translator: question.translator } -->
    <!-- Табы: текст/изображение/видео -->
    <div class="hat-radio-group hat-radio-group--dense survey-question__media-type-selector">
        <!-- ko foreach: { data: question.mediaTypes, as: 'mediaType' } -->
        <div class="hat-radio-group__radio" data-bind="let: { inputId: 'survey-question-media-type-selector-' + mediaType.id }">
            <input class="hat-radio-group__radio-input" type="radio" name="media-type" data-bind="value: mediaType.id,
                checked: question.mediaType,
                disable: question.isBlocked() || question.isFullBlocked,
                attr: { id: inputId }" />
            <label class="hat-radio-group__radio-label" data-bind="attr: { for: inputId },
                click: function() {
                    if (question.isFullBlocked) return false;
                    if (question.isBlocked()) question.tryChangeBlockedParam();
                    else return true;
                }">
                <i class="survey-question__media-type-selector-value-icon" data-bind="class: 'survey-question__media-type-selector-value-' + mediaType.icon + '-icon'"></i>
                <span data-bind="text: mediaType.label"></span>
            </label>
        </div>
        <!-- /ko -->
    </div>
    <!-- /Табы: текст/изображение/видео -->


    <!-- ko let: { mediaTypeTemplate: question.getMediaTypeTemplate() } -->
    <!-- ko template: {
        foreach: templateIf(mediaTypeTemplate === 'text', $data),
    } -->
    <div class="form-group" data-bind>
        <label class="form-label" for="text"><span data-bind="text: $translator.t('Текст')"></span></label>

        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно ввести произвольное описание, которое будет отображаться внутри вопроса. Может использоваться для уточнения основного вопроса')" type="button" title="">
        </button>

        <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: question.text().length">
            <textarea class="form-control" data-bind="
                        textInput: question.text,
                        css: {
                            'is-invalid': controller.formControlErrorStateMatcher(question.text),
                            'is-valid': controller.formControlSuccessStateMatcher(question.text)
                        }, autosizeTextarea,
                        disable: question.isFullBlocked" id="question-main-text" maxlength="500">
                    </textarea>

            <div class="chars-counter__value"></div>
        </div>

        <!-- ko if: controller.formControlErrorStateMatcher(question.text) -->
        <div class="form-error" data-bind="text: question.text.error()"></div>
        <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- ko template: {
        foreach: templateIf(mediaTypeTemplate === 'image', $data),
    } -->
    <div class="form-group" data-bind="let: { mediaController: question.mediaController }, dnd: function(files) {
        question.mediaController.imageLoader.loadFiles(files);
        }, dndDisabled: question.isFullBlocked">
        <label class="form-label"><span data-bind="text: $translator.t('Загрузка изображений')"></span></label>

        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно загрузить изображения только с компьютера. Поддерживаются форматы: PNG, JPG, JPEG, GIF. Ограничение на размер файла - 5 мб')" type="button" title="">
        </button>

        <div class="survey-question__media-form-control">
            <!-- ko ifnot: question.isFullBlocked -->
            <div class="survey-question__media-form-control-actions">


                <button class="f-btn" data-bind="
                            click: function () { mediaController.imageLoader.open() },
                            disable: mediaController.isImageLoading" type="button">
                    <span class="f-btn-prepend">
                        <svg-icon params="name: 'clip'"></svg-icon>
                    </span>
                    <span data-bind="text: $translator.t('С компьютера')"></span>
                </button>

            </div>
            <!-- /ko -->

            <!-- ko template: {
                foreach: templateIf(mediaController.images().length > 0, $data),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
            } -->
            <div class="survey-question__media-form-control-list">
                <div class="survey-question__media-form-control-list-content">
                    <!-- ko foreach: {
                                data: mediaController.images,
                                as: 'image',
                                beforeRemove: fadeBeforeRemoveFactory(200),
                                afterAdd: fadeAfterAddFactory(200)
                            } -->
                    <file-loader-preview class="ml-10p mt-10p" params="
                        loading: image.loading,
                        preview: image.getUrl(),
                        gallery: mediaController.imageGallery,
                        index:  imageIndex(),
                        onRemove: function() {
                            mediaController.removeImage(image);
                        },
                        disabled: question.isFullBlocked">
                        <!-- ko if: mediaController.images().length > 1 -->
                        <input class="survey-question__media-form-control-list-item-label" data-bind="stopClick, textInput: image.label,
                                event: {
                                    change: function() { image.updateLabel() },
                                },
                                attr: { 'data-image-id': image.id }" maxlength="3">
                        <!-- /ko -->
                    </file-loader-preview>
                    <!-- /ko -->
                </div>
            </div>
            <!-- /ko -->
        </div>

        <!-- ko foreach: mediaController.imageLoader.errors -->
        <file-loader-error params="error: $data"></file-loader-error>
        <!-- /ko -->

        <!-- ko if: controller.formControlErrorStateMatcher(mediaController.images) -->
        <div class="form-error" data-bind="text: mediaController.images.error()"></div>
        <!-- /ko -->

        <dnd-cover class="my-n4 mx-n3 foquz-dnd-cover--sm" data-bind="css: {
            'dense': mediaController.images().length < 1
        }" params="type: 'image', mode: 'multiple'">


        </dnd-cover>
    </div>
    <!-- /ko -->


    <!-- ko template: {
        foreach: templateIf(mediaTypeTemplate === 'video', $data),
    } -->
    <div class="form-group" data-bind="let: { mediaController: question.mediaController }, dnd: function(files) {
        question.mediaController.videoLoader.loadFiles(files);
    }">
        <label class="form-label"><span data-bind="text: $translator.t('Загрузка видео')"></span></label>

        <!-- ko ifnot: question.isFullBlocked -->
        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно загрузить видео с компьютера, либо с сервиса Youtube. Поддерживаются форматы: MP4, WEBM. Ограничение на размер файла 10 мб')" type="button" title="">
        </button>
        <!-- /ko -->

        <div class="survey-question__media-form-control">
            <!-- ko ifnot: question.isFullBlocked -->
            <div class="survey-question__media-form-control-actions">

                <button class="f-btn" data-bind="
                            click: function () { mediaController.videoLoader.open() },
                            attr: {disabled: mediaController.isVidelLoading}" type="button">

                    <span class="f-btn-prepend">
                        <svg-icon params="name: 'clip'"></svg-icon>
                    </span>
                    <span data-bind="text: $translator.t('С компьютера')"></span>
                </button>

                <button class="btn btn-default btn-with-icon btn-upload-link
                                survey-question__media-form-control-actions-item" data-bind="
                            click: function () { mediaController.openAddVideoByLinkModal(); },
                            attr: {disabled: mediaController.isVideoLoading} " type="button">
                    <span data-bind="text: $translator.t('По ссылке на Youtube')"></span>
                </button>

            </div>
            <!-- /ko -->



            <!-- ko template: {
                foreach: templateIf(mediaController.videos().length > 0, $data),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
            } -->
            <div class="survey-question__media-form-control-list">
                <div class="survey-question__media-form-control-list-content">
                    <!-- ko foreach: {
                        data: mediaController.videos,
                        as: 'video',
                        beforeRemove: fadeBeforeRemoveFactory(200),
                        afterAdd: fadeAfterAddFactory(200, 200)
                    } -->
                    <file-loader-preview class="ml-10p mt-10p" params="
                        loading: video.loading,
                        preview: video.preview,
                        gallery: mediaController.videoGallery,
                        index: videoIndex(),
                        onRemove: function() {
                            mediaController.removeVideo(video);
                        },
                        disabled: question.isFullBlocked">
                        <!-- ko if: !question.isFullBlocked && mediaController.videos().length > 1 -->
                        <input class="survey-question__media-form-control-list-item-label" data-bind="stopClick, textInput: video.label,
                                event: {
                                    change: function() { video.updateLabel() },
                                },
                                attr: { 'data-image-id': video.id }" maxlength="3">
                        <!-- /ko -->
                    </file-loader-preview>
                    <!-- /ko -->


                </div>
            </div>
            <!-- /ko -->
        </div>




        <!-- ko foreach: mediaController.videoLoader.errors -->
        <file-loader-error params="error: $data"></file-loader-error>
        <!-- /ko -->

        <!-- ko if: controller.formControlErrorStateMatcher(mediaController.videos) -->
        <div class="form-error" data-bind="text: mediaController.videos.error()"></div>
        <!-- /ko -->

        <dnd-cover class="my-n4 mx-n3 foquz-dnd-cover--sm" params="type: 'video', mode: 'multiple'" data-bind="css: {
            'dense': mediaController.videos().length < 1
        }"></dnd-cover>
    </div>
    <!-- /ko -->
    <!-- /ko -->


    <!-- Тип оценки/выбора: 5 звезд/варианты на выбор -->
    <div class="survey-question__assessment-type-selector">
        <div class="form-group survey-question__assessment-type-selector-form-group">
            <label class="form-label" for="assessment-type"><span data-bind="text: $translator.t('Тип оценки / выбора')"></span></label>

            <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Для вопроса можно добавить рейтинг со звёздами, либо варианты на выбор')" type="button" title="">
            </button>

            <div class="select2-wrapper" data-bind="click: function() {
                if (question.isFullBlocked) return false;
                    if (question.isBlocked()) question.tryChangeBlockedParam();
                    else return true;
                }">
                <select data-bind="
                    value: question.assessmentType,

                    valueAllowUnset: true,
                    lazySelect2: {
                        containerCssClass: 'form-control',
                        wrapperCssClass: 'select2-container--form-control'
                    },
                    disable: question.isBlocked() || question.isFullBlocked" id="assessment-type">
                    <!-- ko foreach: question.assessmentTypes -->
                    <option data-bind="value: id, text: label"></option>
                    <!-- /ko -->
                </select>
            </div>

        </div>

        <!-- ko template: {
            foreach: templateIf(question.isStarsAssessmentType(), $data),
            afterAdd: fadeAfterAddFactory(200),
            beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
        <div class="survey-question__assessment-type-selector-rating" data-bind="component: { name: 'rating', params: { value: 3 } }">
        </div>
        <!-- /ko -->
    </div>


    <!-- Тип оценки/выбора: 5 звезд -->
    <!-- ko template: {
        foreach: templateIf(question.isStarsAssessmentType(), $data),
        afterAdd: fadeAfterAddFactory(200),
    } -->

    <hr class="mt-0">

    <!-- Уточняющий вопрос/комментарий -->
    <div class="row mb-4 pt-1">
        <div class="col-6">
            <switch class="mb-0" params="checked: question.clarifyingQuestionEnabled, disabled: question.isBlocked() || question.isFullBlocked" data-bind="click: function() {
                    if (question.isFullBlocked) return false;
                    if (question.isBlocked()) question.tryChangeBlockedParam();
                    else return true;
                }"><span data-bind="text: $translator.t('Уточняющий вопрос')"></span></switch>


            <!-- ko template: {
                foreach: templateIf(question.clarifyingQuestionEnabled(), $data),
                afterAdd: fadeAfterAddFactory(200, 200),
                beforeRemove: fadeBeforeRemoveFactory(200)
            } -->
            <div class="pt-1 mt-4">
                <div class="form-check mb-2 mt-0">
                    <input type="checkbox" class="form-check-input" data-bind="checked: question.clarifyingQuestionForAllRates, enable: question.clarifyingQuestionEnabled, disable: question.isFullBlocked" id="survey-question-clarifying-type-selector-for-all-checkbox">
                    <label class="form-check-label" for="survey-question-clarifying-type-selector-for-all-checkbox">
                        <span data-bind="text: $translator.t('Для всех оценок')"></span>
                        <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Уточняющий вопрос по умолчанию отображается при оценке меньше 5 звёзд. При выборе настройки &quot;Для всех оценок&quot; вопрос будет отображаться всегда, независимо от ответа респондента')" type="button" title="">
                        </button>
                    </label>
                </div>
            </div>
            <!-- /ko -->
        </div>

        <div class="col-6">
            <!-- ko ifnot: question.isSystem -->
            <switch class="mb-0" params="checked: question.commentEnabled, disabled: question.isFullBlocked || (question.isBlocked() && question.clarifyingQuestionEnabled())" data-bind="click: function() {
                    if (question.isFullBlocked) return false;
                    if (question.isBlocked() && question.clarifyingQuestionEnabled()) question.tryChangeBlockedParam();
                    else return true;
                }"><span data-bind="text: $translator.t('Комментарий')"></span></switch>
            <!-- /ko -->

        </div>
    </div>
    <!-- /Уточняющий вопрос/комментарий -->


    <!-- Уточняющий вопрос -->
    <!-- ko template: {
        foreach: templateIf(question.clarifyingQuestionEnabled(), $data),
        afterAdd: slideAfterAddFactory(200, 150),
        beforeRemove: fadeBeforeRemoveFactory(100)
    } -->
    <div>
        <div class="form-group">
            <label class="form-label" for="clarifyingQuestion"><span data-bind="text: $translator.t('Уточняющий вопрос')"></span></label>
            <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно добавить вопрос, который дополнительно будет задан респонденту для уточнения его ответа. Уточняющий вопрос должен быть всегда с вариантами ответов')" type="button" title="">
            </button>
            <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: question.clarifyingQuestionText().length">
                <textarea class="form-control" data-bind="
                        textInput: question.clarifyingQuestionText,
                        disable: question.isFullBlocked,
                        css: {
                            'is-invalid': controller.formControlErrorStateMatcher(question.clarifyingQuestionText),
                            'is-valid': controller.formControlSuccessStateMatcher(question.clarifyingQuestionText)
                        },
                        autosizeTextarea" id="clarifyingQuestion" maxlength="500">
                    </textarea>
                <div class="chars-counter__value"></div>
            </div>
            <!-- ko if: controller.formControlErrorStateMatcher(question.clarifyingQuestionText) -->
            <div class="form-error" data-bind="text: question.clarifyingQuestionText.error()"></div>
            <!-- /ko -->
        </div>
        <question-form-variants-list params="controller: question.clarifyingQuestionController">
        </question-form-variants-list>
    </div>

    <!-- /ko -->
    <!-- /Уточняющий вопрос -->

    <!-- Комментарий -->
    <!-- ko template: {
        foreach: templateIf(question.commentEnabled(), $data),
        afterAdd: slideAfterAddFactory(200, 200),
        beforeRemove: slideBeforeRemoveFactory(150)
    } -->
    <div>
        <div class="form-group">
            <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
            <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
        </div>

        <!-- ko component: {
            name: 'text-field',
            params: {
               controller: question.commentField,
               intervalText: $translator.t('Кол-во символов в комментарии'),
               disabled: question.isFullBlocked
            }
        } -->
        <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /Комментарий -->

    <!-- /ko -->
    <!-- /Тип оценки/выбора: 5 звезд -->





    <!-- Тип оценки/выбора: варианты на выбор -->
    <!-- ko template: {
        foreach: templateIf(!question.isStarsAssessmentType(), $data),
        afterAdd: fadeAfterAddFactory(200, 200),
        beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
    <question-form-variants-list params="controller: question.variantsController">
    </question-form-variants-list>
    <!-- /ko -->
    <!-- /Тип оценки/выбора: варианты на выбор -->

    <!-- /ko -->
</template>