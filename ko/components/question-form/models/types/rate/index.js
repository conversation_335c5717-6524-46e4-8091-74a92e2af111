import Question from "../../question";

import { VariantsController } from "../../../controllers/variants-controller";
import { MediaController } from "../../../controllers/media-controller";
import TextField from "../../text-field";

import { RATE_QUESTION } from "Data/question-types";
import {
  ASSESSMENT_TYPE_STARS,
  ASSESSMENT_TYPE_VARIANTS,
} from "../../../data/assessment-types";
import {
  MEDIA_TYPE_IMAGE,
  MEDIA_TYPE_TEXT,
  MEDIA_TYPE_VIDEO,
} from "../../../data/media-types";
import { createFileInput } from "../../../../../../ko/utils/file-loader/create-file-input";
import { Translator } from "@/utils/translate";
const ValidationTranslator = Translator("validation");
class RateQuestion extends Question {
  constructor(controller, config) {
    super(controller, config);

    this.type = RATE_QUESTION;

    this.mediaType = ko.observable(MEDIA_TYPE_TEXT);
    this.mediaTypes = [
      {
        id: MEDIA_TYPE_TEXT,
        label: this.translator.t("Текст")(),
        icon: "text",
      },
      {
        id: MEDIA_TYPE_IMAGE,
        label: this.translator.t("Изображение")(),
        icon: "image",
      },
      {
        id: MEDIA_TYPE_VIDEO,
        label: this.translator.t("Видео")(),
        icon: "video",
      },
    ];
    this.getMediaTypeTemplate = () => {
      return this.mediaType();
    };

    this.text = ko.observable("");
    this.mediaController = new MediaController(this);

    this.mediaController.images.extend({
      validation: {
        validator: (v) => {
          if (!this.isMediaRequired) return true;
          return v.length > 0;
        },
        message: () =>
          ValidationTranslator.t(
            "Необходимо добавить хотя бы одно изображение"
          )(),
        onlyIf: () => {
          return this.mediaType() == MEDIA_TYPE_IMAGE;
        },
      },
    });

    this.mediaController.videos.extend({
      validation: {
        validator: (v) => {
          return v.length > 0;
        },
        message: () =>
          ValidationTranslator.t("Необходимо добавить хотя бы одно видео")(),
        onlyIf: () => {
          return this.mediaType() == MEDIA_TYPE_VIDEO;
        },
      },
    });

    this.assessmentType = ko.observable(ASSESSMENT_TYPE_STARS);
    this.assessmentTypes = [
      {
        id: ASSESSMENT_TYPE_STARS,
        label: this.translator.t("5 звезд")(),
      },
      {
        id: ASSESSMENT_TYPE_VARIANTS,
        label: this.translator.t("Варианты на выбор")(),
      },
    ];

    this.isStarsAssessmentType = ko.pureComputed(() => {
      return this.assessmentType() == ASSESSMENT_TYPE_STARS;
    });

    this.clarifyingQuestionEnabled = ko.observable(false);
    this.clarifyingQuestionText = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => {
          return (
            this.assessmentType() == ASSESSMENT_TYPE_STARS &&
            this.clarifyingQuestionEnabled()
          );
        },
      },
    });
    this.clarifyingQuestionController = new VariantsController(
      {
        withCustomAnswer: true,
        withVariantsTypeSelect: true,
        withAddVariantButton: true,
        addFirstVariant: true,
      },
      this
    );
    this.clarifyingQuestionForAllRates = ko.observable(0);

    this.commentEnabled = ko.observable(false);
    this.commentField = new TextField();
    this.commentLabel = ko.observable('')

    this.clarifyingQuestionEnabled.subscribe((v) => {
      if (!v) this.clarifyingQuestionForAllRates(false);
      else this.commentEnabled(false);
    });
    this.commentEnabled.subscribe((v) => {
      if (v) this.clarifyingQuestionEnabled(false);
    });

    this.variantsController = new VariantsController(
      {
        withCustomAnswer: true,
        withAddVariantButton: true,
        withVariantsTypeSelect: true,
        addFirstVariant: true,
      },
      this
    );

    [
      this.mediaType,
      this.text,
      this.assessmentType,
      this.clarifyingQuestionEnabled,
      this.commentEnabled,
      this.commentLabel,
      this.clarifyingQuestionForAllRates,
      this.clarifyingQuestionText,
    ].forEach((f) => f.subscribe((v) => this.onChange()));

    [
      this.mediaController,
      this.clarifyingQuestionController,
      this.variantsController,
      this.commentField,
    ].forEach((c) => c.isChanged.subscribe((v) => this.onChange()));
  }

  get isMediaRequired() {
    return true;
  }

  updateData(data, reset) {
    super.updateData(data);

    this.mediaType(data.rateMediaType);
    this.text(data.rateText || "");

    this.mediaController.setImages(data.rateImages, reset);
    this.mediaController.setVideos(data.rateVideos, reset);

    this.assessmentType(data.rateAssessmentType);

    this.clarifyingQuestionEnabled(data.rateClarifyingQuestionEnabled);
    this.clarifyingQuestionForAllRates(data.rateClarifyingQuestionForAllRates);
    this.clarifyingQuestionText(data.rateClarifyingQuestionText || "");
    this.clarifyingQuestionController.updateData({
      type: data.rateClarifyingQuestionVariantsType,
      variants: data.rateClarifyingQuestionVariants,
      customAnswerEnabled: data.rateClarifyingQuestionCustomAnswerEnabled,
      customAnswerRange: data.rateClarifyingQuestionCustomAnswerLengthRange,
      customAnswerPlaceholder:
        data.rateClarifyingQuestionCustomAnswerPlaceholder,
      customAnswerLabel: data.customAnswerLabel,
    });

    this.commentEnabled(data.rateCommentEnabled);
    this.commentLabel(data.rateCommentLabel || '')
    this.commentField.updateData({
      range: data.rateCommentLengthRange,
      placeholder: data.rateCommentPlaceholder,
    });

    this.variantsController.updateData({
      type: data.rateVariantsType,
      variants: data.rateVariants,
      customAnswerEnabled: data.rateVariantsCustomAnswerEnabled,
      customAnswerRange: data.rateVariantsCustomAnswerLengthRange,
      customAnswerPlaceholder: data.rateVariantsCustomAnswerPlaceholder,
      customAnswerLabel: data.customAnswerLabel,
    });
  }

  getData() {
    let data = super.getData();

    data.rateMediaType = this.mediaType();
    data.rateText = this.text();
    const rateImages = this.mediaController.getImages();

    data.rateImages = rateImages;
    data.rateVideos = this.mediaController.getVideos();
    data.rateAssessmentType = this.assessmentType();

    data.rateClarifyingQuestionEnabled = this.clarifyingQuestionEnabled();
    data.rateClarifyingQuestionForAllRates =
      this.clarifyingQuestionForAllRates() ? 1 : 0;
    data.rateClarifyingQuestionText = this.clarifyingQuestionText();

    let clarifyingQuestionVariantsData =
      this.clarifyingQuestionController.getData();
    data.rateClarifyingQuestionVariantsType =
      clarifyingQuestionVariantsData.variantsType;
    data.rateClarifyingQuestionVariants =
      clarifyingQuestionVariantsData.variants;
    data.rateClarifyingQuestionCustomAnswerEnabled =
      clarifyingQuestionVariantsData.customAnswerEnabled;
    data.rateClarifyingQuestionCustomAnswerLengthRange =
      clarifyingQuestionVariantsData.customAnswerRange;
    data.rateClarifyingQuestionCustomAnswerPlaceholder =
      clarifyingQuestionVariantsData.customAnswerPlaceholder;
    data.rateClarifyingQuestionCustomAnswerLabel =
      clarifyingQuestionVariantsData.customAnswerLabel;

    data.rateCommentEnabled = this.commentEnabled();
    let commentFieldData = this.commentField.getData();
    data.rateCommentLengthRange = commentFieldData.range;
    data.rateCommentPlaceholder = commentFieldData.placeholder;
    data.rateCommentLabel = this.commentLabel();

    let variantsData = this.variantsController.getData();
    data.rateVariantsType = variantsData.variantsType;
    data.rateVariants = variantsData.variants;
    data.rateVariantsCustomAnswerEnabled = variantsData.customAnswerEnabled;
    data.rateVariantsCustomAnswerLengthRange = variantsData.customAnswerRange;
    data.rateVariantsCustomAnswerPlaceholder =
      variantsData.customAnswerPlaceholder;
    data.customAnswerLabel = variantsData.customAnswerLabel;

    return data;
  }

  isValid() {
    if (!super.isValid()) return false;

    if (!this.clarifyingQuestionText.isValid()) return false;

    if (!this.mediaController.images.isValid()) return false;
    if (!this.mediaController.videos.isValid()) return false;

    if (this.assessmentType() == ASSESSMENT_TYPE_STARS) {
      if (this.clarifyingQuestionEnabled()) {
        if (!this.clarifyingQuestionController.isValid()) return false;
      }
    } else if (this.assessmentType() == ASSESSMENT_TYPE_VARIANTS) {
      if (!this.variantsController.isValid()) return false;
    }

    // TODO: кол-во изображений или кол-во видео или длина текста
    // clarifyingQuestion
    return true;
  }

  autoFill(config = {}) {
    super.autoFill(config);

    this.assessmentType(1);
    this.clarifyingQuestionEnabled(true);
    this.clarifyingQuestionText("Текст уточняющего вопроса");

    this.clarifyingQuestionController.updateData({
      type: 1,
      variants: [
        { id: 0, value: "Вариант 1" },
        { id: 0, value: "Вариант 2" },
        { id: 0, value: "Вариант 3" },
        { id: 0, value: "Вариант 4" },
        { id: 0, value: "Вариант 5" },
        { id: 0, value: "Вариант 6" },
        { id: 0, value: "Вариант 7" },
        { id: 0, value: "Вариант 8" },
        { id: 0, value: "Вариант 9" },
        { id: 0, value: "Вариант 10" },
      ],
      customAnswerEnabled: true,
      customAnswerRange: [0, 250],
      customAnswerPlaceholder: "Текст подсказки",
    });

    return new Promise((res) => {
      this.mediaType(MEDIA_TYPE_IMAGE);

      if (RateQuestion._autoFillFiles && RateQuestion._autoFillFiles.length) {
        this.mediaController.imageLoader.loadFiles(RateQuestion._autoFillFiles);
        setTimeout(() => res(), 1000);
      } else {
        createFileInput(
          ({ files }) => {
            RateQuestion._autoFillFiles = files;
            this.mediaController.imageLoader.loadFiles(files);

            setTimeout(() => res(), 1000);
          },
          { multiple: true, open: true }
        );
      }
    });
  }
}

export default RateQuestion;
