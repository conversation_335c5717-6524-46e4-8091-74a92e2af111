import Question from "../../question";
import { MediaVariantsController } from "../../../controllers/media-variants-controller";
import { MEDIA_VARIANTS_QUESTION } from "Data/question-types";
import {
  MEDIA_VARIANT_TYPE_IMAGE,
  MEDIA_VARIANT_TYPE_VIDEO,
} from "../../../data/media-variants-types";
import { Translator } from "@/utils/translate";
const QuestionTranslator = Translator("question");
import TextField from "../../text-field";

class MediaVariantsQuestion extends Question {
  constructor(controller, config) {
    super(controller, config);

    this.type = MEDIA_VARIANTS_QUESTION;

    this.mediaType = ko.observable(MEDIA_VARIANT_TYPE_IMAGE);
    this.mediaTypes = [
      {
        id: MEDIA_VARIANT_TYPE_IMAGE,
        label: QuestionTranslator.t("Изображение")(),
        icon: "image",
      },
      {
        id: MEDIA_VARIANT_TYPE_VIDEO,
        label: QuestionTranslator.t("Видео")(),
        icon: "video",
      },
    ];

    this.imagesVariantsController = new MediaVariantsController(
      {
        withVariantsTypeSelect: true,
        withCustomAnswer: false,
        withAddVariantButton: false,
        minVariantsCount: 2,
        mediaType: MEDIA_VARIANT_TYPE_IMAGE,
        variantDescriptionLimit: 150,
        withPoints: this.withPoints,
        withAnswersCountLimit: true,
        withRandomOption: true,
        maxPointsCalcMethod: false
      },
      this
    );

    this.videosVariantsController = new MediaVariantsController(
      {
        withVariantsTypeSelect: true,
        withCustomAnswer: false,
        minVariantsCount: 2,
        mediaType: MEDIA_VARIANT_TYPE_VIDEO,
        variantDescriptionLimit: 150,
        withPoints: this.withPoints,
        withAnswersCountLimit: true,
        withRandomOption: true,
        maxPointsCalcMethod: false
      },
      this
    );

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    this.commentEnabled = ko.observable(false);
    this.commentField = new TextField();
    this.commentLabel = ko.observable("");
    this.commentIsRequired = ko.observable(null);

    this.maxPointsCalcMethod = ko.observable(false);

    [
      this.imagesVariantsController,
      this.videosVariantsController,
      this.commentField,
    ].forEach((c) => c.isChanged.subscribe((v) => this.onChange()));

    [
      this.mediaType,
      this.commentEnabled,
      this.commentLabel,
      this.commentIsRequired,
      this.skip,
      this.skipText,
      this.maxPointsCalcMethod,
    ].forEach((f) => f.subscribe((v) => this.onChange()));
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  updateData(data) {
    super.updateData(data);

    this.mediaType(data.mediaVariantsMediaType);
    this.maxPointsCalcMethod(data.maxPointsCalcMethod)

    let variantsData = {
      type: data.mediaVariantsType,
      variants: data.mediaVariants,
      answersCountLimit: data.answersCountLimit,
      randomOrder: data.randomOrder,
      maxPointsCalcMethod: data.maxPointsCalcMethod,
    };

    if (this.mediaType() === MEDIA_VARIANT_TYPE_IMAGE) {
      this.imagesVariantsController.updateData(variantsData);
    } else {
      this.videosVariantsController.updateData(variantsData);
    }

    this.commentEnabled(data.mediaVariantsCommentEnabled);
    this.commentLabel(data.mediaVariantsCommentLabel || "");
    this.commentIsRequired(data.mediaVariantsCommentRequired)
    this.commentField.updateData({
      range: data.mediaVariantsCommentLengthRange,
      placeholder: data.mediaVariantsCommentPlaceholder,
      required: data.mediaVariantsCommentRequired
    });

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
  }

  getData() {
    let data = super.getData();

    data.mediaVariantsMediaType = this.mediaType();

    let variantsData;
    if (this.mediaType() == MEDIA_VARIANT_TYPE_VIDEO) {
      variantsData = this.videosVariantsController.getData();
    } else if (this.mediaType() == MEDIA_VARIANT_TYPE_IMAGE) {
      variantsData = this.imagesVariantsController.getData();
    } else {
      throw new Error(
        "Media Variants Question error: unknown mediaType",
        this.mediaType()
      );
    }

    data.mediaVariantsType = variantsData.variantsType;
    data.mediaVariants = variantsData.variants;
    data.answersCountLimit = variantsData.answersCountLimit;
    data.randomOrder = variantsData.randomOrder;

    data.mediaVariantsCommentEnabled = this.commentEnabled();
    data.mediaVariantsCommentLabel = this.commentLabel();
    data.maxPointsCalcMethod = this.maxPointsCalcMethod();

    let commentFieldData = this.commentField.getData();
    data.mediaVariantsCommentLengthRange = commentFieldData.range;
    data.mediaVariantsCommentPlaceholder = commentFieldData.placeholder;
    data.mediaVariantsCommentRequired = commentFieldData.required;

    data.skip = this.skip();
    data.skipText = this.skipText();

    return data;
  }

  isValid() {
    if (!super.isValid()) return false;

    if (this.mediaType() == MEDIA_VARIANT_TYPE_VIDEO) {
      return this.videosVariantsController.isValid();
    } else if (this.mediaType() == MEDIA_VARIANT_TYPE_IMAGE) {
      return this.imagesVariantsController.isValid();
    } else {
      throw new Error(
        "Media Variants Question error: unknown mediaType",
        this.mediaType()
      );
    }
  }
}

export default MediaVariantsQuestion;
