<template id="question-form-template-media-variants">
    <!-- ko let: { $translator: question.translator } -->
    <div class="hat-radio-group hat-radio-group--dense survey-question__media-type-selector">
        <!-- ko foreach: { data: question.mediaTypes, as: 'mediaType' } -->
        <div class="hat-radio-group__radio" data-bind="let: { inputId: 'survey-question-media-type-selector-' + mediaType.id }">
            <input class="hat-radio-group__radio-input" type="radio" name="media-type" data-bind="value: mediaType.id,
                checked: question.mediaType,
                disable: question.isBlocked() || question.isFullBlocked,
                attr: { id: inputId }" />
            <label class="hat-radio-group__radio-label" data-bind="attr: { for: inputId },
                click: function() {
                    if (question.isFullBlocked) return false;
                    if (question.isBlocked()) question.tryChangeBlockedParam();
                    else return true;
                }">
                <i class="survey-question__media-type-selector-value-icon" data-bind="class: 'survey-question__media-type-selector-value-' + mediaType.icon + '-icon'"></i>
                <span data-bind="text: mediaType.label"></span>
            </label>
        </div>
        <!-- /ko -->
    </div>
    <!-- ko if: question.mediaType() == 'image' -->
    <question-form-media-variants-list params="controller: question.imagesVariantsController, disabled: question.isFullBlocked">
    </question-form-media-variants-list>
    <!-- /ko -->

    <!-- ko if: question.mediaType() == 'video' -->
    <question-form-media-variants-list params="controller: question.videosVariantsController, disabled: question.isFullBlocked">
    </question-form-media-variants-list>
    <!-- /ko -->

    <div>
        <div class="form-group">
            <fc-switch params="checked: question.skip, label: $translator.t('Пропуск ответа'), disabled: question.isFullBlocked"></fc-switch>
        </div>

        <!-- ko template: {
       foreach: templateIf(question.skip(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
        <div class="form-group">
            <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
            <fc-input
                params="
                    value: question.skipText,
                    counter: true,
                    maxlength: 125,
                    placeholder: $translator.t('Затрудняюсь ответить'),
                    disabled: question.isFullBlocked,
                "
            ></fc-input>
        </div>

        <!-- /ko -->
    </div>

    <hr class="mx-0">

    <!-- Комментарий -->
    <div class="mt-4">
        <switch params="checked: question.commentEnabled, disabled: question.isFullBlocked"><span data-bind="text: $translator.t('Комментарий')"></span></switch>
    </div>


    <!-- ko template: {
        foreach: templateIf(question.commentEnabled(), $data),
        afterAdd: slideAfterAddFactory(200),
        beforeRemove: slideBeforeRemoveFactory(200)
    } -->
    <div>
        <div class="row pb-4">
        <div class="col">
            <div class="f-check">
            <input
                type="checkbox"
                id="comment-required"
                class="f-check-input"
                data-bind="
                checked: question.commentIsRequired,
                disable: question.isFullBlocked,
                event: { change: function() { 
                    question.updateCommentRequired() } }
                "
            />
            <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
            </div>
        </div>
        </div>
        <div class="form-group">
            <fc-label params="text: 'Наименование поля', text: 'Наименование поля'"></fc-label>
            <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
        </div>

        <!-- ko component: {
            name: 'text-field',
            params: {
                controller: question.commentField,
                intervalText: $translator.t('Кол-во символов в комментарии'),
                disabled: question.isFullBlocked
            }
        } -->
        <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /Комментарий -->
    <!-- /ko -->
</template>