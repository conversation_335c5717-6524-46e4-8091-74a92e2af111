import { sortByArray } from "../../../../utils/array/sort-by-array";
import { VariantItemModel } from "../variants-list/variant-item";
import { VariantsListModel } from "../variants-list";

const { observable, computed, observableArray, toJS } = ko;

export function Donor(donors, options = {}) {
  const isChanged = observable(false).extend({ notify: "always" });
  const updating = observable(false);

  const donorsList = computed(() => {
    if (!donors) return [];

    return donors().map((q) => {
      const { index, question } = q;
      return {
        type: question.type,
        id: question.id,
        text: `${index}. ${
          question.descriptionText || question.alias || question.name
        }`,
      };
    });
  });
  const useDonor = observable(false);
  const donorId = observable(null);
  const donorVariantsType = observable(1);

  [useDonor, donorId].forEach((field) => {
    field.subscribe(() => {
      if (updating()) return;
      isChanged(true);
    });
  });

  function getDonor(donorId) {
    const donor = donors().find((q) => {
      const { question } = q;
      return question.id === donorId;
    });
    return donor ? donor.question : null;
  }

  function getFirstDonor(donor) {
    if (!donor.donorId) return donor;

    const parentDonor = getDonor(donor.donorId);
    if (!parentDonor) return null;

    return getFirstDonor(parentDonor);
  }

  function getDonorVariants(firstDonor, donor) {
    if (!firstDonor) return [];
    const variants = firstDonor.variants.map((v, i) => {
      let description = v.description || ''

      if (donor) {
        description = donor?.variants[i].description || v.description || ''
      }

      return {
        id: v.id,
        value: v.value.length ? v.value : `Вариант ${i+1}`,
        points: "",
        dictionary_element_id: v.dictionary_element_id,
        donor_variant_id: v.id,
        file: {
          file_id: v.file_id,
          fileUrl: v.file_url,
          previewUrl: v.preview_url
        },
        description: description
      };
    });
    if (firstDonor.variantsCustomAnswerEnabled) {
      variants.push({
        id: "custom",
        value: firstDonor.customAnswerLabel || "Свой вариант",
        points: "",
        description: firstDonor.selfVariantDescription
      });
    }

    if (firstDonor === donor) return variants;

    const order = donor.variants.map((v) => v.donorId || "custom");
    return sortByArray(variants, order);
  }

  useDonor.subscribe((v) => {
    if (v) {
      const defaultDonor = donorsList()[0];
      if (defaultDonor) {
        donorId(defaultDonor.id);
      }
    } else {
      donorId(null);
    }
  });

  return {
    isChanged,

    donorsList,
    useDonor,
    donorId,
    donorVariantsType,

    getDonorVariants(donorId, options) {
      if (!donorId) return [];
      const donor = getDonor(donorId);
      if (!donor) return [];

      const firstDonor = getFirstDonor(donor);
      const variants = getDonorVariants(firstDonor, donor);
      return variants.map(v => {
        if (options?.tooltip) {
          v.useTooltip = true
        }
        const variant = VariantItemModel(v);
        variant.isChanged.subscribe(() => isChanged(true))
        if (options?.hasChildren) {
          variant.children = new VariantsListModel();
        }
        return variant;
      });
    },

    getData() {
      return {
        useDonor: useDonor(),
        donorId: donorId(),
        donorVariantsType: donorVariantsType(),
      };
    },

    update(data) {
      if (data.donorId) {
        if (!useDonor() && donorId() !== data.donorId) {
          useDonor(true);
          donorId(data.donorId);
        }
        if (donorVariantsType() !== data.donorVariantsType)
          donorVariantsType(data.donorVariantsType);
      } else {
        if (useDonor()) {
          useDonor(false);
          donorId(null);
        }
      }
    },
  };
}
