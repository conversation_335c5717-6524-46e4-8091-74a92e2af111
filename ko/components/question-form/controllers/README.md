# Контроллеры

Включаемая в вопрос функциональность.

* `LinkController` - связь с полями клиента
* `MaskController` - маска для текстовых полей
* `MediaController` - загрузка изображений и видео
* `VariantsController` - блок текстовых вариантов
* `MediaVariantsController` - блок медиа вариантов

## Инициализация и обновление данных

Контроллеры инициализируются без значений по умолчанию.

Для обновления данных и установки дефолтных значений используется метод `updateData` (кроме `MediaController`).

## Отслеживание изменений

Чтобы отслеживать изменения в контроллере, нужно подписаться на его свойство `isChanged`:

```
const controller = new Controller();
controller.isChanged.subscribe(v => {
  if (v) {
    // в контроллере произошли изменения
  }
});
```

## Валидность

Для проверки валидности данных контроллер используется метод `isValid`.

## Получение данных

Для получения данных контроллера используется метод `getData` (кроме `MediaController`).
