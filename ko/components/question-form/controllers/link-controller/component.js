/**
 * Компонент блока привязки к клиентским полям
 */

import { clientFields } from './loader';
import { Translator } from '@/utils/translate';
const QuestionTranslator = Translator('question')
let nextId = 0;
ko.components.register('link-controller-block', {
  viewModel: function (params) {
    this.controller = params.controller;
    this.translator = QuestionTranslator;
    this.id = nextId++;
    this.fields = clientFields.fields;
    this.loaded = clientFields.loaded;
    this.disabled = params.disabled;
    this.optionTemplate = (state) => {
      return $('<span>').text(state.text);
    };
  },
  template: {
    element: 'link-controller-block-template',
  },
});
