/**
 * Контроллер блока привязки к полям клиента
 */

import './component';
import { clientFields } from './loader';

/**
 * @class
 * @property {Question} question
 * @property {Function} linkWithClientField Observable.<boolean>
 * @property {Function} linkedField Observable.<string>
 * @property {Function} rewriteExistLink Observable.<boolean>
 */
class LinkController {
  /**
   * @constructor
   * @param {Question} question
   */
  constructor(question) {
    this.question = question;

    this.linkWithClientField = ko.observable(false);
    this.linkedField = ko.observable('');
    if (clientFields.loaded()) {
      this.linkedField(clientFields.defaultField());
    } else {
      this.subscription = clientFields.loaded.subscribe(() => {
        if (!this.linkedField()) this.linkedField(clientFields.defaultField());
        this.subscription.dispose();
      });
    }

    this.rewriteExistLink = ko.observable(false);

    this.linkWithClientField.subscribe((value) => {
      if (!value) this.rewriteExistLink(false);
    });
  }

  /**
   * Обновление данных контроллера
   * @param {LinkControllerData} data
   */
  updateData(data) {
    if (data.linkedField) {
      this.linkedField(data.linkedField);
    } else {
      this.linkedField(clientFields.defaultField());
    }

    this.rewriteExistLink(data.rewriteExistLink);
    this.linkWithClientField(data.linkWithClientField);
  }

  /**
   * Возвращает данные контроллера
   * @returns {LinkControllerData}
   */
  getData() {
    return {
      linkWithClientField: this.linkWithClientField(),
      linkedField: this.linkedField(),
      rewriteExistLink: this.rewriteExistLink(),
    };
  }

  /**
   * Проверка на валидность контроллера
   * @returns {boolean}
   */
  isValid() {
    return true;
  }
}

export { LinkController };
