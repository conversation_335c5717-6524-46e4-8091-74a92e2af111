/**
 * Загрузчик клиентских полей
 */

const apiUrl = '/foquz/foquz-question/contact-fields';

const loaded = ko.observable(false);
const fields = {
  system: ko.observableArray([]),
  additional: ko.observableArray([]),
};
const defaultField = ko.observable('');

function loadFields() {
  $.ajax({
    url: apiUrl,
    type: 'GET',
  }).done((data) => {
    fields.system = data.system;
    fields.additional = data.additional;
    defaultField(data.system[0].id);
    loaded(true);
  });
}

const clientFields = {
  loaded,
  defaultField,
  get fields() {
    if (!loaded()) loadFields();
    return fields;
  },
};

export { clientFields };
