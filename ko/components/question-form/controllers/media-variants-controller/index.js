/**
 * Контроллер блока медиа-вариантов
 * Типы вопросов: Выбор изображения/видео
 */

import "./component";
import "Legacy/modals/load-by-link";

import { VariantsController } from "../variants-controller";

import ImageVariantModel from "./models/image-variant";
import VideoVariantModel from "./models/video-variant";
import MixVariantModel from "./models/mix-variant";

import {
  MEDIA_VARIANT_TYPE_IMAGE,
  MEDIA_VARIANT_TYPE_MIX,
  MEDIA_VARIANT_TYPE_VIDEO,
} from "../../data/media-variants-types";

import {
  VARIANTS_TYPE_MULTIPLE,
  VARIANTS_TYPE_SINGLE,
} from "../../data/variant-types";
import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";

import { Translator } from "@/utils/translate";

const ValidationTranslator = Translator("validation");

/**
 * Контроллер списка вариантов
 * @class MediaVariantsController
 * @property { string } mediaType
 */
class MediaVariantsController extends VariantsController {
  /**
   * @constructor
   * @param { MediaVariantsControllerConfig } config
   * @param { Question } question
   */
  constructor(config = {}, question) {
    super(config, question);

    this.api = this.controller.api;
    this.variantsType(VARIANTS_TYPE_SINGLE);
    this.mediaType = config.mediaType;

    let loaderConfig = null;
    if (this.mediaType == MEDIA_VARIANT_TYPE_IMAGE) {
      loaderConfig = {
        presets: ["image"],
        errors: {
          format:
            ValidationTranslator.t("Можно загружать файлы форматов:")() +
            " jpg, jpeg, png, gif",
        },
      };
    }
    if (this.mediaType == MEDIA_VARIANT_TYPE_VIDEO) {
      loaderConfig = {
        presets: ["video"],
        errors: {
          format:
            ValidationTranslator.t("Можно загружать файлы форматов:")() +
            " .MP4, .AVI, .WMV, .MOV, .3GP, .FLV, .MPEG-1,2, .WebM",
        },
      };
    }

    this.loader = new FoquzMultipleLoader(loaderConfig);

    this.loader.on("select", ({ file }) => {
      let variant = this.createVariant();
      variant.addByFile(file);

      setTimeout(() => {
        this.variants.push(variant);
      });
    });
  }

  _createMediaVariantModel(variantType, variantData) {
    variantData = variantData || {
      id: "0",
      mediaId: "0",
      url: "",
      preview: "",
      description: "",
      points: "",
    };

    let variant;

    if (variantType == MEDIA_VARIANT_TYPE_IMAGE) {
      variant = new ImageVariantModel(variantData, this);
    } else if (variantType == MEDIA_VARIANT_TYPE_VIDEO) {
      variant = new VideoVariantModel(variantData, this);
    } else if (variantType == MEDIA_VARIANT_TYPE_MIX) {
      variant = new MixVariantModel(variantData, this);
    } else {
      throw new Error(
        "MediaVariantsController error: unknown mediaType",
        variantType
      );
    }

    variant.subscriptions = [
      variant.url.subscribe((v) => this.isChanged(true)),
      variant.description.subscribe((v) => this.isChanged(true)),
      variant.points.subscribe((v) => this.isChanged(true)),
    ];

    return variant;
  }

  /**
   * @override
   */
  _createVariantModel(variantData) {
    return this._createMediaVariantModel(this.config.mediaType, variantData);
  }

  createVariant(variantData) {
    return this._createMediaVariantModel(this.config.mediaType, variantData);
  }

  /**
   * @override
   */
  _shouldCountVariant(v) {
    return v.isSelected();
  }

  /**
   * @override
   */
  isVariantsValid() {
    let variants = this.variants();
    return variants.every((v) => {
      return v.url();
    });
  }

  /**
   * @override
   */
  isValid() {
    if (!this.variantsCount.isValid()) return false;
    return this.countedVariants().every((v) => v.url());
  }
}

export { MediaVariantsController };
