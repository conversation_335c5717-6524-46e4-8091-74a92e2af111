import {
  VariantsControllerViewModel,
  createVariantsControlerViewModelTemplate,
} from '../variants-controller';
import { MEDIA_VARIANT_TYPE_MIX } from '../../data/media-variants-types';

const variantTemplate = `
  <!-- ko if: !controller.isFullBlocked || isSelected() -->
  <!-- ko let: { fileInput: ko.observable(null) } -->
    <input type="file" data-bind="
      element: fileInput,
      attr: {
        accept: accept,
      },
      event: {
          change: function (_, event) {
              const file = event.target.files[0];
              addByFile(file);
              event.target.value = '';
          }
      }" hidden>

  <!-- ko ifnot: isSelected -->
    <div>
      <button class="btn btn-default btn-with-icon btn-upload-disk
          survey-question__media-form-control-action"
          data-bind="click: function () { $(fileInput()).trigger('click'); }, text: _t('C компьютера')">

      </button>

      <!-- ko if: mediaType() == 'video' -->
        <button class="ml-2 btn btn-default btn-with-icon btn-upload-link
          survey-question__media-form-control-actions-item" data-bind="
          click: function () { addByYoutubeLink(); }, text: _t('По ссылке на Youtube')" type="button">

        </button>
      <!-- /ko -->
    </div>



    <!-- ko template: {
      foreach: templateIf(!fileSize.isValid(), $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
      <div class="form-error" data-bind="text: fileSize.error()"></div>
    <!-- /ko -->


  <!-- /ko -->

  <!-- ko if: isSelected -->
    <div class="d-flex">
      <div class="media-preview mr-4"">
        <!-- ko if: isLoading -->
          <div class="media-preview__loader"></div>
        <!-- /ko -->

        <!-- ko if: isLoaded -->
          <img data-bind="
            attr: { src: $data.getPreview() },
            fancyboxGalleryItem: {
              gallery: $component.getGalleryConfig.bind($component),
              index: $index()
            }">

            <!-- ko ifnot: controller.isFullBlocked -->
            <button class="media-preview__remove" data-bind="click: function (_, e) {
              removeMedia($data);
            }" type="button" title="Удалить">
              <i class="fas fa-times"></i>
            </button>
            <!-- /ko -->
        <!-- /ko -->

        <!-- ko if: !isLoaded() && !isLoading() -->

          <div class="media-preview__load"
            data-bind="click: function () { $(fileInput()).trigger('click'); }">
            <!-- ko if: type == 'image' -->
              <i class="i-icon-photo"></i>
            <!-- /ko -->
            <!-- ko if: type == 'video' -->
              <i class="i-icon-video"></i>
            <!-- /ko -->
            <!-- ko if: type == 'mix' -->
              <i class="i-icon-photovideo"></i>
            <!-- /ko -->
          </div>

        <!-- /ko -->
      </div>

      <div class="flex-grow-1">
        <div style="height: 100%;" class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: $data.description().length, charsCounterMaxCount: controller.config.variantDescriptionLimit || 500">
          <textarea class="form-control" style="min-height: 100%; resize: none!important;"
                data-bind="
                textInput: $data.description,
                attr: { maxlength: controller.config.variantDescriptionLimit || 500 },
                autosizeTextarea,  minHeight: 94,
                disable: controller.isFullBlocked"></textarea>
          <div class="chars-counter__value"></div>
        </div>
      </div>
    </div>
  <!-- /ko -->

  <!-- ko if: $component.formControlErrorStateMatcher(variant.url) -->
    <div class="form-error d-block" data-bind="text: variant.url.error()"></div>
  <!-- /ko -->

  <!-- /ko -->
  <!-- /ko -->
`;

const wrapVariantsList = list => {
  return `<div data-bind="dnd: function(files) {
    controller.loader.loadFiles(files);
  }, dndDisabled: controller.isFullBlocked">
  ${list}
  <dnd-cover params="mode: controller.mediaType">

  </dnd-cover>
  </div>`
}

const errors = `

`


ko.components.register('media-variants-controller-block', {
  viewModel: {
    createViewModel: function(params, componentInfo) {
      const model = new VariantsControllerViewModel(params);

      model.className = 'media-variants-controller';

      if (model.controller.mediaType !== MEDIA_VARIANT_TYPE_MIX) {
        model.hideCountError = ko.pureComputed(() => {

          return !model.controller.countedVariants().length;
        });
      } else {
        model.hideCountError = ko.observable(true);
      }

      model.getGalleryConfig = () => {
        return model.controller.variants().map(v => {
          return {
            src: v.getUrl(),
            opts: {
              caption: v.description(),
            }
          }
        })
      }

      return model;
    }

  },
  template:createVariantsControlerViewModelTemplate(variantTemplate, null, errors),
});
