import { Translator } from '@/utils/translate';

const ValidationTranslator = Translator('validation');

const generateRandomId = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Модель одного варианта в списке вариантов
 * @class VariantModel
 */
export default class MediaVariantModel {
  /**
   * @constructor
   * @param { MediaVariantModel } data
   * @param { MediaVariantsController } controller
   */
  constructor(data, controller) {
    this.controller = controller;

    this.maxVideoFileSize = 10 * 1024 * 1024;
    this.maxImageFileSize = 5 * 1024 * 1024;

    this.mediaType = ko.observable(null);

    this.fileSize = ko.observable(null).extend({
      validation: {
        validator: (v) => {
          if (this.mediaType() == 'image') {
            return v <= this.maxImageFileSize;
          }
          if (this.mediaType() == 'video') {
            return v <= this.maxVideoFileSize;
          }
          return true;
        },
        message: () => {
          if (this.mediaType() == 'image') {
            return ValidationTranslator.t(`Размер файла не должен превышать {size} Мб`, {
              size: 5
            })();
          }
          if (this.mediaType() == 'video') {
            return ValidationTranslator.t(`Размер файла не должен превышать {size} Мб`, {
              size: 10
            })();
          }
        },
      },
    });

    this.serverErrors = {
      file: ko.observable(''),
    };

    this.id = data.id;
    this.mediaId = ko.observable(data.mediaId);
    this.persistentId = ko.computed(() => data.id !== '0' ? data.id : (this.mediaId() || generateRandomId()));
    this.url = ko.observable(data.url).extend({
      validation: [
        {
          validator: () => false,
          onlyIf: () => !!this.serverErrors.file(),
          message: () => this.serverErrors.file(),
        },
      ],
    });
    this.preview = ko.observable('');

    this.description = ko.observable(data.description || '');

    this.isSelected = ko.observable(!!data.url);
    this.isLoaded = ko.observable(!!data.url);
    this.isLoading = ko.observable(false);

    this.error = this.url.error;

    this.sortable = ko.pureComputed(() => {
      return this.isSelected();
    });

    let points = '';
    if (data.points) points = parseInt(data.points) || '';

    this.points = ko.observable(points);
  }

  get accept() {
    return '';
  }

  /**
   * @returns { MediaVariantData }
   */
  getData() {
    return {
      id: this.id,
      persistentId: this.persistentId(),
      mediaId: this.mediaId(),
      url: this.url(),
      preview: this.preview(),
      description: this.description(),
      points: this.points(),
    };
  }

  getPreview() {
    return this.preview();
  }

  getUrl() {
    return this.url();
  }

  loadByFile(file, paramName = 'file') {
    this.isLoading(true);
    let fd = new FormData();
    fd.append(paramName, file);

    const that = this;

    return new Promise((res) => {
      this.getLoadByFileApiUrl().then((url) => {
        $.ajax({
          url: url,
          cache: false,
          contentType: false,
          processData: false,
          data: fd,
          type: 'post',
          success: function (data) {
            if (data.success === true) {
              that.isLoading(false);
              res(data.data);
            }
          },
        });
      });
    });
  }

  removeMedia(media) {
    this.isLoaded(false);
    this.url('');
    this.preview('');
  }
}
