import MediaVariantModel from './media-variant';
import { Translator } from '@/utils/translate';

const MainTranslator = Translator('main');
export default class VideoVariantModel extends MediaVariantModel {
  constructor(data, controller) {
    super(data, controller);
    this.preview(data.preview);
    this.url.extend({
      required: {
        message: () => MainTranslator.t('Выберите видео'),
        onlyIf: () => {
          if (this.isSelected()) return true;
          return this.controller.variants().length < 2;
        },
      },
    });
    this.type = 'video';
    this.mediaType('video');
  }

  get accept() {
    return '.MP4, .AVI, .WMV, .MOV, .3GP, .FLV, .MPEG-1,2, .WebM';
  }

  getUrl() {
    if (this.url().indexOf('/') === -1) {
      return 'https://www.youtube.com/watch?v=' + this.url();
    } else {
      return this.url();
    }
  }

  getLoadByFileApiUrl() {
    return this.controller.api.loadVideoByFileUrl(
      this.controller.question.id(),
    );
  }

  getLoadByYoutubeLinkUrl() {
    return this.controller.api.loadYoutubeVideoUrl(
      this.controller.question.id(),
    );
  }

  addByYoutubeLink() {
    return new Promise((res) => {
      this.fileSize(null);
      const that = this;
      window.modalOpens.push({
        dialogTemplateName: 'modal-dialog-template',
        data: {
          componentName: 'load-by-link-dialog',
          title: MainTranslator.t('Ссылка на Youtube')(),
          youtubeSectionTitle: MainTranslator.t('Как вставить ссылку?')()
        },
        close: (result) => {
          if (result && result.link) {
            this.isSelected(true);
            this.isLoading(true);
            this.getLoadByYoutubeLinkUrl().then((url) => {
              $.post(
                url,
                {
                  YouTubeVideoForm: {
                    link: result.link,
                  },
                },
                (response) => {
                  let videoData = response.data.model;
                  this.mediaId(videoData.id);
                  this.url(videoData.link);
                  this.preview(videoData.image);
                  this.isLoading(false);
                  this.isLoaded(true);
                  res();
                },
              );
            });
          }
        },
      });
    });
  }

  addByFile(file) {
    return new Promise((res) => {
      this.fileSize(file.size);

      if (!this.fileSize.isValid()) return;

      this.isSelected(true);

      this.loadByFile(file, 'VideoUploadForm[file]').then((data) => {
        this.mediaId(data.model.id);
        this.url(data.model.link);
        this.preview(data.model.image);
        this.isLoaded(true);
      });
    });
  }
}
