import MediaVariantModel from './media-variant';

import { Translator } from '@/utils/translate';
const MainTranslator = Translator('main');

export default class ImageVariantModel extends MediaVariantModel {
  constructor(data, controller) {
    super(data, controller);
    this.preview(this.url());
    this.type = 'image';
    this.mediaType('image');
    this.url.extend({
      required: {
        message: () => MainTranslator.t('Выберите изображение')(),
        onlyIf: () => {
          if (this.isSelected()) return true;
          return this.controller.variants().length < 2;
        }
      }
    });
  }

  get accept() {
    return '.gif, .jpg, .jpeg, .png';
  }

  getLoadByFileApiUrl() {
    return this.controller.api.loadImageByFileUrl(
      this.controller.question.id()
    );
  }

  addByFile(file) {
    return new Promise((res) => {
      this.isSelected(true);
      this.loadByFile(file, 'UploadForm[file]').then((data) => {
        this.mediaId(data.id);
        this.url('/' + data.image);
        this.preview('/' + data.image);
        this.isLoaded(true);

        res();
      });
    });
  }
}
