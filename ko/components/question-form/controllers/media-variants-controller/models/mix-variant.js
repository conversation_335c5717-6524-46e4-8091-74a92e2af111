import MediaVariantModel from './media-variant';
import { Translator } from '@/utils/translate';

const MainTranslator = Translator('main');
export default class MixVariantModel extends MediaVariantModel {
  constructor(data, controller) {

    super(data, controller);
    this.preview(data.preview);
    this.url.extend({
      required: {
        message: () => MainTranslator.t('Выберите файл')(),
        onlyIf: () => {
          if (this.isSelected()) return true;
          return this.controller.variants().length < 2;
        },
      },
    });
    this.type = 'mix';
  }

  get accept() {
    return '.gif, .jpg, .jpeg, .png, .MP4, .AVI, .WMV, .MOV, .3GP, .FLV, .MPEG-1,2, .WebM';
  }

  getUrl() {
    if (this.mediaType() == 'video') {
      if (!this.url().includes('live') && this.url().indexOf('/') === -1) {
        return 'https://www.youtube.com/watch?v=' + this.url();
      } else {
        return this.url();
      }
    } else {
      return this.url();
    }
  }

  getLoadVideoByFileApiUrl() {
    return this.controller.api.loadVideoByFileUrl(
      this.controller.question.id(),
    );
  }
  getLoadImageByFileApiUrl() {
    return this.controller.api.loadImageByFileUrl(
      this.controller.question.id(),
    );
  }

  getLoadByFileApiUrl() {
    if (this.mediaType() == 'video') {
      return this.getLoadVideoByFileApiUrl();
    } else {
      return this.getLoadImageByFileApiUrl();
    }
  }

  getLoadByLinkUrl() {
    return this.controller.api.loadMediaByUrl(this.controller.question.id());
  }

  addByLink() {
    return new Promise((res) => {
      this.fileSize(null);

      window.modalOpens.push({
        dialogTemplateName: 'modal-dialog-template',
        data: {
          componentName: 'load-by-link-dialog',
          title: MainTranslator.t('Ссылка')(),
          note: MainTranslator.t('Ролик по ссылке можно добавить только с сервиса YouTube')(),
          youtubeSectionTitle: MainTranslator.t('Как вставить ссылку с Youtube?')()
        },
        close: (result) => {
          if (result && result.link) {
            this.isSelected(true);
            this.isLoading(true);
            this.getLoadByLinkUrl().then((url) => {
              $.post(
                url,
                {
                  link: result.link,
                },
                (response) => {
                  let data = response.media;
                  this.mediaType(data.type);
                  this.mediaId(data.id);
                  this.url(data.link);
                  this.preview(data.image);

                  this.isLoading(false);
                  this.isLoaded(true);
                  res();
                },
              );
            });
          }
        },
      });
    });
  }

  addByFile(file) {
    return new Promise((res, rej) => {
      if (file.type.includes('image')) {
        this.mediaType('image');
      } else {
        this.mediaType('video');
      }

      this.fileSize(file.size);

      console.log(this.fileSize.isValid())

      if (!this.fileSize.isValid()) {
        rej(this.fileSize.error());
        return;
      }

      this.isSelected(true);

      let paramName =
        this.mediaType() == 'video'
          ? 'VideoUploadForm[file]'
          : 'UploadForm[file]';

      this.loadByFile(file, paramName).then((data) => {
        if (this.mediaType() == 'video') {
          this.mediaId(data.model.id);
          this.url(data.model.link);
          this.preview(data.model.image);
        } else {
          this.mediaId(data.id);
          this.url('/' + data.image);
          this.preview('/' + data.image);
        }

        this.isLoaded(true);
        res();
      });
    });
  }
}
