let id = 1;

import { get as _get } from "lodash";
import { Translator } from "@/utils/translate";
const ValidationTranslator = Translator("validation");

class MatrixElementModel {
  constructor(label = "", ctx, type) {
    this.id = "" + id++;
    this.rawLabel = label === "" ? `__foquz__empty${this.id}` : label;
    this.label = ko.observable(label).extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
      },
    });
    this.showPrintableLabel = ko.observable(
      label.includes("__foquz_dictionary_element")
    );
    this.printableLabel = ko.observable(
      label.replace("__foquz_dictionary_element", "")
    );
    this.doubled = ko.observable(false);

    this.isNew = ko.observable(true);

    this.isValid = ko.computed(() => {
      return this.label.isValid() && !this.doubled();
    });

    this.error = ko.pureComputed(() => {
      if (!this.label.isValid()) return this.label.error();
      if (this.doubled())
        return ValidationTranslator.t("Название повторяется")();
    });

    this.errorMatcher = ctx.question.formControlErrorStateMatcher(this);

    this.label.subscribe((_) => {
      this.doubled(false);
      ctx.onChange(2);
    });

    this.needExtra = ko.observable(true);

    this.needExtra.subscribe((v) => ctx.onChange());
    this.placeholder = ko.observable(ctx.kano() ? "Характеристика" : "Ряд");
  }
}

export class MatrixController {
  constructor(config = {}, question) {
    this.question = question;
    this.kano = ko.observable(false);

    this.rows = ko.observableArray([
      new MatrixElementModel("", this, "row"),
      new MatrixElementModel("", this, "row"),
      new MatrixElementModel("", this, "row"),
    ]);
    this.columns = ko.observableArray([
      new MatrixElementModel("1", this, "column"),
      new MatrixElementModel("2", this, "column"),
      new MatrixElementModel("3", this, "column"),
      new MatrixElementModel("4", this, "column"),
      new MatrixElementModel("5", this, "column"),
    ]);

    this.points = [];
    this.minRowsReq = ko.observable(this.rows().length);

    this.rows().forEach(() => {
      let pointsRow = [];
      this.columns().forEach(() => {
        let point = ko.observable("");
        point.subscribe((_) => this.onChange());
        pointsRow.push(point);
      });
      this.points.push(pointsRow);
    });

    this.rows.subscribe((f) => {
      this.onChange()
    });

    this.columns.subscribe((f) => {
      this.onChange()
    });

    this.isChanged = ko.observable(false).extend({ notify: "always" });
    this.multipleChoice = question.multipleChoice;
  }

  setRows(labels) {
    while (this.rows().length > labels.length) {
      const lastRow = this.rows()[this.rows().length - 1];
      this.removeRow(lastRow);
    }

    const diff = labels.length - this.rows().length;
    if (diff > 0) {
      Array(diff)
        .fill(null)
        .forEach(() => {
          this.addRow();
        });
    }

    this.rows().forEach((row, i) => {
      row.label(labels[i]);
    });
  }

  resetRows() {
    while (this.rows().length > 0) {
      this.removeRow(this.rows()[0]);
    }

    this.addRow();
    this.addRow();
    this.addRow();
  }

  checkRows() {
    let labels = {};
    this.rows().forEach((row) => {
      let label = row.label();
      if (!label) return;
      if (!labels[label]) labels[label] = [row];
      else labels[label].push(row);
    });
    Object.values(labels).forEach((rows) => {
      let doubled = rows.length > 1;
      rows.forEach((row) => {
        row.doubled(doubled);
      });
    });
  }

  checkColumns() {
    let labels = {};
    this.columns().forEach((column) => {
      let label = column.label();
      if (!label) return;
      if (!labels[label]) labels[label] = [column];
      else labels[label].push(column);
    });
    Object.values(labels).forEach((columns) => {
      let doubled = columns.length > 1;
      columns.forEach((column) => {
        column.doubled(doubled);
      });
    });
  }

  onChange() {
    this.isChanged(true);
  }

  removeRow(row) {
    let index = this.rows().findIndex((r) => r == row);
    this.points.splice(index, 1);
    this.rows.remove(row);
  }

  removeColumn(col) {
    let index = this.columns().findIndex((c) => c == col);
    this.points.forEach((row) => {
      row.splice(index, 1);
    });
    this.columns.remove(col);
  }

  addRow(label = "") {
    let item = new MatrixElementModel(label, this, "row");
    let row = Array(this.columns().length)
      .fill()
      .map((_) => ko.observable(""));
    this.points.push(row);
    this.rows.push(item);
    return item;
  }

  addColumn() {
    let item = new MatrixElementModel("", this, "column");
    this.points.forEach((row) => row.push(ko.observable("")));
    this.columns.push(item);

    return item;
  }

  updateList(list, data, type) {
    let currentCount = list().length;
    let updatedCount = data.length;

    if (currentCount > updatedCount) {
      list(list.slice(0, updatedCount));
      currentCount = list().length;
    }

    list().forEach((item, index) => {
      item.label(data[index]);
      item.isNew(false);
    });

    if (currentCount < updatedCount) {
      for (let i = currentCount; i < updatedCount; i++) {
        let item = new MatrixElementModel(data[i], this, type);
        item.isNew(false);
        list.push(item);
      }
    }
  }

  updateData(data) {
    if (!data) return;
    if (data.minRowsReq) {
      this.minRowsReq(data.minRowsReq);
    } else if (data.rows && data.rows.length) {
      this.minRowsReq(data.rows.length);
    }
    if (data.rows && data.rows.length) {
      this.updateList(this.rows, data.rows, "row");
      this.checkRows();
    }
    if (data.cols && data.cols.length) {
      this.updateList(this.columns, data.cols, "column");
      this.checkColumns();
    }

    if (data.points && data.points.length) {
      this.updatePoints(data.points);
    }

    this.multipleChoice(+data.multiple_choice);
    this.updateNeedExtra(data.extra_question);
  }

  updatePoints(data) {
    let rowsCount = this.rows().length;
    let colsCount = this.columns().length;

    let currentRowsCount = this.points.length;
    let currentColsCount = this.points[0].length;

    // Убрать лишние ряды
    if (currentRowsCount > rowsCount) {
      this.points.length = rowsCount;
      currentRowsCount = rowsCount;
    }

    // Убрать лишние колонки
    if (currentColsCount > colsCount) {
      this.points.forEach((row) => {
        row.length = colsCount;
      });
      currentColsCount = colsCount;
    }

    // Добавить колонки в существующие ряды
    if (currentColsCount < colsCount) {
      this.points.forEach((row) => {
        for (let i = currentColsCount; i < colsCount; i++) {
          let point = ko.observable("");
          point.subscribe((_) => this.onChange());
          row.push(point);
        }
      });
    }

    // Добавить ряды
    if (currentRowsCount < rowsCount) {
      for (let i = currentRowsCount; i < rowsCount; i++) {
        let row = [];
        for (let i = 0; i < colsCount; i++) {
          let point = ko.observable("");
          point.subscribe((_) => this.onChange());
          row.push(point);
        }
        this.points.push(row);
      }
    }

    // Обновить данные
    this.points.forEach((row, y) => {
      let rowData = data[y];
      row.forEach((col, x) => {
        let colData = rowData ? parseInt(rowData[x]) : "";
        col(colData || "");
      });
    });
  }

  updateNeedExtra(extra_question) {
    if (!extra_question) {
      this.rows().forEach((row) => {
        row.needExtra(false);
      });
      this.columns().forEach((col) => {
        col.needExtra(false);
      });
      return;
    }
    this.rows().forEach((row) => {
      if (!extra_question.rows) {
        row.needExtra(false);
        return;
      }
      row.needExtra(extra_question.rows.includes(row.label()));
    });
    this.columns().forEach((col) => {
      if (!extra_question.cols) {
        col.needExtra(false);
        return;
      }
      col.needExtra(extra_question.cols.includes(col.label()));
    });
  }

  getData() {
    const data = {
      rows: this.rows().map((r) => (r.label() === "" ? r.rawLabel : r.label())),
      cols: this.columns().map((c) => c.label()),
      points: ko.toJS(this.points).map((row) => {
        return row.map((c) => c || 0);
      }),
      minRowsReq: this.minRowsReq(),
      multiple_choice: this.multipleChoice(),
    };

    data.extra_question = {
      rows: [],
      cols: [],
    };
    if (this.question.clarifyingQuestionEnabled()) {
      data.extra_question.rows = this.rows().reduce((acc, el) => {
        if (el.needExtra()) {
          return [...acc, el.label() === "" ? el.rawLabel : el.label()];
        }
        return acc;
      }, []);
      data.extra_question.cols = this.columns().reduce((acc, el) => {
        if (el.needExtra()) {
          return [...acc, el.label()];
        }
        return acc;
      }, []);
    }
    return data;
  }

  setKano() {
    this.kano(true);
    this.question.description(
      "Как бы вы себя чувствовали, если бы у нас отсутствовала эта возможность?"
    );
    this.updateData({
      cols: [
        "Мне бы понравилось",
        "Она обязательно должна быть",
        "Мне все равно",
        "Мне бы не понравилось, но я могу смириться с этим",
        "Мне бы не понравилось",
      ],
      multiple_choice: this.multipleChoice(),
    });
    this.rows().forEach((row) => row.placeholder("Характеристика"));
  }

  isValid() {
    let hasInvalidRow = this.rows().some((r) => !r.isValid());
    if (hasInvalidRow) return false;
    let hasInvalidColumn = this.columns().some((r) => !r.isValid());
    if (hasInvalidColumn) return false;

    return true;
  }
}
