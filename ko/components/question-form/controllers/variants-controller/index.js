/**
 * Контроллер блока текстовых вариантов
 * Типы вопросов: Оценка (Уточняющий вопрос, Варианты на выбор), Варианты ответов, Приоритет
 */


import { Translator } from '@/utils/translate';
import { bindAll } from 'lodash';


const QuestionTranslator = Translator('question');
const ValidationTranslator = Translator('validation');
import {
  VARIANTS_TYPE_MULTIPLE,
  VARIANTS_TYPE_SINGLE,
  VARIANTS_TYPE_TEXT
} from '../../data/variant-types';

import TextField from '../../models/text-field';


const generateRandomId = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Модель одного варианта в списке вариантов
 * @class VariantModel
 */
class VariantModel {
  /**
   * @constructor
   * @param { VariantModel } data
   */
  constructor(data, fileEnabled) {
    this.id = data.id;
    this.persistentId = data.id !== '0' ? data.id : generateRandomId()
    this.file = ko.observable(data.file);
    this.fileEnabled = fileEnabled
    this.simple = data.file === undefined
    
    if (data.file_id) {
      this.file({id: data.file_id, fileUrl: data.file_url, previewUrl: data.preview_url})
    }
    this.points = ko.observable(data.points);
    this.value = ko.observable(data.value || '').extend({
      required: {
        message: () => ValidationTranslator.t('Обязательное поле')(),
        onlyIf: () => {
          if (this.fileEnabled && this.fileEnabled()) {
            return false
          } else {
            return true
          }
          
        },
      }
    });
    this.extra_question = data.extra_question;
  }

  /**
   * @returns { VariantData }
   */
  getData() {
    const fileId = this.file() ? this.file().id : null
    return {
      id: this.id,
      persistentId: this.persistentId,
      value: this.value().trim(),
      points: this.points(),
      extra_question: this.extra_question,
      file: this.file(),
      file_id: fileId
    };
  }
}

/**
 * Контроллер списка вариантов
 * @class VariantsController
 * @property { VariantsControllerConfig } config
 * @property { Question } question
 * @property { QuestionFormController } controller
 * @property { number } variantsType Тип вариантов (один/несколько)
 * @property { Function } variants ObservableArray.<VariantModel[]>
 * @property { number } minCount
 * @property { Function } variantsCount Observable.<number> - текущее кол-во вариантов
 * @property { Function } customAnswerEnabled Observable.<boolean> - опция Свой вариант включена
 * @property { TextField } customAnswerField Контроллер поля Свой вариант
 * @property { Function } isChanged Observable.<boolean>
 */
class VariantsController {
  /**
   *
   * @param { VariantsControllerConfig } config
   * @param { Question } question
   */
  constructor(config = {}, question) {
    this.config = config;
    this.question = question;
    this.controller = this.question.controller;
    this.fileEnabled = config.fileEnabled;
    this.isClarifyingQuestion = config.isClarifyingQuestion


    this.file = ko.observable(null)

    this.isFullBlocked = this.question.isFullBlocked;

    let defaultType =
      config.variantsType == 'single'
        ? VARIANTS_TYPE_SINGLE
        : VARIANTS_TYPE_MULTIPLE;

    this.variantsType = ko.observable(defaultType);

    this.variants = ko.observableArray([]);
    this.countedVariants = ko.pureComputed(() => {
      return this.variants().filter((v) => this._shouldCountVariant(v));
    });

    this.variantsType.subscribe((v) => {
      if (this.config.addFirstVariant && !this.variants().length) {
        this.addVariant();
      }
    });

    if (this.config.addFirstVariant) {
      this.addVariant();
    }

    this.minCount = config.minVariantsCount || 0;

    this.variantsCount = ko.observable().extend({
      validation: {
        validator: () => {
          return this.countedVariants().length >= this.minCount;
        },
        message: () => {
          if (typeof this.config.minVariantsCountErrorMessage === 'function') {
            return this.config.minVariantsCountErrorMessage(this.minCount);
          }

          let options = window.utils.declOfNum(this.minCount, [
            'вариант',
            'варианта',
            'вариантов'
          ]);

          return ValidationTranslator.t('Нужно добавить хотя бы {options}', {
            options: ValidationTranslator.t(`{count} ${options}`, {
              count: this.minCount
            })()
          })();
        }
      }
    });

    this.customAnswerEnabled = ko.observable(false);
    this.customAnswerField = new TextField({
      withLabel: true,
      defaultLabel: QuestionTranslator.t('Свой вариант'),
      file: this.file
    });

    this.textAnswerField = new TextField({});

    this.answersCountLimitMin = ko.pureComputed(() => {
      return this.question.required() ? 1 : 0;
    });
    this.answersCountLimitMax = ko.pureComputed(() => {
      return this.variants().length;
    });

    this.answersCountLimit = ko.observable('');

    this.answersCountLimitMin.subscribe((v) => {
      if (this.answersCountLimit() !== '' && this.answersCountLimit() < v) {
        this.answersCountLimit(v);
      }
    });

    this.answersCountLimitMax.subscribe((v) => {
      if (this.answersCountLimit() !== '' && this.answersCountLimit() > v) {
        this.answersCountLimit(v);
      }
    });

    this.dropdown = ko.observable(false);

    this.randomOrder = ko.observable(false);

    // Отслеживание изменений
    this.isChanged = ko.observable(false).extend({
      notify: 'always'
    });

    [
      this.variantsType,
      this.customAnswerEnabled,

      this.variants,
      this.answersCountLimit,
      this.dropdown,

      this.randomOrder
    ].forEach((f) => f.subscribe((v) => this.isChanged(true)));
    this.customAnswerField.isChanged.subscribe((v) => this.isChanged(true));
    this.textAnswerField.isChanged.subscribe((v) => this.isChanged(true));

    bindAll(this, [
      'getVariants',
      '_createVariantModel',
      'addVariant',
      'createVariant',
      'removeVariant',
      'updateData',
    ]);
  }

      
  /**
   * Валидность списка вариантов
   */
  isVariantsValid() {
    if (this.variantsType() == VARIANTS_TYPE_TEXT) return true;
    if (!this.isClarifyingQuestion) {
      if (this.fileEnabled && this.fileEnabled()) {
        if (!this.customAnswerEnabled()) {
          return !this.variants().some((v) => !v.value.isValid() || !v.file());
        } else {
          let fieldData = this.customAnswerField.getData();
          return !this.variants().some((v) => !v.value.isValid() || !v.file()) && fieldData.label.length && fieldData.file()
        }
        
      } else {
        return !this.variants().some((v) => !v.value.isValid())
      }
    } else {
      if (this.fileEnabled && this.fileEnabled()) {
        if (!this.customAnswerEnabled()) {
          return !this.variants().some((v) => !v.file());
        } else {
          let fieldData = this.customAnswerField.getData();
          return !this.variants().some((v) => !v.file()) && fieldData.file()
        }
      } else {
        return !this.variants().some((v) => !v.value.isValid())
      }
    }
  };

  getVariants(ids) {
    return this.variants()
      .filter((variant) => {
        return ids.find((id) => variant.id == id);
      })
      .map((variant) => variant.value);
  }

  _shouldCountVariant(v) {
    return true;
  }

  /**
   * Создает модель варианта
   * И подписывается на его изменения
   */
  _createVariantModel(variantData) {

    variantData = variantData || {
      id: '0',
      value: '',
      points: ''
    };
    let variant = new VariantModel(variantData, this.fileEnabled);
    if (this.isClarifyingQuestion) {
      variant.extra_question = 1
    }
    variant.subscriptions = [
      variant.value.subscribe((v) => this.isChanged(true)),
      variant.points.subscribe((v) => this.isChanged(true)),
    ];
    if (this.fileEnabled) {
      variant.subscriptions.push(variant.file.subscribe((v) => this.isChanged(true)))
    }
    return variant;
  }

  /**
   * Добавляет новый пустой вариант в список
   */
  addVariant(index) {
    let variant = this._createVariantModel();

    if (index == undefined) {
      this.variants.push(variant);
    } else {
      this.variants.splice(index, 0, variant);
    }

    return variant;
  }

  createVariant() {
    return this._createVariantModel();
  }

  /**
   * Удаляет вариант из списка
   * И отписывается от его изменений
   * @param { VariantModel } variant
   */
  removeVariant(variant) {
    if (variant.subscriptions) {
      variant.subscriptions.forEach((s) => s.dispose());
    }
    this.variants.remove(variant);
  }

  /**
   * Обновляет данные контроллера
   */
  updateData(data) {
    if (this.config.withVariantsTypeSelect) {
      this.variantsType(data.type);
    }

    if (data.selfVariantFile) {
      this.file({
        id: data.selfVariantFile.file_id,
        fileUrl: data.selfVariantFile.file_url,
        previewUrl: data.selfVariantFile.preview_url
      })
    }

    if (data.variants) {
      this.variants(
        data.variants.map((v) => {
          return this._createVariantModel(v);
        })
      );
      if (this.variants().length === 0 && this.config.addFirstVariant)
        this.addVariant();
    } else {
      this.variants([]);
      if (this.config.addFirstVariant) {
        this.addVariant();
      }
    }

    if (this.config.withCustomAnswer) {
      this.customAnswerEnabled(data.customAnswerEnabled);
      this.customAnswerField.updateData({
        range: data.customAnswerRange,
        placeholder: data.customAnswerPlaceholder,
        label: data.customAnswerLabel
      });
    }

    if (this.config.withAnswersCountLimit) {
      this.answersCountLimit(data.answersCountLimit);
    }

    if (this.config.withDropdownOption) {
      this.dropdown(data.dropdown);
    }

    if (this.config.withRandomOption) {
      this.randomOrder(data.randomOrder);
    }

    if (this.config.withTextAnswer) {
      this.textAnswerField.updateData({
        range: data.customAnswerRange,
        placeholder: data.customAnswerPlaceholder
      });
    }
  }

  /**
   * Возвращает данные контроллера
   */
  getData() {
    
    let variantsType = parseInt(this.variantsType());

    if (variantsType == VARIANTS_TYPE_TEXT) {
      let textFieldData = this.textAnswerField.getData();
      

      return {
        variantsType,
        variants: [],
        customAnswerEnabled: false,
        customAnswerPlaceholder: textFieldData.placeholder,
        customAnswerRange: textFieldData.range
      };
    }

    let fieldData = this.customAnswerField.getData();

    return {
      variantsType,
      variants: this.variants().map((v) => v.getData()),
      customAnswerEnabled: this.customAnswerEnabled(),
      customAnswerRange: fieldData.range,
      customAnswerPlaceholder: fieldData.placeholder,
      customAnswerLabel: fieldData.label,
      answersCountLimit: this.answersCountLimit(),
      dropdown: this.dropdown(),
      randomOrder: this.randomOrder(),
      customAnswerFile: fieldData.file ? fieldData.file() : null
    };
  }

  /**
   * Валидность всего контроллера
   */
  isValid() {
    const clarifyingToEach = this.question.clarifyingToEach && this.question.clarifyingToEach();
    if (clarifyingToEach) return true;
    if (this.variantsType() == VARIANTS_TYPE_TEXT) return true;
    if (!this.variantsCount.isValid()) return false;
    return this.isVariantsValid();
  }
}
class VariantsControllerViewModel {
  constructor(params) {
    this.controller = params.controller;
    this.question = this.controller.question;

    this.sorting = ko.observable(false);

    this.isBlocked = this.question.isBlocked();
    this.isFullBlocked = this.question.isFullBlocked;

    this.formControlErrorStateMatcher =
      this.question.controller.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher =
      this.question.controller.formControlSuccessStateMatcher;

    this.hideCountError = ko.observable(false);

    this.className = '';
  }

  get variantsTypes() {
    let types = [
      {
        id: VARIANTS_TYPE_SINGLE,
        icon: 'single',
        label: QuestionTranslator.t('Выбор одного варианта')
      },
      {
        id: VARIANTS_TYPE_MULTIPLE,
        icon: 'multiple',
        label: QuestionTranslator.t('Выбор нескольких вариантов')
      }
    ];

    return types;
  }

  onSort(event) {
    let variants = this.controller.variants;
    this.sorting(true);
    const movedItem = variants()[event.data.oldIndex];
    variants.remove(movedItem);
    variants.splice(event.data.newIndex, 0, movedItem);
    this.sorting(false);
  }

  beforeRemove(element) {
    const duration = this.sorting() ? 0 : 200;
    return $(element)
      .delay(0)
      .fadeOut(duration, function () {
        return $(element).remove();
      });
  }

  afterAdd(element) {
    const duration = this.sorting() ? 0 : 200;
    return $(element).hide().delay(0).fadeIn(duration);
  }
}

const variantTemplate = `
<div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: variant.value().length, charsCounterMaxCount: 250">
  <input class="form-control" maxlength="250"
    data-bind="textInput: variant.value,
        css: {
            'is-invalid': $component.formControlErrorStateMatcher(variant.value),
            'is-valid': $component.formControlSuccessStateMatcher(variant.value)
        }, disable: controller.isFullBlocked">

        <div class="chars-counter__value"></div>
        </div>
  <!-- ko if: $component.formControlErrorStateMatcher(variant.value) -->
  <div class="form-error" data-bind="text: variant.value.error()"></div>
  <!-- /ko -->
`;

function createVariantsControlerViewModelTemplate(
  variantTemplate,
  wrapList,
  errors
) {
  const variantsTypeSelect = `
  <div class="hat-radio-group hat-radio-group--dense survey-question__variants-type-selector">
    <!-- ko foreach: $component.variantsTypes -->
    <div class="hat-radio-group__radio" data-bind="let: { inputId: 'survey-question-variants-type-selector-' + id}">
      <input class="hat-radio-group__radio-input" name="variants-type" type="radio" data-bind="value: id, checked: $component.controller.variantsType, attr: { id: inputId, }, disable: $component.isBlocked || $component.controller.isFullBlocked" />
      <label class="hat-radio-group__radio-label" data-bind="attr: { for: inputId },
      click: function() {
        if ($component.isBlocked) $component.question.tryChangeBlockedParam();
        else return true;
      }">
        <i class="survey-question__variants-type-selector-value-icon" data-bind="class: 'survey-question__variants-type-selector-value-' + icon + '-icon'"></i>
        <span data-bind="text: label"></span>
      </label>
    </div>
    <!-- /ko -->
  </div>`;

  const addVariantButton = `<button class="survey-question__variants-control-add-button variants-controller__add-button" data-bind="
    click: function() { controller.addVariant(); },
    attr: { disabled: !controller.isVariantsValid() }
    ">
    <span class="survey-question__variants-control-add-button-icon"></span>
    Добавить вариант
    </button>`;

  const variantBlockTemplate = `
  <div class="survey-question__variants-control-list-item variants-controller__variant">
    <!-- ko if: !controller.isFullBlocked && (!variant.sortable || variant.sortable())  -->
    <div class="survey-question__variants-control-list-item-drag sortable-handle hide-on-dragging variants-controller__sortable-handle">
      <i class="icon icon-drag-arrow"></i>
    </div>
    <!-- /ko -->
    <div class="form-group hide-on-dragging variants-controller__variant-content">
      ${variantTemplate}
    </div>

    <!-- ko template: {
        foreach: templateIf($component.controller.config.freeRemove || $component.controller.config.canRemoveSingleVariant || $component.controller.variants().length > 1, $data),
        afterAdd: $component.afterAdd.bind($component),
        beforeRemove: $component.beforeRemove.bind($component)
    } -->
      <!-- ko ifnot: $component.isFullBlocked -->
      <!-- ko if: $component.controller.config.freeRemove || !$component.isBlocked || variant.id == '0' -->
      <button type="submit" class="hide-on-dragging btn btn-danger survey-question__variants-control-list-item-remove-button variants-controller__variant-remove" title="Удалить" data-bind="
      click: function() { $component.controller.removeVariant(variant); }">
      </button>
      <!-- /ko -->
      <!-- /ko -->
    <!-- /ko -->
  </div>`;

  let variantsList = `<div class="survey-question__variants-control-list variants-controller__list survey-question__variants-control-list-content" data-bind="nativeScrollbar">
    <!-- ko if: controller.variants().length -->
      <div class="variants-controller__sortable-container" data-bind="
            sortable,
            sortableItem: '.variants-controller__variant',
            sortableHandle: '.sortable-handle',
            onSort: $component.onSort.bind($component)">
        <!-- ko foreach: {
          data: controller.variants,
          as: 'variant',
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: $component.beforeRemove.bind($component)
        } -->
          ${variantBlockTemplate}
        <!-- /ko -->
      </div>
    <!-- /ko -->

    <!-- ko if: controller.config.withAddVariantButton -->
    ${addVariantButton}
    <!-- /ko -->



    <!-- ko if: !$component.hideCountError() -->
    ${errors || ''}
    <!-- ko if: $component.formControlErrorStateMatcher(controller.variantsCount) -->
      <div class="form-error" data-bind="text: controller.variantsCount.error()"></div>
    <!-- /ko -->
    <!-- /ko -->
  </div>`;

  if (typeof wrapList == 'function') {
    variantsList = wrapList(variantsList);
  }
  return `
    <!-- ko if: controller.config.withVariantsTypeSelect -->
        ${variantsTypeSelect}
    <!-- /ko -->

    <div class="survey-question__variants-control variants-controller" data-bind="css: $component.className">
      ${variantsList}
    </div>

    <!-- ko if: controller.config.withCustomAnswer -->
    <div class="f-check my-4">
      <input type="checkbox" class="f-check-input" data-bind="checked: controller.customAnswerEnabled, disabled: $component.isBlocked" id="rate-clarifying-question-custom-answer">
      <label for="rate-clarifying-question-custom-answer" class="f-check-label" data-bind="click: function() {
        if ($component.isBlocked) {
          $component.question.tryChangeBlockedParam();
        } else return true;
      }, text: _t('question', 'Свой вариант (произвольное поле)')"></label>
    </div>

    <!-- ko template: {
          foreach: templateIf(controller.customAnswerEnabled(), $data),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
    <div class="mt-4">
      <!-- ko component: {
        name: 'text-field',
        params: {
          controller: controller.customAnswerField
        }
      } -->
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- /ko -->`;
}

ko.components.register('variants-controller-block', {
  viewModel: VariantsControllerViewModel,
  template: createVariantsControlerViewModelTemplate(variantTemplate)
});

export {
  VariantModel,
  VariantsController,
  VariantsControllerViewModel,
  createVariantsControlerViewModelTemplate
};
