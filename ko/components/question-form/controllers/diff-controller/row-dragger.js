// Начало перетаскивания
function onDragStart(event) {
  let row = event.currentTarget;
  event.dataTransfer.effectAllowed = 'move';

  // Копия ряда, которая будет около курсора
  const rowGhost = row.cloneNode(true);
  event.dataTransfer.setDragImage(rowGhost, 0, 0);

  // Ряд в таблице
  row.classList.add('diff-row--dragging');
}

// Прохождение курсора над заголовком ряда
function onDragOver(event) {
  // Разрешить вставку
  if (event.preventDefault) {
    event.preventDefault();
  }

  // Перемещаемый ряд
  const activeRow = document.querySelector('.diff-row--dragging');
  // Ряд, над которым проходит курсор
  const targetRow = event.currentTarget;

  if (activeRow == targetRow) {
    return;
  }

  // Определить место для вставки перетаскиваемого ряда (зависит от позиции курсора)
  const targetCoords = targetRow.getBoundingClientRect();
  const targetCenter = targetCoords.y + targetCoords.height / 2;
  const cursorPosition = event.clientY;

  if (cursorPosition >= targetCenter) {
    if (activeRow === targetRow.nextElementSibling) return;
    $(activeRow).insertAfter(targetRow);
  } else {
    if (activeRow === targetRow.previousElementSibling) return;
    $(activeRow).insertBefore(targetRow);
  }

  event.dataTransfer.dropEffect = 'move';
}

function onDrop(event) {
  if (event.preventDefault) {
    event.preventDefault();
  }
  if (event.stopPropagation) {
    event.stopPropagation();
  }
}

ko.bindingHandlers.diffRow = {
  init: (el, valueAccessor) => {
    let onSort = valueAccessor();

    el.addEventListener('dragstart', (event) => onDragStart(event), false);

    el.addEventListener(
      'dragover',
      (event) => {
        onDragOver(event);
        return false;
      },
      false,
    );

    el.addEventListener(
      'drop',
      (event) => {
        onDrop(event);
      },
      false,
    );

    el.addEventListener(
      'dragend',
      (event) => {
        onSort();

        $('.diff-row--dragging').removeClass('diff-row--dragging');

        $('.diff-ghost').remove();
      },
      false,
    );
  },
};
