import './row-dragger';
import { Translator } from '@/utils/translate';
class DiffControllerViewModel {
  constructor(params) {
    this.controller = params.controller;
    this.question = this.controller.question;
    this.translator = Translator('question');

    this.table = ko.observable(null);

    this.isBlocked = this.question.isBlocked();
    this.isFullBlocked = this.question.isFullBlocked;

    this.formControlErrorStateMatcher = this.question.controller.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher = this.question.controller.formControlSuccessStateMatcher;

    this.container = ko.observable(null);
    this.isSorting = ko.observable(false);
  }

  resortRows() {
    this.isSorting(true);

    let rowsOrder = [...this.table().querySelectorAll('[data-row]')].reduce(
      (acc, $row, index) => {
        let rowId = $row.dataset.row;
        return {
          ...acc,
          [rowId]: index,
        };
      },
      {},
    );

    let rows = this.controller.rows.sorted((a, b) => {
      return rowsOrder[a.tmpId] - rowsOrder[b.tmpId];
    });

    rows.forEach((row, index) => {
      this.controller.rows.splice(index, 1, row);
    });

    this.isSorting(false);
  }

  highlightRow(rowId) {
    // this.domHelper
    //   .getRowElements(rowId)
    //   .forEach((el) => el.classList.add('selected'));
  }

  unhighlightRow(rowId) {
    // this.domHelper
    //   .getRowElements(rowId)
    //   .forEach((el) => el.classList.remove('selected'));
  }

  // afterAdd(element) {
  //   let duration = this.isSorting() ? 0 : 400;
  //   $(element).hide().delay(0).fadeIn(duration);
  // }

  // beforeRemove(element) {
  //   let duration = this.isSorting() ? 0 : 400;
  //   $(element)
  //     .delay(0)
  //     .fadeOut(duration, () => element.remove());
  // }

  addRow() {
    this.controller.addRow();
    //this.domHelper.getRowHeader(row.id).querySelector('input').focus();
  }
}

ko.components.register('diff-controller-block', {
  viewModel: DiffControllerViewModel,
  template: {
    element: 'diff-controller-block-template',
  },
});
