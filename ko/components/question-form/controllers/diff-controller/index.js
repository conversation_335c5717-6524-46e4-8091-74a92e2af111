import './component';
import './style';

import { Translator } from '@/utils/translate';

let nextId = 1;

class DiffRowModel {
  constructor(data) {
    this.id = 0;
    this.tmpId = nextId++;
    this.startLabel = ko.observable('');
    this.endLabel = ko.observable('');

    if (data) this.updateData(data);

    this.onChange = ko.observable(false).extend({ notify: 'always' });
    this.startLabel.subscribe(_ => this.onChange(true));
    this.endLabel.subscribe(_ => this.onChange(true));
  }

  updateData(data) {
    this.id = data.id;
    this.startLabel(data.start_label);
    this.endLabel(data.end_label);
  }

  getData() {
    return {
      id: this.id,
      start_label: this.startLabel(),
      end_label: this.endLabel(),
    };
  }
}

export class DiffController {
  constructor(config = {}, question) {
    this.translator = Translator('question');
    this.question = question;
    this.rows = ko.observableArray([]);
    this.addRow();

    this.onChange = ko.observable(false).extend({ notify: 'always' });

    this.rows.subscribe(() => this.onChange(true));
  }

  createRowModel(data) {
    let row = new DiffRowModel(data);
    row.onChange.subscribe((v) => this.onChange(true));
    return row;
  }

  addRow() {
    this.rows.push(this.createRowModel());
  }

  removeRow(row) {
    this.rows.remove(row);
  }

  updateData(rows) {
    this.rows(rows.map((rowData) => this.createRowModel(rowData)));
  }

  getData() {
    return this.rows().map((r) => r.getData());
  }
}
