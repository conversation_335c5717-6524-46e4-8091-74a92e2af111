<template id="diff-controller-block-template">
  <div class="semantic-differencial">
    <table class="diff-table" data-bind="element: table">
      <thead>
        <tr>

          <td></td>

          <td></td>
          <td class="diff-table__point">1</td>
          <td class="diff-table__point">2</td>
          <td class="diff-table__point">3</td>
          <td class="diff-table__point">4</td>
          <td class="diff-table__point">5</td>
          <td></td>

          <!-- ko ifnot: isFullBlocked -->
          <td></td>
          <!-- /ko -->

        </tr>
      </thead>
      <tbody>
        <!-- ko foreach: {
          data: controller.rows,
          as: 'row',
          afterAdd1: fadeAfterAddFactory(250),
        } -->
        <tr class="diff-row" data-bind="diffRow: $parent.resortRows.bind($parent), attr: {
          'data-row': row.tmpId
        }">

          <td class="diff-row__drag">
            <!-- ko if: !$parent.isFullBlocked && $parent.controller.rows().length > 1 -->
            <div class="diff-row__drag-indicator pr-3" draggable="true">
              <i class="icon icon-drag-arrow--light"></i>
            </div>
            <!-- /ko -->
          </td>

          <td class="pr-3">
            <div class="diff-row__name-field">
              <input class="form-control text-right" type="text" data-bind="textInput: row.startLabel, disable: $parent.isFullBlocked" maxlength="250" />
            </div>
          </td>
          <td class="diff-table__point-filler"></td>
          <td class="diff-table__point-filler"></td>
          <td class="diff-table__point-filler"></td>
          <td class="diff-table__point-filler"></td>
          <td class="diff-table__point-filler"></td>

          <td class="pl-3">
            <div class="diff-row__name-field">
              <input class="form-control" type="text" data-bind="textInput: row.endLabel, disable: $parent.isFullBlocked" maxlength="250" />
            </div>
          </td>

          <!-- ko ifnot: $parent.isFullBlocked -->
          <td class="diff-row__delete text-right" data-bind="event: {
            mouseenter: function(_, e) {
              e.currentTarget.parentElement.classList.add('selected');
            },
            mouseleave: function(_, e) {
              e.currentTarget.parentElement.classList.remove('selected');
            }
          }">
            <!-- ko if:  $parent.controller.rows().length > 1 && (!$component.isBlocked || row.id == 0) -->
            <button class="f-icon f-icon-button f-icon--bin f-icon-light" data-bind="click: function() {
                    $parent.controller.removeRow(row);
                  }, tooltip, tooltipText: _t('Удалить')">
              <svg>
                <use href="#bin-icon"></use>
              </svg>
            </button>
            <!-- /ko -->
          </td>
          <!-- /ko -->

        </tr>
        <!-- /ko -->
      </tbody>
    </table>

    <!-- ko ifnot: isFullBlocked -->
    <button class="f-btn f-btn-add flex-grow-1 mx-1 semantic-differencial__add-row w-100" data-bind="
        click: function() { $component.addRow(); },
        text: $component.translator.t('Добавить строку')
    ">


    </button>
    <!-- /ko -->
  </div>
</template>
