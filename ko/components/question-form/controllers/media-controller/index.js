/**
 * Контроллер загрузки изображений/видео
 * Тип вопроса: Оценка (Медиа-тип Изображение, Медиа-тип Видео)
 */

import "./components/image-preview";
import "./components/video-preview";

import "Legacy/modals/load-by-link";

import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";

const formDataImageParamName = "UploadForm[file]";
const formDataImageLabelParamName = "label";
const formDataVideoParamName = "VideoUploadForm[file]";
const formDataVideoLabelParamName = "label";

import { Translator } from "@/utils/translate";
const MainTranslator = Translator("question");
const ValidationTranslator = Translator("validation");

/**
 * Модель изображения/видео
 * @class MediaModel
 */
class MediaModel {
  /**
   * @constructor
   * @param {MediaModelData} data
   * @param {MediaVariantsController} controller
   */
  constructor(controller) {
    this.controller = controller;

    this.id = ko.observable("");
    this.url = ko.observable("");
    this.label = ko.observable("");

    this.loading = ko.observable(true);
  }

  setData(data) {
    this.id(data.id);
    this.url(data.url);
    this.label(data.label);
    this.loading(false);
  }

  getUrl() {
    if (this.url()[0] == "/") return this.url();
    return "/" + this.url();
  }

  getLabel() {
    return this.label();
  }

  changeLabel(newLabel) {
    this.controller.api.changeMediaLabelUrl(this.id()).then((url) => {
      $.post(url, { label: newLabel }, () => {
        this.label(newLabel);
      });
    });
  }

  updateLabel() {
    this.changeLabel(this.label());
  }
}

class ImageModel extends MediaModel {
  constructor(controller) {
    super(controller);
  }
}

class VideoModel extends MediaModel {
  constructor(controller) {
    super(controller);

    this.preview = ko.observable("");
  }

  setData(data) {
    super.setData(data);
    this.preview(data.preview);
  }

  getUrl() {
    if (this.url().indexOf("/") === -1) {
      return "https://www.youtube.com/watch?v=" + this.url();
    } else {
      return super.getUrl();
    }
  }

  getPoster() {
    return this.preview();
  }
}

/**
 * Контроллер блока загрузки изображений/видео
 * @class
 * @property { MediaControllerApi } api
 * @property { Function } images ObservableArray.<ImageModel>
 * @property { Function } imageUrls Computed.<string>
 * @property { Function } isImageLoading Objservable.<boolean>
 * @property { Function } videos ObservableArray.<VideoModel>
 * @property { Function } videoUrls Computed.<string>
 * @property { Function } isVideoLoading Observable.<boolean>
 * @property { Function } isChanged Observable.<boolean>
 */
class MediaController {
  /**
   * @constructor
   * @param {Question} question
   */
  constructor(question) {
    this.question = question;
    this.api = this.question.controller.api;

    this.isImageLoading = ko.observable(false);
    this.isVideoLoading = ko.observable(false);

    this.imageLoader = new FoquzMultipleLoader({
      presets: ["image"],
      errors: {
        format:
          ValidationTranslator.t("Можно загружать файлы форматов:")() +
          " jpg, jpeg, png, gif",
      },
    });
    this.imageLoader.on("select", ({ file }) => {
      this.addImageByFile(file);
    });

    this.videoLoader = new FoquzMultipleLoader({
      presets: ["video"],
      errors: {
        format:
          ValidationTranslator.t("Можно загружать файлы форматов:")() +
          " .MP4, .AVI, .WMV, .MOV, .3GP, .FLV, .MPEG-1,2, .WebM",
      },
    });
    this.videoLoader.on("select", ({ file }) => {
      setTimeout(() => {
        this.addVideoByFile(file);
      });
    });

    this.images = ko.observableArray([]);
    this.imageUrls = ko.pureComputed(() => {
      return this.images().map((i) => i.getUrl());
    });
    this.imageGallery = ko.pureComputed(() => {
      return this.imageUrls().map((src) => {
        return { src };
      });
    });
    this.nextImageLabelLetter = "A";

    this.videos = ko.observableArray([]);
    this.videoUrls = ko.pureComputed(() => {
      return this.videos().map((v) => v.getUrl());
    });
    this.videoGallery = ko.pureComputed(() => {
      return this.videoUrls().map((src) => {
        return { src };
      });
    });
    this.nextVideoLabelLetter = "A";

    this.isChanged = ko.observable(false).extend({ notify: "always" });
    [this.images, this.videos].forEach((a) =>
      a.subscribe((v) => this.question.onChange())
    );
  }

  /**
   * Установка изображений в контроллер
   * и подписка на изменения
   * @param {MediaModelData[]} images Массив данных изображений
   */
  setImages(images) {
    this.images(
      images.map((i) => {
        let image = new ImageModel(this);
        image.setData({
          id: i.id,
          url: i.url,
          label: i.label,
        });
        image.label.subscribe((v) => this.isChanged(true));
        return image;
      })
    );
  }

  /**
   * Установка видео в контроллер
   * и подписка на изменения
   * @param {MediaModelData[]} videos Массив данных видео
   */
  setVideos(videos) {
    this.videos(
      videos.map((v) => {
        let video = new VideoModel(this);

        video.setData({
          id: v.id,
          url: v.url,
          preview: v.preview,
          label: v.label,
        });
        video.label.subscribe((v) => this.isChanged(true));
        return video;
      })
    );
  }

  /**
   * Получение списка изображений
   * @returns {MediaModelData[]}
   */
  getImages() {
    return this.images().map((i) => ({
      id: ko.toJS(i.id),
      url: i.getUrl(),
      label: i.getLabel(),
    }));
  }

  /**
   * Получение списка видео
   * @returns {MediaModelData[]}
   */
  getVideos() {
    return this.videos().map((v) => ({
      id: ko.toJS(v.id),
      url: v.getUrl(),
      label: v.getLabel(),
      poster: v.getPoster(),
    }));
  }

  /**
   * Удаление изображения из списка
   * @param {MediaModel} image
   */
  removeImage(image) {
    this.images.remove(image);
    if (this.images().length === 1) {
      this.images()[0].changeLabel("A");
    }
  }

  /**
   * Удаление видео из списка
   * @param {MediaModel} video
   */
  removeVideo(video) {
    this.videos.remove(video);
    if (this.videos().length === 1) {
      this.videos()[0].changeLabel("A");
    }
  }

  /**
   * Добавление модели изображения в список
   * и подписка на изменения
   * @param {MediaModel} image
   */
  addImage(image) {
    image.label.subscribe((v) => this.isChanged(true));
    setTimeout(() => {
      this.images.push(image);
    });
  }

  /**
   * Добавление модели видео в список
   * и подписка на изменения
   * @param {MediaModel} video
   */
  addVideo(video) {
    video.label.subscribe((v) => this.isChanged(true));
    setTimeout(() => {
      
      this.videos.push(video);
    });
  }

  increaseNextImageLabelLetter() {
    const charCode = this.nextImageLabelLetter.charCodeAt(0);

    if (charCode < "Z".charCodeAt(0)) {
      this.nextImageLabelLetter = String.fromCharCode(charCode + 1);
    }
  }

  increaseNextVideoLabelLetter() {
    const charCode = this.nextVideoLabelLetter.charCodeAt(0);

    if (charCode < "Z".charCodeAt(0)) {
      this.nextVideoLabelLetter = String.fromCharCode(charCode + 1);
    }
  }

  /**
   * Добавление изображения из файла
   * @param {File} file
   */
  addImageByFile(file) {
    let imageModel = new ImageModel(this);
    this.addImage(imageModel);

    //this.isImageLoading(true);

    let fd = new FormData();
    let that = this;
    fd.append(formDataImageParamName, file);

    if (!this.question.id()) {
      fd.append(formDataImageLabelParamName, this.nextImageLabelLetter);
      this.increaseNextImageLabelLetter();
    }

    this.api.loadImageByFileUrl(this.question.id()).then((url) => {
      $.ajax({
        url: url,
        cache: false,
        contentType: false,
        processData: false,
        data: fd,
        type: "post",
        success: (data) => {
          if (data.success === true) {
            let imageData = data.data;
            imageModel.setData({
              id: imageData.id,
              url: imageData.image,
              label: imageData.file_text,
            });
          }
          this.isImageLoading(false);
        },
        error: (response) => {
          this.isImageLoading(false);
          console.error(response.responseJSON);
        },
      });
    });
  }

  /**
   * Открывает модальное окно для добавления видео по ссылке
   */
  openAddVideoByLinkModal() {
    const that = this;
    window.modalOpens.push({
      dialogTemplateName: "modal-dialog-template",
      data: {
        componentName: "load-by-link-dialog",
        title: MainTranslator.t("Ссылка на Youtube"),
        youtubeSectionTitle: MainTranslator.t("Как вставить ссылку?"),
      },
      close: (result) => {
        if (result && result.link) {
          let params = {
            YouTubeVideoForm: {
              link: result.link,
            },
          };

          if (!this.question.id()) {
            params.label = this.nextVideoLabelLetter;
            this.increaseNextVideoLabelLetter();
          }

          this.api.loadYoutubeVideoUrl(this.question.id()).then((url) => {
            $.post(url, params, (response) => {
              let videoData = response.data.model;
              let video = new VideoModel(that);
              video.setData({
                id: videoData.id,
                url: videoData.link,
                preview: videoData.image,
                label: videoData.file_text,
              });
              this.addVideo(video);
            });
          });
        }
      },
    });
  }

  /**
   * Загрузка видео из файла
   * @param {File} file
   */
  addVideoByFile(file) {
    let videoModel = new VideoModel(this);

    this.addVideo(videoModel);

    this.isVideoLoading(true);

    let fd = new FormData();
    let that = this;
    fd.append(formDataVideoParamName, file);

    if (!this.question.id()) {
      fd.append(formDataVideoLabelParamName, this.nextVideoLabelLetter);
      this.increaseNextVideoLabelLetter();
    }

    this.api.loadVideoByFileUrl(this.question.id()).then((url) => {
      $.ajax({
        url: url,
        cache: false,
        contentType: false,
        processData: false,
        data: fd,
        type: "post",
        success: (data) => {
          if (data.success === true) {
            this.isVideoLoading(false);

            let videoData = data.data.model;

            videoModel.setData({
              id: videoData.id,
              url: videoData.link,
              preview: videoData.image,
              label: videoData.file_text,
            });
          }
        },
        error: (response) => {
          this.isVideoLoading(false);
          console.error(response.responseJSON);
        },
      });
    });
  }

  /**
   * Проверка контроллера на валидность
   * @returns {boolean}
   */
  isValid() {
    // TODO минимальное кол-во изображений
    return true;
  }
}

export { MediaController };
