/**
 * Привязка для отображения превью видео
 */
ko.bindingHandlers.surveyQuestionVideoFormControlListItem = {
  init: function (element, valueAccessor, allBindings) {
    const $element = $(element);
    $element.addClass('survey-question__media-form-control-list-item');

    const urls = allBindings.get('surveyQuestionVideoFormControlListItemUrls');
    const index = allBindings.get(
      'surveyQuestionVideoFormControlListItemIndex',
    );

    $(element).on('click', () => {
      $.fancybox.open(
        ko.utils.unwrapObservable(urls).map((url) => ({
          src: url,
        })),
        {
          index: ko.utils.unwrapObservable(index),
          loop: false,
          buttons: ['zoom', 'close'],
        },
      );
    });
  },
};
