/**
 * Компонент модального диалога для добавления видео по ссылке
 */

import { Translator } from '@/utils/translate';

const ValidationTranslator = Translator('validation');

ko.components.register('survey-question-add-video-by-link-modal-dialog', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        'modal-dialog',
        'modal-dialog-centered',
        'survey-question__add-video-by-link-modal-dialog',
        'survey-question__add-video-by-link-modal-dialog--initializing',
      ]);

      const viewModel = new (function () {
        this.isSubmitted = ko.observable(false);
        this.link = ko.observable('').extend({
          required: {
            message: () => ValidationTranslator.t('Обязательное поле')(),
          },
        });

        this.cancel = function () {
          if ('cancel' in params) {
            params.cancel();
          }
        };

        this.submit = function () {
          this.isSubmitted(true);

          if (this.link.isValid()) {
            if ('submit' in params) {
              params.submit({
                link: this.link(),
              });
            }
          }
        };

        this.formControlErrorStateMatcher = function (formControl) {
          return ko.computed(() => {
            return this.isSubmitted() && !formControl.isValid();
          });
        };
      })();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass(
          'survey-question__add-video-by-link-modal-dialog--initializing',
        );
        viewModel.initializing(false);
      };

      return viewModel;
    },
  },
  template: {
    element: 'survey-question-add-video-by-link-modal-dialog-template',
  },
});
