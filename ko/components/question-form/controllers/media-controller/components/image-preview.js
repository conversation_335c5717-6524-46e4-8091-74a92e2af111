/**
 * Привязка для отображения превью изображения
 */
ko.bindingHandlers.surveyQuestionImageFormControlListItem = {
  init: function (element, valueAccessor, allBindings) {
    const $element = $(element);
    $element.addClass(['survey-question__media-form-control-list-item', 'cursor-zoom-in']);

    const urls = allBindings.get('surveyQuestionImageFormControlListItemUrls');
    const index = allBindings.get(
      'surveyQuestionImageFormControlListItemIndex',
    );

    $(element).on('click', (event) => {
      if (
        !$(event.target).closest(
          '.survey-question__media-form-control-list-item-button',
        ).length
      ) {
        $.fancybox.open(
          ko.utils.unwrapObservable(urls).map((url) => ({
            src: url,
          })),
          {
            index: ko.utils.unwrapObservable(index),
            loop: false,
            buttons: ['rotate', 'zoom', 'close'],
          },
        );
      }
    });
  },
};
