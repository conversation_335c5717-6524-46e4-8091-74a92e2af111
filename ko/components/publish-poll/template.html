<!-- ko if: !window.CURRENT_USER.watcher -->
<!-- ko if: isPublished -->

<div>
  <!-- ko if: isActive -->
  <fc-button
    class="flex-shrink-0 ml-10p stop-btn"
    type="button"
    params="click: function() {
                        stop()
                }, 
                label: translator.t('Выключить опрос'),
                icon: { name: 'stop' },
                color: 'danger'"
  ></fc-button>
  <!-- /ko -->

  <!-- ko ifnot: isActive -->
  <fc-button
    class="flex-shrink-0 ml-10p start-btn"
    type="button"
    params="click: function() {
                start()
        }, 
        label: translator.t('Запустить опрос'),
        icon: { name: 'start' },
        color: 'success'"
  ></fc-button>
  <!-- /ko -->
</div>
<!-- /ko -->

<!-- ko ifnot: isPublished -->
<div class="d-none d-md-block">
  <button
    class="flex-shrink-0 ml-10p publish-btn"
    type="button"
    data-bind="click: publish, disable: isTmp, text: translator.t('Опубликовать')"
  ></button>
</div>

<div class="d-md-none">
  <button
    class="flex-shrink-0 ml-10p publish-btn"
    type="button"
    data-bind="click: publish, disable: isTmp"
  >
    <svg-icon params="name: 'poll-publish'"></svg-icon>
  </button>
</div>

<confirm-dialog
  params="ref: confirm,
     title: translator.t('Опрос будет опубликован'),
     text: confirmText,
     confirm: translator.t('Опубликовать'),
     mode: 'success'"
>
</confirm-dialog>
<!-- /ko -->
<!-- /ko -->

<!-- ko if: window.CURRENT_USER.watcher && isPublished -->
<!-- ko if: isActive -->
<button
  class="flex-shrink-0 ml-10p watcher-btn watcher-btn__started"
  type="button"
  data-bind="disable: true"
>
  <svg-icon params="name: 'poll-start', width: 18, height: 18"></svg-icon>
  <span>Запущен</span>
</button>
<!-- /ko -->

<!-- ko ifnot: isActive -->
<button
  class="flex-shrink-0 ml-10p watcher-btn watcher-btn__stoped"
  type="button"
  data-bind="disable: true"
>
  <svg-icon params="name: 'poll-stop', width: 18, height: 18"></svg-icon>
  <span>Остановлен</span>
</button>
<!-- /ko -->
<!-- /ko -->
