import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('print-footer', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      element.classList.add('only-print');
      element.style.display = "none";

      return new ViewModel(params, element);
    },
  },
  template: html,
});
