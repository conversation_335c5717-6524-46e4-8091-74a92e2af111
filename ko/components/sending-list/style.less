@import 'Style/colors.less';
@import 'Style/typography.less';

.foquz-sending-list {
  display: block;

  .table {
    margin-bottom: 0;
    
    td {
      padding-top: 10px;
      padding-bottom: 10px;

      vertical-align: middle;
      font-size: @f-fs-2;
    }
  }

  .f-icon-channel--repeats {
    color: @f-color-service;
  }

  @media screen and (max-width: 767px) {
    .table {
      .mobile-hidden {
        display: none;
      }

      .foquz-sending-list {

        &__name {
          padding-bottom: 2px;
        }

        &__info {
          td {
            padding-top: 2px;
            border-top: 0;
          }
        }

        &__close {
          text-align: center;

          color: @f-color-primary;
          cursor: pointer;
          border-bottom: 0;

          td {
            font-size: @f-fs-1;
          }
        }
      }
    }

  }
  @media screen and (min-width: 768px) {
    .mobile-visible {
      display: none;
    }
  }
}
