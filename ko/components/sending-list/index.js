import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('sending-list', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);
      $element.addClass('foquz-sending-list');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
