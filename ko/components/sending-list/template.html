

<table class="table f-table">
  <thead>
    <tr>
      <th data-bind="text: _t('Отправлена')"></th>
      <th class="mobile-hidden" data-bind="text: _t('Каналы')"></th>
      <th data-bind="text: _t('Повторы')">
      </th>
      <th data-bind="text: _t('Ответ')">
      </th>
    </tr>
  </thead>

  <tbody>
    <!-- ko foreach: list -->
    <tr class="mobile-visible">
      <td colspan="3" class="foquz-sending-list__name">
        <div class="d-flex align-items-center">
          <foquz-icon class="mr-2" params="prefix: 'channel', icon: type"></foquz-icon>
          <!-- ko text: name -->
          <!-- /ko -->
        </div>
      </td>
    </tr>
    <tr class="foquz-sending-list__info">
      <td data-bind="text: sended"></td>
      <td class="mobile-hidden">
        <div class="d-flex align-items-center">
          <foquz-icon class="mr-2" params="prefix: 'channel', icon: type"></foquz-icon>
          <!-- ko text: _t(name) -->
          <!-- /ko -->
        </div>
      </td>
      <td>
        <div class="d-flex align-items-center justify-content-center">
          <foquz-icon class="mr-20p" params="prefix: 'channel', icon: 'repeats'"></foquz-icon>
          <!-- ko text: repeats -->
          <!-- /ko -->
        </div>
      </td>
      <td class=""
          data-bind="text: hasResponse ? _t('есть') : '—'">
      </td>
    </tr>
    <!-- /ko -->
    <tr class="mobile-visible foquz-sending-list__close">
      <td colspan="3">
        <span data-bind="text: _t('Закрыть')"></span>
      </td>
    </tr>
  </tbody>
</table>
