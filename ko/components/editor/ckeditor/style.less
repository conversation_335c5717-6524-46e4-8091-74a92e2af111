.ckeditor {
  display: block;

  .ck-dropdown__panel {
    .ck-list {
      max-height: 480px;
      overflow-y: auto;
      
      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: #E8EDEE;
        width: 8px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: #8E99A3;
        border-radius: 20px;
      }
    }
  }

  .ck-content {
    min-height: 250px;

    &.ck-editor__editable:not(.ck-focused)[contenteditable="false"] {
      background-color: #f2f5f6;
    }

    b,
    strong {
      font-weight: bold;
    }
    i {
      font-style: italic;
    }
    ul, ol {
      padding-left: 40px;
    }
    ul > li {
      list-style: disc;
    }
    ol > li {
      list-style: decimal;
    }
  }

  &.is-invalid {
    .ck-editor {
      border: 1px solid #f96261;
      box-shadow: 0 0 5px rgba(249, 98, 97, 0.5) !important;
    }
  }
}


.ckeditor_min .ck-content {
  min-height: 48px !important;
}

.ck.ck-balloon-panel {
  z-index: calc(var(--foquz-dialog-z-index) + 1)!important;
}
