import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Bindings/editor/ckeditor';
import 'Components/editor/editor-variables'

ko.components.register('ckeditor', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('ckeditor');
      element.classList.add('cke-wrapper');

      return new ViewModel(params, element);
    }
  },
  template: html
});
