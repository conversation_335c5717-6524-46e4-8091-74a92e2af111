<div>
  <div
    data-bind="
      foquzCkeditor: value,
      foquzCkeditorSettings: {
        readOnly: disabled,
        toolbar,
        placeholder,
        execute,
        options,
        blankExternalLinks,
      },
      foquzCkeditorInstance: editor
    "
  ></div>
</div>

<!-- ko if: error -->
<div class="form-error"
     data-bind="text: error"></div>
<!-- /ko -->

<!-- ko template: {
  foreach: templateIf(inited() && !disabled, $data),
  afterAdd: fadeAfterAddFactory(400)
} -->
<editor-variables
                  params="variables: variables, setVariable: $component.setVariable.bind($component), promocode: promocode">
</editor-variables>
<!-- /ko -->
