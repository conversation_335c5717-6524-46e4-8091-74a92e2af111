let presets = {
  interscreen: {}
};


export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.disabled = params.disabled;

    this.inited = ko.observable(false);

    this.value = params.value;
    this.editor = ko.observable(null);

    this.editor.subscribe((v) => {
      this.inited(true);
    });

    this.variables = params.variables;

    let preset = presets[params.preset];
    this.ckeditorSettings = preset || {};

    this.promocode = params.promocode;

    this.error = params.error;
    
    this.toolbar = params.toolbar;

    this.execute = params.execute;

    this.options = params.options;

    this.placeholder = params.placeholder;

    this.blankExternalLinks = params.blankExternalLinks;
  }

  setVariable(variable) {

    if (typeof variable == 'function') {
      variable().then((v) => this.editor().insertInSelection(v));
    } else {
      this.editor().insertInSelection(variable.toString());
    }
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
