// import { ViewModel } from './model';
// import html from './template.html';
// import './style.less';

// import 'Components/editor/editor-variables';
import 'Bindings/editor/tinymce';

// ko.components.register('tinymce', {
//   viewModel: {
//     createViewModel: (params, componentInfo) => {
//       let element = componentInfo.element;
//       element.classList.add('tinymce');

//       return new ViewModel(params, element);
//     }
//   },
//   template: html
// });
