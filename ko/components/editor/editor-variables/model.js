import { variables } from "Data/variables";
import { DialogsModule } from "Utils/dialogs-module";
import {
  COPY_PROMOCODE_VARIABLE,
  PROMOCODE_VARIABLE,
} from "../../../constants/editor-variables";

let unique = 1;

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];
    this.$element = $(element);

    this.unique = unique++;

    DialogsModule(this);
    //this.modals = ko.observableArray([]);

    this.variablesSet = params.variables;
    this.variables = ko.pureComputed(() => {
      return ko.utils
        .unwrapObservable(this.variablesSet)
        .map((v) => variables[v])
        .filter(Boolean);
    });

    this.promocode = params.promocode;

    this.onSetVariable = params.setVariable;
  }

  setVariable(variable, event) {
    if (event.target.closest(".fc-question")) return;

    if (
      variable.id == PROMOCODE_VARIABLE ||
      variable.id === COPY_PROMOCODE_VARIABLE
    ) {
      this.setPromocode()
        .then(() => {
          this.onSetVariable(variable.value);
        })
        .catch(() => {});

      return;
    }

    this.onSetVariable(variable.value);
  }

  getVariableLabel(variable) {
    if (
      variable.id === PROMOCODE_VARIABLE ||
      variable.id === COPY_PROMOCODE_VARIABLE
    ) {
      const label = variable.id === COPY_PROMOCODE_VARIABLE
        ? "Промокод с копированием"
        : "Промокод";

      const data = this.promocode.poolData();
      if (!data) {
        return label;
      }

      if (this.promocode.isPool()) {
        return `${label}: ${data.title}`;
      }

      return `${label}: ${data.code}`;
    }
    return variable.text;
  }

  getVariableUnique(variable) {
    return "editor-variable-" + this.unique + "-" + variable.id;
  }

  setPromocode() {
    return new Promise((res, rej) => {
      this.openDialog({
        name: "promocode-dialog",
        params: {
          promocode: this.promocode,
        },
        events: {
          submit: () => {
            console.log("submit");
            res();
          },
          hide: () => rej(),
        },
      });
      // this.modals.push({
      //   name: 'promocode-modal',
      //   params: {
      //     promocode: this.promocode,
      //     onChange: () => res(),
      //     onClose: () => rej()
      //   }
      // });
    });
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
