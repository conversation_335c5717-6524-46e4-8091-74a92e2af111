import { ViewModel } from './model';
import html from './template.html';
import './style.less';

// import 'Modals/promocode-modal';
import 'Dialogs/promocode-dialog';

ko.components.register('editor-variables', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('editor-variables');

      return new ViewModel(params, element);
    }
  },
  template: html
});
