import OverlayScrollbars from 'overlayscrollbars';
import { Highchart } from '../highchart';

export class ViewModel extends Highchart {
  get type() {
    return 'column';
  }

  get plotOptions() {
    return {
        maxPointWidth: 80,
    }
  }

  get xAxis() {
    return {
      labels: {
        y: 24,
        style: {
          fontFamily: 'Roboto, Arial, sans-serif'
        }
      }
    }
  }

  get yAxis() {
    return {
      ...super.yAxis,
      labels: {
        x: -8,
        style: {
          fontFamily: 'Roboto, Arial, sans-serif'
        }
      }

    }
  }

  get chart() {
    return {
      ...super.chart,
      spacingRight: 0,
      spacingBottom: 0,
    }
  }
}
