# Bar chart

```
{
  xAxis: {
    type: 'category',
    categories: ['category 1', 'category 2'],
    scrollbar: {
      enabled: true,
      showFull: false
    },

    /* Каким-то образом влияют на расстояние между барами при type=category */
    min: 0,
    max: Math.max(count, 25)
  },

  yAxis: {
    opposite: true, // отсечки сверху
    labels: {
      formatter: function() {
        return this.value;
      }
    }
  },
  plotOptions: {
    bar: {
      pointWidth: 10, // высота бара
      dataLabels: {
        enabled: true, // цифры рядом с барами
      }
    }
  },
}
```

## Опции

```
<bar-highchart params="options: {
  signColor: true, // красные и зеленые бары
  resize: true, // обновлять высоту графика
}"></bar-highchart>
```
