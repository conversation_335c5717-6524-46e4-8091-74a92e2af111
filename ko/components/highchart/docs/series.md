# Данные для графика

Передаются в параметре `series`.

## Формат серии

```
{
  name: 'Seria 1',

  data: [
    {
      name: 'Point 1',
      y: 2,
      color: 'green',
    },
    {
      name: 'Point 2',
      y: 4,
      color: 'green',
    }
  ],

  dataLabels: {
    enabled: true,
    inside: true, // метка внутри столбика
    formatter: function () {
      return ((this.y / total) * 100).toFixed(0) + '%';
    }
  },

  /* раскрашивать столбики внутри серии */
  colorByPoint: true,
}
```
