import { ViewModel } from './model';
import html from '../template.html';
import './style.less';

ko.components.register('pie-highchart', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      $element.addClass('foquz-highchart-pie')

      return new ViewModel(params, element);
    },
  },
  template: html,
});
