import { chartColors } from 'Data/chart-colors';
import { FoquzComponent } from 'Models/foquz-component';
import './empty/';

import './style.less';

let unique = 1;
export class Highchart extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    element.hchart = this;

    this.unique = unique++;

    element.classList.add('foquz-highchart');

    this.options = params.options || {};

    this.loading = params.loading;

    this.chartRef = ko.observable(null);
    this.config = ko.toJS(params.config) || {};

    this.noChartData = ko.observable(true);

    this.pointsCount = ko.observable(0);

    this.instance = ko.observable(null);

    this.noChartData.subscribe((v) => {
      if (!v) this.instance(null);
    });
  }

  isEmptyData(series) {
    if (!series) return true;
    if (!series.length) return true;
    if (!series.some((s) => s.data && s.data.length)) return true;
    return false;
  }

  get colors() {
    return chartColors;
  }

  get credits() {
    return {
      enabled: false
    };
  }

  get legend() {
    return {
      enabled: false
    };
  }

  get title() {
    return {
      text: '',
      style: { display: 'none' }
    };
  }

  get subtitle() {
    return {
      text: '',
      style: { display: 'none' }
    };
  }

  get chart() {
    let that = this;
    return {
      events: {
        load: function () {},
        redraw: function () {},
        render: function () {
          that.onChartRender();
        }
      },
      height: 270,
      spacingLeft: 0,
      style: {
        fontFamily: 'Roboto, Arial, sans-serif'
      }
    };
  }

  get plotOptions() {
    return {};
  }

  get xAxis() {
    return {};
  }

  get yAxis() {
    return {
      title: {
        enabled: false
      }
    };
  }

  get legend() {
    return {
      enabled: false
    };
  }

  get exporting() {
    return {
      enabled: false
    };
  }

  onRedraw(chart) {}

  handleSeries(series = []) {
    return series;
  }

  get handledKeys() {
    return [
      'chart',
      'legend',
      'credits',
      'colors',
      'title',
      'subtitle',
      'series',
      'yAxis',
      'xAxis',
      'plotOptions'
    ];
  }

  getConfig() {
    let config = this.config;

    let handledKeys = this.handledKeys;

    let chartConfig = {};
    Object.keys(config)
      .filter((key) => !handledKeys.includes(key))
      .forEach((key) => {
        chartConfig[key] = config[key];
      });

    chartConfig.series = this.handleSeries(config.series);
    if (chartConfig.series.length == 0) chartConfig.series = [{}];

    let pointsCount = chartConfig.series
      .map((s) => (s.data && s.data.length ? s.data.length : 0))
      .reduce((sum, s) => sum + s);
    this.pointsCount(pointsCount);

    chartConfig.chart = {
      type: this.type,
      ...this.chart,
      ...(config.chart || {})
    };

    let plotOptions = config.plotOptions || {};
    if (this.type in plotOptions) plotOptions = plotOptions[this.type];

    chartConfig.plotOptions = {
      [this.type]: {
        ...this.plotOptions,
        ...plotOptions
      }
    };

    chartConfig.colors = 'colors' in config ? config.colors : this.colors;

    [
      'legend',
      'title',
      'subtitle',
      'credits',
      'legend',
      'xAxis',
      'yAxis',
      'exporting'
    ].forEach((key) => {
      chartConfig[key] = {
        ...this[key],
        ...(config[key] || {})
      };
    });

    return chartConfig;
  }

  onChartRender() {
    this.emitEvent('render');
  }

  onRender() {
    this.log('on render');
    let chartConfig = this.getConfig();
    this.log('get config');

    this.noChartData(this.isEmptyData(chartConfig.series));

    if (!this.chartRef()) return;

    let instance = this.instance();
    // if (instance) {
    //   this.instance().update(chartConfig, true, false, false);
    // } else {
    //   this.instance(Highcharts.chart(this.chartRef(), chartConfig));
    // }
    if (instance) instance.destroy();
    this.log('start render instance');
    let chartInstance = Highcharts.chart(this.chartRef(), chartConfig);

    this.log('finish render instance');

    this.instance(chartInstance);
  }

  update(newConfig) {
    this.log('start', this.config, newConfig);
    let oldConfig = this.config;

    let chartConfig = {};
    Object.keys(oldConfig).forEach((key) => {
      chartConfig[key] = oldConfig[key];
    });
    Object.keys(newConfig).forEach((key) => {
      chartConfig[key] = newConfig[key];
    });

    [
      'chart',
      'legend',
      'title',
      'subtitle',
      'credits',
      'legend',
      'xAxis',
      'yAxis'
    ].forEach((key) => {
      chartConfig[key] = {
        ...oldConfig[key],
        ...(newConfig[key] || {})
      };
    });

    let oldPlotOptions = oldConfig.plotOptions || {};
    if (this.type in oldPlotOptions)
      oldPlotOptions = oldPlotOptions[this.type] || {};

    let newPlotOptions = newConfig.plotOptions || {};
    if (this.type in newPlotOptions)
      newPlotOptions = newPlotOptions[this.type] || {};

    chartConfig.plotOptions = {
      [this.type]: {
        ...oldPlotOptions,
        ...newPlotOptions
      }
    };

    this.config = chartConfig;

    this.log('config created', this.config);

    this.onRender();

    this.log('end');
  }

  setData(series) {
    this.update({
      series: series
    });
  }

  log(...params) {
    //console.log('CHART: ', this.type, ...params);
  }
}
