import { Highchart } from '../highchart';

export class ViewModel extends Highchart {
  get type() {
    return 'line';
  }

  get chart() {
    return {
      ...super.chart,
      scrollablePlotArea: {
        minWidth: 600,
        scrollPositionX: 1
      }
    };
  }

  get plotOptions() {
    return {
      marker: {
        symbol: 'circle',
        width: 1,
        height: 1,
        radius: 1
      }
    };
  }

  get legend() {
    return {
      enabled: true,
      symbolRadius: 5,
      symbolWidth: 10,
      symbolHeight: 10
    };
  }

  onChartRender() {
    this.log('start cb');
    let counter = 0;
    let scrollContainer = this.element.querySelector('.highcharts-scrolling');

    let scrollToEnd = this.options.scrollToEnd;

    setTimeout(() => {
      new OverlayScrollbars(scrollContainer, {
        callbacks: {
          onInitialized: function () {
            // if (scrollToEnd) this.scroll({ x: "100%" });
          },
          onScroll: () => {
            if (!counter++) {

              this.instance().reflow();
            }
          }
        }
      });
    });

    this.log('end cb');
  }

  log(label) {
    // let name = 'chart' + this.type + this.unique;
    // if (label == 'start') {
    //   console.time(name);
    // } else if (label == 'end') {
    //   console.timeEnd(name, 'end');
    // } else {
    //   console.timeLog(name, label);
    // }
  }
}
