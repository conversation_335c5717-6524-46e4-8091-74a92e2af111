import OverlayScrollbars from 'overlayscrollbars';
import { Highchart } from '../highchart';

export class ViewModel extends Highchart {
  get type() {
    return 'funnel';
  }

  get plotOptions() {
    if (this.options.big) {
      return {
        neckWidth: 120,
        neckHeight: '40%',
        dataLabels: {
          connectorWidth: 0,
          style: {
            fontWeight: 400
          }
        },
        width: '100%'
      }
    }
    return {
      neckWidth: 75,
      neckHeight: '40%',
      height: 180,
      width: 300,
      dataLabels: {
        connectorWidth: 0,
        style: {
          fontWeight: 400
        }
      }
    };
  }

  get chart() {
    return {
      ...super.chart,
      spacingRight: 0,
      spacingTop: 0,
      spacingBottom: 0,
    }
  }
}
