import OverlayScrollbars from 'overlayscrollbars';
import { Highchart } from '../highchart';

export class ViewModel extends Highchart {
  get type() {
    return 'bar';
  }

  get chart() {
    return {
      ...super.chart
      // spacingTop: 40,
      // scrollablePlotArea: {
      //   minHeight: 400
      // }
    };
  }

  get plotOptions() {
    let opts = {
      pointWidth: 10,

      dataLabels: {
        enabled: true,
        color: '#2E2F31',
        style: {
          fontWeight: 'normal'
        }
      },

      groupPadding: 0.5
    };

    return opts;
  }

  get xAxis() {
    return {
      type: 'category',
      scrollbar: {
        enabled: true,
        showFull: false
      },
      labels: {
        style: {
          color: '#2E2F31'
        }
      },
      min: 0,
      max: 35,
      lineColor: '#e6e6e6',
      lineWidth: 1
    };
  }

  get yAxis() {
    return {
      ...super.yAxis,
      opposite: true
    };
  }

  getConfig(config) {
    let c = super.getConfig(config);

    if (this.options.fullHeight) {

      

      c.chart.height = this.pointsCount() * 45 + 60;
    } else {
      c.chart.height = Math.min(this.pointsCount() * 45 + 60, 400);
    }

    c.xAxis.min = 0;
    c.xAxis.max = Math.min(this.pointsCount() - 1, 20);

    return c;
  }

  resize(w, h) {
    let instance = this.instance();
    if (!instance) return;
    instance.setSize(w, h, false, false);
  }

  handleSeries(series) {
    return (series || []).map((s) => {
      if (s.data && this.options.signColor) {
        s.data = s.data.map((i) => {
          return {
            ...i,
            color: i.y > 0 ? '#00C968' : '#F96261'
          };
        });
      }
      return {
        colorByPoint: true,
        ...s
      };
    });
  }
}
