@import "Style/colors";

.review-question-preview-wrapper {
  display: block;
  width: 110px !important;
  padding-bottom: 10px;
}

.review-question-preview {
  &--deleted {
    border-color: @f-color-danger;
  }
  &__index {
    display: flex;
  }
  &__point {
    margin-top: -0.7px;
  }

  &__skip {
    display: flex;
    width: 32px;
    height: 32px;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: 50%;
    font-weight: 900;
    font-size: 13px;
    color: black;
    line-height: 1;

    background-color: #eceff1;
  }

  .review-question-preview__question-icon {
    width: 26px;
    height: 26px;
    color: #A6B1BC;
  }
  .review-question-preview__media-type-indicator,
  .review-question-preview__rating-wrapper {
    position: relative;

    .skip-marker {
      transform: translate(6px, -6px);
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      background: #eceff1;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
