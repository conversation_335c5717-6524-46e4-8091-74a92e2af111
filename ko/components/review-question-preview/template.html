<div
  class="review-question-preview"
  data-bind="
        css: {
            'review-question-preview--disabled': disabled && !$data.hasComment,
            'review-question-preview--deleted': deleted,
            'review-question-preview--active': active,
            'review-question-preview--skipped': $data.skipped,
        },
        click: function() { 
          $component.onClick() 
        }
    "
>
  <!-- Номер вопроса -->
  <span class="review-question-preview__index">
    <span
      class="font-weight-500"
      data-bind="text: formattedIndex, tooltip"
    ></span>

    <!-- ko if: question.pointName -->
    <span
      class="review-question-preview__point ml-5p"
      data-bind="tooltip, tooltipText: _t('answers', 'Вопрос связан с точкой контакта {point}', {
            point: question.pointName
          })"
    >
      <svg-icon params="name: 'aim', width: 14, height: 14"></svg-icon>
    </span>
    <!-- /ko -->

    <!-- ko if: donorIndex -->
    <span>
      /
      <span
        class="f-color-danger"
        data-bind="text: donorIndex, tooltip, tooltipText: 'Реципиент вопроса ' + donorIndex"
      ></span
    ></span>
    <!-- /ko -->
  </span>
  <!-- /Номер вопроса -->

  <!-- Комментарий -->
  <!-- ko if: hasComment -->
  <i class="review-question-preview__comment-indicator"></i>
  <!-- /ko -->
  <!-- /Комментарий -->

  <div class="review-question-preview__type">
    <!-- Рейтинг -->
    <!-- ko if: isRating -->
    <!-- ko if: skipped -->
    <div class="review-question-preview__skip">–</div>
    <!-- /ko -->
    <!-- ko ifnot: skipped -->
    <div class="review-question-preview__rating-wrapper">
      <div
        class="review-question-preview__rating"
        data-bind="text: rating, css: 'review-question-preview__rating--value_' + ratingValue"
      ></div>
      <!-- ko if: $data.variantSkipped && !disabled -->
      <svg-icon class="skip-marker" params="name: 'wavy-circle-2'"></svg-icon>
      <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <!-- /Рейтинг -->

    <!-- Тип -->
    <!-- ko if: !isRating && type -->
    <i class="review-question-preview__media-type-indicator">
    <svg class="review-question-preview__question-icon">
      <use data-bind="attr: {
                href: '#foquz-icon-question-' + type
            }"></use>
    </svg>
    <!-- ko if: skipped && !disabled -->
    <svg-icon class="skip-marker" params="name: 'wavy-circle-3'"></svg-icon>
    <!-- /ko -->
    <!-- ko if: !skipped && $data.variantSkipped && !disabled -->
    <svg-icon class="skip-marker" params="name: 'wavy-circle-2'"></svg-icon>
    <!-- /ko -->
    </i>
    <!-- /ko -->
    <!-- /Тип -->
  </div>

  <span
    class="review-question-preview__name"
    data-bind="text: question.shortName, tooltip2, tooltipText: question.shortName"
  >
  </span>

  <!-- ko if: question.employee !== null -->
  <span
    class="review-question-preview__employee-name"
    data-bind="text: question.employee.name, tooltip2, tooltipText: question.employee.name"
  >
  </span>
  <!-- /ko -->

  <!-- ko if: question.answer && question.withPoints && !question.without_points -->
  <span class="review-question-preview__employee-name">
    <span data-bind="text: question.answerPoints || 0"></span> из
    <span data-bind="text: question.maxPoints || 0"></span>
  </span>
  <!-- /ko -->
</div>
