import { find as _find } from "lodash";

import { FoquzComponent } from "Models/foquz-component";

function getQuestionType(question) {

  switch (parseInt(question.type)) {
    case 0:
      return question.mediaType;
    case 1:
      return "vars";
    case 2:
      return "text-input";
    case 3:
      return "date";
    case 4:
      return "address";
    case 5:
      return "upload";
    case 6:
      return "quiz";
    case 7:
      return "star-variants";
    case 8:
      return "priority";
    case 9:
      return "media-variants";
    case 10:
      return "gallery";
    case 11:
      return "smile";
    case 12:
      return "nps";
    case 13:
      return "matrix";
    case 14:
      return "diff";
    case 15:
      return "stars";
    case 17:
      return "filials";
    case 18:
      return "rating";
    case 19:
      return "classifier";
    case 20:
      return "scale";
    case 21:
      return "matrix-3d";
    case 22:
      return "card-sorting";
    case 23:
      return "distribution-scale";
  }
}

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.question = params.question;
    this.active = params.active;
    this.review = params.review;

    this.donorIndex =
      this.question.donorIndex > 0
        ? `${this.question.donorIndex}`.padStart(2, "0")
        : null;

    this.index = ko.unwrap(params.index);

    this.formattedIndex =
      parseInt(this.index + 1) > 9 ? this.index + 1 : "0" + (this.index + 1);

    this.hasComment = this.question.isCommented;

    this.isRating = this.question.isRating;
    this.rating = Math.round(this.question.rating * 10) / 10;
    this.total = 5;

    if (this.question.total) {
      this.total = this.question.total;
    }

    const ratingValue = Math.round((this.rating / this.total) * 100) / 20;
    this.ratingValue = Math.round(ratingValue);

    this.skipped = this.question.skipped;
    this.variantSkipped = this.question.skipped;

    this.type = getQuestionType(this.question);


    this.disabled = !this.question.hasAnswer;
    if (this.type === "scale") { // ToDo refactor
      if (this.skipped) {
        this.variantSkipped = false;
      } else {
        this.disabled = _find(this.question.answerData, el => el >= 0 || isNaN(el)) === undefined;
        this.skipped = this.disabled;
        if (!this.skipped) {
          this.variantSkipped = this.question.variants.find(el => {
            return !(parseInt(this.question.answerData[+el.id]) >= 0);
          });
        }
      }
    }
    if (this.type === "star-variants") { // ToDo refactor
      if (this.skipped) {
        this.variantSkipped = false;
      } else {
        this.variantSkipped = this.question.variants.find(el => {
          return !parseInt(this.question.answerData[+el.id]);
        });
      }      
    }
    if (this.type === "matrix") { // ToDo refactor
      if (this.skipped) {
        this.variantSkipped = false;
      } else {
        this.disabled = _find(this.question.answerData.answer, el => el != "null") === undefined;
        this.skipped = this.disabled;
        if (!this.skipped) {
          this.variantSkipped = this.question.matrix.rows.find(row => {
            return this.question.answerData.answer[row] == "null";
          });
        }
      }
    }
    
    this.deleted = this.question.isDeleted && !this.question.hasAnswer;

    this.onClick = () => {
      params.onClick(this.index);
    };
  }
}
