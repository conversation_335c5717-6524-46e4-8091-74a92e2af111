<div class="group-actions">
  <div
    class="group-actions__container"
    data-bind="fade: checkedCount() > 0"
  >
    <span class="group-actions__counter">
      <span
        data-bind="text: _t('Выбрано')"
        class="group-actions__counter-label"
      ></span>
      <span
        class="group-actions__counter-value"
        data-bind="text: checkedCount()"
      ></span>
    </span>

    <button
      type="submit"
      class="btn btn-default group-actions__select-all"
      data-bind="click: selectAll, text: _t('Выбрать все')"
    ></button>

    <select
      data-bind="
        value: currentActionId,
        select2: {
          containerCssClass: 'form-control',
          wrapperCssClass: 'select2-container--form-control group-actions__select',
          dropdownCssClass: 'group-actions__select-dropdown',
          minimumResultsForSearch: 0,
          allowClear: true,
          placeholder: _t('Выберите действие'),
        }
      "
    >
      <option></option>
      <!-- ko foreach: groupActions -->
        <option
          data-bind="value: id, text: name"
        ></option>
      <!-- /ko -->
    </select>

    <!-- ko if: !!currentAction()?.component -->
      <div
        data-bind="
          class: currentAction().classes,
          component: {
            name: currentAction().component,
            params: currentAction().params,
          }
        "
      ></div>
    <!-- /ko -->

    <button
      type="submit"
      class="btn btn-danger group-actions__dismiss"
      data-bind="click: deselectAll"
    ></button>
    <button
      type="submit"
      class="btn btn-success group-actions__apply"
      data-bind="
        attr: { disabled: currentActionId() === '' },
        click: function() { applyAction(currentActionId(), currentAction()); }
      "
    ></button>
  </div>
</div>
<!-- ko if: showSaveMessage && showErrorMessage && saveMessageText -->
<success-message
  params="
    show: showSaveMessage,
    showCross: true,
    text: saveMessageText,
    delay: 3000,
  "
></success-message>
<success-message
  class="foquz-success-message--error"
  params="
    show: showErrorMessage,
    showCross: true,
    text: 'Произошла ошибка. Попробуйте ещё раз',
    delay: 3000,
  "
></success-message>
<!-- /ko -->
