export class ViewModel {
  constructor({
    currentActionId,
    groupActions,
    checkedCount,
    selectAll,
    deselectAll,
    applyAction,
    showSaveMessage,
    showErrorMessage,
    saveMessageText,
  }) {
    this.currentActionId = (ko.isObservable(currentActionId) && currentActionId) || ko.observable('');
    this.groupActions = groupActions;
    this.checkedCount = checkedCount;
    this.selectAll = selectAll;
    this.deselectAll = deselectAll;
    this.applyAction = applyAction;
    this.showSaveMessage = showSaveMessage;
    this.showErrorMessage = showErrorMessage;
    this.saveMessageText = saveMessageText;

    this.currentAction = ko.computed(() => {
      const currentActionId = this.currentActionId();
      const action
        = this.groupActions.find(({ id }) => id === currentActionId)
      return action;
    })
  }
}
