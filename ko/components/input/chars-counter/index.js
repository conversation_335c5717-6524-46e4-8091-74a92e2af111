import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('foquz-chars-counter', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);
      $element.addClass('foquz-chars-counter');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
