<!-- ko if: rendering -->
rendering...
<!-- /ko -->

<!-- ko ifnot: rendering -->
<select multiple
        data-bind="
          template: { nodes: options() },
          selectedOptions: value,
          valueAllowUnset: true,
          select2: {
            containerCssClass: 'form-control',
            wrapperCssClass: 'select2-container--form-control',
            allowClear: false,
            placeholder: 'Не выбрано',
            templateSelection: $component.selectionTmp.bind($component),
            templateResult: $component.resultTmp.bind($component),
            matcher: $component.matcher.bind($component),
            minimumResultsForSearch: 0,
          },
          event: {
            change: (_, event) => {
              $component.onChange(event);
            },
          },
        }"
        data-placeholder="Не выбрано">
</select>
<!-- /ko -->
