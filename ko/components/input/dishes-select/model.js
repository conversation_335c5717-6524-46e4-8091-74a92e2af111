import { Directory } from '../../../utils/directory';
import html from './options.html';

$('body').append(
  `<template id="foquz-dishes-select-template">${html}</template>`,
);

const tmp = document.createElement('div');

export class ViewModel {
  constructor(params, element) {
    this.value = params.value;

    this.rendering = ko.observable(true);

    this.options = ko.observableArray([]);
    this.directory = new Directory('dish-categories');

    if (!this.directory.loaded()) {
      this.init();
    }
  }

  init() {
    this.directory.load();

    this.directory.onLoad(() => {
      ko.renderTemplate(
        'dishes-select-template',
        {
          categories: this.directory.data,
        },
        {},
        tmp,
      );

      let children = Array.from(tmp.children);
      children.forEach((el) => el.removeAttribute('data-bind'));

      this.options(children);
      this.rendering(false);
    });
  }

  matcher({ term }, data) {
    if (!term) {
      return data;
    }

    term = term.toLowerCase();

    if (!data.id) {
      return null;
    }

    if (data.id[0] != 'c') {
      const categoryId = data.element.dataset.category;
      const category = this.directory
        .data()
        .find((c) => c.id === categoryId);
      if (!category) return null;

      const categoryName = category.name.toLowerCase();
      const dishText = data.text.toLowerCase();

      const match = categoryName.includes(term) || dishText.includes(term);
      return match ? data : null;
    } else {
      const category = this.root.directories.dishes
        .data()
        .find((c) => c.id === data.id.slice(1));
      if (!category) return null;

      const categoryName = category.name.toLowerCase();

      const match =
        categoryName.includes(term) ||
        category.dishes.some((d) => {
          return d.name.toLowerCase().includes(term);
        });
      return match ? data : null;
    }
  }

  selectionTmp(state) {
    if (!state.id) {
      return state.text;
    }
    var $result = $('<span>').text(state.text);

    if (state.id[0] == 'c') {
      $result.addClass('dish-category-value');
    }
    return $result;
  }

  resultTmp(state) {
    if (!state.id) {
      return state.text;
    }

    var $result = $('<span>').text(state.text);

    if (state.id[0] == 'c') {
      $result.addClass('dish-category-option');
    } else {
      $result.addClass('dish-option');
    }

    return $result;
  }

  onChange(event) {
    var options = $(event.target).find('option').get();
    var value = $(event.target).val();

    if (value) {
      var selectedCategories = value.filter(function (v) {
        return v[0] == 'c';
      });
      var selectedOptions = value.filter(function (v) {
        return v[0] != 'c';
      });

      options.forEach(function (option) {
        var optionValue = $(option).attr('value');
        if (!optionValue) return;

        var isCategory = optionValue[0] == 'c';

        if (!isCategory) {
          var categoryId = $(option).data('category');
          if (selectedCategories.includes('c' + categoryId)) {
            if (selectedOptions.includes(optionValue)) {
              var index = selectedOptions.indexOf(optionValue);
              selectedOptions.splice(index, 1);
            }
            $(option).attr('disabled', true);
          } else {
            $(option).attr('disabled', false);
          }
        }
      });

      var newValue = [].concat(
        _toConsumableArray(selectedCategories),
        _toConsumableArray(selectedOptions)
      );

      $(event.target).val(newValue).trigger('change.select2');
    }
  }
}
