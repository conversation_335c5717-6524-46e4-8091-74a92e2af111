.foquz-dishes-select {

}

.select2 {
  .dish-category-value, .category-value {
    color: #37a74a;
    font-weight: 500;
  }
}

.select2-container .select2-dropdown {
  .dish-option {
    margin-left: 14px;
    color: #2e2f31;
    padding-left: 12px;
  }

  .dish-category-option,
  .category-option {
    color: #37a74a;
    font-weight: bold;
    display: flex;
  }

  .select2-results__option[aria-selected="true"]  {
    .dish-category-option, .category-option {
      color: #abefb7 !important;
    }
  }

  .select2-results__option[aria-disabled='true'] & {
    .dish-option {
      color: #73808d;
    }

  }
}
