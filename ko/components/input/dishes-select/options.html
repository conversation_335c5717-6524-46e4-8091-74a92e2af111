<option></option>
<!-- ko foreach: { data : $data.categories, as: 'category'} -->
<option data-bind="attr: { value: 'c' + category.id, 'data-category': category.id }, text: category.name"></option>
<!-- ko foreach: { data: category.dishes, as: 'dish' } -->
<option data-dish
        data-bind="attr: { value: 'd' + dish.id, 'data-category': category.id }, text: dish.name"></option>
<!-- /ko -->
<!-- /ko -->
