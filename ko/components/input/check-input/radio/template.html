<label class="check-input__wrapper" data-bind="attr: { 'for': id }, click: onLabelClick">
  <input type="radio" class="check-input__input"
  data-bind="
    element: input,
    checked: checked,
    value: value,
    attr: {
      id: id,
      name: name,
    },
    disable: disabled,
    event: events">

    <div class="check-input__icon"></div>
    <div class="check-input__label">
      <!-- ko template: { nodes: $componentTemplateNodes } -->
      <!-- /ko -->
    </div>

</div>
