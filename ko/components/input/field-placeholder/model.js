import { Translator } from '@/utils/translate';
const MainTranslator = Translator('main')

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.tooltip = params.tooltip || MainTranslator.t("Можно добавить подсказку внутри поля ввода, которая будет отображаться пока ничего не введено");
    this.limit = params.limit || 125;
    this.placeholder = params.placeholder;
    this.disabled = params.disabled;
  }

  dispose() {
    this.subscriptions.forEach(s => s.dispose());
  }
}
