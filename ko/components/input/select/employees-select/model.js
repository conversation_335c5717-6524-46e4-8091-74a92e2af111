import * as Parent from '../model';

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
  }

  get url() {
    return `${APIConfig.baseApiUrlPath}dictionaries/employees?all=1&access-token=${APIConfig.apiKey}`;
  }

  get placeholder() {
    return this.config.placeholder || 'Выберите сотрудника';
  }

  _getItems(data, params = {}) {
    let items = this._filterDeleted(data.items).map((i) => {
      return {
        ...i,
        text: i.name
      };
    });
    return this._filterByTerm(items, params.term);
  }
}
