<!-- ko descendantsComplete: $component.onRender.bind($component) -->
<!-- ko if: multiple -->
<select data-bind="
  element: select,
  selectedOptions: value,
  valueAllowUnset: true,
  disable: disabled,
  lazySelect2: getSelectConfig()
" multiple>
  <option></option>

  <!-- ko template: { nodes: $componentTemplateNodes } -->
  <!-- /ko -->
</select>
<!-- /ko -->

<!-- ko ifnot: multiple -->

<select data-bind="
  element: select,
  value: value,
  valueAllowUnset: true,
  disable: disabled,
  lazySelect2: getSelectConfig()
">
  <option></option>

  <!-- ko template: { nodes: $componentTemplateNodes } -->
  <!-- /ko -->
</select>
<!-- /ko -->
<!-- /ko -->
