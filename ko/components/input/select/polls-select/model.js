import * as Parent from '../model';

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
    this.params = params
    this.isAuto = 'isAuto' in params ? params.isAuto : null;
    this.companyId = 'companyId' in params ? params.companyId : null;
    this.filter = params.filter;
    this.company = ko.observable(null)
    if (this.companyId) {
      this.companyId.subscribe((v) => {
       this.getSelectConfig()
      });
    }
  }

  get url() {
    if (this.params.companyId) {
      return `${APIConfig.baseApiUrlPath}poll?access-token=${APIConfig.apiKey}&isAuto=${this.isAuto ? 1 : 0}&companyId=${this.params.companyId()}`;
    }
    if (this.isAuto == null) return `${APIConfig.baseApiUrlPath}poll?access-token=${APIConfig.apiKey}`;
    return `${APIConfig.baseApiUrlPath}poll?access-token=${APIConfig.apiKey}&isAuto=${this.isAuto ? 1 : 0}`;
  }

  get placeholder() {
    return this.params.placeholder || _t('Все опросы');
  }

  _getItems(data, params = {}) {
    let items = this._filterDeleted(data.items).map((i) => {
      return {
        ...i,
        text: i.name
      };
    });
    console.log('items', items, this.filter)
    if (typeof this.filter == 'function') {
      items = items.filter(i => this.filter(i))
    }
    return this._filterByTerm(items, params.term);
  }

  getSelectConfig() {
    return {
      ...super.getSelectConfig(),
      dropdownAutoWidth: false,
    }
  }
}
