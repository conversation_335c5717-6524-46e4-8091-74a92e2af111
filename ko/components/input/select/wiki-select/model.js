import * as Parent from '../model';

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
  }

  get url() {
    return `${APIConfig.baseApiUrlPath}wiki`;
  }

  get placeholder() {
    return 'Выберите родительскую страницу';
  }

  _getItems(data, params = {}) {
    let items = [];

    let handleItem = (item, level) => {
      items.push({
        ...item,
        text: item.title,
        level: level
      });

      item.childPages.forEach((c) => {
        handleItem(c, level + 1);
      });
    };

    data.items.forEach((i) => handleItem(i, 0));

    return this._filterByTerm(items, params.term);
  }

  get wrapperCssClass() {
    return 'wiki-select2';
  }

  getSelectConfig() {
    return {
      ...super.getSelectConfig(),
      dropdownAutoWidth: false
    };
  }

  _getResultItem(repo) {
    let padding = repo.level * 20;
    return $('<span></span>')
      .attr('data-level', repo.level)
      .css('padding-left', padding + 'px')
      .text(repo.text);
  }
}
