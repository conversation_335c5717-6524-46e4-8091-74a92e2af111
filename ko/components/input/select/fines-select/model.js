import * as Parent from "../model";

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
  }

  get url() {
    return `${APIConfig.baseApiUrlPath}dictionaries/fines/fine-categories?access-token=${APIConfig.apiKey}`;
  }

  get placeholder() {
    return this.config.placeholder || "Выберите нарушение";
  }

  _getItems(data, params = {}) {


    let items = data.items
      .map((group) => {
        let children = this._filterDeleted(group.items).map((item) => {
          return {
            ...item,
            id: `${item.id}`,
            text: item.title,
          };
        });

        return {
          name: group.name,
          text: group.name,
          children: this._filterByTerm(children, params.term),
        };
      })
      .filter((group) => group.children.length);

    return items;
  }
}
