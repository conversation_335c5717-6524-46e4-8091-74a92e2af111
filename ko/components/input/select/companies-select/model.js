import * as Parent from '../model';
import { ApiUrl } from 'Utils/url/api-url';

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
  }

  get url() {
    return ApiUrl('company/get-list', { sort: 'name', page: 1});
  }

  get placeholder() {
    return 'Все';
  }

  _getItems(data, params = {}) {
    let items = this._filterDeleted(data).map((i) => {
      return {
        ...i,
        text: i.name
      };
    });
    return this._filterByTerm(items, params.term);
  }
}
