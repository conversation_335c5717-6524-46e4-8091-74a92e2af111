import { get as _get } from 'lodash';

import * as Parent from '../model';

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
    this.withoutConditionOnly = params.withoutConditionOnly;
    this.companyId = params.companyId;
    this._placeholder = params.placeholder || 'Все';
    this.statsLinkId = _get(window, 'POLL.statsLink.link', '').replace(/.*stats\/(\w*)/, '$1');
  }

  get url() {
    if (this.companyId) {
      return `${APIConfig.baseApiUrlPath}poll/tags?company_id=${this.companyId}${this.statsLinkId ? `&link=${this.statsLinkId}` : ''}&all=1&access-token=${APIConfig.apiKey}`;
    }
    return `${APIConfig.baseApiUrlPath}contact-tags?all=1&access-token=${APIConfig.apiKey}`;
  }

  get placeholder() {
    return this._placeholder;
  }

  get wrapperCssClass() {
    return 'tags-dropdown-wrapper';
  }

  get dropdownCssClass() {
    return 'tags-dropdown';
  }

  get multiple() {
    return true;
  }

  getSelectConfig() {
    let config = {
      ...super.getSelectConfig(),
      containerCss: { 'min-width': '40px' }
    };

    return config;
  }

  _getItems(data, params = {}) {
    let tags = data.items.map((t) => {
      return {
        ...t,
        text: t.name
      };
    });
    tags = this._filterByTerm(tags, params.term);

    const withoutConditionTags = tags.filter((t) => !t.isAuto);
    withoutConditionTags.sort((a, b) => {
      return a.name < b.name ? -1 : 1;
    });

    let items = [];

    if (this.withoutConditionOnly) {
      items = withoutConditionTags;
    } else {
      const withConditionTags = tags.filter((t) => t.isAuto);
      withConditionTags.sort((a, b) => (a.name < b.name ? -1 : 1));

      if (withConditionTags.length) {
        items.push({
          name: 'with-condition',
          text: 'С условием',
          children: withConditionTags
        });
      }
      if (withoutConditionTags.length) {
        items.push({
          name: 'without-condition',
          text: this.withoutConditionOnly ? undefined : 'Без условия',
          children: withoutConditionTags
        });
      }
    }
      
    return items;
  }

  _getResultItem(repo) {
    let item = $('<span></span>').text(repo.text);

    if (repo.children) {
      if (repo.name == 'with-condition') {
        item.addClass('f-color-success');
      } else {
        item.addClass('f-color-service');
      }
    } else {
      item.addClass('f-color-text');
    }

    return item;
  }

  templateSelection(repo) {
    return $('<span></span>').text(repo.text);
  }
}
