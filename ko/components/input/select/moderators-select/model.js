import * as Parent from '../model';
import { UserOption } from '../utils/user-option';

export class ViewModel extends Parent.ViewModel {
  constructor(params, element) {
    super(params, element);
  }

  get placeholder() {
    return this.params.placeholder || _t('answers', 'Все модераторы');
  }

  _getItems(data, params = {}) {
    let items = this._filterDeleted(data.items)
      .map((i) => {
        return {
          ...i,
          text: i.name,
        };
      });
    return this._filterByTerm(items, params.term);
  }

  _getResultItem(repo) {
    let text = repo.fixed ? _t('Вы') : repo.text;
    let avatar = repo.avatar;


    return UserOption({ text, avatar });
  }
}
