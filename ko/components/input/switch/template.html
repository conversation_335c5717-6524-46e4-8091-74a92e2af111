<div class="switch-form-group">
  <label class="switch form-control"
         data-bind="event: {
    click: function() { return onClick(); }
  }">
    <input type="checkbox"
           data-bind="checked: checked, attr: { id: unique }, disable: disabled">
    <span class="switch__slider"></span>
  </label>
  <label class="form-label"
         data-bind="css: {
    'form-label_checked': checked,
    'form-label_disabled': disabled
    },
    attr: { 'for': unique },
    event: {
      click: function() {
        return onClick()
      }
    }">

    <!-- ko template: { nodes: $componentTemplateNodes } -->
    <!-- /ko -->
  </label>

</div>
