<!-- ko ifnot: withoutIcon -->
<div class="f-input-group__prepend">
  <foquz-icon params="icon: 'search'"></foquz-icon>
</div>
<!-- /ko -->

<!-- ko if: allowClear && value() -->
<div class="f-input-group__append cursor-pointer"
     data-bind="click: clear">
  <foquz-icon params="icon: 'times'"
              class="f-icon-sm f-icon-danger"></foquz-icon>
</div>
<!-- /ko -->

<input type="text"
       class="form-control"
       data-bind="textInput: value, attr: { placeholder: placeholder }, onEnter: onEnter" />
