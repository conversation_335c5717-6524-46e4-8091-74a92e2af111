import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('search-field', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      $element.addClass('f-input-group foquz-search-field');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
