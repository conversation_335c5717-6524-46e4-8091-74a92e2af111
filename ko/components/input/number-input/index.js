import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('foquz-number-input', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      $element.addClass(['foquz-number-input']);

      return new ViewModel(params, element);
    },
  },
  template: html,
});
