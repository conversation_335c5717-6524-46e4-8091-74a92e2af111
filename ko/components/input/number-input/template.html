<!-- ko descendantsComplete: $component.onRender.bind($component) -->
<button type="button"
        class="foquz-number-input__control foquz-number-input__control--decrement"
        data-bind="
    click: decrementValue,
    css: {
      'disabled': isDisabled() || disableDecrement()
    }"></button>

<input class="foquz-number-input__field"
       value="1"
       placeholder="0"
       data-bind="
    autosizeInput: input,
    intervalInput: { max: max, min: min },
    textInput: value,
    disable: isDisabled,
    event: {
        blur: function() {
          value(numberValue());
        }
    }">

<button type="button"
        class="foquz-number-input__control foquz-number-input__control--increment"
        data-bind="
    click: incrementValue,
    css: {
      'disabled': isDisabled() || disableIncrement()
    }"></button>
<!-- /ko -->
