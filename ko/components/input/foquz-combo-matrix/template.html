<!-- ko let: {
  state: ko.observable('closed'),
  component: $component,
  _f: filteredVariants(),
} -->
<div
  class="foquz-combo__wrapper"
  data-bind="
    dropdown,
    dropdownMode: 'select',
    dropdownState: state,
    ref: dropdownRef
  "
>
  <div class="foquz-combo__select form-control" data-dropdown-target>
    <div>
      <!-- ko if: checkedVariants().length === 0 -->
      <div class="placeholder" data-bind="text: component.translation && component.translation().placeholder || placeholder"></div>
      <!-- /ko -->
      <!-- ko ifnot: checkedVariants().length === 0 -->
      <!-- ko foreach: checkedVariants -->
      <div data-bind="text: component.translation && component.translation().cols[component.variants.findIndex(el => el.text === $data.text)] || text"></div>
      <!-- /ko -->
      <!-- /ko -->
      <div class="foquz-combo__opener">
        <foquz-icon
          class="f-transform--transition"
          params="icon: 'arrow-bottom'"
          data-bind="
            css: {
              'f-transform-rotate-180': state() == 'opened',
            }
          "
        ></foquz-icon>
      </div>
    </div>
  </div>

  <template>
    <div class="foquz-combo__dropdown foquz-combo-dropdown" data-bind="let: { q: ko.observable('') }">
      <search-field
        class="foquz-combo__search"
        params="
          value: q,
          withoutIcon: true,
          placeholder: '',
          allowClear: false,
        "
      ></search-field>
      <div class="foquz-combo__variants" data-bind="nativeScrollbar">
        <!-- ko let: {
          vars: filter(q()),
        } -->
        <!-- ko ifnot: vars.length -->
        <div class="foquz-combo__empty" data-bind="text: translator.t('Совпадений не найдено')"></div>
        <!-- /ko -->
        <!-- ko foreach: {data: vars, as: 'variant'} -->
        <div class="foquz-combo__variant">
          <!-- ko if: component.multiple -->
          <foquz-checkbox
            params="
              checked: variant.isChecked,
              value: variant.id,
              disabled: component.blocked() && !variant.isChecked(),
              event: {
                change: function(_, e) {
                  component.toggleVariant(variant, e.target.checked);
                }
              },
              mode: 'fill',
            "
          >
            <div data-bind="text: component.translation && component.translation().cols[$index()] || variant.text"></div>
          </foquz-checkbox>
          <!-- /ko -->
          <!-- ko ifnot: component.multiple -->
          <foquz-radio
            params="
              checked: variant.isChecked() ? variant.id : false,
              value: variant.id,
              name: component.name,
              event: {
                change: function(_, e) {
                  component.toggleVariant(variant, e.target.checked);
                },
              },
              mode: 'fill',
            "
          >
            <div data-bind="text: component.translation && component.translation().cols[$index()] || variant.text"></div>
          </foquz-radio>
          <!-- /ko -->
        </div>
        <!-- /ko -->
        <!-- /ko -->
      </div>

      <footer class="foquz-combo__actions">
        <button
          type="button"
          class="foquz-combo__action foquz-combo__action--reset"
          data-dropdown-close
          data-bind="
            click: function() {
              component.reset();
              return true;
            },
          "
        >
          <foquz-icon params="icon: 'times'"></foquz-icon>
        </button>
        <button
          type="button"
          class="foquz-combo__action foquz-combo__action--apply"
          data-dropdown-close
        >
          <foquz-icon params="icon: 'check'"></foquz-icon>
        </button>
      </footer>
    </div>
  </template>
</div>
<!-- /ko -->
