@import 'Style/breakpoints';

.foquz-combo-matrix {
  display: block;

  &__select {
    position: relative;
    padding-right: 50px;
    height: auto!important;
    min-height: 48px;
    padding-top: 12px!important;
    padding-bottom: 12px!important;
    cursor: pointer;
  }

  &__opener {
    position: absolute;
    right: 10px;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
  }

  &__dropdown {
    max-height: 250px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__empty {
    text-align: center;
    padding: 8px;
    font-size: 14px;
    color: #999;
  }

  &__actions {
    display: none;
  }

  &__variants {
    flex-grow: 1;
    max-height: calc(250px - 68px);
  }

  &__variant {
    padding-top: 8px;
    padding-bottom: 8px;
    padding-right: 15px;
    padding-left: 15px;

    &:hover {
      background: #F1F5F6;
    }

    .f-check-input {
      & + .f-check-label:before {
        border-radius: 3px;
      }
    }

    .f-check-label {
      display: block;
    }
  }

  &__search {
    height: 36px;
    margin-bottom: 16px;
  }

  &__actions {
    margin-left: -5px;
    margin-right: -5px;
    flex-shrink: 0;
    padding-left: 15px;
  }

  &__action {
    height: 36px;
    flex-grow: 1;
    margin: 0 5px;
    border: 2px solid ;
    box-sizing: border-box;
    border-radius: 100px;
    background-color: transparent;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;




    .f-icon {
      color: inherit;
    }

    &--reset {
      color: #f96261
    }

    &--apply {
      color:  #00C968;
    }
  }

  &.invalid {
    .foquz-combo__select {
      border-color:  #f96261;
    }
  }

}

.foquz-combo__select {
  .placeholder {
    color: #A6B1BC;
  }
}

.placeholder {
  color: #A6B1BC;
}

.foquz-combo__dropdown {
  margin-left: -15px;

  .tippy-content {
    padding: 0;
  }

  .foquz-combo__search {
    padding-left: 15px;
  }

  .only-mobile({
    height: 100%;
    max-height: none;

    .foquz-combo__variants {
      max-height: none;
    }

    .os-scrollbar {
      opacity: 0;
    }

    .foquz-combo__variant {


      &:hover {
        background: transparent;
      }
    }

    .foquz-combo__actions {
      margin-top: 16px;
      display: flex;
    }
  });
}
