import { FoquzComponent } from "Models/foquz-component";
import { Translator } from "@/utils/translate";

let unique = 1;

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.name = "foquz-combo-matrix-" + unique++;
    this.translator = Translator("main");

    this.opened = ko.observable(false);

    this.multiple = params.multiple;
    this.query = ko.observable("");
    this.variants = params.variants.map(el => ({ text: el, isChecked: ko.observable(params.value && params.value() == el) }));
    this.value = params.value;

    this.blocked = params.blocked;

    this.translation = params.translation;
    this.rowIndex = params.rowIndex;

    this.filteredVariants = ko.pureComputed(() => {
      let q = this.query();
      if (!q) {
        return [...this.variants];
      }
      q = q.toLowerCase();
      let filtered = this.variants.filter((v) =>
        ko.toJS(v.text).toLowerCase().includes(q)
      );
      return filtered;
    });

    this.checkedVariants = ko.computed(() => {
      return this.variants.filter((v) => {
        return v.isChecked();
      });
    });

    this.customVariantChecked = ko.computed(() => {
      return (
        this.customVariant &&
        this.checkedVariants().includes(this.customVariant.id)
      );
    });

    window.getVariants = () => ko.toJS(this.variants);

    this.max = params.max || Infinity;

    this.dropdownRef = ko.observable(null);
    this.placeholder = params.placeholder;

    this.skip = params.skip;
    this.skip.subscribe(v => {
      if (v) {
        this.reset();
      }
    })

    this.checkedVariants.subscribe((v) => {
      if (!v || !v.length) {
        return;
      }
      this.value(v.map(el => el.text));
    });
  }

  filter(q) {
    q = q || "";
    if (!q) {
      return [...this.variants];
    }
    q = q.toLowerCase();
    let filtered = this.variants.filter((v) =>
    ko.toJS(v.text).toLowerCase().includes(q)
    );
    return filtered;
  }

  toggleVariant(variant, isChecked) {
    if (this.multiple) {
      variant.isChecked(isChecked);
      setTimeout(() => {
        this.updatePosition();
      }, 10);
    } else {
      if (!isChecked) return;
      this.dropdownRef().close();
      this.variants.forEach((v) => {
        v.isChecked(v == variant);
      });
    }
  }

  updatePosition() {
    if (this.dropdownRef()) {
      this.dropdownRef().update();
    }
  }

  reset() {
    this.variants.forEach((v) => v.isChecked(false));
  }
}
