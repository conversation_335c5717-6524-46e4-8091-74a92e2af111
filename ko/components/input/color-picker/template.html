<div class="input-group" data-bind="colorPicker: { hexOnly: isHex, colorFormat: colorFormat }">
  <div class="input-group-prepend poll-design__color-preview js-color-preview"></div>
  <input required="" class="form-control colorpicker-anchor" data-bind="element: anchor, textInput: value, disable: disabled, attr: {
    'data-is-hex': isHex,
    'data-color-format': colorFormat
  }" maxlength="7" style="opacity: 0; position: absolute; right: 0px; width: 50%; z-index: 3;">
  <input class="form-control hidden-input" data-bind="attr: {
    'data-color-format': colorFormat
  }">
</div>
