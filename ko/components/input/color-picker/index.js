import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Bindings/input/color-picker';

ko.components.register('color-picker', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('color-picker');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
