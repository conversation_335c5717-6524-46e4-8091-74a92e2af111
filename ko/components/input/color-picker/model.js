import { dispatchEvent } from "@/utils/dispatch-event";

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.value = params.value;
    this.disabled = params.disabled;
    this.isHex = "opacity" in params ? !params.opacity : true;
    this.anchor = ko.observable(null);
    this.colorFormat = params.colorFormat || 'rgb'

    if (ko.isObservable(this.value)) {
      this.value.subscribe((v) => {
        dispatchEvent(this.anchor(), "redraw");
      });
    }
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
