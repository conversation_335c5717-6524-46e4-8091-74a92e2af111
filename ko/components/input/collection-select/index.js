import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('collection-select', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('collection-select');
      element.classList.add('select2-wrapper');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
