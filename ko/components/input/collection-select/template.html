<!-- ko descendantsComplete: $component.onRender.bind($component) -->

<!-- ko component: {
    name: 'select2',
    params: {
          ref: selectRef,
          value: value,
          except: except,
          exceptText: exceptText,
          exceptCount: exceptCount,
          multiple: multiple,
          dense: dense,
          mode: mode,
          disabled: disabled,
          allowUnset: allowUnset,
          placeholder: placeholder,
          selectConfig: config,
          search: search,
          allowClear: allowClear,
          emptyOption: emptyOption
    }
} -->

<!-- /ko -->
<!-- /ko -->
