# Селект для коллекции данных

```
<collection-select
    data-bind="css: {
      'is-invalid': ko.observable(false)
    }"
    params="
        ref: ko.observable(null),
        multiple: false,
        value: ko.observable(''),
        allowUnset: true,
        dense: true,
        collection: <DataCollection>,
        placeholder: 'Все',
        config: {
          wrapperCssClass: 'select2-container--form-control',
        }"></collection-select>

<collection-select
    data-bind="css: {
      'is-invalid': ko.observable(false)
    }"
    params="
        ref: ko.observable(null),
        multiple: true,
        value: ko.observableArray([]),
        allowUnset: true,
        dense: true,
        collection: <DataCollection>,
        placeholder: 'Все',
        config: {
          wrapperCssClass: 'select2-container--form-control',
        }"></collection-select>
```

## Параметры

* `ref` - ссылка на компонент
* `multiple`
* `value`
* `allowUnset` - значение параметра `valueAllowUnset`
* `dense` - влияет на классы элементов
* `collection` - объект класса DataCollection
* `placeholder`
* `config` - дополнительные настройки для select2
