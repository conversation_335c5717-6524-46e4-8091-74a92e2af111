<div class="foquz-dnd-cover__container">
  <div class="foquz-dnd-cover__content">
    <!-- ko if: type == 'image' -->
    <div class="foquz-dnd-cover__icon">
      <svg-icon params="name: 'image-light', width: 96, height: 96"></svg-icon>
    
  </div>

  <div class="foquz-dnd-cover__text">
    <div data-bind="text: text">
    </div>
    <div class="foquz-dnd-cover__limit">Максимальный размер файла — 5 Мб</div>
  </div>
    <!-- /ko -->

    <!-- ko if: type == 'video' -->
    <div class="foquz-dnd-cover__icon">
    <svg width="96"
         height="96">
      <use href="#video-file-icon"></use>
    </svg>
  </div>
  <div class="foquz-dnd-cover__text">
    <div  data-bind="text: text">

    </div>
    <div class="foquz-dnd-cover__limit">Максимальный размер файла — 10 Мб</div>
  </div>
    <!-- /ko -->

    <!-- ko if: type == 'mix' -->
    <div class="foquz-dnd-cover__icon">
    <svg width="68"
         height="54">
      <use href="#image-video-file-icon"></use>
    </svg>
  </div>
  <div class="foquz-dnd-cover__text">
    <div  data-bind="text: text">

    </div>
    <div class="foquz-dnd-cover__limit">Максимальный размер изображения - 5 мб</div>
    <div class="foquz-dnd-cover__limit">Максимальный размер видео - 10 мб</div>

  </div>
    <!-- /ko -->


    <!-- ko ifnot: type -->
    <!-- ko template: { nodes: $componentTemplateNodes } -->
    <!-- /ko -->
    <!-- /ko -->
  </div>
</div>
