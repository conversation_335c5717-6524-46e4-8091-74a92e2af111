<!-- ko template: {
  foreach: templateIf(type(), $data),
  afterAdd: fadeAfterAddFactory(400),
} -->
<div>
  <div class="form-error">
    <!-- ko if: type() == 'limit' -->
    <span data-bind="html: text"></span>
    <!-- /ko -->

    <!-- ko if: type() == 'format' -->

    <span data-bind="text: _t('Неверный формат файла')"></span>
    <br>
    <span data-bind="html: text"></span>
    <!-- /ko -->
  </div>
</div>
<!-- /ko -->
