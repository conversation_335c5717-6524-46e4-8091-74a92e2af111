<!-- ko if: loading -->
<div class="file-loader-preview__loader">
  <i class="fa fa-spinner fa-pulse fa-2x f-color-primary"></i>
</div>
<!-- /ko -->

<!-- ko ifnot: loading -->

<!-- ko if: preview -->
<div class="file-loader-preview__bg"
     data-bind="
      style: {
        'background-image': backgroundImage() !== 'url(/img/audio-file-back.png)' && !backgroundImage().includes('.mp3') && !backgroundImage().includes('.m4a') && !backgroundImage().includes('.vma') && !backgroundImage().includes('.ogg') ? backgroundImage() : '',
      },
      fancyboxGalleryItem: {
        gallery: gallery,
        index: index
      }"
     alt="">
     <!-- ko if: video -->
      <div class="file-loader-preview__bg-overlay">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 49 48" fill="none">
          <path d="M1.5 7C1.5 3.68629 4.18629 1 7.5 1H41.5C44.8137 1 47.5 3.68629 47.5 7V41C47.5 44.3137 44.8137 47 41.5 47H7.5C4.18629 47 1.5 44.3137 1.5 41V7Z" stroke="white" stroke-width="2"/>
          <path d="M32.1939 26.639C33.9354 25.6752 33.9354 23.2659 32.1939 22.3022L21.4184 16.3392C19.6769 15.3755 17.5 16.5801 17.5 18.5076V30.4336C17.5 32.3611 19.6769 33.5657 21.4184 32.602L32.1939 26.639Z" stroke="white" stroke-width="2"/>
        </svg>
      </div>
      <!-- /ko -->
    </div>
    <!-- ko if: preview && (backgroundImage() == "url(/img/audio-file-back.png)" || backgroundImage().includes('.mp3') || backgroundImage().includes('.wma') || backgroundImage().includes('.ogg') || backgroundImage().includes('.m4a')) -->
    <div class="file-loader-preview__bg-overlay_audio" >
      <svg xmlns="http://www.w3.org/2000/svg" width="49" height="49" viewBox="0 0 49 49" fill="none">
        <path d="M18.5 16.1667L18.3356 15.1803C17.8534 15.2606 17.5 15.6778 17.5 16.1667H18.5ZM34.5 13.5H35.5C35.5 13.206 35.3707 12.927 35.1464 12.737C34.9221 12.547 34.6256 12.4653 34.3356 12.5136L34.5 13.5ZM7.5 2.5H41.5V0.5H7.5V2.5ZM46.5 7.5V41.5H48.5V7.5H46.5ZM41.5 46.5H7.5V48.5H41.5V46.5ZM2.5 41.5V7.5H0.5V41.5H2.5ZM7.5 46.5C4.73858 46.5 2.5 44.2614 2.5 41.5H0.5C0.5 45.366 3.63401 48.5 7.5 48.5V46.5ZM46.5 41.5C46.5 44.2614 44.2614 46.5 41.5 46.5V48.5C45.366 48.5 48.5 45.366 48.5 41.5H46.5ZM41.5 2.5C44.2614 2.5 46.5 4.73858 46.5 7.5H48.5C48.5 3.63401 45.366 0.5 41.5 0.5V2.5ZM7.5 0.5C3.63401 0.5 0.5 3.63401 0.5 7.5H2.5C2.5 4.73858 4.73858 2.5 7.5 2.5V0.5ZM19.5 33.5V16.1667H17.5V33.5H19.5ZM18.6644 17.1531L34.6644 14.4864L34.3356 12.5136L18.3356 15.1803L18.6644 17.1531ZM33.5 13.5V30.8333H35.5V13.5H33.5ZM17.5 33.5C17.5 35.1569 16.1569 36.5 14.5 36.5V38.5C17.2614 38.5 19.5 36.2614 19.5 33.5H17.5ZM14.5 36.5C12.8431 36.5 11.5 35.1569 11.5 33.5H9.5C9.5 36.2614 11.7386 38.5 14.5 38.5V36.5ZM11.5 33.5C11.5 31.8431 12.8431 30.5 14.5 30.5V28.5C11.7386 28.5 9.5 30.7386 9.5 33.5H11.5ZM14.5 30.5C16.1569 30.5 17.5 31.8431 17.5 33.5H19.5C19.5 30.7386 17.2614 28.5 14.5 28.5V30.5ZM33.5 30.8333C33.5 32.4902 32.1569 33.8333 30.5 33.8333V35.8333C33.2614 35.8333 35.5 33.5948 35.5 30.8333H33.5ZM30.5 33.8333C28.8431 33.8333 27.5 32.4902 27.5 30.8333H25.5C25.5 33.5948 27.7386 35.8333 30.5 35.8333V33.8333ZM27.5 30.8333C27.5 29.1765 28.8431 27.8333 30.5 27.8333V25.8333C27.7386 25.8333 25.5 28.0719 25.5 30.8333H27.5ZM30.5 27.8333C32.1569 27.8333 33.5 29.1765 33.5 30.8333H35.5C35.5 28.0719 33.2614 25.8333 30.5 25.8333V27.8333Z" fill="#A6B1BC"/>
      </svg>
    </div>
    <!-- /ko -->

<!-- ko ifnot: disabled -->

<button class="file-loader-preview__remove"
        data-bind="click: remove, tooltip, tooltipText: _t('Удалить')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M10 20.5C15.5228 20.5 20 16.0228 20 10.5C20 4.97715 15.5228 0.5 10 0.5C4.47715 0.5 0 4.97715 0 10.5C0 16.0228 4.47715 20.5 10 20.5Z" fill="black"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M8.35275 10.2979L6.7105 8.65564C6.34405 8.28919 6.34405 7.69201 6.7105 7.32555C7.07695 6.9591 7.67414 6.9591 8.04059 7.32555L9.68284 8.96781L11.4065 7.24412C11.8544 6.79623 12.5602 6.79623 13.0081 7.24412C13.456 7.69201 13.456 8.39777 13.0081 8.84565L11.2844 10.5693L12.9266 12.2116C13.2931 12.578 13.2931 13.1752 12.9266 13.5417C12.5602 13.9081 11.963 13.9081 11.5965 13.5417L9.95429 11.8994L8.2306 13.6231C7.78272 14.071 7.07695 14.071 6.62907 13.6231C6.18118 13.1752 6.18118 12.4695 6.62907 12.0216L8.35275 10.2979Z" fill="white"/>
        </svg>
</button>

<!-- /ko -->
<!-- /ko -->


<div class="file-loader-preview__content">
  <!-- ko template: { nodes: $componentTemplateNodes } -->
  <!-- /ko -->
</div>


<!-- /ko -->
