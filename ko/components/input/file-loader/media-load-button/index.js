import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('media-load-button', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      $element.addClass('media-load-button');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
