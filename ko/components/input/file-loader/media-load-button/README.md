# Кнопка загрузки файлов с превью

![](./screenshots/1.png)
![](./screenshots/2.png)
## В верстке

```
<media-load-button params="loader: loader, gallery: [], index: ko.observable(1)">
  <foquz-icon params="icon: 'upload'"></foquz-icon>
  добавить<br> фоновое изображение
</media-load-button>

<media-load-button
  params="loading: ko.observable(true),
          preview: ko.observable(''),
          remove: function() {},
          load: function() {}">
  <foquz-icon params="icon: 'upload'"></foquz-icon>
  добавить<br> фоновое изображение
</media-load-button>
```

## Параметры

* `loader` - объект класса `FoquzFileLoader`

  * `isPreviewLoading`
  * `preview`
  * `remove`
  * `open`
  * `error`
