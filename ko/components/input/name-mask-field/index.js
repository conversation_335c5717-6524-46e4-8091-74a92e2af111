import { ViewModel } from './model';
import html from './template.html';
import './style.less';
import 'Components/input/text-field-config';

ko.components.register('name-mask-field', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('name-mask-field');
      return new ViewModel(params, element);
    }
  },
  template: html
});
