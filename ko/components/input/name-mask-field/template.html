<!-- ko let: { inputId: 'name-mask-field-' + unique }-->

<div class="d-flex align-items-center justify-content-between">
  <div class="f-check">
    <input type="checkbox"
           class="f-check-input"
           data-bind="checked: controller.visible, attr: { id: inputId }, disable: disabled">
    <label class="f-check-label"
           data-bind="
              attr: { 'for': inputId },
              css: { 'color-service': !controller.visible() }">
      <!-- ko text: controller.label -->
      <!-- /ko -->
      <button class="btn-question"
              tabindex="10"
              data-bind="tooltip, tooltipPlacement: 'top'"
              type="button"
              data-original-title="Для поля ввода с маской ФИО можно настроить поля: Фамилия, Имя, Отчество"></button>
    </label>
  </div>

  <!-- ko template: {
            foreach: templateIf(controller.visible(), $data),
            afterAdd: fadeAfterAddFactory(200),
            beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
  <switch class="mb-0" params="checked: controller.required, disabled: disabled">Обязательный</switch>
  <!-- /ko -->
</div>

<!-- /ko -->

<!-- ko template: {
    foreach: templateIf(controller.visible(), $data),
    afterAdd: slideAfterAddFactory(200),
    beforeRemove: slideBeforeRemoveFactory(200)
  } -->
<text-field-config class="mt-4 compact"
                   params="field: controller.field, disabled: disabled"></text-field-config>
<!-- /ko -->
