<div class="input-group inner-input-group password-inner-input-group">
  <input
    class="form-control"
    data-bind="
      textInput: value,
      attr: { id: id, ...attrs},
      hasFocus: isSelected,
    "
  />
  <button
    class="btn btn-icon btn-icon--simple inner-input-group__suffix password-inner-input-group__visibility-toggle-icon-button-suffix"
    data-bind="
      click: () => visible(!visible()),
      css: {
          'password-inner-input-group__show-password-icon-button-suffix': visible(),
          'password-inner-input-group__hide-password-icon-button-suffix': !visible()
      },
      fbPopper,
      title: tooltipText
    "
  ></button>
  <!-- ko if: help -->
  <fc-popper
    params="
        target: id,
        show: isSelected,
        options: {
            placement: 'top-start',
            arrowPosition: { placement: 'start', offset: 20 },
        },
        behavior: { control: true }
    "
    class="foquz-password-field__help"
  >
    <div>
      <div class="mb-10p">
        Пароль должен содержать:
      </div>
      <div class="d-flex align-center mb-10p">
        <fc-icon
          params="name: 'check', color: 'success', size: 12"
          data-bind="visible: hasMinLength()"
          class="mr-10p"
        ></fc-icon>
        <fc-icon
          params="name: 'minus', color: 'stroke', size: 12"
          data-bind="visible: !hasMinLength()"
          class="mr-10p"
        ></fc-icon>
        не менее 8 символов
      </div>
      <div class="d-flex align-center mb-10p">
        <fc-icon
          data-bind="visible: hasUppeLowerrCase()"
          params="name: 'check', color: 'success', size: 12"
          class="mr-10p"
        ></fc-icon>
        <fc-icon
          data-bind="visible: !hasUppeLowerrCase()"
          params="name: 'minus', color: 'stroke', size: 12"
          class="mr-10p"
        ></fc-icon>
        заглавные и строчные буквы (A-z)
      </div>
      <div class="d-flex align-center mb-10p">
        <fc-icon
          data-bind="visible: hasSimbol()"
          params="name: 'check', color: 'success', size: 12"
          class="mr-10p"
        ></fc-icon>
        <fc-icon
          data-bind="visible: !hasSimbol()"
          params="name: 'minus', color: 'stroke', size: 12"
          class="mr-10p"
        ></fc-icon>
        не менее одного спецсимвола (! # + % и т. п.)
      </div>
      <div class="d-flex align-center mb-10p">
        <fc-icon
          data-bind="visible: hasNum()"
          params="name: 'check', color: 'success', size: 12"
          class="mr-10p"
        ></fc-icon>
        <fc-icon
          data-bind="visible: !hasNum()"
          params="name: 'minus', color: 'stroke', size: 12"
          class="mr-10p"
        ></fc-icon>
        одной цифры (0-9)
      </div>
      <!-- ko if: hasSpace -->
      <div>
        Пробелы не допускаются.
      </div>
      <!-- /ko -->
    </div>
  </fc-popper>
  <!-- /ko -->
</div>

<!-- ko if: help && showErrorMsg() -->
<div class="form-error">
  <!-- ko if: !value() -->
    <div>Обязательное поле</div>
  <!-- /ko -->
  <!-- ko if: value() && !isSelected() -->
    <div class="mb-5p">Пароль должен содержать:</div>
    <ul style="list-style: disc; padding-left: 15px;">
        <li class="mb-5p">не менее 8 символов,</li>
        <li class="mb-5p">заглавные и строчные буквы (A-z),</li>
        <li class="mb-5p">не менее одного спецсимвола (! # + % и т. п.),</li>
        <li class="mb-5p">одной цифры (0-9),</li>
    </ul>
    <div>Пробелы не допускаются.</div>
  <!-- /ko -->
</div>
<!-- /ko -->