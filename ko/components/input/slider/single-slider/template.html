<div class="foquz-slider__slider">
    <div data-bind="slider, sliderMin: min, sliderMax: max, sliderValue: value, disabled: disabled, sliderStep: step, sliderMode:mode">
        <!-- ko if: withHandle -->
        <div class="ui-slider-handle foquz-slider__handle">
            <!-- ko if: withIndicator -->
            <span class="foquz-slider__handle-indicator"
                  data-bind="text: value() + valueSuffix">
      </span>
            <!-- /ko -->
        </div>
        <!-- /ko -->

    </div>
</div>

<!-- ko if: withValue -->
<div class="foquz-slider__value"
     data-bind="text: value() + valueSuffix"></div>
<!-- /ko -->
