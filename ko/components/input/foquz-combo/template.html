<!-- ko let: { state: ko.observable('closed'), component: $component, _f: filteredVariants() } -->
<div
  class="foquz-combo__wrapper"
  data-bind="
    dropdown,
    dropdownMode: 'select',
    dropdownState: state,
    ref: dropdownRef,
    css: {
      'foquz-combo__wrapper--valid': checkedVariants().length > 0,
    },
  ">
  <div
    class="foquz-combo__select form-control"
    data-dropdown-target
    data-bind="
      attr: {
        'data-reference': parentRef,
      },
    "
  >
    <div>
      <!-- ko if: checkedVariants().length === 0 -->
      <div class="placeholder" data-bind="text: placeholder"></div>
      <!-- /ko -->
      <!-- ko ifnot: checkedVariants().length === 0 -->
      <!-- ko foreach: checkedVariants -->
      <div class="d-flex align-items-center">
      <!-- ko if: component.fileEnabled && $data.file_url -->
      <div class="mr-15p" data-bind="css: {'mb-5p': $parent.checkedVariants().length - 1 !== $index()}">
        <file-loader-preview class="file-loader-preview file-loader-preview-selected-variant" data-bind="click: function (_, event) {
          event.stopPropagation();
        }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url">

      </file-loader-preview>
      </div>
      
      <!-- /ko -->
      <div
        class="foquz-combo__item-text"
        data-bind="
          css: {
            'foquz-combo__item-text--many': $parent.checkedVariants().length > 1,
            'foquz-combo__item-text-with-file': component.fileEnabled && $data.file_url
          },
          text: component.translation &&
            component.translation() &&
            component.translation()[id] ||
            text
        "
      ></div>
    </div>
      <!-- /ko -->
      <!-- /ko -->

      <div class="foquz-combo__opener" >
        <foquz-icon class="f-transform--transition" params="icon: 'arrow-bottom'" data-bind="css: {
          'f-transform-rotate-180': state() == 'opened',
        }"></foquz-icon>
      </div>
    </div>
  </div>


  <template>
    <div class="foquz-combo__dropdown foquz-combo-dropdown" data-bind="let: { q: ko.observable('') }">

      <search-field class="foquz-combo__search" params="value: q, withoutIcon: true, placeholder: '', allowClear: false">

      </search-field>


      <div class="foquz-combo__variants" data-bind="nativeScrollbar">
        <!-- ko let: {
          vars: filter(q())
        } -->
        <!-- ko ifnot: vars.length -->
        <div class="foquz-combo__empty" data-bind="text: translator.t('Совпадений не найдено')">
          
        </div>
        <!-- /ko -->

        <!-- ko foreach: {data: vars, as: 'variant'} -->
        <div class="foquz-combo__variant">

          <!-- ko if: component.multiple -->
          <foquz-checkbox
            data-bind="css: {'file-enabled':component.fileEnabled && variant.file_url}"
            params="checked: variant.isChecked,
            value: variant.id,
            disabled: component.blocked() && !variant.isChecked(),
            event: {
              change: function(_, e) {
                component.toggleVariant(variant, e.target.checked);
                //$(e.target).trigger('close');
              }
            },
            mode: 'fill'">
            <!-- ko if: component.fileEnabled && variant.file_url -->
            <div class="mr-15p file-loader-preview-wrapper">
              <file-loader-preview class="file-loader-preview" data-bind="click: function (_, event) {
                event.stopPropagation();
              }," params="loading: false, disabled: true, file: variant.file_url, preview: variant.preview_url,
              onRemove: function() { 
                  variant.file(null)
                  variant.value('')
              }">

            </file-loader-preview>
            </div>
            
            <!-- /ko -->
            <div
            class="variant-text"
              data-bind="
                text: component.translation && component.translation() && component.translation()[variant.id] ||
                  
                  variant.text,
              "
            ></div>
          </foquz-checkbox>
          <!-- /ko -->

          <!-- ko ifnot: component.multiple -->
          <foquz-radio data-bind="css: {'file-enabled':component.fileEnabled && variant.file_url}" params="checked: variant.isChecked() ? variant.id : false, value: variant.id,  name: component.name, event: {
            change: function(_, e) {
              component.toggleVariant(variant, e.target.checked);
              //$(e.target).trigger('close');
            }
          }, mode: 'fill'">
            <!-- ko if: component.fileEnabled && variant.file_url -->
            <div class="mr-15p file-loader-preview-wrapper">
              <file-loader-preview class="file-loader-preview" data-bind="click: function (_, event) {
                event.stopPropagation();
              }," params="loading: false, disabled: true, file: variant.file_url, preview: variant.preview_url,
              onRemove: function() { 
                  variant.file(null)
                  variant.value('')
              }">

            </file-loader-preview>
            </div>
            
            <!-- /ko -->
            <div class="variant-text" data-bind="text: component.translation && component.translation() && component.translation()[variant.id] || variant.text"></div>
          </foquz-radio>
          <!-- /ko -->

        </div>
        <!-- /ko -->
        <!-- /ko -->

      </div>

      <footer class="foquz-combo__actions">
        <button type="button" class="foquz-combo__action foquz-combo__action--reset" data-dropdown-close data-bind="click: function() {
          component.reset();
          return true;
        }">
          <foquz-icon params="icon: 'times'"></foquz-icon>
        </button>
        <button type="button" class="foquz-combo__action foquz-combo__action--apply" data-dropdown-close >
          <foquz-icon params="icon: 'check'"></foquz-icon>
        </button>
      </footer>
    </div>
  </template>

</div>
<!-- /ko -->
