import { FoquzComponent } from "Models/foquz-component";
import { Translator } from "@/utils/translate";

let unique = 1;

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.fileEnabled = params.fileEnabled
    this.selfVariantNothing = params.selfVariantNothing

    this.name = "foquz-combo-" + unique++;
    this.translator = Translator("main");

    this.opened = ko.observable(false);

    this.multiple = params.multiple;
    this.query = ko.observable("");
    this.variants = ko.isObservable(params.variants)
      ? params.variants()
      : params.variants;

    this.blocked = params.blocked;

    this.translation = params.translation;

    this.filteredVariants = ko.pureComputed(() => {
      let q = this.query();
      if (!q) {
        return [...this.variants];
      }
      q = q.toLowerCase();
      let filtered = this.variants.filter((v) =>
        ko.toJS(v.text).toLowerCase().includes(q)
      );
      return filtered;
    });


    this.checkedVariants = ko.computed(() => {
      return this.variants.filter((v) => {
        return v.isChecked();
      });
    });

    this.customVariantChecked = ko.computed(() => {
      return (
        this.customVariant &&
        this.checkedVariants().includes(this.customVariant.id)
      );
    });

    window.getVariants = () => ko.toJS(this.variants);

    this.max = params.max || Infinity;

    this.dropdownRef = ko.observable(null);
    this.placeholder = params.placeholder;
    this.parentRef = params.parentRef;
  }

  filter(q) {
    q = q || "";
    if (!q) {
      return [...this.variants];
    }
    q = q.toLowerCase();
    let filtered = this.variants.filter((v) =>
    ko.toJS(v.text).toLowerCase().includes(q)
    );
    return filtered;
  }

  toggleVariant(variant, isChecked) {

    if (variant.type === 1 || (variant.id == "is_self_answer" && this.selfVariantNothing)) {
      this.variants.filter(v => v.id !== variant.id).forEach((v) => {
        v.isChecked(false);
      });
    } else {
      if (this.variants.some(v => v.type === 1) || (this.variants.some(v => v.id == "is_self_answer") && this.selfVariantNothing)) {
        this.variants.filter(v => v.type === 1 || (v.id == "is_self_answer" && this.selfVariantNothing)).forEach((v) => {
          v.isChecked(false);
        });
      }
    }
    if (this.multiple) {
      variant.isChecked(isChecked);
      setTimeout(() => {
        this.updatePosition();
      }, 10);
    } else {
      if (!isChecked) return;
      this.dropdownRef().close();
      this.variants.forEach((v) => {
        v.isChecked(v == variant);
      });
    }
    this.emit('toggleVariant')
  }

  updatePosition() {
    if (this.dropdownRef()) {
      this.dropdownRef().update();
    }
  }

  reset() {
    this.variants.forEach((v) => v.isChecked(false));
  }
}
