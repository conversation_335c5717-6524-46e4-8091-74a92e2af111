@import 'Style/breakpoints';

.foquz-combo {
  display: block;

  &__select {
    position: relative;
    padding-right: 50px;
    height: auto!important;
    min-height: 48px;
    padding-top: 12px!important;
    padding-bottom: 12px!important;
    cursor: pointer;
  }

  &__opener {
    position: absolute;
    right: 10px;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
  }

  &__dropdown {
    max-height: 295px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__empty {
    text-align: center;
    padding: 8px;
    font-size: 14px;
    color: #999;
  }

  &__actions {
    display: none;
  }

  &__variants {
    flex-grow: 1;
    max-height: calc(250px - 10px);
  }

  &__variant {
    padding-top: 8px;
    padding-bottom: 8px;
    padding-right: 15px;
    padding-left: 15px;

    &:hover {
      background: #F1F5F6;
    }

    .f-check-input {
      & + .f-check-label:before {
        border-radius: 3px;
      }
    }

    .f-check-label {
      display: block;
    }
  }

  &__search {
    height: 36px;
    margin-bottom: 16px;
  }

  &__actions {
    margin-left: -5px;
    margin-right: -5px;
    flex-shrink: 0;
    padding-left: 15px;
  }

  &__action {
    height: 36px;
    flex-grow: 1;
    margin: 0 5px;
    border: 2px solid ;
    box-sizing: border-box;
    border-radius: 100px;
    background-color: transparent;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;




    .f-icon {
      color: inherit;
    }

    &--reset {
      color: #f96261
    }

    &--apply {
      color:  #00C968;
    }
  }

  &.invalid {
    .foquz-combo__select {
      border-color:  #f96261;
    }
  }

}

.foquz-combo__dropdown {
  margin-left: -15px;

  .tippy-content {
    padding: 0;
  }

  .foquz-combo__search {
    padding-left: 15px;
  }

  .only-mobile({
    height: 100%;
    max-height: none;

    .foquz-combo__variants {
      max-height: none;
    }

    .os-scrollbar {
      opacity: 0;
    }

    .foquz-combo__variant {


      &:hover {
        background: transparent;
      }
    }

    .foquz-combo__actions {
      margin-top: 16px;
      display: flex;
    }
  });
}

.foquz-check-control {
  &.file-enabled {
    .f-check-label {
      .file-loader-preview {
        width: 48px;
        height: 48px;
        svg {
          width: 22px;
          height: 22px;
        }
      }
      display: flex;
      align-items: center;
      font-size: 12px !important;
      &::before {
        top: 50%;
        transform: translateY(-50%);
      }
      &::after {
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .f-radio-label {
      .file-loader-preview {
        width: 48px;
        height: 48px;
        svg {
          width: 22px;
          height: 22px;
        }
      }
      display: flex;
      align-items: center;
      font-size: 12px !important;
      &::before {
        top: 50%;
        transform: translateY(-50%);
      }
      &::after {
        top: 50%;
        transform: translateY(-50%);
      }
    }
    @media screen and (max-width: 616px) {
      min-height: 200px;
      border: 1px solid #E7EBED;
      border-radius: 8px;
      margin-bottom: 15px;
      .f-check {
        min-height: 200px;
        padding: 0;
      }
      .f-check-label {
        svg {
          width: 92px !important;
          height: 92px !important;
        }
        width: 100%;
        padding: 0;
        flex-wrap: wrap;
        overflow-x: hidden;
        &::before {
          z-index: 10;
          top: 20px;
          left: 10px;
        }
        &::after {
          z-index: 10;
          top: 20px;
          left: 17px;
        }
        .variant-text {
          padding: 10px;
        }
        .file-loader-preview-wrapper {
          height: 200px;
          width: 100%;
          margin-right: 0 !important;
          .file-loader-preview {
            width: 100%;
            height: 100%;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
          }
        }
      }

      .f-radio {
        min-height: 200px;
        padding: 0;
      }
      .f-radio-label {
        width: 100%;
        padding: 0;
        flex-wrap: wrap;
        overflow-x: hidden;
        svg {
          width: 92px !important;
          height: 92px !important;
        }
        &::before {
          z-index: 10;
          top: 20px;
          left: 10px;
        }
        &::after {
          z-index: 10;
          top: 20px;
          left: 18px;
        }
        .variant-text {
          padding: 10px;
        }
        .file-loader-preview-wrapper {
          height: 200px;
          width: 100%;
          margin-right: 0 !important;
          .file-loader-preview {
            width: 100%;
            height: 100%;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
          }
        }
      }
    }
  }
}

.file-loader-preview-selected-variant {
  width: 48px;
  height: 48px;
  svg {
    width: 22px;
    height: 22px;
  }
}
.foquz-combo__item-text-with-file {
  font-size: 12px !important;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 90%;
  overflow: hidden;
}
