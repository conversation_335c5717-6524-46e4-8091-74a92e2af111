import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './popover';

ko.components.register('foquz-tags-select', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);
      $element.addClass(['clients__tag-input']);

      return new ViewModel(params, element);
    },
  },
  template: html,
});
