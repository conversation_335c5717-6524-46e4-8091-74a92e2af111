<!-- ko template: { afterRender: onInit } -->
<div class="clients__tag-input-content">
  <!-- ko foreach: { data: value, afterAdd: fadeAfterAddFactory(200), beforeRemove: fadeBeforeRemoveFactory(200) } -->
  <div
    class="clients__tag-input-item"
    data-bind="css: {
      'color-success': $data.isAuto
  }"
  >
    <span data-bind="text: $data.name"></span>&nbsp;
    <!-- ko ifnot: $data.isAuto || $component.disabled -->
    <button
      class="btn btn-icon btn-default clients__tag-input-item-remove-button"
      data-bind="click: function() { $component.removeTag($data); }"
    ></button>
    <!-- /ko -->
  </div>
  <!-- /ko -->

  <!-- ko if: !disabled && addButton !== null -->
  <div
    class="clients__tag-input-add-tag-button-wrapper"
    data-bind="tagsSelectPopover, popoverOptions: popoverOptions"
  >
    <button
      type="button"
      class="btn btn-circle clients__tag-input-add-tag-button"
      title="Добавить теги"
    ></button>

    <!-- ko if: addButton.label !== null && value().length === 0 -->
    <span
      class="clients__tag-input-add-tag-button-label"
      data-bind="text: addButton.label"
    ></span>
    <!-- /ko -->
  </div>
  <!-- /ko -->
</div>
<!-- /ko -->
