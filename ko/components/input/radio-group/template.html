<div class="hat-radio-group">
  <!-- ko foreach: options -->
  <div class="hat-radio-group__radio"
       data-bind="let: { inputId: $component.optionUnique }">
    <input type="radio"
           class="hat-radio-group__radio-input"
           data-bind="
           disable: $component.disabled,
        value: $data.value,
        checked: $component.value,
        attr: { name: $component.unique, id: inputId }">
    <label class="hat-radio-group__radio-label"
           data-bind="attr: { 'for': inputId }">
      <!-- ko ifnot: $component.withoutIcon -->
        <!-- ko ifnot: $data.icon -->
        <span class="hat-radio-group__radio-indicator"></span>
        <!-- /ko -->

        <!-- ko if: $data.icon -->
        <svg-icon params="name: $data.icon"
                  class="flex-shrink-0 mr-2" data-bind="css: {
                    'f-color-primary': $data.value == $component.value(),
                    'f-color-service': $data.value != $component.value(),
                  }"></svg-icon>
        <!-- /ko -->
      <!-- /ko -->

      <!-- ko text: $data.label -->
      <!-- /ko -->

    </label>
  </div>
  <!-- /ko -->
</div>
