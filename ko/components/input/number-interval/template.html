<div class="foquz-number-interval__fields">
  <foquz-number-input class="foquz-number-interval__field"
                      params="value: interval.from, min: min, max: max"
                      data-bind="css: {
                        'is-invalid': formControlErrorStateMatcher(interval.from),
                        'dense': dense
                      }">
  </foquz-number-input>

  <div class="foquz-number-interval__separator">
    –
  </div>

  <foquz-number-input class="foquz-number-interval__field"
                      params="value: interval.to, min: min, max: max"
                      data-bind="css: {
                        'is-invalid': formControlErrorStateMatcher(interval.to),
                        'dense': dense
                      }">
  </foquz-number-input>
</div>

<validation-feedback class="px-2" params="text: interval.error,
                     show: formControlErrorStateMatcher(interval)">
</validation-feedback>
