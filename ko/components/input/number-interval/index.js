import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('foquz-number-interval', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);
      element.classList.add('foquz-number-interval');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
