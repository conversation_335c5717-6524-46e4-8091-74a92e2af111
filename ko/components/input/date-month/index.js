import { ViewModel } from './model';
import html from './template.html';
import './style.less';
import 'Components/input/month-picker';

ko.components.register('date-month', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('date-month');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
