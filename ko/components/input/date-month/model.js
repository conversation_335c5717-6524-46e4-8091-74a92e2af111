import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.model = params.model;

    this.required = this.model.required;
    this.allowEmptyMonth = this.model.allowEmptyMonth;
    this.disabled = params.disabled;

    this.formControlErrorStateMatcher =
      params.formControlErrorStateMatcher ||
      commonFormControlErrorStateMatcher();

    this.formatValue();


  }

  formatValue() {
    let date = parseInt(this.model.date());
    if (date) {
      this.model.date((date + '').padStart(2, '0'));
    } else {
      this.model.date('');
    }
  }
}
