<div>
  <div class="d-flex">
    <input
      type="text"
      class="form-control mr-15p text-center p-0 date-month__date"
      data-bind="
        onlyNumbers,
        textInput: model.date,
        css: {
          'is-invalid': formControlErrorStateMatcher(model.date),
        },
        event: {
          blur: function() {
            formatValue();
          },
        },
        disable: disabled,
      "
      maxlength="2"
      placeholder="00"
    />
    <div
      class="select2-wrapper date-month__month"
      data-bind="
        css: {
          'is-invalid': formControlErrorStateMatcher(model.month),
        },
      "
    >
      <month-picker
        params="
          value: model.month,
          allowEmptyMonth: allowEmptyMonth,
          disabled: disabled,
        "
      ></month-picker>
      <!-- ko ifnot: formControlErrorStateMatcher(model.date) -->
      <validation-feedback params="show: formControlErrorStateMatcher(model.month), text: model.month.error"></validation-feedback>
      <!-- /ko -->
    </div>
  </div>
  <!-- ko if: formControlErrorStateMatcher(model.date) -->
  <validation-feedback params="show: formControlErrorStateMatcher(model.date), text: model.date.error"></validation-feedback>
  <!-- /ko -->
</div>
