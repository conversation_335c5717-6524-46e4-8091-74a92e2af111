<div class="d-flex flex-wrap">
  <div class="variant-text-field__variant">
    <radio-group
      params="value: variantValue, options: options, disabled: disabled"
    ></radio-group>
  </div>
  <div class="variant-text-field__text">
    <!-- ko if: slot -->
      <!-- ko using: $parent -->
      <!-- ko template: { nodes: $componentTemplateNodes } -->
      <!-- /ko -->
      <!-- /ko -->
    <!-- /ko -->
    <!-- ko ifnot: slot -->
    <input
      type="text"
      class="form-control"
      data-bind="
        textInput: value,
        attr: {
          placeholder: placeholder,
        }
      "
    />
    <!-- /ko -->
  </div>
</div>