<!-- ko descendantsComplete: $component.onRender.bind($component) -->



<div class="foquz-select2__wrapper d-flex align-items-end">

      <!-- ko if: multiple -->

      <select data-bind="element: selectRef,
            disable: disabled,
            selectedOptions: _value,
            valueAllowUnset: allowUnset,
            lazySelect2: config,
        "
              multiple>
            <!-- ko if: emptyOption -->
            <option></option>
            <!-- /ko -->

            <!-- ko template: { nodes: $componentTemplateNodes } -->
            <!-- /ko -->
      </select>
      <!-- /ko -->



      <!-- ko ifnot: multiple -->
      <select data-bind="element: selectRef,
            disable: disabled,
            value: _value,
            valueAllowUnset: allowUnset,
            lazySelect2: config
        ">
            <!-- ko if: emptyOption -->
            <option></option>
            <!-- /ko -->

            <!-- ko template: { nodes: $componentTemplateNodes } -->
            <!-- /ko -->
      </select>
      <!-- /ko -->


      <!-- ko if: multiple && withExcept && value().length >= exceptCount -->

      <!-- ko ifnot: disabled -->
      <foquz-checkbox class="foquz-select2__except sm"
                      params="checked: except, disabled: disabled">
            <!-- ko text: $parent.exceptText -->
            <!-- /ko -->
      </foquz-checkbox>
      <!-- /ko -->

      <!-- ko if: disabled -->
      <!-- ko if: except -->
      <foquz-checkbox class="foquz-select2__except sm"
                      params="checked: except, disabled: disabled">
            <!-- ko text: $parent.exceptText -->
            <!-- /ko -->
      </foquz-checkbox>
      <!-- /ko -->
      <!-- /ko -->

      <!-- /ko -->
</div>


<!-- /ko -->
