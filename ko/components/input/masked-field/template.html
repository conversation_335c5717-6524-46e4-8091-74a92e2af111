<!-- ko ifnot: ['name', 'period', 'date-month'].includes(alias) -->
<div class="form-group">
  <!-- ko if: maskedField.fieldConfig.label -->
  <label
    class="form-label"
    data-bind="text: texts().label"
  ></label>
  <!-- /ko -->

  <!-- ko if: maskedField.fieldConfig.multiline -->
  <textarea
    class="form-control form-control_full sm"
    data-bind="autosizeTextarea, textInput: maskedField.field,
    attr:{
      maxlength: maskedField.fieldConfig.maxLength,
      minlength: maskedField.fieldConfig.minLength,
      placeholder: texts().placeholderText
    },
    css: {
      'is-invalid': formControlErrorStateMatcher(maskedField.field)
    }"
  ></textarea>
  <!-- /ko -->

  <!-- ko ifnot: maskedField.fieldConfig.multiline -->
  <!-- ko if: alias == 'no-mask' -->
  <input
    class="form-control"
    data-bind="
          textInput: maskedField.field,
          css: {
            'is-invalid': formControlErrorStateMatcher(maskedField.field)
          },
          attr: {
            maxlength: maskedField.fieldConfig.maxLength,
            minlength: maskedField.fieldConfig.minLength,
            placeholder: texts().placeholderText
          }"
  />
  <!-- /ko -->

  <!-- ko if: alias == 'phone' -->
  <input
    class="form-control"
    name="answer"
    data-bind="
            textInput: maskedField.field,
            css: {
              'is-invalid': formControlErrorStateMatcher(maskedField.field)
            },
            attr: {
              maxlength: maskedField.fieldConfig.maxLength,
              minlength: maskedField.fieldConfig.minLength
            },
            phoneInput: {
              placeholder: texts().placeholderText
            }"
    inputmode="tel"
  />
  <!-- /ko -->

  <!-- ko if: alias == 'email' -->
  <input
    class="form-control"
    name="answer"
    data-bind="
          textInput: maskedField.field,
          css: {
            'is-invalid': formControlErrorStateMatcher(maskedField.field)
          },
          attr: {
            maxlength: maskedField.fieldConfig.maxLength,
            minlength: maskedField.fieldConfig.minLength,
            placeholder: texts().placeholderText
          }"
    inputmode="email"
  />
  <!-- /ko -->

  <!-- ko if: alias == 'number' -->
  <input
    type="number"
    class="form-control"
    data-bind="textInput: maskedField.field,
          css: {
            'is-invalid': formControlErrorStateMatcher(maskedField.field)
          },
          attr: {
            maxlength: maskedField.fieldConfig.maxLength,
            minlength: maskedField.fieldConfig.minLength,
            placeholder: texts().placeholderText
          }"
    inputmode="decimal"
    min="0"
    step="1"
    onkeydown="if(event.key==='e' ||event.key==='E' || (this.value.length>=this.getAttribute('maxlength') && event.keyCode !== 8)) {event.preventDefault();}"
  />
  <!-- /ko -->

  <!-- ko if: alias == 'site' -->
  <input
    class="form-control"
    name="answer"
    data-bind="
        textInput: maskedField.field,
        css: {
          'is-invalid': formControlErrorStateMatcher(maskedField.field)
        },
        attr: {
          maxlength: maskedField.fieldConfig.maxLength,
          minlength: maskedField.fieldConfig.minLength,
          placeholder: texts().placeholderText
        }"
    inputmode="url"
  />
  <!-- /ko -->

  <!-- ko if: alias == 'date' -->
  <div>
    <fc-date-picker params="value: maskedField.field, placeholder: texts().placeholderText, dropdowns: true, 
      invalid: formControlErrorStateMatcher(maskedField.field)"></fc-date-picker>
    
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko template: {
      foreach: templateIf(maskedField.field.error, $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
  <validation-feedback
    params="show: formControlErrorStateMatcher(maskedField.field), text: maskedField.field.error"
  >
  </validation-feedback>
  <!-- /ko -->
</div>
<!-- /ko -->

<!-- ko if: alias == 'date-month' -->
<div class="form-group">
  <!-- ko if: maskedField.fieldConfig.label -->
  <label
    class="form-label"
    data-bind="text: texts().label"
  ></label>
  <!-- /ko -->
  <div class="date-month-wrapper">
    <date-month
      params="model: maskedField.dateMonth, formControlErrorStateMatcher: formControlErrorStateMatcher, modal: modal"
    ></date-month>
  </div>
</div>
<!-- /ko -->

<!-- ko if: alias == 'period' -->
<div class="form-group">
  <!-- ko if: maskedField.fieldConfig.label -->
  <label
    class="form-label"
    data-bind="text: texts().label"
  ></label>
  <!-- /ko -->

  <div class="d-flex align-items-center period-wrapper">
    <fc-date-picker params="value: maskedField.from, placeholder: texts().placeholderText, dropdowns: true, 
      invalid: formControlErrorStateMatcher(maskedField.period)"></fc-date-picker>

    

    <span class="period-separator">&ndash;</span>

    <fc-date-picker params="value: maskedField.to, placeholder: texts().placeholderText, dropdowns: true, 
      invalid: formControlErrorStateMatcher(maskedField.period)"></fc-date-picker>
  </div>

  <!-- ko template: {
      foreach: templateIf(maskedField.period.error, $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
  <validation-feedback
    params="show: formControlErrorStateMatcher(maskedField.period), text: maskedField.period.error"
  >
  </validation-feedback>
  <!-- /ko -->
</div>
<!-- /ko -->

<!-- ko if: alias == 'name' -->
<div class="name-mask">
  <!-- ko foreach: nameFields -->
  <div
    data-bind="css: {
    'mb-4': $index() < $component.nameFields.length - 1
  }"
  >
    <label
      class="form-label"
      data-bind="attr: { 'for': 'answer-' + $data.id }, text: $data.label"
    ></label>
    <input
      type="text"
      class="form-control"
      data-bind="
              css: {
                'is-invalid': $component.formControlErrorStateMatcher($data.value)
              },
              attr: {
                  id: 'answer-' + $data.id,
                  maxlength: $data.config.maxLength,
                  minlength: $data.config.minLength,
                  placeholder: $data.placeholder
              },
              textInput: value"
    />
    <!-- ko template: {
      foreach: templateIf(value.error, $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <validation-feedback
      params="show: $component.formControlErrorStateMatcher(value), text: value.error"
    >
    </validation-feedback>
    <!-- /ko -->
  </div>
  <!-- /ko -->
</div>
<!-- /ko -->
