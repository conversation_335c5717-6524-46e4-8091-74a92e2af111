import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/input/date-month';

ko.components.register('masked-field', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('masked-field');
      return new ViewModel(params, element);
    },
  },
  template: html,
});
