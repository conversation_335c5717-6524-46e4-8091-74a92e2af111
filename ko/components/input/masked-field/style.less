.masked-field {
  display: block;
  margin-bottom: 24px;

  .validationMessage {
    display: none;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &--date {
    .date-input-group {
      width: 145px;
      margin-bottom: 4px;
    }
    .form-error {
      margin-top: 2px;
    }
  }

  &--period {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .period-separator {
      margin: 0 4px 4px;
      line-height: 1;
    }
    .fc-date-picker {
      width: 145px;
      margin-bottom: 4px;
      input {
        width: 145px;
      }
    }
    .form-error {
      margin-top: 2px;
    }

  }



  @media screen and (max-width: 375px) {


    .date-input-group {
      width: 135px;
      .form-control {
        padding-right: 40px;
        width: 100%;
        // flex-grow: 0;
      }

    }
  }
}
