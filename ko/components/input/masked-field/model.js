import { maskAliases } from "Models/masked-field-config/aliases";
import { Translator } from "@/utils/translate";
export const translator = Translator("main");

const MainTranslator = Translator("main");

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.modal = params.modal;

    this.maskedField = params.maskedField;
    this.translation = params.translation;

    this.defaultTexts = {
      label: this.maskedField?.fieldConfig?.label,
      placeholderText: this.maskedField?.fieldConfig?.placeholderText,

      nameLabel: translator.t("Имя"),
      surnameLabel: translator.t("Фамилия"),
      patronymicLabel: translator.t("Отчество"),
      namePlaceholder: this.maskedField.config?.name?.placeholderText,
      surnamePlaceholder: this.maskedField.config?.surname?.placeholderText,
      patronymicPlaceholder:
        this.maskedField.config?.patronymic?.placeholderText,
    };

    this.translations = ko.computed(() => {
      const translation = ko.toJS(this.translation) || {};

      const {
        label,
        placeholderText,
        name<PERSON>abel,
        surname<PERSON><PERSON><PERSON>,
        patronymic<PERSON>abel,
        namePlaceholder,
        surnamePlaceholder,
        patronymicPlaceholder,
      } = translation;
      return {
        label,
        placeholderText,
        nameLabel,
        surnameLabel,
        patronymicLabel,
        namePlaceholder,
        surnamePlaceholder,
        patronymicPlaceholder,
      };
    });

    this.texts = ko.computed(() => {
      const original = ko.toJS(this.defaultTexts);
      const translation = ko.toJS(this.translations);

      const result = {};

      Object.keys(original).forEach((key) => {
        result[key] = translation[key] || original[key];
      });

      return result;
    });

    this.alias = this.maskedField.type
      ? maskAliases[this.maskedField.type]
      : maskAliases[0];
    element.classList.add("masked-field--" + this.alias);

    this.formControlErrorStateMatcher =
      params.formControlErrorStateMatcher ||
      commonFormControlErrorStateMatcher();

    this.nameFields = [
      {
        id: "surname",
        label: ko.computed(() => {
          return this.texts().surnameLabel;
        }),
        show: this.maskedField.hasSurname(),
        value: this.maskedField.surname,
        config: this.maskedField.config.surname,
        placeholder: ko.computed(() => {
          return this.texts().surnamePlaceholder;
        }),
      },
      {
        id: "name",
        label: ko.computed(() => {
          return this.texts().nameLabel;
        }),
        show: this.maskedField.hasName(),
        value: this.maskedField.name,
        config: this.maskedField.config.name,
        placeholder: ko.computed(() => {
          return this.texts().namePlaceholder;
        }),
      },
      {
        id: "patronymic",
        label: ko.computed(() => {
          return this.texts().patronymicLabel;
        }),
        show: this.maskedField.hasPatronymic(),
        value: this.maskedField.patronymic,
        config: this.maskedField.config.patronymic,
        placeholder: ko.computed(() => {
          return this.texts().patronymicPlaceholder;
        }),
      },
    ].filter((f) => f.show);
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
