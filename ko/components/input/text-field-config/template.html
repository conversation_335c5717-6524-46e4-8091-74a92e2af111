<div data-bind="using: field"
     class="row">

  <!-- ko if: withLabel -->
  <div class="col-12">
    <div class="form-group">
      <foquz-chars-counter params="value: label, max: 120">
        <input type="text"
               class="form-control"
               data-bind="textInput: $parent.label, attr: {
          placeholder: $parent.defaultLabel
        }, disable: disabled">
      </foquz-chars-counter>
    </div>
  </div>
  <!-- /ko -->

  <div class="col-6 text-field__placeholder mb-2" >
    <field-placeholder params="placeholder: placeholder, disabled: $component.disabled"></field-placeholder>
  </div>

  <div class="col-auto text-field__range">
    <div class="form-group pb-3 mb-4">
      <label class="form-label mb-3"
             data-bind="text: $component.intervalText"></label>

      <div class="mx-n2">
        <interval-slider params="minLimit: 0,
        maxLimit: 3000,
        range: range, disabled: $component.disabled"></interval-slider>
      </div>

    </div>
  </div>

</div>
