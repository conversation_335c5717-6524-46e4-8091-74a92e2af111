import * as maskTypes from 'Models/masked-field-config/types';
import { Translator } from '@/utils/translate';
const MainTranslator = Translator('main')

const limitTypes = [
  maskTypes.NO_MASK,
  maskTypes.MASK_EMAIL,
  maskTypes.MASK_NUMBER,
  maskTypes.MASK_SITE
];

const noPlaceholderTypes = [maskTypes.MASK_NAME, maskTypes.MASK_DATE_MONTH];

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.disabled = params.disabled;

    this.mask = params.mask;
    this.types = [
      {
        id: maskTypes.MASK_PHONE,
        label: MainTranslator.t('Телефон')()
      },
      {
        id: maskTypes.MASK_EMAIL,
        label: MainTranslator.t('Почта')()
      },
      {
        id: maskTypes.MASK_NUMBER,
        label: MainTranslator.t('Число')()
      },
      {
        id: maskTypes.MASK_SITE,
        label: MainTranslator.t('Сайт')()
      },
      {
        id: maskTypes.MASK_NAME,
        label: MainTranslator.t('ФИО')()
      },
      {
        id: maskTypes.MASK_DATE,
        label: MainTranslator.t('Дата')()
      },
      {
        id: maskTypes.MASK_PERIOD,
        label: MainTranslator.t('Период')()
      },
      {
        id: maskTypes.MASK_DATE_MONTH,
        label: MainTranslator.t('Дата (день и месяц)')()
      },
    ];

    this.formControlErrorStateMatcher =
      params.formControlErrorStateMatcher ||
      commonFormControlErrorStateMatcher();

    this.hasLimit = ko.pureComputed(() => {
      return limitTypes.unstrictIncludes(this.mask.type());
    });

    this.hasPlaceholder = ko.pureComputed(() => {
      return !noPlaceholderTypes.unstrictIncludes(this.mask.type());
    });
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
