import { ViewModel } from './model';
import html from './template.html';
import './style.less';
import 'Components/input/field-placeholder';
import 'Components/input/name-mask-field';

ko.components.register('masked-field-config', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('masked-field');
      return new ViewModel(params, element);
    },
  },
  template: html,
});
