<div class="">

  <div class="row">
    <div class="col-6" >
      <div class="form-group">
        <label class="form-label" data-bind="text: _t('Маска')"></label>
        <button class="btn-question"
                tabindex="10"
                data-bind="tooltip, tooltipPlacement: 'top', tooltipText: _t('Для поля ввода можно установить маску: Телефон, Почта, Число, Сайт, ФИО. Используется для получения более точного ответа на вопрос')"
                type="button"></button>

        <select data-bind=" value: mask.type,
        valueAllowUnset: true,
        disable: disabled,
          lazySelect2: {
              containerCssClass: 'form-control',
              wrapperCssClass: 'select2-container--form-control',
              placeholder: _t('Без маски'),
              allowClear: true,
          }">
          <option></option>
          <!-- ko foreach: types -->
          <option data-bind="value: id, text: label"></option>
          <!-- /ko -->
        </select>
      </div>
    </div>

    <!-- ko template: {
        foreach: templateIf(hasPlaceholder(), $data),
        afterAdd: fadeAfterAddFactory(200, 200),
        beforeRemove: fadeBeforeRemoveFactory(100)
    } -->
    <div class="col-6">
      <field-placeholder class="flex-grow-1 mb-4"
                         params="placeholder: mask.field.placeholder, disabled: disabled"></field-placeholder>
    </div>
    <!-- /ko -->
  </div>

  <!-- ko if: formControlErrorStateMatcher(mask) -->
  <div class="survey-question__mask">
    <div class="form-error"
         data-bind="text: mask.error"></div>
  </div>
  <!-- /ko -->
</div>

<!-- ko template: {
    foreach: templateIf(mask.isNameMask(), $data),
    afterAdd: slideAfterAddFactory(600, 200),
    beforeRemove: slideBeforeRemoveFactory(100)
} -->
<div>
  <!-- ko foreach: { data: mask.nameMask.fields, as: 'maskField' } -->
  <name-mask-field params="field: maskField, disabled: $parent.disabled"></name-mask-field>
  <!-- /ko -->
</div>
<!-- /ko -->

<!-- ko template: {
  foreach: templateIf(hasLimit(), $data),
  afterAdd: slideAfterAddFactory(200),
    beforeRemove: slideBeforeRemoveFactory(100)
} -->
<div class="mt-2 mb-4 pb-3">
  <label class="form-label mb-3" data-bind="text: _t('Допустимое количество символов в ответе')"></label>

  <div class="mx-n2">
    <interval-slider params="minLimit: 0,
    maxLimit: 3000,
    range: mask.field.range,
    disabled: disabled"></interval-slider>
  </div>

</div>
<!-- /ko -->
