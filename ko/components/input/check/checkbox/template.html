
<label class="f-check" data-bind="css: {
  'f-check--partially': partially,
  'f-check--checked': checked
}">
  <input type="checkbox" class="f-check-input"
  data-bind="
    checked: checked,
    value: value,
    attr: {
      id: id,
      name: name,
    },
    disable: disabled,
    event: event">
  <label class="f-check-label" data-bind="attr: { 'for': id }">
    <!-- ko template: { nodes: $componentTemplateNodes } -->
    <!-- /ko -->
  </label>
</label>
