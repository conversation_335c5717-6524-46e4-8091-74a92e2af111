<label class="f-radio"
       data-bind="css: {
  'f-radio--checked': checked
}">
  <input type="radio"
         class="f-radio-input"
         data-bind="
    checked: checked,
    value: value,
    attr: {
      id: id,
      name: name,
    },
    disable: disabled,
    event: event,">
  <label class="f-radio-label"
         data-bind="attr: { 'for': id }">
    <!-- ko template: { nodes: $componentTemplateNodes } -->
    <!-- /ko -->
  </label>
</label>
