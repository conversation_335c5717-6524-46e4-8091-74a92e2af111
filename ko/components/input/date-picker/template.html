
<!-- ko if: mode() == 'view' -->
<!-- ko if: value -->
<div data-bind="text: value"
     class="font-weight-500 date-picker__placeholder"></div>
<!-- /ko -->
<!-- ko ifnot: value -->
<div data-bind="text: placeholder"
     class="font-weight-400 f-color-service date-picker__placeholder"></div>
<!-- /ko -->
<!-- /ko -->

<!-- ko ifnot: mode() == 'view' -->
<div class="input-group date-input-group"
     data-bind="dateInputGroup, let: { input: ko.observable(null) },
     css: {
       'date-input-group--has-value': allowClear && value()
     },">
  <!-- ko if: autosize -->
  <input type="text"
         class="form-control"
         data-bind="
            disable: disabled,
          element: input,
          foquzMask, maskPattern: mask,
          value: value,

          attr: { placeholder: placeholder },
          autosizeInput: autosizeInput,
          periodPicker,
          periodPickerConfig: pluginOptions,
          periodPickerApply: function () { resize() }">
  <!-- /ko -->

  <!-- ko ifnot: autosize -->
  <input type="text"
         class="form-control"
         data-bind="
               disable: disabled,
                element: input,
                foquzMask, maskPattern: mask,
                value: value,
                attr: { placeholder: placeholder },
                periodPicker,
                periodPickerConfig: pluginOptions">
  <!-- /ko -->

  <!-- ko if: allowClear && value() -->
  <foquz-icon params="icon: 'times'"
              class="f-icon-sm f-icon-danger date-input-group__clear"
              data-bind="click: function() {reset()}"></foquz-icon>
  <!-- /ko -->


  <i class="date-input-group__icon"
     data-bind="click: function() { input().focus() }"></i>
</div>
<!-- /ko -->
