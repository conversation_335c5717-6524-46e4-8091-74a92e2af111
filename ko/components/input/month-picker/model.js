import { FoquzComponent } from "Models/foquz-component";
import { Translator } from "@/utils/translate";
const translator = Translator("calendar");

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.value = params.value;
    this.allowEmptyMonth = params.allowEmptyMonth;
    this.disabled = params.disabled;

    this.options = this.months.map((m, i) => {
      return {
        text: m.name,
        id: `${i + 1}`
      }
    })
  }

  get months() {
    return [
      { name: translator.t("Январь")(), days: 31 },
      { name: translator.t("Февраль")(), days: 29 },
      { name: translator.t("Март")(), days: 31 },
      { name: translator.t("Апрель")(), days: 30 },
      { name: translator.t("Май")(), days: 31 },
      { name: translator.t("Июнь")(), days: 30 },
      { name: translator.t("<PERSON>юль")(), days: 31 },
      { name: translator.t("Август")(), days: 31 },
      { name: translator.t("Сентябрь")(), days: 30 },
      { name: translator.t("Октябрь")(), days: 31 },
      { name: translator.t("Ноябрь")(), days: 30 },
      { name: translator.t("Декабрь")(), days: 31 },
    ];
  }
}
