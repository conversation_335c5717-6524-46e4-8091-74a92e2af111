<div class="row">
  <div class="col-6">
    <div class="form-group">
      <label class="form-label"
             data-bind="text: label"></label>
      <select data-bind="
            value: model.type,
            select2: {
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                allowClear: true,
                placeholder: 'Не выбрано'
            }
        ">
        <option></option>
        <option value="1">Дата</option>
        <option value="2">Период</option>
      </select>
    </div>
  </div>

  <div class="col-6">
    <div class="row flex-nowrap">

      <div class="col-12">
        <!-- ko template: {
            foreach: templateIf(model.type() === '1', $data),
            afterAdd: fadeAfterAddFactory(200)
          } -->
        <div class="">
          <div class=" form-group">
            <label class="form-label">Дата</label>
            <date-picker params="value: model.date"></date-picker>
          </div>
        </div>
        <!-- /ko -->

        <!-- ko template: {
            foreach: templateIf(model.type() === '2', $data),
            afterAdd: fadeAfterAddFactory(200)
          } -->
        <div class="">
          <div class="form-group">
            <label class="form-label">Период</label>
            <period-picker params="value: model.period, ranges: true"></period-picker>
          </div>
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>
</div>
