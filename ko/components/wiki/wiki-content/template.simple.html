
<div class="wiki-content-simple__wrapper">
  <div class="wiki-content__header ">
    <div class="wiki-content__width-overflow">
      <div class="wiki-content__search wiki-content__width">
        <search-field params="value: query, placeholder: '', withoutIcon: true"></search-field>

      </div>
    </div>

  </div>

  <div class="wiki-content__content">
      <div class="h-100" data-bind="nativeScrollbar">
        <div class="wiki-content__width-overflow">
          <div class="wiki-content__items wiki-content__width">
            <!-- ko ifnot: articles().length -->
            <div class="text-center f-color-service">Ничего не найдено</div>
            <!-- /ko -->
            <!-- ko foreach: articles -->
            <wiki-content-item params="article: $data,
                click: $component.selectArticle,
                mode: $component.mode,
                filter: $component.filter,
                link: $component.link,
                active: $component.active,
                selected: $component.articleId"></wiki-content-item>
            <!-- /ko -->
          </div>
        </div>
      </div>

  </div>
</div>
