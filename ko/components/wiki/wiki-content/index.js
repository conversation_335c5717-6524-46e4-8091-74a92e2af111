import { ViewModel } from './model';
import html from './template.html';
import htmlSimple from './template.simple.html';
import './style.less';

import './wiki-content-item';

import 'Bindings/sticky-resize'

ko.components.register('wiki-content', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('wiki-content');

      return new ViewModel(params, element);
    },
  },
  template: html,
});

ko.components.register('wiki-content-simple', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('wiki-content');
      element.classList.add('wiki-content-simple');

      return new ViewModel(params, element);
    },
  },
  template: htmlSimple,
});
