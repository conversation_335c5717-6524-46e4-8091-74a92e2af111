export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.opened = ko.observable(ko.toJS(params.filter));

    this.article = params.article;

    this.mode = params.mode || 'default';
    this.click = params.click;
    this.selected = params.selected;

    this.link = params.link;
    this.href =
      this.mode == 'link'
        ? (params.link || '').replace('{id}', this.article.id)
        : this.article.name ? '/foquz/user-wiki/' + this.article.name : '/foquz/user-wiki?id=' + this.article.id;

    element.classList.toggle(
      'selected',
      ko.unwrap(this.selected) == this.article.id
    );

    if (ko.isObservable(this.selected)) {
      this.selected.subscribe((v) => {
        element.classList.toggle('selected', v == this.article.id);
      });
    }
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
