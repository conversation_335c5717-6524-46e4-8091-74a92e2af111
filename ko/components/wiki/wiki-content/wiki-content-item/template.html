<div class="wiki-content-item__data">
  <a
    class="wiki-content-item__title"
    data-bind="click: function() {
      if (mode == 'link') return true;
      click(article.id);
    },
    attr: {
      'href': href
    }"
  >
    <span>
      <!-- ko text: article.title -->
      <!-- /ko -->
    </span>
  </a>

  <!-- ko if: article.children.length -->
  <div
    class="wiki-content-item__toggler f-transform--transition"
    data-bind="click: function() { opened(!opened()); }"
  >
    <foquz-icon params="icon: 'arrow-bottom'" class="f-icon-sm"></foquz-icon>
  </div>
  <!-- /ko -->
</div>

<!-- ko if: article.children.length -->
<!-- ko template: {
    foreach: templateIf(opened(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400)
  } -->
<div class="wiki-content-item__children">
  <!-- ko foreach: article.children -->
  <wiki-content-item
    params="
        article: $data,
        click: $component.click,
        mode: $component.mode,
        collapsed: $component.collapsed,
        link: $component.link,
        selected: $component.selected"
  ></wiki-content-item>
  <!-- /ko -->
</div>
<!-- /ko -->
<!-- /ko -->
