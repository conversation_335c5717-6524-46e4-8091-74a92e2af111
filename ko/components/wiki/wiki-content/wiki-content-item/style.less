@import 'Style/colors';

.wiki-content-item {
  display: block;
  margin-bottom: 2px;

  &__data {
    position: relative;
  }

  &__toggler {
    position: absolute;
    left: 2px;
    top: 8px;
    color: #a6b1bc;
    cursor: pointer;

    svg {
      width: 10px;
      height: 6px;
    }
  }

  &__title {
    display: flex;
    min-height: 30px;
    align-items: center;

    padding-top: 7px;
    padding-bottom: 7px;
    padding-left: 20px;
    padding-right: 7px;

    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    color: #000000;

    border-radius: 4px;

    &:hover,
    &:focus,
    &:active {
      background: #f1f5f6;
      text-decoration: none;
      color: @f-color-text;
    }

    span {
      display: inline-block;
      width: 100%;
    }
  }

  &[data-level='1'] {
    & > .wiki-content-item__data {
      .wiki-content-item__title {
        font-size: 15px;
        font-weight: 500;
      }
    }
  }

  &[data-level='2'] {
    & > .wiki-content-item__data {
      .wiki-content-item__title {
        font-weight: 500;
      }
    }
  }

  &.disabled {
    & > .wiki-content-item__data .wiki-content-item__title {
      color: #8e99a3;
    }
  }

  &__children {
    margin-top: 2px;
    padding-left: 20px;
  }

  &.selected {
    & > .wiki-content-item__data {
      .wiki-content-item__title {
        background: #f1f5f6;
      }
    }
  }
}
