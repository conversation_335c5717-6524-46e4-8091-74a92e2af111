const WikiListIsClosedField = 'wiki_list_is_closed';
const isClosed = () => {
  return localStorage.getItem(WikiListIsClosedField) == 1;
};
const setClosed = (state) => {
  localStorage.setItem(WikiListIsClosedField, state ? 1 : 0);
};

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.filter = params.filter;

    this.loading = params.loading;
    this.closed = ko.observable(isClosed());

    this.closed.subscribe((v) => {
      setClosed(v);
    });

    this.articles = params.list;
    this.articleId = params.activeId;

    this.query = params.filter;
    this.active = !!params.active;

    this.subscriptions.push(
      this.closed.subscribe((v) => element.classList.toggle('closed', v))
    );

    element.classList.toggle('closed', this.closed());

    element.classList.toggle('wiki-content--shadow', 'shadow' in params ? !!params.shadow : true);

    this.mode = typeof params.onSelect == 'function' ? 'default' : 'link';
    this.link = params.link;

    this.selectArticle = (articleId) => {
      if (!articleId) {
        if (this.mode != 'link') params.onSelect(null);
        return;
      }

      if (this.mode != 'link') {
        params.onSelect(articleId);
      }
    };
  }

  toggle() {
    let isClosed = this.closed();
    this.closed(!isClosed);
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
