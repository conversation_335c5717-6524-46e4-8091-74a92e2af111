<article data-bind="using: article" class="wiki-article">
  <header class="wiki-article__header" data-bind="sticky">
    <div class="d-flex justify-content-between align-items-center">
      <div class="flex-grow-1">
        <div class="d-flex f-fs-1 f-color-service">
          <div class="font-weight-500 mr-4" data-bind="css: {
            'f-color-success': active,
          }">
            <!-- ko text: active() ? 'Активен' : 'Не активен' -->
            <!-- /ko -->
          </div>

          <div class="mr-4">
            Ред.: <span data-bind="text: updatedAt" class="font-weight-500"></span>
          </div>

          <!-- ko if: weight -->
          <div class="mr-4">
            Вес: <span data-bind="text: weight"  class="font-weight-500"></span>
          </div>
          <!-- /ko -->
        </div>

        <div class="f-fs-2 bold wiki-article__title mt-1" >
          <span data-bind="text: title"></span>
        </div>
      </div>

      <div class="d-flex">
        <button  class="f-btn f-btn--square f-btn-danger" type="button" data-bind="click: $component.onRemove">
          <foquz-icon class="f-icon-sm" params="icon: 'times'"></foquz-icon>
          </button>
        <a class="f-btn f-btn--square" data-bind="attr: { href: editLink}">
          <foquz-icon params="icon: 'pencil'"></foquz-icon>
        </a>

      </div>
    </div>
  </header>


  <div class="wiki-article__content">
    <h1 class="f-h1 wiki-article__title mb-4" data-bind="text: title"></h1>

    <div class="wiki-article__text" data-bind="html: html">

    </div>


  </div>
</article>
