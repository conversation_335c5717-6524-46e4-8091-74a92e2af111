@import 'Style/content';

wiki-article {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.wiki-article {
  flex-grow: 1;


  &__header {
    position: sticky;
    top: 0;
    padding-top: 15px;
    padding-left: 20px;
    padding-right: 20px;
    background: white;
    z-index: 5;
    height: 66px;

    .wiki-article__title {
      display: none;
    }

    &:after {
      content: '';
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(
        180deg,
        rgba(115, 128, 141, 0.15) 0%,
        rgba(115, 128, 141, 0) 100%
      );
      opacity: 0;
      pointer-events: none;
    }

    &.stuck {
      &:after {
        opacity: 1;
      }

      .wiki-article__title {
        display: block;
        position: relative;
        height: 1.2em;

        span {
          position: absolute;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 100%;
          padding-right: 20px;
        }
      }
    }
  }

  &__content {
    padding-left: 20px;
    padding-right: 20px;
  }

  &__title {
    padding-bottom: 0;
  }

  &__text {
    .content();
  }
}
