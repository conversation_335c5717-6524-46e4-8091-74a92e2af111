@wiki-list-full-width: 330px;

.wiki-list {
  position: relative;
  display: block;
  height: 100%;

  &__wrapper {
    flex-grow: 1;
    padding-bottom: 24px;

    width: @wiki-list-full-width;
    transition: width 400ms ease-in-out;

    overflow: hidden;
  }

  &__scroll-container {
    padding-bottom: 16px;
    width: @wiki-list-full-width;
    transition: width 400ms ease-in-out;
  }

  &__scroll-track {
    display: flex;
    flex-direction: column;
    height: 100% !important;
    overflow: hidden;
  }

  &__toggler {
    position: sticky;
    top: 31px;
    left: 0px;
    z-index: 15;
  }

  &-toggler {
    position: absolute;
    top: -15px;
    left: -15px;
    width: 30px;
    height: 30px;
    cursor: pointer;
    background: white;
    box-shadow: 0px 5px 15px rgba(46, 47, 49, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    padding-right: 2px;

    svg {
      transform-origin: 59% 50%;
      transition: transform 400ms ease-in-out;
    }
  }

  &__content {
    width: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  &__items {
    padding-left: 20px;
    padding-right: 20px;
  }

  &__header {
    position: relative;
    background: white;
    z-index: 10;

    &:after {
      content: '';
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(
        180deg,
        rgba(115, 128, 141, 0.15) 0%,
        rgba(115, 128, 141, 0) 100%
      );
      opacity: 0;
      pointer-events: none;
    }
  }

  &__width-overflow {
    overflow: hidden;
  }
  &__width {
    width: @wiki-list-full-width;
  }

  &__search {
    padding-left: 20px;
    padding-top: 15px;
    padding-bottom: 15px;
    padding-right: 20px;

    .form-control {
      height: 36px;
    }
  }

  &.closed {
    .wiki-list__wrapper,
    .wiki-list__scroll-container {
      width: 0;
    }
    .wiki-list__toggler {
      color: #73808D;
      svg {
        transform: scaleX(-1);
      }
    }
  }

  &.wiki-list--shadow {
    .wiki-list__scroll-container.stuck {
      .wiki-list__header {
        &:after {
          opacity: 1;
        }
      }
    }
  }
}

.wiki-list-simple {
  &__wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;


  }

  .wiki-list__width {
    width: 100%!important;
  }
}
