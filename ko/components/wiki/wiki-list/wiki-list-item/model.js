import { Wiki } from 'Models/wiki';

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.collapsed = params.collapsed;
    
    this.article = params.article;

    this.mode = params.mode || 'default';
    this.click = params.click;
    this.selected = params.selected;
    this.active = params.active;

    this.link = params.link;
    this.href =
      this.mode == 'link'
        ? (params.link || '').replace('{id}', this.article.id)
        : '';

    element.classList.toggle(
      'selected',
      ko.unwrap(this.selected) == this.article.id
    );

    if (ko.isObservable(this.selected)) {
      this.selected.subscribe((v) => {
        element.classList.toggle('selected', v == this.article.id);
      });
    }

    element.classList.toggle('disabled', !this.article.active());
    this.article.active.subscribe((v) => {
      element.classList.toggle('disabled', !v);
    });

    this.children = ko.pureComputed(() => {
      return this.article.children().filter((i) => {
        if (this.active) return i.active();
        return true;
      });
    });
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
