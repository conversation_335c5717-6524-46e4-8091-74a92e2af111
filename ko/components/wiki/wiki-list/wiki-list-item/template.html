<!-- ko if: !active || article.active  -->
<!-- ko let: { opened: ko.observable(!collapsed) } -->

<div class="wiki-list-item__data">
  <a class="wiki-list-item__title"
     data-bind="click: function() {
      if (mode == 'link') return true;
      click(article.id);
    },
    attr: {
      'href': href
    }">
    <span>
      <!-- ko text: article.title -->
      <!-- /ko -->

      <sup class="wiki-list-item__weight"
           data-bind="text: article.weight"></sup>
    </span>
  </a>

  <!-- ko if: children().length -->
  <div class="wiki-list-item__toggler f-transform--transition"
       data-bind="click: function() { opened(!opened()); }">
    <foquz-icon params="icon: 'arrow-bottom'"
                class="f-icon-sm"></foquz-icon>
  </div>
  <!-- /ko -->
</div>


<!-- ko if: article.children().length -->
<!-- ko template: {
    foreach: templateIf(opened(), $data),
    afterAdd: slideAfterAddFactory(400),
    beforeRemove: slideBeforeRemoveFactory(400)
  } -->
<div class="wiki-list-item__children">
  <!-- ko foreach: article.children -->
  <wiki-list-item params="
        article: $data,
        click: $component.click,
        mode: $component.mode,
        collapsed: $component.collapsed,
        link: $component.link,
        active: $component.active,
        selected: $component.selected"></wiki-list-item>
  <!-- /ko -->
</div>
<!-- /ko -->
<!-- /ko -->
<!-- /ko -->
<!-- /ko -->
