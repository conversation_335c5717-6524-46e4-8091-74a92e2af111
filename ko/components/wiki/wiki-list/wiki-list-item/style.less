@import 'Style/colors';

.wiki-list-item {
  display: block;
  margin-bottom: 2px;

  &__data {
    position: relative;
  }

  &__toggler {
    position: absolute;
    left: 2px;
    top: 8px;
    color: #a6b1bc;
    cursor: pointer;

    svg {
      width: 10px;
      height: 6px;
    }
  }

  &__title {
    display: flex;
    min-height: 30px;
    align-items: center;

    padding-top: 7px;
    padding-bottom: 7px;
    padding-left: 20px;
    padding-right: 7px;

    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    color: #000000;

    border-radius: 4px;

    &:hover,
    &:focus,
    &:active {
      background: #f1f5f6;
      text-decoration: none;
      color: @f-color-text;
    }

    span {
      display: inline-block;
      width: 100%;
    }
  }

  &__weight {
    font-size: 10px;
    color: #9bb0fb;
    font-weight: bold;
    margin-left: 4px;
  }

  &[data-level='1'] {
    & > .wiki-list-item__data {
      .wiki-list-item__title {
        font-size: 15px;
        font-weight: 500;
      }
    }
  }

  &[data-level='2'] {
    & > .wiki-list-item__data {
      .wiki-list-item__title {
        font-weight: 500;
      }
    }
  }

  &.disabled {
    & > .wiki-list-item__data .wiki-list-item__title {
      color: #8e99a3;
    }
  }

  &__children {
    margin-top: 2px;
    padding-left: 20px;
  }

  &.selected {
    & > .wiki-list-item__data {
      .wiki-list-item__title {
        background: #f1f5f6;
      }
    }
  }
}
