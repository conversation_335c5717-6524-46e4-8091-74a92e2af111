
<div class="wiki-list-simple__wrapper">
  <div class="wiki-list__header ">
    <div class="wiki-list__width-overflow">
      <div class="wiki-list__search wiki-list__width">
        <search-field params="value: query, placeholder: '', withoutIcon: true"></search-field>

      </div>
    </div>

  </div>

  <div class="wiki-list__content">
      <div class="h-100" data-bind="nativeScrollbar">
        <div class="wiki-list__width-overflow">
          <div class="wiki-list__items wiki-list__width">
            <!-- ko ifnot: articles().length -->
            <div class="text-center f-color-service">Ничего не найдено</div>
            <!-- /ko -->
            <!-- ko foreach: articles -->
            <wiki-list-item params="article: $data,
                click: $component.selectArticle,
                mode: $component.mode,
                link: $component.link,
                active: $component.active,
                selected: $component.articleId"></wiki-list-item>
            <!-- /ko -->
          </div>
        </div>
      </div>

  </div>
</div>
