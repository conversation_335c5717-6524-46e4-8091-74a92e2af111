<!-- ko let: { isSticky: ko.observable(false)} -->
<div class="wiki-list__toggler" data-bind="sticky">
  <div class="wiki-list-toggler"
       data-bind="click: toggle, tooltip, tooltipText: closed() ? 'Развернуть меню' : 'Свернуть меню'">
    <svg width="13"
         height="10"
         viewBox="0 0 13 10"
         fill="none"
         xmlns="http://www.w3.org/2000/svg">
      <path d="M12 0.999995L8 5L12 9"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round" />
      <path d="M5 0.999995L1 5L5 9"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round" />
    </svg>
  </div>
</div>



<div class="wiki-list__wrapper" data-bind="descendantsComplete: function() { $(window).resize().scroll(); }">

  <div class="wiki-list__scroll-container" data-bind="stickyResize: {
    parent: '.wiki-list'
  }">
    <div class="wiki-list__scroll-track">
      <div class="wiki-list__header ">
        <div class="wiki-list__width-overflow">
          <div class="wiki-list__search wiki-list__width">
            <search-field params="value: query, placeholder: '', withoutIcon: true"></search-field>

          </div>
        </div>

      </div>

      <div class="wiki-list__content">
          <div class="h-100" data-bind="nativeScrollbar">
            <div class="wiki-list__width-overflow">
              <div class="wiki-list__items wiki-list__width">
                <!-- ko if: loading -->
                <spinner></spinner>
                <!-- /ko -->

                <!-- ko if: !loading() && !articles().length -->
                <div class="text-center f-color-service">Ничего не найдено</div>
                <!-- /ko -->

                <!-- ko foreach: {
                  data: articles,
                  afterRender: function() { $(window).resize().scroll(); }
                } -->
                <wiki-list-item params="article: $data,
                    click: $component.selectArticle,
                    mode: $component.mode,
                    collapsed: $component.collapsed,
                    link: $component.link,
                    active: $component.active,
                    selected: $component.articleId"></wiki-list-item>
                <!-- /ko -->
              </div>
            </div>
          </div>

      </div>
    </div>
  </div>
</div>

<!-- /ko -->
