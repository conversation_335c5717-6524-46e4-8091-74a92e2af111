import { ViewModel } from './model';
import html from './template.html';
import htmlSimple from './template.simple.html';
import './style.less';

import './wiki-list-item';

import 'Bindings/sticky-resize'

ko.components.register('wiki-list', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('wiki-list');

      return new ViewModel(params, element);
    },
  },
  template: html,
});

ko.components.register('wiki-list-simple', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('wiki-list');
      element.classList.add('wiki-list-simple');

      return new ViewModel(params, element);
    },
  },
  template: htmlSimple,
});
