import { Wiki } from "Models/wiki";

const WikiListIsClosedField = "wiki_list_is_closed";
const isClosed = () => {
  return localStorage.getItem(WikiListIsClosedField) == 1;
};
const setClosed = (state) => {
  localStorage.setItem(WikiListIsClosedField, state ? 1 : 0);
};

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.collapsed = params.collapsed;

    this.loading = ko.observable(true);
    this.closed = ko.observable(isClosed());

    this.closed.subscribe((v) => {
      setClosed(v);
    });

    this.articles = ko.observableArray([]);
    this.articleId = params.articleId;

    this.query = ko.observable("");
    this.active = !!params.active;

    this.update = _.debounce(() => {
      return this.getList();
    }, 500);
    this.subscriptions.push(this.query.subscribe(() => this.update()));

    this.subscriptions.push(
      this.closed.subscribe((v) => element.classList.toggle("closed", v))
    );

    element.classList.toggle("closed", this.closed());

    element.classList.toggle(
      "wiki-list--shadow",
      "shadow" in params ? !!params.shadow : true
    );

    this.mode = typeof params.onSelect == "function" ? "default" : "link";
    this.link = params.link;

    this.selectArticle = (articleId) => {
      if (!articleId) {
        if (this.mode != "link") params.onSelect(null);
        return;
      }

      if (this.mode != "link") {
        params.onSelect(articleId);
      }
    };

    let autoSelect = params.autoSelect;

    this.getList().then(() => {
      this.loading(false);
      let selected = ko.unwrap(this.articleId);
      if (!selected && autoSelect) {
        let firstArticle = this.articles()[0];
        if (firstArticle) selected = firstArticle.id;
      }

      this.selectArticle(selected);
    });

    if (ko.isObservable(params.instance)) {
      params.instance({
        update: (id) => {
          this.loading(true);
          return this.getList().then(() => {
            this.loading(false);
            let selected = id;
            if (!selected && autoSelect) {
              let firstArticle = this.articles()[0];
              if (firstArticle) selected = firstArticle.id;
            }
            this.selectArticle(selected);
          });
        },
      });
    }
  }

  getList() {
    return Wiki.getList({
      q: this.query(),
    }).then((list) => {
      if (this.active) list = list.filter((a) => a.active());
      this.articles(list);
    });
  }

  toggle() {
    let isClosed = this.closed();
    this.closed(!isClosed);
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
