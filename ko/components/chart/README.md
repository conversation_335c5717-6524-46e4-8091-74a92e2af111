# Графики Highcharts.js

## series

Полностью берется из параметра `config.series`

```
series: [
  /* Серия 1 */
  {
    name: 'Серия 1',

    /* Данные серии */
    data: [
      {
        name: 'Отправлено',
        y: parseInt(data.sended) || 0
      },
      {
        name: 'Пройдено',
        y: parseInt(data.processed) || 0 + parseInt(data.done) || 0
      }
    ],

    dataLabels: {
      enabled: true,
      inside: true, // метка внутри столбика
      formatter: function () {
        return ((this.y / total) * 100).toFixed(0) + '%';
      }
    },

    /* раскрашивать столбики внутри серии */
    colorByPoint: true,
  }
]
```

Обновление только данных: `Chart.prototype.updateSeries`

## plotOptions[chart_type]

Объединение свойства `plotOptions` класса графика и параметра `config.plotOptions`
