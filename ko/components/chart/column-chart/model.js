import { Chart } from '../chart';

export class ViewModel extends Chart {
  get type() {
    return 'column';
  }

  get plotOptions() {
    return {
        maxPointWidth: 80,
    }
  }

  get xAxis() {
    return {
      labels: {
        y: 24,
        style: {
          fontFamily: '<PERSON><PERSON>, <PERSON>l, sans-serif'
        }
      }
    }
  }

  get yAxis() {
    return {
      ...super.yAxis,
      labels: {
        x: -8,
        style: {
          fontFamily: 'Roboto, Arial, sans-serif'
        }
      }

    }
  }
}
