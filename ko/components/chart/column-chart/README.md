# Funnel Chart

```
{
  chart: {
    height: 270, // высота графика
  },
  plotOptions: {
    column: {
      maxPointWidth: 80, // максимальная ширина колонки
      dataLabels: {
        enabled: true
      }
    }
  },

  xAxis: {
    type: 'category',
  },

  tooltip: {
    headerFormat: `Название сектора: {point.key}`,
    formatter: function() {
      return `${this.key} ${this.y} ${this.percentage}`;
    }
  },

  series: [
    {
      data: [
        {
          name: 'Отправлено',
          y: parseInt(data.sended) || 0
        },
        {
          name: 'Пройдено',
          y: parseInt(data.processed) || 0 + parseInt(data.done) || 0
        }
      ],
      dataLabels: {
        enabled: true,
        inside: true, // метка внутри столбика
        formatter: function () {
          return ((this.y / total) * 100).toFixed(0) + '%';
        }
      },
      colorByPoint: true, // раскрашивать столбики внутри серии
    }
  ]
}
```
