import { chartColors } from 'Data/chart-colors';
import { FoquzComponent } from 'Models/foquz-component';

import './style.less';
export class Chart extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.chartElement = ko.observable(null);
    this.chart = null;

    this.config = params.config;

    this.noChartData = ko.observable(this.isEmptyData());

    this.subscriptions = [];

    if (ko.isObservable(this.config)) {
      this.subscriptions.push(
        this.config.subscribe((v) => {
          this.update();
          this.noChartData(this.isEmptyData());
        })
      );
    }
  }

  isEmptyData() {
    let config = ko.toJS(this.config);
    return !config || !config.series || config.series.length == 0;
  }

  get colors() {
    return chartColors;
  }

  get chartOptions() {
    return {
      height: 270,
      spacingLeft: 0,
      style: {
        fontFamily: 'Roboto, Arial, sans-serif'
      }
    };
  }

  get plotOptions() {
    return {};
  }

  get series() {
    return ko.toJS(this.config).series;
  }

  get xAxis() {
    return {};
  }

  get yAxis() {
    return {
      title: {
        enabled: false
      },
    };
  }

  getChartConfig() {
    let config = ko.toJS(this.config);

    let keys = [
      'chart',
      'title',
      'subtitle',
      'series',
      'yAxis',
      'xAxis',
      'plotOptions'
    ];
    let otherProps = {};
    Object.keys(config)
      .filter((key) => !keys.includes(key))
      .forEach((key) => {
        otherProps[key] = config[key];
      });

    return {
      chart: {
        type: this.type,
        ...this.chartOptions,
        ...(config.chart || {})
      },

      colors: this.colors,

      title: {
        text: '',
        style: { display: 'none' },
        ...(config.title || {})
      },

      subtitle: {
        text: '',
        style: { display: 'none' },
        ...(config.subtitle || {})
      },

      credits: {
        enabled: false
      },

      legend: {
        enabled: false
      },

      yAxis: {
        ...this.yAxis,
        ...(config.yAxis || {})
      },

      xAxis: {
        ...this.xAxis,
        ...(config.xAxis || {})
      },

      series: this.series,

      plotOptions: {
        [this.type]: {
          ...this.plotOptions,
          ...(config.plotOptions || {})
        }
      },

      ...otherProps
    };
  }

  onRender() {
    if (this.noChartData()) return;
    let config = this.getChartConfig();

    console.log(this.type, config);

    this.chart = Highcharts.chart(this.chartElement(), config);
  }

  update() {
    if (this.noChartData()) return;
    if (!this.chart) {
      this.onRender();
      return;
    }

    this.chart.update(this.getChartConfig());
  }

  updateConfig(config) {
    this.config = {
      ...ko.toJS(this.config),
      ...config
    };

    this.noChartData(this.isEmptyData());
    this.update();
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
