import { Chart } from '../chart';

export class ViewModel extends Chart {
  get type() {
    return 'funnel';
  }

  get series() {
    let series = ko.toJS(this.config).series;
    return series;
  }

  get plotOptions() {
    return {
        neckWidth: 75,
        neckHeight: '40%',
        height: 180,
        width: 300,
        dataLabels: {
          connectorWidth: 0,
          style: {
            fontWeight: 400
          }
        }

    };
  }
}
