# Funnel Chart

```
{
  plotOptions: {
    funnel: {
      // ширина воронки
      width: 300,
      width: '100%',

      // высота воронки
      height: 300,
      height: '100%',

      // ширина нижней части
      neckWidth: 75,
      neckWidth: '35%',

      // высота нижней части
      neckHeight: 75,
      neckHeight: '45%',

      dataLabels: {
        connectorWidth: 0, // убрать коннекторы
      }
    }
  },

  tooltip: {
    headerFormat: `Название сектора: {point.key}`,
    formatter: function() {
      return `${this.key} ${this.y} ${this.percentage}`;
    }
  },

  series: [
    {

      // слои воронки сверху вниз
      data: [
        { name: 'name1', y: 30 },
        { name: 'name2', y: 20 },
        { name: 'name3', y: 10 },
      ],

    }
  ]
}
```
