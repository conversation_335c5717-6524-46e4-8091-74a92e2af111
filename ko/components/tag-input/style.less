

.clients__tag-input {
    .fc-popper__container {
        padding: 15px 20px!important;
    }
    &-more {
        display: flex;
        width: 20px !important;
        height: 20px !important;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: none!important;

        &:before {
            width: 18px !important;
            height: 18px !important;
            background-size: 100% 100% !important;
            background-repeat: no-repeat !important;
            content: '';
            display: inline-block;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='5' viewBox='0 0 18 5' fill='none'%3E%3Cpath d='M1.99219 2.50781H2.00219M9 2.50781H9.01M16 2.50781H16.01' stroke='%2373808D' stroke-width='4' stroke-linecap='round'/%3E%3C/svg%3E") !important;
            transform: rotate(0);
        }
    }
    &-content {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .clients__tag-input-item,
        .clients__tag-input-add-tag-button-wrapper {
            margin-top: 4px;
            margin-bottom: 4px;
            margin-right: 15px;
        }
    }

    &-item {
        font-weight: 500;
        white-space: nowrap;
        span {
            white-space: normal;
        }

        &-remove-button {
            margin-left: 7px;
            display: inline-flex;
            width: 20px !important;
            height: 20px !important;
            align-items: center;
            justify-content: center;

            border-radius: 50%;
            background: #ECEFF1 !important;
        }
  }


  &-add-tag-button {
        display: flex;
        width: 20px !important;
        height: 20px !important;
        align-items: center;
        justify-content: center;

        border-radius: 50%;
        background: #ECEFF1 !important;

        &-wrapper {
            display: flex;
            align-items: center;

        }
        &-label {
            margin-left: 12px;
            font-weight: 500;
            font-size: 12px;
            color: #2E2F31;
        }
  }


  &-dropdown {
      font-family: inherit;
      margin-top: 10px;
      box-shadow: 0 5px 20px rgba(115, 128, 141, 0.4);

        &[x-placement="top"] {
            .clients__tag-input-dropdown-menu {
                &:before {
                    bottom: -5px;
                    top: auto;
                    transform: rotate(180deg) translateX(50%);
                }
            }
        }

      &-menu {
        width: 240px;

        &-message {
            margin-top: 14px; 
        }

        &-list {
            max-height: 290px;
            margin-top: 20px;

            &.scroll-wrapper {
                margin-top: 14px;
            }

            .dropdown-item {
                color: #3f65f1 !important;
                cursor: pointer;
                white-space: normal;
                font-size: 14px !important;
                margin-bottom: 12px;
                padding-right: 0!important;
                padding-left: 0!important;
            }

            .scroll-element.scroll-y {
                right: 20px;
            }
        }
        &-control {
            height: 36px;
            padding: 9px 41px 9px 9px;

            font-size: 14px;

            &-wrapper {
                position: relative;
            }

            &-create-tag-button {            
                position: absolute;
                right: 13px;
                top: 50%;
                transform: translateY(-50%);
                line-height: 0;
                padding: 0;

                border-radius: 0;
                box-shadow: none !important;

                transition: none;
            }
        }

        &-message {
            display: block;

            font-weight: normal;
            font-size: 14px;
            color: #2E2F31;
        }
      }
  }
}
