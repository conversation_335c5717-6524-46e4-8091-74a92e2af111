<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <div style="height: 100%;" class="foquz-dialog__body d-flex flex-column">
    <div class="form-group">

      <input class="form-control clients__tag-input-dropdown-menu-control"
                               data-bind="textInput: dropdownMenuSearchTerm">

    </div>

    <div class="foquz-dialog__scroll flex-grow-1" data-bind="nativeScrollbar">
      <div style="height: 100%;">
        <!-- ko ifnot: dropdownMenuFilteredList().length -->
        <div class="f-color-service text-center pb-4">Ничего не найдено</div>
        <!-- /ko -->
        <!-- ko let: { addTag: (data) => {
              addTag(data);
              hide();
         } } -->
        <div class="" data-bind="nativeScrollbar">
          <!-- ko foreach: dropdownMenuFilteredList -->
          <botton
            class="d-block mt-10p mb-10p fc-btn-b--primary fc-btn-b--mode_text"
            data-bind="click: function() {
              addTag($data);
            }, text: $data.name"
          ></botton>
          <!-- /ko -->
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button"
              class="f-btn f-btn-link px-2"
              data-bind="
                click: function() {
                  hide();
                }">
        Отменить
      </button>
    </div>
  </div>
</foquz-dialog>
