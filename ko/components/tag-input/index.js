import "./tag-input-dialog"
import "./style.less"
import { DialogsModule } from 'Utils/dialogs-module';

import html from "./template.html"

let index = 0;
const currentIndex = ko.observable();

ko.components.register("clients-tag-input", {
  viewModel: {
    createViewModel: function ({
      afterAddTag,
      value,
      list,
      client_id,
      answer_id,
      client,
      addButton = { label: null },
      popover = {},
      maxItem,
      open,
    }, componentInfo) {
      const $element = $(componentInfo.element);

      $element.addClass(["clients__tag-input"]);

      const viewModel = new (function () {
        DialogsModule(this);

        this.index = ++index;

        this.tagInputPop = ko.observable(false);

        this.tagInputPop.subscribe((flag) => {
          this.dropdownMenuSearchTerm('');
          if (flag) currentIndex(this.index);
        })

        currentIndex.subscribe((index) => {
          if (index !== this.index) {
            this.tagInputPop(false);
          }
        })

        this.addBtnId = `clients__tag-input-${this.index}`;

        this.afterAddTag = afterAddTag;

        this.value = value;
        this.openable = typeof maxItem === 'number';
        this.maxItem = ko.observable(maxItem);
        this.open = (ko.isObservable(open) && open) || ko.observable(!this.openable);
        this.visibleValue = ko.computed(() => {
          return this.open()
            ? this.value()
            : this.value().slice(0, this.maxItem());
        });
        this.list = list;
        this.client_id = client_id;
        this.answer_id = answer_id;

        this.client = client;
        this.addButton = addButton;

        this.popoverOptions = popover;

        this.dropdownMenuSearchTerm = ko.observable("");

        this.isWatcher = ko.observable(!window.CURRENT_USER.admin);


        this.toggleOpen = function (flag) {
          if (!this.openable) return;
          this.open(flag);
        }

        this.autoTags = ko.computed(() => {
          if (typeof this.list === "function") {
            const list = this.list()
              .filter((t) => t.isAuto);
            return list;
          } else {
            return [];
          }
        });

        this.tags = ko.computed(() => {
          if (typeof this.list === "function") {
            const list = this.list()
              .filter((t) => !t.isAuto);
            list.sort((a, b) => (a.name < b.name ? -1 : 1));
            return list;
          } else {
            return [];
          }
        });

        this.avaibleTags = ko.computed(() => {
          const value = this.value();
          const list = this.tags().filter((t) => !value.find(({name}) => t.name === name));
          return list;
        });

        this.dropdownMenuFilteredList = ko.computed(() => {
          let list = this.avaibleTags();
          const search = this.dropdownMenuSearchTerm()
          if (search && (typeof search === 'string')) {
            const term = search.toLowerCase();
            list = list
              .filter((t) => t.name.toLowerCase().includes(term));
          }
          return list;
        });

        this.dropdownMenuFoundExactTagInFilteredList = ko.computed(() => {
          if (
            typeof this.dropdownMenuFilteredList === "function" &&
            this.dropdownMenuFilteredList()
          ) {
            return this.dropdownMenuFilteredList().find(
              (t) => this.dropdownMenuSearchTerm() === t.name
            );
          } else {
            return false;
          }
        });

        this.dropdownMenuFoundExactTagInValue = ko.computed(() => {
          const search = `${this.dropdownMenuSearchTerm()}`.trim();
          return this.value().find(
            (t) => search === `${t.name}`.trim()
          );
        });

        this.dropdownMenuFoundExactTagInAutoTags = ko.computed(() => {        
          return this.autoTags().find(
            (t) => this.dropdownMenuSearchTerm() === t.name
          );
        });

        this.dropdownMenuControlCreateTagError = ko.observable(false);
        this.serverError = ko.observable("");
        this.dropdownMenuControlCreateTagButtonDisabled = ko.computed(() => {
          return (
            this.dropdownMenuFoundExactTagInAutoTags() ||
            this.serverError() ||
            this.dropdownMenuFoundExactTagInFilteredList() ||
            this.dropdownMenuFoundExactTagInValue()
          );
        });

        this.dropdownMenuSearchTerm.subscribe((_) => {
          this.dropdownMenuControlCreateTagError(false);
          this.serverError("");
        });

        this.dropdownMenuControlCreateTagButtonTitle = ko.computed(() => {
          if (this.dropdownMenuFoundExactTagInValue()) {
            return "Тег уже выбран";
          } else if (this.dropdownMenuFoundExactTagInAutoTags()) {
            return "Тег уже добавлен в систему как автоматический. Автоматические теги для анкеты добавлять нельзя";
          } else if (this.dropdownMenuFoundExactTagInFilteredList()) {
            return "Тег уже существует";
          } else if (this.serverError()) {
            return this.serverError();
          } else {
            return "Создать новый тег";
          }
        });

        this.tagInputDialog  = function () {
          this.dropdownMenuSearchTerm('');
          const self = this;
          self.openDialog({
            name: 'tag-input-dialog',
            params: {
              dropdownMenuSearchTerm: self.dropdownMenuSearchTerm,
              dropdownMenuFilteredList: self.dropdownMenuFilteredList,
              addTag: self.addTag.bind(self),
            },
          })
        };

        this.dropdownMenuHidden = function () {
          this.dropdownMenuSearchTerm("");
        };

        this.createTag = function () {
          const name = this.dropdownMenuSearchTerm();

          if (this.dropdownMenuControlCreateTagButtonDisabled()) {
            this.dropdownMenuControlCreateTagError(true);
            return;
          }

          const newTag = {
            id: name,
            name: name,
          };
          const data = {
            tags: [newTag],
          };

          let url;

          if (this.client_id) {
            data.clients = [this.client_id];
            url = '/foquz/foquz-contact/add-tags';
          } else if (this.answer_id) {
            data.answers = [this.answer_id];
            url = `/foquz/api/poll/add-tags?access-token=${APIConfig.apiKey}`;
          }

          $.ajax({
            method: "POST",
            url,
            data,
            dataType: "json",
            success: () => {
              this.value.push(newTag);
              this.list.push(newTag);
              if (typeof this.afterAddTag === "function") {
                this.afterAddTag();
              }
              $(this.popover).popover("hide");
              this.tagInputPop(false);
              if (this.openable && !this.open() && (this.value().length >= 5)) {
                this.open(true);
              }
            },
            error: (response) => {
              const errors = response?.responseJSON?.errors || response?.errors || {};
              const text = Object.values(errors).join(', ') || 'Ошибка сервера';
              this.serverError(text);
              this.dropdownMenuControlCreateTagError(true);
            },
          });
        };

        this.removeTag = function (tag) {
          if (this.client_id || this.answer_id) {
            const data = {
              tags: [tag.id],
            };
            let url;
            if (this.client_id) {
              data.clients = [this.client_id];
              url = '/foquz/foquz-contact/remove-tags';
            } else if (this.answer_id) {
              data.answers = [this.answer_id];
              url = `/foquz/api/poll/remove-tags?access-token=${APIConfig.apiKey}`;
            }

            $.ajax({
              method: "POST",
              url,
              data,
              dataType: "json",
              success: () => {
                this.value.remove(tag);
              },
              error: (response) => {
                const errors = response?.responseJSON?.errors || response?.errors || {};
                const text = Object.values(errors).join(', ') || 'Ошибка сервера';
                this.serverError(text);
                this.dropdownMenuControlCreateTagError(true);
              },
            });
          } else {
            this.value.remove(tag);
          }
        };

        this.addTag = function (tag) {
          const name = tag.name || tag.tag; 
          if (this.client_id || this.answer_id) {
            const data = {
              tags: [{ id: name, name: name }],
            };
            let url;
            if (this.client_id) {
              data.clients = [this.client_id];
              url = '/foquz/foquz-contact/add-tags';
            } else if (this.answer_id) {
              data.answers = [this.answer_id];
              url = `/foquz/api/poll/add-tags?access-token=${APIConfig.apiKey}`;
            }

            $.ajax({
              method: "POST",
              url,
              data,
              dataType: "json",
              success: () => {
                this.value.push(tag);
                this.tagInputPop(false);
              },
              error: (response) => {
                const errors = response?.responseJSON?.errors || response?.errors || {};
                const text = Object.values(errors).join(', ') || 'Ошибка сервера';
                this.serverError(text);
                this.dropdownMenuControlCreateTagError(true);
              },
            });
          } else {
            this.value.push(tag);
          }
          if (this.openable && !this.open() && (this.value().length >= 5)) {
            this.open(true);
          }
        };
      })();

      return viewModel;
    },
  },
  template: html,
});
