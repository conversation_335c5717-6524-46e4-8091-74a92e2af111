<div
  class="clients__tag-input-content"
  data-bind="click: () => toggleOpen(!open())"
>
  <!-- ko ifnot: $component.isWatcher() -->
    <!-- ko foreach: {
      data: visibleValue,
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
      <span
        class="clients__tag-input-item"
        data-bind="css: { 'color-success': $data.isAuto }"
      >
        <span
          data-bind="text: $data.name || $data.tag"
        ></span>&nbsp;
        <!-- ko ifnot: $data.isAuto -->
          <button
            class="btn btn-icon btn-default clients__tag-input-item-remove-button"
            data-bind="
              click: function() { $component.removeTag($data) },
              clickBubble: false,
            "
          >
            <fc-icon params="name: 'plus', color: 'danger', size: 8" style="transform: rotate(45deg);"></fc-icon>
          </button>
        <!-- /ko -->
      </span>
    <!-- /ko -->
  <!-- /ko -->
  <!-- ko if: $component.isWatcher() -->
    <!-- ko let: { tagsStreengs: visibleValue().map(tag => tag.name || tag.tag).join(', ').split(' ') } -->
      <!-- ko foreach: {
        data: tagsStreengs,
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
        <span class="mr-5p"
          data-bind="
            text: $data,
            css: {
              'mr-5p': ($index() < (tagsStreengs.length - 1)),
              'mr-15p': ($index() === (tagsStreengs.length - 1)),
            }
          "
        ></span>
      <!-- /ko -->
    <!-- /ko -->
  <!-- /ko -->

  <!-- ko template: {
    foreach: templateIf(openable && !open() && (value().length > 5)),
    afterAdd: fadeAfterAddFactory(200, 200),
    beforeRemove: fadeBeforeRemoveFactory(0)
  } -->
  <span class="clients__tag-input-add-tag-button-wrapper">
    <button
      type="button"
      class="btn btn-circle clients__tag-input-more"
      data-bind="
        click: () => $component.toggleOpen(true),
        clickBubble: false,
      "
      title="показать всё"
    ></button>
  </span>
  <!-- /ko -->

  <!-- ko if: ((addButton !== null) && !isWatcher()) -->
    <span
      class="clients__tag-input-add-tag-button-wrapper clients__tag-input-add-tag-button-wrapper_desctop d-none d-md-flex"
    >
      <!-- ko let: {
        addTag: (d) => addTag(d),
        tagInputPop: tagInputPop,
      } -->
      <button
        type="button"
        class="btn btn-circle clients__tag-input-add-tag-button"
        title="Добавить теги"
        data-bind="
          click: (_, e) => {
            e.stopPropagation();
            tagInputPop(!tagInputPop());
          },
          attr: { id: addBtnId },
        "
      >
        <fc-icon params="name: 'plus', size: 8"></fc-icon>
      </button>
      <fc-popper
        params="
          target: addBtnId,
          show: tagInputPop,
          options: {
              placement: 'bottom',
              control: true,
          },
          behavior : {
            interactive: true
          }
        "
      >
        <div class="clients__tag-input-dropdown-menu">
          <div class="clients__tag-input-dropdown-menu-control-wrapper">
            <input
              class="form-control clients__tag-input-dropdown-menu-control"
              data-bind="textInput: dropdownMenuSearchTerm"
            />

            <!-- ko if: dropdownMenuSearchTerm().length !== 0 -->
              <button
                class="btn clients__tag-input-dropdown-menu-control-create-tag-button"
                data-bind="
                  click: () => createTag(),
                  attr: {
                    disabled: dropdownMenuControlCreateTagButtonDisabled() && dropdownMenuControlCreateTagError(),
                    title: dropdownMenuControlCreateTagButtonTitle
                  }
                "
              >
                <fc-icon params="name: 'plus', size: 16"></fc-icon>
              </button>
            <!-- /ko -->
          </div>

          <!-- ko if: dropdownMenuControlCreateTagError -->
              <span class="form-error" data-bind="text: dropdownMenuControlCreateTagButtonTitle"></span>
          <!-- /ko -->

          <!-- ko if: dropdownMenuFilteredList().length > 0 -->
            <div class="clients__tag-input-dropdown-menu-list" data-bind="fScrollbar: { onlyY: true, gradient: true }">
              <!-- ko template: {
                  foreach: dropdownMenuFilteredList,
              } -->
                <a
                  class="dropdown-item"
                  data-bind="
                    click: () => { tagInputPop(false); addTag($data); },
                    text: $data.name
                  "
                ></a>
              <!-- /ko -->
            </div>
          <!-- /ko -->

          <!-- ko if: dropdownMenuFilteredList().length === 0 -->
            <span class="clients__tag-input-dropdown-menu-message">Совпадений не найдено</span>
          <!-- /ko -->
        </div>
      </fc-popper>
      <!-- /ko -->

      <!-- ko if: addButton.label !== null && value().length === 0 -->
        <span
          class="clients__tag-input-add-tag-button-label"
          data-bind="text: addButton.label"
        ></span>
      <!-- /ko -->
    </span>
    <span
      class="clients__tag-input-add-tag-button-wrapper clients__tag-input-add-tag-button-wrapper_mobile d-md-none"
      data-bind="
        click: (_, e) => {
          e.stopPropagation();
          tagInputDialog();
        }
      "
    >
      <button
        type="button"
        class="btn btn-circle clients__tag-input-add-tag-button"
        title="Добавить теги"
      >  
       <fc-icon params="name: 'plus', size: 8"></fc-icon>
      </button>
      <!-- ko if: addButton.label !== null && value().length === 0 -->
        <span
          class="clients__tag-input-add-tag-button-label"
          data-bind="text: addButton.label"
        ></span>
      <!-- /ko -->
    </span>
  <!-- /ko -->
</div>
