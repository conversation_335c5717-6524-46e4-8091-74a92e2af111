<div class="row">
  <div class="col-6">
    <div class="form-group">
      <label class="form-label">По дате рождения</label>
      <select data-bind="
          value: model.type,
          select2: {
              containerCssClass: 'form-control',
              wrapperCssClass: 'select2-container--form-control',
              allowClear: true,
              placeholder: 'Не выбрано'
          }
      ">
        <option></option>
        <option value="1">День рождения</option>
        <option value="2">Период</option>
        <option value="3">Исполнилось на дату</option>
        <option value="4">Месяц рождения</option>
        <option value="5">Дата рождения</option>
      </select>
    </div>
  </div>

  <div class="col-6">
    <div class="row flex-nowrap">

      <!-- ko template: {
        foreach: templateIf(model.type() === '1'), $data),
        afterAdd: fadeAfterAddFactory(200)
      } -->
      <div class="col-12 mailings__details-modal-dialog-filter">
        <div class="form-group">
          <label class="form-label">Дата</label>

          <date-picker params="value: model.date"></date-picker>


        </div>
      </div>
      <!-- /ko -->

      <!-- ko template: {
        foreach: templateIf(model.type() === '3', $data),
        afterAdd: fadeAfterAddFactory(200)
      } -->
      <div class="col-auto mailings__details-modal-dialog-filter">
        <div class="form-group">
          <label class="form-label">Дата</label>

          <date-picker params="value: model.date"></date-picker>


        </div>
      </div>
      <!-- /ko -->

      <!-- ko template: {
        foreach: templateIf(model.type() === '2', $data),
        afterAdd: fadeAfterAddFactory(200)
      } -->
      <div class="col-auto mailings__details-modal-dialog-filter">
        <div class="form-group">
          <label class="form-label">Период</label>

          <div class="input-group date-input-group"
               data-bind="dateInputGroup">
            <!-- ko let: { autosizeInput: ko.observable(null) } -->
            <input class="form-control"
                   placeholder="00.00.0000-00.00.0000"
                   style="min-width: 80px"
                   data-bind="value: model.period, mask, maskPattern: '00.00.0000-00.00.0000', autosizeInput: autosizeInput, periodPicker, periodPickerArrowPosition: { anchor: 'right', offset: -10 }, periodPickerSeparator: '-', periodPickerApply: function () { autosizeInput().update(); }">
            <!-- /ko -->
            <i class="date-input-group__icon"></i>
          </div>
        </div>
      </div>
      <!-- /ko -->

      <!-- ko template: {
        foreach: templateIf(model.type() === '3', $data),
        afterAdd: fadeAfterAddFactory(200)
      } -->
      <div class="col-auto mailings__details-modal-dialog-filter">
        <div class="form-group"
             style="width: 100px">
          <label class="form-label">Возраст, лет</label>

          <input class="form-control"
                 placeholder="00"
                 data-bind="textInput: model.age, mask, maskPattern: '00'">
        </div>
      </div>
      <!-- /ko -->

      <!-- ko template: {
        foreach: templateIf(model.type() === '5', $data),
        afterAdd: fadeAfterAddFactory(200)
      } -->
      <div class="col-auto pr-0 mailings__details-modal-dialog-filter">
        <div class="form-group"
             style="width: 55px">
          <label class="form-label">День</label>

          <input class="form-control px-1 text-center"
                 placeholder="00"
                 data-bind="textInput: model.day, mask, maskPattern: '00', numericIntervalField: { min: 1, max: 31 }, css: {
            'is-invalid': $component.formControlErrorStateMatcher(model.day),
          }">
        </div>
      </div>
      <!-- /ko -->

      <!-- ko template: {
        foreach: templateIf(['4', '5'].includes(model.type()), $data),
        afterAdd: fadeAfterAddFactory(200)
      } -->
      <div class="col-auto mailings__details-modal-dialog-filter">

        <div class="form-group"
             style="width: 220px">
          <label class="form-label">Месяц</label>
          <select data-bind="
            value: model.month,
            select2: {
              containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                minimumResultsForSearch: 0,
            }
            ">
            <option value="1">Январь</option>
            <option value="2">Февраль</option>
            <option value="3">Март</option>
            <option value="4">Апрель</option>
            <option value="5">Май</option>
            <option value="6">Июнь</option>
            <option value="7">Июль</option>
            <option value="8">Август</option>
            <option value="9">Сентябрь</option>
            <option value="10">Октябрь</option>
            <option value="11">Ноябрь</option>
            <option value="12">Декабрь</option>
          </select>
        </div>
      </div>
      <!-- /ko -->
    </div>

    <validation-feedback params="show: $component.formControlErrorStateMatcher($component.model), error: $component.model.error"
                         style="top: -26px; position: relative;"></validation-feedback>

  </div>
</div>
