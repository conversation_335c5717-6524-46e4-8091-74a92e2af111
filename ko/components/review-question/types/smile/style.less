.review-question-view--type_smile {
  .smile-result + .review-question-view-comment {
    margin-top: 16px !important;
  }

  .smile {
    width: 35px!important;
    height: 35px;
    object-fit: contain;
  }
}
.review-question-view-clarifying-question {
  &.new-type {
    margin-top: 25px;
    &.external {
      margin-left: 40px;
      .review-question-view-variant {
        padding: 0;
        background: none;
      }
    }
  }
}