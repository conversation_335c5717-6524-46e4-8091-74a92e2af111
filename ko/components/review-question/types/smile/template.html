<!-- ko using: question -->
<div class="review-question-view review-question-view--type_smile">
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
    <!-- ko if: $data.linkWithClientField -->
    <small class="service-text" data-bind="text: _t('main', 'связан с параметром {param} контакта', {
      param: linkedClientField
    })"></small>
    <!-- /ko -->
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <!-- ko if: text !== null -->
  <div class="review-question-view__text" data-bind="text: text">
  </div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <!-- ko ifnot: hasAnswer -->
    <div class="review-question-view-block skip-block">
      <div
        class="review-question-view-block__text review-question-view-block__text--skipped"
        data-bind="
          text: $component.isHidden ?
            'Вопрос для респондента не отображался' :
            'Вопрос пропущен',
        "
      ></div>
    </div>
    <!-- /ko -->

    <!-- ko if: hasAnswer -->
      <div class="review-question-view__answer-section">
        <!-- ko if: gallery.length -->
          <div
            class="review-question-view__gallery f-color-text ml-n5p mt-n1 mb-4 d-flex align-items-center cursor-pointer"
            data-bind="fancyboxGalleryItem: {
            gallery: $component.sortGallery(gallery).map(function(i) {
              return {
                src: i.url,
                opts: {
                  caption: i.description
                }
              }
            }),
            index: 0,
            noCursor: true,
            }"
          >
            <span class="f-icon f-icon--picture mr-2">
              <svg>
                <use href="#picture-icon"></use>
              </svg>
            </span>
            <span
              class="f-color-primary f-fs-3 bold"
              data-bind="text: _t('answers', 'Галерея')"
            ></span>
          </div>
        <!-- /ko -->
        <!-- ko ifnot: skipped -->
          <!-- ko if: smile -->
          <div class="d-flex align-items-center smile-result">
            <div class="mr-3">
              <div class="img-rating img-rating--sm" data-bind="css: 'img-rating--' + smileType">
                <div class="img-rating__icon">
                  <img data-bind="attr: { src: smile.url }" class="smile">
                </div>

              </div>

            </div>
            <div class="f-fs-3 f-color-text bold" data-bind="text: smile.label"></div>
          </div>
          <!-- /ko -->
        <!-- /ko -->
      
        <!-- ko if: skipped -->
          <div class="review-question-stars__skipped" data-bind="text: _t('answers', 'Респондент отказался от оценки')"></div>
        <!-- /ko -->
      </div>
    <!-- /ko -->
  </div>
  <!-- ko if: clarifyingQuestion && !skipped && hasAnswer -->
  <!-- Уточняющий вопрос -->
        <!-- ko template: { name: 'review-question-view-clarifying-question-template-new', data: $data } -->
        <!-- /ko -->
  <!-- /Уточняющий вопрос -->
  <!-- /ko -->
  <!-- ko if: answer -->
  <!-- ko template: {
    name: 'review-question-view-comment-template',
    data: { text: comment }
  } -->
  <!-- /ko -->
  <!-- /ko -->
</div>
<!-- /ko -->
