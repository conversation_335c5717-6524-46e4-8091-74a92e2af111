<!-- ko using: question -->
<div class="review-question-view"
     data-bind="css: 'review-questions-view--type_' + type">
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>

    <!-- ko if: linkWithClientField -->
    <small class="service-text"
           data-bind="text: _t('main', 'связан с параметром {param} контакта', {
            param: linkedClientField
          })"></small>
    <!-- /ko -->
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <!-- ko if: $parent.review.withPoints && correctVariant -->
  <div class="review-question-view__meta">
    <!-- ko if: hasAnswer -->
    <div class="mr-4 mb-2">
      <span class="f-color-service" data-bind="text: _t('Баллов за ответ') + ':'"></span>
      <span
        data-bind="html: _t('main', '{num1} из {num2}', {
        num1: '<span class=\'bold\'>' + (answerPoints || 0) + '</span>',
        num2: '<span class=\'bold\'>' + (maxPoints || 0) + '</span>',
      }), css: {'empty': answerPoints == 0 || !answerPoints}"
      ></span>
      <!-- ko if: answerPoints == 0 || !answerPoints -->
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0 7C0 8.933 0.783502 10.683 2.05025 11.9497C3.317 13.2165 5.067 14 7 14C10.866 14 14 10.866 14 7C14 5.067 13.2165 3.317 11.9497 2.05025C10.683 0.783502 8.933 0 7 0C3.13401 0 0 3.13401 0 7Z" fill="#FF0200"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3 7C3 6.44772 3.44772 6 4 6H10C10.5523 6 11 6.44772 11 7C11 7.55228 10.5523 8 10 8H4C3.44772 8 3 7.55228 3 7Z" fill="white"/>
      </svg>
      <!-- /ko -->
    </div>
    
    <div class="mb-2">
      <span class="f-color-service" data-bind="text: _t('Правильный ответ') + ':'"></span>
      <span data-bind="text: correctVariant"></span>
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->

  <!-- ko if: text -->
  <div class="review-question-view__text"
       data-bind="text: text">
  </div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko if: answer -->
      <div class="review-question-view-block">
        <div class="review-question-view-block__text">
          <span data-bind="html: answer"></span>
          <!-- ko if: isCorrectAnswer -->
          <svg-icon params="name: 'check'" class="svg-icon--sm f-color-text"></svg-icon>
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
        <div class="review-question-view-block skip-block">
          <div
            class="review-question-view-block__text review-question-view-block__text--skipped"
            data-bind="
              text: $component.isHidden ?
                'Вопрос для респондента не отображался' :
                'Вопрос пропущен',
            "
          ></div>
        </div>
      <!-- /ko -->
    </div>
  </div>
</div>
<!-- /ko -->
