<!-- ko using: question -->
<div
  class="review-question-view"
  data-bind="css: 'review-questions-view--type_' + type + ' '"
>
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
      <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
       <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
  </div>

  <!-- ko if: subDescription -->
    <div
      class="review-question-view__subdescription service-text"
      data-bind="text: subDescription"
    ></div>
  <!-- /ko -->

  <!-- ko if: text -->
    <div class="review-question-view__text" data-bind="text: text"></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko if: hasAnswer -->
        <!-- ko if: gallery.length -->
          <div
            class="f-color-text mb-4 d-flex align-items-center cursor-pointer"
            data-bind="
              fancyboxGalleryItem: {
                gallery: fancyGallery,
                index: 0,
                noCursor: true,
              },
            "
          >
            <span class="f-icon f-icon--picture mr-2">
              <svg>
                <use href="#picture-icon"></use>
              </svg>
            </span>
            <span
              class="f-color-primary f-fs-3 bold"
              data-bind="text: _t('answers', 'Галерея')"
            ></span>
          </div>
        <!-- /ko -->
        <!-- ko if: skipped -->
          <div
            class="review-question-stars__skipped"
            data-bind="text: _t('answers', 'Респондент отказался от оценки')"
          ></div>
        <!-- /ko -->
        <!-- ko ifnot: skipped -->
          <!-- ko foreach: { data: categories, as: 'category' } -->
            <div class="mb-15p">
              <div
                data-bind="text: category.name"
                class="mb-10p f-color-service f-fs-1"
              ></div>
              <!-- ko foreach: { data: category.cards, as: 'card' } -->
                <div
                  data-bind="
                    text: card.variant,
                    css: {'review-question-view-card-sorting__card': !window.IS_EXTERNAL}
                  "
                ></div>
              <!-- /ko -->
            </div>
          <!-- /ko -->
          <!-- ko if: variantsWithoutCategory.length -->
            <div class="mb-10p f-color-service f-fs-1">
              Без категории
            </div>
            <!-- ko foreach: { data: variantsWithoutCategory, as: 'card' } -->
              <div
                data-bind="
                  text: card.variant,
                  css: {'review-question-view-card-sorting__card review-question-view-card-sorting__card_without-category': !window.IS_EXTERNAL}
                "
              ></div>
            <!-- /ko -->
          <!-- /ko -->
        <!-- /ko -->
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
        <div class="review-question-view-block skip-block">
          <div
            class="review-question-view-block__text review-question-view-block__text--skipped"
            data-bind="
              text: $component.isHidden ?
                'Вопрос для респондента не отображался' :
                'Вопрос пропущен',
            "
          ></div>
        </div>
      <!-- /ko -->
    </div>
  </div>

  <!-- ko template: {
    name: 'review-question-view-comment-template',
    data: { text: comment || '' }
  } -->
  <!-- /ko -->
</div>
<!-- /ko -->
