import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('review-question-stars', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('review-question-stars');

      const model = new ViewModel(params, element);

      return model;
    },
  },
  template: html,
});
