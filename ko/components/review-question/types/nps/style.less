@import "Style/breakpoints";

.review-question-view-clarifying-question {
  &.new-type {
    margin-top: 10px;
    &.external {
      margin-left: 40px;
      .review-question-view-variant {
        padding: 0;
        background: none;
      }
    }
  }
}
.review-question-nps {
  
  &__skipped {
    font-size: 16px;
    font-weight: 900;
    margin-top: 12px;

    .only-mobile({
            margin-top: 6px;
        });
  }

  .star-variants {
    width: 100%;
    max-width: 660px;
  }
  .review-question-stars__skipped-variant {
    font-weight: 700;
    font-size: 12px;
    line-height: 110%;
    color: #2E2F31;
  }

  .star-variant {
    border-top: 1px solid #e7ebed;
    padding: 12px 15px;

    &--deleted {
      
    }

    .removed {
      font-style: italic;
      color: var(--f-color-danger);
      font-size: 12px;
    }

    td {
      vertical-align: middle;
      padding: 8px 10px;

    }

    &:last-child {
      border-bottom: 1px solid #e7ebed;
    }

    &__text {
      padding-right: 15px;
      flex-grow: 1;
      font-size: 15px;
      font-weight: 400;
      line-height: 18px;
    }

    &__label {
      padding-left: 15px;
      font-weight: 500;
    }

    &__rating {
      .nps-scale__item {
        height: auto;
        width: auto;
        font-size: 13px;
        font-weight: 400;
        line-height: 14px;
        padding: 3px 8px;
        margin-left: unset;
        margin-right: unset;
      }
    }

    .nps-scale__list {
      margin-left: unset;
      margin-right: unset;
    }
  }

  .nps-scale {
    &.nps-scale--review {
      width: unset;
      .nps-scale__item {
        opacity: 1;
      }
    }
  }
}
