<!-- ko using: question -->
<div
  class="review-question-view review-questions-view--type_rating"
>
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko if: hasAnswer -->
      <!-- ko if: gallery.length -->
      <div
        class="review-question-view__gallery f-color-text ml-n5p mt-n1 mb-4 d-flex align-items-center cursor-pointer"
        data-bind="fancyboxGalleryItem: {
        gallery: $component.sortGallery(gallery).map(function(i) {
          return {
            src: i.url,
            opts: {
              caption: i.description
            }
          }
        }),
        index: 0,
        noCursor: true,
        }"
      >
        <span class="f-icon f-icon--picture mr-2">
          <svg>
            <use href="#picture-icon"></use>
          </svg>
        </span>
        <span
          class="f-color-primary f-fs-3 bold"
          data-bind="text: _t('answers', 'Галерея')"
        ></span>
      </div>
      <!-- /ko -->

      <!-- ko if: skipped -->
      <div
        class="review-question-stars__skipped"
        data-bind="text: _t('answers', 'Респондент отказался от оценки')"
      ></div>
      <!-- /ko -->
      <!-- ko ifnot: skipped -->
      <!-- Рейтинг -->
      <div class="review-question-view-rating">
        <div class="rating-answer">
          <div
            class="rating-answer__value"
            data-bind="text: rating || '–', style: {
              'background': config.color
            }"
          ></div>
          <!-- ko if: config.labelsArray[rating-1] -->
          <div
            class="rating-answer__label"
            data-bind="text: config.labelsArray[rating-1]"
          ></div>
          <!-- /ko -->
        </div>
      </div>
      <!-- /Рейтинг -->

      <!-- Уточняющий вопрос -->
      <!-- ko template: { name: 'review-question-view-clarifying-question-template', data: $data } -->
      <!-- /ko -->
      <!-- /Уточняющий вопрос -->
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
      <div class="review-question-view-block skip-block">
        <div
          class="review-question-view-block__text review-question-view-block__text--skipped"
          data-bind="
            text: $component.isHidden ?
              'Вопрос для респондента не отображался' :
              'Вопрос пропущен',
          "
        ></div>
      </div>
      <!-- /ko -->
    </div>
  </div>
  <!-- Комментарий -->
  <!-- ko template: { name: 'review-question-view-comment-template', data: { text: comment } } -->
  <!-- /ko -->
  <!-- /Комментарий -->
</div>
<!-- /ko -->
