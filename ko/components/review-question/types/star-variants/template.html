<!-- ko using: question -->
<div class="review-question-view review-questions-view--type_star-variants">
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko if: hasAnswer -->
      <!-- ko if: gallery.length -->
      <div
        class="review-question-view__gallery f-color-text ml-n5p mt-n1 mb-4 d-flex align-items-center cursor-pointer"
        data-bind="fancyboxGalleryItem: {
        gallery: $component.sortGallery(gallery).map(function(i) {
          return {
            src: i.url,
            opts: {
              caption: i.description
            }
          }
        }),
        index: 0,
        noCursor: true,
        }"
      >
        <span class="f-icon f-icon--picture mr-2">
          <svg>
            <use href="#picture-icon"></use>
          </svg>
        </span>
        <span
          class="f-color-primary f-fs-3 bold"
          data-bind="text: _t('answers', 'Галерея')"
        ></span>
      </div>
      <!-- /ko -->

      <!-- ko if: skipped -->
      <div
        class="review-question-stars__skipped"
        data-bind="text: _t('answers', 'Респондент отказался от оценки')"
      ></div>
      <!-- /ko -->
      <!-- ko ifnot: skipped -->

      <!-- ko if: clarifyingQuestion && !window.IS_EXTERNAL -->
      <div class="review-question-view-clarifying-question__text">
        <!-- ko text: clarifyingQuestion -->
        <!-- /ko -->
        <!-- ko if: $data.forAllRates || (!starRatingOptions.extra_question_rate_from && !starRatingOptions.extra_question_rate_to) -->
        <small class="service-text" data-bind="text: '(' + _t('для всех оценок') +')'"></small>
        <!-- /ko -->
        <!-- ko ifnot: $data.forAllRates || (!starRatingOptions.extra_question_rate_from && !starRatingOptions.extra_question_rate_to) -->
        <small class="service-text" data-bind="text: '(' + _t(`для оценок ${starRatingOptions.extra_question_rate_from}-${starRatingOptions.extra_question_rate_to}`) +')'"></small>
        <!-- /ko -->
      </div>
      <!-- /ko -->

      <div class="star-variants" data-bind="log">
        <!-- ko foreach: { data: variants, as: 'variant' } -->
        <div
          class="star-variant"
          data-bind="css: {
          'star-variant--deleted': variant.deleted,
          'star-variant--selected': $parent.answerData[variant.id] > 0
        }"
        >
          <div class="star-variant__text">
            <span data-bind="text: variant.text"></span>
            <!-- ko if: variant.deleted -->
            <span class="removed">(Удален)</span>
            <!-- /ko -->
          </div>
          <div class="star-variant__rating">
            <!-- ko if: isNaN($parent.answerData[variant.id]) -->
            <div class="scale-variant__no-value review-question-stars__skipped" data-bind="text: _t('answers', 'Респондент отказался от оценки')"></div>
            <!-- /ko -->
            <!-- ko ifnot: isNaN($parent.answerData[variant.id]) -->
            <star-rating
              params="
                count: $parent.config.count,
                value: $parent.answerData[variant.id],
                label: false
              "
            ></star-rating>
            <!-- /ko -->
          </div>

          <div class="star-variant__label">
            <span
              data-bind="text: $parent.config.labelsArray[$parent.answerData[variant.id] - 1]"
            ></span>
          </div>
        </div>
        <!-- ko if: $parent.clarifyingQuestion && window.IS_EXTERNAL -->
        <div class="review-star-variant__clarifying-row-text" data-bind="text: $parent.clarifyingQuestion">
        </div>
        <!-- /ko -->
        <!-- Уточняющий вопрос -->
        <!-- ko template: { name: 'review-question-variant-view-clarifying-question-template', data: $parent } -->
        <!-- /ko -->
        <!-- /Уточняющий вопрос -->
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
      <div class="review-question-view-block skip-block">
        <div
          class="review-question-view-block__text review-question-view-block__text--skipped"
          data-bind="
            text: $component.isHidden ?
              'Вопрос для респондента не отображался' :
              'Вопрос пропущен',
          "
        ></div>
      </div>
      <!-- /ko -->
    </div>
  </div>
  <!-- Комментарий -->
  <!-- ko template: { name: 'review-question-view-comment-template', data: { text: comment } } -->
  <!-- /ko -->
  <!-- /Комментарий -->
</div>
<!-- /ko -->
