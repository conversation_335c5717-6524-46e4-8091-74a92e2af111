import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('review-question-star-variants', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('review-question-star-variants');

      const model = new ViewModel(params, element);

      return model;
    },
  },
  template: html,
});
