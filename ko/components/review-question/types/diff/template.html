<!-- ko using: question -->
<div class="review-question-view review-question-view--type_diff">
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko ifnot: hasAnswer -->
        <div class="review-question-view-block skip-block">
          <div
            class="review-question-view-block__text review-question-view-block__text--skipped"
            data-bind="
              text: $component.isHidden ?
                'Вопрос для респондента не отображался' :
                'Вопрос пропущен',
            "
          ></div>
        </div>
      <!-- /ko -->
      <!-- ko if: hasAnswer -->
      <!-- ko if: gallery.length -->
      <div
        class="f-color-text mb-4 d-flex align-items-center cursor-pointer"
        data-bind="fancyboxGalleryItem: {
        gallery: $component.sortGallery(gallery).map(function(i) {
          return {
            src: i.url,
            opts: {
              caption: i.description
            }
          }
        }),
        index: 0,
        noCursor: true,
        }"
      >
        <span class="f-icon f-icon--picture mr-2">
          <svg>
            <use href="#picture-icon"></use>
          </svg>
        </span>
        <span
          class="f-color-primary f-fs-3 bold"
          data-bind="text: _t('answers', 'Галерея')"
        ></span>
      </div>
      <!-- /ko -->

      
      <!-- ko if: skipped -->
      <div
        class="review-question-stars__skipped"
        data-bind="text: _t('answers', 'Респондент отказался от оценки')"
      ></div>
      <!-- /ko -->

      <!-- ko ifnot: skipped -->

      <div class="mb-4">
        <table
          class="table f-table f-table-dense f-color-text w-auto diff-table"
        >
          <thead>
            <tr>
              <th width="120" class="d-none d-md-table-cell"></th>
              <!-- ko foreach: { data: ['1', '2', '3', '4', '5'], as: 'column' } -->
              <th width="60" data-bind="text: column" align="center"></th>
              <!-- /ko -->
              <th width="120" class="d-none d-md-table-cell"></th>
            </tr>
          </thead>

          <tbody>
            <!-- ko foreach: { data: differentialRows, as: 'row' } -->
            <tr class="d-md-none">
              <td colspan="5">
                <div class="d-flex justify-content-between">
                  <div>
                    <span
                      class="font-weight-700 f-fs-1"
                      data-bind="text: row.start_label"
                    ></span>
                  </div>
                  <div>
                    <span
                      class="font-weight-700 f-fs-1"
                      data-bind="text: row.end_label"
                    ></span>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              data-bind="let: { rowAnswer: $parent.answer.answer.answer ? $parent.answer.answer.answer[row.id] : '' }"
            >
              <td
                valign="middle"
                align="right"
                class="py-1 d-none d-md-table-cell"
              >
                <span
                  class="font-weight-700 f-fs-1"
                  data-bind="text: row.start_label"
                ></span>
              </td>

              <!-- ko foreach: { data: ['1','2','3','4','5'], as: 'column' } -->
              <td
                align="center"
                valign="middle"
                data-bind="let: { checked: rowAnswer == column }"
                class="py-1"
              >
                <!-- ko if: checked -->
                <!-- ko if: !window.IS_EXTERNAL -->
                <span class="f-icon f-icon--check f-icon-success">
                  <svg>
                    <use href="#check-icon"></use>
                  </svg>
                </span>
                <!-- /ko -->
                <!-- ko ifnot: !window.IS_EXTERNAL -->
                <span class="f-icon f-icon--check f-icon-success">
                  <svg>
                    <use href="#check-icon-2"></use>
                  </svg>
                </span>
                <!-- /ko -->
                <!-- /ko -->
                <!-- ko ifnot: checked -->
                <span class="f-icon-dash"></span>
                <!-- /ko -->
              </td>
              <!-- /ko -->

              <td valign="middle" class="py-1 d-none d-md-table-cell">
                <span
                  class="font-weight-700 f-fs-1"
                  data-bind="text: row.end_label"
                ></span>
              </td>
            </tr>
            <!-- /ko -->
          </tbody>
        </table>
      </div>
      <!-- /ko -->
      <!-- /ko -->
    </div>
  </div>
  <!-- ko if: answer -->
  <!-- ko template: {name: 'review-question-view-comment-template', data: {text: answer.answer.comment }} -->
  <!-- /ko -->
  <!-- /ko -->
</div>
<!-- /ko -->
