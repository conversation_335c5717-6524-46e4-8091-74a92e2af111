import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('review-question-distribution-scale', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('review-question-distribution-scale');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
