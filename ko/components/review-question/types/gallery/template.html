<!-- ko using: question -->
<div
  class="review-question-view"
  data-bind="css: 'review-questions-view--type_' + type"
>
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>

    <!-- ko if: linkWithClientField -->
    <small
      class="service-text"
      data-bind="text: _t('main', 'связан с параметром {param} контакта', {
            param: linkedClientField
          })"
    ></small>
    <!-- /ko -->
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <!-- ko if: text -->
  <div class="review-question-view__text" data-bind="text: text"></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <!-- ko if: skipped -->
    <div
      class="review-question-stars__skipped"
      data-bind="text: _t('answers', 'Респондент отказался от оценки')"
    ></div>
    <!-- /ko -->
    <!-- ko ifnot: hasAnswer -->
      <div class="review-question-view-block skip-block">
        <div
          class="review-question-view-block__text review-question-view-block__text--skipped"
          data-bind="
            text: $component.isHidden ?
              'Вопрос для респондента не отображался' :
              'Вопрос пропущен',
          "
        ></div>
      </div>
    <!-- /ko -->

    <!-- ko ifnot: skipped -->
    <!-- ko if: answer.length == 1 -->
    <div
      class="review-question-view__media-section"
      data-bind="let: { mediaEl: answer[0] }"
    >
      <div class="review-question-view-media" data-bind="log: 'gallery'">
        <!-- Изображения -->
        <!-- ko if: mediaEl.url == mediaEl.poster -->
        <img
          class="review-question-view-image"
          data-bind="
                    attr: { src: mediaEl.poster },
                    fancyboxGalleryItem: {
                      gallery: [
                        {
                          src: mediaEl.url,
                          opts: { caption: mediaEl.description || '' }
                        }
                      ],
                    }"
        />
        <!-- /ko -->
        <!-- /Изображения -->

        <!-- Видео -->
        <!-- ko ifnot: mediaEl.url == mediaEl.poster -->

        <div
          class="review-question-view-video"
          data-bind="fancyboxGalleryItem: {
                gallery: [
                        {
                          src: mediaEl.url,
                          opts: { caption: mediaEl.description || '' }
                        }
                      ],
                    }"
        >
          <video
            class="review-question-view-video__content"
            data-bind="attr: { poster: mediaEl.poster }"
          >
            <source data-bind="attr: { src: mediaEl.url }" />
            Тег video не поддерживается вашим браузером.
          </video>

          <div class="review-question-view-video__mask"></div>
          <div class="review-question-view-video__play-button"></div>
        </div>

        <!-- ko template: { name: 'review-question-view-video-template',  data: {
                  fancy: {
                    urls: [mediaEl.url],
                  },
                  url: mediaEl.url,
                  poster: mediaEl.poster

                } } -->
        <!-- /ko -->

        <!-- /ko -->
        <!-- /Видео -->
      </div>
      <!-- .review-question-view-media -->

      <div
        data-bind="text: mediaEl.description"
        class="f-color-text f-fs-2 mt-2 ml-2"
      ></div>
    </div>
    <!-- .review-question-view__media-section -->
    <!-- /ko -->

    <div class="review-question-view__answer-section">
      <!-- ko if: hasAnswer -->
      <div class="review-question-view-gallery">
        <!-- ko if: answer.length > 1 -->
        <div class="review-gallery-table">
          <div class="review-gallery-table__header">
            <div data-bind="text: _t('answers', 'Фото/видео')"></div>
            <div data-bind="text: _t('answers', 'Оценка')"></div>
          </div>
          <!-- ko foreach: answer -->
          <div
            class="review-question-view-gallery__item"
            data-bind="
              css: {
                'review-question-view-gallery__item--inactive': rating == 0,
              },
            "
          >
            <div class="review-gallery-table__item-data">
              <div
                class="review-question-view-gallery__item-media"
                data-bind="
                  fancyboxGalleryItem: {
                    gallery: $parent.gallery,
                    index: $index(),
                  },
                "
              >
                <img data-bind="attr: { src: $data.poster }" />
              </div>
              <div
                class="review-question-view-gallery__item-description"
                data-bind="text: description"
              ></div>
            </div>
            <div class="review-question-view-rating" data-bind="">
              <!-- ko template: {
                name: 'rating-template',
                data: {
                  value: rating,
                },
              } --><!-- /ko -->
              <span
                class="review-question-view-rating__label ml-3"
                data-bind="text: rating > 0 ? parseInt(rating).toFixed(1) : '—'"
              ></span>
            </div>
          </div>
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- ko if: answer.length == 1 -->

        <div
          class="review-question-view-rating"
          data-bind="let: { ratingValue: answer[0].rating || 0 }"
        >
          <!-- ko template: { name: 'rating-template', data: { value: ratingValue } } -->
          <!-- /ko -->

          <span
            class="review-question-view-rating__label ml-3"
            data-bind="text: ratingValue > 0 ? parseInt(ratingValue).toFixed(1) : '—'"
          >
          </span>
        </div>
        <!-- /ko -->
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
  <!-- Комментарий -->
  <!-- ko template: { name: 'review-question-view-comment-template', data: { text: comment } } -->
  <!-- /ko -->
  <!-- /Комментарий -->
</div>
<!-- /ko -->
