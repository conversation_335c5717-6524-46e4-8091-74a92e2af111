.scale-variant__text {
  font-size: 15px;
  line-height: 120%;
  color: #2E2F31;
}

.scale-variant-block {
  width: 100%;
}

.scale-variants {
  width: 100%;
}

.scale-variants {
  width: 100%;
}

.scale-variant {
  display: flex;
  align-items: center;
  width: 100%;
  border-bottom: 1px solid #E7EBED;
  padding-top: 12px;
  padding-bottom: 12px;

  &:first-child {
    border-top: 1px solid #E7EBED;
  }

  &--deleted {
    color: var(--f-color-service);
  }

  .removed {
    font-style: italic;
    color: var(--f-color-danger);
    font-size: 12px;
  }
}

.scale-variant__text {
  margin-right: auto;
}

.scale-variant__value,
.scale-single__value {
  font-weight: 700;
  font-size: 14px;
  line-height: 110%;
  color: #2E2F31;
  margin-right: 10px;
}

.scale-variant__no-value {
  font-style: italic;
  font-weight: 400;
  font-size: 12px;
  line-height: 120%;
  color: #73808D;
}

.scale-variant__rating,
.scale-single__rating {
  display: flex;
  align-items: center;

  @media screen and (max-width: 767.98px) {
    padding-left: 15px;
    padding-right: 15px;
    .foquz-slider__slider {
      min-width: 60px;
    }
  }


  .ui-state-disabled {
    opacity: 1;
  }

  .ui-slider {
    background-color: #D9D9D9;

    .ui-slider-range {
      background-color: #3F65F1;

    }
  }
}

.review-question-view--type_scale {
  .review-question-view__subdescription {
    @media screen and (max-width: 767.98px) {
      padding-left: 15px;
      padding-right: 15px;
    }
  }
}

.review-question-view__body_scale {
  flex-direction: column;

  .gallery-button,
  .scale-variant {
    @media screen and (max-width: 767.98px) {
      padding-left: 15px;
      padding-right: 15px;
    }
  }

  .review-question-view-block {
    @media screen and (max-width: 767.98px) {
      padding-left: 30px;
      padding-right: 30px;

    }

  }
}
