<!-- ko using: question -->
<div class="review-question-view review-question-view--type_scale">
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
    <!-- ko if: $data.linkWithClientField -->
    <small class="service-text" data-bind="text: _t('main', 'связан с параметром {param} контакта', {
      param: linkedClientField
    })"></small>
    <!-- /ko -->
  </div>
  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->
  <!-- ko if: text !== null -->
  <div class="review-question-view__text" data-bind="text: text"></div>
  <!-- /ko -->
  <div class="review-question-view__body review-question-view__body_scale">
    <!-- ko if: $component.question.gallery.length -->
    <div
      class="f-color-text mt-3 mb-4 d-flex align-items-center cursor-pointer no-print gallery-button"
      data-bind="
        fancyboxGalleryItem: {
          gallery: $component.question.gallery.map(function(i) {
            return {
              src: i.url,
              opts: {
                caption: i.description
              }
            }
          }),
          noCursor: true,
          index: 0,
        },
      "
    >
      <span class="f-icon f-icon--picture f-icon-sm mr-2">
        <svg>
          <use href="#picture-icon"></use>
        </svg>
      </span>
      <span class="f-color-primary f-fs-3 bold">Галерея</span>
    </div>
    <!-- /ko -->
    <!-- ko ifnot: hasAnswer -->
    <div class="review-question-view-block skip-block">
      <div
        class="review-question-view-block__text review-question-view-block__text--skipped"
        data-bind="
          text: $component.isHidden ?
            'Вопрос для респондента не отображался' :
            'Вопрос пропущен',
        "
      ></div>
    </div>
    <!-- /ko -->
    <!-- ko if: skipped -->
    <div class="review-question-stars__skipped" data-bind="text: _t('answers', 'Респондент отказался от оценки')"></div>
    <!-- /ko -->
    <!-- ko ifnot: skipped || !hasAnswer -->
    <!-- ko ifnot: isVariants -->
    <div class="scale-single__rating d-flex align-items-center">
      <span class="scale-single__value" data-bind="text: answer.answer.rating"></span>
      <foquz-slider
        params="
          value: answer.answer.rating,
          min: config.minValue,
          max: config.maxValue,
          withIndicator: false,
          withHandle: false,
          disabled: true,
        "
      ></foquz-slider>
    </div>
    <!-- /ko -->
    <!-- ko if: isVariants -->
    <div class="scale-variant-block d-flex align-items-center">
      <table class="scale-variants" data-bind="log">
        <tbody>
          <!-- ko foreach: { data: variants, as: 'variant' } -->
          <tr
            class="scale-variant"
            data-bind="
              css: {
                'scale-variant--deleted': variant.deleted,
                'scale-variant--selected': $parent.answerData[variant.id] > 0
              },
            "
          >
            <td class="scale-variant__text">
              <span data-bind="text: variant.text"></span>
              <!-- ko if: variant.deleted -->
              <span class="removed">(Удален)</span>
              <!-- /ko -->
            </td>
            <td class="scale-variant__rating">
              <!-- ko if: isNaN($parent.answerData[variant.id]) -->
              <div class="scale-variant__no-value" data-bind="text: _t('answers', 'Респондент отказался от оценки')"></div>
              <!-- /ko -->
              <!-- ko ifnot: isNaN($parent.answerData[variant.id]) -->
              <div class="scale-variant__value" data-bind="text: $parent.answerData[variant.id]"></div>
              <foquz-slider
                params="
                  value: $parent.answerData[variant.id],
                  min: $parent.config.minValue,
                  max: $parent.config.maxValue,
                  withIndicator: false,
                  disabled: true,
                  withHandle: false,
                "
              ></foquz-slider>
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>
  <!-- ko if: answer?.comment -->
    <!-- ko template: {
      name: 'review-question-view-comment-template',
      data: { text: answer.comment }
    } --><!-- /ko -->
  <!-- /ko -->
  <!-- ko if: answer?.answer && answer?.answer?.comment -->
    <!-- ko template: {
      name: 'review-question-view-comment-template',
      data: { text: answer.answer.comment }
    } --><!-- /ko -->
  <!-- /ko -->
</div>
<!-- /ko -->
