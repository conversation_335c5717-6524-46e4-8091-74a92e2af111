<!-- ko using: question -->
<div
  class="review-question-view"
  data-bind="log, css: 'review-questions-view--type_' + type + ' '"
>
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <!-- ko if: text -->
  <div class="review-question-view__text" data-bind="text: text"></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko ifnot: hasAnswer -->
        <div class="review-question-view-block skip-block">
          <div
            class="review-question-view-block__text review-question-view-block__text--skipped"
            data-bind="
              text: $component.isHidden ?
                'Вопрос для респондента не отображался' :
                'Вопрос пропущен',
            "
          ></div>
        </div>
      <!-- /ko -->
      <!-- ko if: hasAnswer -->
      <!-- ko if: gallery.length -->
      <div
        class="f-color-text mb-4 d-flex align-items-center cursor-pointer"
        data-bind="fancyboxGalleryItem: {
        gallery: fancyGallery,
        index: 0,
        noCursor: true,
        }"
      >
        <span class="f-icon f-icon--picture mr-2">
          <svg>
            <use href="#picture-icon"></use>
          </svg>
        </span>
        <span
          class="f-color-primary f-fs-3 bold"
          data-bind="text: _t('answers', 'Галерея')"
        ></span>
      </div>
      <!-- /ko -->

      <!-- ko if: skipped -->
      <div
        class="review-question-stars__skipped"
        data-bind="text: _t('answers', 'Респондент отказался от оценки')"
      ></div>
      <!-- /ko -->

      <!-- ko ifnot: skipped -->

      <!-- ko if: loading -->
      <fc-spinner class="f-color-primary"></fc-spinner>
      <!-- /ko -->

      <!-- ko ifnot: loading -->
      <div class="review-question-view-variants">
        <!-- ko if: listType === 'tree' -->
        <fc-classifier-tree class="review-classifier-tree" params="tree: tree, itemComponentName: 'fc-classifier-item', itemData: {
          counts: counts,
          answer: answer
        }"></fc-classifier-tree>
        <!-- /ko -->

        <!-- ko ifnot: listType === 'tree' -->
        <!-- ko foreach: { data: variants, as: 'variant' } -->
        <div
          class="review-question-view-variant"
          data-bind="
            css: {
              'review-question-view-variant--selected': $parent.answer.includes($data.id),
            },
            visible: $parent.answer.includes($data.id) || !window.IS_EXTERNAL,
          "
        >
          <!-- ko text: variant.value -->
          <!-- /ko -->
        </div>
        <!-- /ko -->
        <!-- /ko -->
      </div>
      <!-- /ko -->

      <!-- /ko -->
      <!-- /ko -->
    </div>
    <!-- .review-question-view__answer-section -->
  </div>
  <!-- .review-question-view__body -->
  <!-- ko template: {
    name: 'review-question-view-comment-template',
    data: { text: comment || '' }
  } -->
  <!-- /ko -->
</div>
<!-- .review-question-view -->
<!-- /ko -->
