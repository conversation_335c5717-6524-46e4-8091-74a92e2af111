import { TreeNodeViewProps } from "@/presentation/views/fc-tree/types";

export function ViewModel(props: TreeNodeViewProps, element: HTMLElement) {
  const { node, nodeStructure, itemData } = props;
  const { counts, answer } = itemData;

  console.log(props);

  const isSelected = !nodeStructure.category && answer.includes(node.id);

  if (isSelected) element.classList.add('selected')

  return {
    name: node.data.name,
    isCategory: nodeStructure.category,
    elementsCount: nodeStructure.elementsCount,
    count: ko.computed(() => {
      const data = counts();
      return data[node.id];
    }),
  };
}
