import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";
import * as TreeComponent from "@/presentation/views/fc-tree";
import * as TreeItemComponent from "./classifier-item";
import { registerComponent } from "@/utils/engine/register-component";

registerComponent("fc-classifier-tree", TreeComponent);
registerComponent("fc-classifier-item", TreeItemComponent);

ko.components.register("review-question-classifier", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("review-question-classifier");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
