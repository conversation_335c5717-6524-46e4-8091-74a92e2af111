.review-classifier-tree {


  .fc-tree-node-wrapper[data-item] {
    margin-bottom: 0;

    font-size: 16px;
    color: #2e2f31;

    .fc-tree-node-wrapper__data {
      width: 100%;
      min-height: 40px;
      padding: 10px 1px;
      border-top: 1px solid #e7ebed;
      padding-left: calc(1px + 15px);
      padding-right: calc(1px + 15px);
      width: 100%;

      &.selected {
        border: none;

        background: #d9e0fc;
        border-radius: 8px;
      }
    }
  }

  .category-count {
    height: 20px;
    min-width: 20px;
    flex-grow: 0;
    flex-shrink: 0;
    background: #3f65f1;
    color: white;
    padding: 0;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 900;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    line-height: 1;
  }

  .category-name {
    font-size: 16px;
    font-weight: 700;
  }
}
