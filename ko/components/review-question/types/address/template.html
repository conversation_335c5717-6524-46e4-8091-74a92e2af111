<!-- ko using: question -->
<div
  class="review-question-view"
  data-bind="css: 'review-questions-view--type_' + type"
>
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>

    <!-- ko if: linkWithClientField -->
    <small
      class="service-text"
      data-bind="text: _t('main', 'связан с параметром {param} контакта', {
             param: linkedClientField
           })"
    ></small>
    <!-- /ko -->
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <!-- ko if: text -->
  <div class="review-question-view__text" data-bind="text: text"></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko if: answer -->
      <div class="review-question-view-block">
        <div
          class="review-question-view-block__text"
          data-bind="text: answer"
        ></div>
      </div>
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
    <div class="review-question-view-block skip-block">
      <div
        class="review-question-view-block__text review-question-view-block__text--skipped"
        data-bind="
          text: $component.isHidden ?
            'Вопрос для респондента не отображался' :
            'Вопрос пропущен',
        "
      ></div>
    </div>
    <!-- /ko -->
    </div>
  </div>
</div>
<!-- /ko -->
