<!-- ko using: question -->
<div class="review-question-view review-question-view--type_matrix">
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
    <!-- ko if: linkWithClientField -->
    <small class="service-text" data-bind="text: _t('main', 'связан с параметром {param} контакта', {
      param: linkedClientField
    })"></small>
    <!-- /ko -->
  </div>
  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->
  <!-- ko if: $component.review.withPoints -->
  <div class="review-question-view__meta">
    <!-- ko if: hasAnswer -->
    <div class="mr-4 mb-2">
      <span class="f-color-service" data-bind="text: _t('Баллов за ответ') + ':'"></span>
      <span
        data-bind="html: _t('main', '{num1} из {num2}', {
        num1: '<span class=\'bold\'>' + (answerPoints || 0) + '</span>',
        num2: '<span class=\'bold\'>' + (maxPoints || 0) + '</span>',
      }), css: {'empty': answerPoints == 0 || !answerPoints}"
      ></span>
      <!-- ko if: answerPoints == 0 || !answerPoints -->
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0 7C0 8.933 0.783502 10.683 2.05025 11.9497C3.317 13.2165 5.067 14 7 14C10.866 14 14 10.866 14 7C14 5.067 13.2165 3.317 11.9497 2.05025C10.683 0.783502 8.933 0 7 0C3.13401 0 0 3.13401 0 7Z" fill="#FF0200"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3 7C3 6.44772 3.44772 6 4 6H10C10.5523 6 11 6.44772 11 7C11 7.55228 10.5523 8 10 8H4C3.44772 8 3 7.55228 3 7Z" fill="white"/>
      </svg>
      <!-- /ko -->
    </div>
    <!-- /ko -->
    <div class="mb-2">
      <span class="f-color-service" data-bind="text: _t('Правильный ответ') + ':'"></span>
      <span data-bind="text: correctVariantsString"></span>
    </div>
  </div>
  <!-- /ko -->
  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko if: hasAnswer -->
      <!-- ko if: gallery.length -->
      <div class="f-color-text mb-4 d-flex align-items-center cursor-pointer" data-bind="fancyboxGalleryItem: {
        gallery: fancyGallery,
        index: 0,
        noCursor: true,
        }">
        <span class="f-icon f-icon--picture mr-2">
          <svg>
            <use href="#picture-icon"></use>
          </svg>
        </span>
        <span class="f-color-primary f-fs-3 bold" data-bind="text: _t('answers', 'Галерея')"></span>
      </div>
      <!-- /ko -->
      <!-- ko if: skipped -->
      <div class="review-question-stars__skipped" data-bind="text: _t('answers', 'Респондент отказался от оценки')"></div>
      <!-- /ko -->
      <!-- ko ifnot: skipped -->
      <div
        data-bind="
          css: {
            'mb-4': !window.IS_EXTERNAL,
          },
        "
      >
        <!-- ko if: clarifyingQuestion && !window.IS_EXTERNAL -->
        <div class="review-question-view-clarifying-question__text">
          <!-- ko text: clarifyingQuestion -->
          <!-- /ko -->
          <!-- ko if: $data.forAllRates -->
          <small class="service-text" data-bind="text: '(' + _t('для всех столбцов, для всех строк') +')'"></small>
          <!-- /ko -->
          <!-- ko ifnot: $data.forAllRates -->
          <small class="service-text" data-bind="text: '(' + _t(`для столбцов ${$parent.needExtraColsStr()}, для строк ${$parent.needExtraRowsStr()}`) +')'"></small>
          <!-- /ko -->
        </div>
        <!-- /ko -->
        <div data-bind="fScrollbar">
          <table class="table f-table f-table-dense f-color-text review-matrix-table">
            <thead>
              <tr>
                <th width="120" class="d-none d-md-table-cell matrix-row-name-cell"></th>

                <!-- ko foreach: { data: matrix.cols, as: 'column' } -->
                <th width="100" data-bind="text: column" align="center"></th>
                <!-- /ko -->

                <!-- ko if: $component.review.withPoints -->
                <th class="border-0" style="width: 33px;"></th>
                <!-- /ko -->
              </tr>
            </thead>
            <tbody>
              <!-- ko foreach: { data: matrix.rows, as: 'row' } -->
              <tr class="d-md-none matrix-row-name">
                <td data-bind="attr: { colspan: $parent.matrix.cols.length }" >
                  <span class="font-weight-500" data-bind="text: row"></span>
                </td>
              </tr>
              <tr
                data-bind="
                  let: {
                    rowAnswer: $parent.answer[row] || '',
                  },
                "
              >
                <td valign="middle" class="d-none d-md-table-cell matrix-row-name-cell" data-bind="log: 'matrix-row'">
                  <span class="font-weight-500" data-bind="text: row.replace('__foquz_dictionary_element', '')"></span>
                </td>
                <!-- ko if: rowAnswer == 'null' -->
                <td
                  class="review-matrix-table__no-value-cell"
                  valign="middle"
                  data-bind="
                    attr: {
                      colspan: $parent.matrix.cols.length,
                    },
                  "
                >
                  <!-- ko if: window.IS_EXTERNAL -->
                  <div class="review-question-stars__skipped" data-bind="text: _t('answers', 'Респондент отказался от оценки')"></div>
                  <!-- /ko -->
                  <!-- ko ifnot: window.IS_EXTERNAL -->
                  <!-- ko text: 'Респондент отказался от оценки' --><!-- /ko -->
                  <!-- /ko -->
                </td>
                <!-- /ko -->
                <!-- ko ifnot: rowAnswer == 'null' -->
                <!-- ko foreach: { data: $parent.matrix.cols, as: 'column' } -->
                <td align="center" valign="middle" data-bind="let: { checked: rowAnswer.includes(column) }">
                  <!-- ko if: checked -->
                  <!-- ko if: window.IS_EXTERNAL -->
                  <span class="f-icon f-icon--check f-icon-success">
                    <svg>
                      <use href="#plus-icon-2"></use>
                    </svg>
                  </span>
                  <!-- /ko -->
                  <!-- ko ifnot: window.IS_EXTERNAL -->
                  <svg-icon params="name: 'plus'" class="f-color-success"></svg-icon>
                  <!-- /ko -->
                  <!-- /ko -->
                  <!-- ko ifnot: checked -->
                  <span class="f-icon-dash"></span>
                  <!-- /ko -->
                </td>
                <!-- /ko -->
                <!-- ko if: $component.review.withPoints -->
                <td class="border-0" valign="middle">
                  <!-- ko if: $parent.correctVariants[row].isCorrect -->
                  <svg-icon params="name: 'check'" class="svg-icon--sm f-color-text"></svg-icon>
                  <!-- /ko -->
                </td>
                <!-- /ko -->
                <!-- /ko -->
              </tr>
              <!-- ko if: $parent.clarifyingQuestion && $parent.clarifyingQuestion !== null && $parent.answerData.extra && $parent.answerData.extra[row] -->
              <tr class="review-matrix-table__clarifying-row">
                <td colspan="100%">
                  <!-- ko if: window.IS_EXTERNAL -->
                  <div class="review-matrix-table__clarifying-row-text" data-bind="text: $parent.clarifyingQuestion">
                  </div>
                  <!-- /ko -->
                  <!-- Уточняющий вопрос -->
                  <!-- ko template: { name: 'review-question-variant-view-clarifying-matrix-question-template', data: $parent } -->
                  <!-- /ko -->
                  <!-- /Уточняющий вопрос -->
                </td>
              </tr>
              <!-- /ko -->
              <!-- /ko -->
            </tbody>
          </table>
        </div>
      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
      <div class="review-question-view-block skip-block">
        <div
          class="review-question-view-block__text review-question-view-block__text--skipped"
          data-bind="
            text: $component.isHidden ?
              'Вопрос для респондента не отображался' :
              'Вопрос пропущен',
          "
        ></div>
      </div>
      <!-- /ko -->
    </div>
  </div>
  <!-- ko template: {
    name: 'review-question-view-comment-template',
    data: { text: comment }
  } -->
  <!-- /ko -->
</div>
<!-- /ko -->
