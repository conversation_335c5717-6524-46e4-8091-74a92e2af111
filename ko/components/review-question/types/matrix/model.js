import { get as _get } from "lodash";

import { QuestionViewModel } from "../question";

export class ViewModel extends QuestionViewModel {
  constructor(params) {
    super(params);
    
    this.needExtraRowsStr = ko.computed(() => {
      return this.question.matrixSettings.rows.reduce(
        (acc, el, index) => {
          if (!_get(this.question, 'matrixSettings.extra_question.rows', []).includes(el)) {
            return acc;
          }
          return `${acc}${acc === '' ? '' : ','}${index + 1}`;
        },
        '',
      );
    });
    this.needExtraColsStr = ko.computed(() => {
      return this.question.matrixSettings.cols.reduce(
        (acc, el, index) => {
          if (!_get(this.question, 'matrixSettings.extra_question.cols', []).includes(el)) {
            return acc;
          }
          return `${acc}${acc === '' ? '' : ','}${index + 1}`;
        },
        '',
      );
    });
  }
}
