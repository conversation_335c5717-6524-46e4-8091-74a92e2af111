.review-matrix-table__no-value-cell {
  font-size: 12px;
  font-style: italic;
  font-weight: 400;
  line-height: 14px;
  color: #73808D;
  text-align: left;
}

.review-question-view-clarifying-question {
  margin-top: 0px;
}

.review-matrix-table__clarifying-row {
  td {
    padding: 10px 0;
  }
}

.matrix-3d-table__desktop-header {
  display: flex;
  &_review {
    width: fit-content;
  }
}
.matrix-3d-table__row {
  display: flex;
}
.matrix-3d-table__row-data {
  display: flex;
}
.matrix-3d-table__row-item {
  display: flex;
}
.matrix-3d-table__col-header {
  display: none;
}

@media screen and (max-width: 768px) {
  .matrix-3d-table__desktop-header {
    display: none;
  }
 .matrix-3d-table__col-header {
    display: block;
  }
  .matrix-3d-table__row {
    flex-direction: column;
  }
  .matrix-3d-table__row-data {
    flex-direction: column;
  }
}
.matrix-3d-table__header-item {
  min-width: 130px;
  font-size: 12px;
  font-weight: 700;
  line-height: 13px;
  flex-grow: 1;
}

.matrix-3d-table__desktop-header {
  justify-content: space-between;
  align-items: flex-end;
  border-bottom: 2px solid #E1E8EB;
  padding-bottom: 10px;
}

.matrix-3d-table__row {
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  padding-bottom: 10px;
  width: fit-content;
  border-bottom: 1px solid #E1E8EB;
}

.matrix-3d-table__row-header {
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #E1E8EB;

  &:first-child {
    border-bottom: 2px solid #E1E8EB;
  }
}

.matrix-3d-table__row-data {
  flex-grow: 1;
}

.matrix-3d-table__row-item {
  min-width: 130px;
  font-size: 12px;
  font-weight: 500;
  line-height: 12px;

  .matrix-3d-table__row-item--skipped {
    font-size: 12px;
    font-style: italic;
    font-weight: 400;
    line-height: 14px;
    color: #73808D;
  }
}

.matrix-3d-table {
  display: flex;
}

.matrix-3d-table__desktop-row-headers {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  font-weight: 500;
  line-height: 14px;

  >* {
    padding-right: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  @media screen and (max-width: 768px) {
    display: none;
  }
}

.matrix-3d-table__body {
  width: 75%;

  @media screen and (max-width: 768px) {
    width: 100%;
  }

  &.matrix-3d-table__body--rows-above {
    width: 100%;
  }
}

.matrix-3d-table__above-header {
  font-size: 15px;
  font-weight: 700;
  line-height: 17px;
  margin-top: 20px;
  position: sticky;
  left: 1px;
  width: fit-content;

  @media screen and (max-width: 768px) {
    display: block;
    width: 100%;
  }

  &.matrix-3d-table__above-header--hidden {
    display: none;
    @media screen and (max-width: 768px) {
      display: block;
    }
  }
}

@media screen and (max-width: 768px) {
  .matrix-3d-table__row {
    width: 100%;
  }
  .matrix-3d-table__row-item {
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    &:not(:last-child) {
      margin-right: 0;
    }
  }
  .foquz-combo {
    max-width: 130px;
  }
}

.matrix-3d-table {
  .foquz-combo__wrapper--valid {
    position: relative;

    .foquz-combo__select {
      border-color: #00C968;
    }

    &::after {
      content: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M7 14C10.866 14 14 10.866 14 7C14 3.13401 10.866 0 7 0C3.13401 0 0 3.13401 0 7C0 10.866 3.13401 14 7 14Z' fill='%2300C968'/%3E%3Cpath d='M4 6.7077L6.32424 9L9.93806 5' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
      position: absolute;
      top: -7px;
      right: -7px;
    }
  }
}

.matrix-3d-table__wrapper {
  margin-bottom: 15px;
}

.review-question-matrix-3d {
  .review-question-view-comment { // ToDo refactor
    margin-top: -18px !important;
  }
}
