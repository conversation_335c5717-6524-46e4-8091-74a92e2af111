<!-- ko using: question -->
<div class="review-question-view review-question-view--type_matrix-3d">
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName --><!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
    <!-- ko if: linkWithClientField -->
    <small
      class="service-text"
      data-bind="
        text: _t('main', 'связан с параметром {param} контакта', { param: linkedClientField }),
      "
    ></small>
    <!-- /ko -->
  </div>
  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->
  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko if: hasAnswer -->
      <!-- ko if: gallery.length -->
      <div
        class="f-color-text mb-4 d-flex align-items-center cursor-pointer"
        data-bind="
          fancyboxGalleryItem: {
            gallery: fancyGallery,
            index: 0,
            noCursor: true,
          },
        "
      >
        <span class="f-icon f-icon--picture mr-2">
          <svg>
            <use href="#picture-icon"></use>
          </svg>
        </span>
        <span class="f-color-primary f-fs-3 bold" data-bind="text: _t('answers', 'Галерея')"></span>
      </div>
      <!-- /ko -->
      <!-- ko if: skipped -->
      <div class="review-question-stars__skipped" data-bind="text: _t('answers', 'Респондент отказался от оценки')"></div>
      <!-- /ko -->
      <!-- ko ifnot: skipped -->
      <div class="mb-4">
        <div class="matrix-3d-table" data-bind="matrix3DTable">
          <div class="matrix-3d-table__desktop-row-headers">
            <div class="matrix-3d-table__row-header"></div>
            <!-- ko foreach: matrixElements.rows -->
            <!-- ko if: $parent.answer[$data.id] && $data.isChecked -->
            <div class="matrix-3d-table__row-header" data-bind="text: name"></div>
            <!-- /ko -->
            <!-- /ko -->
          </div>
          <div class="matrix-3d-table__body">
            <div class="" data-bind="nativeScrollbar">
              <div class="matrix-3d-table__wrapper">
                <div class="matrix-3d-table__desktop-header matrix-3d-table__desktop-header_review">
                  <!-- ko foreach: matrixElements.columns -->
                  <!-- ko if: !$data.disabled && $data.isChecked -->
                  <div class="matrix-3d-table__header-item" data-bind="text: name"></div>
                  <!-- /ko -->
                  <!-- /ko -->
                </div>
                <!-- ko foreach: matrixElements.rows -->
                <!-- ko if: $parent.answer[$data.id] && $data.isChecked -->
                <div class="matrix-3d-table__row">
                  <!-- ko foreach: $parent.matrixElements.columns -->
                  <!-- ko if: !$data.disabled && $data.isChecked -->
                  <div class="matrix-3d-table__row-item">
                    <div class="matrix-3d-table__col-header" data-bind="text: name"></div>
                    <!-- ko if: !$parents[1].answer[$parent.id] || !$parents[1].answer[$parent.id][id] || !$parents[1].answer[$parent.id][id][0] -->
                    <span class="matrix-3d-table__row-item--skipped"></span>
                    <!-- /ko -->
                    <!-- ko ifnot: !$parents[1].answer[$parent.id] || !$parents[1].answer[$parent.id][id] || !$parents[1].answer[$parent.id][id][0] -->
                    <!-- ko if: $parents[1].answer[$parent.id][id][0] == '-1' -->
                    <span class="matrix-3d-table__row-item--skipped">
                      Затрудняюсь ответить
                    </span>
                    <!-- /ko -->
                    <!-- ko if: $parents[1].answer[$parent.id][id][0] != '-1' -->
                    <span class="matrix-3d-table__row-item-answer">
                      <!-- ko foreach: $parents[1].answer[$parent.id][id] -->
                      <!-- ko let: {
                        cellAnswer: $parent.variants.find(el => el.id == $data),
                      }-->
                      <!-- ko if: cellAnswer -->
                      <span data-bind="text: cellAnswer.name"></span><br>
                      <!-- /ko -->
                      <!-- /ko -->
                      <!-- /ko -->
                    </span>
                    <!-- /ko -->
                    <!-- /ko -->
                  </div>
                  <!-- /ko -->
                  <!-- /ko -->
                </div>
                <!-- /ko -->
                <!-- /ko -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
      <div class="review-question-view-block skip-block">
        <div
          class="review-question-view-block__text review-question-view-block__text--skipped"
          data-bind="
            text: $component.isHidden ?
              'Вопрос для респондента не отображался' :
              'Вопрос пропущен',
          "
        ></div>
      </div>
      <!-- /ko -->
    </div>
  </div>
  <!-- ko template: {
    name: 'review-question-view-comment-template',
    data: {
      text: comment,
    },
  } --><!-- /ko -->
</div>
<!-- /ko -->
