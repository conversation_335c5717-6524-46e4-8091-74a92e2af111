import { QuestionViewModel } from "../question";

export class ViewModel extends QuestionViewModel {
    constructor(params, element) {
        super(params, element);

        this.hasMixedFilesPhotoAndVideo = ko.observable(false);
        this.hasMixedAudioFiles = ko.observable(false);

        this.calculateHasMixedFiles(params);

        this.SUPPORTED_IMAGE_FORMATS = ['png', 'jpg', 'jpeg', 'gif', 'heic', 'webp'];
        this.UN_SUPPORTED_VIDEO_FORMATS = ['png', 'jpg', 'jpeg', 'gif', 'mp3', 'wmv', 'ogg', 'm4a', 'heic', 'webp', 'wav', 'acc', 'aac', 'amr'];
    }

    calculateHasMixedFiles(params) {
        const files = params?.question?.answer?.answer?.files || [];
        const audioExtensions = [".mp3", ".wma", ".ogg", ".m4a"];
        const isAudio = (name) =>
            audioExtensions.some(ext => name.toLowerCase().endsWith(ext));
        const audioCount = files.filter(file => isAudio(file.name)).length;
        this.hasMixedFilesPhotoAndVideo(audioCount > 0 && audioCount < files.length);


        const extendedAudioExtensions = [".mp3", ".aac", ".wma", ".ogg", ".amr", ".m4a"];
        const isExtendedAudio = (name) =>
            extendedAudioExtensions.some(ext => name.toLowerCase().includes(ext));
        const extendedAudioFiles = files.filter(file => isExtendedAudio(file.name));
        this.hasMixedAudioFiles(
            extendedAudioFiles.length > 0 && extendedAudioFiles.length < files.length
        );
    }
}
