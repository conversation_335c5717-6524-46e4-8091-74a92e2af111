<!-- ko using: question -->
<div
  class="review-question-view"
  data-bind="css: 'review-questions-view--type_' + type + ' ' +
              'review-question-view--media-type_' + mediaType"
>
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>

    <!-- ko if: $data.linkWithClientField -->
    <small
      class="service-text"
      data-bind="text: _t('main', 'связан с параметром {param} контакта', {
        param: linkedClientField
      })"
    ></small>
    <!-- /ko -->
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <!-- ko if: answer && maxPoints -->
  <div class="f-color-text mt-3 mb-4">
    <span
      class="f-color-service"
      data-bind="text: _t('Баллов за ответ') + ':'"
    ></span>
    <span
        data-bind="html: _t('main', '{num1} из {num2}', {
        num1: '<span class=\'bold\'>' + (answerPoints || 0) + '</span>',
        num2: '<span class=\'bold\'>' + (maxPoints || 0) + '</span>',
      }), css: {'empty': answerPoints == 0 || !answerPoints}"
      ></span>
      <!-- ko if: answerPoints == 0 || !answerPoints -->
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0 7C0 8.933 0.783502 10.683 2.05025 11.9497C3.317 13.2165 5.067 14 7 14C10.866 14 14 10.866 14 7C14 5.067 13.2165 3.317 11.9497 2.05025C10.683 0.783502 8.933 0 7 0C3.13401 0 0 3.13401 0 7Z" fill="#FF0200"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3 7C3 6.44772 3.44772 6 4 6H10C10.5523 6 11 6.44772 11 7C11 7.55228 10.5523 8 10 8H4C3.44772 8 3 7.55228 3 7Z" fill="white"/>
      </svg>
      <!-- /ko -->
  </div>
  <!-- /ko -->

  <!-- ko if: text !== null -->
  <div class="review-question-view__text" data-bind="text: text"></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <!-- ko if: type == 0 -->
    <!-- ko if: mediaType !== 'text' -->
    <div class="review-question-view__media-section">
      <div class="review-question-view-media">
        <!-- Изображения -->
        <!-- ko if: mediaType == 'image' -->
        <!-- ko if: mediaUrls.length == 1 -->
        <img
          class="review-question-view-image"
          data-bind="
                      attr: { src: mediaUrls[0].src },
                      fancybox: { urls: [mediaUrls[0].url] }"
        />
        <!-- /ko -->

        <!-- ko if: mediaUrls.length > 1 -->
        <div
          class="review-question-view-slider"
          data-bind="component: { name: 'statistics__details-modal-dialog-slider' }"
        >
          <!-- ko foreach: $parent.mediaUrls -->
          <div
            data-bind="component: { name: 'statistics__details-modal-dialog-slide', params: { index: $index(), count: $parentContext.$parent.mediaUrls.length } }"
          >
            <img
              class="review-question-view-image"
              data-bind="attr: { src: $parent.src }, fancybox: {
                          urls: $parentContext.$parentContext.$parent.mediaUrls.map(url => url.url),
                          index: $index()
                        }"
            />
          </div>
          <!-- /ko -->
        </div>
        <!-- .review-question-view-slider -->
        <!-- /ko -->
        <!-- /ko -->
        <!-- /Изображения -->

        <!-- Видео -->
        <!-- ko if: mediaType == 'video' -->
        <!-- ko if: mediaUrls.length == 1 -->
        <!-- ko template: { name: 'review-question-view-video-template', data: {
                    fancy: {
                      urls: mediaUrls.map(url => url.url),
                      index: 0,
                    },
                    url: mediaUrls[0].url,
                  } } -->
        <!-- /ko -->
        <!-- /ko -->

        <!-- ko if: mediaUrls.length > 1 -->
        <div
          class="review-question-view-slider"
          data-bind="component: { name: 'statistics__details-modal-dialog-slider' }"
        >
          <!-- ko foreach: $parent.mediaUrls -->
          <div
            data-bind="component: { name: 'statistics__details-modal-dialog-slide', params: { index: $index(), count: $parentContext.$parent.mediaUrls.length } }"
          >
            <!-- ko template: {
                    name: 'review-question-view-video-template',
                    data: {
                          fancy: {
                            urls: $parentContext.$parentContext.$parent.mediaUrls.map(url => url.url),
                            index: $index(),
                          },
                          url: $parent.url
                        }
                  } -->
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>
        <!-- /ko -->
        <!-- /ko -->
        <!-- /Видео -->
      </div>
      <!-- .review-question-view-media -->
    </div>
    <!-- .review-question-view__media-section -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: type == 11 -->
    <!-- ko if: gallery.length == 1 -->
    <div
      class="review-question-view__media-section"
      data-bind="let: { media: gallery[0] }"
    >
      <div class="review-question-view-media">
        <!-- Изображения -->
        <!-- ko if: media.url == media.poster -->
        <img
          class="review-question-view-image"
          data-bind="
                      attr: { src: media.poster },
                      fancyboxGalleryItem: {
                        gallery: [
                          {
                            src: media.url,
                            opts: { caption: media.description || '' }
                          }
                        ],
                      }"
        />
        <!-- /ko -->
        <!-- /Изображения -->

        <!-- Видео -->
        <!-- ko ifnot: media.url == media.poster -->

        <div
          class="review-question-view-video"
          data-bind="fancyboxGalleryItem: {
            gallery: [
                          {
                            src: media.url,
                            opts: { caption: media.description || '' }
                          }
                        ],
                      }"
        >
          <video
            class="review-question-view-video__content"
            data-bind="attr: { poster: media.poster }"
          >
            <source data-bind="attr: { src: $data.url }" />
            Тег video не поддерживается вашим браузером.
          </video>

          <div class="review-question-view-video__mask"></div>
          <div class="review-question-view-video__play-button"></div>
        </div>

        <!-- ko template: { name: 'review-question-view-video-template', data: {
                    fancy: {
                      urls: [media.url],
                    },
                    url: media.url,
                    poster: media.poster

                  } } -->
        <!-- /ko -->

        <!-- /ko -->
        <!-- /Видео -->
      </div>
      <!-- .review-question-view-media -->

      <div
        data-bind="text: media.description"
        class="f-color-text f-fs-2 mt-2 ml-2"
      ></div>
    </div>
    <!-- .review-question-view__media-section -->
    <!-- /ko -->

    <!-- ko if: gallery.length > 1 -->
    <div class="review-question-view__media-section">
      <div class="review-question-view-media">
        <!-- ko if: mediaUrls.length > 1 -->
        <div
          class="review-question-view-slider"
          data-bind="component: { name: 'statistics__details-modal-dialog-slider' }"
        >
          <!-- ko foreach: $parent.gallery -->
          <div
            data-bind="component: { name: 'statistics__details-modal-dialog-slide',
              params: { index: $index(), count: $parentContext.$parent.gallery.length } }"
          >
            <!-- ko if: url == poster -->
            <img
              class="review-question-view-image"
              data-bind="attr: { src: $parent.src }, fancybox: {
                          urls: $parentContext.$parentContext.$parent.gallery.map(url => url.url),
                          index: $index()
                        }"
            />
            <!-- /ko -->

            <!-- ko ifnot: url == poster -->
            <!-- ko template: {
                    name: 'review-question-view-video-template',
                    data: {
                          fancy: {
                            urls: $parentContext.$parentContext.$parent.gallery.map(url => url.url),
                            index: $index(),
                          },
                          url: $parent.url
                        }
                  } -->
            <!-- /ko -->
            <!-- /ko -->
          </div>

          <!-- /ko -->
        </div>
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <div class="review-question-view__answer-section">
      <!-- ko if: hasAnswer -->
      <!-- Тип: 0 (оценка) -->
      <!-- ko if: type == 0 -->

      <!-- 5 звезд -->
      <!-- ko if: assessmentType == 1 -->

      <!-- Рейтинг -->
      <div class="review-question-view-rating">
        <!-- ko template: { name: 'rating-template', data: { value: answer.rating } } -->
        <!-- /ko -->
        <span
          class="review-question-view-rating__label"
          data-bind="text: $component.getRatingLabel(answer.rating)"
        >
        </span>
      </div>
      <!-- /Рейтинг -->

      <!-- Уточняющий вопрос -->
      <!-- ko template: { name: 'review-question-view-clarifying-question-template', data: $data } -->
      <!-- /ko -->
      <!-- /Уточняющий вопрос -->

      <!-- Комментарий -->
      <!-- ko template: { name: 'review-question-view-comment-template', data: { text: answer.comment } } -->
      <!-- /ko -->
      <!-- /Комментарий -->

      <!-- /ko -->
      <!-- /5 звезд -->

      <!-- Варианты -->
      <!-- ko if: assessmentType == 2 -->

      <!-- ko template: { name: 'review-question-view-variants-template', data: $data } -->
      <!-- /ko -->

      <!-- /ko -->
      <!-- /Варианты -->

      <!-- /ko -->
      <!-- / Тип: 0 (оценка) -->

      <!-- Тип: 5 (загрузка файла) -->
      <!-- ko if: type == 5 -->
      <!-- ko if: $parent.hasMixedFilesPhotoAndVideo()  -->
      <div class="review-question-view__files-list-type mb-15p">Фото/видео</div>
      <!-- /ko -->
      <div class="review-question-view__files-list">
        
        <!-- ko let: {
        arFiles: ko.observable(answer.answer.files),
         imageFormats: $parent.SUPPORTED_IMAGE_FORMATS,
         unSupported: $parent.UN_SUPPORTED_VIDEO_FORMATS
         } -->
        <!-- ko foreach: answer.answer.files -->

        <!-- ko let: { ext: $data.url.substr($data.url.lastIndexOf('.') + 1) } -->
        <!-- ko if: imageFormats.includes(ext) -->
        <div class="review-question-view-file-wrapper">

          <div class="review-question-view-file">
            <a
              data-bind="
                click: () => {window.open(window.location.origin + $data.url, '_blank')},
                attr: {href: window.location.origin + $data.url}
              "
              target="_blank"
              class="img-print-link"
            >
              <img
                data-bind="attr: {
                           src:ext ==='heic' ? '/img/image-file-back.png' : ($data.preview_url ?? $data.url)
                        }"
                alt=""
              />
            </a>
            <img
              data-bind="attr: {
                          src: ext ==='heic' ? '/img/image-file-back.png' : $data.url
                      },
                      fancybox: {
                        urls: arFiles().map(i => {
                            return i.url
                        }),
                        index: $index(),
                        caption: $component.getFilesFancyboxCaption($parent.answer.answer)
                      }"
              alt=""
              class="img-prewiew"
            />
          </div>
          <span data-bind="text: window.location.origin + $data.url"></span>
        </div>
        <!-- /ko -->

        <!-- ko if: !unSupported.includes(ext) -->
        <div class="review-question-view-file-wrapper">
          <div class="review-question-view-file">
            <div class="img-prewiew">
              <!-- ko template: {name: 'review-question-view-video-template', data: {
                      url: $data.url,
                      preview_url: $data.preview_url,
                      fancy: {
                        urls: arFiles().map(i => {
                            return i.url
                        }),
                        passedAt: $parent.answer.passedAt,
                        text: $parent.answer.comment,
                        index: $index()
                      }
                    }} -->
            <!-- /ko -->
            </div>
            <a
              data-bind="
                click: () => {window.open(window.location.origin + $data.url, '_blank')},
                attr: {href: window.location.origin + $data.url},
                css: {'w-100': ext == 'mov'}
              "
              target="_blank"
              class="img-print-link"
            >
              <!-- ko template: {name: 'review-question-view-video-template', data: {
                      url: $data.url,
                      preview_url: $data.preview_url,
                      fancy: {
                        urls: arFiles().map(i => {
                            return i.url
                        }),
                        passedAt: $parent.answer.passedAt,
                        text: $parent.answer.comment,
                        index: $index()
                      }
                    }} -->
            <!-- /ko -->
            </a>
          </div>
          <span data-bind="text: window.location.origin + $data.url"></span>
        </div>

        <!-- /ko -->
        <!-- /ko -->

        <!-- /ko -->
        <!-- /ko -->
      </div>
      <!-- ko if: $parent.hasMixedAudioFiles() -->
      <div class="review-question-view__files-list-type mb-15p">Аудио</div>
      <!-- /ko -->
      <!-- ko let: { arFiles: ko.observable(answer.answer.files) } -->
        <!-- ko foreach: answer.answer.files -->

        <!-- Проверка на аудио формат для вывода-->
        <!-- ko let: { ext: $data.url.substr($data.url.lastIndexOf('.') + 1) } -->
        <!-- ko if: $data.name.includes(".mp3") ||  $data.name.includes(".amr") || $data.name.includes(".aac") || $data.name.includes(".wma") || $data.name.includes(".ogg") || $data.name.includes(".m4a") -->

      <!-- ko if: !$data.name.includes(".amr") -->
        <audio class="fs-file-audio mb-10p" controls data-bind="attr: { src: $data.url }"></audio>
      <!-- /ko -->

        <!-- ko if: $data.name.includes(".amr") -->
      <div class="fs-file-audio-wrapper z-1" style="max-width: max-content" data-bind="click: function(data, event) {
         event.stopPropagation();
         var modalTrigger = document.querySelector('.review-question-view-audi-amr-template');
         if (modalTrigger) modalTrigger.click();
     }">
        <audio class="fs-file-audio mb-10p position-relative" style="z-index: -1" controls data-bind="attr: { src: $data.url }"></audio>
      </div>
      <div style="display:none;" id="audioModal">
        <!-- ko template: {name: 'review-question-view-audi-amr-template', data: {
          url: $data.url,
          fancy: {
            urls: arFiles().map(i => {
                return i.url
            }),

            index: $index()
          }
        }} -->
        <!-- /ko -->
      </div>
        <!-- /ko -->


        <div class="d-flex align-items-center mb-15p no-print-audio">
          <svg class="mr-10p fs-file-audio-icon" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.5 8.33333L9.3356 7.34694C8.85341 7.4273 8.5 7.84449 8.5 8.33333H9.5ZM17.5 7H18.5C18.5 6.70605 18.3707 6.42698 18.1464 6.23698C17.9221 6.04698 17.6256 5.96528 17.3356 6.01361L17.5 7ZM4 2H21V0H4V2ZM23 4V21H25V4H23ZM21 23H4V25H21V23ZM2 21V4H0V21H2ZM4 23C2.89543 23 2 22.1046 2 21H0C0 23.2091 1.79086 25 4 25V23ZM23 21C23 22.1046 22.1046 23 21 23V25C23.2091 25 25 23.2091 25 21H23ZM21 2C22.1046 2 23 2.89543 23 4H25C25 1.79086 23.2091 0 21 0V2ZM4 0C1.79086 0 0 1.79086 0 4H2C2 2.89543 2.89543 2 4 2V0ZM10.5 17V8.33333H8.5V17H10.5ZM9.6644 9.31973L17.6644 7.98639L17.3356 6.01361L9.3356 7.34694L9.6644 9.31973ZM16.5 7V15.6667H18.5V7H16.5ZM8.5 17C8.5 17.5523 8.05228 18 7.5 18V20C9.15685 20 10.5 18.6569 10.5 17H8.5ZM7.5 18C6.94772 18 6.5 17.5523 6.5 17H4.5C4.5 18.6569 5.84315 20 7.5 20V18ZM6.5 17C6.5 16.4477 6.94772 16 7.5 16V14C5.84315 14 4.5 15.3431 4.5 17H6.5ZM7.5 16C8.05228 16 8.5 16.4477 8.5 17H10.5C10.5 15.3431 9.15685 14 7.5 14V16ZM16.5 15.6667C16.5 16.219 16.0523 16.6667 15.5 16.6667V18.6667C17.1569 18.6667 18.5 17.3235 18.5 15.6667H16.5ZM15.5 16.6667C14.9477 16.6667 14.5 16.219 14.5 15.6667H12.5C12.5 17.3235 13.8431 18.6667 15.5 18.6667V16.6667ZM14.5 15.6667C14.5 15.1144 14.9477 14.6667 15.5 14.6667V12.6667C13.8431 12.6667 12.5 14.0098 12.5 15.6667H14.5ZM15.5 14.6667C16.0523 14.6667 16.5 15.1144 16.5 15.6667H18.5C18.5 14.0098 17.1569 12.6667 15.5 12.6667V14.6667Z" fill="#A6B1BC"/>
          </svg>
          <div class="review-question-view__files-audio-name" data-bind="text: $data.name"></div>
        </div>
        <a class="d-flex align-items-center mb-15p print-audio" data-bind="attr: {href: window.location.origin + $data.url}" target="_blank">
          <svg class="mr-10p fs-file-audio-icon" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.5 8.33333L9.3356 7.34694C8.85341 7.4273 8.5 7.84449 8.5 8.33333H9.5ZM17.5 7H18.5C18.5 6.70605 18.3707 6.42698 18.1464 6.23698C17.9221 6.04698 17.6256 5.96528 17.3356 6.01361L17.5 7ZM4 2H21V0H4V2ZM23 4V21H25V4H23ZM21 23H4V25H21V23ZM2 21V4H0V21H2ZM4 23C2.89543 23 2 22.1046 2 21H0C0 23.2091 1.79086 25 4 25V23ZM23 21C23 22.1046 22.1046 23 21 23V25C23.2091 25 25 23.2091 25 21H23ZM21 2C22.1046 2 23 2.89543 23 4H25C25 1.79086 23.2091 0 21 0V2ZM4 0C1.79086 0 0 1.79086 0 4H2C2 2.89543 2.89543 2 4 2V0ZM10.5 17V8.33333H8.5V17H10.5ZM9.6644 9.31973L17.6644 7.98639L17.3356 6.01361L9.3356 7.34694L9.6644 9.31973ZM16.5 7V15.6667H18.5V7H16.5ZM8.5 17C8.5 17.5523 8.05228 18 7.5 18V20C9.15685 20 10.5 18.6569 10.5 17H8.5ZM7.5 18C6.94772 18 6.5 17.5523 6.5 17H4.5C4.5 18.6569 5.84315 20 7.5 20V18ZM6.5 17C6.5 16.4477 6.94772 16 7.5 16V14C5.84315 14 4.5 15.3431 4.5 17H6.5ZM7.5 16C8.05228 16 8.5 16.4477 8.5 17H10.5C10.5 15.3431 9.15685 14 7.5 14V16ZM16.5 15.6667C16.5 16.219 16.0523 16.6667 15.5 16.6667V18.6667C17.1569 18.6667 18.5 17.3235 18.5 15.6667H16.5ZM15.5 16.6667C14.9477 16.6667 14.5 16.219 14.5 15.6667H12.5C12.5 17.3235 13.8431 18.6667 15.5 18.6667V16.6667ZM14.5 15.6667C14.5 15.1144 14.9477 14.6667 15.5 14.6667V12.6667C13.8431 12.6667 12.5 14.0098 12.5 15.6667H14.5ZM15.5 14.6667C16.0523 14.6667 16.5 15.1144 16.5 15.6667H18.5C18.5 14.0098 17.1569 12.6667 15.5 12.6667V14.6667Z" fill="#A6B1BC"/>
          </svg>
          <span class="review-question-view__files-audio-name" data-bind="text: $data.name"></span>
        </a>
        

        <!-- /ko -->
        <!-- /ko -->
        
        <!-- /ko -->
        <!-- /ko -->

      <!-- /ko -->
      <!-- /Тип: 5 (загрузка файла) -->
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
      <div class="review-question-view-block  skip-block">
        <div
          class="review-question-view-block__text review-question-view-block__text--skipped"
          data-bind="
            text: $component.isHidden ?
              'Вопрос для респондента не отображался' :
              'Вопрос пропущен',
          "
        ></div>
      </div>
      <!-- /ko -->
    </div>
    <!-- .review-question-view__answer-section -->
  </div>
  <!-- .review-question-view__body -->
  <!-- Комментарий -->
  <!-- ko if: answer -->
  <!-- ko template: {name: 'review-question-view-comment-template', data: {text: answer.answer.comment }} -->
  <!-- /ko -->
  <!-- /ko -->
  <!-- /Комментарий -->
</div>
<!-- .review-question-view -->
<!-- /ko -->
