<div data-bind="component: {
  name: componentName,
  params: {
    question: question,
    review: review
  }
}"></div>

<template type="text/html"
          id="rating-template">
  <div class="review-rating">
    <i class="review-rating__item"
       data-bind="css: { 'review-rating__item--active': value >= 1 }">
    </i>

    <i class="review-rating__item"
       data-bind="css: { 'review-rating__item--active': value >= 2 }">
    </i>

    <i class="review-rating__item"
       data-bind="css: { 'review-rating__item--active': value >= 3 }">
    </i>

    <i class="review-rating__item"
       data-bind="css: { 'review-rating__item--active': value >= 4 }">
    </i>

    <i class="review-rating__item"
       data-bind="css: { 'review-rating__item--active': value >= 5 }">
    </i>
  </div>
</template>

<template id="review-question-view-clarifying-question-template">
  <!-- ko if: showClarifyingQuestion() && clarifyingQuestion && clarifyingQuestion !== null -->
  <div class="review-question-view-clarifying-question">
    <div class="review-question-view-clarifying-question__text">
      <!-- ko text: clarifyingQuestion -->
      <!-- /ko -->
      <!-- ko if: !window.IS_EXTERNAL -->
      <!-- ko if: $data.forAllRates || (!starRatingOptions.extra_question_rate_from && !starRatingOptions.extra_question_rate_to) -->
      <small class="service-text" data-bind="text: '(' + _t('для всех оценок') +')'"></small>
      <!-- /ko -->
      <!-- ko ifnot: $data.forAllRates || (!starRatingOptions.extra_question_rate_from && !starRatingOptions.extra_question_rate_to) -->
      <small class="service-text" data-bind="text: '(' + _t(`для оценок ${starRatingOptions.extra_question_rate_from}-${starRatingOptions.extra_question_rate_to}`) +')'"></small>
      <!-- /ko -->
      <!-- /ko -->
    </div>

    <!-- ko if: $data.variantsType == 2 -->
    <!-- ko if: answer.selfVariant -->
    <div class="review-question-view-block review-question__self-answer">
      <div class="f-fs-2"
           data-bind="text: answer.selfVariant">
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: $data.variantsType == 2 -->
    <div class="review-question-view-variants">

      <!-- ko if: !$data.variants.length && !answer.selfVariant -->
      –
      <!-- /ko -->

      <!-- ko foreach: $data.variants -->
      <!-- ko let: {
        variantSelected: $parent.answer.selectedIds && $parent.answer.selectedIds.includes('' + $data.id),
      } -->
      <div
        class="review-question-view-variant"
        data-bind="
          css: {
            'review-question-view-variant--selected': variantSelected,
          },
          visible: !window.IS_EXTERNAL || variantSelected,
        "
      >
        <!-- ko text: $data.variant -->
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- /ko -->

      <!-- ko if: answer.selfVariant -->
      <div class="review-question-view-variant review-question-view-variant--self-variant review-question-view-variant--selected">
        <!-- ko text: $data.self_variant_text || 'Свой вариант' -->
        <!-- /ko -->

        <div class="review-question-view-variant__custom-variant"
             data-bind="text: answer.selfVariant">
        </div>
      </div>
      <!-- /ko -->

    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
</template>

<template id="review-question-view-clarifying-question-template-new">
  <!-- ko if: showClarifyingQuestion() && clarifyingQuestion && clarifyingQuestion !== null -->
  <div class="review-question-view-clarifying-question new-type" data-bind="css: {'external': window.IS_EXTERNAL}">
    <div class="review-question-view-clarifying-question__text">
      <!-- ko text: clarifyingQuestion -->
      <!-- /ko -->
      <!-- ko if: !window.IS_EXTERNAL -->
      <!-- ko if: $data.forAllRates || (!$data.extra_question_rate_from && !$data.extra_question_rate_to) -->
      <small class="service-text" data-bind="text: '(' + _t('для всех оценок') +')'"></small>
      <!-- /ko -->
      <!-- ko ifnot: $data.forAllRates || (!$data.extra_question_rate_from && !$data.extra_question_rate_to) -->
      <small class="service-text" data-bind="text: '(' + _t(`для оценок ${$data.extra_question_rate_from}-${$data.extra_question_rate_to}`) +')'"></small>
      <!-- /ko -->
      <!-- /ko -->
    </div>

    <!-- ko if: $data.variantsType == 2 -->
    <!-- ko if: $data.answer.text_answer || ($parent.getExtraComment && $parent.getExtraComment()) -->
    <div class="review-question-view-block review-question__self-answer">
      <div class="f-fs-2"
           data-bind="text: $data.answer.text_answer || ($parent.getExtraComment && $parent.getExtraComment())">
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: $data.variantsType == 2 -->
    <div class="review-question-view-variants">

      <!-- ko if: !$data.variants.length && !answer.selfVariant -->
      –
      <!-- /ko -->

      <!-- ko foreach: { data: $data.clarifyingQuestionVariants, as: 'variant' } -->
      <!-- ko let: {
        variantSelected: $parent.isExtraVariantPicked(null, variant.id),
      } -->
      <div
        class="review-question-view-variant align-items-center"
        data-bind="
          css: {
            'review-question-view-variant--selected': variantSelected,
          },
          visible: !window.IS_EXTERNAL || variantSelected,
        "
      >
          <!-- ko if: variant.file_id -->
          <a
            data-bind="attr: {href: window.location.origin + variant.file_url}"
            target="_blank"
            class="file-loader-preview_review-print-link"
          >
            <img data-bind="attr: { src: variant.preview_url }" />
          </a>
          <file-loader-preview
            class="file-loader-preview file-loader-preview_review mr-15p"
            data-bind="click: (_, event) => event.stopPropagation()"
            params="
              loading: false,
              disabled: true,
              file: variant.file_url,
              preview: variant.preview_url,
            "
          ></file-loader-preview>
          <!-- /ko -->
        <!-- ko text: $data.text -->
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- /ko -->

      <!-- ko if: $data.answer?.self_variant || $data.answer?.selfVariant -->
      <!-- ko let: { selfVariant: $data.answer?.self_variant || $data.answer?.selfVariant }-->
      <div class="review-question-view-variant review-question-view-variant--self-variant review-question-view-variant--selected d-flex">
        <!-- ko if: $data.self_variant_file && $data.self_variant_file.file_id -->
          <a data-bind="attr: {href: window.location.origin + $data.self_variant_file.file_url}" target="_blank" class="file-loader-preview_review-print-link">
            <img
              data-bind="attr: {
                          src: $data.self_variant_file.preview_url
                      }"
              alt=""
            />
          </a>
          <file-loader-preview class="file-loader-preview file-loader-preview_review mr-15p" data-bind="click: function (_, event) {
            event.stopPropagation();
            }," params="loading: false, disabled: true, file: $data.self_variant_file.file_url, preview: $data.self_variant_file.preview_url,
            onRemove: function() { 
                variant.file(null)
                variant.value('')
            }">
          </file-loader-preview>
        <!-- /ko -->
        <div>
          <!-- ko text: $data.self_variant_text || 'Свой вариант' -->
          <!-- /ko -->

          <div class="review-question-view-variant__custom-variant"
                data-bind="text: selfVariant">
          </div>
        </div>
        
      </div>
      <!-- /ko -->
      <!-- /ko -->

    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
</template>

<template id="review-question-variant-view-clarifying-question-template">
  <!-- ko let: { variantData: answerData.extra && answerData.extra[+variant.id] ? answerData.extra[+variant.id] : answer.detail_item && answer.detail_item[+variant.id] ? answer.detail_item[+variant.id] : null }-->
  <!-- ko if: clarifyingQuestion && clarifyingQuestion !== null && variantData -->
  <div class="review-question-view-clarifying-question">
    <!-- ko if: $data.variantsType == 2 -->
    <!-- ko if: variantData.answer -->
    <div class="review-question-view-block review-question-view-block--comment review-question__self-answer">
      <div
        class="clarifying-question__answer-text"
        data-bind="text: variantData.answer"></div>
    </div>
    <!-- /ko -->
    <!-- ko ifnot: variantData.answer -->
    <div class="">
      <div class="clarifying-question__answer-text">–
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: $data.variantsType == 2 -->
    <div class="review-question-view-variants">
      <!-- ko if: !$data.variants.length && !answer.selfVariant -->
      –
      <!-- /ko -->
      <!-- ko foreach: clarifyingQuestionVariants -->
      <!-- ko if: $parent.isExtraVariantPicked(variant.id, id) -->
      <div
        class="review-question-view-variant review-question-view-variant--selected"
        data-bind="text: text, click: function () {console.log($data)}"
      ></div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko if: variantData.self_variant -->
      <div class="review-question-view-variant review-question-view-variant--self-variant review-question-view-variant--selected">
        <div
          class="review-question-view-variant__custom-variant-header"
          data-bind="text: self_variant_text || 'Свой вариант'"
        ></div>
        <div
          class="review-question-view-variant__custom-variant"
          data-bind="text: variantData.self_variant"
        ></div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- ko if: extra_question_type == 3 && variantData -->
  <div class="review-question-view-clarifying-question">
    <!-- ko if: variant.variants_element_type == 2 -->
    <!-- ko if: variantData.answer -->
    <div class="review-question-view-block review-question-view-block--comment review-question__self-answer">
      <div
        class="clarifying-question__answer-text"
        data-bind="text: variantData.answer"></div>
    </div>
    <!-- /ko -->
    <!-- ko ifnot: variantData.answer -->
    <div class="">
      <div class="clarifying-question__answer-text">–
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: variant.variants_element_type == 2 -->
    <div class="review-question-view-variants">
      <!-- ko if: !$data.variants.length && !answer.selfVariant -->
      –
      <!-- /ko -->
      <!-- ko foreach: clarifyingQuestionVariants -->
      <!-- ko if: $parent.isExtraVariantPicked(variant.id, id) -->
      <div
        class="review-question-view-variant review-question-view-variant--selected"
        data-bind="text: text"
      ></div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko if: variantData.self_variant -->
      <div class="review-question-view-variant review-question-view-variant--self-variant review-question-view-variant--selected">
        <div
          class="review-question-view-variant__custom-variant-header"
          data-bind="text: self_variant_text || 'Свой вариант'"
        ></div>
        <div
          class="review-question-view-variant__custom-variant"
          data-bind="text: variantData.self_variant"
        ></div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /ko -->
</template>

<template id="review-question-variant-view-clarifying-question-template-multiple">
  <!-- ko let: { variantData: answerData.extra && answerData.extra[+variant.id] ? answerData.extra[+variant.id] : answer.detail_item && answer.detail_item[+variant.id] ? answer.detail_item[+variant.id] : null }-->
  <!-- ko if: clarifyingQuestion && clarifyingQuestion !== null && variantData && extra_question_type !== 3 -->
  <div class="review-question-view-clarifying-question">
    <!-- ko if: $data.variantsType == 2 -->
    <!-- ko if: variantData.answer -->
    <div class="review-question-view-block review-question-view-block--comment review-question__self-answer">
      <div
        class="clarifying-question__answer-text"
        data-bind="text: variantData.answer"></div>
    </div>
    <!-- /ko -->
    <!-- ko ifnot: variantData.answer -->
    <div class="">
      <div class="clarifying-question__answer-text">–
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: $data.variantsType == 2 -->
    <div class="review-question-view-variants">
      <!-- ko if: !$data.variants.length && !answer.selfVariant -->
      –
      <!-- /ko -->
      <!-- ko foreach: clarifyingQuestionVariants -->
      <!-- ko if: $parent.isExtraVariantPicked(variant.id, id) -->
      <div
        class="review-question-view-variant align-items-center review-question-view-variant--selected"
      >
          <!-- ko if: $data.file_id -->
          <a data-bind="attr: {href: window.location.origin + $data.file_url}" target="_blank" class="file-loader-preview_review-print-link">
            <img
              data-bind="attr: {
                          src: $data.preview_url
                      }"
              alt=""
            />
          </a>
          <file-loader-preview class="file-loader-preview file-loader-preview_review mr-15p" data-bind="click: function (_, event) {
            event.stopPropagation();
          }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url,
          ">

        </file-loader-preview>
          <!-- /ko -->
        <!-- ko text: $data.text -->
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko if: variantData.self_variant -->
      <div class="review-question-view-variant review-question-view-variant--self-variant review-question-view-variant--self-variant-nps-clarify review-question-view-variant--selected">
        <!-- ko if: $data.self_variant_file && $data.self_variant_file.file_id -->
        <a data-bind="attr: {href: window.location.origin + $data.self_variant_file.file_url}" target="_blank" class="file-loader-preview_review-print-link">
          <img
            data-bind="attr: {
                        src: $data.self_variant_file.preview_url
                    }"
            alt=""
          />
        </a>
        <file-loader-preview class="file-loader-preview file-loader-preview_review mr-15p" data-bind="click: function (_, event) {
          event.stopPropagation();
          }," params="loading: false, disabled: true, file: $data.self_variant_file.file_url, preview: $data.self_variant_file.preview_url,
          onRemove: function() { 
              variant.file(null)
              variant.value('')
          }">
        </file-loader-preview>
      <!-- /ko -->
      <div>
        <div
          class="review-question-view-variant__custom-variant-header"
          data-bind="text: self_variant_text || 'Свой вариант', click: function () {console.log(question)}"
        ></div>
        <div
          class="review-question-view-variant__custom-variant"
          data-bind="text: variantData.self_variant"
        ></div>
      </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- ko if: extra_question_type == 3 && variantData -->
  <div class="review-question-view-clarifying-question">
    <!-- ko if: typeof variantData.answer === 'string' -->
    <!-- ko if: variantData.answer -->
    <div class="review-question-view-block review-question-view-block--comment review-question__self-answer">
      <div
        class="clarifying-question__answer-text"
        data-bind="text: variantData.answer"></div>
    </div>
    <!-- /ko -->
    <!-- ko ifnot: variantData.answer -->
    <div class="">
      <div class="clarifying-question__answer-text">–
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: typeof variantData.answer === 'string' -->
    <div class="review-question-view-variants">
      <!-- ko if: !$data.variants.length && !answer.selfVariant -->
      –
      <!-- /ko -->
      <!-- ko foreach: clarifyingQuestionVariants -->
      <!-- ko if: $parent.isExtraVariantPicked(variant.id, id) -->
      <div
        class="review-question-view-variant align-items-center review-question-view-variant--selected"
      >
          <!-- ko if: $data.file_id -->
          <a data-bind="attr: {href: window.location.origin + $data.file_url}" target="_blank" class="file-loader-preview_review-print-link">
            <img
              data-bind="attr: {
                          src: $data.preview_url
                      }"
              alt=""
            />
          </a>
          <file-loader-preview class="file-loader-preview file-loader-preview_review mr-15p" data-bind="click: function (_, event) {
            event.stopPropagation();
          }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url,
          ">

        </file-loader-preview>
          <!-- /ko -->
        <!-- ko text: $data.text -->
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko if: variantData.self_variant -->
      <div class="review-question-view-variant review-question-view-variant--self-variant review-question-view-variant--self-variant-nps-clarify review-question-view-variant--selected">
        <!-- ko if: $parent.selfVariantFile && $parent.selfVariantFile.file_id -->
        <a data-bind="attr: {href: window.location.origin + $parent.selfVariantFile.file_url}" target="_blank" class="file-loader-preview_review-print-link">
          <img
            data-bind="attr: {
                        src: $parent.selfVariantFile.preview_url
                    }"
            alt=""
          />
        </a>
        <file-loader-preview class="file-loader-preview file-loader-preview_review mr-15p" data-bind="click: function (_, event) {
          event.stopPropagation();
          }," params="loading: false, disabled: true, file: $parent.selfVariantFile.file_url, preview: $parent.selfVariantFile.preview_url,
          onRemove: function() { 
              variant.file(null)
              variant.value('')
          }">
        </file-loader-preview>
      <!-- /ko -->
      <div>
        <div
          class="review-question-view-variant__custom-variant-header"
          data-bind="text: self_variant_text || 'Свой вариант', click: function () {console.log($parent)}"
        ></div>
        <div
          class="review-question-view-variant__custom-variant"
          data-bind="text: variantData.self_variant"
        ></div>
      </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /ko -->
</template>

<template id="review-question-variant-view-clarifying-matrix-question-template">
  <div class="review-question-view-clarifying-question">
    <!-- ko if: $data.variantsType == 2 -->
    <!-- ko if: answerData.extra[row] && answerData.extra[row].answer -->
    <div class="review-question-view-block review-question-view-block--comment review-question__self-answer">
      <div
        class="clarifying-question__answer-text"
        data-bind="text: answerData.extra[row].answer"></div>
    </div>
    <!-- /ko -->
    <!-- ko ifnot: answerData.extra[row] && answerData.extra[row].answer -->
    <div class="">
      <div class="clarifying-question__answer-text">–
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: $data.variantsType == 2 -->
    <div class="review-question-view-variants">
      <!-- ko if: !$data.variants.length && !answer.selfVariant -->
      –
      <!-- /ko -->
      <!-- ko foreach: clarifyingQuestionVariants -->
      <!-- ko if: $parent.isExtraVariantPicked(row, id) -->
      <div
        class="review-question-view-variant review-question-view-variant--selected"
        data-bind="text: text"
      ></div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko if: answerData.extra[row].self_variant -->
      <div class="review-question-view-variant review-question-view-variant--self-variant review-question-view-variant--selected">
        <div
          class="review-question-view-variant__custom-variant-header"
          data-bind="text: self_variant_text || 'Свой вариант'"
        ></div>
        <div
          class="review-question-view-variant__custom-variant"
          data-bind="text: answerData.extra[row].self_variant"
        ></div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
</template>

<template id="review-question-view-variants-template">
  <!-- ko if: $data.variants && $data.variants.length > 0 -->
  <div class="review-question-view-variants">
    <!-- ko foreach: $data.variants -->
    <div class="review-question-view-variant"
         data-bind="css: {
               'review-question-view-variant--selected':  $parent.answer.selectedIds && $parent.answer.selectedIds.includes('' + $data.id)
              }">
      <!-- ko text: $data.variant -->
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- ko if: answer.selfVariant -->
    <div class="review-question-view-variant review-question-view-variant--selected">
      <!-- ko text: $data.self_variant_text || 'Свой вариант' -->
      <!-- /ko -->

      <div class="review-question-view-variant__custom-variant"
           data-bind="text: answer.selfVariant">
      </div>
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
</template>

<template id="review-question-view-comment-template">
  <!-- ko if: $data.text -->
  <div
    class="review-question-view-block review-question-view-comment"
    data-bind="
      css: {
        'mt-3': !$parent.clarifyingQuestion || !$parent.usesClarifyingQuestion,
        'review-question-view-comment--clarifying': $parent.showClarifyingQuestion() && $parent.clarifyingQuestion && $parent.clarifyingQuestion !== null,
      },
    "
  >
    <!-- ko if: (!$parent.clarifyingQuestion || !$parent.usesClarifyingQuestion) -->
    <div
      class="review-question-view-block__title"
      data-bind="text: _t('answers', 'Комментарий')"
    ></div>
    <!-- /ko -->
    <div
      class="review-question-view-block__small-text"
      data-bind="text: $data.text"
    ></div>
  </div>
  <!-- /ko -->
</template>

<template id="review-question-view-video-template">
  <span class="review-question-view-video" data-bind="fancybox: $data.fancy">
     <p class="review-question-view-video__preview-svg">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 49 48" fill="none">
          <path d="M1.5 7C1.5 3.68629 4.18629 1 7.5 1H41.5C44.8137 1 47.5 3.68629 47.5 7V41C47.5 44.3137 44.8137 47 41.5 47H7.5C4.18629 47 1.5 44.3137 1.5 41V7Z" stroke="white" stroke-width="2"/>
          <path d="M32.1939 26.639C33.9354 25.6752 33.9354 23.2659 32.1939 22.3022L21.4184 16.3392C19.6769 15.3755 17.5 16.5801 17.5 18.5076V30.4336C17.5 32.3611 19.6769 33.5657 21.4184 32.602L32.1939 26.639Z" stroke="white" stroke-width="2"/>
        </svg>
     </p>

    <!-- ko if: ['avi', '3gp', 'flv'].some(ext => $data.url.toLowerCase().endsWith('.' + ext))  -->
      <img data-bind="attr: { src:  $data.preview_url }"
           class="review-question-view-video__preview">
      <span class="review-question-view-video__mask"></span>
      <span class="review-question-view-video__play-button"></span>
    <!-- /ko -->

    <!-- ko ifnot: ['avi', '3gp', 'flv'].some(ext => $data.url.toLowerCase().endsWith('.' + ext)) -->
      <video class="review-question-view-video__content">
        <source data-bind="attr: { src: $data.url }">
      </video>
      <span class="review-question-view-video__mask"></span>
      <span class="review-question-view-video__play-button"></span>
    <!-- /ko -->
  </span>
</template>

<template id="review-question-view-audi-amr-template">
  <span class="review-question-view-audi-amr-template" data-bind="fancybox: $data.fancy">
    <audio class="review-question-view-video__content" controls data-bind="attr: { src: $data.url }">
    </audio>
  </span>
</template>

<template id="review-question-view-audio-template">
  <span class="review-question-view-video">
    <audio class="review-question-view-video__content" controls data-bind="attr: { src: $data.url }">
      Тег audio не поддерживается вашим браузером.
    </audio>

    <span class="review-question-view-video__mask"></span>
    <span class="review-question-view-video__play-button"></span>
  </span>
</template>


<template id="review-question-view-question-gallery-item-template">
  <div class="review-question-view-media">
    <!-- ko if: item.type == 'image' -->
    <img class="review-question-view-image"
         data-bind="
      attr: { src: item.src },
      fancyboxGalleryItem: {
        gallery: gallery,
        index: index
      }">
    <!-- /ko -->

    <!-- ko if: item.type == 'video' -->
    <div class="review-question-view-video"
         data-bind="fancyboxGalleryItem: {
        gallery: gallery,
        index: index
      }">

      <video class="review-question-view-video__content"
             data-bind="attr: { src: item.src, poster: item.preview || item.poster }">

        Тег video не поддерживается вашим браузером.
      </video>

      <div class="review-question-view-video__mask"></div>
      <div class="review-question-view-video__play-button"></div>
    </div>
    <!-- /ko -->


  </div>

  <!-- ko if: item.description -->
  <div class="f-fs-2 mt-2 f-color-text"
       data-bind="text: item.description"></div>
  <!-- /ko -->
</template>

<template id="review-question-view-question-gallery-template">
  <!-- ko if: gallery.length -->
  <div class="review-question-view__media-section"
       data-bind="let: {
    galleryObject: gallery,
    fancyGallery: gallery.map(function(item) {
      return {
        src: item.src,
        opts: {
          caption: item.description || ''
        }
      }
    })
  }">


    <!-- ko if: galleryObject.length == 1 -->
    <!-- ko template: {
        name: 'review-question-view-question-gallery-item-template',
        data: {
          gallery: fancyGallery,
          item: galleryObject[0],
          index: 0
        }
      } -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: galleryObject.length > 1 -->
    <div class="review-question-view-slider"
         data-bind="component: { name: 'statistics__details-modal-dialog-slider' }">
      <!-- ko foreach: galleryObject -->
      <div data-bind="component: {
          name: 'statistics__details-modal-dialog-slide',
          params: { index: $index(), count: galleryObject.length }
        }">
        <!-- ko template: {
            name: 'review-question-view-question-gallery-item-template',
            data: {
              gallery: fancyGallery,
              item: galleryObject[$index()],
              index: $index()
            }
          } -->
        <!-- /ko -->
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->


  </div>
  <!-- /ko -->
</template>
