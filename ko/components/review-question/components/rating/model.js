import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);
    this.question = ReviewQuestionFactory(params.question);
    this.active = params.active;
    this.review = params.review;

    this.index = params.index;
    this.formattedIndex =
      parseInt(params.index + 1) > 9
        ? params.index + 1
        : '0' + (params.index + 1);

    this.hasComment = ReviewAbstractModel.isQuestionCommented(this.question);

    this.rating = ReviewAbstractModel.getQuestionRating(this.question);

    this.type = params.type;

    this.disabled = !this.question.hasAnswer;

    this.onClick = () => {
      if (!this.disabled) params.onClick(this.index);
    };
  }
}
