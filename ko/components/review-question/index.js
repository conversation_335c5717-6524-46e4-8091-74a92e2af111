import { ViewModel } from "./model";
import html from "./template.html";
import "./style.less";

import "./types/base";
import "./types/stars";
import "./types/variants";
import "./types/text";
import "./types/date";
import "./types/address";
import "./types/quiz";
import "./types/gallery";
import "./types/diff";
import "./types/dishes";
import "./types/matrix";
import "./types/matrix-3d";
import "./types/media-variants";
import "./types/nps";
import "./types/priority";
import "./types/smile";
import "./types/star-variants";
import "./types/rating";
import "./types/filials";
import "./types/classifier";
import "./types/scale";
import "./types/distribution-scale";
import "./types/card-sorting";


ko.components.register("review-question", {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add("review-question");

      return new ViewModel(params, element);
    },
  },
  template: html,
});
