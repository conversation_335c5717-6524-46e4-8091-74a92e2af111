import { FoquzComponent } from "Models/foquz-component";

function getQuestionType(question) {
  switch (parseInt(question.type)) {
    case 0:
      return question.mediaType;
    case 1:
      return "vars";
    case 2:
      return "text-input";
    case 3:
      return "date";
    case 4:
      return "address";
    case 5:
      return "upload";
    case 6:
      return "quiz";
    case 7:
      return "star-variants";
    case 8:
      return "priority";
    case 9:
      return "media-variants";
    case 10:
      return "gallery";
    case 11:
      return "smile";
    case 12:
      return "nps";
    case 13:
      return "matrix";
    case 14:
      return "diff";
    case 15:
      return "stars";
    case 17:
      return "filials";
    case 18:
      return "rating";
    case 19:
      return "classifier";
    case 20:
      return "scale";
    case 21:
      return "matrix-3d";
    case 22:
      return "card-sorting";
    case 23:
      return "distribution-scale";
  }
}

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);
    this.question = ko.unwrap(params.question);
    this.type = getQuestionType(this.question);
    this.review = params.review;

    this.componentName = "review-question-base";

    let type = parseInt(this.question.type);

    switch (type) {
      case 0:
        if (this.question.answer && this.question.answer.dishRatings) {
          this.componentName = "review-question-dishes";
        }
        break;
      case 1:
        this.componentName = "review-question-variants";
        break;
      case 2:
        this.componentName = "review-question-text";
        break;
      case 3:
        this.componentName = "review-question-date";
        break;
      case 4:
        this.componentName = "review-question-address";
        break;
      case 6:
        this.componentName = "review-question-quiz";
        break;
      case 7:
        this.componentName = "review-question-star-variants";
        break;
      case 8:
        this.componentName = "review-question-priority";
        break;
      case 9:
        this.componentName = "review-question-media-variants";
        break;
      case 10:
        this.componentName = "review-question-gallery";
        break;
      case 11:
        this.componentName = "review-question-smile";
        break;
      case 12:
        this.componentName = "review-question-nps";
        break;
      case 13:
        this.componentName = "review-question-matrix";
        break;
      case 14:
        this.componentName = "review-question-diff";
        break;
      case 15:
        this.componentName = "review-question-stars";
        break;
      case 17:
        this.componentName = "review-question-filials";
        break;
      case 18:
        this.componentName = "review-question-rating";
        break;
      case 19:
        this.componentName = "review-question-classifier";
        break;
      case 20:
        this.componentName = "review-question-scale";
        break;
      case 21:
        this.componentName = "review-question-matrix-3d";
        break;
      case 22:
        this.componentName = "review-question-card-sorting";
        break;
      case 23:
        this.componentName = "review-question-distribution-scale";
        break;
    }
  }
}
