<div
  class="variant-switcher__swiper-holder mb-10p"
  data-bind="visible: !!scrollbar()"
>
  <button
    data-bind="
      visible: !!arrowLeft(),
      click: () => prev()
    "
    class="variant-switcher__nav-button swiper-button-prev"
  ></button>
  <div
    class="swiper-container variant-switcher__swiper-container"
    data-bind="
      nativeScrollbar,
      ref: scrollbar,
      onScroll: (viewport) => onScroll(viewport),
    "
  >
    <div
      class="btn-group variant-switcher swiper-wrapper"
      role="group"
    >
      <!-- ko foreach: {
        data: items,
        as: 'item',
        noChildContext: true,
      } -->
        <button
          type="button"
          class="swiper-slide btn btn-default variant-switcher__button"
          data-bind="
            css: {
              'variant-switcher__button--active': currentIndex() === $index(),
            },
            click: () => currentIndex($index()),
          "
        >
          <!-- ko text: getText(item, $index()) -->
          <!-- /ko -->
          <!-- ko if: !!getHint -->
            <span
              class="variant-switcher__hint"
              data-bind="html: getHint(item, $index())"
            ></span>
          <!-- /ko -->
        </button>
      <!-- /ko -->
    </div>
  </div>
  <button
    data-bind="
      visible: arrowRight(),
      click: () => next()
    "
    class="variant-switcher__nav-button swiper-button-next"
  ></button>
</div>