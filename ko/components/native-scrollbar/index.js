import './style.less';
import html from './template.html';
const { observable, observableArray } = ko;

class ViewModel {
  constructor({ items, getText, getHint, currentIndex }) {
    this.items = items;
    this.getText = getText;
    this.getHint = getHint;
    this.currentIndex = currentIndex;
    
    this.arrowLeft = observable(false);
    this.arrowRight = observable(false);

    this.scrollbar = observable(null);
    this.scrollbar.subscribe(() => {
      if (!this.scrollbar()?.instance) return;
      this.setArrowState();
    })
  }

  setArrowState() {
    const { viewport } = this.scrollbar()?.instance?.getElements();
    this.arrowLeft(viewport.scrollLeft > 10);
    this.arrowRight(viewport.clientWidth + viewport.scrollLeft + 10 < viewport.scrollWidth);
  }

  onScroll() {
    this.setArrowState();
  }

  next() {
    const { viewport } = this.scrollbar()?.instance?.getElements();
    const { scrollLeft } = viewport;
    viewport.scrollTo({ left: scrollLeft + 100 });
  }

  prev() {
    const { viewport } = this.scrollbar()?.instance?.getElements();
    const { scrollLeft } = viewport;
    viewport.scrollTo({ left: scrollLeft - 100 });
  }
}

ko.components.register('native-scrollbar', {
  viewModel: ViewModel,
  template: html
});
