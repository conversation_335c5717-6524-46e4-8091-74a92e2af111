.variant-switcher__button {
  background-color: #F1F5F6;
  color: #73808D;
  border: 1px solid #CFD8DC !important;
  padding: 17.5px 15.5px;
  min-height: 48px;
  min-width: 50px;
  font-size: 16px;
  line-height: 13px;
  max-width: fit-content;
  position: relative;

  &:hover, &--active {
    background-color: white;
    color: #2E2F31;
  }
}

.variant-switcher__hint {
  position: absolute;
  pointer-events: none;
  top: 0;
  right: 0;
}

.variant-switcher__nav-button {
  background: #ffffff;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='14' viewBox='0 0 8 14' fill='none'%3E%3Cpath d='M1 13L7 7L1 1' stroke='%2373808D' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  box-shadow: 0 5px 15px rgba(46, 47, 49, 0.3);
  border: none;
  border-radius: 50%;
  width: 34px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0;
  transform: translateY(-50%);

  &.swiper-button-disabled {
    display: none;
  }
  &.swiper-button-prev {
    left: 0;
    transform: translate(-50%; -50%) rotate(180deg);
  }
  &.swiper-button-next {
    right: 0;
    transform: translate(50%; -50%);
  }
}

.variant-switcher__swiper-container {
  padding-bottom: 3px;
  .os-scrollbar {
    display: none;
  }
}

.variant-switcher__swiper-holder {
  position: relative;
}