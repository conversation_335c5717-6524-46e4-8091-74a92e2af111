<!-- ko template: { afterRender: $component.init.bind($component) } -->


<div class="survey-list__element-card-section poll-card__card">

  <!-- ko if: !window.CURRENT_USER.watcher -->
  <!-- ko ifnot: archived -->
  <div class="poll-card__checked survey-list__survey-card-checked f-check"
       data-bind="let: {
    inputId: 'survey-' + unique
  }">
    <input type="checkbox"
           class="f-check-input"
           data-bind="checked: checked, attr: {
      id: inputId
    }" />
    <label class="f-check-label"
           data-bind="attr: {
      'for': inputId
    }, click: function() {
      $root.toggleItemCheck($data);
    }"></label>
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <div class="survey-list__element-card-section-content">
    <a href="#"
       class="survey-list__element-card-section-background survey-list__survey-card-section-background"
       data-bind="style: { 'background-image': 'url(' + backgroundUrl  + ')' }, attr: {href:actualLink}">
    </a>

    <div class="poll-card__header">

      <!-- ko if: favour -->
      <svg-icon params="name: 'rating-star-fill'"
                data-bind="tooltip, click: function() {
                  if (window.CURRENT_USER.watcher) {
                    return;
                  }
                  favour(false);
                  addToFavorite();
                }, tooltipText: _t('Добавлено в избранное')"
                class="poll-card__favour f-color-yellow mr-10p cursor-pointer"></svg-icon>
      <!-- /ko -->

      <!-- ko if: !window.CURRENT_USER.watcher -->
      <!-- ko ifnot: favour -->
      <svg-icon params="name: 'rating-star-empty'"
                data-bind="tooltip, click: function() {
                  favour(true);
                  addToFavorite();
                }, tooltipText: _t('Добавить в избранное')"
                class="poll-card__favour f-color-yellow mr-10p cursor-pointer"></svg-icon>
      <!-- /ko -->
      <!-- /ko -->

      <span class="survey-list__element-card-created-at mr-10p"
            data-bind="text: createdAt"></span>

      <!-- ko ifnot: isAuto -->
      <svg-icon params="name: 'type-manual', width: 18, height: 18"
                class="poll-card__type mr-10p f-color-service"
                data-bind="tooltip, tooltipText: _t('Ручной опрос')"></svg-icon>
      <!-- /ko -->

      <!-- ko if: isAuto -->
      <svg-icon params="name: 'type-auto', width: 18, height: 18"
                class="poll-card__type mr-10p f-color-service"
                data-bind="tooltip, tooltipText: _t('Автоматический опрос')"></svg-icon>
      <!-- /ko -->

      

      <!-- ko ifnot: model.isDurationAvailable -->
      <svg-icon params="name: 'alarm', width: 18, height: 18"
                class="poll-card__alarm f-color-danger mr-10p"
                data-bind="tooltip, tooltipText:  model.durationText"></svg-icon>
      <!-- /ko -->

      <!-- ko if: isActive -->
      <fc-icon params="name: 'start', color: '#37A74A'"  data-bind="tooltip, tooltipText: 'Опрос активен'"></fc-icon>
      <!-- /ko -->

      <!-- ko ifnot: isActive -->
          <fc-icon params="name: 'stop'" data-bind="tooltip, tooltipText: 'Опрос неактивен и не собирает ответы'"></fc-icon>
      <!-- /ko -->
    </div>

    <!-- ko template: { name: 'survey-list-element-card-statistics-template', data: statistics } -->
    <!-- /ko -->

    <div class="survey-list__survey-card-owner">
      <span class="userpic">
        <img class=""
             data-bind="attr: { src: ownerAvatarUrl }">
      </span>

      <span class="survey-list__survey-card-owner-name"
            data-bind="text: ownerName"></span>
    </div>
  </div>

  <div class="survey-list__element-card-dropdown"
       data-bind="dropdown, dropdownClass: 'poll-menu-tippy'">
    <button class="btn survey-list__menu-button"
            type="button"
            data-dropdown-target>
    </button>

    <template>
      <div class="tippy-mask"
           data-dropdown-close></div>
      <div class="tippy-list poll-card-dropdown">

        <button class="button-ghost tippy-close"
                data-dropdown-close>
          <svg-icon params="name: 'times'"
                    class="f-color-service svg-icon--sm"></svg-icon>
        </button>

        <a class="tippy-list__item"
           data-bind="attr: { href: model.statsLink }, text: _t('Статистика')"></a>
        <a class="tippy-list__item"
           data-bind="attr: { href: model.answersLink }, text: _t('Ответы')"></a>

        <!-- ko if: !window.CURRENT_USER.watcher -->
        <hr>

        <a class="tippy-list__item"
           href="javascript:void(0)"
           data-bind="click: function () { duplicateClick(); }">
          <div class="d-flex align-items-center">
            <svg-icon params="name: 'poll-copy'"
                      class="f-color-service mr-3"></svg-icon>
            <span data-bind="text: _t('Дублировать')"></span>
          </div>
        </a>
        <a class="tippy-list__item"
           href="javascript:void(0)"
           data-bind="click: function () { moveClick(); }">
          <div class="d-flex align-items-center">
            <svg-icon params="name: 'poll-move'"
                      class="f-color-service mr-3"></svg-icon>
            <span data-bind="text: _t('Переместить')"></span>
          </div>
        </a>
        <!-- ko ifnot: isAuto -->
        <a class="tippy-list__item"
           data-bind="attr: { href: '/foquz/foquz-poll/download-json?id=' + model.id }">
          <div class="d-flex align-items-center">
            <svg-icon params="name: 'poll-export'"
                      class="f-color-service mr-3"></svg-icon>
            <span data-bind="text: _t('Экспортировать')"></span>
          </div>
        </a>
        <!-- /ko -->
        <a class="tippy-list__item"
           href="javascript:void(0)"
           data-bind="click: function () { archiveClick(); }">
          <div class="d-flex align-items-center">
            <svg-icon params="name: 'poll-archive'"
                      class="f-color-service mr-3"></svg-icon>
            <span data-bind="text: _t('В архив')"></span>
          </div>
        </a>
        <!-- ko ifnot: isPublished -->
        <a class="tippy-list__item"
           href="javascript:void(0)"
           data-bind="click: function () { deleteClick(); }">
          <div class="d-flex align-items-center">
            <svg-icon params="name: 'poll-remove'"
                      class="f-color-service mr-3"></svg-icon>
            <span data-bind="text: _t('Удалить')"></span>
          </div>
        </a>
        <!-- /ko -->
        <!-- /ko -->
      </div>
    </template>


  </div>

  <!-- ko if: archived -->
  <div class="survey-list__survey-card-section-archived-content">
    <div class="survey-list__survey-card-section-archived-content-actions">
      <!-- ko if: !window.CURRENT_USER.watcher -->
      <button type="button"
              class="btn btn-white"
              data-bind="click: function () { restoreClick(); }, text: _t('Восстановить')"></button>
      <!-- ko ifnot: isPublished -->
      <button type="button"
              class="btn btn-white"
              data-bind="click: function () { deleteClick(); }, text: _t('Удалить')"></button>
      <!-- /ko -->
      <!-- /ko -->
    </div>
  </div>
  <!-- /ko -->
</div>

<!-- ko ifnot: archived -->
<a class="survey-list__element-card-label"
   data-bind="attr: { href: actualLink }">
  <div class="d-flex">

    <!-- ko ifnot: isPublished -->
    <div class="mr-2 flex-shrink-0">
      <svg-icon params="name: 'test-mode', width: 14, height: 14"
                data-bind='tooltip, tooltipText: _t(testModeText)'></svg-icon>
    </div>
    <!-- /ko -->

    <div>
      <div class="survey-list__element-card-name"
           data-bind="text: name"></div>
      <!-- ko if: folderName !== null -->
      <div class="survey-list__survey-card-folder-name"
           data-bind="text: folderName"></div>
      <!-- /ko -->
    </div>
  </div>
</a>
<!-- /ko -->

<!-- ko if: archived -->
<div class="survey-list__element-card-label">

  <div class="d-flex">

    <!-- ko ifnot: isPublished -->
    <div class="mr-2 flex-shrink-0">
      <svg-icon params="name: 'test-mode', width: 14, height: 14"
      data-bind='tooltip, tooltipText: _t("Опрос в \"Тестовом режиме\"")'></svg-icon>
    </div>
    <!-- /ko -->

    <div>
      <div class="survey-list__element-card-name"
           data-bind="text: name"></div>
      <!-- ko if: folderName !== null -->
      <div class="survey-list__survey-card-folder-name"
           data-bind="text: folderName"></div>
      <!-- /ko -->
    </div>
  </div>
</div>

</div>
<!-- /ko -->
<!-- /ko -->
