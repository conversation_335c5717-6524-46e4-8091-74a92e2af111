import { FoquzComponent } from "Models/foquz-component";
import { TEST_MODE_HINT } from "../../constants/test-mode";

let unique = 1;

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.unique = unique++;

    let model = params.model;
    this.model = model;

    this.actualLink = ko.computed(() => {
      return this.model.link;
    });

    this.id = model.id;
    this.name = model.name;
    this.createdAt = model.createdAt;
    this.statistics = model.statistics;
    this.favour = model.favour;
    this.folderName = model.folderName;
    this.backgroundUrl = model.backgroundUrl;
    this.status = model.status;
    this.isAuto = model.isAuto;
    this.ownerAvatarUrl = model.ownerAvatarUrl;
    this.ownerName = model.ownerName;
    this.archived = model.archived;
    this.folderId = model.folderId;
    this.checked = model.checked;
    this.isPublished = model.isPublished;
    this.isActive = model.isActive;

    this.duplicateClick = params.duplicateClick;
    this.moveClick = params.moveClick;
    this.archiveClick = params.archiveClick;
    this.deleteClick = params.deleteClick;
    this.restoreClick = params.restoreClick;
    this.addToFavorite = params.addToFavorite;

    this.checked.subscribe((v) => {
      $(element).toggleClass("checked", !!v);
    });

    this.testModeText = TEST_MODE_HINT;
  }

  dispose() {
    super.dispose();
    this.model.dispose();
  }

  init() {
    $(this.element).addClass("survey-list__survey-card");
    $(this.element).toggleClass(
      "survey-list__survey-card--archived",
      !!this.archived
    );
  }
}
