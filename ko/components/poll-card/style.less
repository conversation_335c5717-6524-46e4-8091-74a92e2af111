@import 'Style/breakpoints';

.poll-card {
  &-dropdown {
    .tippy-list__item {
      margin-top: 2px;
    }

    hr {
      margin-top: 9px;
      margin-bottom: 9px;
    }
  }

  &__header {
    position: absolute;
    top: 0;
    left: 0;
    padding-top: 9px;
    padding-left: 10px;
    padding-right: 10px;
    display: flex;
    align-items: flex-end;

    .survey-list__favour {
      margin-top: -5px;
    }
  }

  .only-mobile({
    &__checked {
      display: none;
    }
    &.checked {
      .poll-card__card {
        background: #2e2f31;
      }
    }
  });
}

.poll-menu-tippy {
  .tippy-close {
    display: none;
  }
  .only-mobile({
    position: fixed!important;
    transform: none!important;
    top: 0!important;
    left: 0!important;
    right: 0!important;
    bottom: 0!important;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;


    .tippy-box {
      width: 280px;
      min-height: 200px;

    }

    .tippy-list {
      position: relative;
    }

    .tippy-close {
      display: block;
      position: absolute;
      top: -4px;
      right: 10px;
    }

    .tippy-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      cursor: pointer;
      z-index: -1;
    }

    .tippy-svg-arrow {

    }
  });
}
