<div data-bind="dropdown" class="d-none d-md-block">
  <div class="position-relative">
    <fc-button class="ml-10p" params="icon: 'info-circle', shape: 'square', size: 'lg'" data-dropdown-target>
    </fc-button>
    <!-- ko if: noAnswersLeft -->
    <div class="f-exclamation-mark"></div>
    <!-- /ko -->
  </div>
  <template>
    <div class="poll-info-dropdown" data-bind="using: model">
      <!-- ko let: { translator: $component.translator }-->

      <div class="poll-info-dropdown__item">
        <div class="poll-info-dropdown__label" data-bind="text: translator.t('ID опроса')"></div>
        <div class="d-flex align-items-center">
          <span data-bind="text: id" class="bold"></span>
          <fc-button class="ml-10p" params="size: 'auto', mode: 'text', icon: 'poll-copy'" data-bind="click: function() {
            $component.copyId();
          }, fbPopper, title: 'Скопировать ID опроса'"></fc-button>
        </div>

        <!-- ko template: {
           foreach: templateIf($component.copied(), $data),
           afterAdd: fadeAfterAddFactory(400),
           beforeRemove: fadeBeforeRemoveFactory(400)
        } -->
        <div class="copied-message">ID опроса скопирован</div>
        <!-- /ko -->
      </div>

      <div class="poll-info-dropdown__item" >
        <!-- ko if: !isPublished() || hasPublishedAnswersLimit() -->
        <div class="poll-info-dropdown__label" data-bind="text: translator.t('Доступно анкет')"></div>
        <div class="bold" data-bind="text: limitText, css: {
          'f-color-danger': $component.noAnswersLeft
        }" ></div>
        <!-- /ko -->
      </div>

      <div class="poll-info-dropdown__item">
        <div class="poll-info-dropdown__label" data-bind="text: translator.t('Создан')"></div>
        <div data-bind="text: createdAt" class="bold"></div>
      </div>

      <div class="poll-info-dropdown__item">
        <div class="poll-info-dropdown__label" data-bind="text: translator.t('Вопросов / страниц')"></div>
        <div class="bold">
          <span data-bind="text: questions"></span> /
          <span data-bind="text: pages"></span>
        </div>
      </div>

      <!-- ko if: start || finish -->
      <div class="poll-info-dropdown__item">
        <div class="poll-info-dropdown__label" data-bind="text: translator.t('Продолжительность')"></div>
        <div>
          <!-- ko if: start -->
          <!-- ko let: { fromDateString: '<span class="bold">' + start + '</span>' } -->
          <div data-bind="html: translator.t('с {date}', {
            date: fromDateString
          })"></div>
          <!-- /ko -->
          <!-- /ko -->

          <!-- ko if: finish -->
          <!-- ko let: { toDateString: '<span class="bold">' + finish + '</span>' } -->
          <div data-bind="html: translator.t('до {date}', {
            date: toDateString
          })"></div>
          <!-- /ko -->
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->

      <!-- ko if: timeToPass -->
      <div class="poll-info-dropdown__item">
        <div class="poll-info-dropdown__label" data-bind="text: translator.t('Время прохождения')"></div>
        <div class="bold" data-bind="text: timeToPass"></div>
      </div>
      <!-- /ko -->

      <!-- ko ifnot: isPublished -->
      <div class="poll-info-dropdown__item poll-info-dropdown__item--full">
        <div class="f-color-danger mt-2"
          data-bind="text: translator.t('В тестовом режиме доступно только 10 анкет для прохождения')"></div>
      </div>
      <!-- /ko -->

      <!-- ko if: $component.noAnswersLeft -->
      <div class="poll-info-dropdown__item poll-info-dropdown__item--full">
        <div class="f-color-danger mt-2">
          <div data-bind="text: translator.t('Достигнут лимит количества ответов.')"></div>
          <div data-bind="text: translator.t('Опрос недоступен для прохождения.')"></div>
        </div>
      </div>
      <!-- /ko -->
      <!-- /ko -->
    </div>
  </template>
</div>

<div class="d-md-none">
  <button class="ml-10p f-btn f-btn-lg f-btn--square" data-bind="click: openInfoDialog">
    <svg-icon params="name: 'info-circle'"></svg-icon>
  </button>
</div>