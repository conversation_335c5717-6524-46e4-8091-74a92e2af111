import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Dialogs/poll-info-dialog';

ko.components.register('poll-info-button', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('poll-info-button');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
