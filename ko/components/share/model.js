export class ViewModel {
  constructor(params, element) {
    this.element = element;

    this.subscriptions = [];

    this.share = params.share;

    let { form } = this.share;
    if (form === 'square_rounded') form = 'round-rect';

    $(element).addClass([
      'share',
      'share-' + form,
      'share-' + this.share.size
    ]);

    if (this.share.substrate) $(element).addClass('share-overlay');

    this.options = [
      form,
      this.share.style,
      'default',
      'absolute',
      'horizontal',
      'size' + this.share.size,
      'eachCounter' + (this.share.forEachCounter ? 1 : 0),
      'counter' + (this.share.totalCounter ? 1 : 0),
      'counter-' + this.share.locationForTotalCounter,
      'nomobile'
    ].join(',');

    this.networks = Object.entries(this.share.networks || {})
      .filter(([key, value]) => value == 1)
      .map(([key, value]) => key)
      .join(',');
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }

  onRender() {
    if (!window.uSocialShare) {
      let script = document.createElement('script');
      script.setAttribute('data-script', 'usocial');
      script.setAttribute('charset', 'utf-8');
      $(this.element).prepend(script);
      script.async = true;
      script.src = 'https://usocial.pro/usocial/usocial.js?v=6.1.4';
    } else {
      window.uSocialShare.init();
    }
  }
}
