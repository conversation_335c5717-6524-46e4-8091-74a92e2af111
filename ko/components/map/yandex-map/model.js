import { FoquzComponent } from 'Models/foquz-component';
import { MOSCOW_COORDS } from 'Data/map';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.placeId = params.place;
    this.geoObject = null;

    this.map = new ymaps.Map(element, {
      center: [MOSCOW_COORDS.lat, MOSCOW_COORDS.lng],
      zoom: 11,
      controls: ['zoomControl']
    });

    this.setPlace(ko.toJS(this.placeId));

    if (ko.isObservable(this.placeId)) {
      this.placeId.subscribe((v) => {
        this.setPlace(v);
      });
    }
  }

  setPlace(placeId) {
    console.log('yandex map, set place', placeId);

    if (this.geoObject) {
      this.map.geoObjects.remove(this.geoObject);
      this.geoObject = null;
    }

    ymaps.findOrganization(placeId).then(
      (orgGeoObject) => {
        this.geoObject = orgGeoObject;

        let coords = orgGeoObject.geometry._coordinates;
        this.map.geoObjects.add(orgGeoObject);
        this.map.setCenter(coords);
        // orgGeoObject.balloon.open();
        // orgGeoObject.hint.open();
      },
      function (err) {
        // обработка ошибок
        console.error(err);
      }
    );
  }
}

