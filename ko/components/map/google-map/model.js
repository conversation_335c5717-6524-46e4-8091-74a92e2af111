import { FoquzComponent } from 'Models/foquz-component';
import { MOSCOW_COORDS } from 'Data/map';
export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.element = element;

    this.geocoder = new google.maps.Geocoder();

    let placeId = ko.toJS(params.place);

    this.map = new google.maps.Map(element, {
      zoom: 11,
      center: MOSCOW_COORDS
    });

    this.geocoder.geocode({ placeId }, (results, status) => {
      if (status === 'OK') {
        if (results[0]) {
          let object = results[0];
          this.map.setCenter(object.geometry.location);
          const marker = new google.maps.Marker({
            map: this.map,
            position: object.geometry.location
          });

          const infowindow = new google.maps.InfoWindow({
            content: object.formatted_address,
            maxWidth: 250,
          });

          marker.addListener("click", () => {
            infowindow.open(this.map, marker);
          });
        } else {
          console.error('Place not found');
        }
      } else {
        console.error('Geocoder failed due to: ' + status);
      }
    });
  }
}
