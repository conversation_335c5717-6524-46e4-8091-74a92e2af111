export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];
    this.tags = params.tags;
    this.withoutConditionOnly = params.withoutConditionOnly;
    this.operation = params.operation;
    this.allSelected = ko.observable(this.operation() == 2);
    this.allSelected.subscribe((v) => {
      this.operation(v ? '2' : '1');
    });
    this.operation.subscribe((v) => {
      this.allSelected(v == 2);
    });

    this.tags.subscribe((v) => {
      let search = element.querySelector('.select2-search__field');
      setTimeout(() => {
        search.style.width = '15px';
      }, 0);
    });

    this.formGroupClass = params.formGroupClass || '';
    this.companyId = params.companyId;

    this.label = params.label || _t('Тег контакта');

    this.placeholder = params.placeholder || _t('Все');

    setTimeout(() => {
    }, 2000);
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}
