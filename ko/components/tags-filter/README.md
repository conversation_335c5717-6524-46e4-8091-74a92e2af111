# Фильтр по тегам

```
<tags-filter
  params="tags: ko.observableArray([]),
          operation: ko.observable(''),
          formGroupClass: '',
          label: 'Теги',
          companyId: '1'">

</tags-filter>
```

## Параметры

* `tags` - массив выбранных тегов
* `operation` - параметр `tags_operation`
* `formGroupClass` - css-класс для каждой группы фильтра
* `companyId` - id текущей компании для получения списка тегов (для неавторизованных пользователей)
