<div class="form-group dense-form-group" data-bind="css: formGroupClass">
  <label class="form-label" data-bind="text: label"></label>
  <div class="d-flex align-items-end">


    <tags-select params="value: tags, withoutConditionOnly: withoutConditionOnly, placeholder: placeholder, dense: true, multiple: true, companyId: companyId">
    </tags-select>

    <!-- ko template: {
      foreach: templateIf(tags().length > 1, $data),
      afterAdd: fadeAfterAddFactory(400),
      beforeRemove: fadeBeforeRemoveFactory(400)
    } -->
    <foquz-checkbox params="checked: allSelected">
      <!-- ko text: _t('Все выбранные') -->
      <!-- /ko -->
    </foquz-checkbox>
    <!-- /ko -->
  </div>
</div>
