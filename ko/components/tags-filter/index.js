import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/input/select/tags-select'

ko.components.register('tags-filter', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('tags-filter');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
