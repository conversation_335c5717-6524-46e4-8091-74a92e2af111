import { InteractiveTable } from 'Models/interactive-table';
import { ApiUrl } from 'Utils/url/api-url';
import { PaymentPeriod } from './payment-period';
import { SortModel } from 'Models/sort';

export class PaymentsTable extends InteractiveTable {
  getSort() {
    return new SortModel('paymentPeriod', true);
  }

  load() {
    return new Promise((res, rej) => {
      if (!this.beforeLoad()) {
        res();
        return;
      }

      let urlParams = {};
      if (this.company) {
        urlParams.companyId = this.company;
      }

      this.xhr = $.ajax({
        url: ApiUrl('company/payment-periods', urlParams),
        data: this.getParams(),
        success: (response) => {
          this.afterLoad(response.map((c) => new PaymentPeriod(c)));
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.onError();
          rej();
        }
      });
    });
  }
}
