import { ApiUrl } from "Utils/url/api-url";
import { formatServerDateToClient } from "Utils/date/format";

export class PaymentPeriod {
  constructor(data) {
    this.id = data.id;
    this.answersCount = data.answersCount;
    this.answersLimit = data.answers;
    this.answersOverLimit = data.more_limit_answers;
    this.answerCost = data.answerCost;
    this.sum = data.sum;
    this.tariff = data.tariffTitle;
    this.bonuses = data.more_limit_answers;
    this.type = data.payment_type;

    this.isPaid = ko.observable(data.is_paid == 1);
    this.readyToPay = data.readyToPay == 1;

    let [from, to] = data.paymentPeriod.split(" ");
    from = formatServerDateToClient(from);
    to = formatServerDateToClient(to);
    this.period = `${from}–${to}`;
  }

  changeStatus() {
    let status = this.isPaid();
    let url = status ? "company/set-as-unpaid" : "company/set-as-paid";
    $.ajax({
      url: ApiUrl(url, {
        periodId: this.id,
      }),
      success: (response) => {
        this.isPaid(!status);
      },
      error: (response) => {
        console.error(response.responseJSON);
      },
    });
  }
}
