<!-- ko let: { $history: $component }-->
<interactive-table
  params="table: table, scroll: true, notFoundText: 'Оплат пока нет'"
>
  <table class="table f-table">
    <thead>
      <tr>
        <th width="180" class="f-table__outer-cell"></th>
        <th style="min-width: 200px" class="text-nowrap">
          <table-head-cell
            params="table: $data, name: 'paymentPeriod', withSearch: false"
            >Расчетный период
          </table-head-cell>
        </th>
        <th style="min-width: 140px">
          <table-head-cell
            params="table: $data, name: 'tariffName', withSearch: false"
            >Тариф</table-head-cell
          >
        </th>
        <th style="min-width: 140px">
          <table-head-cell
            params="table: $data, name: 'payment_type', withSearch: false"
            >Тип оплаты</table-head-cell
          >
        </th>
        <th style="min-width: 115px" class="text-nowrap" align="right">
          <table-head-cell params="table: $data, name: 'sum', withSearch: false"
            >Сумма
          </table-head-cell>
        </th>
        <th style="min-width: 110px" class="text-nowrap" align="right">
          <table-head-cell
            params="table: $data, name: 'answersCount', withSearch: false"
            >Кол-во ответов
          </table-head-cell>
        </th>
        <th style="min-width: 110px" class="text-nowrap" align="right">
          <table-head-cell
            params="table: $data, name: 'bonuses', withSearch: false"
            >Бонусные ответы
          </table-head-cell>
        </th>
        <th width="20">
          <table-head-cell
            params="table: $data, name: 'is_paid', withSearch: false"
          >
            <!-- ko if: $history.canEdit -->
            Оплачено
            <!-- /ko -->

            <!-- ko ifnot: $history.canEdit -->
            Статус оплаты
            <!-- /ko -->
          </table-head-cell>
        </th>
        <th width="100%"></th>

        <th style="min-width: 80px">Счет</th>
        <th class="f-table__outer-cell"></th>
      </tr>
    </thead>
    <tbody>
      <!-- ko foreach: { data: items, as: 'paymentPeriod' } -->
      <tr
        class="f-table__row"
        data-bind="css: {
        'f-table__row--error': paymentPeriod.readyToPay && !paymentPeriod.isPaid()
      }"
      >
        <td class="f-table__outer-cell"></td>
        <td class="text-nowrap" data-bind="text: paymentPeriod.period"></td>
        <td data-bind="text: paymentPeriod.tariff"></td>
        <td>
          <!-- ko if: paymentPeriod.type === 'prepayment' -->
          Предоплата
          <!-- /ko -->

          <!-- ko if: paymentPeriod.type === 'supplement' -->
          Доплата
          <!-- /ko -->
        </td>
        <td class="font-weight-500" align="right">
          <!-- ko if: paymentPeriod.sum -->
          <span data-bind="text: paymentPeriod.sum"></span>&nbsp;<span
            class="rub"
            >₽</span
          >
          <!-- /ko -->

          <!-- ko ifnot: paymentPeriod.sum -->
          –
          <!-- /ko -->
        </td>
        <td align="right">
          <!-- ko if: paymentPeriod.type === 'prepayment' -->
          <span
            data-bind="text: paymentPeriod.answersCount, css: {
            'f-color-danger': paymentPeriod.answersLimit && paymentPeriod.answersLimit - paymentPeriod.answersCount < 0
          }"
          ></span>
          /
          <span data-bind="text: paymentPeriod.answersLimit || '∞'"></span>
          <!-- /ko -->

          <!-- ko if: paymentPeriod.type === 'supplement' -->
          <span
            data-bind="text: paymentPeriod.answersOverLimit"
          ></span>
          <!-- /ko -->
        </td>
        <td align="right" data-bind="text: paymentPeriod.bonuses || '–'"></td>

        <td align="center">
          <!-- ko if: $history.canEdit && paymentPeriod.readyToPay -->
          <input-checkbox
            class="single"
            params="checked: paymentPeriod.isPaid, onChange: function() {
            paymentPeriod.changeStatus();
          }"
          ></input-checkbox>
          <!-- /ko -->

          <!-- ko ifnot: $history.canEdit -->
          <!-- ko if: paymentPeriod.isPaid -->
          <span class="f-color-success f-fs-1">Оплачено</span>
          <!-- /ko -->
          <!-- ko ifnot: paymentPeriod.isPaid -->
          <span class="f-color-danger f-fs-1">Не&nbsp;оплачено</span>
          <!-- /ko -->
          <!-- /ko -->
        </td>
        <td></td>
        <td>
          <!-- ko if: paymentPeriod.readyToPay -->
          <div class="d-flex">
            <a
              class="button-ghost"
              data-bind="attr: {
                href: '/foquz/payments/download-invoice-pdf?companyTariffId=' + paymentPeriod.id
              }"
              download
            >
              <svg-icon
                params="name: 'download'"
                class="f-color-service"
              ></svg-icon>
            </a>
            <a
              class="button-ghost ml-4"
              target="_blank"
              data-bind="attr: {
                 href: '/foquz/payments/print-invoice?companyTariffId=' + paymentPeriod.id
               }"
            >
              <svg-icon
                params="name: 'print'"
                class="f-color-service"
              ></svg-icon>
            </a>
          </div>
          <!-- /ko -->
        </td>
        <td class="f-table__outer-cell"></td>
      </tr>
      <!-- /ko -->
    </tbody>
  </table>
</interactive-table>
<!-- /ko -->
