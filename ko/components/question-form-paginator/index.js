import { ViewModel } from './model';
import html from './template.html';
import './style.less';


ko.components.register('question-form-paginator', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      element.classList.add('question-form-paginator')

      return new ViewModel(params, element);
    },
  },
  template: html,
});
