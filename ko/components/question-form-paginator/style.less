@import 'Style/colors';

@keyframes blink-paginator-item {
  0% {
    box-shadow: none;
  }
  10% {
    box-shadow: 0px 0px 15px #3f65f1;
  }
  to {
    box-shadow: none;
  }
}

.question-form-paginator {
  display: block;
  margin-right: -8px;
  counter-reset: qindex;

  &__container {
    margin-bottom: -12px;

    position: relative;
    //display: inline;
  }



  &__group {
    display: inline-block;
    vertical-align: top;
    background: white;
    padding: 15px 0px 8px;
    border-radius: 6px;
    position: relative;
    margin-right: 2px;

    &--empty {
      .question-page {
        width: 44px;
        height: 28px;
      }
    }

    &-meta {
      display: flex;
      justify-content: space-between;
      color: #A6B1BC;
      font-size: 11px;
      position: absolute;
      top: 3px;
      left: 5px;
      right: 5px;
    }

    &-items {
      display: flex;
      flex-wrap: wrap;
    }
  }

  &__item {
    position: relative;

    min-width: 28px;
    height: 28px;
    margin: 2px 8px;
    padding: 0 10px;
    margin-left: 0;

    display: inline-flex;
    align-items: center;
    justify-content: center;

    vertical-align: top;

    border-radius: 4px;
    background: #d9e0fc;
    color: @f-color-primary;
    outline: none;
    border: none;
    box-shadow: none;

    font-weight: bold;
    font-size: 10px;
    text-decoration: none;
    line-height: 1;

    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;

    &.active {
      background: @f-color-primary;
      color: #fff;
    }

    &.blinked {
      animation-name: blink-paginator-item;
      animation-iteration-count: 5;
      animation-duration: 1200ms;
    }

    &[data-type='default'] {
      border-radius: 20px;
      font-size: 15px;
    }

    &:not([data-type='default']):not([data-point]) {
      width: 28px;
      padding: 0;
    }

    &[data-type='inter'] {
      font-size: 19px;
      padding-top: 7px;

      &[data-point] {
        padding-top: 0;
      }
    }

    .f-exclamation-mark {
      position: absolute;
      top: -5px;
      right: -5px;
    }
  }

  .ui-sortable-placeholder {
    display: inline-flex;
    vertical-align: middle;
    flex-shrink: 0;

    height: 28px;

    margin: 2px 8px;

    visibility: visible !important;
    opacity: 1 !important;
    background-color: unset;

    border: 1px dotted @f-color-primary;

    border-radius: 50%;

    &.item-end,&.item-inter {
      border-radius: 4px;
    }
  }

  .ui-sortable-helper {
    border: 1px solid @f-color-primary;
    margin-left: -14px;
    margin-right: -14px;
  }

  &.dense {
    .question-form-paginator__item {
      &[data-type='default']:not([data-point]) {
        width: 28px;
        padding: 0;
        border-radius: 50%;
      }

      &[data-point] {
        padding-left: 6px;
      }

    }
    .sortable-ghost {
      width: 28px;
      border-radius: 4px;

      &[data-type='default'] {
        border-radius: 50%;
      }
    }
  }

  &__random {
    cursor: pointer;
    display: inline;
    vertical-align: top;
    margin-left: 4px;
    margin-top: 1px;

    .question-form-paginator__item + & {
      margin-left: -7px;
    }
  }

  &__container--groups {
    margin-top: -7px;


    .question-form-paginator__item {
      margin-bottom: 8px;
      margin-top: 15px;
    }
    .question-form-paginator__group {
      margin-bottom: 8px;
      .question-form-paginator__item {
        margin-bottom: 0;
        margin-top: 0;
        margin-left: 8px;
      }

      & + .item-end {
        margin-left: 6px;
      }
    }

    .fc-btn {
      margin-top: 12px;
    }
  }

  [data-index] {
    counter-increment: qindex;

    &:after {
      content: counter(qindex);
    }
    
  }
}
