function Share() {
  const share = Ya.share2("foquz-share", {
    content: {},
    theme: {
      services: "vkontakte,facebook,twitter,moimir,odnoklassniki,tumblr",
      lang: "ru",
      size: "m",
    },
  });

  return {
    update: (data) => {
      share.updateContent(data);
    },
  };
}

export class ViewModel {
  constructor({
    link = ko.observable(''),
    name = ko.observable(''),
  }) {
    const shareContent = ko.computed(() => {
      let content = {
        url: ko.isObservable(link) ? link() : link,
        image: location.protocol + "//" + location.host,
        title: ko.isObservable(name) ? name() : name,
      };
      return content;
    });

    let share = new Share();
    share.update(shareContent());

    shareContent.subscribe((v) => {
      share.update(v);
    });
  }

}
