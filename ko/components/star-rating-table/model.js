import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    let data = ko.unwrap(params.data);

    this.data = data.map(i => parseInt(i) || 0);
    let total = this.data.reduce((summ, i) => summ + i, 0);

    this.points = this.data.map((count, index) => {
      let percent = total > 0 ? (count * 100 / total) : 0;
      percent = Math.floor(percent * 10) / 10;
      return {
        rating: 5 - index,
        count: count,
        percent
      }
    })
  }
}
