<div class="breadcrumbs">

  <!-- ko foreach: folders -->
  <a class="breadcrumbs__item"
     data-bind="text: $data.name, attr: { href: $data.link }"></a>
  <!-- /ko -->

  <a class="breadcrumbs__item">

    <!-- ko if: !isEditing() -->
    <!-- ko text: name() -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: isEditing() -->
    <edit-form params="value: name,
      maxLength: 255,
      onSave: function(v) { $component.save(v) },
      onCancel: function() { $component.cancel() }">
    </edit-form>
    <!-- /ko -->

    <!-- ko if: !isEditing() -->
    <button class="f-btn f-btn--round f-btn-white poll-name-form__edit"
            type="button"
            title="Редактировать название"
            data-bind="click: function() { isEditing(true); }, tooltip,
              disable: disabled">
      <svg-icon params="name: 'pencil'"
                class="f-color-service"></svg-icon>
    </button>
    <!-- /ko -->
  </a>
</div>
