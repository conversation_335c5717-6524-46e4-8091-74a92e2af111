import { NameForm } from '../form';
import { ApiUrl } from 'Utils/url/api-url';
import { RequestProjectActivateEvent } from 'Utils/events/request-project';
export class ViewModel extends NameForm {
  constructor(params) {
    let project = params.project;

    super({
      name: project.name,
      folders: []
    });

    this.projectId = project.id;

    this.disabled = ko.computed(() => {
      return this.isAuto && this.isNew();
    });

    this.onEdit = params.onEdit;
  }

  afterSave(newValue) {
    $.ajax({
      method: 'POST',
      url: ApiUrl('requests-projects/update', { id: this.projectId }),
      data: {
        name: newValue
      },
      success: (response) => {
        RequestProjectActivateEvent.emit({
          id: this.projectId,
          link: response.link
        });
        if (typeof this.onEdit == 'function') this.onEdit(newValue);
      }
    });
  }
}
