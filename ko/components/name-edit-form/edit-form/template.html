<!-- ko template: { afterRender: $component.onRender.bind($component) } -->
<input
      class="edit-form__value"
      data-bind="
            textInput: value,
            element: input,
            attr: {
                  placeholder: placeholder,
                  maxlength: maxLength,
                  disabled: disabled,
            },
            onEnter: function() { save()}
      "
>


<button
      class="f-btn f-btn--round f-btn-danger edit-form__cancel"
      type="button"
      title="Отменить"
      data-bind="
            click: () => cancel(),
            attr: { disabled: disabled },
      "
>
  <svg-icon params="name: 'times'" class="svg-icon--sm"></svg-icon>
</button>

<button
      class="f-btn f-btn--round f-btn-success edit-form__save"
      type="button"
      title="Сохранить"
      data-bind="
            click: () => save(),
            attr: { disabled: disabled },
      "
>
  <svg-icon params="name: 'check'"  class="svg-icon--md"></svg-icon>
</button>


<span
      class="edit-form__hidden"
      data-bind="
            text: value() ? value() : placeholder,
            element: hidden
      "
></span>
<!-- /ko -->
