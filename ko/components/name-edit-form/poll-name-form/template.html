<div class="breadcrumbs">
  <!-- ko foreach: folders -->
  <a
    class="breadcrumbs__item"
    data-bind="text: $data.name, attr: { href: $data.link }"
  ></a>
  <!-- /ko -->

  <a class="breadcrumbs__item">
    <!-- ko if: isPublished() && !isDurationAvailable() -->
    <svg-icon
      params="name: 'alarm'"
      class="no-print poll-name-form__alarm f-color-danger svg-icon--lg"
      data-bind="tooltip, tooltipText: durationText"
    ></svg-icon>
    <!-- /ko -->

    <!-- ko ifnot: isPublished -->
    <svg-icon
      params="name: 'test-mode', width: 21, height: 21"
      class="no-print poll-name-form__test"
      data-bind="tooltip, tooltipText: testModeText"
    ></svg-icon>
    <!-- /ko -->

    <span
      class="no-print d-inline-block cursor-pointer poll-name-form__type"
      data-bind="tooltip, tooltipText: $component.isAuto ? translator.t('Автоматический опрос') : translator.t('Ручной опрос')"
    >
      <svg-icon
        params="name: 'type-' + ($component.isAuto ? 'auto' : 'manual')"
        class="f-color-service svg-icon--lg"
      ></svg-icon
    ></span>

    <!-- ko if: !isEditing() -->
    <!-- ko text: name() -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: isEditing() -->
    <edit-form
      params="value: name,
      maxLength: 255,
      onSave: function(v) { $component.save(v) },
      onCancel: function() { $component.cancel() }"
    >
    </edit-form>
    <!-- /ko -->

    <!-- ko if: !isEditing() && !window.CURRENT_USER.watcher -->
    <button
      class="f-btn f-btn--round f-btn-white poll-name-form__edit"
      type="button"
      data-bind="click: function() { isEditing(true); }, tooltip,
              disable: disabled, tooltipText: translator.t('Редактировать название')"
    >
      <svg-icon params="name: 'pencil'" class="f-color-service"></svg-icon>
    </button>
    <!-- /ko -->
  </a>
</div>
