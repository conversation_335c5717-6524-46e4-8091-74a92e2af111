import { FiltersController } from './filters-controller';
import { bindAll } from 'lodash';

export class ControllerOverview {
  constructor({
    columns = [],
    filters = [],
    stats = [],
    directories = {},
    emptySearchText = 'Ничего не найдено',
    emptyTemplate = '',
    emptyComponent = '',
    currentItems,
    totalItems,
    pagination = false,
  } = {}) {
    this.directories = directories;
    this.filtersController = new FiltersController({ columns, filters })
    this.items = ko.observableArray(null);
    this.columns = columns;
    this.filters = filters;
    this.stats = stats;
    this.search = location.search;
    this.page = ko.observable(1);
    this.currentItems = currentItems;
    this.totalItems = totalItems;
    this.pagination = pagination;

    const query = this.filtersController.updateFilters();
    this.query = ko.observable(query);
    this.pendingInit = ko.observable(false);
    this.pendingPage = ko.observable(false);
    this.pending = ko.observable(false);
    this.emptySearchText = emptySearchText;

    this.init();
    
    Object.keys(this.directories).forEach((key) => {
      this.directories[key].load();
    });

    if (emptyTemplate || emptyComponent) {
      this.hasEmptyPlaceholder = true;
      this.emptyTemplate = emptyTemplate;
      this.emptyComponent = emptyComponent;
    }
    
    this.showEmptyPlaceholder = ko.computed(() => {
      const show = this.hasEmptyPlaceholder
        && !this.currentItems()
        && !this.totalItems()
        && (!this.items() || !this.items().length);
      return show;
    });

    bindAll(this, [
      'updateItems',
      'resetFilters',
      'getItems',
    ]);
  }

  async init() {
    this.pendingInit(true);
    const query = this.query();
    const [items] = await Promise.all([
      this.getItems(query),
      this.getStats(query),
    ])
    this.items(items);
    this.pendingInit(false);
  }

  async nextPage() {
    this.pendingPage(true);
    const page = this.page() + 1;
    this.page(page);
    const items = await this.getItems(`${this.query()}&page=${page}`);
    this.items([...this.items(), ...items]);
    this.pendingPage(false);
  }

  async resetFilters() {
    this.page(1);
    const query = this.filtersController.reset();
    this.query(query);
    this.pending(true);
    const items = await this.getItems(query);
    this.items(items);
    this.pending(false);
  }

  async updateItems() {
    this.page(1);
    const query = this.filtersController.updateQuery();
    this.query(query);
    this.pending(true);
    const items = await this.getItems(query);
    this.items(items);
    this.pending(false);
  }

  async getItems(query = '') {
    return new Promise((resolve) => {
      setTimeout(() => resolve([]), 1000);
    });
  }
  async getStats(query = '') {}
}