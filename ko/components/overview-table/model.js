import "./components/scroll-container"
import './components/overview-table-head-cell';
import './components/overview-table-body-cell';
import './components/overview-table-filter';
import { Observer } from "./observer";

export class ViewModel {
  constructor({
    controller,
  }) {
    this.controller = controller;
    this.columns = controller.columns;
    this.filters = controller.filters;
    this.items = controller.items;
    this.stats = controller.stats;
    this.directories = controller.directories;
    this.pagination = controller.pagination;
    this.pending = controller.pending;
    this.pendingPage = controller.pendingPage;
    this.pendingInit = controller.pendingInit;
    this.emptySearchText = controller.emptySearchText;
    this.hasEmptyPlaceholder = controller.hasEmptyPlaceholder;
    this.currentItems = controller.currentItems;
    this.totalItems = controller.totalItems;
    this.emptyTemplate = controller.emptyTemplate;
    this.emptyComponent = controller.emptyComponent;
    this.showEmptyPlaceholder = controller.showEmptyPlaceholder;
    this.observers = {};


    if (this.pagination) {
      this.initObserver();
      this.items.subscribe(() => {
        this.activator();
      });
      this.pending.subscribe((pending) => {
        if (pending) {
          this.deactivateObserver();
        } else {
          this.activator();
        }
      });
    }
  }

  onDescendantsComplete() {
    this.activator();
  }

  activator() {
    const current = this.currentItems();
    const items = this.items();
    if (Array.isArray(items) && items.length && (current > items.length)) {
      this.activateObserver();
    } else {
      this.deactivateObserver();
    }
  }

  update() {
    this.controller.updateItems();
  }

  reset() {
    this.controller.resetFilters();
  }

  nextPage() {
    this.controller.nextPage();
  }
  
  initObserver() {
    const that = this;
    this.observers.reviewsList = new Observer(
      function (entries, observer) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            that.nextPage();
          }
        });
      },
      {
        rootMargin: "50px",
      }
    );
  }

  deactivateObserver() {
    this.observers.reviewsList.deactivate();
  }

  activateObserver() {
    this.deactivateObserver()
    const target = document.getElementById("overview-table-bottom")
    this.observers.reviewsList.setTarget(target);
    this.observers.reviewsList.activate();
  }
}