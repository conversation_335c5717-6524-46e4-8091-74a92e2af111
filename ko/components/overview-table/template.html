<!-- ko let: {
  columns: columns,
  filters: filters,
  items: items,
  stats: stats,
  directories: directories,
  controller: controller,
  pending: pending,
  pendingInit: pendingInit,
  pendingPage: pendingPage,
  showEmptyPlaceholder: showEmptyPlaceholder,
  update: update,
  reset: update,
} -->
  <!-- ko if: pendingInit -->
    <div class="my-3">
      <spinner></spinner>
    </div>
  <!-- /ko -->
  <!-- ko if: !pendingInit() && showEmptyPlaceholder() -->
    <!-- ko if: emptyTemplate -->
      <div
        data-bind="template: { name: emptyTemplate }"
      ></div>
    <!-- /ko -->
    <!-- ko if: !emptyTemplate && emptyComponent -->
      <div
        data-bind="component: { name: emptyComponent }"
      ></div>
    <!-- /ko -->
  <!-- /ko -->
  <!-- ko if: !pendingInit() && !showEmptyPlaceholder() -->
  <div
    class="reviews-page"
    data-bind="descendantsComplete: () => onDescendantsComplete()"
  >
    <div class="f-card__section reviews-page-filters f-card__divider">
      <div class="reviews-page-filters__container">
        <!-- ko foreach: filters -->
          <!-- ko component: {
            name: 'overview-table-filter',
            params: {
              filter: $data,
              directories: directories,
            }
          } -->
          <!-- /ko -->
        <!-- /ko -->
        <div class="reviews-filter reviews-page-filters__actions">
          <button
            type="button"
            class="f-btn f-btn-link mr-3 d-none d-md-flex"
            data-bind="click: reset"
          >
            Сбросить
          </button>
    
          <button
            type="button"
            class="f-btn d-md-none f-btn-lg"
            data-bind="click: reset"
          >
            <span class="f-btn-prepend">
              <svg-icon
                params="name: 'bin'"
                class="svg-icon svg-icon--bin"
              >
                <!-- ko using: icon -->
                  <svg
                    data-bind="attr: {
                      viewBox: viewBox,
                    }"
                    viewBox="0 0 18 18"
                  >
                    <use
                      data-bind="attr: {
                        href: '#' + id,
                        'xlink:href': '#' + id
                      }"
                      href="#foquz-icon-bin"
                      xlink:href="#foquz-icon-bin"
                    ></use>
                  </svg>
                <!-- /ko -->
              </svg-icon>
            </span>
            Сбросить
          </button>
    
          <button
            type="button"
            class="f-btn f-btn-success f-btn-lg"
            data-bind="click: update"
          >
            <span class="f-btn-prepend">
              <svg-icon
                params="name: 'check'"
                class="svg-icon svg-icon--check"
              >
                <!-- ko using: icon -->
                <svg
                  data-bind="attr: {
                    viewBox: viewBox,
                  }"
                  viewBox="0 0 22 16"
                >
                  <use
                    data-bind="attr: {
                      href: '#' + id,
                      'xlink:href': '#' + id
                    }"
                    href="#foquz-icon-check"
                    xlink:href="#foquz-icon-check"
                  ></use>
                </svg>
                <!-- /ko -->
              </svg-icon>
            </span>
            Применить
          </button>
        </div>
      </div>
    </div>
    <div class="f-card__section f-card__divider reviews-page-info">
      <div class="reviews-page-meta">
        <!-- ko foreach: stats -->
          <!-- ko let: {
            current: current,
            total: total,
            name: name,
            color: color,
          } -->
            <foquz-stats-item class="adaptive">
              <div
                class="value"
                data-bind="style: { color: color }"
              >
                <span
                  data-bind="text: current() || 0"
                ></span>
                <!-- ko if: total() !== null -->
                  <small>
                    /
                    <!-- ko text: total() || 0 -->
                    <!-- /ko -->
                  </small>
                <!-- /ko -->
              </div>
              <div
                class="label"
                data-bind="text: name"
              ></div>
            </foquz-stats-item>
          <!-- /ko -->
        <!-- /ko -->
      </div>
    </div>
    <div class="position-relative">
      <div class="reviews-list">
        <div
          class="reviews-list__table"
          data-bind="component: {name: 'scroll-container'}"
        >
          <div data-bind="component: { name: 'scroll-container-content'}">
            <table class="reviews-table reviews-table--header table">
              <thead>
                <tr class="reviews-list__row">
                  <!-- ko foreach: columns -->
                    <!-- ko component: {
                        name: 'overview-table-head-cell',
                        params: {
                          column: $data,
                          update: () => update(),
                        }
                    } -->
                    <!-- /ko -->
                  <!-- /ko -->
                </tr>
              </thead>
              <tbody 
                data-bind="visible: !pending()"
              >
                <!-- ko foreach: items -->
                  <!-- ko let: {
                    item: $data,
                  } -->
                    <tr
                      class="reviews-list__row cursor-pointer"
                    >
                      <!-- ko foreach: columns -->
                        <!-- ko component: {
                          name: 'overview-table-body-cell',
                          params: {
                            column: $data,
                            controller: controller,
                            directories: directories,
                            item: item,
                          }
                        } -->
                        <!-- /ko -->
                      <!-- /ko -->
                    </tr>
                  <!-- /ko -->
                <!-- /ko -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div
        class="my-3"
        data-bind="visible: (pending() || pendingPage())"
      >
        <spinner></spinner>
      </div>
      <!-- ko if: !pending() && !currentItems() -->
        <div
          class="f-card__section f-card__grow justify-content-center align-items-center d-flex f-color-service"
          data-bind="text: emptySearchText"
        ></div>
      <!-- /ko -->
    </div>
    <div id="overview-table-bottom"></div>
  </div>
  <!-- /ko -->
<!-- /ko -->