export class Column {
  constructor({
    name = '',
    apiName = '',
    placeholder = '',
    isSortable = false,
    isSearchable = false,
    thComponent = '',
    tdComponent = '',
    thTemplate = '',
    tdTemplate = '',
    fieldSearch = 'search',
    minWidth = 200,
    tooltipText = ''
  } = {}) {
    this.name = name;
    this._fieldSearch = fieldSearch;
    this.minWidth = minWidth;
    this.apiName = apiName;
    this.placeholder = placeholder;
    this.isSortable = isSortable;
    this.isSearchable = isSearchable;
    this.thComponent = thComponent;
    this.tdComponent = tdComponent;
    this.thTemplate = thTemplate;
    this.tdTemplate = tdTemplate;
    this.tooltipText = tooltipText;

    if (isSearchable) {
      this.searchValue = ko.observable('');
    }

    if (isSortable) {
      this.sortValue = ko.observable('');
    }
  }

  get serchField() {
    return `${this._fieldSearch}[${this.apiName}]`;
  }
}
