export class Filter {
  constructor({
    name = '',
    apiName = '',
    component = '',
    template = '',
    isArray = false,
    field = 'filter',
    valueFormater = (val) => val,
  }) {
    this.name = name;
    this._field = field;
    this.apiName = apiName;
    this.component = component;
    this.template = template;
    this._value = isArray ? ko.observableArray([]) : ko.observable('');

    this.value = ko.pureComputed({
      read() {
        return this._value();
      },
      write(value) {
        isArray
          ? this._value(Array.isArray(value) ? value.map(val => valueFormater(val)) : null)
          : this._value(valueFormater(value));
      },
      owner: this
    });
  }

  get serchField() {
    return `${this._field}[${this.apiName}]`
  }
}
