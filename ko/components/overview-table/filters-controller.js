import { bindAll } from 'lodash';
import queryString from 'query-string';

export class FiltersController {
  constructor({
    columns = [],
    filters = [],
  } = {}) {
    this.filters = {};
    this.sort = {};
    columns.forEach(({ apiName, serchField, sortValue, searchValue }) => {
      if (!apiName) return;
      if (ko.isObservable(sortValue)) {
        this.sort[apiName] = sortValue;
        this.sort[apiName].subscribe((value) => {
          if (!value) return;
          this.resetSort(apiName);
        });

      }
      if (ko.isObservable(searchValue)) {
        this.filters[serchField] = searchValue;
      }
    });
    filters.forEach(({ apiName, serchField, value }) => {
      if (!apiName) return;
      if (ko.isObservable(value)) {
        this.filters[serchField] = value;
      }
    });

    bindAll(this, [
      'updateFilters',
      'updateQuery',
      'resetSort',
      'resetFilters',
    ]);
  }

  resetSort(actualKey) {
    Object.keys(this.sort).forEach(key => {
      if (key !== actualKey) {
        this.sort[key]('');
      }
    });
  }

  resetFilters() {
    Object.keys(this.filters).forEach(key => {
      this.filters[key]('');
    });
  }

  reset() {
    this.resetFilters();
    this.resetSort();
    const url = new URL(window.location.origin + window.location.pathname);
    window.history.pushState(null, '', url.toString());
    return '';
  }

  updateFilters() {
    const search = location.search;
    const parsed = queryString.parse(search, {arrayFormat: 'comma'});
    const params = {};
    Object.keys(this.filters).forEach(key => {
      const value = parsed[key];
      if (value || value === 0) {
        this.filters[key](value);
        params[key] = value;
      } else {
        this.filters[key]('');
      }
    });
    if (parsed.sort) {
      const value = parsed.sort;
      const sortKey = value.replace('-', '');
      if (sortKey in this.sort) {
        this.sort[sortKey](value);
        params.sort = value;
      }
    }
    const query = queryString.stringify(params, {arrayFormat: 'bracket'});
    return query;
  }

  updateQuery() {
    const values = {};
    const url = new URL(window.location.origin + window.location.pathname);
    Object.keys(this.filters).forEach(key => {
      const value = this.filters[key]();
      if (value || value === 0) {
        values[key] = value;
        url.searchParams.set(key, value);
      }
    })
    const sortKey = Object.keys(this.sort).find((key) => !!this.sort[key]());
    if (sortKey) {
      const value = this.sort[sortKey]();
      values.sort = value;
      url.searchParams.set('sort', value);
    }
    window.history.pushState(null, '', url.toString());
    const query = queryString.stringify(values, {arrayFormat: 'bracket'});
    return query;
  }
}