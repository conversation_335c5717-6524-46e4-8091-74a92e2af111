ko.components.register("scroll-container-content", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const element = componentInfo.element;

      return {
        _childData: ko.contextFor(element).$data,
        onInit: () => {
          $(element).trigger("scroll.container.reinit");
        },
      };
    },
  },
  template: `
    <div class="scroll-container__wrapper">
      <div data-bind="fScrollbar: { onlyX: true, gradient: true, }, childrenComplete: onInit">
      <!-- ko template: {nodes: $componentTemplateNodes} --><!-- /ko -->
      </div>
    </div>
    `,
});

ko.components.register("scroll-container", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const speed = 400;
      const frequency = 30;
      let timerId = null;
      let scrollingState = "stop";

      const element = componentInfo.element;
      let $container, $leftScrollButton, $rightScrollButton;
      let blocks = [];

      function updateScrollButtonsVisibility() {
        const isVisible = blocks.some((block) => {
          if (!block.wrapper) return false;
          return block.wrapper.offsetWidth < block.wrapper.scrollWidth;
        });

        $leftScrollButton.toggle(isVisible);
        $rightScrollButton.toggle(isVisible);
      }

      const windowResizeHandler = () => {
        updateScrollButtonsVisibility();
      };

      function scrollAll(scroll, except) {
        blocks.forEach((block) => {
          if (block.wrapper === except) return;
          block.wrapper.sync = true;
          block.wrapper.scrollLeft = scroll;
        });
      }

      function findBlocks() {
        if (!$container) return;

        const $blocks = $container.find(".scroll-container__wrapper");
        $blocks.each(function (index, element) {
          const block = initBlock(element);
          blocks.push(block);
        });
      }

      function initBlock(block) {
        const wrapper = block.querySelector(".ps");
        const onScroll = function () {
          if (!wrapper.sync) {
            const scroll = wrapper.scrollLeft;
            scrollAll(scroll, wrapper);
          }
          wrapper.sync = false;
        };

        $(wrapper).on("scroll", onScroll);

        return {
          wrapper: wrapper,
          onScroll: onScroll,
        };
      }

      function reInit() {
        blocks.forEach((block) => {
          $(block.wrapper).off("scroll", block.onScroll);
        });
        blocks = [];
        findBlocks();
      }

      $(element).on("scroll.container.reinit", function () {
        reInit();
      });

      const ViewModel = function () {
        this.stopScroll = function () {
          if (scrollingState !== "stop") {
            clearInterval(timerId);
            scrollingState = "stop";
          }
        };

        this.onInit = function () {
          $container = $(element).find(".scroll-container");

          $leftScrollButton = $container.find(
            ".scroll-container__scroll-button--left"
          );
          $rightScrollButton = $container.find(
            ".scroll-container__scroll-button--right"
          );

          findBlocks();

          $(window).on("resize", windowResizeHandler);
          updateScrollButtonsVisibility();

          $(window).on("mouseup touchend", () => {
            this.stopScroll();
          });
        };

        this.startScrollLeft = function () {
          if (scrollingState !== "scrollLeft") {
            this.stopScroll();
            timerId = setInterval(() => {
              const scroll = blocks[0].wrapper.scrollLeft - speed / frequency;
              scrollAll(scroll);
            }, 1000 / frequency);

            scrollingState = "scrollLeft";
          }
        };

        this.startScrollRight = function () {
          if (scrollingState !== "scrollRight") {
            this.stopScroll();

            timerId = setInterval(() => {
              const scroll = blocks[0].wrapper.scrollLeft + speed / frequency;
              scrollAll(scroll);
            }, 1000 / frequency);

            scrollingState = "scrollRight";
          }
        };

        this.resetScroll = () => {
          scrollAll(0);
        };

        this._childData = ko.contextFor(element).$data;
      };

      ko.utils.domNodeDisposal.addDisposeCallback(element, function () {
        $(window).off("resize", windowResizeHandler);
      });

      const vm = new ViewModel();

      let ref = params && params.ref;
      if (ko.isObservable(ref)) {
        ref(vm);
      }

      return vm;
    },
  },
  template: `
    <div class="scroll-container" data-bind="descendantsComplete: $component.onInit.bind($component)">
      <div class="scroll-container__scroll-gutter scroll-container__scroll-gutter--left">
        <button class="scroll-container__scroll-button scroll-container__scroll-button--left"
          data-bind="event: {
            mousedown: startScrollLeft,
            touchstart: startScrollLeft
          }">
        </button>
      </div>
  
      <!-- ko template: {nodes: $componentTemplateNodes} --><!-- /ko -->
  
  
      <div class="scroll-container__scroll-gutter scroll-container__scroll-gutter--right">
        <button class="scroll-container__scroll-button scroll-container__scroll-button--right"
        data-bind="event: {
          mousedown: startScrollRight,
          touchstart: startScrollRight
        }">
        </button>
      </div>
    </div>
    `,
});
