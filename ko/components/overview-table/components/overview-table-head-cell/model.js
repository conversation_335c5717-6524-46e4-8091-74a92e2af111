export class ViewModel {
  constructor({ column, update }) {
    this.isSortable = column.isSortable;
    this.sortValue = column.sortValue;
    this.placeholder = column.placeholder;
    this.isSearchable = column.isSearchable;
    this.searchValue = column.searchValue;
    this.name = column.name;
    this.apiName = column.apiName;
    this.minWidth = column.minWidth;
    this.tooltipText = column.tooltipText;
    this.update = update;
  }
  
  onKeyup(_, e) {
    if (e.key === "Enter") {
      this.update();
    }
    return true;
  }

  onSort() {
    if (!this.isSortable) return;

    let value = this.sortValue();

    switch (value) {
      case '':
        value = this.apiName;
        break;
      case this.apiName:
        value = '-' + this.apiName;
        break;
      default:
        value = '';
        break;
    }

    this.sortValue(value);
    this.update();
  }
}