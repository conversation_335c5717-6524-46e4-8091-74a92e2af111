<th
  class="reviews-list__cell reviews-list__head-cell test"
  data-bind="style: { minWidth: minWidth + 'px' }"
>
  <div class="reviews-list__cell-content">
    <div
      class="reviews-list__head-cell-name"
    >
      <span
        data-bind="
          click: isSortable ? onSort : null,
          css: {
            'cursor-pointer': isSortable,
            'foq-table__head-cell-title--active': sortValue,
          }
        "
      >
        <!-- ko text: name -->
        <!-- /ko -->

        <!-- ko if: isSortable -->
          <i
            class="reviews-list__order icon icon-sorting"
            data-bind="css: {
              'reviews-list__order--asc': sortValue() === apiName,
              'reviews-list__order--desc': sortValue() === ('-' + apiName),
              'icon-sorting--active': !!sortValue(),
            }"
          ></i>
        <!-- /ko -->
      </span>
      <!-- ko if: tooltipText -->
        <button
          class="btn-question"
          data-bind="tooltip, tooltipText: tooltipText"
        ></button>
      <!-- /ko -->

      <!-- ko if: searchValue -->
        <!-- ko if: searchValue().length -->
          <i
            class="cursor-pointer reviews-list__filter icon icon-filtering icon-filtering--active"
            data-bind="
              click: update,
            "
          ></i>
        <!-- /ko -->
      <!-- /ko -->
    </div>

    <!-- ko if: isSearchable -->
      <input
        class="reviews-list__search"
        data-bind="
          textInput: searchValue,
          attr: {placeholder: placeholder},
          event: {
            keyup: onKeyup
          }
        "
      />
    <!-- /ko -->
  </div>
</th>
