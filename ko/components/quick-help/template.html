<!-- ko let: { $modal: $component } -->
<div class="b-quick-help__body">
      <div class="b-quick-help__arrow"></div>
      <a href="#"
         class="b-quick-help__close"
         data-bind="click: (_,event)=>{
        event.preventDefault();
        $modal.onHide();
    }"></a>
      <div class="b-quick-help__text">
            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'polls', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg width="250"
                 class="b-quick-help__icon"
                 height="111"
                 viewBox="0 0 250 111"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M250 110.294C248.805 98.3 245.787 86.5219 241.025 75.3489C234.714 60.5401 225.463 47.0844 213.802 35.7502C202.14 24.416 188.296 15.4252 173.059 9.29117C157.823 3.15714 141.492 0 125 0C108.508 0 92.1775 3.15715 76.9408 9.29118C61.7042 15.4252 47.8598 24.416 36.1982 35.7502C24.5366 47.0844 15.2861 60.5401 8.97486 75.349C4.21319 86.5219 1.19502 98.3 0 110.294H250Z"
                        fill="#EFF2FA" />
                  <path d="M84.5591 35.2941C84.5591 28.7966 89.8263 23.5294 96.3238 23.5294H150.736C157.233 23.5294 162.5 28.7966 162.5 35.2941V69.1176C162.5 75.6151 157.233 80.8823 150.736 80.8823H96.3238C89.8263 80.8823 84.5591 75.6151 84.5591 69.1176V35.2941Z"
                        fill="#255EB2" />
                  <path d="M128.309 91.9118H133.456L129.044 76.1029L117.28 80.8823L128.309 91.9118Z"
                        fill="#255EB2" />
                  <path d="M89.7061 35.2941C89.7061 28.7966 94.9733 23.5294 101.471 23.5294H155.883C162.38 23.5294 167.647 28.7966 167.647 35.2941V69.1176C167.647 75.6151 162.38 80.8823 155.883 80.8823H101.471C94.9733 80.8823 89.7061 75.6151 89.7061 69.1176V35.2941Z"
                        fill="#2D99FF" />
                  <path d="M133.456 91.9118V80.8823H122.427L133.456 91.9118Z"
                        fill="#2D99FF" />
                  <path d="M166.177 45.5882V35.1333C166.177 29.5368 161.636 25 156.035 25H131.133"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M122.794 25H113.971"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <rect x="117.647"
                        y="39.7059"
                        width="31.6177"
                        height="2.94118"
                        rx="1.47059"
                        fill="#2876D1" />
                  <rect x="117.647"
                        y="51.4706"
                        width="31.6177"
                        height="2.94118"
                        rx="1.47059"
                        fill="#2876D1" />
                  <rect x="117.647"
                        y="63.2353"
                        width="31.6177"
                        height="2.94118"
                        rx="1.47059"
                        fill="#2876D1" />
                  <path d="M105.147 39.723L107.712 42.288L112.5 37.5"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M105.147 51.4877L107.712 54.0526L112.5 49.2647"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M105.147 63.2524L107.712 65.8174L112.5 61.0294"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M68.1745 72.0588H50.2083C48.0629 72.0588 46.3237 73.7479 46.3237 75.8315V86.9516C46.3237 89.0351 48.0629 90.7242 50.2083 90.7242H55.4741L61.7649 96.2344V90.7242H68.1745C70.3198 90.7242 72.059 89.0351 72.059 86.9516V75.8315C72.059 73.7479 70.3198 72.0588 68.1745 72.0588Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <rect x="52.9409"
                        y="77.2059"
                        width="12.5"
                        height="3"
                        rx="1.5"
                        fill="#C1DFFB" />
                  <rect x="52.9409"
                        y="82.353"
                        width="12.5"
                        height="3"
                        rx="1.5"
                        fill="#C1DFFB" />
                  <path d="M45.9905 27.9412H17.2446C13.812 27.9412 11.0293 30.6704 11.0293 34.037V52.0046C11.0293 55.3712 13.812 58.1004 17.2446 58.1004H25.6698L35.7352 67.0037V58.1004H45.9905C49.4231 58.1004 52.2058 55.3712 52.2058 52.0046V34.037C52.2058 30.6704 49.4231 27.9412 45.9905 27.9412Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <rect x="20.5879"
                        y="35.3759"
                        width="22.0588"
                        height="3"
                        rx="1.5"
                        fill="#6EB9FF" />
                  <rect x="20.5879"
                        y="41.1765"
                        width="22.0588"
                        height="3"
                        rx="1.5"
                        fill="#6EB9FF" />
                  <rect x="20.5879"
                        y="47.0588"
                        width="15.2715"
                        height="3"
                        rx="1.5"
                        fill="#6EB9FF" />
                  <path d="M225.797 63.9706H240.242C241.976 63.9706 243.382 62.5778 243.382 60.8597V47.172C243.382 45.4539 241.976 44.0611 240.242 44.0611H225.797V41.5724C225.797 40.5416 224.953 39.7059 223.912 39.7059H215.12C214.079 39.7059 213.235 40.5416 213.235 41.5724V59.9265"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M220.711 59.0121H200.981V55.543C200.981 54.106 199.828 52.9412 198.407 52.9412H186.397C184.976 52.9412 183.824 54.106 183.824 55.543V82.4284C183.824 84.8233 185.744 86.7647 188.113 86.7647H220.711C223.08 86.7647 225 84.8233 225 82.4284V63.3484C225 60.9535 223.08 59.0121 220.711 59.0121Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <circle cx="204.412"
                          cy="72.7941"
                          r="8.08824"
                          stroke="#C1DFFB"
                          stroke-width="3" />
                  <path d="M204.412 69.1176V73.5294H208.089"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
            </svg>

            <div data-bind="html: texts.polls"></div>
            <!-- /ko -->
            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'answers', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg class="b-quick-help__icon"
                 width="255"
                 height="115"
                 viewBox="0 0 255 115"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M255 114.294C253.805 102.3 250.787 90.5219 246.025 79.3489C239.714 64.5401 230.463 51.0844 218.802 39.7502C207.14 28.416 193.296 19.4252 178.059 13.2912C162.823 7.15714 146.492 4 130 4C113.508 4 97.1775 7.15715 81.9408 13.2912C66.7042 19.4252 52.8598 28.416 41.1982 39.7502C29.5366 51.0844 20.2861 64.5401 13.9749 79.349C9.21321 90.5219 6.19504 102.3 5.00002 114.294H255Z"
                        fill="#EFF2FA" />
                  <path d="M149.357 40.7534C155.379 39.3005 160.527 41.1141 161.857 46.6263C162.49 49.2512 161.552 52.2551 159.838 54.3354C160.94 55.4584 162.105 56.8438 162.485 58.4187C163.118 61.0436 161.856 63.8482 160.078 65.666C160.918 66.8521 161.496 68.1014 161.876 69.6764C162.7 73.0887 160.976 76.2821 158.151 78.3526C158.539 78.8144 158.666 79.3394 158.856 80.1268C159.743 83.8017 156.828 88.9491 151.592 90.2124C151.592 90.2124 120.337 99.9386 109.741 94.7538C96.2213 75.7972 99.9196 56.9552 99.9196 56.9552C105.782 52.958 114.939 41.5292 116.537 32.9302C118.327 23.2972 114.836 17.0695 118.914 16.0857L124.698 14.6901C125.648 18.6274 131.983 39.9451 131.814 44.9857L149.357 40.7534Z"
                        fill="#A4CAF6" />
                  <path d="M91.7061 103.788L107.454 99.9883C110.341 99.2917 112.141 96.3888 111.453 93.5372L102.697 57.2451C102.009 54.3936 99.0841 52.6305 96.197 53.327L80.4488 57.1263C77.5616 57.8229 75.7623 60.7259 76.4502 63.5774L85.2059 99.8695C85.8938 102.721 88.8189 104.484 91.7061 103.788Z"
                        fill="#255EB2" />
                  <path d="M99.483 101.911L115.231 98.1121C118.118 97.4155 119.918 94.5126 119.23 91.661L110.474 55.3689C109.786 52.5174 106.861 50.7543 103.974 51.4508L88.2257 55.2501C85.3385 55.9467 83.5392 58.8496 84.2271 61.7012L92.9828 97.9933C93.6707 100.845 96.5958 102.608 99.483 101.911Z"
                        fill="#2D99FF" />
                  <path fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M169.604 44.6286C168.274 39.1163 163.114 37.3058 157.075 38.7628L137.039 43.5967C137.468 36.2747 137.523 30.6136 136.574 26.6762C133.835 15.3272 128.581 13.6248 124.491 14.6114C122.171 15.1713 122.35 18.0012 122.611 22.1053C122.809 25.2336 123.055 29.1022 122.276 33.2699C121.055 39.8024 116.609 46.5499 112.26 51.3521C113.279 52.0697 114.051 53.1385 114.362 54.4308L123.118 90.7229C123.405 91.9126 123.259 93.1113 122.782 94.1549C136.016 95.4642 159.284 88.228 159.284 88.228C164.535 86.9611 167.461 81.8111 166.574 78.1362L166.574 78.1361C166.384 77.3487 166.257 76.8237 165.868 76.3621C168.702 74.2895 170.432 71.0945 169.609 67.6822C169.229 66.1072 168.65 64.8581 167.808 63.6724C169.592 61.8531 170.86 59.0473 170.227 56.4224C169.847 54.8475 168.679 53.4626 167.575 52.3402C169.295 50.2585 170.238 47.2535 169.604 44.6286Z"
                        fill="#C1DFFB" />
                  <path d="M91.3765 55.5187L104.208 52.4229C106.561 51.8553 108.943 53.2866 109.502 55.6035L111.065 62.0842"
                        stroke="#AFD8FF"
                        stroke-width="2"
                        stroke-linecap="round" />
                  <path d="M112.238 66.9447L113.176 70.8332"
                        stroke="#AFD8FF"
                        stroke-width="2"
                        stroke-linecap="round" />
                  <circle cx="110.593"
                          cy="89.9728"
                          r="3"
                          transform="rotate(-13.5638 110.593 89.9728)"
                          stroke="#2876D1"
                          stroke-width="2" />
                  <line x1="89.713"
                        y1="63.1208"
                        x2="97.2179"
                        y2="94.2283"
                        stroke="#2876D1"
                        stroke-width="2"
                        stroke-linecap="round" />
                  <path d="M37.0998 53.8145C36.1684 52.8826 35.0624 52.1434 33.8451 51.639C32.6279 51.1346 31.3232 50.875 30.0055 50.875C28.6879 50.875 27.3832 51.1346 26.1659 51.639C24.9486 52.1434 23.8427 52.8826 22.9112 53.8145L20.978 55.7477L19.0449 53.8145C17.1633 51.933 14.6114 50.876 11.9506 50.876C9.28967 50.876 6.73776 51.933 4.85623 53.8145C2.9747 55.6961 1.91766 58.248 1.91766 60.9089C1.91766 63.5698 2.9747 66.1217 4.85623 68.0032L6.78939 69.9363L20.978 84.125L35.1667 69.9363L37.0998 68.0032C38.0318 67.0717 38.771 65.9657 39.2754 64.7485C39.7798 63.5312 40.0394 62.2265 40.0394 60.9089C40.0394 59.5912 39.7798 58.2865 39.2754 57.0693C38.771 55.852 38.0318 54.746 37.0998 53.8145V53.8145Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M53.7667 70.0707C53.0314 69.335 52.1582 68.7513 51.1972 68.3531C50.2362 67.955 49.2062 67.75 48.166 67.75C47.1257 67.75 46.0957 67.955 45.1347 68.3531C44.1737 68.7513 43.3006 69.335 42.5652 70.0707L41.039 71.5969L39.5128 70.0707C38.0274 68.5853 36.0127 67.7508 33.912 67.7508C31.8113 67.7508 29.7967 68.5853 28.3113 70.0707C26.8258 71.5561 25.9913 73.5708 25.9913 75.6715C25.9913 77.7722 26.8258 79.7868 28.3113 81.2723L29.8374 82.7984L41.039 94L52.2406 82.7984L53.7667 81.2723C54.5025 80.5369 55.0861 79.6637 55.4843 78.7027C55.8825 77.7417 56.0874 76.7117 56.0874 75.6715C56.0874 74.6312 55.8825 73.6012 55.4843 72.6402C55.0861 71.6792 54.5025 70.8061 53.7667 70.0707V70.0707Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M207 16L213.466 29.1008L227.923 31.2016L217.462 41.3992L219.931 55.7984L207 49L194.069 55.7984L196.538 41.3992L186.077 31.2016L200.534 29.1008L207 16Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M204.735 44.363L204.735 44.363L204.741 44.3665C205.426 44.7837 206.197 44.988 207.04 44.988C207.892 44.988 208.669 44.7843 209.356 44.3665L209.356 44.3665L209.361 44.363C210.035 43.9405 210.568 43.3733 210.956 42.6689C211.359 41.9458 211.556 41.1408 211.556 40.2655C211.556 39.3916 211.359 38.5918 210.956 37.8787C210.568 37.1643 210.035 36.5955 209.358 36.1828C208.672 35.7531 207.894 35.543 207.04 35.543H205.374V33.2847H209.823C210.129 33.2847 210.411 33.1745 210.628 32.9464L210.635 32.9385L210.642 32.9304C210.838 32.7058 210.939 32.4338 210.939 32.1363C210.939 31.8319 210.827 31.5576 210.612 31.3427C210.408 31.1166 210.136 30.988 209.823 30.988H204.273C203.956 30.988 203.673 31.1123 203.461 31.3508C203.257 31.5697 203.157 31.8419 203.157 32.1363V36.6913C203.157 36.9888 203.258 37.2608 203.454 37.4854L203.461 37.4935L203.468 37.5014C203.685 37.7295 203.967 37.8397 204.273 37.8397H207.04C207.483 37.8397 207.866 37.9455 208.204 38.1499C208.548 38.3579 208.819 38.6413 209.02 39.0092C209.219 39.372 209.323 39.7869 209.323 40.2655C209.323 40.7441 209.219 41.159 209.02 41.5218C208.819 41.8897 208.548 42.1731 208.204 42.3811C207.866 42.5855 207.483 42.6913 207.04 42.6913C206.599 42.6913 206.216 42.586 205.878 42.3825C205.545 42.1754 205.278 41.8919 205.076 41.5218C204.952 41.2947 204.889 41.2014 204.849 41.1412C204.847 41.1382 204.845 41.1353 204.843 41.1325C204.837 41.1237 204.834 41.1196 204.833 41.1177C204.831 41.1128 204.823 41.0958 204.806 41.0476C204.797 41.0245 204.789 40.9998 204.78 40.9739C204.723 40.8087 204.648 40.5926 204.454 40.4018C204.238 40.1789 203.959 40.0712 203.657 40.0712C203.352 40.0712 203.078 40.1881 202.868 40.4095C202.652 40.6358 202.556 40.9196 202.556 41.2196C202.556 41.4893 202.621 41.7035 202.726 41.9271C202.787 42.0576 202.873 42.2131 202.972 42.3906C203.019 42.4768 203.07 42.5683 203.122 42.6646L203.122 42.6646L203.127 42.6733C203.525 43.3753 204.062 43.9411 204.735 44.363Z"
                        fill="#C1DFFB"
                        stroke="#C1DFFB" />
                  <path d="M202 78L206.114 86.3369L215.315 87.6738L208.657 94.1631L210.229 103.326L202 99L193.771 103.326L195.343 94.1631L188.685 87.6738L197.886 86.3369L202 78Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M234 65L237.233 71.5504L244.462 72.6008L239.231 77.6996L240.466 84.8992L234 81.5L227.534 84.8992L228.769 77.6996L223.538 72.6008L230.767 71.5504L234 65Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <circle cx="72"
                          cy="19"
                          r="18"
                          stroke="#C1DFFB"
                          stroke-width="2" />
                  <path d="M77.9104 25.3649C76.2044 26.6978 74.0979 27.4149 71.9331 27.3998C69.7682 27.3847 67.672 26.6383 65.9847 25.2818"
                        stroke="#6EB9FF"
                        stroke-width="2"
                        stroke-linecap="round" />
                  <path d="M68.3698 15.6059C67.9798 15.1602 67.5168 14.8066 67.0073 14.5654C66.4977 14.3242 65.9515 14.2 65.4 14.2C64.8484 14.2 64.3023 14.3242 63.7927 14.5654C63.2832 14.8066 62.8202 15.1602 62.4301 15.6059"
                        stroke="#6EB9FF"
                        stroke-width="2"
                        stroke-linecap="round" />
                  <path d="M81.5699 15.6059C81.1798 15.1602 80.7168 14.8066 80.2073 14.5654C79.6977 14.3242 79.1516 14.2 78.6 14.2C78.0485 14.2 77.5023 14.3242 76.9927 14.5654C76.4832 14.8066 76.0202 15.1602 75.6302 15.6059"
                        stroke="#6EB9FF"
                        stroke-width="2"
                        stroke-linecap="round" />
            </svg>

            <div data-bind="html: texts.answers"></div>

            <!-- /ko -->

            <!-- ko template: {
            foreach: templateIf($modal.activeStepId() === 'reports', $data),
            afterAdd: fadeAfterAddFactory(200, 0),
            beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg class="b-quick-help__icon"
                 width="251"
                 height="111"
                 viewBox="0 0 251 111"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M250.237 110.294C249.042 98.3 246.024 86.5219 241.262 75.3489C234.951 60.5401 225.7 47.0844 214.039 35.7502C202.377 24.416 188.533 15.4252 173.296 9.29117C158.059 3.15714 141.729 0 125.237 0C108.745 0 92.4143 3.15715 77.1776 9.29118C61.941 15.4252 48.0966 24.416 36.435 35.7502C24.7734 47.0844 15.5229 60.5401 9.21167 75.349C4.45001 86.5219 1.43184 98.3 0.236816 110.294H250.237Z"
                        fill="#EFF2FA" />
                  <path d="M177.443 90.441H237.737"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="1.5"
                        y1="-1.5"
                        x2="20.5588"
                        y2="-1.5"
                        transform="matrix(0 -1 -1 -2.79129e-09 190.679 83.8236)"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="1.5"
                        y1="-1.5"
                        x2="33.7941"
                        y2="-1.5"
                        transform="matrix(0 -1 -1 -2.79129e-09 208.323 83.8236)"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="218.589"
                        y1="82.3236"
                        x2="218.589"
                        y2="33.853"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="1.5"
                        y1="-1.5"
                        x2="27.9118"
                        y2="-1.5"
                        transform="matrix(0 -1 -1 -2.79129e-09 181.855 83.8236)"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="1.5"
                        y1="-1.5"
                        x2="41.1471"
                        y2="-1.5"
                        transform="matrix(0 -1 -1 -2.79129e-09 199.499 83.8236)"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="227.414"
                        y1="82.3236"
                        x2="227.414"
                        y2="41.206"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M17.1479 53.6766L61.2656 53.6766"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M57.5879 37.2328L52.301 31.8782L44.3978 36.7349L33.686 27.0263L25.407 29.9748L19.5494 23.5296"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M18.9878 37.6336L24.4326 40.6837L30.1389 35.7781L41.4191 43.3454L50.6002 42.1264L57.0263 46.3235"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M29.6514 96.3239V73.1621"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="37.7695"
                        y1="92.177"
                        x2="74.4754"
                        y2="92.177"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="37.7695"
                        y1="84.8975"
                        x2="61.2401"
                        y2="84.8975"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="37.7695"
                        y1="77.6179"
                        x2="67.8578"
                        y2="77.6179"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M104.649 20.956C110.948 18.016 118.987 16.5443 126.708 16.5443C140.93 17.4244 152.033 25.9702 158.484 39.7912C167.876 59.9111 159.178 83.8345 139.058 93.2257C134.691 95.2645 131.247 96.657 126.708 97.059C110.339 98.5088 92.9764 89.5519 85.624 73.7998C76.2327 53.68 84.5292 30.3472 104.649 20.956ZM130.556 75.0106C140.616 70.315 144.965 58.3533 140.269 48.2934C135.574 38.2335 124.87 35.662 117.884 38.5804C107.824 43.276 102.075 51.8384 108.325 66.5443C113.021 76.6042 124.502 77.2061 130.556 75.0106Z"
                        fill="#255EB3" />
                  <mask id="mask0"
                        mask-type="alpha"
                        maskUnits="userSpaceOnUse"
                        x="81"
                        y="16"
                        width="82"
                        height="82">
                        <path d="M105.049 20.3652C111.348 17.4251 119.123 16.2579 125.604 16.659C139.827 17.539 152.033 25.97 158.484 39.791C167.875 59.9109 159.178 83.8343 139.058 93.2256C134.69 95.2643 131.246 96.6569 126.707 97.0589C110.339 98.5086 92.976 89.5517 85.6235 73.7997C76.2323 53.6798 84.9296 29.7564 105.049 20.3652ZM130.556 75.0105C140.616 70.3148 144.964 58.3531 140.269 48.2932C135.573 38.2333 124.869 35.6618 117.884 38.5803C107.824 43.2759 102.075 51.8383 108.325 66.5441C113.021 76.6041 124.502 77.2059 130.556 75.0105Z"
                              fill="#255EB3" />
                  </mask>
                  <g mask="url(#mask0)">
                        <path opacity="0.6"
                              d="M118.218 38.521L126.035 34.79L133.84 37.5566L131.644 42.1759L119.03 41.0845L118.218 38.521Z"
                              fill="#6EB9FF" />
                        <path opacity="0.6"
                              d="M133.776 37.7091L151.391 45.3741L146.926 59.9265L138.47 59.7905L130.997 43.5086L133.776 37.7091Z"
                              fill="#2D99FF" />
                  </g>
                  <path d="M109.766 20.3653C103.451 23.3133 98.0097 27.8526 93.9776 33.5377C89.9455 39.2229 87.4607 45.8587 86.7669 52.794C86.0732 59.7292 87.1944 66.7257 90.0205 73.0969C92.8466 79.4681 97.2805 84.9952 102.887 89.1357C108.494 93.2763 115.081 95.8881 122.001 96.715C128.922 97.5418 135.939 96.5552 142.363 93.852C148.787 91.1488 154.399 86.8219 158.646 81.2958C162.894 75.7696 165.631 69.2342 166.591 62.3307L146.681 59.5631C146.201 63.0149 144.832 66.2826 142.708 69.0456C140.585 71.8087 137.779 73.9722 134.567 75.3238C131.355 76.6754 127.846 77.1687 124.386 76.7552C120.926 76.3418 117.632 75.0359 114.829 72.9656C112.026 70.8953 109.809 68.1318 108.396 64.9462C106.983 61.7606 106.422 58.2624 106.769 54.7947C107.116 51.3271 108.358 48.0092 110.374 45.1666C112.39 42.324 115.111 40.0544 118.268 38.5804L109.766 20.3653Z"
                        fill="#2D99FF" />
                  <path d="M109.771 20.3653C116.382 17.2796 123.708 16.0509 130.964 16.811C138.22 17.5711 145.132 20.2914 150.96 24.6802C156.788 29.0691 161.312 34.9611 164.047 41.7248C166.782 48.4885 167.624 55.8689 166.485 63.075L146.63 59.9353C147.2 56.3322 146.778 52.642 145.411 49.2602C144.043 45.8783 141.781 42.9323 138.868 40.7379C135.954 38.5435 132.497 37.1833 128.869 36.8033C125.241 36.4232 121.578 37.0375 118.273 38.5804L109.771 20.3653Z"
                        fill="#6EB9FF" />
                  <path opacity="0.6"
                        d="M109.755 20.3617C114.552 18.1227 119.743 16.8505 125.031 16.6176C130.32 16.3847 135.603 17.1957 140.578 19.0044L133.691 37.9467C131.204 37.0424 128.562 36.6369 125.918 36.7533C123.274 36.8698 120.678 37.5059 118.28 38.6254L109.755 20.3617Z"
                        fill="#A9D5FF" />
                  <path opacity="0.5"
                        d="M101.533 82.6715C95.0294 76.1547 91.2533 67.4047 90.9733 58.2021"
                        stroke="white"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path opacity="0.5"
                        d="M109.855 88.8966C108.523 88.1717 107.237 87.3637 106.007 86.4775"
                        stroke="white"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path opacity="0.5"
                        d="M153.973 32.7884C156.097 35.132 157.906 37.744 159.352 40.5577"
                        stroke="white"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path opacity="0.5"
                        d="M148.017 27.5186C148.637 27.9556 149.243 28.4119 149.834 28.8868"
                        stroke="white"
                        stroke-width="3"
                        stroke-linecap="round" />
            </svg>

            <div data-bind="html: texts.reports"></div>
            <!-- /ko -->
            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'clients', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg width="250"
                 class="b-quick-help__icon"
                 height="112"
                 viewBox="0 0 250 112"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M250 111.147C248.805 99.153 245.787 87.3748 241.025 76.2019C234.714 61.3931 225.463 47.9374 213.802 36.6032C202.14 25.269 188.296 16.2782 173.059 10.1441C157.823 4.01011 141.492 0.852966 125 0.852966C108.508 0.852966 92.1775 4.01011 76.9408 10.1441C61.7042 16.2782 47.8598 25.269 36.1982 36.6032C24.5366 47.9374 15.2861 61.3931 8.97486 76.2019C4.21319 87.3748 1.19502 99.153 0 111.147H250Z"
                        fill="#EFF2FA" />
                  <mask id="mask0"
                        mask-type="alpha"
                        maskUnits="userSpaceOnUse"
                        x="2"
                        y="4"
                        width="247"
                        height="108">
                        <path d="M2.20605 11.5295C2.20605 7.66349 5.34006 4.52948 9.20605 4.52948H241.53C245.396 4.52948 248.53 7.66349 248.53 11.5295V104.147C248.53 108.013 245.396 111.147 241.53 111.147H9.20605C5.34006 111.147 2.20605 108.013 2.20605 104.147V11.5295Z"
                              fill="white" />
                  </mask>
                  <g mask="url(#mask0)">
                        <rect x="81"
                              y="73.6471"
                              width="88.2353"
                              height="75.7353"
                              rx="30"
                              fill="#2D99FF" />
                        <path opacity="0.5"
                              d="M81 102.528C81 87.3756 93.5803 73.6471 110.412 73.6471H128.059C115.259 73.6471 108.206 82.1919 108.206 97.3439C108.206 112.496 108.206 122.145 108.206 137.297C108.206 152.449 116.604 164.732 128.794 164.732H112.947C96.1152 164.732 81 152.449 81 137.297V102.528Z"
                              fill="#357CE6" />
                        <path d="M139.706 73.6471C139.706 80.1446 133.451 85.4118 125.735 85.4118C118.019 85.4118 111.765 80.1446 111.765 73.6471"
                              stroke="#2876D1"
                              stroke-width="3" />
                        <path d="M98.7646 98.4412C98.7646 97.6128 98.0931 96.9412 97.2646 96.9412C96.4362 96.9412 95.7646 97.6128 95.7646 98.4412H98.7646ZM95.7646 98.4412V111.677H98.7646V98.4412H95.7646Z"
                              fill="#2876D1" />
                        <path d="M151.706 98.4412C151.706 97.6128 151.034 96.9412 150.206 96.9412C149.378 96.9412 148.706 97.6128 148.706 98.4412H151.706ZM148.706 98.4412V111.677H151.706V98.4412H148.706Z"
                              fill="#2876D1" />
                        <path d="M134 75.1177H139.95C152.669 75.1177 163.383 83.8343 166.616 95.7059"
                              stroke="#AFD8FF"
                              stroke-width="3"
                              stroke-linecap="round" />
                        <path d="M128.611 75.1177H125.669"
                              stroke="#AFD8FF"
                              stroke-width="3"
                              stroke-linecap="round" />
                  </g>
                  <circle cx="125.735"
                          cy="45.8407"
                          r="22.0588"
                          fill="#A4CAF6" />
                  <mask id="mask1"
                        mask-type="alpha"
                        maskUnits="userSpaceOnUse"
                        x="103"
                        y="23"
                        width="45"
                        height="45">
                        <circle cx="125.735"
                                cy="45.8407"
                                r="22.0588"
                                fill="#A4CAF6" />
                  </mask>
                  <g mask="url(#mask1)">
                        <circle cx="131.071"
                                cy="41.5942"
                                r="21.1327"
                                fill="#C1DFFB" />
                  </g>
                  <circle opacity="0.7"
                          cx="136.029"
                          cy="36.8824"
                          r="4.41177"
                          fill="white" />
                  <path d="M181.617 14.8235H205.147C206.765 14.8235 208.088 15.9816 208.088 17.3971V32.8383C208.088 34.2537 206.765 35.4118 205.147 35.4118H181.617C180 35.4118 178.676 34.2537 178.676 32.8383V17.3971C178.676 15.9816 180 14.8235 181.617 14.8235Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M201.729 21.6863L192.985 27.024L184.241 21.6863"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M32.3533 72.9118H55.8827C57.5003 72.9118 58.8239 74.0699 58.8239 75.4853V90.9265C58.8239 92.342 57.5003 93.5 55.8827 93.5H32.3533C30.7356 93.5 29.4121 92.342 29.4121 90.9265V75.4853C29.4121 74.0699 30.7356 72.9118 32.3533 72.9118Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M52.6677 79.5532L43.7757 85.5304L34.8838 79.5532"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M186.629 89.7167V95.0663C186.627 95.5629 186.729 96.0544 186.928 96.5095C187.128 96.9645 187.42 97.3729 187.787 97.7087C188.154 98.0444 188.586 98.3 189.058 98.4591C189.529 98.6182 190.029 98.6773 190.524 98.6326C196.022 98.0364 201.303 96.1614 205.944 93.1583C210.261 90.4205 213.921 86.7677 216.664 82.4592C219.683 77.8073 221.563 72.5109 222.149 66.9991C222.194 66.506 222.135 66.009 221.977 65.5398C221.818 65.0706 221.564 64.6394 221.229 64.2738C220.895 63.9081 220.488 63.6159 220.034 63.4159C219.58 63.2158 219.09 63.1123 218.594 63.1118H213.233C212.366 63.1033 211.526 63.4097 210.868 63.974C210.211 64.5383 209.781 65.322 209.66 66.1789C209.434 67.8908 209.014 69.5718 208.409 71.1896C208.169 71.8278 208.117 72.5215 208.259 73.1883C208.402 73.8551 208.733 74.4672 209.213 74.9521L211.482 77.2167C208.939 81.681 205.235 85.3773 200.762 87.9157L198.493 85.6511C198.007 85.1717 197.394 84.8413 196.726 84.6991C196.058 84.5568 195.363 84.6088 194.723 84.8487C193.102 85.4524 191.418 85.8711 189.702 86.0969C188.834 86.2191 188.042 86.6554 187.475 87.3228C186.909 87.9902 186.607 88.8422 186.629 89.7167Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M200.595 67.0074C198.266 67.4617 196.126 68.6006 194.448 70.2783C192.77 71.956 191.632 74.0962 191.177 76.4249M200.595 57.4706C195.757 58.0081 191.245 60.1747 187.801 63.6147C184.356 67.0547 182.184 71.5636 181.64 76.4011"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <rect x="41.1929"
                        y="19.2514"
                        width="31.8843"
                        height="38.1134"
                        rx="5"
                        transform="rotate(-20.9619 41.1929 19.2514)"
                        stroke="#C1DFFB"
                        stroke-width="3" />
                  <path d="M66.201 38.1615C62.3244 39.6496 57.9762 37.7137 56.4881 33.8371C55 29.9604 56.936 25.6123 60.8126 24.1242C64.6892 22.6361 69.0373 24.572 70.5254 28.4487L70.9744 29.6184C71.3879 30.6954 70.8502 31.903 69.7732 32.3164C68.6962 32.7298 67.4886 32.1922 67.0752 31.1152L66.6262 29.9454M66.6262 29.9454C65.9649 28.2227 64.032 27.3622 62.3094 28.0235C60.5867 28.6847 59.7261 30.6176 60.3874 32.3403C61.0487 34.063 62.9815 34.9235 64.7042 34.2622C66.4269 33.601 67.2874 31.6681 66.6262 29.9454Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M47.5313 28.2105L42.4624 30.1523"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M51.0508 37.3977L45.9819 39.3396"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M54.4492 46.2683L49.3804 48.2102"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
            </svg>
            <div data-bind="html: texts.clients"></div>
            <!-- /ko -->
            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'points', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg width="251"
                 class="b-quick-help__icon"
                 height="111"
                 viewBox="0 0 251 111"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M250.237 110.765C249.042 98.7706 246.024 86.9925 241.262 75.8195C234.951 61.0107 225.7 47.555 214.039 36.2208C202.377 24.8866 188.533 15.8958 173.296 9.76176C158.059 3.62772 141.729 0.470581 125.237 0.470581C108.745 0.470581 92.4143 3.62773 77.1776 9.76176C61.941 15.8958 48.0966 24.8866 36.435 36.2208C24.7734 47.555 15.5229 61.0107 9.21167 75.8195C4.45001 86.9925 1.43184 98.7706 0.236816 110.765H250.237Z"
                        fill="#EFF2FA" />
                  <path d="M121.56 19.5882C105.723 19.5882 92.8838 32.4271 92.8838 48.2647C92.8838 66.6818 108.789 84.4937 114.943 91.0037C116.218 92.3525 120.938 96.029 122.342 96.4035C123.745 96.778 125.168 96.955 127.052 96.5251C135.26 87.9643 150.237 64.7212 150.237 48.2647C150.237 34.2188 140.838 22.0528 127.505 19.5882L121.56 19.5882Z"
                        fill="#255EB2" />
                  <path d="M126.707 96.5251C130.797 96.5251 155.384 70.6463 155.384 48.2647C155.384 32.4271 142.545 19.5882 126.707 19.5882C110.87 19.5882 98.0308 32.4271 98.0308 48.2647C98.0308 70.2966 122.617 96.5251 126.707 96.5251Z"
                        fill="#2D99FF" />
                  <circle cx="127.075"
                          cy="47.8971"
                          r="19.4853"
                          fill="#6EB9FF" />
                  <circle cx="127.443"
                          cy="47.5294"
                          r="9.55882"
                          fill="#2876D1" />
                  <path d="M129.219 21.1766C126.165 20.8814 123.179 21.1001 120.348 21.7621"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M115.887 23.3235C115.168 23.6466 114.47 23.9969 113.793 24.3727"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M153.908 47.8971C153.908 35.4925 145.266 25.04 133.486 21.8631"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M44.3546 96.9466C44.3546 96.9466 61.2664 81.6847 61.2664 68.4853C61.2664 59.1452 53.6947 51.5735 44.3546 51.5735C35.0145 51.5735 27.4429 59.1452 27.4429 68.4853C27.4429 81.4785 44.3546 96.9466 44.3546 96.9466Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M36.2979 73.3159V67.9173C36.2979 65.7696 37.151 63.7098 38.6697 62.1912C40.1884 60.6725 42.2481 59.8193 44.3958 59.8193C46.5435 59.8193 48.6033 60.6725 50.1219 62.1912C51.6406 63.7098 52.4938 65.7696 52.4938 67.9173V73.3159M52.4938 74.2157C52.4938 74.693 52.3042 75.1507 51.9667 75.4882C51.6292 75.8257 51.1715 76.0153 50.6942 76.0153H49.7945C49.3172 76.0153 48.8595 75.8257 48.522 75.4882C48.1845 75.1507 47.9949 74.693 47.9949 74.2157V71.5164C47.9949 71.0391 48.1845 70.5814 48.522 70.2439C48.8595 69.9064 49.3172 69.7168 49.7945 69.7168H52.4938V74.2157ZM36.2979 74.2157C36.2979 74.693 36.4874 75.1507 36.8249 75.4882C37.1624 75.8257 37.6201 76.0153 38.0974 76.0153H38.9972C39.4744 76.0153 39.9322 75.8257 40.2696 75.4882C40.6071 75.1507 40.7967 74.693 40.7967 74.2157V71.5164C40.7967 71.0391 40.6071 70.5814 40.2696 70.2439C39.9322 69.9064 39.4744 69.7168 38.9972 69.7168H36.2979V74.2157Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M64.5751 41.6471C64.5751 41.6471 78.1781 29.2808 78.1781 18.5856C78.1781 11.0175 72.0878 4.88235 64.5751 4.88235C57.0624 4.88235 50.9722 11.0175 50.9722 18.5856C50.9722 29.1136 64.5751 41.6471 64.5751 41.6471Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M65.9124 13.0259C65.6565 12.5228 65.1398 12.2059 64.5753 12.2059C64.0108 12.2059 63.4941 12.5228 63.2383 13.0259L62.1152 15.2344L59.5852 15.5934C59.0149 15.6743 58.5413 16.0746 58.3666 16.6235C58.1918 17.1723 58.3468 17.7728 58.7654 18.1685L60.5679 19.8725L60.1445 22.2684C60.0455 22.8288 60.2721 23.397 60.7296 23.7354C61.1871 24.0738 61.7967 24.1242 62.3036 23.8654L64.5753 22.7058L66.847 23.8654C67.3539 24.1242 67.9635 24.0738 68.421 23.7354C68.8785 23.397 69.1051 22.8288 69.0061 22.2684L68.5828 19.8725L70.3852 18.1685C70.8038 17.7728 70.9588 17.1723 70.784 16.6235C70.6093 16.0746 70.1357 15.6743 69.5654 15.5934L67.0355 15.2344L65.9124 13.0259Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M209.428 71.0588C209.428 71.0588 226.708 55.4773 226.708 42.0013C226.708 32.4656 218.971 24.7353 209.428 24.7353C199.885 24.7353 192.149 32.4656 192.149 42.0013C192.149 55.2667 209.428 71.0588 209.428 71.0588Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M216.098 39.1127H217.044C218.047 39.1127 219.008 39.4141 219.718 40.1173C220.427 40.8206 220.825 41.7743 220.825 42.7688C220.825 43.7633 220.427 44.7171 219.718 45.4203C219.008 46.1236 218.047 46.5186 217.044 46.5186H216.098M216.098 39.1127H200.972V47.4561C200.972 48.4506 201.371 49.4044 202.08 50.1076C202.789 50.8108 203.751 51.2059 204.754 51.2059H212.317C213.32 51.2059 214.281 50.8108 214.991 50.1076C215.7 49.4044 216.098 48.4506 216.098 47.4561V39.1127ZM204.281 32.0882V35.2693M208.199 32.0882V35.2693M212.141 32.0882V35.2693"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M176.708 100.872C176.708 100.872 189.208 89.5913 189.208 79.8352C189.208 72.9316 183.611 67.3352 176.708 67.3352C169.804 67.3352 164.208 72.9316 164.208 79.8352C164.208 89.4389 176.708 100.872 176.708 100.872Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M171.715 77.186L176.708 80.074L181.7 77.186M176.708 85.833V80.0683M181.855 82.3559V77.7807C181.854 77.5801 181.802 77.3831 181.701 77.2095C181.601 77.0358 181.456 76.8916 181.283 76.7913L177.28 74.5038C177.106 74.4034 176.908 74.3505 176.708 74.3505C176.507 74.3505 176.31 74.4034 176.136 74.5038L172.132 76.7913C171.959 76.8916 171.814 77.0358 171.714 77.2095C171.614 77.3831 171.561 77.5801 171.561 77.7807V82.3559C171.561 82.5565 171.614 82.7535 171.714 82.9271C171.814 83.1008 171.959 83.245 172.132 83.3453L176.136 85.6329C176.31 85.7332 176.507 85.7861 176.708 85.7861C176.908 85.7861 177.106 85.7332 177.28 85.6329L181.283 83.3453C181.456 83.245 181.601 83.1008 181.701 82.9271C181.802 82.7535 181.854 82.5565 181.855 82.3559Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M87.2952 81.8468H72.7371M72.7371 81.8468L80.0161 89.1258M72.7371 81.8468L80.0161 74.5677"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M169.67 38.8951H180.068M180.068 38.8951L174.869 33.6958M180.068 38.8951L174.869 44.0944"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
            </svg>

            <div data-bind="html: texts.points"></div>
            <!-- /ko -->


            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'settings', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg width="250"
                 class="b-quick-help__icon"
                 height="111"
                 viewBox="0 0 250 111"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M250 110.294C248.805 98.3 245.787 86.5219 241.025 75.3489C234.714 60.5401 225.463 47.0844 213.802 35.7502C202.14 24.416 188.296 15.4252 173.059 9.29117C157.823 3.15714 141.492 0 125 0C108.508 0 92.1775 3.15715 76.9408 9.29118C61.7042 15.4252 47.8598 24.416 36.1982 35.7502C24.5366 47.0844 15.2861 60.5401 8.97486 75.349C4.21319 86.5219 1.19502 98.3 0 110.294H250Z"
                        fill="#EFF2FA" />
                  <path fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M162.666 62.0037V53.1612L151.944 49.6242L149.457 43.6003L153.86 27.1864H148.076L138.072 32.2156L132.049 29.7286L128.235 19.1176H119.393L115.911 29.8392L112.844 31.0826L105.515 27.4627H99.6077L93.3627 33.7077L98.3919 43.7108L95.9049 49.7347L85.2939 53.4928V62.28L96.0155 65.817L98.5024 71.8409L93.639 82.0098L99.884 88.2548H105.836L109.887 83.2256L115.911 85.7126L119.724 96.3235H128.512L131.993 85.602L138.128 83.1151L148.297 87.9784H154.044L149.457 71.7304L152.055 65.7065L162.666 61.8931V62.0037ZM123.98 74.3279C114.806 74.3279 107.4 66.9223 107.4 57.7482C107.4 48.5742 114.806 41.1686 123.98 41.1686C133.154 41.1686 140.559 48.5742 140.559 57.7482C140.559 66.9223 133.154 74.3279 123.98 74.3279Z"
                        fill="#255EB2" />
                  <path fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M168.548 62.0037V53.1612L157.826 49.6242L155.34 43.6003L160.203 33.4314L153.958 27.1864L143.955 32.2156L137.931 29.7286L134.118 19.1176H125.275L121.793 29.8392L115.659 32.3261L105.49 27.4627L99.245 33.7077L104.274 43.7108L101.787 49.7347L91.1763 53.4928V62.28L101.898 65.817L104.385 71.8409L99.5214 82.0098L105.766 88.2548L115.769 83.2256L121.793 85.7126L125.607 96.3235H134.394L137.876 85.602L144.01 83.1151L154.179 87.9785L160.424 81.7335L155.34 71.7304L157.937 65.7065L168.548 61.8931V62.0037ZM129.862 74.3279C120.688 74.3279 113.282 66.9223 113.282 57.7482C113.282 48.5742 120.688 41.1686 129.862 41.1686C139.036 41.1686 146.442 48.5742 146.442 57.7482C146.442 66.9223 139.036 74.3279 129.862 74.3279Z"
                        fill="#2D99FF" />
                  <circle cx="129.78"
                          cy="57.7206"
                          r="20.9559"
                          stroke="#2876D1"
                          stroke-width="3" />
                  <path d="M152.842 51.5008C151.927 47.9032 150.19 44.677 147.72 41.905C145.251 39.1331 142.228 36.9366 138.76 35.6131C135.291 34.2897 131.285 33.4814 127.597 33.9037"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M121.441 35.3759C119.753 36.0387 118.052 36.8789 116.47 37.8911"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M109.673 70.6663C110.689 72.1693 111.88 73.6454 113.213 74.9689"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M112.707 41.2081C112.145 41.8113 111.596 42.4486 111.072 43.1143"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M117.181 78.037C117.892 78.4538 118.634 78.8499 119.398 79.2161"
                        stroke="#AFD8FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M56.1131 23.6066C56.3825 23.8814 56.5333 24.2509 56.5333 24.6357C56.5333 25.0206 56.3825 25.3901 56.1131 25.6649L53.7607 28.0172C53.4859 28.2866 53.1164 28.4375 52.7316 28.4375C52.3468 28.4375 51.9773 28.2866 51.7025 28.0172L46.1598 22.4745C45.4205 24.1082 45.1967 25.9284 45.5181 27.6925C45.8395 29.4566 46.691 31.0809 47.9589 32.3488C49.2269 33.6168 50.8511 34.4682 52.6152 34.7896C54.3794 35.1111 56.1995 34.8872 57.8332 34.148L67.9923 44.3071C68.5772 44.892 69.3705 45.2205 70.1976 45.2205C71.0248 45.2205 71.8181 44.892 72.4029 44.3071C72.9878 43.7222 73.3164 42.9289 73.3164 42.1018C73.3164 41.2746 72.9878 40.4813 72.4029 39.8965L62.2438 29.7373C62.9831 28.1037 63.2069 26.2835 62.8855 24.5194C62.5641 22.7553 61.7127 21.131 60.4447 19.863C59.1767 18.5951 57.5525 17.7437 55.7884 17.4222C54.0243 17.1008 52.2041 17.3246 50.5704 18.0639L56.0984 23.5919L56.1131 23.6066Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M195.053 91.1764V87.4999C195.053 85.5498 195.842 83.6796 197.246 82.3006C198.65 80.9217 200.555 80.147 202.54 80.147H217.513C219.499 80.147 221.403 80.9217 222.807 82.3006C224.211 83.6796 225 85.5498 225 87.4999V91.1764M183.824 91.1764V87.4999C183.825 85.8708 184.377 84.2881 185.393 83.0005C186.409 81.7129 187.832 80.7933 189.438 80.386M196.925 58.3271C195.315 58.7321 193.887 59.652 192.868 60.9417C191.849 62.2314 191.295 63.8176 191.295 65.4503C191.295 67.083 191.849 68.6692 192.868 69.9589C193.887 71.2486 195.315 72.1685 196.925 72.5735M202.54 65.4411C202.54 69.502 205.892 72.7941 210.027 72.7941C214.161 72.7941 217.513 69.502 217.513 65.4411C217.513 61.3802 214.161 58.0882 210.027 58.0882C205.892 58.0882 202.54 61.3802 202.54 65.4411Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M29.4121 70.5882L40.4415 70.5882"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M53.6768 70.5882H69.1179"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M29.4121 82.3529L52.2062 82.3529"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M48.5298 94.1176H69.118"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M29.4121 94.1176L35.2945 94.1176"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M65.4414 82.3529H69.1179"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <circle cx="44.1179"
                          cy="70.5882"
                          r="3.67647"
                          stroke="#C1DFFB"
                          stroke-width="3" />
                  <circle cx="38.9709"
                          cy="94.1176"
                          r="3.67647"
                          stroke="#C1DFFB"
                          stroke-width="3" />
                  <circle cx="55.8825"
                          cy="82.3529"
                          r="3.67647"
                          stroke="#C1DFFB"
                          stroke-width="3" />
                  <path d="M199.673 11.0294L204.412 15.7085M204.412 15.7085L199.673 20.3876M204.412 15.7085H187.827C186.57 15.7085 185.365 16.2015 184.476 17.079C183.588 17.9565 183.088 19.1467 183.088 20.3876V22.7272M187.827 36.7647L183.088 32.0855M183.088 32.0855L187.827 27.4064M183.088 32.0855H199.673C200.93 32.0855 202.135 31.5925 203.024 30.715C203.913 29.8375 204.412 28.6474 204.412 27.4064V25.0668"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
            </svg>

            <div data-bind="html: texts.settings"></div>
            <!-- /ko -->

            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'mailings', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg class="b-quick-help__icon"
                 width="264"
                 height="112"
                 viewBox="0 0 264 112"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M255 111.294C253.805 99.3 250.787 87.5219 246.025 76.3489C239.714 61.5401 230.464 48.0844 218.802 36.7502C207.14 25.416 193.296 16.4252 178.059 10.2912C162.823 4.15714 146.492 1 130 1C113.508 1 97.1777 4.15715 81.9411 10.2912C66.7044 16.4252 52.8601 25.416 41.1984 36.7502C29.5368 48.0844 20.2863 61.5401 13.9751 76.349C9.21344 87.5219 6.19527 99.3 5.00024 111.294H255Z"
                        fill="#EFF2FA" />
                  <path d="M240.295 93.4169C228.369 93.4169 218.702 83.7503 218.702 71.8236C218.702 59.8968 228.369 50.2302 240.295 50.2302C252.222 50.2302 261.889 59.8968 261.889 71.8236V75.4225C261.889 78.7358 259.204 81.4206 255.89 81.4206C252.577 81.4206 249.892 78.7358 249.892 75.4225V71.8236M249.892 71.8236C249.892 66.5236 245.595 62.2265 240.295 62.2265C234.995 62.2265 230.698 66.5236 230.698 71.8236C230.698 77.1235 234.995 81.4206 240.295 81.4206C245.595 81.4206 249.892 77.1235 249.892 71.8236Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M45.6623 16.0661L47.1617 16.1077L47.1617 16.1076L45.6623 16.0661ZM59.6761 2.05232L59.7176 3.55174L59.7177 3.55174L59.6761 2.05232ZM73.1705 22.5656L71.8103 21.9332C71.674 22.2264 71.6361 22.5559 71.7022 22.8724L73.1705 22.5656ZM75.027 31.4523L74.7155 32.9196C75.2114 33.0249 75.7269 32.8726 76.086 32.5147C76.445 32.1569 76.599 31.6418 76.4953 31.1456L75.027 31.4523ZM66.1564 29.5685L66.468 28.1013C66.1502 28.0338 65.8192 28.0713 65.5246 28.2081L66.1564 29.5685ZM61.0542 16.7138C61.0542 17.1207 60.7244 17.4505 60.3175 17.4505V20.4505C62.3812 20.4505 64.0542 18.7775 64.0542 16.7138H61.0542ZM60.3175 17.4505C59.9107 17.4505 59.5809 17.1207 59.5809 16.7138H56.5809C56.5809 18.7775 58.2538 20.4505 60.3175 20.4505V17.4505ZM59.5809 16.7138C59.5809 16.3069 59.9107 15.9771 60.3175 15.9771V12.9771C58.2538 12.9771 56.5809 14.6501 56.5809 16.7138H59.5809ZM60.3175 15.9771C60.7244 15.9771 61.0542 16.3069 61.0542 16.7138H64.0542C64.0542 14.6501 62.3812 12.9771 60.3175 12.9771V15.9771ZM53.1502 16.7137C53.1502 17.1206 52.8204 17.4504 52.4135 17.4504V20.4504C54.4772 20.4504 56.1502 18.7774 56.1502 16.7137H53.1502ZM52.4135 17.4504C52.0067 17.4504 51.6768 17.1206 51.6768 16.7137H48.6768C48.6768 18.7774 50.3498 20.4504 52.4135 20.4504V17.4504ZM51.6768 16.7137C51.6768 16.3069 52.0067 15.977 52.4135 15.977V12.977C50.3498 12.977 48.6768 14.65 48.6768 16.7137H51.6768ZM52.4135 15.977C52.8204 15.977 53.1502 16.3069 53.1502 16.7137H56.1502C56.1502 14.65 54.4772 12.977 52.4135 12.977V15.977ZM68.9583 16.7137C68.9583 17.1206 68.6285 17.4504 68.2216 17.4504V20.4504C70.2853 20.4504 71.9583 18.7774 71.9583 16.7137H68.9583ZM68.2216 17.4504C67.8148 17.4504 67.4849 17.1206 67.4849 16.7137H64.4849C64.4849 18.7774 66.1579 20.4504 68.2216 20.4504V17.4504ZM67.4849 16.7137C67.4849 16.3069 67.8148 15.977 68.2216 15.977V12.977C66.1579 12.977 64.4849 14.65 64.4849 16.7137H67.4849ZM68.2216 15.977C68.6285 15.977 68.9583 16.3069 68.9583 16.7137H71.9583C71.9583 14.65 70.2853 12.977 68.2216 12.977V15.977ZM60.085 29.404C52.8229 29.404 46.9591 23.4165 47.1617 16.1077L44.1629 16.0246C43.9134 25.027 51.1383 32.404 60.085 32.404V29.404ZM47.1617 16.1076C47.3481 9.37477 52.9848 3.73813 59.7176 3.55174L59.6346 0.552891C51.31 0.783343 44.3933 7.69999 44.1629 16.0246L47.1617 16.1076ZM59.7177 3.55174C67.0265 3.34916 73.014 9.21294 73.014 16.475H76.014C76.014 7.52834 68.637 0.303366 59.6346 0.552892L59.7177 3.55174ZM73.014 16.475C73.014 18.4266 72.5815 20.2745 71.8103 21.9332L74.5307 23.198C75.4814 21.1533 76.014 18.8745 76.014 16.475H73.014ZM71.7022 22.8724L73.5587 31.7591L76.4953 31.1456L74.6388 22.2589L71.7022 22.8724ZM75.3386 29.985L66.468 28.1013L65.8448 31.0358L74.7155 32.9196L75.3386 29.985ZM65.5246 28.2081C63.8717 28.9757 62.03 29.404 60.085 29.404V32.404C62.475 32.404 64.7472 31.8768 66.7882 30.929L65.5246 28.2081Z"
                        fill="#6EB9FF" />
                  <path d="M35.0463 41.9164C31.4104 38.2782 26.575 36.2725 21.4237 36.2703C10.8052 36.2703 2.16592 44.9051 2.16371 55.5192C2.15929 58.8967 3.04508 62.2167 4.73273 65.1436L2.00024 75.1193L12.2101 72.442C15.0353 73.9795 18.1986 74.7857 21.4148 74.7857H21.4237C32.0377 74.7857 40.677 66.1487 40.6814 55.5346C40.6836 50.3922 38.6823 45.5567 35.0463 41.9164Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-miterlimit="10"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M30.5644 63.0054C31.7329 61.8368 31.4325 59.8686 29.9657 59.1043L28.3598 58.2671C27.4122 57.7723 26.2525 57.9512 25.497 58.7067C24.6974 59.5064 23.4449 59.6698 22.4818 59.0778C21.5849 58.5256 20.7345 57.8629 19.9569 57.0831C19.1794 56.3056 18.5167 55.4551 17.9622 54.5583C17.368 53.5952 17.5337 52.3427 18.3333 51.543C19.0888 50.7876 19.2677 49.6279 18.7729 48.6802L17.9357 47.0743C17.1714 45.6076 15.2032 45.3071 14.0347 46.4757C11.1189 49.3915 10.5269 53.9729 12.7601 57.441C13.6238 58.7818 14.6422 60.052 15.8173 61.2271C16.9925 62.4023 18.2627 63.4206 19.6035 64.2843C23.0672 66.5132 27.6485 65.9212 30.5644 63.0054Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-miterlimit="10"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M56.054 94.1397L53.2101 96.9065C52.717 97.3858 51.8921 97.1737 51.6924 96.5154L49.7973 90.2766M59.234 85.9053C59.234 85.9053 55.6684 89.1224 53.8395 90.7737C53.2927 91.2668 53.3382 92.1344 53.9304 92.571L61.337 98.0427C62.0656 98.5812 63.1068 98.1859 63.2954 97.299L66.9327 80.1568C67.109 79.3277 66.295 78.6349 65.5045 78.9393L44.137 87.1806C43.5076 87.423 43.5379 88.3223 44.181 88.5234L49.796 90.2752"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-miterlimit="10"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M204.208 43.5804C203.803 43.9771 203.301 44.2605 202.752 44.4021C202.203 44.5437 201.627 44.5386 201.08 44.3874C200.534 44.2361 200.037 43.944 199.639 43.5402C199.241 43.1364 198.956 42.6352 198.813 42.0867M216.047 26.7121C216.734 24.2308 216.407 21.5782 215.138 19.3379C213.869 17.0975 211.762 15.453 209.281 14.766C206.8 14.079 204.147 14.4059 201.907 15.6747C199.667 16.9435 198.022 19.0503 197.335 21.5316C194.313 32.4468 188.772 34.2703 188.772 34.2703L216.839 42.041C216.839 42.041 213.025 37.6273 216.047 26.7121Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <rect x="84.0271"
                        y="47.3577"
                        width="72"
                        height="50"
                        rx="6"
                        transform="rotate(-10.0671 84.0271 47.3577)"
                        fill="#17407C" />
                  <rect x="87.7063"
                        y="37.2483"
                        width="76"
                        height="54"
                        rx="8"
                        transform="rotate(-10.0671 87.7063 37.2483)"
                        fill="#2967C2" />
                  <rect x="92.3844"
                        y="28.1392"
                        width="78"
                        height="56"
                        rx="8"
                        transform="rotate(-10.0671 92.3844 28.1392)"
                        fill="#2D99FF" />
                  <path d="M134.192 47.2855C133.718 46.631 133.891 45.723 134.579 45.2574L161.503 29.2442C162.191 28.7787 163.134 28.9319 163.608 29.5864C164.083 30.2409 163.91 31.1489 163.222 31.6145L136.297 47.6277C135.609 48.0932 134.667 47.94 134.192 47.2855Z"
                        fill="#6EB9FF" />
                  <path d="M136.526 46.874C136.748 46.0967 136.275 45.3026 135.469 45.1005L104.865 40.2465C104.059 40.0443 103.226 40.5106 103.004 41.288C102.782 42.0653 103.255 42.8594 104.061 43.0615L134.665 47.9155C135.471 48.1176 136.304 47.6514 136.526 46.874Z"
                        fill="#6EB9FF" />
                  <rect x="148.074"
                        y="55.844"
                        width="20"
                        height="3"
                        rx="1.5"
                        transform="rotate(-10 148.074 55.844)"
                        fill="#2876D1" />
                  <rect x="149.116"
                        y="61.7527"
                        width="16"
                        height="3"
                        rx="1.5"
                        transform="rotate(-10 149.116 61.7527)"
                        fill="#2876D1" />
                  <path d="M119.335 74.6197L110.533 76.1717C109.191 76.2378 106.386 75.6876 105.905 72.958L105.205 68.9878M126.29 73.3933L124.321 73.7406"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
            </svg>

            <div data-bind="html: texts.mailings"></div>
            <!-- /ko -->

            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'widget', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <img src="/img/quick-help/widget.png"
                 alt=""
                 width="253"
                 class="b-quick-help__icon">
            <div data-bind="html: texts.widget"></div>
            <!-- /ko -->

            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'requests', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <img src="/img/quick-help/requests.png"
                 alt=""
                 width="253"
                 class="b-quick-help__icon">
            <div data-bind="html: texts.requests"></div>
            <!-- /ko -->

            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'payment', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->

            <svg class="b-quick-help__icon"
                 width="259"
                 height="111"
                 viewBox="0 0 259 111"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M250 111C248.805 99.0376 245.787 87.2908 241.025 76.1477C234.714 61.3783 225.464 47.9586 213.802 36.6546C202.14 25.3506 188.296 16.3838 173.059 10.2661C157.823 4.14843 141.492 0.99971 125 0.99971C108.508 0.99971 92.1777 4.14844 76.9411 10.2661C61.7044 16.3838 47.8601 25.3506 36.1985 36.6546C24.5369 47.9586 15.2864 61.3784 8.97513 76.1477C4.21347 87.2909 1.1953 99.0376 0.000274658 111H250Z"
                        fill="#EFF2FA" />
                  <path d="M123.316 21.944C124.531 20.0992 127.012 19.5889 128.857 20.8042L160.51 37.3699C160.51 38.7761 160.548 42.5904 160.51 43.93C160.489 44.6475 160.275 45.3683 159.851 46.0117L123.543 101.128C122.328 102.973 119.847 103.484 118.002 102.268L88.1476 82.6015C87.0329 81.8672 86.4054 80.6709 86.3515 79.432C86.3161 78.6205 86.3515 74.9636 86.3514 73.2761L123.316 21.944Z"
                        fill="#2967C2" />
                  <rect x="125.517"
                        y="12.4482"
                        width="43.75"
                        height="74.0011"
                        rx="4"
                        transform="rotate(33.3751 125.517 12.4482)"
                        fill="#2D99FF" />
                  <path d="M123.717 88.4011L120.955 92.5406C120.651 93.0018 120.031 93.1294 119.57 92.8256L109.344 86.0895M105.279 83.357L103.345 82.0897"
                        stroke="#B7DCFF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <rect x="120.364"
                        y="30.9657"
                        width="32.3003"
                        height="19.7929"
                        rx="2"
                        transform="rotate(33.3751 120.364 30.9657)"
                        fill="#6EB9FF" />
                  <path d="M105.839 54.6024C106.447 53.6801 107.687 53.425 108.61 54.0327L111.95 56.2335C112.872 56.8412 113.127 58.0816 112.519 59.004C111.912 59.9263 110.671 60.1814 109.749 59.5737L106.409 57.3729C105.486 56.7652 105.231 55.5248 105.839 54.6024Z"
                        fill="#2967C2" />
                  <path d="M115.025 60.6545C115.632 59.7322 116.873 59.4771 117.795 60.0848L121.135 62.2856C122.057 62.8933 122.313 64.1337 121.705 65.056C121.097 65.9784 119.857 66.2335 118.934 65.6257L115.594 63.425C114.672 62.8173 114.417 61.5769 115.025 60.6545Z"
                        fill="#2967C2" />
                  <path d="M124.21 66.7066C124.818 65.7842 126.058 65.5292 126.98 66.1369L130.321 68.3377C131.243 68.9454 131.498 70.1858 130.89 71.1081C130.283 72.0305 129.042 72.2855 128.12 71.6778L124.78 69.4771C123.857 68.8693 123.602 67.629 124.21 66.7066Z"
                        fill="#2967C2" />
                  <path d="M101.445 61.271C102.053 60.3487 103.293 60.0936 104.216 60.7013L107.556 62.9021C108.478 63.5098 108.733 64.7502 108.126 65.6725C107.518 66.5949 106.278 66.8499 105.355 66.2422L102.015 64.0415C101.093 63.4337 100.838 62.1934 101.445 61.271Z"
                        fill="#2967C2" />
                  <path d="M110.631 67.3231C111.238 66.4007 112.479 66.1457 113.401 66.7534L116.741 68.9541C117.664 69.5619 117.919 70.8022 117.311 71.7246C116.703 72.647 115.463 72.902 114.541 72.2943L111.2 70.0936C110.278 69.4858 110.023 68.2455 110.631 67.3231Z"
                        fill="#2967C2" />
                  <path d="M119.816 73.3752C120.424 72.4528 121.664 72.1978 122.587 72.8055L125.927 75.0062C126.849 75.614 127.104 76.8543 126.496 77.7767C125.889 78.699 124.648 78.9541 123.726 78.3464L120.386 76.1456C119.464 75.5379 119.208 74.2975 119.816 73.3752Z"
                        fill="#2967C2" />
                  <path d="M97.0438 67.9513C97.6515 67.029 98.8919 66.7739 99.8142 67.3816L103.154 69.5824C104.077 70.1901 104.332 71.4305 103.724 72.3528C103.116 73.2752 101.876 73.5303 100.954 72.9225L97.6135 70.7218C96.6911 70.1141 96.4361 68.8737 97.0438 67.9513Z"
                        fill="#2967C2" />
                  <path d="M106.229 74.0034C106.837 73.0811 108.077 72.826 109 73.4337L112.34 75.6345C113.262 76.2422 113.517 77.4826 112.91 78.4049C112.302 79.3273 111.061 79.5823 110.139 78.9746L106.799 76.7739C105.877 76.1661 105.622 74.9258 106.229 74.0034Z"
                        fill="#2967C2" />
                  <path d="M115.415 80.0555C116.022 79.1331 117.263 78.8781 118.185 79.4858L121.525 81.6866C122.448 82.2943 122.703 83.5347 122.095 84.457C121.487 85.3794 120.247 85.6344 119.325 85.0267L115.984 82.826C115.062 82.2182 114.807 80.9779 115.415 80.0555Z"
                        fill="#2967C2" />
                  <path d="M105.839 52.8871C106.447 51.9647 107.687 51.7097 108.61 52.3174L111.95 54.5182C112.872 55.1259 113.127 56.3663 112.519 57.2886C111.912 58.211 110.671 58.466 109.749 57.8583L106.409 55.6576C105.486 55.0498 105.231 53.8095 105.839 52.8871Z"
                        fill="#6EB9FF" />
                  <path d="M115.025 58.9392C115.632 58.0168 116.873 57.7618 117.795 58.3695L121.135 60.5702C122.057 61.178 122.313 62.4183 121.705 63.3407C121.097 64.2631 119.857 64.5181 118.934 63.9104L115.594 61.7096C114.672 61.1019 114.417 59.8615 115.025 58.9392Z"
                        fill="#6EB9FF" />
                  <path d="M124.21 64.9913C124.818 64.0689 126.058 63.8138 126.98 64.4216L130.321 66.6223C131.243 67.23 131.498 68.4704 130.89 69.3928C130.283 70.3151 129.042 70.5702 128.12 69.9625L124.78 67.7617C123.857 67.154 123.602 65.9136 124.21 64.9913Z"
                        fill="#6EB9FF" />
                  <path d="M101.445 59.5557C102.053 58.6333 103.293 58.3783 104.216 58.986L107.556 61.1867C108.478 61.7945 108.733 63.0348 108.126 63.9572C107.518 64.8796 106.278 65.1346 105.355 64.5269L102.015 62.3261C101.093 61.7184 100.838 60.478 101.445 59.5557Z"
                        fill="#6EB9FF" />
                  <path d="M110.631 65.6078C111.238 64.6854 112.479 64.4303 113.401 65.0381L116.741 67.2388C117.664 67.8465 117.919 69.0869 117.311 70.0093C116.703 70.9316 115.463 71.1867 114.541 70.579L111.2 68.3782C110.278 67.7705 110.023 66.5301 110.631 65.6078Z"
                        fill="#6EB9FF" />
                  <path d="M119.816 71.6598C120.424 70.7375 121.664 70.4824 122.587 71.0901L125.927 73.2909C126.849 73.8986 127.104 75.139 126.496 76.0614C125.889 76.9837 124.648 77.2388 123.726 76.6311L120.386 74.4303C119.464 73.8226 119.208 72.5822 119.816 71.6598Z"
                        fill="#6EB9FF" />
                  <path d="M97.0438 66.236C97.6515 65.3136 98.8919 65.0586 99.8142 65.6663L103.154 67.8671C104.077 68.4748 104.332 69.7152 103.724 70.6375C103.116 71.5599 101.876 71.8149 100.954 71.2072L97.6135 69.0065C96.6911 68.3987 96.4361 67.1584 97.0438 66.236Z"
                        fill="#6EB9FF" />
                  <path d="M106.229 72.2881C106.837 71.3657 108.077 71.1107 109 71.7184L112.34 73.9191C113.262 74.5269 113.517 75.7672 112.91 76.6896C112.302 77.612 111.061 77.867 110.139 77.2593L106.799 75.0585C105.877 74.4508 105.622 73.2104 106.229 72.2881Z"
                        fill="#6EB9FF" />
                  <path d="M115.415 78.3402C116.022 77.4178 117.263 77.1627 118.185 77.7705L121.525 79.9712C122.448 80.5789 122.703 81.8193 122.095 82.7417C121.487 83.664 120.247 83.9191 119.325 83.3114L115.984 81.1106C115.062 80.5029 114.807 79.2625 115.415 78.3402Z"
                        fill="#6EB9FF" />
                  <rect x="128.169"
                        y="20.4501"
                        width="30.2103"
                        height="7.90854"
                        rx="2"
                        transform="rotate(33.3751 128.169 20.4501)"
                        fill="#2967C2" />
                  <path d="M154.241 30.6254L148.313 39.6872L127.704 26.1876C127.704 26.1876 131.485 20.6003 133.632 17.1257C135.779 13.6512 140.592 13.3386 140.592 13.3386L161.763 25.8384C161.763 25.8384 156.773 26.5172 154.241 30.6254Z"
                        fill="#C1DFFB" />
                  <rect x="28.6047"
                        y="9.2644"
                        width="58.9309"
                        height="6.54788"
                        transform="rotate(22.8733 28.6047 9.2644)"
                        stroke="#C1DFFB"
                        stroke-width="3" />
                  <rect x="29.1232"
                        y="24.881"
                        width="19.6436"
                        height="6.54788"
                        rx="3"
                        transform="rotate(22.8733 29.1232 24.881)"
                        stroke="#C1DFFB"
                        stroke-width="3" />
                  <rect x="31.6693"
                        y="2"
                        width="58.9309"
                        height="35.1672"
                        rx="3"
                        transform="rotate(22.8733 31.6693 2)"
                        stroke="#6EB9FF"
                        stroke-width="3" />
                  <path d="M243.201 49.7656C206.337 50.9009 212.226 67.9178 177.394 70.5096L185.845 97.3225C220.678 94.7307 214.789 77.7137 251.653 76.5785L243.201 49.7656Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M248.766 67.419C250.97 67.1915 253.358 67.0324 255.959 66.9523L247.507 40.1395C210.643 41.2747 216.531 58.2917 181.699 60.8834L184.521 69.8357"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <ellipse cx="214.206"
                           cy="72.5362"
                           rx="6.5"
                           ry="8"
                           transform="rotate(-17.4959 214.206 72.5362)"
                           stroke="#6EB9FF"
                           stroke-width="3" />
                  <circle cx="189.284"
                          cy="78.295"
                          r="3"
                          transform="rotate(-17.4959 189.284 78.295)"
                          stroke="#C1DFFB"
                          stroke-width="3" />
                  <path d="M190.031 90.6418C191.403 90.5588 194.718 90.2126 197.007 89.491"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M243.492 70.6442C241.757 70.8417 237.551 71.4685 234.608 72.3962"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M241.989 65.8755C239.708 66.2448 234.167 67.2925 230.243 68.5294"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <circle r="12.5"
                          transform="matrix(-1 0 0 1 52.5 84.5)"
                          stroke="#C1DFFB"
                          stroke-width="3" />
                  <path d="M39.6355 83.4676C37.8027 84.1085 35.8175 84.1723 33.9473 83.6506C32.077 83.1289 30.4116 82.0466 29.1752 80.5495C27.9387 79.0525 27.1907 77.2125 27.0319 75.2773C26.873 73.3422 27.3109 71.4049 28.2866 69.7262C29.2623 68.0475 30.7289 66.7081 32.489 65.8883C34.2491 65.0685 36.2181 64.8077 38.1309 65.141C40.0437 65.4744 41.8085 66.3858 43.1875 67.7526C44.5666 69.1195 45.4937 70.876 45.8441 72.7858"
                        stroke="#C1DFFB"
                        stroke-width="3" />
                  <path d="M48.8516 84.4375L49.5469 78.625H56.1953V80.6719H51.6875L51.4297 82.9297C51.6172 82.8203 51.862 82.724 52.1641 82.6406C52.4714 82.5573 52.7708 82.5156 53.0625 82.5156C54.1927 82.5156 55.0599 82.8516 55.6641 83.5234C56.2734 84.1901 56.5781 85.1276 56.5781 86.3359C56.5781 87.0651 56.4141 87.7266 56.0859 88.3203C55.763 88.9089 55.3073 89.362 54.7188 89.6797C54.1302 89.9974 53.4349 90.1562 52.6328 90.1562C51.9193 90.1562 51.25 90.0104 50.625 89.7188C50 89.4219 49.5104 89.0182 49.1562 88.5078C48.8021 87.9922 48.6276 87.4115 48.6328 86.7656H51.2734C51.2995 87.1823 51.4323 87.513 51.6719 87.7578C51.9115 88.0026 52.2266 88.125 52.6172 88.125C53.5026 88.125 53.9453 87.4688 53.9453 86.1562C53.9453 84.9427 53.4036 84.3359 52.3203 84.3359C51.7057 84.3359 51.2474 84.5339 50.9453 84.9297L48.8516 84.4375Z"
                        fill="#6EB9FF" />
                  <path d="M37.8984 79H35.918V72.6953L33.9727 73.2637V71.7637L37.7168 70.4688H37.8984V79Z"
                        fill="#6EB9FF" />
                  <circle r="14"
                          transform="matrix(-1 0 0 1 197 22)"
                          stroke="#6EB9FF"
                          stroke-width="3" />
                  <path d="M194.531 28H191.891V19.5938L189.297 20.3516V18.3516L194.289 16.625H194.531V28ZM204.259 23.3516C204.259 24.888 203.913 26.0729 203.22 26.9062C202.533 27.7396 201.561 28.1562 200.306 28.1562C199.041 28.1562 198.061 27.737 197.369 26.8984C196.676 26.0599 196.33 24.8776 196.33 23.3516V21.2734C196.33 19.737 196.673 18.5521 197.361 17.7188C198.054 16.8854 199.03 16.4688 200.291 16.4688C201.551 16.4688 202.528 16.888 203.22 17.7266C203.913 18.5651 204.259 19.75 204.259 21.2812V23.3516ZM201.627 20.9375C201.627 20.1198 201.52 19.5104 201.306 19.1094C201.093 18.7031 200.754 18.5 200.291 18.5C199.838 18.5 199.507 18.6875 199.298 19.0625C199.095 19.4375 198.986 20.0052 198.97 20.7656V23.6719C198.97 24.5208 199.077 25.1432 199.291 25.5391C199.504 25.9297 199.843 26.125 200.306 26.125C200.754 26.125 201.085 25.9349 201.298 25.5547C201.512 25.1693 201.621 24.5677 201.627 23.75V20.9375Z"
                        fill="#C1DFFB" />
            </svg>


            <div data-bind="html: texts.payment"></div>

            <!-- /ko -->

            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'help', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg class="b-quick-help__icon"
                 width="250"
                 height="112"
                 viewBox="0 0 250 112"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M250 112C248.805 100.038 245.787 88.2911 241.025 77.148C234.714 62.3786 225.464 48.9589 213.802 37.6549C202.14 26.3509 188.296 17.3841 173.059 11.2664C157.823 5.14872 141.492 2 125 2C108.508 2 92.1777 5.14873 76.941 11.2664C61.7044 17.3841 47.86 26.3509 36.1984 37.6549C24.5368 48.9589 15.2863 62.3786 8.97509 77.148C4.21342 88.2911 1.19525 100.038 0.000228882 112H250Z"
                        fill="#EFF2FA" />
                  <path d="M218.069 91.6329L213.202 86.7371C212.988 86.5271 212.681 86.3849 212.346 86.3419C212.012 86.2988 211.678 86.3585 211.419 86.5077L206.853 89.217L201.008 81.9659C200.795 81.7559 200.487 81.6137 200.153 81.5706C199.818 81.5276 199.485 81.5872 199.225 81.7364L193.117 86.4768"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M209.701 82.0173C211.441 82.2412 213.032 81.0125 213.256 79.2729C213.48 77.5333 212.251 75.9416 210.512 75.7177C208.772 75.4938 207.18 76.7225 206.957 78.4621C206.733 80.2017 207.961 81.7934 209.701 82.0173Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M196.26 95.23L213.059 97.392C215.378 97.6905 217.501 96.0522 217.799 93.7328L219.961 76.9339C220.26 74.6144 218.621 72.4921 216.302 72.1936L199.503 70.0316C197.184 69.7331 195.061 71.3714 194.763 73.6908L192.601 90.4897C192.302 92.8092 193.941 94.9314 196.26 95.23Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M217.433 44.4068C216.715 46.4961 214.44 47.6082 212.35 46.8907L208.567 45.5915L213.114 32.3505L216.898 33.6497C218.987 34.3673 220.099 36.6427 219.382 38.7321L217.433 44.4068Z"
                        stroke="#6EB9FF"
                        stroke-width="3" />
                  <path d="M173.602 30.4118L177.662 18.5895C179.299 13.8236 181.476 12.0031 186.003 9.79046C190.531 7.57778 196.063 7.99144 200.829 9.62815C205.594 11.2648 210.273 14.3575 212.485 18.8848C214.698 23.4122 215.063 26.6758 213.602 30.9319M181.863 51.0055C175.183 48.7116 169.893 41.2117 172.532 33.5272L175.316 25.4201"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M168.309 21.1929C169.027 19.1035 171.302 17.9914 173.392 18.709L177.175 20.0082L172.628 33.2491L168.844 31.9499C166.755 31.2324 165.643 28.957 166.36 26.8676L168.309 21.1929Z"
                        stroke="#6EB9FF"
                        stroke-width="3" />
                  <path d="M181.13 54.6723C180.086 54.3136 179.53 53.1758 179.888 52.1312L180.863 49.2938C181.221 48.2491 182.359 47.6931 183.404 48.0518L190.97 50.6502C192.015 51.009 192.571 52.1467 192.212 53.1914L191.238 56.0287C190.879 57.0734 189.741 57.6295 188.697 57.2707L181.13 54.6723Z"
                        stroke="#6EB9FF"
                        stroke-width="3" />
                  <path d="M50.6165 66.5111L53.3838 76.168M57.078 89.0591L59.8454 98.716M71.3334 77.9991L61.6765 80.7665M48.7854 84.4606L39.1285 87.228"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M47.3955 96.4149C55.2201 100.754 65.0806 97.9281 69.4196 90.1035C73.7585 82.2789 70.9328 72.4184 63.1082 68.0795C55.2836 63.7405 45.4231 66.5662 41.0841 74.3908C36.7452 82.2155 39.5709 92.076 47.3955 96.4149Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M51.931 88.2358C55.2385 90.0699 59.4065 88.8755 61.2405 85.568C63.0746 82.2606 61.8802 78.0926 58.5727 76.2585C55.2653 74.4245 51.0973 75.6189 49.2632 78.9263C47.4291 82.2338 48.6236 86.4018 51.931 88.2358Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M80.5563 37.4956C79.9978 35.4113 81.1933 33.2597 83.2581 32.6328L137.971 16.0241C140.074 15.3855 142.299 16.5643 142.952 18.6635L162.723 82.2164C163.388 84.3558 162.167 86.6245 160.015 87.2471L102.563 103.866C100.411 104.489 98.1679 103.223 97.5881 101.059L80.5563 37.4956Z"
                        fill="#2967C2" />
                  <path d="M159.125 84.5625V75.2523L101.6 92.39C101.6 92.39 98.1679 92.88 99.6386 98.7601C100.391 101.767 101.6 101.21 101.6 101.21L159.125 84.5625Z"
                        fill="#C1DFFB" />
                  <path d="M79.2722 33.5435C78.76 30.4703 79.2723 28.9337 82.3454 27.9093L139.077 12.1406C141.263 11.5549 143.51 12.8521 144.095 15.038L162.113 72.3163C162.698 74.5022 161.401 76.749 159.215 77.3347L99.7831 94.6518C97.5972 95.2375 96.5255 97.1532 97.2235 99.7142L79.2722 33.5435Z"
                        fill="#2D99FF" />
                  <rect x="98.0879"
                        y="90.5813"
                        width="48.5858"
                        height="3.0262"
                        rx="1.5131"
                        transform="rotate(-105 98.0879 90.5813)"
                        fill="#2876D1" />
                  <rect x="123.878"
                        y="64.7836"
                        width="13.6508"
                        height="3"
                        rx="1.5"
                        transform="rotate(-106.001 123.878 64.7836)"
                        fill="#6EB9FF" />
                  <rect x="118.082"
                        y="49.1683"
                        width="5"
                        height="5"
                        rx="2.5"
                        transform="rotate(-106.001 118.082 49.1683)"
                        fill="#6EB9FF" />
                  <circle cx="122.066"
                          cy="53.4344"
                          r="16.4344"
                          stroke="#6EB9FF"
                          stroke-width="3" />
                  <rect x="148.051"
                        y="47.4613"
                        width="20.9205"
                        height="3.0262"
                        rx="1.5131"
                        transform="rotate(-107.001 148.051 47.4613)"
                        fill="#B7DCFF" />
                  <rect x="140.961"
                        y="25.2182"
                        width="7.37627"
                        height="3.0262"
                        rx="1.5131"
                        transform="rotate(-107.001 140.961 25.2182)"
                        fill="#B7DCFF" />
                  <line x1="22.8893"
                        y1="27.4937"
                        x2="52.0198"
                        y2="16.891"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="27.6776"
                        y1="40.6493"
                        x2="56.8081"
                        y2="30.0467"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="24.5994"
                        y1="32.1921"
                        x2="42.4536"
                        y2="25.6937"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="29.3877"
                        y1="45.3478"
                        x2="47.2419"
                        y2="38.8494"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="32.4659"
                        y1="53.805"
                        x2="61.5963"
                        y2="43.2024"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <line x1="34.176"
                        y1="58.5035"
                        x2="52.0301"
                        y2="52.0051"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M12.1006 32.9377L14.0901 29.6321L16.4842 36.21"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M17.5227 44.9108C16.8173 42.9727 20.0807 41.6956 20.9407 44.0584C21.5048 45.6081 20.3741 47.6285 19.3931 50.0497L23.1519 48.6817"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                  <path d="M23.6897 61.8546C24.3845 63.7634 27.6873 62.5945 26.8309 60.2417C26.273 58.7087 25.3521 58.6556 24.0118 58.4451L24.665 55.5801L21.7872 56.6276"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
            </svg>


            <div data-bind="html: texts.help"></div>
            <!-- /ko -->

            <!-- ko template: {
        foreach: templateIf($modal.activeStepId() === 'create', $data),
        afterAdd: fadeAfterAddFactory(200, 0),
        beforeRemove: fadeBeforeRemoveFactory(0)
      } -->
            <svg width="251"
                 class="b-quick-help__icon"
                 height="111"
                 viewBox="0 0 251 111"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                  <path d="M250.237 110.294C249.042 98.3 246.024 86.5219 241.262 75.3489C234.951 60.5401 225.7 47.0844 214.039 35.7502C202.377 24.416 188.533 15.4252 173.296 9.29117C158.059 3.15714 141.729 0 125.237 0C108.745 0 92.4143 3.15715 77.1776 9.29118C61.941 15.4252 48.0966 24.416 36.435 35.7502C24.7734 47.0844 15.5229 60.5401 9.21167 75.349C4.45001 86.5219 1.43184 98.3 0.236816 110.294H250.237Z"
                        fill="#EFF2FA" />
                  <path d="M90.7411 39.0285C90.1387 36.9277 91.3534 34.7363 93.4542 34.1339L130.225 23.5901L146.551 32.6735L159.992 79.5479C160.595 81.6487 159.38 83.8401 157.279 84.4425L111.633 97.5313C109.532 98.1337 107.341 96.919 106.739 94.8182L90.7411 39.0285Z"
                        fill="#17407C" />
                  <path d="M92.7116 33.6441C92.0945 31.492 93.3388 29.2472 95.4909 28.6301L133.158 17.8292L149.883 27.1341L163.652 75.1518C164.269 77.3038 163.025 79.5487 160.873 80.1657L114.113 93.5738C111.961 94.1909 109.716 92.9466 109.099 90.7945L92.7116 33.6441Z"
                        fill="#2967C2" />
                  <path d="M95.8634 29.929C95.2463 27.7769 96.4907 25.5321 98.6427 24.915L136.31 14.1141L153.035 23.4189L166.804 71.4367C167.421 73.5887 166.177 75.8335 164.024 76.4506L117.265 89.8587C115.113 90.4758 112.868 89.2314 112.251 87.0794L95.8634 29.929Z"
                        fill="#2D99FF" />
                  <path d="M152.983 23.39L143.932 25.9855C141.78 26.6026 139.535 25.3583 138.918 23.2062L136.311 14.1141L152.983 23.39Z"
                        fill="#C1DFFB" />
                  <rect x="113.333"
                        y="41.8316"
                        width="30.4024"
                        height="2.70243"
                        rx="1.35122"
                        transform="rotate(-16 113.333 41.8316)"
                        fill="#2876D1" />
                  <rect x="120.968"
                        y="68.4585"
                        width="30.4024"
                        height="2.70243"
                        rx="1.35122"
                        transform="rotate(-16 120.968 68.4585)"
                        fill="#2876D1" />
                  <rect x="114.517"
                        y="83.7853"
                        width="15.6799"
                        height="2.70243"
                        rx="1.35122"
                        transform="rotate(-106.001 114.517 83.7853)"
                        fill="#AFD8FF" />
                  <rect x="109.36"
                        y="66.7005"
                        width="6.58711"
                        height="2.70243"
                        rx="1.35122"
                        transform="rotate(-106.001 109.36 66.7005)"
                        fill="#AFD8FF" />
                  <rect x="110.714"
                        y="32.6989"
                        width="16.8902"
                        height="4.05365"
                        rx="2.02683"
                        transform="rotate(-16 110.714 32.6989)"
                        fill="#6EB9FF" />
                  <rect x="118.35"
                        y="59.3258"
                        width="16.8902"
                        height="4.05365"
                        rx="2.02683"
                        transform="rotate(-16 118.35 59.3258)"
                        fill="#6EB9FF" />
                  <rect x="115.009"
                        y="47.6765"
                        width="30.4024"
                        height="2.70243"
                        rx="1.35122"
                        transform="rotate(-16 115.009 47.6765)"
                        fill="#2876D1" />
                  <rect x="122.645"
                        y="74.3034"
                        width="30.4024"
                        height="2.70243"
                        rx="1.35122"
                        transform="rotate(-16 122.645 74.3034)"
                        fill="#2876D1" />
                  <path d="M60.5366 96.7022L92.4522 86.1242"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M65.4829 85.5595L89.6142 77.5614"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M50.1899 81.1248L86.776 68.9987"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M23.9249 30.8131C23.6123 29.5428 24.3887 28.2595 25.6591 27.9468L45.1897 23.1406L58.145 31.0837L64.4304 56.625C64.743 57.8954 63.9666 59.1787 62.6962 59.4913L35.0934 66.284C33.8231 66.5967 32.5398 65.8202 32.2272 64.5499L23.9249 30.8131Z"
                        stroke="#6EB9FF"
                        stroke-width="3" />
                  <path d="M57.7447 30.7368L54.1171 31.6295C49.0841 32.8681 47.3617 31.9676 46.0871 26.7883L45.1895 23.1406L57.7447 30.7368Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M37.609 45.3365L44.0158 49.2128L50.2678 38.8792"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M193.226 16.5623C193.553 15.5564 194.633 15.006 195.639 15.3328L211.102 20.3572L216.615 31.3354L210.044 51.5578C209.717 52.5636 208.637 53.1141 207.631 52.7873L185.777 45.6863C184.771 45.3595 184.22 44.2792 184.547 43.2734L193.226 16.5623Z"
                        stroke="#C1DFFB"
                        stroke-width="3" />
                  <path d="M216.488 30.9267L213.616 29.9935C209.631 28.6987 208.832 27.3459 210.164 23.2452L211.103 20.3572L216.488 30.9267Z"
                        stroke="#C1DFFB"
                        stroke-width="3"
                        stroke-linejoin="round" />
                  <path d="M196.409 35.0336L204.801 37.7602M199.242 40.5927L201.968 32.201"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linecap="round" />
                  <path d="M194.799 76.0141L193.715 76.264L190.931 77.5468L186.776 68.5302C186.129 67.3419 184.714 67.1228 183.537 67.6654C182.242 68.2623 181.692 69.8002 182.1 70.6849L189.539 86.8285L181.469 86.7569L181.014 86.9667C180.434 87.1956 179.939 87.6924 179.745 88.2623L179.316 90.0158L194.255 97.6849C195.029 98.0007 195.867 97.9603 196.637 97.6057L207.443 92.6267C208.118 92.3155 208.652 91.7811 208.92 91.0814C209.266 90.3844 209.272 89.6131 209.052 88.8882L205.997 78.5389L205.807 78.1268C205.361 76.9108 204.135 76.1503 202.868 76.2924L194.799 76.0141Z"
                        stroke="#6EB9FF"
                        stroke-width="3"
                        stroke-linejoin="round" />
            </svg>

            <div data-bind="html: texts.create"></div>
            <!-- /ko -->
      </div><!-- .b-quick-help__text -->

      <div class="b-quick-help__nav">
            <div class="pagination">
                  <!-- ko foreach: $modal.steps -->
                  <div class="item"
                       data-bind="css: {
                'passed': $index() <= $modal.activeStep()
            }, click: (_, event)=>{

                $modal.changeStep($index());
            }"></div>
                  <!-- /ko -->
            </div>


            <div class="d-flex align-items-center mt-4">
                  <!-- ko ifnot: $modal.isLastStep -->
                  <button class="f-btn f-btn-link"
                          data-bind="text: _t('Завершить'), click: ()=>{
                              $modal.onHide();
                              }">
                  </button>

                  <button class="f-btn f-btn-primary ml-auto"
                          data-bind="text: _t('Дальше'), click: (_, e)=>{
                                e.stopPropagation();
                                e.preventDefault();
                              $modal.nextStep();
                        }">
                  </button>
                  <!-- /ko -->

                  <!-- ko if: $modal.isLastStep -->
                  <button class="f-btn f-btn-primary ml-auto"
                          data-bind="text: _t('Завершить'), click: ()=>{
                  $modal.onHide();
              }">
                  </button>
                  <!-- /ko -->



            </div>

      </div><!-- .b-quick-help__nav -->


</div><!-- .b-quick-help__body -->
<!-- /ko -->
