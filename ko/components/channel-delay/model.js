import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.model = params.model;

    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher || commonFormControlErrorStateMatcher();
    this.formControlSuccessStateMatcher = params.formControlSuccessStateMatcher || commonFormControlSuccessStateMatcher();

    // this.text = ko.pureComputed(() => {
    //   let params = this.model.params();
    //   let hours = params.hours ? `${params.hours}ч.` : '';
    //   let minutes = params.minutes ? `${params.minutes}мин.` : '';
    //   return [hours, minutes].join(' ').trim();
    // });
  }
}
