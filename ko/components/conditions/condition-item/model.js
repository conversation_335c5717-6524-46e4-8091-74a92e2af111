import { conditionTypes } from "../../../data/condition-types";

import "../types/condition-criterion";
import "../types/condition-complaint";
import "../types/condition-interval";
import "../types/condition-days";
import "../types/condition-time";
import "../types/condition-min-price";
import "../types/condition-dishes";
import "../types/condition-polls-participation";
import "../types/condition-order-type";
import "../types/condition-source-type";
import "../types/condition-filial-order-type";
import "../types/condition-filials";
import "../types/condition-contact";

export class ViewModel {
  constructor(params, element) {
    this.controller = params.condition;
    this.condition = this.controller.condition;

    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher = params.formControlSuccessStateMatcher;

    this.index = params.index;
    this.hasOrder = params.hasOrder;

    $(element).addClass(["condition"]);

    this.conditionTypes = conditionTypes;

    console.log({ conditionTypes })

    this.isIntervalCondition = ko.pureComputed(() => {
      return intervalConditions.includes(this.condition.type());
    });

    this.canRemove = params.canRemove;
  }
}
