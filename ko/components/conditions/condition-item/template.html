<div class="condition__row">
  <div class="condition__type">
    <div class="form-group">

      <label class="form-label">Условие</label>
      <button class="btn-question"
              data-bind="tooltip, tooltipPlacement: 'top'"
              type="button"
              title="Условие">
      </button>

      <div class="select2-wrapper"
           data-bind="css: {
              'is-invalid': $component.formControlErrorStateMatcher(controller.type),
              'is-valid': $component.formControlSuccessStateMatcher(controller.type),
            }">
        <select data-bind="value: controller.type,
            valueAllowUnset: true,
            lazySelect2: {
              containerCssClass: 'form-control',
              wrapperCssClass: 'select2-container--form-control',
              minimumResultsForSearch: 0,
              allowClear: true,
              placeholder: 'Не выбрано'
            }">
          <option></option>
          <!-- ko foreach: $component.conditionTypes -->
          <option data-bind="value: $data.id, text: $data.text"></option>
          <!-- /ko -->
        </select>


      </div>

      <validation-feedback params="text: controller.type.error, show: controller.type"></validation-feedback>

    </div>

  </div>

  <div class="condition__value overflow-hidden">
    <!-- ko template: {
      foreach: templateIf(condition.type() == 'last-order-date', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-criterion params="title: 'Критерий', tooltip: 'Критерий', condition: condition"></condition-criterion>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(condition.type() == 'complaint-in-the-order', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-criterion params="title: 'Заказ сделан', tooltip: 'Заказ сделан', condition: condition">
    </condition-criterion>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf([
        'avg-year-count-orders',
        'avg-month-count-orders',
        ].includes(condition.type()), $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-interval params="title: 'Количество', tooltip: 'Количество', condition: condition"></condition-interval>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf([
        'order-amount-from-client-by-year',
        'order-amount-from-client-by-month',
        'avg-check-customer-orders'
        ].includes(condition.type()), $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-interval params="title: 'Выручка, ₽', tooltip: 'Выручка, ₽', condition: condition"></condition-interval>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(condition.type() == 'main-orders-days', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-days params="condition: condition"></condition-days>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(condition.type() == 'favorite-order-time', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-time params="condition: condition"></condition-time>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(condition.type() == 'favorite-dish', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-min-price params="condition: condition"></condition-min-price>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(condition.type() == 'polls-participation', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-polls-participation params="condition: condition"></condition-polls-participation>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(condition.type() == 'order-type', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-order-type params="condition: condition"></condition-order-type>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(condition.type() == 'source-type', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-source-type params="condition: condition"></condition-source-type>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(condition.type() == 'filial', $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    }  -->
    <condition-filial-order-type params="condition: condition, hasOrder: hasOrder"></condition-filial-order-type>
    <!-- /ko -->

  </div>
</div>

<!-- ko template: {
    foreach: templateIf(condition.type()=='filial', $data),
    afterAdd: fadeAfterAddFactory(200, 200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
<div class="condition__row">
  <condition-filials params="condition: condition"></condition-filials>
</div>
<!-- /ko -->

<!-- ko template: {
  foreach: templateIf(type()=='complaint-in-the-order', $data),
  afterAdd: fadeAfterAddFactory(200, 200),
  beforeRemove: fadeBeforeRemoveFactory(200)
} -->
<div class="condition__row">
  <condition-complaint params="condition: condition"></condition-complaint>
</div>
<!-- /ko -->

<!-- ko template: {
  foreach: templateIf(type()=='favorite-dish', $data),
  afterAdd: fadeAfterAddFactory(200, 200),
  beforeRemove: fadeBeforeRemoveFactory(200)
} -->
<div class="condition__row">
  <condition-dishes params="condition: condition"></condition-dishes>
</div>
<!-- /ko -->

<!-- ko template: {
  foreach: templateIf(type()=='contact-data', $data),
  afterAdd: fadeAfterAddFactory(200, 200),
  beforeRemove: fadeBeforeRemoveFactory(200)
} -->
<div class="condition__row">
  <condition-contact params="condition: condition"></condition-contact>
</div>
<!-- /ko -->


<!-- ko if: canRemove -->
<button type="submit"
        class="btn btn-danger btn-remove"
        title="Удалить"
        data-bind="click: $component.removeCondition, tooltip">
</button>
<!-- /ko -->
