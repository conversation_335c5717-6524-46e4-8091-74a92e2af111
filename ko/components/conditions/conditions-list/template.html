<div class="conditions-list__header mb-3 d-flex justify-content-between align-items-center">
  <div>

    <h3 class="conditions-list__title">
      <!-- ko text: title -->
      <!-- /ko -->

      <span class="conditions-list__count"
            data-bind="text: controller.conditions().length"></span>
    </h3>

    <!--  ko if: subtitle -->
    <div class="conditions-list__subtitle service-text"
         data-bind="text: subtitle"></div>
    <!-- /ko -->

  </div>

  <button class="btn btn-success btn-with-icon btn-add"
          data-bind="click: addCondition, disable: !canAddCondition(), text: addButtonName"></button>
</div>

<validation-feedback params="text: error, show: error"></validation-feedback>


<div class="conditions-list__content">

  <!-- ko foreach: {
    data: controller.conditions,
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <condition-item class="conditions-list__item"
                  params="
                    formControlErrorStateMatcher: $parent.formControlErrorStateMatcher,
                    formControlSuccessStateMatcher: $parent.formControlSuccessStateMatcher,
                    condition: $data,
                    hasOrder: $parent.hasOrder,
                    canRemove: $parent.removeAll || $parent.controller.conditions().length > 1">
  </condition-item>
  <!-- /ko -->

  <!-- ko template: {
      foreach: templateIf(conditions().length > 0, $data),
      afterAdd: fadeAfterAddFactory(200, 200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
  <footer class="conditions-list__footer d-flex justify-content-end align-items-center">
    <button class="btn btn-success btn-with-icon btn-add"
            data-bind="click: addCondition, disable: !canAddCondition(), text: addButtonName"></button>
  </footer>
  <!-- /ko -->
</div>
