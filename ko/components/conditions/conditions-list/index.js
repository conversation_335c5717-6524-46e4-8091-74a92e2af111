import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('foquz-conditions-list', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      $element.addClass('conditions-list');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
