export class ViewModel {
  constructor(params, element) {

    this.title = params.title || 'Условия';
    this.subtitle = params.subtitle || '';

    this.addButtonName = params.addButtonName || 'Добавить условие';
    this.removeAll = params.removeAll || false;
    this.hasOrder = params.hasOrder;

    this.controller = params.controller;

    this.error = params.error || ko.observable('');
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher = params.formControlSuccessStateMatcher;

    this.canAddCondition = ko.pureComputed(() => {
      return this.conditions().every((c) => c.type());
    });
  }

  addCondition() {
    this.controller.addCondition();
  }

  removeCondition(condition) {
    this.controller.removeCondition(condition);
  }
}
