export class ViewModel {
  constructor(params, element) {
    this.directory = new Directory('dish-categories');

    this.dishesSelectHtml = document.createElement('div');
    this.dishesSelectHtmlRendered = ko.observable(false);

    directories.dishes.loaded.subscribe((v) => {
      if (v) {
        ko.renderTemplate(
          'dishes-select-template',
          {
            categories: directories.dishes.data,
          },
          {},
          this.dishesSelectHtml,
        );
        this.dishesSelectHtmlRendered(true);
      }
    });

    Object.keys(directories).forEach((key) => directories[key].load());
  }
}
