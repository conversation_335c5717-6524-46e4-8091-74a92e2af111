import '../../../dishes-select';

import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('favorite-dish-condition', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      return new ViewModel(params, element);
    },
  },
  template: html,
});
