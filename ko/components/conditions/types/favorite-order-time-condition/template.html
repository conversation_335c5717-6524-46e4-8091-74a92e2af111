<div class="condition-value condition-value--time">
  <div class="conditions-list__time">
    <div class="conditions-list__time-columns">
      <!--ko foreach: { data: $component.timeList} -->
      <div class="form-group form-group_to_check">
        <div class="form-check">
          <input type="checkbox" class="form-check-input" name="timeCheck" data-bind="attr: {
                             id: 'timeCheck_'+$index(),
                          },
                          value: $data.index,
                          checked: $parent.time">
          <label class="form-check-label" data-bind="text: $data.text,
                         attr: { 'for': 'timeCheck_'+$index() }">
          </label>
        </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- ko template: {
          foreach: $component.formControlErrorStateMatcher($data.time),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
    <div class="form-error" data-bind="text: $parent.time.error()"></div>
    <!-- /ko -->
  </div>
</div>
