<div class="condition-value condition-value--days">
  <div class="conditions-list__days">
    <div class="conditions-list__days-columns">
      <!--ko foreach: { data: $component.daysList } -->
      <div class="form-group form-group_to_check">
        <div class="form-check">
          <input type="checkbox" class="form-check-input" name="daysCheck" data-bind="
                            value: $data.index,
                            checked: $parent.days,
                            attr: { id: 'daysCheck_' + $index() }">
          <label class="form-check-label" data-bind="text: $data.text,
                         attr: { 'for': 'daysCheck_'+$index()}">
          </label>
        </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- ko template: {
          foreach: $component.formControlErrorStateMatcher($data.days),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
    <div class="form-error" data-bind="text: $parent.days.error()"></div>
    <!-- /ko -->
  </div>
</div>
