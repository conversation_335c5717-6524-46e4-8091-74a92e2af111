<div class="condition-value condition-value--orders-per-month">

  <div class="">
    <label class="form-label">Количество</label>

    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Количество">
    </button>

    <div class="d-flex flex-wrap">
      <div class="mb-4" data-bind="component: {
          name: 'number-control',
          params: {
              value: $data.from,
              isInvalid: $component.formControlErrorStateMatcher($data.from),
                              min: 0
          } }">
      </div>
      <div class="mb-4 conditions-list__separator">
        –
      </div>
      <div class="mb-4" data-bind="component: {
              name: 'number-control',
              params: {
                  value: $data.to,
                  isInvalid: $component.formControlErrorStateMatcher($data.to),
                              min: 0
              }
          }">
      </div>
    </div>

    <!-- ko template: {
      foreach: $component.formControlErrorStateMatcher($data.from)() || $component.formControlErrorStateMatcher($data.to)(),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
    <div class="form-error" data-bind="text: $parent.from.error() || $parent.to.error()"></div>
    <!-- /ko -->
  </div>



</div>
