<div class="condition-value condition-value--dishes">
  <div class="">
    <label class="form-label">Блюда/категории</label>

    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Блюда/категории">
    </button>

    <div class="mb-4">
      <div class="" data-bind="descendantsComplete: function(el) { $data.renderDishesSelectBlock(el, $data) }">

        <div class="select2-wrapper  condition-value__select w-100" , data-bind="css: {
                  'is-invalid': $component.formControlErrorStateMatcher($data.dishes)
              }">
          <select multiple data-bind="visible: false" data-placeholder="Не выбрано">
          </select>
        </div>
      </div>
    </div>

    <!-- ko template: {
                      foreach: $component.formControlErrorStateMatcher($data.dishes),
                      afterAdd: fadeAfterAddFactory(200),
                      beforeRemove: fadeBeforeRemoveFactory(200)
                  } -->
    <div class="form-error" data-bind="text: $parent.dishes.error()"></div>
    <!-- /ko -->
  </div>
</div>
