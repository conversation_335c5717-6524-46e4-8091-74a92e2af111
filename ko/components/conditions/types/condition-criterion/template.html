<div class="condition-value condition-value--last-order-date">
  <div class="">
    <label class="form-label">Критерий</label>

    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Критерий">
    </button>

    <div class="d-flex flex-wrap">

      <div class="mb-4 mr-4 condition-value__select">
        <div class="select2-wrapper">
          <select data-bind="value: $data.criterion,
              select2: {
                  containerCssClass: 'form-control',
                  wrapperCssClass: 'select2-container--form-control',
                  allowClear: false,
                  placeholder: 'Не выбрано'
              }">
            <option value="1">Дней прошло</option>
            <option value="2">Период</option>
            <option value="3">До</option>
            <option value="4">После</option>
            <option value="5">Конкретная дата</option>
          </select>
        </div>
      </div>

      <div class="flex-grow-1">
        <!-- Дней прошло -->
        <!-- ko template: {
                  foreach: templateIf($data.criterion() == 1, $data),
                  afterAdd: fadeAfterAddFactory(200)
              } -->
        <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
          <div class="mb-4" data-bind="component: {
                          name: 'number-control',

                          params: {
                              className: 'number-control--small',
                              value: $data.from,
                              isInvalid: $component.formControlErrorStateMatcher($data.from),
                              min: 0
                          }
                      }">
          </div>
          <div class="mb-4 conditions-list__separator">
            –
          </div>
          <div class="mb-4  number-control--small" data-bind="component: {
                          name: 'number-control',
                          params: {
                            className: 'number-control--small',
                              value: $data.to,
                              isInvalid: $component.formControlErrorStateMatcher($data.to),
                              min: 0
                          }
                      }">
          </div>


        </div>
        <!-- ko template: {
                      foreach: $component.formControlErrorStateMatcher($data.from)() || $component.formControlErrorStateMatcher($data.to)(),
                      afterAdd: fadeAfterAddFactory(200),
                      beforeRemove: fadeBeforeRemoveFactory(200)
                  } -->
        <div class="form-error" data-bind="text: $parent.from.error() || $parent.to.error()"></div>
        <!-- /ko -->
        <!-- /ko -->
        <!-- /Дней прошло -->

        <!-- Период -->
        <!-- ko template: {
                  foreach: templateIf($data.criterion() == 2, $data),
                  afterAdd: fadeAfterAddFactory(200),
              } -->
        <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
          <div class="mb-4 condition-value__period date-input-group">
            <input class="form-control" placeholder="00.00.0000-00.00.0000" data-bind="value: $data.period,
                          periodPicker,
                          periodPickerArrowPosition: { anchor: 'right', offset: -10 },
                          periodPickerRanges: $component.periodPickerRanges,
                          periodPickerApply: function() {},
                          periodPickerSeparator: '-',
                          css: {
                              'is-invalid': $component.formControlErrorStateMatcher($data.period)
                          }">
            <i class="date-input-group__icon"></i>
          </div>
        </div>
        <!-- ko template: {
                      foreach: $component.formControlErrorStateMatcher($data.period),
                      afterAdd: fadeAfterAddFactory(200),
                      beforeRemove: fadeBeforeRemoveFactory(200)
                  } -->
        <div class="form-error" data-bind="text: $parent.period.error()"></div>
        <!-- /ko -->
        <!-- /ko -->
        <!-- /Период -->

        <!-- Одна дата (до/после/конкретная дата) -->
        <!-- ko template: {
                  foreach: templateIf($data.criterion() >= 3, $data),
                  afterAdd: fadeAfterAddFactory(200),
              } -->
        <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
          <div class="mb-4 condition-value__date date-input-group">
            <input class="form-control" data-bind="mask, maskPattern: '00.00.0000',
                         textInput:$data.date,
                         periodPicker,
                         periodPickerArrowPosition: { anchor: 'right', offset: -27 },
                         periodPickerSeparator: '-',
                         periodPickerOpens: 'left',
                         periodPickerDrops: 'up',
                         periodPickerSingle: true,
                         css: {
                              'is-invalid': $component.formControlErrorStateMatcher($data.date)
                          }" placeholder="00.00.0000">
            <i class="date-input-group__icon"></i>
          </div>


        </div>

        <!-- ko template: {
                      foreach: $component.formControlErrorStateMatcher($data.date),
                      afterAdd: fadeAfterAddFactory(200),
                      beforeRemove: fadeBeforeRemoveFactory(200)
                  } -->
        <div class="form-error" data-bind="text: $parent.date.error()"></div>
        <!-- /ko -->
        <!-- /ko -->
      </div>

    </div>
  </div>
</div>
