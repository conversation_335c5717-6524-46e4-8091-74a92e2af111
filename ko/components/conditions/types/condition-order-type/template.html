<div class="condition-value condition-value--type">
  <div class="d-flex flex-wrap">
    <div class="mb-4 mr-4">
      <label class="form-label">Тип заказа</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Тип заказа">
      </button>

      <div class='select2-wrapper condition-value__select'>
        <select data-bind="value: $data.orderType,
              select2: {
                  containerCssClass: 'form-control',
                  wrapperCssClass: 'select2-container--form-control',
                  placeholder: 'Не выбрано',
                  minimumResultsForSearch: 10,
              },
              options: $component.directories.orderTypes.data,
              optionsText: 'name',
              optionsValue: 'id'" data-placeholder="Не выбрано">
        </select>
      </div>
    </div>
    <div class="mb-4">
      <label class="form-label">Заказ</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Заказ">
      </button>
      <div class='select2-wrapper condition-value__select condition-value__select--lg'>
        <select data-bind="value: $data.orderValue,
                                  select2: {
                                      containerCssClass: 'form-control',
                                      wrapperCssClass: 'select2-container--form-control',
                                      placeholder: 'Не выбрано',
                                      minimumResultsForSearch: 10,
                                  }">
          <option value="1">Хотя бы один раз</option>
          <option value="2">Не заказывал ни разу</option>
        </select>
      </div>
    </div>
  </div>
</div>
