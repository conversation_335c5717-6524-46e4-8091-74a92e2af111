<!-- ko template: {
    foreach: templateIf($component.directories.filials.loaded(), $data),
    afterAdd: fadeAfterAddFactory(200),
  } -->
  <div class="flex-grow-1 mb-2">
    <div class="form-group condition-value condition-value--filial">
      <label class="form-label">Филиал</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Филиал">
      </button>
      <div class='select2-wrapper select2-wrapper--with-flag condition-value__select condition-value__select--full' data-bind="css: {
        'is-invalid': $component.formControlErrorStateMatcher($data.filials),
        'is-valid': $component.formControlSuccessStateMatcher($data.filials),
      }">
        <fc-select
          params="
            options: $component.directories.filials.data,
            value: $data.filials,
            multiple: true,
            placeholder: 'Все филиалы',
            childrenProp: 'elements',
            disableFoldersSelect: true,
            optionTextProp: 'name',
          "
        ></fc-select>

        <div class="form-check" data-bind="stopEvents">
          <input type="checkbox" class="form-check-input" data-bind="checked: $data.filialsExclusionFlag,
          enable: $data.filials().length,
          attr: {
            id: 'filial-condition-exclusion-flag' + conditionIndex
          }">
          <label data-bind="attr: { 'for': 'filial-condition-exclusion-flag' + conditionIndex }" class="form-check-label">Все, кроме</label>
        </div>
      </div>
    </div>
    <!-- ko template: {
    foreach: templateIf($component.formControlErrorStateMatcher($data.filials)(), $data),
    afterAdd: fadeAfterAddFactory(200),
  } -->
    <div class="form-error" data-bind="text: $data.filials.error()"></div>
    <!-- /ko -->
  </div>

  <!-- /ko -->
