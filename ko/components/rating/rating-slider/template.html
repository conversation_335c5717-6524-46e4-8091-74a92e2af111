<div class="rating-slider"
     data-bind="
        css: {
          'rating-slider--closed': !opened(),
          'rating-slider--dense': isDense,
          'with-remove': allowRemove
        }">
  <div class="rating-slider__wrapper">
    <div class="rating-slider__slider"
         data-bind="
                slider,
                sliderRange: true,
                sliderValue: value,
                sliderMin: min(),
                sliderMax: max()
              ">
      <rating-point class="ui-slider-handle lg"
                    params="value: value()[0], type: view, point: points()[0], icon: pointIcons()[0]"></rating-point>

      <rating-point class="ui-slider-handle lg"
                    params="value: value()[1], type: view, point: points()[1], icon: pointIcons()[1]"></rating-point>
    </div>
  </div>

  <!-- ko if: !opened() -->
  <div class="rating-slider__curtain">
  </div>
  <!-- /ko -->
</div>


<div class="rating-slider__text">
  <span class="rating-slider__name"
        data-bind="text: name, attr: { title: name }, tooltip"></span>


  <!-- ko if: allowRemove -->
  <span class="rating-slider__remove"
        data-bind="click: function(_, e) {
              remove();
            }"></span>
  <!-- /ko -->
</div>
</div>
