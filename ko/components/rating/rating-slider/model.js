import { FoquzComponent } from "Models/foquz-component";
import { getRatingPoint } from "Utils/rating/get-rating-point";
export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.min = params.min;
    this.max = params.max;

    this.name = params.name;
    this.value = params.value;

    this.icons = params.icons;

    const smileType = ko.toJS(params.view);

    this.view = ko.computed(() => {
      let view = ko.toJS(params.view);
      if (view == "face") return "face";
      if (view == "like" || view == "heart") return "smile";
      return "default";
    });

    this.points = ko.computed(() => {
      let value = this.value();
      let min = ko.toJS(this.min);
      let max = ko.toJS(this.max);

      return [
        getRatingPoint(value[0], min, max),
        getRatingPoint(value[1], min, max),
      ];
    });

    this.pointIcons = ko.computed(() => {
      if (['face', 'like', 'heart'].includes(smileType)) return [];
      
      const [from, to] = this.value();

      if (this.icons) {
        return [this.icons[from - 1], this.icons[to - 1]];
      }

      return [];
    });

    this.opened = params.opened;
    this.isDense = params.dense;
    this.allowRemove = params.allowRemove;
  }

  remove() {
    this.emitEvent("remove");
  }
}
