<div class="rating-point__point"
     data-bind="attr: {
        'data-point': point,
        'data-img': hasIcon() && imgIcon()
      }">

  <!-- ko if: hasIcon -->
  <!-- ko if: imgIcon -->
  <img data-bind="attr: { src: imgIcon }" alt="">
  <!-- /ko -->
  <!-- ko ifnot: imgIcon -->
  <svg-icon params="name: icon"></svg-icon>
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko ifnot: hasIcon -->
  <!-- ko text: text -->
  <!-- /ko -->
  <!-- /ko -->
</div>

<!-- ko if: variantSkipped -->
<div class="rating-point__skipped f-color-service">
  <svg-icon params="name: 'wavy-circle'" class="svg-icon--sm"></svg-icon>
</div>
<!-- /ko -->

<!-- ko if: comment || (clarifyingComments && clarifyingComments.length) -->
<div class="rating-point__comment f-color-service">
  <svg-icon params="name: 'message-filled-stroke'" class="svg-icon--sm"></svg-icon>
</div>
<!-- /ko -->