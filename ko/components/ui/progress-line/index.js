import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('progress-line', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      element.classList.add('progress-line')

      return new ViewModel(params, element);
    },
  },
  template: html,
});
