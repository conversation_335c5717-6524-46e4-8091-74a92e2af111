<!-- ko template: {
  foreach: templateIf(show(), $data),
  afterAdd: fadeAfterAddFactory(200),
  beforeRemove: fadeBeforeRemoveFactory(200)
} -->
  <div class="foquz-success-message">
    <!-- ko if: showCross -->
    <div class="foquz-success-message__cross" data-bind="click: function() { show(false); }">
      <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.96105 1L1.00024 8.9608M9.00024 8.99996L1.03944 1.03915" stroke="#73808D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>        
    </div>
    <!-- /ko -->
    <span data-bind="text: text"></span>
  </div>
<!-- /ko -->
