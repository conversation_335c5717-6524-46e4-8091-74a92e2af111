import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './correct-mark-controller'

ko.components.register('correct-mark', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('correct-mark');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
