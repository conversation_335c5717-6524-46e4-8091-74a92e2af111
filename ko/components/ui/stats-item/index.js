import { ViewModel } from './model';
import html from './template.html';
import './style.less';



ko.components.register('foquz-stats-item', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);
      element.classList.add('foquz-stats-item');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
