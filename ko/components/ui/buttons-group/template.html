
<div class="f-btn-group">
  <!-- ko foreach: { data: buttons, as: 'btn' } -->
  <button class="f-btn" type="button"
      data-bind="css: {
          'active': $parent.value() == btn.value,
          'disabled': btn.disabled
      }, click: function() {
          if (btn.disabled()) {
              btn.onDisabledClick();
              return false;
          }
          $parent.value(btn.value);
      }">

        <span data-bind="text: btn.text"></span>
         <!-- ko if: btn.disabled -->
            <!-- ko if: btn.blockedIcon -->
            <svg-icon params="name: 'lock'" class="svg-icon--sm ml-2"></svg-icon>
            <!-- /ko -->
        <!-- /ko -->
      </button>
  <!-- /ko -->
</div>
