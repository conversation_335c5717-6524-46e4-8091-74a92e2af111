<div
  class="foquz-media-preview__container"
  data-bind="
    fancyboxGalleryItem: {
      gallery: gallery,
      index: index,
    },
  "
>
  <video class="video__content">
    <source data-bind="attr: { src: $data.url }">
    Тег video не поддерживается вашим браузером.
  </video>
  <div class="video__mask" data-bind="style: { 'background-image': `url(${$data.poster})` }">
    <div class="video-mask-owerlay">
      <svg xmlns="http://www.w3.org/2000/svg" width="49" height="48" viewBox="0 0 49 48" fill="none">
        <path d="M1.5 7C1.5 3.68629 4.18629 1 7.5 1H41.5C44.8137 1 47.5 3.68629 47.5 7V41C47.5 44.3137 44.8137 47 41.5 47H7.5C4.18629 47 1.5 44.3137 1.5 41V7Z" stroke="white" stroke-width="2"/>
        <path d="M32.1939 26.639C33.9354 25.6752 33.9354 23.2659 32.1939 22.3022L21.4184 16.3392C19.6769 15.3755 17.5 16.5801 17.5 18.5076V30.4336C17.5 32.3611 19.6769 33.5657 21.4184 32.602L32.1939 26.639Z" stroke="white" stroke-width="2"/>
      </svg>
    </div>
  </div>
  
</div>

<!-- ko if: description -->
<div class="foquz-media-preview__description" data-bind="text: description"></div>
<!-- /ko -->
