import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('filter-icon', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);
      element.classList.add('foquz-filter-icon')

      return new ViewModel(params, element);
    },
  },
  template: html,
});
