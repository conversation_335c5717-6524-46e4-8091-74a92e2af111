import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('toggler-icon', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);
      element.classList.add('foquz-toggler-icon')

      return new ViewModel(params, element);
    },
  },
  template: html,
});
