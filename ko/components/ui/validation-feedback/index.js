import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('validation-feedback', {
  viewModel: {
    createViewModel: (params, componentInfo) => {

      let element = componentInfo.element;
      let $element = $(element);

      element.classList.add('form-error');
      element.classList.add('validation-feedback');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
