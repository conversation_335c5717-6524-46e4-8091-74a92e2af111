<div class="foquz-media-preview__container" data-bind="fancyboxGalleryItem: {
  gallery: gallery,
  index: index
}">
  <img data-bind="attr: {
    src: poster
  }">

  <!-- ko if: isVideo -->
    <svg class="video-icon" width="43" height="30">
      <use href="#video-play-button" xlink:href="#video-play-button"></use>
    </svg>
  <!-- /ko -->
</div>

<!-- ko if: description -->
<div class="foquz-media-preview__description" data-bind="text: description"></div>
<!-- /ko -->
