<!-- ko if: photos.length || files.length -->
<div>

  <!-- ko if: photos.length -->
  <div class="d-inline-flex align-items-center mr-3 my-1" data-bind="fancyboxGalleryItem: {
      gallery: gallery,
    }">
    <svg-icon params="name: 'image-bold'" class="f-color-service mr-2"></svg-icon>
    <span data-bind="text: photos.length + ' фото'" class="f-color-primary"></span>
  </div>
  <!-- /ko -->

  <!-- ko foreach: files -->

  <a class="d-inline-flex align-items-center mr-3 my-1" data-bind="attr: { href: link }" download>
    <svg-icon params="name: 'clip'" class="f-color-service mr-2"></svg-icon>
    <span data-bind="text: name" class="f-color-primary"></span>
  </a>

  <!-- /ko -->


</div>
<!-- /ko -->
