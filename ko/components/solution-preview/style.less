@import 'Style/breakpoints';

.solution-preview {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 100vh;

  &__loader {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: black;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__frame {
    position: relative;
    flex-grow: 1;
    //overflow: auto;
  }

  iframe {
    min-height: 100%;
    width: 100%;
  }

  &__info {
    padding: 17px 20px;
    width: 385px;
    background: white;
    font-size: 15px;
    line-height: 1.5;
    overflow: auto;

    header {
      margin-bottom: 28px;
    }
  }

  &__close {
    background: none;
    border: none;
    outline: none;
    box-shadow: none;
    width: 35px;
    height: 35px;
    cursor: pointer;
    padding: 0;
  }

  &__title {
    font-size: 18px;
    font-weight: 900;
    line-height: 1.2;
  }

  &__folder {
    color: var(--primary);
    font-size: 15px;
  }

  &__description {
    color: #2e2f31;
    margin-top: 20px;
    padding-bottom: 50px;
  }

  &__description-toggler {
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;

    svg {
      margin-left: 10px;
      transition: transform 400ms;
    }

    &.opened {
      svg {
        transform: rotate(-180deg);
      }
    }
  }

  .btn-primary-outline {
    font-size: 13px !important;
  }

  .tablet-and-desktop({
    &__description {
      display: block !important;
    }
  });

  .mobile-and-tablet({
    flex-direction: column-reverse;
    height: auto;

    &__info {
      width: 100%;
      padding-bottom: 30px;

      header {
        margin-bottom: 16px;
      }
    }

    &__frame {
      min-height: 655px;
      overflow: unset;
    }

    &__description {
      display: none;
    }
  });

  .only-mobile({
    &__info {
      padding-bottom: 16px;
      header {
        margin-bottom: 12px;
      }
    }

    &__description {
      margin-top: 8px;
      padding-bottom: 8px;
    }


    &__title, &__description, &__folder, &__description-toggler {
      font-size: 13px;
    }
  });
}

.foquz-solution-preview.opened {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  z-index: 1000;
  background: black;

  .mobile-and-tablet({
      overflow: auto;
    });
}
