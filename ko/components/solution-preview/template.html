<!-- ko if: opened -->
<!-- ko let: {loading: ko.observable(true) } -->
<div class="solution-preview solution-preview--loading"
     data-bind="css: {
  'solution-preview--loading': loading
  }">
  <div class="solution-preview__frame">

    <!-- ko template: {
          foreach: loading(),
          beforeRemove: fadeBeforeRemoveFactory(400)
      } -->
    <div class="solution-preview__loader f-color-primary">
      <i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i>
    </div>
    <!-- /ko -->

    <iframe data-bind="element: frame,
      attr: {
          src: solution().preview
      },
      event: {
        load: function(_, e) {
          loading(false);
          updateFrame();
          return true;
        }
      }"
            frameborder="0"
            seamless="seamless"></iframe>
  </div>
  <div class="solution-preview__info"
       data-bind="fbScroll">
    <header class="d-flex justify-content-between align-items-center">
      <a href="/user-management/auth/login"
         class="btn btn-primary-outline mr-4">Использовать шаблон</a>
      <button type="button"
              class="solution-preview__close"
              data-bind="click: function() {
              onClose()
          }"><svg width="33"
             height="33"
             viewBox="0 0 35 35"
             fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <path d="M22.6845 12.3154L17.4998 17.5001M17.4998 17.5001L12.3151 22.6848M17.4998 17.5001L22.6845 22.6848M17.4998 17.5001L12.3151 12.3154M17.5 34C8.3865 34 1 26.6135 1 17.5C1 8.3865 8.3865 1 17.5 1C26.6135 1 34 8.3865 34 17.5C34 26.6135 26.6135 34 17.5 34Z"
                stroke="#73808D"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round" />
        </svg>
      </button>
    </header>

    <h2 class="solution-preview__title"
        data-bind="text: solution().name"></h2>
    <div class="d-flex justify-content-between align-items-center mt-2">

      <div class="solution-preview__folder">
        <!-- ko if: solution().folderName -->
        <!-- ko if: !availableFolders().length || availableFolders().includes(solution().folderName) -->
        <!-- ko if: folderLink -->
        <a href="javascript:void(0)"
           data-bind="text: solution().folderName, attr: { href: folderLink }">
        </a>
        <!-- /ko -->
        <!-- ko ifnot: folderLink -->
        <a href="javascript:void(0)"
           data-bind="text: solution().folderName, click: function() {
                  toFolder();
              }">
        </a>
        <!-- /ko -->
        <!-- /ko -->
        <!-- /ko -->
      </div>

      <div class="solution-preview__description-toggler d-md-none"
           data-bind="click: function(_, e) {
              $('.solution-preview__description').slideToggle(400);
              $(e.currentTarget).toggleClass('opened');
          }">
        Описание

        <svg width="18"
             height="11"
             viewBox="0 0 18 11"
             fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <path d="M16 2L9 9L2 2"
                stroke="#3F65F1"
                stroke-width="3"
                stroke-linecap="round"
                stroke-linejoin="round" />
        </svg>
      </div>

    </div>

    <div class="solution-preview__description"
         data-bind="html: solution().description"></div>

  </div>
</div>
<!-- /ko -->
<!-- /ko -->
