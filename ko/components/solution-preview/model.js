import { FoquzComponent } from "Models/foquz-component";
import ee from "event-emitter";
import { debounce } from "@/utils/timer/debounce";

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);
    ee(this);

    this.solution = ko.observable(null);
    this.availableFolders = ko.observableArray([]);
    this.folderLink = ko.observable("link");

    this.frame = ko.observable(null);

    this.opened = ko.observable(false);
    this.opened.subscribe((v) => {
      document.body.style.overflow = v ? "hidden" : "";
      element.classList.toggle("opened", !!v);
    });

    this.update = debounce(() => {
      this.updateFrame();
    }, 400);
    $(window).on("resize", () => this.update());
  }

  open(config) {
    this.solution(config.solution);
    this.availableFolders(config.folders || []);
    this.folderLink(config.folderLink);
    this.opened(true);
  }

  close() {
    this.opened(false);
    this.solution(null);
    this.availableFolders([]);
  }

  updateFrame() {
    let frame = this.frame();
    if (!frame) return;
    let parent = frame.parentElement;
    let frameHeight = frame.contentDocument.body.scrollHeight;
    let parentHeight = parent.offsetHeight;
    frame.style.height = Math.max(parentHeight, frameHeight) + 40 + "px";
  }

  onClose() {
    this.close();
    this.emit("close");
  }

  onFolderClick() {
    this.close();
    this.emit("folderClick", this.solution().folderName);
  }
}
