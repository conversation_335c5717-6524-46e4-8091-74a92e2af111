<div data-bind="dnd: function(files) {
  screen.bgLoader.loadFile(files[0]);
}, dndDisabled: screen.bgLoader.preview() || !screen.enabled()">
  <div class="settings__inter-screens-disable-curtain"
       data-bind="fade: !screen.enabled()"></div>

  <div class="settings__inter-screens-header">
    <label class="switch settings__inter-screens-preview-enable-toggle">
      <input type="checkbox"
             data-bind="checked: screen.enabled">
      <span class="switch__slider"></span>
    </label>

    <span class="settings__inter-screens-preview-name"
          data-bind="text: screen.screenName"></span>

    <button type="button"
            class="btn-question"
            data-placement="top"
            data-bind="attr: {
title: screen.screenHint,
}, tooltip,">
    </button>

    <div class="spacer"></div>

    <div class="form-group form-check settings__inter-screens-preview-checkbox"
         data-bind="let: { inputId: screen.id + '-preview' }">
      <input type="checkbox"
             class="form-check-input"
             data-bind="checked: screen.preview, attr: { disabled: !screen.enabled(), id: inputId }, ">
      <label class="form-check-label"
             data-bind="attr: { 'for': inputId }">Предпросмотр</label>
    </div>

    <div class="settings__inter-screens-preview-header-view-buttons">
      <button type="button"
              class="btn settings__inter-screens-view-button settings__inter-screens-view-button--desktop"
              data-bind="click: function() { screen.open('desktop'); }, attr: { disabled: !screen.enabled() }">
      </button>

      <button type="button"
              class="btn settings__inter-screens-view-button settings__inter-screens-view-button--tablet"
              data-bind="click: function() { screen.open('tablet'); }, attr: { disabled: !screen.enabled() }">
      </button>

      <button type="button"
              class="btn settings__inter-screens-view-button settings__inter-screens-view-button--mobile"
              data-bind="click: function() { screen.open('mobile'); }, attr: { disabled: !screen.enabled() }">
      </button>
    </div>
  </div>

  <div class="settings__inter-screens-content"
       data-bind="css: { 'settings__inter-screens-content--disabled': !screen.enabled() }">
    <div class="settings__inter-screens-preview">
      <div class="settings__inter-screens-page">
        <!-- ko if: screen.bgLoader.preview() !== null -->
        <div class="settings__inter-screens-page-background"
             data-bind="style: { 'background-image': 'url(' + screen.bgLoader.preview() + ')' }"></div>
        <div class="settings__inter-screens-page-covering"></div>
        <!-- /ko -->

        <div class="settings__inter-screens-page-content">


          <div class="settings__inter-screens-page-title"
               data-bind="html: screen.previewTitle">
          </div>

          <div class="settings__inter-screens-page-text"
               data-bind="html: screen.previewDescription">
          </div>

          <!-- ko if: screen.id == 'start' -->
          <a class="btn btn-survey-white settings__inter-screens-page-start-button"
             href="#"
             data-bind="text: screen.texts.takeSurvey() || screen.defaultTexts.takeSurvey">
          </a>
          <!-- /ko -->

          <!-- ko if: screen.hasComplainButton() || screen.hasUnsubscribeButton() -->
          <div class="settings__inter-screens-page-additional-actions">
            <!-- ko if: screen.hasComplainButton -->
            <a class="settings__inter-screens-page-complain-button"
               href="#"
               data-bind="text: screen.texts.complain() || screen.defaultTexts.complain"></a>
            <!-- /ko -->

            <!-- ko if: screen.hasUnsubscribeButton -->
            <a class="settings__inter-screens-page-unsubscribe-button"
               href="#"
               data-bind="text: screen.texts.unsubscribe() || screen.defaultTexts.unsubscribe">
              Отписаться от рассылки
            </a>
            <!-- /ko -->
          </div>
          <!-- /ko -->

          <!-- ko if: !screen.poll().isAuto && screen.share.enabled() -->

          <!-- ko template: {
    name: 'share-buttons-template',
    data: {
      share: screen.share
    }
  } -->
          <!-- /ko -->

          <!-- /ko -->

        </div>
      </div>
    </div>

    <div class="settings__inter-screens-row">


      <div class="form-group settings__inter-screens-editor-form-group settings__inter-screens-title-form-group"
           data-bind="settingsInterScreensEditorFormGroup: screen.titleEditorId">
        <label class="form-label">Заголовок раздела</label>

        <div class="chars-counter chars-counter--type_textarea">
          <textarea class="form-control"
                    maxlength="10"
                    data-bind="value: screen.title, attr: {
    id: screen.titleEditorId
  }">
                  </textarea>
          <!--                            <span class="chars-counter__value"></span>-->
        </div>

        <div class="settings__variables">
          <div class="settings__variable"
               data-variable
               data-value="{ФИО}">ФИО</div>

          <!-- ko if: screen.hasOrder -->
          <div class="settings__variable"
               data-variable
               data-bind="visible: $root.isOrderTrigger()"
               data-value="{№}">Номер заказа</div>
          <div class="settings__variable"
               data-variable
               data-bind="visible: $root.isOrderTrigger()"
               data-value="{Дата}">Время заказа</div>
          <!-- /ko -->

          <div class="settings__variable"
               data-bind="click: function($data, e) {
                    screen.setPromocode(screen.titlePromocode, e.target);
                }">
            <!-- ko text: screen.titlePromocode.label  -->
            <!-- /ko -->
          </div>

          <!-- ko if: screen.withPoints -->
          <div class="settings__variable"
               data-variable
               data-value="{Общее кол-во баллов}">Общее кол-во баллов</div>
          <div class="settings__variable"
               data-variable
               data-value="{Набранное кол-во баллов}">Набранное кол-во баллов</div>
          <div class="settings__variable"
               data-variable
               data-value="{Набранное кол-во баллов в %}">Набранное кол-во баллов в %</div>
          <!-- /ko -->
        </div>
      </div>

      <div class="settings__inter-screens-background-picker d-flex flex-column align-items-end">
        <media-load-button params="
                loader: screen.bgLoader">
          <foquz-icon params="icon: 'upload'"></foquz-icon>

          <span class="mt-2">
            добавить фон
          </span>
        </media-load-button>

        <file-loader-error style="width: 105px"
                           params="error: screen.bgLoader.error"></file-loader-error>
      </div>

    </div>

    <div class="form-group settings__inter-screens-editor-form-group"
         data-bind="settingsInterScreensEditorFormGroup: screen.descriptionEditorId">
      <label class="form-label">Текст раздела</label>

      <div class="chars-counter chars-counter--type_textarea">
        <textarea class="form-control"
                  maxlength="10"
                  data-bind="value: screen.description, attr: {
  id: screen.descriptionEditorId
}">
              </textarea>
        <!--                        <span class="chars-counter__value"></span>-->
      </div>

      <div class="settings__variables">
        <div class="settings__variable"
             data-variable
             data-value="{ФИО}">ФИО</div>
        <!-- ko if: screen.hasOrder -->
        <div class="settings__variable"
             data-variable
             data-bind="visible: $root.isOrderTrigger()"
             data-value="{№}">Номер заказа</div>
        <div class="settings__variable"
             data-variable
             data-bind="visible: $root.isOrderTrigger()"
             data-value="{Дата}">Время заказа</div>
        <!-- /ko -->
        <div class="settings__variable"
             data-bind="click: function($data, e) {
              screen.setPromocode(screen.descriptionPromocode, e.target);
          }">
          <!-- ko text: screen.descriptionPromocode.label  -->
          <!-- /ko -->
        </div>

        <!-- ko if: screen.withPoints -->
        <div class="settings__variable"
             data-variable
             data-value="{Общее кол-во баллов}">Общее кол-во баллов</div>
        <div class="settings__variable"
             data-variable
             data-value="{Набранное кол-во баллов}">Набранное кол-во баллов</div>
        <div class="settings__variable"
             data-variable
             data-value="{Набранное кол-во баллов в %}">Набранное кол-во баллов в %</div>
        <!-- /ko -->
      </div>
    </div>

    <div class="row">
      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <div class="form-group form-check"
             data-bind="let: { inputId: screen.id + '-complain' }">
          <input type="checkbox"
                 class="form-check-input"
                 data-bind="checked: screen.hasComplainButton, attr: { id: inputId }">
          <label class="form-check-label"
                 data-bind="attr: { 'for': inputId }">
            кнопка «Пожаловаться»
          </label>
          <button type="button"
                  class="btn-question"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Функция используется для добавления жалобы">
          </button>
        </div>
      </div>

      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <div class="form-group form-check settings__inter-screens-unsubscribe-checkbox"
             data-bind="let: { inputId: screen.id + '-unsubscribe' }">
          <input type="checkbox"
                 class="form-check-input"
                 data-bind="checked: screen.hasUnsubscribeButton, attr: { id: inputId }">
          <label class="form-check-label"
                 data-bind="attr: { 'for': inputId }">
            Отписаться от рассылки
          </label>
          <button type="button"
                  class="btn-question"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Функция используется для возможности отписаться от рассылки опросов">
          </button>
        </div>
      </div>
    </div>

    <div>

      <div class="row">

        <div class="col col-12 col-lg-6">
          <div class="row">
            <!-- ko if: screen.id == 'start' -->
            <div class="col-12">
              <div class="form-group">
                <label class="form-label">Текст кнопки «пройти опрос»</label>
                <div class="chars-counter chars-counter--type_input"
                     data-bind="charsCounter, charsCounterCount: screen.texts.takeSurvey().length">
                  <input type="text"
                         class="form-control"
                         data-bind="textInput: screen.texts.takeSurvey, attr: { placeholder: screen.defaultTexts.takeSurvey }"
                         maxlength="30">
                  <div class="chars-counter__value"></div>
                </div>
              </div>
            </div>
            <!-- /ko -->
            <div class="col-12">
              <div class="form-group">
                <label class="form-label">Текст ссылки «пожаловаться»</label>
                <div class="chars-counter chars-counter--type_input"
                     data-bind="charsCounter, charsCounterCount: screen.texts.complain().length">
                  <input type="text"
                         class="form-control"
                         data-bind="textInput: screen.texts.complain, attr: { placeholder: screen.defaultTexts.complain }"
                         maxlength="30">
                  <div class="chars-counter__value"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col col-12 col-lg-6">
          <div class="form-group">
            <label class="form-label">Текст ссылки «отписаться»</label>
            <div class="chars-counter chars-counter--type_input"
                 data-bind="
    charsCounter,
    charsCounterCount: screen.texts.unsubscribe().length">
              <input type="text"
                     class="form-control"
                     data-bind="textInput: screen.texts.unsubscribe, attr: { placeholder: screen.defaultTexts.unsubscribe }"
                     maxlength="30">
              <div class="chars-counter__value"></div>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- ko ifnot: screen.poll().isAuto -->
    <hr class="mb-4 mt-0">
    <div>
      <!-- ko template: {
name: 'share-template',
data: {
  share: screen.share
}
} -->
      <!-- /ko -->
    </div>
    <!-- /ko -->

  </div>

  <dnd-cover class="ma-n15p"
             params="type: 'image'"></dnd-cover>

</div>
