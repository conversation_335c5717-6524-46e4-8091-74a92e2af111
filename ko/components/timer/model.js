import { FoquzComponent } from 'Models/foquz-component';
import { splitSeconds } from 'Utils/date/time';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.time = params.time; // секунды

    this.intervals = ko.pureComputed(() => {
      let timestamp = this.time();

      let { h, m, s } = splitSeconds(timestamp);

      return {
        hours: h,
        minutes: m,
        seconds: s
      };
    });
  }

  wrapValue(value) {
    return ('' + value).padStart(2, '0');
  }
}
