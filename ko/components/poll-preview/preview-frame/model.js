import { FoquzComponent } from 'Models/foquz-component';
import OverlayScrollbars from 'overlayscrollbars';
import 'overlayscrollbars/css/OverlayScrollbars.min.css';
import { Translator } from '@/utils/translate';

const PreviewTranslator = Translator('poll-preview');
export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);
    this.translator = PreviewTranslator;
    this.url = params.url;
    this.previewType = params.previewType;
    this.onLoad = params.onLoad;
    this.frame = ko.observable(null);

    /**
     * @var boolean Флаг, указывающий, включен ли режим превью для Vue приложения
     * @default false
     */
    this.isVuePreview = params.isVuePreview || false;
  }

  onFrameLoad() {

    if (!this.isVuePreview) {
      // Для Vue приложения не используется OverlayScrollbars
      let frameBody = this.frame().contentDocument.body;
      OverlayScrollbars(frameBody, {});
    }


    if (typeof this.onLoad == 'function') this.onLoad();
  }

  setSize(w, h) {
    let frame = this.frame();

    if (this.isVuePreview) {
      // Для Vue приложения используется postMessage для взаимодействия с iframe окном
      frame.contentWindow.postMessage({
        type: 'SET_SIZE',
        data: { width: w, height: h }
      }, '*')
    } else {
      // Для обычного iframe используется прямое изменение стилей
      let frameBody = frame.contentDocument?.body;

      if (frameBody) {
        frame.style.width = `${w}px`;
        frame.style.height = `${h}px`;
        frameBody.setAttribute('style', `width: ${w}px!important; height: ${h}px!important`);
      }
    }
  }
}
