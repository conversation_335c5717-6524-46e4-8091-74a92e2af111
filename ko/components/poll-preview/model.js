import { FoquzComponent } from 'Models/foquz-component';
import { Translator } from '@/utils/translate';

const PreviewTranslator = Translator('poll-preview');

const sizes = {
  mobile: {
    w: 360,
    h: 620
  },
  trade: {
    w: 1024,
    h: 669
  },
  desktop: {
    w: 1024,
    h: 639
  },
  pagestopDesktop: {
    w: 680,
    h: 526
  }
};

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.translator = PreviewTranslator;

    this.previewType = ko.observable('mobile');
    this.isWidgetMode = ko.observable(false);
    this.widgetType = ko.observable('');
    this.onChange = params.onChange;

    this.scroll = ko.observable(null);
    this.frame = ko.observable(null);
    this.fullPreview = ko.observable(null);
    this.mode = params.mode || 'default';
    this.isAuto = params.isAuto;

    this.title = params.title;
    this.subtitle = params.subtitle;
    this.onOpen = params.onOpen;

    /**
     * @var boolean Флаг, указывающий, включен ли режим превью для Vue приложения
     * @default false
     */
    this.isVuePreview = params.isVuePreview || false;

    element.classList.add('poll-preview--' + this.mode);

    this._url = params.url;
    this._tradeUrl = params.tradeUrl;
    
    this._widgetBaseUrl = this.createWidgetBaseUrl(this._url);
    
    this.pollId = this.extractPollId(this._url);

    // Сохраняем ссылку на элемент для дальнейшего использования
    this.element = element;

    // Обработчик события message для отслеживания fz:resize
    this.messageHandler = this.handleMessage.bind(this);
    window.addEventListener('message', this.messageHandler);

    this.subscriptions.push(
      this.isWidgetMode.subscribe((isWidget) => {
        if (isWidget) {
          this.widgetType('pagestop-mobile');
          
          if (this.isFullscreen()) {
            this.toggleFullscreen();
          }
          
          this.showWidgetPreview('pagestop-mobile');
        } else {
          this.previewType('mobile');
          if (this.isFullscreen()) {
            this.toggleFullscreen();
          }
          if (this.previewType() === 'trade') {
            this.url(this._tradeUrl);
          } else {
            this.url(this._url);
          }
        }

        if (typeof this.onChange == 'function') this.onChange();
      })
    );

    this.subscriptions.push(
      this.previewType.subscribe((v) => {
        console.log('previewType', v);
        if (typeof this.onChange == 'function') this.onChange();

        this.sendHeightToCurrentIframe();

        if (this.isFullscreen()) {
          const previewElement = document.querySelector('.poll-preview-container');
          if (previewElement) {
            if (v === 'trade') {
              previewElement.style.height = '735px';
              previewElement.querySelector('.poll-preview__panel').style.height = '682px';
            } else {
              previewElement.style.height = '';
              previewElement.querySelector('.poll-preview__panel').style.height = '652px';
            }
          }
        }
        
        if (!this.isWidgetMode()) {
          if (v === 'trade') {
            this.url(this._tradeUrl);
          } else {
            this.url(this._url);
          }
        }
      })
    );

    this.subscriptions.push(
      this.widgetType.subscribe((type) => {
        if (this.isWidgetMode()) {
          if (type.includes('desktop') && !this.isFullscreen()) {
            this.toggleFullscreen();
          } 
          else if (type.includes('mobile') && this.isFullscreen()) {
            this.toggleFullscreen();
          }
          
          this.showWidgetPreview(type);
          
          setTimeout(() => {
            this.setSize();
          }, 0);

          if (typeof this.onChange == 'function') this.onChange();
        }
      })
    );

    this.withOpenButton = typeof this.openView == 'function';

    this.url = ko.observable(this._url);
    this.fullPreviewOpened = ko.observable(false);

    this.openInNewTabUrl = ko.computed(() => {
      let baseUrl;
      
      if (this.previewType() === 'trade') {
        baseUrl = this._tradeUrl;
      } else {
        baseUrl = this._url;
      }
      
      if (!this.isVuePreview) {
        return baseUrl;
      }
      
      const url = new URL(baseUrl);
      url.searchParams.set('fullPreview', '1');
      url.searchParams.delete('questionId');
      return url.toString();
    });

    this.isFullscreen = ko.observable(false);
  }

  extractPollId(url) {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      for (let i = 0; i < pathParts.length; i++) {
        if (pathParts[i] === 'p' && i + 1 < pathParts.length) {
          return pathParts[i + 1];
        }
        if (pathParts[i] === 'poll-vue' && i + 1 < pathParts.length) {
          return pathParts[i + 1];
        }
      }
      return null;
    } catch (e) {
      console.error('Error extracting poll ID:', e);
      return null;
    }
  }

  createWidgetBaseUrl(url) {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      let pollId = null;
      
      for (let i = 0; i < pathParts.length; i++) {
        if (pathParts[i] === 'p' && i + 1 < pathParts.length) {
          pollId = pathParts[i + 1];
          break;
        }
        if (pathParts[i] === 'poll-vue' && i + 1 < pathParts.length) {
          pollId = pathParts[i + 1];
          break;
        }
      }
      
      if (!pollId) return url;
      
      const origin = urlObj.origin;
      return `${origin}/widget/${pollId}`;
    } catch (e) {
      console.error('Error creating widget base URL:', e);
      return url;
    }
  }

  showWidgetPreview(widgetType, skipToggleFullscreen = false) {
    try {
      const currentUrl = new URL(this._url);
      
      let previewUrl;
      
      if (currentUrl.pathname.includes('/preview')) {
        previewUrl = this._url;
        const url = new URL(previewUrl);
        
        url.searchParams.delete('nopreview');
        url.searchParams.delete('inFrame');
        url.searchParams.delete('simple');
        url.searchParams.delete('preview-mode');
        url.searchParams.delete('hello-mode');
        
        url.searchParams.set('nopreview', '1');
        url.searchParams.set('inFrame', '1');
        url.searchParams.set('simple', '1');
        url.searchParams.set('preview-mode', '1');
        url.searchParams.set('close_by_finish_button', '1');
        
        switch (widgetType) {
          case 'helloboard-mobile':
            url.searchParams.set('hello-mode', '1');
            break;
          case 'helloboard-desktop':
            url.searchParams.set('hello-mode', '1');
            break;
        }
        
        previewUrl = url.toString();
      }
      
      this.url(previewUrl);
      
      if (!skipToggleFullscreen) {
        if (widgetType.includes('desktop') && !this.isFullscreen()) {
          this.toggleFullscreen();
        } else if (widgetType.includes('mobile') && this.isFullscreen()) {
          const savedWidgetType = widgetType;
          
          this.toggleFullscreen();
          
          if (savedWidgetType === 'helloboard-mobile') {
            setTimeout(() => {
              this.widgetType(savedWidgetType);
            }, 600);
          }
        }
      }
      
    } catch (e) {
      console.error('Error creating widget preview URL:', e);
    }
  }

  setWidgetPagestopMobile() {
    this.widgetType('pagestop-mobile');
    this.showWidgetPreview('pagestop-mobile');
    
    if (this.frame()) {
      this.sendHeightToCurrentIframe();
    }
  }

  setWidgetPagestopDesktop() {
    this.widgetType('pagestop-desktop');
    this.showWidgetPreview('pagestop-desktop');
    
    if (this.frame()) {
      this.sendHeightToCurrentIframe();
    }
  }

  setWidgetHelloboardMobile() {
    this.widgetType('helloboard-mobile');
    
    if (this.frame()) {
      this.sendHeightToCurrentIframe();
    }
    
    if (this.isFullscreen()) {
      const savedWidgetType = 'helloboard-mobile';
      
      this.toggleFullscreen();
      
      setTimeout(() => {
        this.widgetType(savedWidgetType);
        this.showWidgetPreview(savedWidgetType, true);
      }, 600);
    } else {
      this.showWidgetPreview('helloboard-mobile');
    }
  }

  setWidgetHelloboardDesktop() {
    this.widgetType('helloboard-desktop');
    this.showWidgetPreview('helloboard-desktop');
    if (this.frame()) {
      this.sendHeightToCurrentIframe();
    }
  }

  openFullView() {
    this.fullPreviewOpened(true);
    setTimeout(() => {
      if (typeof this.onOpen === 'function') this.onOpen();
      this.fullPreview().open({
        size: this.previewType()
      });
      
      if (this.frame()) {
        this.sendHeightToCurrentIframe();
      }
    }, 500);
  }

  setSize() {
    let sizeKey = this.previewType();
    
    if (this.isWidgetMode() && this.widgetType() === 'pagestop-desktop') {
      sizeKey = 'pagestopDesktop';
    }
    
    const size = sizes[sizeKey] || sizes.mobile;
  }

  toggleFullscreen() {
    const newState = !this.isFullscreen();
    const previewElement = document.querySelector('.poll-preview-container');
    const wrapperElement = document.querySelector('.poll-preview-wrapper');
    const upwardButtonContainer = document.querySelector('.upward-button-container');
    const pollPreviewElement = document.querySelector('.poll-preview');
    
    const currentWidgetType = this.widgetType();
    const isExplicitHelloboardMobile = currentWidgetType === 'helloboard-mobile';
    
    if (!this.isFullscreen()) {
      if (previewElement) {
        const rect = previewElement.getBoundingClientRect();
        previewElement.dataset.originalHeight = rect.height + 'px';
        previewElement.dataset.originalWidth = rect.width + 'px';
        
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        if (scrollTop <= 125) {
          previewElement.style.top = '-80px';
        } else {
          previewElement.style.top = '0';
        }
        
        if (this.previewType() === 'trade') {
          previewElement.style.height = '735px';
          previewElement.querySelector('.poll-preview__panel').style.height = '682px';
        } else {
          previewElement.style.height = '';
          previewElement.querySelector('.poll-preview__panel').style.height = '';
        }
      }
      
      if (wrapperElement) {
        wrapperElement.style.zIndex = '999';
      }
      
      if (upwardButtonContainer) {
        upwardButtonContainer.style.display = 'none';
      }
      
      if (pollPreviewElement) {
        pollPreviewElement.classList.add('active');
      }
      
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = scrollbarWidth + 'px';
      
      document.body.dataset.scrollTop = window.pageYOffset;
    } else {
      if (previewElement) {
        previewElement.classList.add('was-fullscreen');
        
        if (this.isWidgetMode() && 
            currentWidgetType !== 'pagestop-mobile' && 
            !isExplicitHelloboardMobile) {
          this.widgetType('pagestop-mobile');
          this.showWidgetPreview('pagestop-mobile');
        }
        
        setTimeout(() => {
          previewElement.classList.remove('was-fullscreen');
          
          if (wrapperElement) {
            wrapperElement.style.zIndex = '1049';
          }
          
          previewElement.style.top = '';
          previewElement.style.height = '';
          if (previewElement.querySelector('.poll-preview__panel')) {
            previewElement.querySelector('.poll-preview__panel').style.height = '';
          }
          
          if (upwardButtonContainer) {
            upwardButtonContainer.style.display = '';
          }
          
          if (pollPreviewElement) {
            pollPreviewElement.classList.remove('active');
          }
        }, 500);
      }
      
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
      
      this.previewType('mobile');
    }
    
    this.isFullscreen(newState);
  }

  /**
   * Отправляет данные о высоте в iframe
   * @param {HTMLIFrameElement} frame - iframe элемент
   * @param {number} height - высота для отправки
   */
  sendHeightToIframe(frame, height) {
    if (!frame || !frame.contentWindow) {
      console.log('No valid iframe or contentWindow');
      return;
    }
    
    const windowWidth = window.innerWidth;
    
    const minHeight = 230;

    console.log('sendHeightToIframe: height', height);
    
    
    const message = {
      type: "fz:set_max_height",
      height: height,
      minHeight: minHeight,
      setAutoHeight: true,
      windowWidth: windowWidth,
    };
    
    
    const messageString = JSON.stringify(message);
    
    try {
      frame.contentWindow.postMessage(messageString, "*");
      console.log('Sent height to iframe:', height, 'Message:', messageString);
    } catch (e) {
      console.error('Error sending message to iframe:', e);
    }
  }
  
  /**
   * Отправляет данные о высоте в текущий iframe
   */
  sendHeightToCurrentIframe() {    
    
    const previewPanel = document.querySelector('.poll-preview-widget-frame');
    
    if (!previewPanel) {
      return;
    }
    
    const iframe = previewPanel.querySelector('iframe');
    
    if (!iframe) {
      return;
    }
    
    const height = previewPanel.offsetHeight - 30;
    
    this.sendHeightToIframe(iframe, height);
  }

  handleMessage(event) {
    try {
      const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
      if (data && data.type === 'fz:resize' && data.height) {
        const previewPanel = document.querySelector('.poll-preview-widget-frame');
        const iframe = previewPanel.querySelector('iframe');
        
        if (previewPanel && iframe) {
          previewPanel.style.setProperty(
            "--fqz-modal-iframe-content-height",
            `${data.height}px`
          );
          
          console.log('Resize event received, new height:', data.height);
        }
      } 

      if (data && data.type === 'fz:app_ready') {
        if (typeof this.onChange == 'function') this.onChange();
        const iframe = document.querySelector('.poll-preview-widget-frame iframe');
        if (iframe) {
          setTimeout(() => {
            this.sendHeightToCurrentIframe();
          }, 0);
        }
      }
    } catch (e) {
      console.error('Error handling message event:', e);
    }
  }

  dispose() {
    window.removeEventListener('message', this.messageHandler);
    
    window.removeEventListener('resize', this.resizeHandler);
    
    super.dispose();
  }
}
