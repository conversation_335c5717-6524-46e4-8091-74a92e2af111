import { ViewModel } from './model';
import html from './template.html';
import './style.less';
import './preview-frame';
import './preview-frame-widget';
import './full-preview';

ko.components.register('poll-preview', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      element.classList.add('poll-preview');

      return new ViewModel(params, element);
    }
  },
  template: html
});
