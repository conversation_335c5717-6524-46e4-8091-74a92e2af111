@import 'Style/colors';

.poll-preview {
  height: 665px;
  display: flex;
  flex-direction: column;
  
  &.active {
    height: 1px;
  }

  &__nav {
    display: flex;
    border-bottom: 1px solid @f-color-border;

    &-item {
      background: transparent;
      border: 0;
      box-shadow: none;
      outline: none;
      border-right: 1px solid @f-color-border;
      font-size: 16px;
      color: #73808D;
      font-family: inherit;
      font-weight: 700;
      transition: color 400ms ease-in-out;

      .btn-question {
        display: inline-block;
      }

      &.btn {
        height: 16px;
        padding: 0;
      }

      &:last-child {
        border-right: none;
      }

      &.active,
      &:hover {
        color: @f-color-text;
      }

      &:focus {
        box-shadow: none;
      }
    }

    &-expand {
      width: auto;
      padding-right: 0;
      flex-grow: 0;
      color: @f-color-service;
      transition: color 400ms ease-in-out;

      &:hover {
        color: @f-color-text;
      }
    }
  }

  &__panel {
    flex-grow: 1;
    width: 100%;

    display: flex;
    flex-direction: column;
    overflow: hidden;

    position: relative;

    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
  }

  &-frame {
    width: 100%;
    min-height: 660px;
    flex-grow: 1;
    position: relative;
    overflow-y: auto;
    margin-top: 25px;

    iframe {
      display: block;
    }

    .os-scrollbar {
      &-vertical {
        width: 8px;
      }
      &-horizontal {
        height: 8px;
      }
      &-handle {
        background: white!important;
      }
      &-track {
        background: rgba(0, 0, 0, 0.5)!important;
      }
    }

    &--mobile {
      border-radius: 9px;
      border: 1px solid #CFD8DC;
      box-sizing: content-box;
      height: 620px;
      min-height: 620px;
      max-height: 620px;
      margin-left: auto;
      margin-right: auto;
      margin-top: 15px;

      &:before {
        left: 15px;
        right: 15px;
      }

      .os-scrollbar-vertical {
        right: 8.5px;
      }
      .os-scrollbar-handle {
        background: #CFD8DC!important;
      }

      .os-content {
        outline: 1px solid #CFD8DC!important;
      }

      iframe {
        width: 360px;
        height: 620px;
      }
    }

    &--trade {
      border-radius: 8px;

      iframe {
        width: 100%;
        height: 670px;
      }
    }

    &--desktop {
      border-radius: 8px;

      iframe {
        width: 100%;
        height: 639px;
      }
    }
  }

  &-widget-frame {
    display: flex;
    width: 100%;
    flex-grow: 1;
    position: relative;
    margin-top: 15px;
    border: 1px solid #CFD8DC;
    border-radius: 8px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;

    iframe {
      display: block;
      height: var(--fqz-modal-iframe-content-height);
      transition: height 0.3s ease-in-out;
    }

    &--pagestop-mobile {
      background-image: var(--poll-mobile-bg-image);
      justify-content: center;
      align-items: center;
      height: 620px;

      iframe {
        width: 330px;
      }
    }

    &--pagestop-desktop {
      background-image: var(--poll-bg-image);
      justify-content: center;
      align-items: center;

      iframe {
        width: 680px;
      }
    }

    &--helloboard-mobile {
      background-image: var(--poll-mobile-bg-image);
      justify-content: center;
      align-items: end;
      height: 620px;

      iframe {
        width: 330px;
      }
    }

    &--helloboard-desktop {
      background-image: var(--poll-bg-image);
      justify-content: end;
      align-items: end;
      padding-right: 15px;

      iframe {
        width: 330px;
      }
    }
  }
}

.poll-preview-container {
  transition: none;
  position: relative;
  width: 100%;
  
  &.fullscreen {
    position: absolute;
    right: 0;
    z-index: 999;
    background: white;
    padding: 15px;
    margin: 0;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 1054px;
    
    .poll-preview__panel {
      height: 652px;
    }
    
    .poll-preview-frame {
      height: 100%;
      width: 100%;
      margin-top: 13px;
    }
  }
}

@keyframes expand-width {
  from {
    width: 370px;
    right: 0;
  }
  to {
    width: 1054px;
    right: 0;
  }
}

@keyframes shrink-width {
  from {
    width: 1054px;
    right: 0;
  }
  to {
    width: 370px;
    right: 0;
  }
}

@keyframes fade-in-content {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-out-content {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

.poll-preview-container.fullscreen {
  animation: expand-width 0.5s ease-out forwards;
  
  .poll-preview__panel,
  .poll-preview__nav {
    animation: fade-in-content 0.4s ease-out 0.1s forwards;
  }
}

.poll-preview-container.was-fullscreen {
  position: absolute;
  right: 0;
  z-index: 9999;
  background: white;
  padding: 20px;
  margin: 0;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: shrink-width 0.5s ease-in forwards;
  
  .poll-preview__panel,
  .poll-preview__nav {
    animation: fade-out-content 0.3s ease-in forwards;
  }
}

.poll-preview-container.fullscreen .poll-preview__panel,
.poll-preview-container.fullscreen .poll-preview__nav,
.poll-preview-container.was-fullscreen .poll-preview__panel,
.poll-preview-container.was-fullscreen .poll-preview__nav {
  opacity: 1;
}

.poll-preview-placeholder {
  display: none;
  
  &.active {
    display: block;
  }
}

.poll-preview-fullscreen-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  z-index: 10;
}

.poll-preview-close-button {
  position: absolute;
  top: 10px;
  left: -17px;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: white;
  border: none;
  box-shadow: 0px 5px 15px 0px rgba(46, 47, 49, 0.30);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f5f6fa;
  }
  
  svg {
    width: 15px;
    height: 15px;
    color: #73808D;
  }
}

.poll-preview-open-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: white;
  border: 1px solid @f-color-border;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  
  &:hover {
    background: #f5f5f5;
    transform: scale(1.1);
  }
  
  svg {
    width: 16px;
    height: 16px;
    color: @f-color-text;
  }
}

.poll-preview-wrapper {
  position: sticky;
  top: 20px;
  z-index: 1049;
  overflow: visible;
  
  &.expanded {
    position: relative;
    z-index: 9999;
  }
}

body.scroll-locked {
  overflow: hidden;
  padding-right: var(--scrollbar-width, 0);
}

.preview-buttons-container,
.widget-buttons-container {
  display: flex;
}

.preview-buttons-container.hidden {
  display: none;
}

.widget-buttons-container {
  display: none;
}

.widget-buttons-container.visible {
  display: flex;
}
