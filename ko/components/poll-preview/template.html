<div class="poll-preview-container" data-bind="css: { 'fullscreen': isFullscreen }">
  <!-- ko if: isFullscreen -->
  <button type="button" class="poll-preview-close-button" data-bind="click: toggleFullscreen">
    <svg-icon params="name: 'chevron-right'" class="svg-icon--lg"></svg-icon>
  </button>
  <!-- /ko -->
  
  <div class="poll-preview__nav justify-content-between align-items-center border-bottom-0 pl-10p pr-10p">
    <div class="d-flex position-relative">
      <div class="switch-form-group switch-form-group--sm mr-20p">
        <label class="switch switch--sm form-control">
          <input type="checkbox" data-bind="checked: isWidgetMode">
          <span class="switch__slider"></span>
        </label>

        <label class="form-label" data-bind="text: $component.translator.t('Виджет')"></label>
      </div>
      
      <div class="preview-buttons-container" data-bind="css: { 'hidden': isWidgetMode() }">
        <button type="button"
                class="button-ghost mr-20p"
                data-bind="tooltip, tooltipText: $component.translator.t('Просмотр опроса на мобильных устройствах'),
                css: {
                  'f-color-primary': $component.previewType() == 'mobile',
                  'f-color-service': $component.previewType() != 'mobile',
                },
                click: function() { 
                  $component.previewType('mobile');
                  if ($component.isFullscreen()) {
                    $component.toggleFullscreen();
                  }
                }">
          <svg-icon params="name: 'viewport-mobile'"
                    class="svg-icon--lg"></svg-icon>
        </button>
        <button type="button"
                class="button-ghost mr-20p"
                data-bind="tooltip, tooltipText: $component.translator.t('Просмотр опроса на компьютере'),
              css: {
                'f-color-primary': $component.previewType() == 'desktop',
                'f-color-service': $component.previewType() != 'desktop',
              },
              click: function() { 
                $component.previewType('desktop');
                if (!$component.isFullscreen()) {
                  $component.toggleFullscreen();
                }
              }">
          <svg-icon params="name: 'viewport-desktop'"
                    class="svg-icon--lg"></svg-icon>
        </button>

        <!-- ko ifnot: isAuto -->
        <button type="button"
                class="button-ghost mr-20p"
                data-bind="tooltip, tooltipText: $component.translator.t('Просмотр опроса для планшета в торговой точке'),
              css: {
                'f-color-primary': $component.previewType() == 'trade',
                'f-color-service': $component.previewType() != 'trade',
              },
              click: function() {
                $component.previewType('trade')
                if (!$component.isFullscreen()) {
                  $component.toggleFullscreen();
                }
              }">
          <svg-icon params="name: 'viewport-trade'"
                    class="svg-icon--lg"></svg-icon>
        </button>
        <!-- /ko -->
      </div>
      
      <div class="widget-buttons-container" data-bind="css: { 'visible': isWidgetMode() }">
        <button type="button"
                class="button-ghost mr-20p"
                data-widget-type="pagestop-mobile"
                data-bind="tooltip, tooltipText: $component.translator.t('Виджет «Упрощённый Page-stop», просмотр на мобильном устройстве'),
                css: {
                  'f-color-primary': $component.widgetType() == 'pagestop-mobile',
                  'f-color-service': $component.widgetType() != 'pagestop-mobile',
                },
                click: setWidgetPagestopMobile">
          <svg-icon params="name: 'viewport-pagestop-mobile'"
                    class="svg-icon--lg"></svg-icon>
        </button>
        <button type="button"
                class="button-ghost mr-20p"
                data-bind="tooltip, tooltipText: $component.translator.t('Виджет «Упрощённый Page-stop», просмотр на компьютере'),
                css: {
                  'f-color-primary': $component.widgetType() == 'pagestop-desktop',
                  'f-color-service': $component.widgetType() != 'pagestop-desktop',
                },
                click: setWidgetPagestopDesktop">
          <svg-icon params="name: 'viewport-pagestop-desktop'"
                    class="svg-icon--lg"></svg-icon>
        </button>
        <button type="button"
                class="button-ghost mr-20p"
                data-bind="tooltip, tooltipText: $component.translator.t('Виджет «Hello-board», просмотр на мобильном устройстве'),
                css: {
                  'f-color-primary': $component.widgetType() == 'helloboard-mobile',
                  'f-color-service': $component.widgetType() != 'helloboard-mobile',
                },
                click: setWidgetHelloboardMobile">
          <svg-icon params="name: 'viewport-helloboard-mobile'"
                    class="svg-icon--lg"></svg-icon>
        </button>
        <button type="button"
                class="button-ghost mr-20p"
                data-bind="tooltip, tooltipText: $component.translator.t('Виджет «Hello-board», просмотр на компьютере'),
                css: {
                  'f-color-primary': $component.widgetType() == 'helloboard-desktop',
                  'f-color-service': $component.widgetType() != 'helloboard-desktop',
                },
                click: setWidgetHelloboardDesktop">
          <svg-icon params="name: 'viewport-helloboard-desktop'"
                    class="svg-icon--lg"></svg-icon>
        </button>
      </div>
    </div>

    <a
      class="btn poll-preview__nav-item"
      target="_blank"
      data-bind="attr: { href: openInNewTabUrl }, tooltip, tooltipText: $component.translator.t('Открыть в новой вкладке')">
      <svg-icon params="name: 'target-link'"></svg-icon>
    </a>
  </div>

  <div class="poll-preview__panel">
    <!-- ko if: isWidgetMode() -->
    <preview-frame-widget params="ref: frame, url: url, onLoad: function() { setSize() }, isVuePreview: isVuePreview"
                   data-bind="css: 'poll-preview-widget-frame--' + $component.widgetType()"
                   class="poll-preview-widget-frame"></preview-frame-widget>
    <!-- /ko -->
    <!-- ko ifnot: isWidgetMode() -->
    <preview-frame params="ref: frame, url: url, onLoad: function() { setSize() }, isVuePreview: isVuePreview"
                   data-bind="css: 'poll-preview-frame--' + $component.previewType()"
                   class="poll-preview-frame"></preview-frame>
    <!-- /ko -->
  </div>
</div>

<div class="poll-preview-placeholder" data-bind="css: { 'active': isFullscreen }, style: { height: isFullscreen() ? '500px' : '0px' }"></div>

<!-- ko if: $component.fullPreviewOpened() -->
<full-preview
              params="ref: fullPreview, url: _url, tradeUrl: _tradeUrl, title: title, subtitle: subtitle, isAuto: isAuto, isVuePreview: isVuePreview">
</full-preview>
<!-- /ko -->
