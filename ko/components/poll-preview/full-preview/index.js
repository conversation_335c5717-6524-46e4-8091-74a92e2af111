import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('full-preview', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      let $element = $(element);

      element.classList.add('full-preview');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
