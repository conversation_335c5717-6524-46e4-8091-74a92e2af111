import OverlayScrollbars from 'overlayscrollbars';
import 'overlayscrollbars/css/OverlayScrollbars.min.css';
import { FoquzComponent } from 'Models/foquz-component';
import { Translator } from '@/utils/translate';

const PreviewTranslator = Translator('poll-preview');
const sizes = {
  mobile: {
    w: 360,
    h: 620
  },
  trade: {
    w: 1024,
    h: 669
  },
  desktop: {
    w: 1024,
    h: 639
  }
};
export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);
    this.translator = PreviewTranslator;
    this.modal = ko.observable(null);
    window.__modal = this.modal;
    this._url = params.url;
    this._tradeUrl = params.tradeUrl;
    this.isAuto = params.isAuto;

    /**
     * @var boolean Флаг, указывающий, включен ли режим превью для Vue приложения
     * @default false
     */
    this.isVuePreview = params.isVuePreview || false;

    this.url = ko.observable(this._url);
    this.newTabUrl = ko.observable(this._url);

    this.openInNewTabUrl = ko.computed(() => {
      if (!this.isVuePreview) {
        return this.url();
      }

      const url = new URL(this.newTabUrl());
      url.searchParams.set('fullPreview', '1');
      url.searchParams.delete('questionId');
      return url.toString();
    });

    this.title = params.title;
    this.subtitle = params.subtitle;

    this._opened = ko.observable(false);
    this._opened.subscribe((v) => {
      if (v) this.modal().open();
      else this.modal().close();
    });

    this.frame = ko.observable(null);

    this.openedView = ko.observable(null);

    this.openedView.subscribe((v) => {
      if (v && v.size == 'trade') {
        this.url(this._tradeUrl);
      } else {
        this.url(this._url);
      }
    });
  }

  open(view) {
    this.openedView(view);
    this._opened(true);
  }

  changeViewSize(size) {
    this.openedView({
      size
    });
  }

  closeView() {
    this._opened(false);
    this.openedView(null);
  }
}
