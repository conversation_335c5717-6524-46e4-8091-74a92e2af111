<!-- ko let: { $ctx: $component } -->
<foquz-modal
  params="
    modal: modal,
    hide: true,
    replace: true
  "
  class="full-preview-modal"
>
  <div class="modal-body" data-bind="using: $ctx">
    <div class="survey-question__view-modal-background"></div>

    <div class="survey-question__view-modal-header">
      <div class="overflow-hidden">
        <div
          class="survey-question__view-modal-title text-nowrap"
          data-bind="text: title"
        ></div>
        <div
          class="survey-question__view-modal-sub-title"
          data-bind="text: subtitle"
        ></div>
      </div>

      <div class="d-flex align-items-center flex-shrink-0 mx-4">
        <button
          class="f-icon-button f-icon mx-3"
          data-bind="css: {
                  'f-color-primary': openedView() && openedView().size === 'desktop',
                  'f-color-service': !openedView() || openedView().size !== 'desktop',
              }, click: function() { changeViewSize('desktop'); },
              attr: {
                title: $ctx.translator.t('Просмотр опроса на компьютере')
              }"
        >
          <svg-icon
            params="name: 'viewport-desktop'"
            class="svg-icon--lg"
          ></svg-icon>
        </button>

        <button
          class="f-icon-button f-icon mx-3"
          data-bind="css: {
                  'f-color-primary': openedView() && openedView().size === 'tablet',
                  'f-color-service': !openedView() || openedView().size !== 'tablet',
              }, click: function() { changeViewSize('tablet'); }, attr: {
                title: $ctx.translator.t('Просмотр опроса на планшете')
              }"
        >
          <svg-icon
            params="name: 'viewport-tablet'"
            class="svg-icon--lg"
          ></svg-icon>
        </button>

        <button
          class="f-icon-button f-icon mx-3"
          data-bind="css: {
                  'f-color-primary': openedView() && openedView().size === 'mobile',
                  'f-color-service': !openedView() || openedView().size !== 'mobile',
              }, click: function() { changeViewSize('mobile'); }, attr: {
                title: $ctx.translator.t('Просмотр опроса на мобильных устройствах')
              }"
        >
          <svg-icon
            params="name: 'viewport-mobile'"
            class="svg-icon--lg"
          ></svg-icon>
        </button>

        <!-- ko ifnot: isAuto -->
        <button
          class="f-icon-button f-icon mx-3"
          data-bind="css: {
                  'f-color-primary': openedView() && openedView().size === 'trade',
                  'f-color-service': !openedView() || openedView().size !== 'trade',
              }, click: function() { changeViewSize('trade'); }, attr: {
                title: $ctx.translator.t('Просмотр опроса для планшета в торговой точке')
              }"
        >
          <svg-icon
            params="name: 'viewport-trade'"
            class="svg-icon--lg"
          ></svg-icon>
        </button>
        <!-- /ko -->
      </div>

      <div class="flex-shrink-0">
        <a
          class="btn survey-question__view-modal-open-in-new-tab-button"
          target="_blank"
          data-bind="attr: { href: openInNewTabUrl }, text: $ctx.translator.t('Открыть в новой вкладке')"
        >
        </a>

        <button
          type="button"
          class="btn survey-question__view-modal-close-button"
          data-bind="click: function() { closeView(); }"
          data-toggle="tooltip, tooltipText: $ctx.translator.t('Закрыть')"
        ></button>
      </div>
    </div>

    <div
      class="survey-question__view-modal-content active"
      data-bind="
          css: openedView() ? 'survey-question__view-modal-content--' + openedView().size : ''
      "
    >
      <iframe
        class="survey-question__view-modal-page"
        id="full-screen"
        data-bind="attr: { src: url() + '&fullpreview=1' }, element: frame"
      >
      </iframe>
    </div>
  </div>
</foquz-modal>
<!-- /ko -->
