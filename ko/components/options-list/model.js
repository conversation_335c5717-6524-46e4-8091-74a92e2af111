import { FoquzComponent } from 'Models/foquz-component';

let unique = 1;

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.unique = unique++;

    this.config = {
      sort: true,
      remove: true,
      removeSingle: false,
      removeOld: true,
      add: true,
      addOnEnter: true,
      addText: _t('Добавить вариант'),
      randomId: true,
      maxLength: 250,
      showCounter: false
    };

    this.formControlErrorStateMatcher =
      params.formControlErrorStateMatcher ||
      commonFormControlErrorStateMatcher(ko.observable(false));

    this.value = ko.observableArray([]).extend({
      validation: {
        validator: (v) => {
          let invalidOption = v.find((o) => {
            console.log('OPTION', o);
            return !o.text.isValid();
          });
          return !invalidOption;
        }
      }
    });
    this.ids = ko.computed(() => {
      return this.value().map((o) => o.id);
    });

    this.activeOption = ko.observable(null);

    if (!this.value().length) {
      this.addOption();
    }

    this.canAdd = ko.pureComputed(() => {
      return this.config.add;
    });

    this.isValid = this.value.isValid;

    if ('options' in params) {
      console.log(
        ko.isObservable(params.options),
        ko.toJS(params.options),
        ko.unwrap(params.options)
      );
      this.setData(ko.unwrap(params.options));
      if (ko.isObservable(params.options)) {
        params.options.subscribe((v) => {
          this.setData(v);
        });
      }
    }
  }

  getId() {
    if (!this.config.randomId) return 0;

    if (this.ids().some((id) => id == this.unique)) {
      this.unique++;
      return this.getId();
    }

    return this.unique++;
  }

  onEnter(optionIndex) {
    if (!this.config.addOnEnter) return;
    if (!this.canAdd()) return;

    if (!this.value()[optionIndex].text()) return;

    let option = this.createOption();
    this.value.splice(optionIndex + 1, 0, option);
    this.activeOption(option);
  }

  canRemove(option) {
    if (!this.config.remove) return false;
    if (this.value().length == 1 && !this.config.removeSingle) return false;
    if (!option.isNew() && !this.config.removeOld) return false;
    return true;
  }

  createOption() {
    return {
      id: this.getId(),
      text: ko.observable('').extend({
        required: {
          message: _t('Обязательное поле')
        }
      }),
      isNew: ko.observable(true),
      el: ko.observable(null)
    };
  }

  addOption() {
    let option = this.createOption();
    this.value.push(option);
    this.activeOption(option);
  }

  removeOption(option) {
    this.value.remove(option);
  }

  getData() {
    return this.value().map((o) => {
      return {
        id: o.id,
        text: o.text()
      };
    });
  }

  setData(data) {
    this.value(
      data.map((option) => {
        console.log('option', option);
        return {
          id: option.id,
          text: ko.observable(option.text).extend({
            required: {
              message: _t('Обязательное поле')
            }
          }),
          isNew: ko.observable(false),
          el: ko.observable(false)
        };
      })
    );

    if (!this.value().length) {
      this.addOption();
    }
  }

  save() {
    this.value.forEach((o) => o.isNew(false));
  }

  afterMove() {}
}
