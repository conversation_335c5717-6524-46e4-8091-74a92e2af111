<div data-bind="foquzSortable: {
  data: value,
  as: 'option',
  afterMove: function() {
    afterMove();
  },
  connectClass: false,
  options: {
    handle: '.drag-handle',
  }
}"
     class="styled">

  <div class="options-list-item"
       data-bind="descendantsComplete: function() {
    if ($parent.activeOption() && $parent.activeOption() === option) {
      option.el().focus();
    }
  }">
    <div class="options-list-item__wrapper">
      <!-- ko if: $parent.config.sort && $parent.value().length > 1 -->
      <div class="drag-handle">
        <fc-icon params="name: 'drag-arrow', width: 16, height: 20"></fc-icon>
      </div>
      <!-- /ko -->
      <div class="options-list-item__value">
        <!-- ko if: $parent.config.showCounter -->
        <foquz-chars-counter params="value: option.text, max: $parent.config.maxLength"
                             data-bind="css: {
          'is-invalid': $parent.formControlErrorStateMatcher(option.text)
        }">
          <input type="text"
                 class="form-control"
                 data-bind="textInput: $parent.option.text, onEnter: function() {
            $parents[1].onEnter($index());
            return true;
          }, element: $parent.option.el">
        </foquz-chars-counter>
        <!-- /ko -->
        <!-- ko ifnot: $parent.config.showCounter -->
        <input type="text"
               class="form-control"
               data-bind="textInput: option.text, onEnter: function() {
                 $parent.onEnter($index());
                 return true;
               }, attr: { maxlength: $parent.config.maxLength }, css: {
                'is-invalid': $parent.formControlErrorStateMatcher(option.text)
              }, element: option.el">
        <!-- /ko -->

        <validation-feedback params="show: $parent.formControlErrorStateMatcher(option.text), text: option.text.error">
        </validation-feedback>

      </div>
      <!-- ko if: $parent.canRemove(option) -->
      <div class="options-list-item__remove">
        <button class="f-btn f-btn--square f-btn-danger" data-bind="click: function() { $parent.removeOption(option) }">
          <fc-icon params="name: 'times', size: 14"></fc-icon>
        </button>
      </div>
      <!-- /ko -->
    </div>

  </div>
</div>

<!-- ko if: canAdd -->
<button class="f-btn options-list-add"
        type="button"
        data-bind="click: function() {
  addOption();
}">
  <span class="options-list-add__icon">
    <fc-icon params="name: 'plus', color: 'white', size: 14"></fc-icon>
  </span>
  <span class="options-list-add__text"
        data-bind="text: config.addText"></span>
</button>
<!-- /ko -->
