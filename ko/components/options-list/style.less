@import 'Style/colors';

.options-list {
  display: block;
  overflow: hidden;
  

  &-item {
    margin-left: -20px;
    margin-right: -20px;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 8px;
    padding-bottom: 8px;
    margin-bottom: 8px;

    &:first-child {
      margin-top: -8px;
    }

    &:last-child {
      margin-bottom: 16px;
    }

    &__wrapper {
      display: flex;
      align-items: center;
    }

    .drag-handle {
      display: flex;
      align-items: center;
      padding-right: 16px;
      flex-shrink: 0;
    }

    &__value {
      flex-grow: 1;
    }

    &__remove {
      margin-left: 16px;
      flex-shrink: 0;
    }

    .form-error {
      position: absolute;
    }
  }

  &-add {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    height: 48px;
    border-radius: 4px;
    overflow: hidden;
    background: #f5f6fa;
    color: @f-color-primary;
    font-size: 16px;
    font-weight: 500;
    padding: 0;

    &__icon {
      display: flex;
      width: 48px;
      height: 48px;
      align-items: center;
      justify-content: center;
      background: @f-color-primary;
    }

    &__text {
      flex-grow: 1;
    }
  }
}
