import { FoquzComponent } from 'Models/foquz-component';
import { DialogsModule } from 'Utils/dialogs-module';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    DialogsModule(this);

    this.table = params.table;
    this.collection = this.table.collection;

    this.onEdit = params.onEdit;
    this.onRemove = params.onRemove;
  }

  editItem(item) {
    if (typeof this.onEdit == 'function') this.onEdit(item);
  }

  removeItem(item) {
    if (typeof this.onRemove == 'function') this.onRemove(item);
  }


}
