<table class="f-table table f-table--searchable">
  <thead>
    <tr>
      <th data-bind="component: {
        name: 'table-head-cell',
        params: {
          sortName: 'created_at',
          sort: table.sort,
          withSearch: true,
          searchValue: table.search.created_at,
          onSearch: function() { table.reset() }
        }
      }"
          width="170">Дата добавления</th>

      <th data-bind="component: {
        name: 'table-head-cell',
        params: {
          sortName: collection.titleField,
          sort: table.sort,
          withSearch: true,
          searchValue: table.search[collection.titleField],
          onSearch: function() { table.reset() }
        }
      }">
        <!-- ko text: $parent.collection.texts.itemName -->
        <!-- /ko -->
      </th>

      <th align="right">

      </th>

    </tr>
  </thead>
  <tbody>
    <!-- ko foreach: table.items -->
    <tr>
      <td width="150"
          data-bind="text: createdAt"></td>

      <td data-bind="text: name"></td>

      <td width="50"
          align="right">
        <div class="d-flex">
          <button class="button-ghost button-ghost-service mr-4"
                  type="button"
                  data-bind="tooltip, click: function() { $parent.editItem($data) }"
                  title="Редактировать">
            <svg-icon params="name: 'pencil'"></svg-icon>
          </button>

          <button class="button-ghost button-ghost-service"
                  type="button"
                  data-bind="tooltip, click: function() { $parent.removeItem($data) }"
                  title="Удалить">
            <svg-icon params="name: 'bin'"></svg-icon>
          </button>
        </div>
      </td>
    </tr>
    <!-- /ko -->
  </tbody>
</table>
