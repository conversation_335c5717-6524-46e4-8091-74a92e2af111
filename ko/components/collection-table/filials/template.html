<table class="f-table table f-table--searchable filials-table">
  <thead>
    <tr>
      <th
        data-bind="component: {
        name: 'table-head-cell',
        params: {
          sortName: 'created_at',
          sort: table.sort,
          withSearch: true,
          searchValue: table.search.created_at,
          onSearch: function() { table.reset() }
        }
      }"
        width="170"
      >
        Дата добавления
      </th>

      <th
        data-bind="component: {
        name: 'table-head-cell',
        params: {
          sortName: collection.titleField,
          sort: table.sort,
          withSearch: true,
          searchValue: table.search[collection.titleField],
          onSearch: function() { table.reset() }
        }
      }"
      >
        Название филиала
      </th>

      <th
        data-bind="component: {
        name: 'table-head-cell',
        params: {
          sortName: 'category',
          sort: table.sort,
          withSearch: true,
          searchValue: table.search.category,
          onSearch: function() { table.reset() }
        }
      }"
      >
        Категория
      </th>

      <th
        data-bind="component: {
          name: 'table-head-cell',
          params: {
            sortName: 'address',
            sort: table.sort,
            withSearch: true,
            searchValue: table.search.address,
            onSearch: function() { table.reset() }
          }
        }"
      >
        Адрес
      </th>

      <th
        data-bind="component: {
          name: 'table-head-cell',
          params: {
            sortName: 'crm_id',
            sort: table.sort,
            withSearch: true,
            searchValue: table.search.crm_id,
            onSearch: function() { table.reset() }
          }
        }"
      >
        Внешний ID
      </th>

      <th align="right"></th>
    </tr>
  </thead>
  <tbody>
    <!-- ko foreach: { data: table.items, as: 'item' } -->
    <!-- ko let: { map: ko.observable(null) } -->
    <tr class="filials-table-item">
      <td width="150" data-bind="text: item.createdAt"></td>

      <td>
        <div class="d-flex align-items-center">
          <!-- ko if: item.logo -->
          <div class="filials-table-item__logo">
            <img
              alt=""
              data-bind="attr: {
                  src: item.logoUrl
                }, fancyboxGalleryItem: {
                  gallery: [{ src: item.logoUrl() }],
                }"
            />
          </div>
          <!-- /ko -->
          <!-- ko ifnot: item.logo -->
          <div class="filials-table-item__logo filials-table-item__logo--empty">
            <svg-icon params="name: 'home'"></svg-icon>
          </div>
          <!-- /ko -->
          <span data-bind="text: item.name"></span>
        </div>
      </td>

      <td valign="middle">
        <span data-bind="text: item.categoryName"></span>
      </td>

      <td>
        <!-- ko if: item.address -->
        <span data-bind="text: item.address" class="mr-15p"></span>
        <!-- /ko -->

        <!-- ko if: item.googlePlaceId -->
        <button
          type="button"
          class="button-ghost mr-15p"
          data-bind="click: function() {
          map('google');
        }"
        >
          <svg-icon
            params="name: 'map-marker-google'"
            class="svg-icon--lg"
          ></svg-icon>
        </button>
        <!-- /ko -->

        <!-- ko if: item.yandexLocation -->
        <button
          type="button"
          class="button-ghost"
          data-bind="click: function() {
          map('yandex');
        }"
        >
          <svg-icon
            params="name: 'map-marker-yandex'"
            class="svg-icon--lg"
          ></svg-icon>
        </button>
        <!-- /ko -->
      </td>

      <td>
        <span data-bind="text: item.crm_id"></span>
      </td>

      <td width="50" align="right">
        <div class="d-flex">
          <button
            class="button-ghost button-ghost-service mr-4"
            type="button"
            data-bind="tooltip, click: function() { $parent.editItem($data) }"
            title="Редактировать"
          >
            <svg-icon params="name: 'pencil'"></svg-icon>
          </button>
          <button
            class="button-ghost button-ghost-service"
            type="button"
            data-bind="tooltip, click: function() { $parent.removeItem($data) }"
            title="Удалить"
          >
            <svg-icon params="name: 'bin'"></svg-icon>
          </button>
        </div>
      </td>
    </tr>
    <tr class="collapsed">
      <td colspan="4" class="p-0 border-0">
        <!-- ko template: {
          foreach: templateIf(map(), $data),
          afterAdd: slideAfterAddFactory(400),
          beforeRemove: slideBeforeRemoveFactory(400)
        } -->
        <div class="map-wrapper">
          <div class="map">
            <!-- ko if: map() == 'google' -->
            <google-map params="place: item.googlePlaceId"></google-map>
            <!-- /ko -->

            <!-- ko if: map() == 'yandex' -->
            <yandex-map params="place: item.yandexLocation"></yandex-map>
            <!-- /ko -->
          </div>
          <button
            class="map-wrapper__close"
            type="button"
            data-bind="click: function() { map(null) }"
          >
            <svg-icon
              params="name: 'times'"
              class="f-color-service svg-icon-sm"
            ></svg-icon>
          </button>
        </div>
        <!-- /ko -->
      </td>
    </tr>
    <!-- /ko -->
    <!-- /ko -->
  </tbody>
</table>
