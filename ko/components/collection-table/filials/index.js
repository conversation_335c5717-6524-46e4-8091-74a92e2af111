import { ViewModel } from './model';
import html from './template.html';

import './style';

import 'Components/map/google-map';
import 'Components/map/yandex-map';


ko.components.register('filials-collection-table', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('collection-table');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
