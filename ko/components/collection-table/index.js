import { ViewModel } from './model';
import html from './template.html';

ko.components.register('collection-table', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('collection-table');

      return new ViewModel(params, element);
    },
  },
  template: html,
});

import './fines';
import './filials';
import './themes';
