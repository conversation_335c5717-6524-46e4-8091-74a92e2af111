<table class="f-table table f-table--searchable filials-table">
  <thead>
    <tr>
      <th data-bind="component: {
        name: 'table-head-cell',
        params: {
          sortName: 'createdAt',
          sort: table.sort,
          withSearch: true,
          searchValue: table.search.createdAt,
          onSearch: function() { table.reset() }
        }
      }"
          width="170">Дата добавления</th>


      <th data-bind="component: {
        name: 'table-head-cell',
        params: {
          sortName: collection.titleField,
          sort: table.sort,
          withSearch: true,
          searchValue: table.search[collection.titleField],
          onSearch: function() { table.reset() }
        }
      }" width="220">Наименование темы</th>

      <th data-bind="component: {
          name: 'table-head-cell',
          params: {
            sortName: 'buttonText',
            sort: table.sort,
            withSearch: true,
            searchValue: table.search.buttonText,
            onSearch: function() { table.reset() }
          }
        }">Название кнопки</th>

      <th align="right">

      </th>

    </tr>
  </thead>
  <tbody>
    <!-- ko foreach: { data: table.items, as: 'item' } -->
    <tr class="themes-table-item">
      <td valign="middle" width="150"
          data-bind="text: item.createdAt"></td>

      <td valign="middle">
        <div class="d-flex align-items-center">
          <div class="themes-table-item__logo">
            <!-- ko if: item.icon() -->
            <img alt=""
                 data-bind="attr: {
                  src: item.icon
                }, fancyboxGalleryItem: {
                  gallery: [{ src: item.icon() }],
                }">
            <!-- /ko -->
          </div>
          <span data-bind="text: item.name"></span>
        </div>
      </td>


      <td valign="middle">
        <span data-bind="text: item.button"
              class="mr-15p"></span>
      </td>

      <td valign="middle" width="50"
          align="right">
        <div class="d-flex">

          <button class="button-ghost button-ghost-service mr-4"
                  type="button"
                  data-bind="tooltip, click: function() { $parent.editItem($data) }"
                  title="Редактировать">
            <svg-icon params="name: 'pencil'"></svg-icon>
          </button>
          <button class="button-ghost button-ghost-service"
                  type="button"
                  data-bind="tooltip, click: function() { $parent.removeItem($data) }"
                  title="Удалить">
            <svg-icon params="name: 'bin'"></svg-icon>
          </button>
        </div>
      </td>
    </tr>
    <!-- /ko -->
  </tbody>
</table>
