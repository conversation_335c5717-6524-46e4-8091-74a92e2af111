# Контейнер для модальных окон

```
<foquz-modals-container
  params="
    modals: ko.observableArray([]),
    closeAll: true
  ">
</foquz-modals-container>
```

## Параметры

**modals**

Массив модальных окон

* `name` - имя компонента модального окна
* `params` - параметры для компонента модального окна

**closeAll**

Кнопка Закрыть все модальные окна

## Открыть модальное окно

```
let modals = ko.observableArray([]);
modals.push({
  name: 'modal-component-name',
  params: {}
});
```
