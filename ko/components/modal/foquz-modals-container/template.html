
<!-- ko descendantsComplete: $component.onRender.bind($component) -->
<div class="foquz-modals-container" data-bind="attr: { 'data-id': unique }">
  <!-- ko foreach: formattedModals -->
  <div class="foquz-modal-wrapper"
       data-bind="
        css: {
          'foquz-modal-wrapper--single': $parent.modals().length == 1,
          'foquz-modal-wrapper--last': $index() == $parent.modals().length - 1,
        },
        component: {
          name: $data.name,
          params: $data.params
        },
        event: {
          'hidden.bs.modal': function() {
            $parent.removeModal($data);
          }
        }">
  </div>
  <!-- /ko -->

  <!-- ko if: closeAll && modals().length > 1 -->
  <div class="foquz-modals-container__close-all">
    <button data-bind="click: removeAll">Закрыть все</button>
  </div>
  <!-- /ko -->
</div>
<!-- /ko -->
