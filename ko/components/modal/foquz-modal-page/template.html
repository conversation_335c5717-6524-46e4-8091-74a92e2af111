<div class="modal-dialog" data-bind="event: {
  'hidden.bs.modal': function() {
    $parent.removeModal($data);
  }
}">
  <div class="foquz-modal-page__mask" data-bind="click: function() { close() }"></div>

  <div class="modal-content">
    <button class="foquz-modal-page__close" data-bind="click: function() { close() }">
      &times;
    </button>

    <!-- ko template: { nodes: $componentTemplateNodes } -->
    <!-- /ko -->
  </div>


</div>
