# Компонент модального окна

Вертикально центрированное модальное окно.

```
<foquz-modal
  params="
    modal: ko.observable(null),
    hide: true,
    replace: true
  ">

  <div class="modal-body"></div>

</foquz-modal>
```

## Параметры

**modal**

Контроллер модального окна

**hide**

Скрыть модальное окно при создании

**replace**

Переместить модальное окно вниз тега `body`. (Для статических окон)

**onClose**

Коллбэк при закрытии


## Контроллер

Свойства:

* `$modal` - html-элемент, на события которого можно подписаться

Методы:

* `open`
* `close`
* `openOuterModal`
