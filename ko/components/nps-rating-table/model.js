import { FoquzComponent } from 'Models/foquz-component';
import { doubleGradientCss } from 'Utils/color/gradient';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    let model = params.model;

    this.gradient = doubleGradientCss({
      start: '#F96261',
      end: '#00C968',
      neutral: '#B9A0A8',
      count: model.points().length
    });

    this.groups = model.groups;
  }
}
