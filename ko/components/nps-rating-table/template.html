<table class="table f-table ">
  <thead>
    <tr>
      <th data-bind="text: _t('Оценки')"></th>
      <th align="right"  data-bind="text: _t('оценок в диапазоне')">

      </th>
      <th width="120" align="right"  data-bind="text: _t('оценок')">

      </th>
    </tr>
  </thead>
  <tbody>
    <!-- ko foreach: { data: groups, as: 'group'} -->
    <!-- ko foreach: { data: group.points, as: 'point' } -->
    <tr data-bind="attr: {
      'data-group-index': pointIndex()
    }">
      <td data-bind="css: {
        'empty': pointIndex() > 0
      }">
        <div class="d-flex align-items-center">
          <span class="point" data-bind="text: point.value, style: {
            backgroundColor: $component.gradient[point.value - 1]
          }"></span>
          <!-- ko if: pointIndex() == 0 -->
          <span class="font-weight-700" data-bind="text: group.name"></span>
          <!-- /ko -->
        </div>
      </td>
      <td data-bind="css: {
        'empty': pointIndex() > 0
      }">
        <!-- ko if: pointIndex() == 0 -->
        <div class="d-flex justify-content-end">
          <span data-bind="text: group.count"></span>/
          <span data-bind="text: group.percent + '%'"></span>
        </div>
        <!-- /ko -->
      </td>
      <td>
        <div class="d-flex justify-content-end">
          <span data-bind="text: point.count"></span>/
          <span data-bind="text: point.percent + '%'"></span>
        </div>
      </td>
    </tr>
    <!-- /ko -->
    <!-- /ko -->
  </tbody>
</table>
