<div class="complex-table__container" data-bind="descendantsComplete: $component.render.bind($component),">
  <!-- ko if: fixedHeader -->
  <div class="complex-table__target" data-bind="element: stickyTarget"></div>
  <!-- /ko -->

  <!-- ko if: horizontalScroll && fixedHeader -->
  <div class="complex-table__head" data-bind="element: headTableContainer">

  </div>
  <!-- /ko -->


  <div class="complex-table__body" data-bind="element: bodyTableContainer, ref: scrollbar, nativeScrollbar, nativeScrollbarDisabled: !horizontalScroll,">
    <!-- ko template: { nodes: $componentTemplateNodes } -->
    <!-- /ko -->
  </div>


  <!-- ko if: horizontalScroll -->
  <div class="complex-table__controls">
    <div class="complex-table__control complex-table__control--left">
      <button data-bind="click: function() { scroll(-250) }">
        <svg-icon params="name: 'chevron-left'"></svg-icon>
      </button>
    </div>
    <div class="complex-table__control complex-table__control--right">
      <button data-bind="click: function() { scroll(250) }">
        <svg-icon params="name: 'chevron-right'"></svg-icon>
      </button>
    </div>
  </div>
  <!-- /ko -->

</div>
