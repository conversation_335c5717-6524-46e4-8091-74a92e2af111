<div
  class="table-head-cell__name"
  data-bind="click: sortBy, css: {
       'cursor-pointer': sortName,
       'foq-table__head-cell-title--active': sortName === sort.order()
     }"
>
  <!-- ko template: { nodes: $componentTemplateNodes } -->
  <!-- /ko -->

  <sort-icon
    params="name: sortName, sort: sort.order, asc: sort.asc"
  ></sort-icon>

  <!-- ko if: withSearch && searchValue && searchValue() -->
  <filter-icon params="value: searchValue"></filter-icon>
  <!-- /ko -->
</div>

<!-- ko if: withSearch && !!searchValue -->
<input
  class="f-table__search"
  type="text"
  data-bind="textInput: searchValue, onEnter: $component.search.bind($component), attr: {placeholder: placeholder}"
/>
<!-- /ko -->
