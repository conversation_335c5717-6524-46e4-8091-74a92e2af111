<!-- ko let: { $interactiveTable: $component, $nodes: $componentTemplateNodes } -->


<!-- ko if: empty -->
<div class="interactive-table__empty"
     data-bind="html: emptyText"></div>
<!-- /ko -->


<!-- ko ifnot: empty -->
<!-- ko if: horizontal -->
<div class="interactive-table__table"
     data-bind="using: table">
  <!-- ko template: { nodes: $nodes } -->
  <!-- /ko -->

  <!-- ko if: $parent.pagination -->
  <observer-target class="table-observer table-observer--right"
                   params="action: function() {
  $parent.onIntersection()
}, config: $parent.observerConfig"></observer-target>
  <!-- /ko -->
</div>
<!-- /ko -->

<!-- ko ifnot: horizontal -->
<div class="interactive-table__table"
     data-bind="using: table">

  <complex-table params="scroll: $interactiveTable.scroll, fixedHeader: $interactiveTable.fixedHeader">
    <!-- ko using: $interactiveTable.table -->
    <!-- ko template: { nodes: $nodes } -->
    <!-- /ko -->
    <!-- /ko -->
  </complex-table>


</div>
<!-- /ko -->
<!-- /ko -->

<!-- ko if: table.initing() || table.loading() -->
<div class="interactive-table__loading">
  <fc-spinner class="f-color-primary"></fc-spinner>
</div>
<!-- /ko -->


<!-- ko ifnot: empty -->
<!-- ko if: !table.initing() && !table.loading() && !table.items().length -->
<div class="interactive-table__not-found"
     data-bind="text: $component.notFoundText"></div>
<!-- /ko -->

<!-- ko if: pagination && !horizontal -->
<observer-target class="table-observer"
                 params="action: function() {
  $component.onIntersection()
}, config: observerConfig"></observer-target>
<!-- /ko -->
<!-- /ko -->



<!-- /ko -->
