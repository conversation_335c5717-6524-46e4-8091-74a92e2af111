import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import './file-loader'

ko.components.register('request-form-field', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('request-form-field');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
