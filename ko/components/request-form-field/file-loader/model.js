/**
 * field {RequestFormField}
 * readMode: true|false
 */

import { FoquzComponent } from 'Models/foquz-component';
import { FoquzMultipleLoader } from 'Models/file-loader/multiple-loader';
import { Delay } from 'Utils/timer/delay';
import { getSize } from 'Utils/file';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher;

    this.readMode = params.readMode;
    this.invalid = params.invalid;

    this.loader = new FoquzMultipleLoader();
    this.files = ko.observableArray([]);
    this.loadingFiles = ko.observableArray([]);

    this.loader.on('select', ({ file }) => {
      this.addFile(file);
    });

    this.gallery = ko.computed(() => {
      return this.files().map((file) => {
        return {
          src: file.preview()
        };
      });
    });

    ko.applyBindingsToNode(element, {
      css: {
        'request-field-loader--invalid': this.invalid
      }
    })
  }

  addFile(file) {
    let loadingFile = {
      name: file.name,
      size: getSize(file.size)
    };
    this.loadingFiles.push(loadingFile);
    this.emitEvent('add.file', file);

    let delay = Delay(2000);
    FoquzMultipleLoader.getPreview(file).then((url) => {
      delay.then(() => {
        this.loadingFiles.remove(loadingFile);
        this.files.push({
          file,
          preview: ko.observable(url),
          name: file.name,
          size: file.size
        });
      });
    });
  }

  removeFile(file) {
    this.files.remove(file);
    this.emitEvent('remove.file', file.file);
  }

  reset() {
    this.files([]);
  }
}
