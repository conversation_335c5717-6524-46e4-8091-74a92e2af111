@import 'Style/colors';

.request-field-loader {
  display: block;
  padding-top: 10px;


  &__load-btn {
    .fc-btn-b {
      height: 30px;
      font-weight: 500;
      font-size: 13px;
    }
  }

  &__files-list {
    display: flex;
    flex-wrap: wrap;
    margin-left: -14px;
    margin-right: -14px;
  }
  &__file {
    margin-bottom: 20px;
    margin-left: 14px;
    margin-right: 14px;
  }

  &-control {
    position: relative;

    &--desktop {
      display: none;
      border: 2px dotted #9bb0fb;
      border-radius: 6px;
      padding: 16px 20px 20px;
    }
  }

  &--invalid {
    .request-field-loader-control {
      &--desktop {
        border-color: @f-color-danger;
      }
    }
  }

  @media screen and (min-width: 768px) {
    &-control--desktop {
      display: block;
    }
    &-control--mobile {
      display: none;
    }
  }
}
