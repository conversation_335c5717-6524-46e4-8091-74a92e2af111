<div class="request-field-loader__wrapper">
  <div class="request-field-loader__files-list">

    <!-- ko foreach: files -->
    <div class="request-field-loader__file">
      <fc-media-wrapper
        params="width: 120, height: 120,
            preview: $data.preview,
            gallery: $parent.gallery,
            index: $index(),
            remove: true,
            removeOnHover: false,
            label: file.name"
          data-bind="event: {
            remove: function() { $parent.removeFile($data); }
          }"></fc-media-wrapper>
    </div>
    <!-- /ko -->
  </div>

  <div class="request-field-loader-control request-field-loader-control--mobile">
    <!-- ko if: loadingFiles().length -->
    <fc-spinner params="mode: 'gradient'"
                class="f-color-primary mb-2"></fc-spinner>
    <!-- ko foreach: loadingFiles -->
    <div class="text-center">
      <div data-bind="text: name"></div>
      <div data-bind="text: size"></div>
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <!-- ko ifnot: loadingFiles().length -->
    <fc-button class="request-field-loader__load-btn" params="label: 'Загрузить файл',
              click: function() { loader.open() },
              block: true,"></fc-button>
    <div class="text-center f-color-service f-fs-1 mt-10p">Максимальный размер файла — 5 Мб</div>
    <!-- /ko -->
  </div>

  <div data-bind="dnd: function(files) {
    loader.loadFiles(files);
  }"
        class="request-field-loader-control request-field-loader-control--desktop">
    <!-- ko ifnot: loadingFiles().length -->
    <div class="text-center f-color-service f-fs-2-5">Перетащите файлы фото/видео сюда или воспользуйтесь
      обозревателем</div>
    <div class="text-center f-color-service f-fs-1">Максимальный размер файла — 5 Мб</div>
    <div class="d-flex justify-content-center mt-10p">

      <fc-button class="request-field-loader__load-btn" params="label: 'Обзор', click: function() {
        loader.open();
      }"></fc-button>
    </div>
    <!-- /ko -->

    <!-- ko if: loadingFiles().length -->
    <fc-spinner params="mode: 'gradient'"
                class="f-color-primary mb-2"></fc-spinner>
    <!-- ko foreach: loadingFiles -->
    <div class="text-center f-fs-1">
      <div data-bind="text: name"></div>
      <div data-bind="text: size"></div>
    </div>
    <!-- /ko -->
    <!-- /ko -->

  </div>
  <div class="error-wrapper">
    <!-- ko foreach:loader.errors -->
    <file-loader-error params="error: $data"></file-loader-error>
    <!-- /ko -->
  </div>
</div>
