<div>
  <div class="form-group">
    <fc-label
      params="text: field.label, required: field.isRequired && !readMode(), hint: field.description, style: readMode() ? 'sm' : ''"
    ></fc-label>

    <div data-bind="if: field.isInput">
      <fc-input
        params="value: field.value,
              mask: field.mask,
              placeholder: field.placeholder,
              invalid: formControlErrorStateMatcher(field.value),
              counter: !field.mask,
              maxlength: field.mask ? null : field.maxLength,
              readMode: readMode"
      ></fc-input>

      <fc-error
        params="show: formControlErrorStateMatcher(field.value), text: field.value.error"
      ></fc-error>
    </div>

    <div data-bind="if: field.isTextarea">
      <fc-textarea
        params="counter: field.maxLength, maxlength: field.maxLength, invalid: formControlErrorStateMatcher(field.value), value: field.value, placeholder: field.placeholder, readMode: readMode"
      >
      </fc-textarea>

      <fc-error
        params="show: formControlErrorStateMatcher(field.value), text: field.value.error"
      >
      </fc-error>
    </div>

    <div data-bind="if: field.isDate">
      <fc-calendar
        params="value: field.value, invalid: formControlErrorStateMatcher(field.value), readMode: readMode, showDropdowns: true, mobileView: 'modal', placeholder: field.placeholder"
      >
      </fc-calendar>

      <fc-error
        params="show: formControlErrorStateMatcher(field.value), text: field.value.error"
      >
      </fc-error>
    </div>

    <div data-bind="if: field.isSelect">
      <!-- ko if: field.isClient -->
      <fc-autocomplete
        params="invalid: formControlErrorStateMatcher(field.value),
              value: field.value,
              getOptions: field.getClients,
              fields: ['name', 'email', 'phone'],
              placeholder: field.placeholder,
              searchPlaceholder: 'Поиск контакта по ФИО, телефону или email', readMode: readMode"
      >
        <template data-slot="option">
          <div
            data-bind="html: $data.name  + ' ' + $data.email + ' ' + $data.phone"
          ></div>
        </template>
        <template data-slot="result">
          <div
            data-bind="html: $data.name  || $data.email || $data.phone"
          ></div>
        </template>
      </fc-autocomplete>
      <!-- /ko -->

      <!-- ko ifnot: field.isClient -->
      <fc-select
        params="multiple: field.isMultiple,
            invalid: formControlErrorStateMatcher(field.isMultiple ? field.values : field.value),
            value: field.isMultiple ? field.values : field.value,
            options: field.options.map(function(o) {
              return {
                ...o,
                text: o.name
              }
            }),
            clearable: true,
            fields: { text: 'name' },
            optionsForSearch: field.name === 'priority' ? 10 : 0,
            placeholder: field.placeholder, readMode: readMode"
      ></fc-select>
      <!-- /ko -->

      <!-- ko ifnot: field.isMultiple -->
      <fc-error
        params="show: formControlErrorStateMatcher(field.value), text: field.value.error"
      >
      </fc-error>
      <!-- /ko -->

      <!-- ko if: field.isMultiple -->
      <fc-error
        params="show: formControlErrorStateMatcher(field.values), text: field.values.error"
      >
      </fc-error>
      <!-- /ko -->
    </div>

    <div data-bind="if: field.isFile">
      <request-field-loader
        params="ref: loader, readMode: readMode,
          invalid: formControlErrorStateMatcher(field.files)"
        data-bind="event: {
        'add.file': function(_, e, file) {
          addFile(file)
        },
        'remove.file': function(_, e, file) {
          removeFile(file)
        }
      }"
      ></request-field-loader>

      <fc-error
        params="show: formControlErrorStateMatcher(field.files), text: field.files.error"
      >
      </fc-error>
    </div>
  </div>
</div>
