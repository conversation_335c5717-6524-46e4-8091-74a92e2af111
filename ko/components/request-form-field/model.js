/**
 * field {RequestFormField}
 * readMode: true|false
 */

import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    element.setAttribute('data-type', params.field.type);
    element.setAttribute('data-name', params.field.name);

    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher;

    this.field = params.field;
    this.readMode = ko.computed(() => ko.toJS(params.readMode));

    this.loader = ko.observable(null);

    this.field.files.subscribe((v) => {
      if (!v.length) {
        let loader = this.loader();
        if (loader) loader.reset();
      }
    });
  }

  addFile(file) {
    this.field.files.push(file);
  }

  removeFile(file) {
    this.field.files.remove(file);
  }
}
