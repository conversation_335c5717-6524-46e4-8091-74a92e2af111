<div class="pool-promocodes">
  <foquz-modals-container params="modals: modals"></foquz-modals-container>

  <header class="pool-promocodes__header mb-4">
    <h2 class="f-h2 pb-0">
      <!-- ko if: type !== 'reusable'-->
      Промокоды
      <!-- /ko -->

      <!-- ko if: type === 'reusable'-->
      Отправленный купон
      <!-- /ko -->
    </h2>

    <!-- ko if: type !== 'reusable'-->
    <div>
      <!-- ko if: isLoadingFromCsv() -->
      <div
        data-bind="component: { name: 'progress-bar', params: { value: loadingFromCsvProgress } }"
        class="ml-3"
      ></div>
      <!-- /ko -->

      <!-- ko if: !isLoadingFromCsv() -->
      <div class="dropdown">
        <button
          class="
            btn
            button-add button-add--with-dropdown
            clients__new-contact-button
          "
          type="button"
          data-toggle="dropdown"
          data-flip="false"
        >
          Добавить промокоды
        </button>

        <div class="dropdown-menu dropdown-menu-left">
          <a class="dropdown-item" data-bind="click: create"
            >Добавить вручную</a
          >
          <a class="dropdown-item" data-bind="click: loadFromCSV">
            Добавить из файла CSV
          </a>
          <a class="dropdown-item" href="/csv/coupons.csv" download
            >Скачать пример CSV файла</a
          >
        </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </header>

  <hr class="mx-n30p" />
  <div class="d-flex">
    <div class="form-group dense-form-group mr-20p">
      <label class="form-label">
        <!-- ko if: type !== 'reusable'-->
        ФИО, телефон, email, промокод
        <!-- /ko -->
        <!-- ko if: type === 'reusable'-->
        ФИО, телефон, email
        <!-- /ko -->
      </label>
      <input
        class="form-control"
        type="text"
        data-bind="textInput: filters.query"
        placeholder="Любые"
      />
    </div>
    <div class="form-group dense-form-group">
      <!-- ko if: type !== 'reusable'-->
      <label class="form-label">Промокоды созданы</label>
      <!-- /ko -->
      <!-- ko if: type === 'reusable'-->
      <label class="form-label">Промокоды отправлены</label>
      <!-- /ko -->
      <period-picker
        params="ranges: true, value: filters.period, allowClear: true, autosize: true"
      ></period-picker>
    </div>
    <div class="ml-auto pl-20p d-flex align-items-center">
      <button
        class="f-btn f-btn-link mr-2"
        data-bind="click: function() { resetFilters() }"
      >
        Сбросить
      </button>
      <button
        class="f-btn f-btn-success f-btn-lg"
        data-bind="click: function() {applyFilters()}"
      >
        Применить
      </button>
    </div>
  </div>

  <hr class="mx-n30p" />

  <div class="w-100" data-bind="nativeScrollbar">
    <!-- ko if: codes().length -->
    <table class="table foq-table foq-table--filterable pool-promocodes-table">
      <thead>
        <tr>
          <!-- ko if: type !== 'reusable'-->
          <th>
            <div class="foq-table__head-cell-title">
              <div class="foq-table__head-cell-name">Промокод</div>
            </div>
          </th>
          <!-- /ko -->
          <!-- ko if: type !== 'reusable'-->
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('created_at'); }"
              >
                Создан
                <sort-icon
                  params="name: 'created_at', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>
          <!-- /ko -->
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('sended_at'); }"
              >
                Отправлен
                <sort-icon
                  params="name: 'sended_at', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('answered'); }"
              >
                Получен ответ
                <sort-icon
                  params="name: 'answered', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('poll'); }"
              >
                Опрос
                <sort-icon
                  params="name: 'poll', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('mailing'); }"
              >
                Рассылка
                <sort-icon
                  params="name: 'mailing', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('type'); }"
              >
                Способ отправки
                <sort-icon
                  params="name: 'type', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('client'); }"
              >
                Клиент
                <sort-icon
                  params="name: 'client', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('order'); }"
              >
                Заказ
                <sort-icon
                  params="name: 'order', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>
          <th>
            <div class="foq-table__head-cell-title cursor-pointer">
              <div
                class="foq-table__head-cell-name"
                data-bind="click: function() { sortBy('used'); }"
              >
                Использован
                <sort-icon
                  params="name: 'used', sort: sortModel.order, asc: sortModel.asc"
                ></sort-icon>
              </div>
            </div>
          </th>

          <th width="20"></th>

        </tr>
      </thead>
      <tbody>
        <!-- ko foreach: { data: codes, as: '$promocode' } -->
        <tr>
          <!-- ko if: $parent.type !== 'reusable'-->
          <td>
            <!-- ko text: code -->
            <!-- /ko -->
          </td>
          <!-- /ko -->
          <!-- ko if: $parent.type !== 'reusable'-->
          <td>
            <!-- ko text: createdAt -->
            <!-- /ko -->
          </td>
          <!-- /ko -->
          <td>
            <!-- ko text: sendedAt -->
            <!-- /ko -->
          </td>
          <td>
            <!-- ko text: answered -->
            <!-- /ko -->
          </td>
          <td>
            <div></div>
            <!-- ko if: hasPoll -->
            <!-- ko if: poll.isArchived || poll.deleted -->
            <span
              class="f-fs-1 font-weight-500"
              data-bind="text: poll.name"
            ></span>
            <!-- /ko -->
            <!-- ko ifnot: poll.isArchived || poll.deleted -->
            <a
              class="f-fs-1 font-weight-500"
              data-bind="text: poll.name, attr: {
                        href: pollLink
                      }"
            ></a>
            <!-- /ko -->
            <!-- /ko -->
          </td>
          <td>
            <!-- ko if: hasMailing -->
            <!-- ko if: mailing.isArchived -->
            <span
              class="f-fs-1 font-weight-500"
              data-bind="text: mailing.name"
            ></span>
            <!-- /ko -->
            <!-- ko ifnot: mailing.isArchived -->
            <a
              class="f-fs-1 font-weight-500"
              data-bind="text: mailing.name, attr: {
                        href: mailingLink
                      }"
            ></a>
            <!-- /ko -->
            <!-- /ko -->
          </td>
          <td>
            <!-- ko if: hasMailing -->
            <div class="d-flex align-items-center">
              <span
                class="f-icon f-icon-channel mr-2"
                data-bind="css: 'f-icon-channel--' + mailingChannelType"
              >
                <svg>
                  <use
                    data-bind="attr: {
                                href: '#channel-icon-' + mailingChannelType
                              }"
                  ></use>
                </svg>
              </span>
              <span data-bind="text: mailing.activeChannel"></span>
            </div>
            <!-- /ko -->
            <!-- ko ifnot: hasMailing -->
            <!-- ko text: sendType -->
            <!-- /ko -->
            <!-- /ko -->
          </td>
          <td>
            <!-- ko if: hasClient -->
            <a
              class="f-fs-1 font-weight-500"
              href="javascript:void(0)"
              data-bind="click: function() {
              $parent.openClientCard(client.id);
            }, text: client.fio"
            ></a>
            <!-- /ko -->
            <!-- ko ifnot: hasClient -->
            —
            <!-- /ko -->
          </td>
          <td>
            <!-- ko if: hasOrder -->
            <a
              target="_blank"
              class="f-fs-1 font-weight-500"
              data-bind="attr: { href: orderLink }, text: order.id"
            ></a>
            <br />
            <span class="f-fs-1" data-bind="text: orderDate"></span>
            <!-- /ko -->
            <!-- ko ifnot: hasOrder -->
            —
            <!-- /ko -->
          </td>
          <td>
            <!-- ko if: hasUsedForOrder -->
            <span
              class="f-fs-1 font-weight-500"
              data-bind="text: usedForOrder.id"
            ></span>
            <span class="f-fs-1" data-bind="text: usedDate"></span>
            <!-- ko if: clientWhoUsed -->
            <a
              class="f-fs-1 font-weight-500"
              href="javascript:void(0)"
              data-bind="click: function() {
              $parent.openClientCard(clientWhoUsed.id)
            }, text: clientWhoUsed.fio"
            ></a>
            <!-- /ko -->
            <!-- /ko -->
            <!-- ko ifnot: hasUsedForOrder -->
            —
            <!-- /ko -->
          </td>
          <td align="right">
            <!-- ko if: isUsed -->
            <span class="f-color-danger f-fs-1">Использован</span>
            <!-- /ko -->
            <!-- ko if: $parent.type !== 'reusable'-->
            <!-- ko ifnot: isUsed -->
            <button
              class="btn btn-icon btn-icon-remove"
              title="Удалить"
              data-bind="click: function() { $parent.removeCode($promocode) }, tooltip"
            ></button>
            <!-- /ko -->
            <!-- /ko -->
          </td>
        </tr>
        <!-- /ko -->
      </tbody>
    </table>
    <!-- /ko -->
  </div>

  <div id="codes-list-bottom" class="mb-4"></div>

  <!-- ko template: {
    foreach: templateIf(loading(), $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <div class="spinner-block">
    <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
  </div>
  <!-- /ko -->

  <!-- ko template: {
    foreach: templateIf(!loading() && codes().length == 0, $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <div class="not-found p-4 service-text text-center">Промокодов пока нет</div>
  <!-- /ko -->
</div>
