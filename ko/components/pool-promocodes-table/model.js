import { SortModel } from '../../models/sort';
import { formatPeriodToArray } from 'Utils/date/period';
import { Promocode } from './promocode';
import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.element = element;
    this.pool = params.pool;

    this.modals = ko.observableArray([]);
    this.dialogsContainer = params.dialogsContainer;

    this.type = params.type || 'pool';
    this.codes = ko.observableArray([]);

    this.page = ko.observable(1);
    this.loading = ko.observable(false);
    this.isLastRequestEmpty = ko.observable(false);
    this.blocked = ko.observable(false);

    this.filters = {
      query: ko.observable(''),
      period: ko.observable('')
    };

    this.queryParams = ko.computed(() => {
      let [from, to] = formatPeriodToArray(this.filters.period());
      let q = this.filters.query();
      let params = {};

      if (q) params.search = { q };
      if (from) params.filters = { from, to };

      return params;
    });

    this.sortModel = new SortModel('created_at', true);

    this.isLoadingFromCsv = ko.observable(false);
    this.loadingFromCsvProgress = ko.observable(0);

    this.configuratedCSVLoader = CSVLoader({
      url: `${APIConfig.baseApiUrlPath}discount-pool-code/add-by-file?access-token=${APIConfig.apiKey}&poolId=${this.pool.id}`,
      progressUrl: (id) => {
        return `${APIConfig.baseApiUrlPath}discount-pool-code/check-progress?access-token=${APIConfig.apiKey}&id=${id}`;
      },
      onStart: () => {
        this.isLoadingFromCsv(true);
      },
      onFinish: (data) => {
        this.loadingFromCsvProgress(100);
        const failed = data.failed
          ? `<p class="mb-2"><b class="bold">${data.failedRows.join(
              ', '
            )} строки</b> пропущены (ошибка в данных)</p>`
          : '';
        const updated = data.updated
          ? `<p><b class="bold">${data.updatedRows.join(
              ', '
            )} строки</b> — данные обновлены (коды совпадают с существующими)</p>`
          : '';
        const news = `<p><b class="bold">${data.inserted} ${utils.declOfNum(
          data.inserted,
          ['промокод', 'промокода', 'промокодов']
        )}</b> добавлено</p>`;
        this.isLoadingFromCsv(false);

        this.modals.push({
          name: 'foquz-info-modal',
          params: {
            data: {
              text: failed + updated + news
            }
          }
        });

        this.pool.load();
        this.pool.poolsModel.loadStats();
        this.blocked(true);
        this.reset();
        this.load();
        this.blocked(false);
      },
      onProgress: (progress) => {
        this.loadingFromCsvProgress(progress);
      }
    });

    this.load();
    this.activate();
  }

  resetFilters() {
    this.filters.query('');
    this.filters.period('');

    this.update();
  }

  applyFilters() {
    this.update();
  }

  sortBy(order) {
    this.sortModel.sortBy(order);
    this.blocked(true);
    this.reset();
    this.load();
    this.blocked(false);
  }

  addCode(codeData) {
    this.codes.push(new Promocode(codeData));
  }

  removeCode(code) {
    console.log('remove code', code);
    const that = this;
    $.ajax({
      method: 'DELETE',
      url: `${APIConfig.baseApiUrlPath}discount-pool-code/delete?id=${code.id}&access-token=${APIConfig.apiKey}`,
      success: function () {
        that.codes.remove(code);
        that.pool.load();
      }
    });
  }

  reset() {
    this.codes.removeAll();
    this.page(1);
  }

  trackLoadingProgress() {
    const timerId = setInterval(() => {
      this.loadingFromCsvProgress(
        Math.min(this.loadingFromCsvProgress() + Math.random() * 10, 100)
      );

      if (this.loadingFromCsvProgress() === 100) {
        clearInterval(timerId);
        this.isLoadingFromCsv(false);

        this.modals.push({
          name: 'foquz-info-modal',
          params: {
            data: {
              text: `<p class="mb-2"><b class="bold">15, 28, 69 строки</b> пропущены (ошибка в данных)</p>
              <p><b class="bold">29, 105 строки</b> — данные обновлены (контакты совпадают с существующими)</p>`
            }
          }
        });
      }
    }, 200);
  }

  loadFromCSV() {
    this.configuratedCSVLoader();
  }

  create() {
    this.modals.push({
      name: 'add-promocodes-modal',
      params: {
        data: {
          pool: this.pool,
          onUpdate: (data) => {
            if (data.exist == 0) {
              this.modals.push({
                name: 'foquz-info-modal',
                params: {
                  data: {
                    text: `${utils.declOfNum(data.added, [
                      'Добавлен',
                      'Добавлено',
                      'Добавлено'
                    ])} ${data.added} ${utils.declOfNum(data.added, [
                      'промокод',
                      'промокода',
                      'промокодов'
                    ])}`
                  }
                }
              });
            }
            this.pool.load();
            this.pool.poolsModel.loadStats();
            this.blocked(true);
            this.reset();
            this.load();
            this.blocked(false);
          }
        }
      }
    });
  }

  update() {
    this.emitEvent('update', {
      filters: this.queryParams(),
      period: this.filters.period()
    });

    this.reset();
    this.load();
  }

  load() {
    this.loading(true);
    const that = this;

    let params = this.queryParams();

    $.ajax({
      method: 'GET',
      url: `${APIConfig.baseApiUrlPath}discount-pool-code?poolId=${this.pool.id}&access-token=${APIConfig.apiKey}`,
      data: {
        page: this.page(),
        order: this.sortModel.sort(),
        ...params
      },
      success: function (data) {
        data.items.forEach((codeData) => that.addCode(codeData));
        that.isLastRequestEmpty(data.items.length === 0);
      },
      complete: function () {
        that.loading(false);
      }
    });
  }

  nextPage() {
    if (this.blocked() || this.loading() || this.isLastRequestEmpty()) return;
    this.page(this.page() + 1);
    this.load();
  }

  activate() {
    const that = this;
    const root = this.element.closest('.foquz-dialog-sidesheet');

    this.observer = new IntersectionObserver(
      function (entries, observer) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            that.nextPage();
          }
        });
      },
      {
        root: root,
        rootMargin: '100px'
      }
    );

    this.bottom = document.getElementById('codes-list-bottom');

    if (this.bottom) {
      this.observer.observe(this.bottom);
    }
  }

  deactivate() {
    if (this.bottom) {
      this.observer.unobserve(this.bottom);
    }
  }

  openClientCard(clientId) {
    this.dialogsContainer.add({
      name: 'edit-client-sidesheet',
      params: {
        data: {
          clientId: clientId
        }
      }
    });
  }
}
