import { channelTypes } from '../../data/channels';

export class Promocode {
  constructor(data) {
    this.id = data.id;

    this.code = data.code;
    this.createdAt = data.created_at;
    this.sendedAt = data.sended_at;
    this.answered = data.answered;

    this.client = data.client;

    this.poll = data.poll;
    this.mailing = data.mailing;
    this.sendType = data.sendType;

    this.isUsed = data.used;

    this.order = data.sentFromOrder;
    this.usedForOrder = data.usedForOrder;
  }

  get hasPoll() {
    return this.poll && this.poll.id;
  }

  get hasMailing() {
    return this.mailing && this.mailing.id;
  }

  get hasClient() {
    return this.client && this.client.id;
  }

  get hasOrder() {
    return this.order && this.order.id;
  }

  get hasUsedForOrder() {
    return this.usedForOrder && this.usedForOrder.id;
  }

  get clientWhoUsed() {
    if (!this.hasUsedForOrder) return null;
    if (!this.usedForOrder.client.id) return null;
    return this.usedForOrder.client;
  }

  get pollLink() {
    if (!this.hasPoll) return '';
    if (this.poll.isArchived) return '';
    return '/foquz/foquz-poll/settings?id=' + this.poll.id;
  }

  get mailingLink() {
    if (!this.hasMailing) return '';
    if (this.mailing.isArchived) return '';
    return '/foquz/mailings/update?id=' + this.mailing.id;
  }

  get mailingChannelType() {
    return channelTypes[this.mailing.activeChannel];
  }

  get orderDate() {
    if (!this.hasOrder) return '';
    if (!this.order.date) return '';
    let date = moment(this.order.date, 'YYYY-MM-DD');
    if (date.isValid()) return date.format('DD.MM.YYYY');
    return '';
  }

  get usedDate() {
    if (!this.hasUsedForOrder) return null;
    if (!this.usedForOrder.date) return null;
    let date = moment(this.usedForOrder.date, 'YYYY-MM-DD');
    if (date.isValid()) return date.format('DD.MM.YYYY');
    return '';
  }

  get orderLink() {
    if (!this.hasOrder) return '';
    return '/foquz/answers?search%5BorderNumber%5D=' + this.order.id;
  }
}
