import { FoquzComponent } from "Models/foquz-component";
import { ClientFieldsCollection } from "Models/data-collection/client-fields";

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.initializing = ko.observable(true);

    this.review = params.review;

    this.values = {};

    [
      "gender",
      "birthday",
      "lastOrderDate",
      "ltv",
      "addedAt",
      "updatedAt",
    ].forEach((key) => {
      if (this.review.clientSystemFields[key])
        this.values[key] = this.review.clientSystemFields[key];
    });

    ["tags"].forEach((key) => {
      if (this.review.clientSystemFields[key].length)
        this.values[key] = this.review.clientSystemFields[key];
    });

    if (this.review.clientSystemFields.filials) {
      if (Array.isArray(this.review.clientSystemFields.filials)) {
        this.values.filials = this.review.clientSystemFields.filials;
      } else {
        const div = document.createElement('div');
        div.innerHTML = this.review.clientSystemFields.filials;
        this.values.filials = div.textContent.split(',');
      }
    }

    console.log('values', this.values, this.review)

    if (this.review.clientFields.length) {
      this.values.additional = this.review.clientFields;
    }

    if (this.review.clientApiFields.length) {
      this.values.api = this.review.clientApiFields;
    }

    this.fields = {};

    Object.keys(this.values).forEach(
      (key) => (this.fields[key] = ko.observable(false))
    );

    if (this.fields.api) this.fields.api(false);

    this.hasHidden = ko.pureComputed(() => {
      return Object.values(this.fields).some((v) => !v());
    });

    this.line = ko.observable(null);

    this.collection = ClientFieldsCollection.getInstance();
    this.collection.on("load", () => {
      this.initializing(false);
    });
    this.collection.load();
  }

  getFieldName(id) {
    return this.collection.getById(id).text;
  }

  check() {
    let line = this.line();
    if (!line) return;
    let right = line.getBoundingClientRect().right;

    let items = [...line.children];
    let visible = [];
    for (let i = 0, count = items.length; i < count; i++) {
      let item = items[i];
      if (item.getBoundingClientRect().right > right) {
        break;
      }
      visible.push(item.dataset.item);
    }

    Object.keys(this.fields).forEach((key) => {
      if (key === "api") this.fields.api(false);
      else this.fields[key](visible.indexOf(key) > -1);
    });
  }

  onInit() {
    this.check();

    window.addEventListener(
      "resize",
      _.throttle(() => {
        this.check();
      }, 400)
    );
  }
}
