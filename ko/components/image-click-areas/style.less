@import "../../less/vars/colors.less";

.image-click-areas {
  width: 100%;
  height: 100%;
  position: relative;
  
  &__container {
    width: 100%;
    height: 100%;
    position: relative;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    cursor: crosshair;
    outline: none;
    user-select: none;

    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
  }

  &--drawing {
    cursor: crosshair;
  }

  &__area {
    position: absolute;
    border: 2px dashed @primary-color;
    background-color: rgba(0, 0, 0, 0.4);
    cursor: move;
    user-select: none;
    min-width: 10px;
    min-height: 10px;

    transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.5);
    }
    
    &--selected {
      border-color: #F96261;
      z-index: 5;
      .image-click-areas__resize-handle {
        border-color: #F96261;
      }
    }
    
    &--drawing {
      border-color: #28a745;
    }
    
    &--dragging,
    &--resizing {
      &:hover {
        border-color: inherit;
        background-color: inherit;
      }
    }
  }

  &__area-name {
    position: absolute;
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.3;
    white-space: nowrap;
    pointer-events: none;
    max-width: 224px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__resize-handles {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    pointer-events: none;
  }

  &__resize-handle {
    @resize-handle-size: 8px;
    position: absolute;
    background: white;
    border: 2px solid @primary-color;
    pointer-events: auto;
    transition: opacity 0.2s ease-in-out, border-color 0.2s ease-in-out;
    
    &--nw {
      top: 0;
      left: 0;
      width: @resize-handle-size;
      height: @resize-handle-size;
      cursor: nw-resize;
    }
    
    &--ne {
      top: 0;
      right: 0;
      width: @resize-handle-size;
      height: @resize-handle-size;
      cursor: ne-resize;
    }
    
    &--sw {
      bottom: 0;
      left: 0;
      width: @resize-handle-size;
      height: @resize-handle-size;
      cursor: sw-resize;
    }
    
    &--se {
      bottom: 0;
      right: 0;
      width: @resize-handle-size;
      height: @resize-handle-size;
      cursor: se-resize;
    }
    
    &--n {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: @resize-handle-size;
      height: @resize-handle-size;
      cursor: n-resize;
    }
    
    &--s {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: @resize-handle-size;
      height: @resize-handle-size;
      cursor: s-resize;
    }
    
    &--w {
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: @resize-handle-size;
      height: @resize-handle-size;
      cursor: w-resize;
    }
    
    &--e {
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: @resize-handle-size;
      height: @resize-handle-size;
      cursor: e-resize;
    }
    
    &:hover {
      opacity: 0.8;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .image-click-areas__container {
    min-height: 300px;
  }
  
  .image-click-areas__resize-handle {
    &--nw, &--ne, &--sw, &--se {
      width: 12px;
      height: 12px;
    }
    
    &--n, &--s {
      width: 12px;
      height: 6px;
    }
    
    &--w, &--e {
      width: 6px;
      height: 12px;
    }
  }
}
