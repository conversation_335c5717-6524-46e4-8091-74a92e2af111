<div class="image-click-areas__container"
     data-bind="style: {
                  backgroundImage: imageUrl() ? 'url(' + imageUrl() + ')' : 'none'
                },
                event: {
                  mousedown: handleMouseDown,
                  mousemove: handleMouseMove,
                  mouseup: handleMouseUp
                },
                attr: { tabindex: 0 },
                css: { 'image-click-areas--drawing': isDrawing() }">

  <!-- Render existing areas -->
  <!-- ko foreach: areas -->
  <div class="image-click-areas__area"
       data-bind="style: {
                    left: $parent.calculatePixelX($data.x()) + $parent.imageOffset().x + 'px',
                    top: $parent.calculatePixelY($data.y()) + $parent.imageOffset().y + 'px',
                    width: $parent.calculatePixelWidth($data.width()) + 'px',
                    height: $parent.calculatePixelHeight($data.height()) + 'px'
                  },
                  css: {
                    'image-click-areas__area--selected': $parent.selectedArea() === $data,
                    'image-click-areas__area--dragging': $parent.isDragging() && $parent.draggedArea === $data,
                    'image-click-areas__area--resizing': $parent.isResizing() && $parent.resizedArea === $data
                  },
                  event: {
                    mousedown: function(data, event) { return $parent.onAreaMouseDown($data, data, event); }
                  }">
    
    <span class="image-click-areas__area-name"
          data-bind="text: displayName || name,
                     style: $parent.getAreaNameStyles($data)"></span>
    
    <!-- Resize handles for selected area -->
    <div class="image-click-areas__resize-handles">
      <div class="image-click-areas__resize-handle image-click-areas__resize-handle--nw" data-handle="nw"></div>
      <div class="image-click-areas__resize-handle image-click-areas__resize-handle--ne" data-handle="ne"></div>
      <div class="image-click-areas__resize-handle image-click-areas__resize-handle--sw" data-handle="sw"></div>
      <div class="image-click-areas__resize-handle image-click-areas__resize-handle--se" data-handle="se"></div>
      <div class="image-click-areas__resize-handle image-click-areas__resize-handle--n" data-handle="n"></div>
      <div class="image-click-areas__resize-handle image-click-areas__resize-handle--s" data-handle="s"></div>
      <div class="image-click-areas__resize-handle image-click-areas__resize-handle--w" data-handle="w"></div>
      <div class="image-click-areas__resize-handle image-click-areas__resize-handle--e" data-handle="e"></div>
    </div>
  </div>
  <!-- /ko -->

  <!-- Temporary area for drawing -->
  <!-- ko if: isDrawing() && drawingArea() -->
  <div class="image-click-areas__area image-click-areas__area--drawing"
       data-bind="style: {
                    left: drawingArea().left + 'px',
                    top: drawingArea().top + 'px',
                    width: drawingArea().width + 'px',
                    height: drawingArea().height + 'px'
                  }">
  </div>
  <!-- /ko -->

</div>
