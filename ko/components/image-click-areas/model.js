import { FoquzComponent } from 'Models/foquz-component';

const CONTAINER_CLASS = 'image-click-areas__container';
const RESIZE_HANDLE_CLASS = 'image-click-areas__resize-handle';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    // Parameters from parent
    this.imageUrl = params.imageUrl;
    this.areas = params.areas || ko.observableArray([]);
    this.onAreasChange = params.onAreasChange;
    this.parentSelectedAreaObservable = params.selectedAreaObservable;
    this.augmentAreaWithPixelComputeds = params.augmentAreaWithPixelComputeds;
    this.originalImageDimensions = params.originalImageDimensions || ko.observable({ width: 0, height: 0 });
    this.displayedImageDimensionsOutput = params.displayedImageDimensionsOutput; // Added to output displayed dimensions

    // Internal state
    this.displayedImageDimensions = ko.observable({ width: 0, height: 0 });
    this.imageOffset = ko.observable({ x: 0, y: 0 });
    this.selectedArea = ko.observable(null);
    this.isDrawing = ko.observable(false);
    this.drawingArea = ko.observable(null);

    // Drawing state
    this.drawStartX = 0;
    this.drawStartY = 0;
    this.currentDrawX = 0;
    this.currentDrawY = 0;

    this.isDragging = ko.observable(false);
    this.isResizing = ko.observable(false);
    this.dragStartX = 0;
    this.dragStartY = 0;
    this.resizeHandle = null;
    this.lastDragPixelPosition = { x: 0, y: 0 };

    this.containerElement = null;
    this.imageElement = null;

    this.initializeComponent();
    this.setupEventHandlers();

    this.displayedImageDimensions.subscribe((newDimensions) => {
      if (this.displayedImageDimensionsOutput && ko.isObservable(this.displayedImageDimensionsOutput)) {
        this.displayedImageDimensionsOutput(newDimensions);
      }
    });
  }



  initializeComponent() {
    setTimeout(() => {
      this.containerElement = this.element.querySelector(`.${CONTAINER_CLASS}`);
      if (this.containerElement) {
        this.setupResizeObserver();
        this.setupDocumentEventListeners();
        this.updateDisplayedDimensions();
      }
    }, 0);

    if (ko.isObservable(this.originalImageDimensions)) {
      this.originalImageDimensions.subscribe(() => {
        this.updateDisplayedDimensions();
      });
    }

    this.areas().forEach((area, index) => {
      if (!area.pixelWidth || !area.pixelHeight) {
        if (this.augmentAreaWithPixelComputeds) {
          this.augmentAreaWithPixelComputeds(area);
        }
      }
    });

    if (ko.isObservable(this.imageUrl)) {
      this.imageUrl.subscribe(() => {
        this.updateDisplayedDimensions();
      });
    }

    if (this.parentSelectedAreaObservable) {
      this.parentSelectedAreaObservable.subscribe((newlySelectedAreaFromParent) => {
        if (this.selectedArea() !== newlySelectedAreaFromParent) {
          this.areas().forEach(a => {
            if (ko.isObservable(a.isSelected)) {
              a.isSelected(a === newlySelectedAreaFromParent);
            } else {
              console.warn("Area 'isSelected' property is not observable:", a);
            }
          });
          this.selectedArea(newlySelectedAreaFromParent);
        }
      });
      const initialParentSelection = this.parentSelectedAreaObservable();
      if (initialParentSelection) {
          this.selectedArea(initialParentSelection);
          this.areas().forEach(a => {
            if (ko.isObservable(a.isSelected)) {
                a.isSelected(a === initialParentSelection);
            }
          });
      }
    }
    
    if (ko.isObservable(this.areas)) {
        this.areas.subscribe((newArray) => {
            const currentSelection = this.selectedArea();
            newArray.forEach((area, index) => {
                if (ko.isObservable(area.isSelected)) {
                    area.isSelected(area === currentSelection);
                }
                if (!area.pixelWidth || !area.pixelHeight) {
                  if (this.augmentAreaWithPixelComputeds) {
                    this.augmentAreaWithPixelComputeds(area);
                  }
                }
            });
        });
    }
  }

  setupDocumentEventListeners() {
    this.boundHandleDocumentMouseMove = this.handleDocumentMouseMove.bind(this);
    this.boundHandleDocumentMouseUp = this.handleDocumentMouseUp.bind(this);
    this.boundHandleDragMove = this.handleDragMove.bind(this);
    this.boundHandleDragEnd = this.handleDragEnd.bind(this);
    this.boundHandleResizeMove = this.handleResizeMove.bind(this);
    this.boundHandleResizeEnd = this.handleResizeEnd.bind(this);
  }

  setupResizeObserver() {
    if (!this.containerElement) return;

    const resizeObserver = new ResizeObserver(() => {
      this.updateDisplayedDimensions();
    });

    resizeObserver.observe(this.containerElement);
  }

  updateDisplayedDimensions() {
    if (!this.containerElement) return;

    const containerRect = this.containerElement.getBoundingClientRect();
    const original = this.originalImageDimensions();
    
    if (original.width === 0 || original.height === 0) {
      // If original dimensions are not set, reset displayed and output
      this.displayedImageDimensions({ width: 0, height: 0 });
      this.imageOffset({ x: 0, y: 0 });
      if (this.displayedImageDimensionsOutput && ko.isObservable(this.displayedImageDimensionsOutput)) {
        this.displayedImageDimensionsOutput({ width: 0, height: 0 });
      }
      return;
    }

    const containerAspect = containerRect.width / containerRect.height;
    const imageAspect = original.width / original.height;

    let displayedWidth, displayedHeight;

    if (imageAspect > containerAspect) {
      displayedWidth = containerRect.width;
      displayedHeight = containerRect.width / imageAspect;
    } else {
      displayedHeight = containerRect.height;
      displayedWidth = containerRect.height * imageAspect;
    }

    const newDisplayedDimensions = {
      width: displayedWidth,
      height: displayedHeight
    };
    this.displayedImageDimensions(newDisplayedDimensions);
    // The subscription to this.displayedImageDimensions will update displayedImageDimensionsOutput

    const calculatedImageOffsetX = (containerRect.width - displayedWidth) / 2;
    const calculatedImageOffsetY = (containerRect.height - displayedHeight) / 2;
    this.imageOffset({ x: calculatedImageOffsetX, y: calculatedImageOffsetY });

    console.log('[IMAGE-CLICK-AREAS] updateDisplayedDimensions:',
                { original: ko.toJS(original), container: containerRect, displayed: ko.toJS(this.displayedImageDimensions()), offset: ko.toJS(this.imageOffset()) });
  }

  setupEventHandlers() {
    // Keyboard events for deletion and movement
    document.addEventListener('keydown', (e) => {
      const selectedArea = this.selectedArea();
      if (!selectedArea) return;

      if (e.key === 'Delete') {
        this.deleteArea(selectedArea);
      } else if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
        e.preventDefault();
        this.moveAreaByArrowKey(selectedArea, e.key);
      }
    });
  }

  moveAreaByArrowKey(area, key) {
    const displayed = this.displayedImageDimensions();
    if (displayed.width === 0 || displayed.height === 0) return;

    // Calculate movement step (1% of image dimensions in proportional units)
    const stepX = 0.01;
    const stepY = 0.01;

    // Get current position
    let currentX = area.x();
    let currentY = area.y();
    const currentWidth = area.width();
    const currentHeight = area.height();

    // Apply movement based on key
    switch (key) {
      case 'ArrowUp':
        currentY = Math.max(0, currentY - stepY);
        break;
      case 'ArrowDown':
        currentY = Math.min(1 - currentHeight, currentY + stepY);
        break;
      case 'ArrowLeft':
        currentX = Math.max(0, currentX - stepX);
        break;
      case 'ArrowRight':
        currentX = Math.min(1 - currentWidth, currentX + stepX);
        break;
    }

    // Update area position
    area.x(currentX);
    area.y(currentY);

    console.log('[IMAGE-CLICK-AREAS] moveAreaByArrowKey:',
                { key, newX: currentX, newY: currentY, width: currentWidth, height: currentHeight });

    // Notify about the change
    this.notifyAreasChange();
  }

  calculatePixelX(proportionalX) {
    const displayed = this.displayedImageDimensions();
    return (proportionalX || 0) * displayed.width;
  }

  calculatePixelY(proportionalY) {
    const displayed = this.displayedImageDimensions();
    return (proportionalY || 0) * displayed.height;
  }

  calculatePixelWidth(proportionalWidth) {
    const displayed = this.displayedImageDimensions();
    return (proportionalWidth || 0) * displayed.width;
  }

  calculatePixelHeight(proportionalHeight) {
    const displayed = this.displayedImageDimensions();
    return (proportionalHeight || 0) * displayed.height;
  }

  pixelToProportionalX(pixelX) {
    const displayed = this.displayedImageDimensions();
    return displayed.width > 0 ? pixelX / displayed.width : 0;
  }

  pixelToProportionalY(pixelY) {
    const displayed = this.displayedImageDimensions();
    return displayed.height > 0 ? pixelY / displayed.height : 0;
  }

  pixelToProportionalWidth(pixelWidth) {
    const displayed = this.displayedImageDimensions();
    return displayed.width > 0 ? pixelWidth / displayed.width : 0;
  }

  pixelToProportionalHeight(pixelHeight) {
    const displayed = this.displayedImageDimensions();
    return displayed.height > 0 ? pixelHeight / displayed.height : 0;
  }

  getAreaNameStyles(area) {
    const areaY = this.calculatePixelY(ko.unwrap(area.y()));
    const areaHeight = this.calculatePixelHeight(ko.unwrap(area.height()));
    const areaX = this.calculatePixelX(ko.unwrap(area.x()));
    const imageOffset = this.imageOffset();
    const displayedDimensions = this.displayedImageDimensions();
    
    const labelHeight = 24;
    const labelWidth = 224;
    const labelPadding = 9;
    const labelOffset = 3;
    
    const wouldOverflowBottom = areaY + areaHeight + imageOffset.y + labelHeight > 
                               displayedDimensions.height + imageOffset.y * 2;
    const wouldOverflowRight = areaX + imageOffset.x + labelWidth > 
                              displayedDimensions.width + imageOffset.x * 2;
    
    return {
      top: wouldOverflowBottom ? 'auto' : (areaHeight + labelPadding) + 'px',
      bottom: wouldOverflowBottom ? (areaHeight + labelPadding) + 'px' : 'auto',
      left: wouldOverflowRight ? 'auto' : (-labelOffset) + 'px',
      right: wouldOverflowRight ? (-labelOffset) + 'px' : 'auto'
    };
  }

  handleMouseDown(_, event) {
    if (event.target.classList.contains(CONTAINER_CLASS)) {
      event.preventDefault();
      this.startDrawing(event);
    }
  }

  startDrawing(event) {
    if (!this.containerElement) return;

    const rect = this.containerElement.getBoundingClientRect();
    const displayed = this.displayedImageDimensions();

    const imageOffsetX = (rect.width - displayed.width) / 2;
    const imageOffsetY = (rect.height - displayed.height) / 2;

    const mouseXInContainer = event.clientX - rect.left;
    const mouseYInContainer = event.clientY - rect.top;
    
    let imageRelativeX = mouseXInContainer - imageOffsetX;
    let imageRelativeY = mouseYInContainer - imageOffsetY;

    this.drawStartX = Math.max(0, Math.min(imageRelativeX, displayed.width));
    this.drawStartY = Math.max(0, Math.min(imageRelativeY, displayed.height));

    this.currentDrawX = this.drawStartX;
    this.currentDrawY = this.drawStartY;

    this.isDrawing(true);
    this.selectedArea(null);

    console.log('[IMAGE-CLICK-AREAS] startDrawing:', { sx: this.drawStartX, sy: this.drawStartY, displayed: ko.toJS(this.displayedImageDimensions()) });

    document.addEventListener('mousemove', this.boundHandleDocumentMouseMove);
    document.addEventListener('mouseup', this.boundHandleDocumentMouseUp);

    this.updateDrawingArea();
  }

  handleMouseMove() {
  }

  handleDocumentMouseMove(event) {
    if (this.isDrawing()) {
      this.updateDrawing(event);
    }
  }

  updateDrawing(event) {
    if (!this.containerElement) return;

    const rect = this.containerElement.getBoundingClientRect();
    const displayed = this.displayedImageDimensions();

    const imageOffsetX = (rect.width - displayed.width) / 2;
    const imageOffsetY = (rect.height - displayed.height) / 2;

    const mouseXInContainer = event.clientX - rect.left;
    const mouseYInContainer = event.clientY - rect.top;
    
    let imageRelativeX = mouseXInContainer - imageOffsetX;
    let imageRelativeY = mouseYInContainer - imageOffsetY;

    // Constrain to image bounds
    this.currentDrawX = Math.max(0, Math.min(imageRelativeX, displayed.width));
    this.currentDrawY = Math.max(0, Math.min(imageRelativeY, displayed.height));

    this.updateDrawingArea();
  }

  updateDrawingArea() {
    const left = Math.min(this.drawStartX, this.currentDrawX);
    const top = Math.min(this.drawStartY, this.currentDrawY);
    const width = Math.abs(this.currentDrawX - this.drawStartX);
    const height = Math.abs(this.currentDrawY - this.drawStartY);

    const currentImageOffset = this.imageOffset();
    this.drawingArea({
      left: currentImageOffset.x + left,
      top: currentImageOffset.y + top,
      width: width,
      height: height
    });
    console.log('[IMAGE-CLICK-AREAS] updateDrawingArea:', { drawingArea: ko.toJS(this.drawingArea()) });
  }

  handleMouseUp() {
    // This is for the container mouseup - not used for drawing
    // Drawing uses document mouseup for better tracking
  }

  handleDocumentMouseUp() {
    if (this.isDrawing()) {
      this.finishDrawing();
    }
  }

  finishDrawing() {
    // Clean up document event listeners
    document.removeEventListener('mousemove', this.boundHandleDocumentMouseMove);
    document.removeEventListener('mouseup', this.boundHandleDocumentMouseUp);

    const drawing = this.drawingArea();
    const currentImageOffset = this.imageOffset();

    if (drawing && drawing.width > 10 && drawing.height > 10) {
      // Create new area with proportional coordinates
      const newArea = {
        id: this.generateAreaId(),
        name: ko.observable(''),
        x: ko.observable(this.pixelToProportionalX(drawing.left - currentImageOffset.x)),
        y: ko.observable(this.pixelToProportionalY(drawing.top - currentImageOffset.y)),
        width: ko.observable(this.pixelToProportionalWidth(drawing.width)),
        height: ko.observable(this.pixelToProportionalHeight(drawing.height)),
        isSelected: ko.observable(false), // Initially not selected by this component
        clickCount: ko.observable(0)
      };
      
      // Final clamping for proportional values
      let px = newArea.x();
      let py = newArea.y();
      let pw = newArea.width();
      let ph = newArea.height();

      px = Math.max(0, Math.min(px, 1));
      py = Math.max(0, Math.min(py, 1));
      pw = Math.max(0.001, Math.min(pw, 1 - px)); // Ensure x+width <= 1 and min width
      ph = Math.max(0.001, Math.min(ph, 1 - py)); // Ensure y+height <= 1 and min height

      newArea.x(px);
      newArea.y(py);
      newArea.width(pw);
      newArea.height(ph);

      console.log('[IMAGE-CLICK-AREAS] finishDrawing - New Area (proportional):', 
                  { x: px, y: py, w: pw, h: ph, originalDrawing: ko.toJS(drawing) });

      const augmentedArea = this.augmentAreaWithPixelComputeds(newArea);
      this.areas.push(augmentedArea); // Push directly after augmentation

      if (this.parentSelectedAreaObservable) {
        this.parentSelectedAreaObservable(augmentedArea); 
      } else {
        this.selectedArea(augmentedArea); // Fallback if no parent observable
        augmentedArea.isSelected(true);
      }
      this.notifyAreasChange();
    }

    this.isDrawing(false);
    this.drawingArea(null);
  }

  generateAreaId() {
    return 'area_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  // Area interaction handlers
  onAreaMouseDown(area, _, event) {
    event.stopPropagation();
    
    // Update this component's internal selectedArea
    this.selectedArea(area);

    // Update the parent's selectedAreaObservable
    if (this.parentSelectedAreaObservable) {
      this.parentSelectedAreaObservable(area);
    }

    // Set isSelected on all areas
    this.areas().forEach(a => {
      if (ko.isObservable(a.isSelected)) {
        a.isSelected(a === area);
      }
    });
    // area.isSelected(true); // This is handled by the loop above

    // Check if clicking on resize handle
    if (event.target.classList.contains(RESIZE_HANDLE_CLASS)) {
      this.startResize(area, event);
    } else {
      this.startDrag(area, event);
    }
  }

  startDrag(area, event) {
    this.isDragging(true);
    this.draggedArea = area;

    const rect = this.containerElement.getBoundingClientRect();
    const displayed = this.displayedImageDimensions();
    const imageOffsetX = (rect.width - displayed.width) / 2;
    const imageOffsetY = (rect.height - displayed.height) / 2;

    // Mouse position at the start of the drag, relative to the image's top-left
    this.dragStartX = event.clientX - rect.left - imageOffsetX;
    this.dragStartY = event.clientY - rect.top - imageOffsetY;

    // Calculate the area's initial top-left pixel position relative to the image
    const initialAreaPixelX = this.calculatePixelX(area.x());
    const initialAreaPixelY = this.calculatePixelY(area.y());

    this.dragAreaStartX = initialAreaPixelX; // Store for calculating delta during move
    this.dragAreaStartY = initialAreaPixelY; // Store for calculating delta during move

    // Initialize lastDragPixelPosition with the area's current position
    // This is crucial for clicks without movement.
    this.lastDragPixelPosition = { x: initialAreaPixelX, y: initialAreaPixelY };

    console.log('[IMAGE-CLICK-AREAS] startDrag:',
                { area: ko.toJS(area), mouseStart: {x: this.dragStartX, y: this.dragStartY},
                  areaInitialPixel: {x: initialAreaPixelX, y: initialAreaPixelY} });

    document.addEventListener('mousemove', this.boundHandleDragMove);
    document.addEventListener('mouseup', this.boundHandleDragEnd);
  }

  handleDragMove(event) {
    if (!this.isDragging() || !this.draggedArea) return;

    const rect = this.containerElement.getBoundingClientRect();
    const displayed = this.displayedImageDimensions();
    const imageOffsetX = (rect.width - displayed.width) / 2;
    const imageOffsetY = (rect.height - displayed.height) / 2;

    const currentX = event.clientX - rect.left - imageOffsetX;
    const currentY = event.clientY - rect.top - imageOffsetY;

    const deltaX = currentX - this.dragStartX;
    const deltaY = currentY - this.dragStartY;

    const newX = this.dragAreaStartX + deltaX;
    const newY = this.dragAreaStartY + deltaY;

    // Constrain to image bounds
    const areaWidth = this.calculatePixelWidth(this.draggedArea.width());
    const areaHeight = this.calculatePixelHeight(this.draggedArea.height());

    const constrainedX = Math.max(0, Math.min(newX, displayed.width - areaWidth));
    const constrainedY = Math.max(0, Math.min(newY, displayed.height - areaHeight));

    this.lastDragPixelPosition = { x: constrainedX, y: constrainedY };

    this.draggedArea.x(this.pixelToProportionalX(constrainedX));
    this.draggedArea.y(this.pixelToProportionalY(constrainedY));

    console.log('[IMAGE-CLICK-AREAS] handleDragMove:', 
                { deltaX, deltaY, newX, newY, constrainedX, constrainedY, 
                  propX: this.draggedArea.x(), propY: this.draggedArea.y() });
  }

  handleDragEnd() {
    if (!this.draggedArea) return;

    this.isDragging(false);

    document.removeEventListener('mousemove', this.boundHandleDragMove);
    document.removeEventListener('mouseup', this.boundHandleDragEnd);

    // Force update of display dimensions to get the very latest values
    // This ensures that container resizes that might have just happened
    // (e.g., due to focus change, scrollbar, etc. on mouseup) are accounted for.
    this.updateDisplayedDimensions();

    // Now, convert the last known pixel position (relative to image)
    // into a proportion using these latest dimensions.
    const finalX = this.pixelToProportionalX(this.lastDragPixelPosition.x);
    const finalY = this.pixelToProportionalY(this.lastDragPixelPosition.y);

    this.draggedArea.x(finalX);
    this.draggedArea.y(finalY);

    console.log('[IMAGE-CLICK-AREAS] handleDragEnd - Final Proportional:', { x: finalX, y: finalY, lastPixel: this.lastDragPixelPosition });

    this.notifyAreasChange();
    this.draggedArea = null;
  }

  startResize(area, event) {
    this.isResizing(true);
    this.resizedArea = area;
    this.resizeHandle = event.target.getAttribute('data-handle');

    const rect = this.containerElement.getBoundingClientRect();
    const displayed = this.displayedImageDimensions();
    const imageOffsetX = (rect.width - displayed.width) / 2;
    const imageOffsetY = (rect.height - displayed.height) / 2;

    this.resizeStartX = event.clientX - rect.left - imageOffsetX;
    this.resizeStartY = event.clientY - rect.top - imageOffsetY;

    this.resizeAreaStartX = this.calculatePixelX(area.x());
    this.resizeAreaStartY = this.calculatePixelY(area.y());
    this.resizeAreaStartWidth = this.calculatePixelWidth(area.width());
    this.resizeAreaStartHeight = this.calculatePixelHeight(area.height());

    document.addEventListener('mousemove', this.boundHandleResizeMove);
    document.addEventListener('mouseup', this.boundHandleResizeEnd);
  }

  handleResizeMove(event) {
    if (!this.isResizing() || !this.resizedArea) return;

    const rect = this.containerElement.getBoundingClientRect();
    const displayed = this.displayedImageDimensions();
    const imageOffsetX = (rect.width - displayed.width) / 2;
    const imageOffsetY = (rect.height - displayed.height) / 2;

    const currentX = event.clientX - rect.left - imageOffsetX;
    const currentY = event.clientY - rect.top - imageOffsetY;

    const deltaX = currentX - this.resizeStartX;
    const deltaY = currentY - this.resizeStartY;

    this.applyResize(deltaX, deltaY);
  }

  applyResize(deltaX, deltaY) {
    const displayed = this.displayedImageDimensions();
    let newX = this.resizeAreaStartX;
    let newY = this.resizeAreaStartY;
    let newWidth = this.resizeAreaStartWidth;
    let newHeight = this.resizeAreaStartHeight;

    const handle = this.resizeHandle;

    // Apply resize based on handle
    if (handle.includes('n')) {
      newY = this.resizeAreaStartY + deltaY;
      newHeight = this.resizeAreaStartHeight - deltaY;
    }
    if (handle.includes('s')) {
      newHeight = this.resizeAreaStartHeight + deltaY;
    }
    if (handle.includes('w')) {
      newX = this.resizeAreaStartX + deltaX;
      newWidth = this.resizeAreaStartWidth - deltaX;
    }
    if (handle.includes('e')) {
      newWidth = this.resizeAreaStartWidth + deltaX;
    }

    // Constrain to minimum size and image bounds
    const minSize = 10;
    newWidth = Math.max(minSize, newWidth);
    newHeight = Math.max(minSize, newHeight);

    newX = Math.max(0, Math.min(newX, displayed.width - newWidth));
    newY = Math.max(0, Math.min(newY, displayed.height - newHeight));

    // Update area with proportional values
    this.resizedArea.x(this.pixelToProportionalX(newX));
    this.resizedArea.y(this.pixelToProportionalY(newY));
    this.resizedArea.width(this.pixelToProportionalWidth(newWidth));
    this.resizedArea.height(this.pixelToProportionalHeight(newHeight));

    console.log('[IMAGE-CLICK-AREAS] applyResize - Resized Area (pixel):', 
                { newX, newY, newWidth, newHeight, handle: this.resizeHandle, deltaX, deltaY });
    console.log('[IMAGE-CLICK-AREAS] applyResize - Resized Area (proportional):', 
                { x: this.resizedArea.x(), y: this.resizedArea.y(), w: this.resizedArea.width(), h: this.resizedArea.height() });
  }

  handleResizeEnd() {
    if (!this.resizedArea) return;

    this.isResizing(false);

    document.removeEventListener('mousemove', this.boundHandleResizeMove);
    document.removeEventListener('mouseup', this.boundHandleResizeEnd);

    // Final clamping for proportional values
    let px = this.resizedArea.x();
    let py = this.resizedArea.y();
    let pw = this.resizedArea.width();
    let ph = this.resizedArea.height();

    px = Math.max(0, Math.min(px, 1));
    py = Math.max(0, Math.min(py, 1));
    // Ensure width and height don't make the area exceed 1.0 in proportion, and maintain a minimum size
    pw = Math.max(0.001, Math.min(pw, 1 - px)); 
    ph = Math.max(0.001, Math.min(ph, 1 - py));

    this.resizedArea.x(px);
    this.resizedArea.y(py);
    this.resizedArea.width(pw);
    this.resizedArea.height(ph);
    
    console.log('[IMAGE-CLICK-AREAS] handleResizeEnd - Final Proportional:', 
                { x: px, y: py, w: pw, h: ph });

    this.notifyAreasChange();
    this.resizedArea = null;
    this.resizeHandle = null;
  }

  deleteArea(areaToDelete) {
    const wasSelected = (this.selectedArea() === areaToDelete);
    const areaIdToDelete = ko.unwrap(areaToDelete.id); // Get ID before removing for notification
    this.areas.remove(areaToDelete);
    
    if (wasSelected) {
      this.selectedArea(null);
      if (this.parentSelectedAreaObservable) {
        this.parentSelectedAreaObservable(null);
      }
    }
    this.notifyAreasChange();
  }

  notifyAreasChange() {
    if (typeof this.onAreasChange === 'function') {
      this.onAreasChange();
    }
  }

  dispose() {
    // Clean up document event listeners
    if (this.boundHandleDocumentMouseMove) {
      document.removeEventListener('mousemove', this.boundHandleDocumentMouseMove);
    }
    if (this.boundHandleDocumentMouseUp) {
      document.removeEventListener('mouseup', this.boundHandleDocumentMouseUp);
    }

    super.dispose();
  }
}
