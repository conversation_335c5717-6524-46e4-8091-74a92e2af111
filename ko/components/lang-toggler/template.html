<!-- ko ifnot: loading -->
<div class="dropdown" data-bind="log">
  <button
    data-toggle="dropdown"
    aria-haspopup="true"
    aria-expanded="false"
    class="f-btn f-btn-white f-btn--pill"
    data-bind="element: dropdown"
  >
    <span data-bind="text: activeLang().name" class="text mr-5p"></span>

    <span class="f-btn-append">
      <svg-icon
        params="name: 'chevron-bottom'"
        class="f-color-service"
      ></svg-icon>
    </span>
  </button>

  <div class="dropdown-menu dropdown-menu-right">
    <div class="dropdown-wrapper" data-bind="nativeScrollbar">
      <button
        class="dropdown-close button-ghost f-color-service"
        data-bind="click: function() { close() }"
      >
        <svg-icon params="name: 'times'" class="svg-icon--sm"></svg-icon>
      </button>
      <ul class="dropdown-menu__list">
        <!-- ko foreach: { data: langs, as: 'lang' } -->
        <!-- ko if: lang.sign === 'ru-RU'-->
        <!-- ko if: lang === $parent.activeLang() -->
        <li class="dropdown-item" style="opacity: 0.6">
          <span data-bind="text: name"></span>
        </li>
        <!-- /ko -->
        <!-- ko ifnot: lang === $parent.activeLang() -->
        <li
          class="dropdown-item"
          data-bind="click: function() { $parent.setLang($data) }"
        >
          <a href="javascript:void(0)" data-bind="text: name"></a>
        </li>
        <!-- /ko -->
        <!-- /ko -->
        <!-- /ko -->
      </ul>
    </div>
  </div>
</div>
<!-- /ko -->
