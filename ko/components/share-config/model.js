export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.share = params.share;
    this.disabled = params.disabled;
  }

  dispose() {
    this.subscriptions.forEach(s => s.dispose());
  }


  shapeTemplate(state) {
    const radius = {
      square: '0px',
      square_rounded: '9px',
      round: '50%'
    };

    let option = $('<span>')
      .text(state.text)
      .addClass(['d-flex', 'align-items-center']);
    let shape = $('<span>').css({
      width: '32px',
      height: '32px',
      background: '#CCD3D9',
      marginRight: '14px',
      borderRadius: radius[state.id],
      flexShrink: 0
    });
    option.prepend(shape);
    return option;
  }

  styleTemplate(state) {
    let itemTmp = (id) => {
      return `<div class="uscl-item">
			<span data-item="${id}" class="ico_uscl_soc ico_uscl ico_uscl-${id} uscl-${id}"></span>
		</div>`;
    };
    let items = ['vk', 'mail', 'ok', 'tumblr', 'pinterest']
      .map(itemTmp)
      .join('');
    let tmp = `<div class="uSocial-Share" data-type="share" data-mobile="sms" style="display: block;">
			<div class="uscl-bar uscl-default uscl-absolute uscl-horizontal uscl-rect uscl-size32 uscl-eachCounter0 uscl-counter0 uscl-${state.id}" style="display: block;">
				<div class="uscl-list">
					${items}
				</div>
			</div>
		</div>`;

    return $(tmp);
  }
}
