<!-- ko let: { $ctx: $component }-->
<div class="interscreen-share-form">


  <div class="form-group">
    <div class="f-check-row">
      <!-- ko foreach: share.networks -->
      <div class="f-check" data-bind="let: { inputId: 'share-' + $parent.share.id + '-network-' + id}">
        <input type="checkbox" class="f-check-input" data-bind="checked: checked, attr: { id: inputId }, disable: $ctx.disabled" name="check-value">
        <label class="f-check-label" data-bind="text: name, attr: {'for': inputId }"></label>
      </div>
      <!-- /ko -->
    </div>
  </div>

  <div class="form-group">
    <label class="form-label">Форма</label>
    <div class="select2-wrapper">
      <select data-bind="
      value: share.shape,
      valueAllowUnset: true,
      disable: $ctx.disabled,
      lazySelect2: {
        containerCssClass: 'form-control',
        wrapperCssClass: 'select2-container--form-control',

        templateSelection: shapeTemplate,
        templateResult: shapeTemplate,
      }
    ">
        <!-- ko foreach: share.shapes -->
        <option data-bind="value: id, text: name"></option>
        <!-- /ko -->
      </select>
    </div>
  </div>

  <div class="form-group">
    <label class="form-label">Стиль</label>
    <div class="select2-wrapper">
      <select data-bind="
      value: share.style,
      disable: $ctx.disabled,
      valueAllowUnset: true,
      lazySelect2: {
        containerCssClass: 'form-control',
        wrapperCssClass: 'select2-container--form-control',

        templateSelection: styleTemplate,
        templateResult: styleTemplate,
      }
    ">
        <!-- ko foreach: share.styles -->
        <option data-bind="value: id, text: name"></option>
        <!-- /ko -->
      </select>
    </div>
  </div>

  <div class="form-group">
    <div class="f-check" data-bind="let: { inputId: 'share-' + share.id + '-overlay'}">
      <input type="checkbox" class="f-check-input" data-bind="checked: share.hasOverlay, attr: { id: inputId }, disable: $ctx.disabled," name="check-value">
      <label class="f-check-label" data-bind="attr: {'for': inputId }">Добавить подложку</label>
    </div>
  </div>

  <div class="row">
    <div class="col-6">

      <label class="form-label mb-3">Счетчики</label>

      <div class="form-group">
        <div class="f-check" data-bind="let: { inputId: 'share-' + share.id + '-counter'}">
          <input type="checkbox" class="f-check-input" data-bind="checked: share.counter, attr: { id: inputId }, disable: $ctx.disabled," name="check-value">
          <label class="f-check-label" data-bind="attr: {'for': inputId }">Общий счетчик</label>
        </div>

        <!-- ko template: {
        foreach: templateIf(share.counter(), $data),
        afterAdd: slideAfterAddFactory(200),
        beforeRemove: slideBeforeRemoveFactory(200)
      } -->

        <div>
          <div class="d-flex mt-3" data-bind="let: {inputGroupName: 'share-' + share.id + '-counter-position'}">

            <div class="f-radio f-radio-custom  mr-3" data-bind="let: {inputId: 'share-' + share.id + '-counter-position-before'}">
              <input type="radio" class="f-radio-input" value="before" data-bind="attr: { name: inputGroupName, id: inputId }, checked: share.counterPosition, disable: $ctx.disabled,">

              <label class="f-radio-label" data-bind="attr: { 'for': inputId }">
                <svg width="33" height="16" viewBox="0 0 33 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2" data-bind="css: {
                    'f-color-primary': share.counterPosition() == 'before',
                    'f-color-ligh': share.counterPosition() != 'before'
                  }">
                  <mask id="path-1-inside-1" fill="white">
                    <path d="M27 0H3C1.34315 0 0 1.34315 0 3V13C0 14.6569 1.34315 16 3 16H11.5H12H27C28.6569 16 30 14.6569 30 13V12.2426C30 11.447 30.3161 10.6839 30.8787 10.1213L33 8L30.8787 5.87868C30.3161 5.31607 30 4.55301 30 3.75736V3C30 1.34315 28.6569 0 27 0Z" />
                  </mask>
                  <path d="M30.8787 5.87868L29.4645 7.29289L29.4645 7.29289L30.8787 5.87868ZM33 8L34.4142 9.41421L35.8284 8L34.4142 6.58579L33 8ZM30.8787 10.1213L32.2929 11.5355L32.2929 11.5355L30.8787 10.1213ZM3 2H27V-2H3V2ZM2 13V3H-2V13H2ZM28 3V3.75736H32V3H28ZM28 12.2426V13H32V12.2426H28ZM29.4645 7.29289L31.5858 9.41421L34.4142 6.58579L32.2929 4.46447L29.4645 7.29289ZM31.5858 6.58579L29.4645 8.70711L32.2929 11.5355L34.4142 9.41421L31.5858 6.58579ZM12 14H11.5V18H12V14ZM11.5 14H3V18H11.5V14ZM27 14H12V18H27V14ZM32 12.2426C32 11.9774 32.1054 11.7231 32.2929 11.5355L29.4645 8.7071C28.5268 9.64479 28 10.9166 28 12.2426H32ZM28 3.75736C28 5.08344 28.5268 6.35521 29.4645 7.29289L32.2929 4.46447C32.1054 4.27693 32 4.02258 32 3.75736H28ZM-2 13C-2 15.7614 0.238577 18 3 18V14C2.44772 14 2 13.5523 2 13H-2ZM28 13C28 13.5523 27.5523 14 27 14V18C29.7614 18 32 15.7614 32 13H28ZM27 2C27.5523 2 28 2.44772 28 3H32C32 0.238576 29.7614 -2 27 -2V2ZM3 -2C0.238576 -2 -2 0.238576 -2 3H2C2 2.44772 2.44772 2 3 2V-2Z" fill="currentColor" mask="url(#path-1-inside-1)" />
                </svg>
                <span class="f-fs-3 font-weight-500">Перед</span>
              </label>
            </div>

            <div class="f-radio f-radio-custom ml-3" data-bind="let: {inputId: 'share-' + share.id + '-counter-position-after'}">
              <input type="radio" class="f-radio-input" value="after" data-bind="attr: { name: inputGroupName, id: inputId }, checked: share.counterPosition, disable: $ctx.disabled,">

              <label class="f-radio-label" data-bind="attr: { 'for': inputId },">
                <svg class="f-transform-mirror-x mr-2" width="33" height="16" viewBox="0 0 33 16" fill="none" xmlns="http://www.w3.org/2000/svg" data-bind="css: {
                'f-color-primary': share.counterPosition() == 'after',
                'f-color-light': share.counterPosition() != 'after'
              }">
                  <mask id="path-1-inside-1" fill="white">
                    <path d="M27 0H3C1.34315 0 0 1.34315 0 3V13C0 14.6569 1.34315 16 3 16H11.5H12H27C28.6569 16 30 14.6569 30 13V12.2426C30 11.447 30.3161 10.6839 30.8787 10.1213L33 8L30.8787 5.87868C30.3161 5.31607 30 4.55301 30 3.75736V3C30 1.34315 28.6569 0 27 0Z" />
                  </mask>
                  <path d="M30.8787 5.87868L29.4645 7.29289L29.4645 7.29289L30.8787 5.87868ZM33 8L34.4142 9.41421L35.8284 8L34.4142 6.58579L33 8ZM30.8787 10.1213L32.2929 11.5355L32.2929 11.5355L30.8787 10.1213ZM3 2H27V-2H3V2ZM2 13V3H-2V13H2ZM28 3V3.75736H32V3H28ZM28 12.2426V13H32V12.2426H28ZM29.4645 7.29289L31.5858 9.41421L34.4142 6.58579L32.2929 4.46447L29.4645 7.29289ZM31.5858 6.58579L29.4645 8.70711L32.2929 11.5355L34.4142 9.41421L31.5858 6.58579ZM12 14H11.5V18H12V14ZM11.5 14H3V18H11.5V14ZM27 14H12V18H27V14ZM32 12.2426C32 11.9774 32.1054 11.7231 32.2929 11.5355L29.4645 8.7071C28.5268 9.64479 28 10.9166 28 12.2426H32ZM28 3.75736C28 5.08344 28.5268 6.35521 29.4645 7.29289L32.2929 4.46447C32.1054 4.27693 32 4.02258 32 3.75736H28ZM-2 13C-2 15.7614 0.238577 18 3 18V14C2.44772 14 2 13.5523 2 13H-2ZM28 13C28 13.5523 27.5523 14 27 14V18C29.7614 18 32 15.7614 32 13H28ZM27 2C27.5523 2 28 2.44772 28 3H32C32 0.238576 29.7614 -2 27 -2V2ZM3 -2C0.238576 -2 -2 0.238576 -2 3H2C2 2.44772 2.44772 2 3 2V-2Z" fill="currentColor" mask="url(#path-1-inside-1)" />
                </svg>
                <span class="f-fs-3 font-weight-500">После</span>
              </label>
            </div>
          </div>
        </div>

        <!-- /ko -->
      </div>

      <div class="form-group">
        <div class="f-check" data-bind="let: { inputId: 'share-' + share.id + '-icon-counters'}">
          <input type="checkbox" class="f-check-input" data-bind="checked: share.iconCounters, attr: { id: inputId }, enable: share.canSetIconCounter, disable: $ctx.disabled," name="check-value">
          <label class="f-check-label" data-bind="attr: {'for': inputId }">Счетчик для каждой иконки</label>
        </div>

      </div>


    </div>
    <div class="col-6">
      <label class="form-label mb-3">Размер</label>

      <div class="d-flex align-items-end" data-bind="let: { inputGroupName: 'share-' + share.id + '-size' }">
        <!-- ko foreach: share.sizes -->
        <div class="f-radio f-radio-custom" data-bind="let: { inputId: 'share-' + $parent.share.id + '-size-' + id}, " style='margin-right: 35px'>
          <input type="radio" class="f-radio-input" data-bind="
            attr: {name: inputGroupName, id: inputId },
            value: id,
            checked: $parent.share.size,
            disable: $ctx.disabled,">

          <label class="f-radio-label" data-bind="attr: { 'for': inputId }">

            <div data-bind="style: {
              width: size,
              height: size,
              borderRadius: ({ 48: '9px', 32: '7px', 24: '5px' })[id],
              border: '2px solid',
            }, css: {
              'f-color-primary': $parent.share.size() == id,
              'f-color-light': $parent.share.size() != id,
            }" class="mb-2"></div>
            <div data-bind="text: name, " class="f-fs-3 font-weight-500 text-center"></div>

          </label>
        </div>

        <!-- /ko -->
      </div>
    </div>
  </div>

</div>
<!-- /ko -->
