import { ViewModel } from './model';
import html from './template.html';
import pollHtml from './template.poll.html';
import './style.less';

ko.components.register('mailing-trigger', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('mailing-trigger');

      return new ViewModel(params, element);
    },
  },
  template: html,
});

ko.components.register('poll-mailing-trigger', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('mailing-trigger');

      return new ViewModel(params, element);
    },
  },
  template: pollHtml,
});
