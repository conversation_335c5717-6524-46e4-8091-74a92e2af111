<div class="row row--sm">
  <div class="col-6">
    <div class="form-group">
      <label
        class="form-label"
        data-bind="text: translator.t('Триггер')"
      ></label>

      <button
        type="button"
        class="btn-question"
        data-toggle="tooltip"
        data-placement="top"
        data-bind="attr: {
                        title: translator.t('Действие клиента, по которому автоматически ему будет отправлена анкета опроса')
                    }"
      ></button>

      <div class="mb-4">
        <div
          class="settings__common-select"
          data-bind="css: {
                        'is-invalid': formControlErrorStateMatcher(model.trigger),
                        'is-valid': formControlSuccessStateMatcher(model.trigger),
                    }"
        >
          <select
            data-bind="value: model.trigger,
                        valueAllowUnset: true,
                        lazySelect2: {
                        containerCssClass: 'form-control',
                        wrapperCssClass: 'select2-container--form-control',
                        dropdownAutoWidth: false,
                        }"
            data-placeholder=""
          >
            <!-- ko foreach: model.triggers -->
            <option data-bind="value: id, text: name"></option>
            <!-- /ko -->
          </select>

          <validation-feedback
            params="show: formControlErrorStateMatcher(model.trigger), text: model.trigger.error"
          >
          </validation-feedback>
        </div>
      </div>
    </div>
  </div>

  <!-- ko template: {
      foreach: templateIf(model.triggerType() === 'BIRTHDAY_TRIGGER', $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(0)
    } -->
  <div class="col-6">
    <div class="form-group ghost-label">
      <div class="d-flex">
        <div class="flex-grow-1" style="max-width: 175px">
          <div
            class="select2-wrapper"
            data-bind="css: {
                            'is-invalid': formControlErrorStateMatcher(model.birthdayCondition),
                            'is-valid': formControlSuccessStateMatcher(model.birthdayCondition),
                        }"
          >
            <select
              data-bind="
                        value: model.birthdayCondition,
                        valueAllowUnset: true,
                        lazySelect2: {
                          containerCssClass: 'form-control',
                          wrapperCssClass: 'select2-container--form-control'
                        }"
              data-placeholder=""
            >
              <!-- ko foreach: model.birthdayConditions -->
              <option data-bind="value: id, text: name"></option>
              <!-- /ko -->
            </select>

            <validation-feedback
              params="show: formControlErrorStateMatcher(model.birthdayCondition), text: model.birthdayCondition.error"
            >
            </validation-feedback>
          </div>
        </div>

        <!-- ko if: model.birthdayCondition() != '1' -->
        <div class="ml-3" style="min-width: 105px">
          <input
            class="form-control form-control--simple-validation"
            data-bind="
                            textInput: model.birthdayDays,
                            numericIntervalField: { min: 1, max: 366 },
                            css: {
                                'is-invalid': formControlErrorStateMatcher(model.birthdayDays),
                                'is-valid': formControlSuccessStateMatcher(model.birthdayDays),
                            }"
            maxlength="3"
            style="max-width: 65px"
          />

          <validation-feedback
            params="show: formControlErrorStateMatcher(model.birthdayDays), text: model.birthdayDays.error"
          >
          </validation-feedback>
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko template: {
      foreach: templateIf(model.triggerType() === 'DAYS_WITHOUT_ORDER_TRIGGER', $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(0)
    } -->
  <div class="col-6">
    <div class="form-group ghost-label">
      <input
        class="form-control"
        data-bind="textInput: model.days, onlyNumbers, css: {
                        'is-invalid': formControlErrorStateMatcher(model.days),
                        'is-valid': formControlSuccessStateMatcher(model.days),
                    }, attr: {
                        placeholder: translator.t('Количество дней')
                    }"
      />

      <validation-feedback
        params="show: formControlErrorStateMatcher(model.days), text: model.days.error"
      >
      </validation-feedback>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko template: {
      foreach: templateIf(model.triggerType() === 'ORDERS_TRIGGER', $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(0)
    } -->
  <div class="col-6">
    <div class="form-group">
      <label class="form-label">
        <span data-bind="text: translator.t('Кол-во заказов')"></span>
        <button
          type="button"
          class="btn-question"
          data-toggle="tooltip"
          data-placement="top"
          data-bind="attr: {
                            title: translator.t('Кол-во заказов')
                        }"
        ></button>
      </label>
      <input
        class="form-control"
        placeholder=""
        data-bind="textInput: model.orders, onlyNumbers, css: {
                        'is-invalid': formControlErrorStateMatcher(model.orders),
                        'is-valid': formControlSuccessStateMatcher(model.orders),
                    }"
      />

      <validation-feedback
        params="show: formControlErrorStateMatcher(model.orders), text: model.orders.error"
      >
      </validation-feedback>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko template: {
      foreach: templateIf(model.triggerType() === 'NO_ORDER_AS_USUAL_TRIGGER', $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(0)
    } -->
  <div class="col-6">
    <div class="form-group">
      <label class="form-label">
        <span data-bind="text: translator.t('Период, дней')"></span>
        <button
          type="button"
          class="btn-question"
          data-toggle="tooltip"
          data-placement="top"
          data-bind="attr: {
                            title: translator.t('Период, дней')
                        }"
        ></button>
      </label>
      <input
        class="form-control"
        placeholder=""
        maxlength="10"
        data-bind="textInput: model.days, onlyNumbers, css: {
              'is-invalid': formControlErrorStateMatcher(model.days),
              'is-valid': formControlSuccessStateMatcher(model.days),
          }"
      />

      <validation-feedback
        params="show: formControlErrorStateMatcher(model.days), text: model.days.error"
      >
      </validation-feedback>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko template: {
      foreach: templateIf(['ORDER_MADE_TRIGGER', 'ORDER_DELIVERED_TRIGGER'].includes(model.triggerType()), $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(0)
    } -->
  <div class="col-6">
    <div class="form-group ghost-label">
      <input
        class="form-control"
        data-time-picker-wrapper-class="settings__common-trigger-form-group-time-form-control"
        data-bind="textInput: model.time, timePicker, css: {
              'is-invalid': formControlErrorStateMatcher(model.time),
              'is-valid': formControlSuccessStateMatcher(model.time),
              }"
      />

      <validation-feedback
        params="show: formControlErrorStateMatcher(model.time), text: model.time.error"
      >
      </validation-feedback>
    </div>
  </div>
  <!-- /ko -->
</div>

<!-- ko template: {
  foreach: templateIf(model.triggerType() == 'NO_ORDER_AS_USUAL_TRIGGER', $data),
  afterAdd: fadeAfterAddFactory(200),
} -->
<div class="row row--sm">
  <div class="col-6">
    <div class="form-group">
      <label class="form-label">
        <span data-bind="text: translator.t('Кол-во заказов')"></span>,
        <span
          class="font-weight-normal"
          data-bind="text: translator.t('от – до')"
        ></span>
        <button
          class="btn-question"
          data-bind="tooltip, tooltipText: translator.t('Кол-во заказов')"
        ></button
      ></label>

      <div class="d-flex align-items-center">
        <input
          type="text"
          class="form-control"
          maxlength="10"
          data-bind="textInput: model.minOrders, onlyNumbers, css: {
                        'is-invalid': formControlErrorStateMatcher(model.minOrders),
                        'is-valid': formControlSuccessStateMatcher(model.minOrders),
                      }"
        />

        <span class="px-1">–</span>

        <input
          type="text"
          class="form-control"
          maxlength="10"
          data-bind="textInput: model.maxOrders, onlyNumbers, css: {
                       'is-invalid': formControlErrorStateMatcher(model.maxOrders),
                       'is-valid': formControlSuccessStateMatcher(model.maxOrders),
                     }"
        />
      </div>

      <validation-feedback
        params="show: formControlErrorStateMatcher(model.ordersCount), text: model.ordersCount.error"
      >
      </validation-feedback>
    </div>
  </div>

  <div class="col-6">
    <div class="form-group">
      <label class="form-label">
        <span data-bind="text: translator.t('Коэффициент')"></span>
        <button
          class="btn-question"
          data-bind="tooltip, tooltipText: translator.t('Коэффициент')"
        ></button
      ></label>

      <input
        type="text"
        class="form-control text-left"
        data-bind="textInput: model.coefficient, decimalNumbers: { max: 10000 }, css: {
                         'is-invalid': formControlErrorStateMatcher(model.coefficient),
                         'is-valid': formControlSuccessStateMatcher(model.coefficient),
                       }"
      />

      <validation-feedback
        params="show: formControlErrorStateMatcher(model.coefficient), text: model.coefficient.error"
      >
      </validation-feedback>
    </div>
  </div>
</div>
<!-- /ko -->

<!-- ko template: {
  foreach: templateIf(!model.isOrderTrigger(), $data),
  afterAdd: fadeAfterAddFactory(200),
} -->

<div class="row row--sm">
  <div class="col-6">
    <div class="form-group">
      <label class="form-label">
        <span data-bind="text: translator.t('Время рассылки опроса')"></span>
        <button
          class="btn-question"
          data-bind="tooltip, tooltipText: translator.t('Время рассылки опроса')"
        ></button
      ></label>

      <input
        type="text"
        class="form-control"
        placeholder="00:00"
        data-bind="textInput: model.sendTime, foquzMask, maskPattern: '99:99', css: {
                  'is-invalid': formControlErrorStateMatcher(model.sendTime),
                  'is-valid': formControlSuccessStateMatcher(model.sendTime),
                  }"
      />

      <validation-feedback
        params="show: formControlErrorStateMatcher(model.sendTime), text: model.sendTime.error"
      >
      </validation-feedback>
    </div>
  </div>
</div>

<!-- /ko -->
