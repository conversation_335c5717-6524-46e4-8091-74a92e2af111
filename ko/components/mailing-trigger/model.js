import { Translator } from '@/utils/translate';
const TriggersTranslator = Translator('triggers');

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];

    this.translator = TriggersTranslator;

    this.blocked = params.blocked;

    this.model = params.trigger;
    this.formControlErrorStateMatcher = params.formControlErrorStateMatcher || commonFormControlErrorStateMatcher();
    this.formControlSuccessStateMatcher = params.formControlSuccessStateMatcher || commonFormControlSuccessStateMatcher();
  }

  dispose() {
    this.subscriptions.forEach(s => s.dispose());
  }
}
