<!-- ko if: translator.loaded -->
<div class="row">
  <div class="col-6">
    <div class="form-group">
      <label class="form-label" data-bind="text: translator.t('Триггер')"></label>

      <button type="button"
              class="btn-question"

              data-placement="top"
              data-bind="tooltip, tooltipText: translator.t('Действие клиента, по которому автоматически ему будет отправлена анкета опроса')"></button>

      <div class="mb-4">

        <div class="settings__common-select"
             data-bind="css: {
                      'is-invalid': formControlErrorStateMatcher(model.trigger),
                      'is-valid': formControlSuccessStateMatcher(model.trigger),
                  }">
          <select data-bind="value: model.trigger,
                      valueAllowUnset: true,
                      disable: blocked,
                      lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      dropdownAutoWidth: false,
                      }"
                  data-placeholder="">

            <!-- ko foreach: model.triggers -->
            <option data-bind="value: id, text: name"></option>
            <!-- /ko -->
          </select>

          <validation-feedback params="show: formControlErrorStateMatcher(model.trigger), text: model.trigger.error">
          </validation-feedback>
        </div>
      </div>
    </div>
  </div>

  <!-- ko template: {
    foreach: templateIf(['ORDER_MADE_TRIGGER', 'ORDER_DELIVERED_TRIGGER'].includes(model.triggerType()), $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(0)
  } -->
  <div class="col-6">
    <div class="form-group ghost-label">
      <input class="form-control"
             style="width: 120px"
             data-time-picker-wrapper-class="settings__common-trigger-form-group-time-form-control"
             data-bind="textInput: model.time, timePicker, css: {
          'is-invalid': formControlErrorStateMatcher(model.time),
          'is-valid': formControlSuccessStateMatcher(model.time),
          }, disable: blocked">

      <validation-feedback params="show: formControlErrorStateMatcher(model.time), text: model.time.error">
      </validation-feedback>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko template: {
    foreach: templateIf(model.triggerType() === 'DAYS_WITHOUT_ORDER_TRIGGER', $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(0)
  } -->
  <div class="col-6">
    <div class="row">
      <div class="col-5">
        <div class="form-group ghost-label">
          <input class="form-control"
                 data-bind="textInput: model.days, onlyNumbers, css: {
                          'is-invalid': formControlErrorStateMatcher(model.days),
                          'is-valid': formControlSuccessStateMatcher(model.days),
                      }, disable: blocked, attr: {
                        placeholder: translator.t('Кол-во дней')
                      }">
          <validation-feedback params="show: formControlErrorStateMatcher(model.days), text: model.days.error">
          </validation-feedback>
        </div>
      </div>
      <div class="col-7">
        <div class="form-group">
          <label class="form-label">
            <span  data-bind="text: translator.t('Время рассылки опроса')"></span> <button class="btn-question"
                    data-bind="tooltip, tooltipText: translator.t('Время рассылки опроса')"></button></label>
          <input type="text"
                 class="form-control"
                 data-bind="textInput: model.sendTime, foquzMask, maskPattern: '99:99', css: {
                    'is-invalid': formControlErrorStateMatcher(model.sendTime),
                    'is-valid': formControlSuccessStateMatcher(model.sendTime),
                    }, disable: blocked, attr: {
                      placeholder: translator.t('00:00')
                    }">
          <validation-feedback params="show: formControlErrorStateMatcher(model.sendTime), text: model.sendTime.error">
          </validation-feedback>
        </div>
      </div>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko template: {
    foreach: templateIf(model.triggerType() === 'ORDERS_TRIGGER', $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(0)
  } -->
  <div class="col-6">
    <div class="row">
      <div class="col-5">
        <div class="form-group">
          <label class="form-label">
            <span  data-bind="text: translator.t('Кол-во заказов')"></span><button class="btn-question"
            data-bind="tooltip, tooltipText: translator.t('Кол-во заказов')"></button></label>
          <input class="form-control"
                 placeholder=""
                 data-bind="textInput: model.orders, onlyNumbers, css: {
                          'is-invalid': formControlErrorStateMatcher(model.orders),
                          'is-valid': formControlSuccessStateMatcher(model.orders),
                      }, disable: blocked">
          <validation-feedback params="show: formControlErrorStateMatcher(model.orders), text: model.orders.error">
          </validation-feedback>
        </div>
      </div>
      <div class="col-7">
        <div class="form-group">
          <label class="form-label">
            <span  data-bind="text: translator.t('Время рассылки опроса')"></span>
             <button class="btn-question"
                    data-bind="tooltip, tooltipText: translator.t('Время рассылки опроса')"></button></label>
          <input type="text"
                 class="form-control"
                 data-bind="textInput: model.sendTime, foquzMask, maskPattern: '99:99', css: {
                    'is-invalid': formControlErrorStateMatcher(model.sendTime),
                    'is-valid': formControlSuccessStateMatcher(model.sendTime),
                    }, disable: blocked, attr: {
                      placeholder: translator.t('00:00')
                    }">
          <validation-feedback params="show: formControlErrorStateMatcher(model.sendTime), text: model.sendTime.error">
          </validation-feedback>
        </div>
      </div>
    </div>
  </div>
  <!-- /ko -->
</div>

<!-- ko template: {
  foreach: templateIf(model.triggerType() === 'NO_ORDER_AS_USUAL_TRIGGER', $data),
  afterAdd: fadeAfterAddFactory(200),
  beforeRemove: fadeBeforeRemoveFactory(0)
} -->
<div class="row">
  <div class="col-6">
    <div class="row">
      <div class="col-5">
        <div class="form-group">
          <label class="form-label">
              <span  data-bind="text: translator.t('Период')"></span>, <span class="font-weight-normal" data-bind="text: translator.t('дней')"></span>
            <button type="button"
                    class="btn-question"
                    data-bind="tooltip, tooltipText: translator.t('Период, дней')"
                    data-placement="top"></button>
          </label>
          <input class="form-control"
                 placeholder=""
                 maxlength="10"
                 data-bind="textInput: model.days, onlyNumbers, css: {
                'is-invalid': formControlErrorStateMatcher(model.days),
                'is-valid': formControlSuccessStateMatcher(model.days),
            }, disable: blocked">
          <validation-feedback params="show: formControlErrorStateMatcher(model.days), text: model.days.error">
          </validation-feedback>
        </div>
      </div>
      <div class="col-7">
        <div class="form-group">
          <label class="form-label">
            <span  data-bind="text: translator.t('Кол-во заказов')"></span>, <span class="font-weight-normal" data-bind="text: translator.t('от – до')"></span> <button
                    class="btn-question"
                    data-bind="tooltip, tooltipText: translator.t('Кол-во заказов')"
                   ></button></label>



          <div class="d-flex align-items-center">
            <input type="text"
                   class="form-control"
                   maxlength="10"
                   data-bind="textInput: model.minOrders, onlyNumbers, css: {
                          'is-invalid': formControlErrorStateMatcher(model.minOrders),
                          'is-valid': formControlSuccessStateMatcher(model.minOrders),
                        }, disable: blocked">

            <span class="px-1">–</span>

            <input type="text"
                   class="form-control"
                   maxlength="10"
                   data-bind="textInput: model.maxOrders, onlyNumbers, css: {
                         'is-invalid': formControlErrorStateMatcher(model.maxOrders),
                         'is-valid': formControlSuccessStateMatcher(model.maxOrders),
                       }, disable: blocked">
          </div>

          <validation-feedback
                               params="show: formControlErrorStateMatcher(model.ordersCount), text: model.ordersCount.error">
          </validation-feedback>
        </div>
      </div>
    </div>
  </div>
  <div class="col-6">
    <div class="row">
      <div class="col-5">
        <div class="form-group">
          <label class="form-label">
            <span  data-bind="text: translator.t('Коэффициент')"></span>
             <button class="btn-question"
                    data-bind="tooltip, tooltipText: translator.t('Коэффициент')"></button></label>


          <input type="text"
                 class="form-control text-left"
                 data-bind="textInput: model.coefficient, decimalNumbers: { max: 10000 }, css: {
                           'is-invalid': formControlErrorStateMatcher(model.coefficient),
                           'is-valid': formControlSuccessStateMatcher(model.coefficient),
                         }, disable: blocked">

          <validation-feedback
                               params="show: formControlErrorStateMatcher(model.coefficient), text: model.coefficient.error">
          </validation-feedback>

        </div>
      </div>
      <div class="col-7">
        <div class="form-group">
          <label class="form-label">
            <span  data-bind="text: translator.t('Время рассылки опроса')"></span> <button class="btn-question"
                    data-bind="tooltip, tooltipText: translator.t('Время рассылки опроса')"></button></label>

          <input type="text"
                 class="form-control"
                 data-bind="textInput: model.sendTime, foquzMask, maskPattern: '99:99', css: {
                    'is-invalid': formControlErrorStateMatcher(model.sendTime),
                    'is-valid': formControlSuccessStateMatcher(model.sendTime),
                    }, disable: blocked, attr: {
                      placeholder: translator.t('00:00')
                    }">

          <validation-feedback params="show: formControlErrorStateMatcher(model.sendTime), text: model.sendTime.error">
          </validation-feedback>
        </div>

      </div>
    </div>
  </div>
</div>
<!-- /ko -->
<!-- /ko -->
