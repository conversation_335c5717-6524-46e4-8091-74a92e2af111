import { FoquzComponent } from 'Models/foquz-component';
import { doubleGradientCss } from 'Utils/color/gradient';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.shape = params.shape || 'rect';
    this.startLabel = params.startLabel;
    this.endLabel = params.endLabel;
    this.startColor = params.startColor;
    this.endColor = params.endColor;
    this.value = params.value;

    this.gradient = [];

    if (this.shape == 'circle') {
      element.classList.add('sem-diff-row--circle');
      this.gradient = doubleGradientCss({
        start: this.startColor,
        end: this.endColor,
        neutral: '#CFD8DC',
        count: 5,
        center: 3
      });
    }
  }
}
