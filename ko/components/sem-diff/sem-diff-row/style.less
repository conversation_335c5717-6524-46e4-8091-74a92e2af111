@import 'Style/breakpoints';
@import 'Style/colors';

.sem-diff-row {
  display: block;

  &__labels {
    display: none;
  }

  &__values {
    display: flex;
    align-items: center;
    margin-left: -1px;
    margin-right: -1px;

    .sem-diff-item {
      margin-left: 1px;
      margin-right: 1px;
    }
  }

  &--circle {
    .sem-diff-row__values {
      margin-left: -3px;
      margin-right: -3px;

      .sem-diff-item {
        margin-left: 3px;
        margin-right: 3px;
      }
    }
  }

  .sem-diff-row + & {
    margin-top: 10px;
  }

  .only-mobile({
    .sem-diff-row__labels {
      display: flex;
      align-items: center;
      width: 100%;

      .sem-diff-label {
        margin-bottom: 10px;
      }
    }

    .sem-diff-row__wrapper {
      .sem-diff-label {
        span {
          display: none;
        }
      }
    }
  })
}

.sem-diff-label {
  width: 100%;
  color: @f-color-service;

  &--start {
    padding-right: 10px;
    text-align: right;
  }

  &--end {
    padding-left: 10px;
  }
}
