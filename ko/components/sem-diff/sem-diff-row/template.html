<div class="sem-diff-row__labels">
  <div class="flex-grow-1 sem-diff-label sem-diff-label--start">
    <span data-bind="text: startLabel"></span>
  </div>
  <div class="flex-grow-1 sem-diff-label sem-diff-label--end">
    <span data-bind="text: endLabel"></span>
  </div>
</div>


<div class="d-flex align-items-center w-100 sem-diff-row__wrapper">

  <div class="flex-grow-1 sem-diff-label sem-diff-label--start">
    <span data-bind="text: startLabel"></span>
  </div>


  <div class="sem-diff-row__values">

    <!-- ko foreach: { data: [1, 2, 3, 4, 5], as: 'point' } -->

    <sem-diff-item params="shape: $component.shape, point: point, selected: $component.value == point"
                   data-bind="
              style: {
                color: $component.shape == 'circle' ? $component.gradient[point - 1] : ''
              }"></sem-diff-item>


    <!-- /ko -->


  </div>


  <div class="flex-grow-1 sem-diff-label sem-diff-label--end">
    <span data-bind="text: endLabel"></span>
  </div>

</div>
