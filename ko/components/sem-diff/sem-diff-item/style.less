@import 'Style/colors';


.sem-diff-item {

  width: 28px;
  height: 24px;

  display: flex;
  justify-content: center;
  align-items: center;

  background: #e1e8eb;
  border-radius: 4px;

  color: @f-color-text;
  font-size: 12px;
  font-weight: 400;

  .svg-icon {
    display: none;
    color: white;
    width: 14px;
    height: 14px;
  }

  &--selected {
    background: @f-color-primary;
    color: white;

    .svg-icon {
      display: block;
    }
  }

  &--circle {
    width: 15px;
    height: 15px;

    border: 2px solid currentColor;
    border-radius: 50%;
    background: transparent;

    .svg-icon {
      display: none;
    }

    &[data-point="1"], &[data-point="5"] {
      width: 24px;
      height: 24px;
    }

    &[data-point="2"], &[data-point="4"] {
      width: 18px;
      height: 18px;
    }

    &.sem-diff-item--selected {
      background: currentColor;

      .svg-icon {
        display: block;
      }
    }
  }
}
