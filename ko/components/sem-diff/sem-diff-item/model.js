import { FoquzComponent } from 'Models/foquz-component';
import { doubleGradientCss } from 'Utils/color/gradient';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.shape = params.shape || 'rect';
    this.point = params.point;
    this.selected = params.selected;

    element.setAttribute('data-point', this.point);
    if (this.shape == 'circle') {
      element.classList.add('sem-diff-item--circle');
    }
    if (this.selected) {
      element.classList.add('sem-diff-item--selected');
    }
  }
}
