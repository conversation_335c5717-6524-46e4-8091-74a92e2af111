import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.shape = params.shape;
    this.rows = params.rows;
    this.startColor = params.startColor;
    this.endColor = params.endColor;

    this.hasLabels = this.rows.some(r => r.startLabel || r.endLabel);
    if (!this.hasLabels) element.classList.add('sem-diff--no-labels');
  }
}
