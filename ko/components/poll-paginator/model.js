import { INTER_BLOCK } from "Data/question-types";
import { FoquzComponent } from "Models/foquz-component";
import "@/presentation/views/fc-paginator";

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.mode = params.mode;

    this.viewState = params.viewState || {};

    this.questions = params.questions;

    this.translation = params.translation;
    this.isFinishScreen = params.isFinishScreen;
    this.nextQuestionIndex = params.nextQuestionIndex;

    this.names = ko.computed(() => {
      const translation = ko.toJS(this.translation);
      if (!translation) return {};

      const { questions } = translation;

      const result = {};

      (questions || []).forEach((q) => {
        result[q.foquz_question_id] = q.name;
      });

      return result;
    });

    this.filteredQuestions = ko.computed(() => {
      return this.questions()
        .map((q, i) => {
          q.realIndex = i;

          return q;
        })
        .filter((q) => q._showNumber);
    });

    this.questionsCount = ko.computed(() => {
      return this.filteredQuestions().length;
    });

    this.touchedCount = ko.pureComputed(() => {
      if (!this.nextQuestionIndex()) {
        return this.filteredQuestions().filter((q) => q.touched()).length;
      }
      if (!this.questions()[this.nextQuestionIndex()]) {
        return this.questionsCount()
      }
      if (!this.filteredQuestions().length || (this.realIndex() >= 0 && !this.filteredQuestions()[this.realIndex()]?.touched())) {
        return this.realIndex();
      }

      if (this.nextQuestionIndex() === this.filteredQuestions().length && this.filteredQuestions().length == this.questions().length) {
        return this.questionsCount();
      }
      if (this.nextQuestionIndex() === this.filteredQuestions().length && (this.filteredQuestions().length - this.questions().length == 1)) {
        return this.questionsCount() - 1;
      }
      if (this.nextQuestionIndex() !== this.filteredQuestions().length && (this.filteredQuestions().length - this.questions().length == 2)) {
        return this.questionsCount() - 2;
      }
      if (this.nextQuestionIndex() > this.filteredQuestions().length && this.questions()[this.nextQuestionIndex()]?.type == 16) {
        return this.questionsCount();
      }
      if (this.filteredQuestions().findIndex((q) => {
        return q.questionId == this.questions()[this.nextQuestionIndex()]?.questionId;
      }) == -1) {
        return this.realIndex();
      }
      
      return this.filteredQuestions().findIndex((q) => {
        return q.questionId == this.questions()[this.nextQuestionIndex()].questionId;
      });
    });
    this.touchedPercent = ko.pureComputed(() => {
      if (this.touchedCount() >= 0) {
        return Math.round((this.touchedCount() / this.questionsCount()) * 100);
      } else {
        return 0
      }      
    });

    this.activeId = params.activeId;
    this.realIndex = ko.pureComputed(() => {
      let index = this.filteredQuestions().findIndex((q) => {
        return q.questionId == this.activeId();
      });
      return index;
    });

    this.slider = ko.observable(null);

    this.pagesMode = params.pagesMode;
    this.pages = params.pages;
    this.filteredPages = ko.computed(() => {
      return this.pages()
        .map((p, i) => {
          p.realIndex = i;
          return p;
        })
        .filter((p) => p.type != "start" && p.type != "end");
    });
    this.activePageId = params.activePageId;
    this.realPageIndex = ko.pureComputed(() => {
      let index = this.filteredPages().findIndex((p) => {
        return p.id == this.activePageId();
      });
      return index;
    });

    this.realIndex.subscribe((index) => {
      if (index < 0) return;
      setTimeout(() => {
        if (this.mode == 1) {
          this.slideTo(index);
        }
      }, 1000);
    });

    this.paginatorItems = ko.computed(() => {
      if (ko.toJS(this.pagesMode)) {
        return this.filteredPages().map((p) => {
          return {
            id: p.id,
            label: p.name,
            blocked: p.blocked,
            visible: true,
          };
        });
      }
      return this.filteredQuestions().map((q) => {
        return {
          id: q.questionId,
          label: q.name,
          blocked: ko.computed(() => {
            return q.blocked() || ko.toJS(q.isEmpty);
          }),
          visible:
            q.questionId in this.viewState
              ? this.viewState[q.questionId]
              : true,
        };
      });
    });

    this.paginatorActive = ko.computed(() => {
      if (ko.toJS(this.pagesMode)) {
        return this.activePageId();
      }
      return ko.toJS(this.activeId);
    });
  }

  slideTo(index) {
    $(window).resize();
    let slider = this.slider();
    if (!slider) return;
    slider.update();
    slider.slideTo(index - 1);
    slider.update();
  }
}
