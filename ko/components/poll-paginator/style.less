@import 'Style/breakpoints';

.poll-paginator {
  .fc-paginator {
    color: white;
    margin-top: 16px;
    margin-bottom: 16px;
  }
  .fc-paginator-item__label {
    color: var(--poll-text-on-bg);
  }
  .poll-progress-bar {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 710px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 24px;
    margin-bottom: 16px;
    padding-left: 15px;
    padding-right: 15px;
    font-weight: 700;

    color: var(--poll-text-on-bg);

    .progress-line {
      flex-grow: 1;
      margin-left: 18px;
      margin-right: 18px;
      height: 10px;
      background-color: transparent;
      border: 1px solid var(--poll-main-color);

      &__indicator {
        background: var(--poll-main-color);
      }
    }

    .only-mobile({
      margin-top: 18px;
      margin-bottom: 16px;
    });
  }
}
