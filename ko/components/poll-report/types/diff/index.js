import { ViewModel } from './model';
import html from './template.html';
import './style.less';

import 'Components/sem-diff';

ko.components.register('poll-report-question-diff', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('poll-report-question');
      element.classList.add('poll-report-question--diff');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
