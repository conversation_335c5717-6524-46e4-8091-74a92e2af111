import { FoquzComponent } from 'Models/foquz-component';
import { doubleGradientCss } from 'Utils/color/gradient';
import { NPSGradient, NPSLabel } from 'Legacy/utils/nps';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.question = params.question;

    let config = this.question.config;


    this.backgroundColor = (() => {
      if (config.design == 2) return 'none';
      let scale = NPSGradient(config.startColor, config.endColor);

      return scale[this.question.rating];
    })();
  }
}
