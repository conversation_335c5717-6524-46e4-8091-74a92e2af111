<!-- ko using: question -->
<div class="poll-report-question__answer">
  <div
    class="nps-scale"
    data-bind="
      css: {
        'nps-scale--colored': config.design !== 2,
      },
    "
  >
    <div class="nps-scale__list">
      <!-- ko if: isVariants -->
      <!-- ko foreach: variants -->
      <div class="poll-report-question__answer-comment-header" data-bind="text: text"></div>
      <div
        class="nps-scale__item mr-3"
        data-bind="
          style: {
            'background-color': $parent.config.design == 2 ? 'none' : $parent.colorScale[+$parent.answerData[+id]],
          },
        "
      >
        <span data-bind="text: $parent.answerData[+id]"></span>
      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko ifnot: isVariants -->
      <div
        class="nps-scale__item mr-3"
        data-bind="
          style: {
            'background-color': $component.backgroundColor,
          },
        "
      >
        <span data-bind="text: rating"></span>
      </div>
      <!-- /ko -->
    </div>
  </div>
</div>
<!-- /ko -->
