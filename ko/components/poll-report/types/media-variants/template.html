<!-- ko using: question -->
<!-- ko if: hasPoints -->
<div class="poll-report-question__mark">
  <correct-mark-controller params="value: answerPoints, max: maxPoints"></correct-mark>
</div>
<!-- /ko -->
<div class="poll-report-question__answer">
  <!-- ko foreach: selectedVariants -->
  <!-- ko let: {
    isVideo: $data.url !== $data.poster,
  } -->
  <media-preview
    params="
      poster: poster,
      gallery: $component.selectedGallery,
      index: $index(),
      description: description,
      isVideo: isVideo,
    "
  ></media-preview>
  <!-- /ko -->
  <!-- /ko -->
</div>
<!-- ko if: hasPoints -->
<!-- ko ifnot: answerPoints == maxPoints -->
<div class="poll-report-question__correct-answer">
  <div class="f-color-service mb-1">Правильный ответ:</div>
  <div class="d-flex flex-wrap mx-n1">
    
    <!-- ko if: correctVariants.length -->
    <!-- ko foreach: correctVariants -->
    <div class="m-1">
      <media-preview params="poster: poster, gallery: $component.correctGallery, index: $index(), description: description">
      </media-preview>
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <!-- ko ifnot: correctVariants.length -->
    <div class="m-1">не задан</div>
    <!-- /ko -->
  </div>
</div>
<!-- /ko -->
<!-- /ko -->
<!-- /ko -->
