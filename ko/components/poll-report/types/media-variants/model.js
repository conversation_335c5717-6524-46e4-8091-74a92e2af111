import { FoquzComponent } from 'Models/foquz-component';


export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.question = params.question;

    console.log('media variants', this.question)

    this.selectedGallery = this.question.selectedVariants.map(v => {
      return {
        src: v.url,
        opts: {
          caption: v.description
        }
      }
    })

    this.correctGallery = this.question.correctVariants.map(v => {
      return {
        src: v.url,
        opts: {
          caption: v.description
        }
      }
    })
  }

}
