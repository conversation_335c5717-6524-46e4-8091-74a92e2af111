<!-- ko using: question -->
<!-- ko if: hasPoints && !without_points -->
<div class="poll-report-question__mark">
  <correct-mark-controller params="value: answerPoints, max: maxPoints"></correct-mark>
</div>
<!-- /ko -->
<div class="poll-report-question__answer" data-bind="css: {'d-flex align-items-center flex-wrap': variants.some(i => i.file_id)}">
  <!-- ko foreach: answerReportList -->
  <!-- ko if: $data === $parent.selfVariant-->
  <div class="poll-report-question__self-variant-header">
    Свой ответ
  </div>
  <!-- /ko -->
  <!-- ko let: { currVar: $parent.variants.find(i => i.id == $data.id ) } -->
  
  <!-- ko if: currVar && currVar.file_id -->
  <a data-bind="attr: {href: window.location.origin + currVar.file_url}" target="_blank" class="file-loader-preview_process-review-print-link mb-10p">
    <img
      data-bind="attr: {
                  src: currVar.preview_url
              }"
      alt=""
    />
  </a>
  <file-loader-preview class="file-loader-preview file-loader-preview_process-review mr-10p mb-10p" data-bind="click: function (_, event) {
    event.stopPropagation();
    }," params="loading: false, disabled: true, file: currVar.file_url, preview: currVar.preview_url,
    onRemove: function() { 
        variant.file(null)
        variant.value('')
    }">

  </file-loader-preview>
  <!-- /ko -->
  <div class="poll-report-question__answer-item" data-bind="text: $data.text, css: {'with-file': currVar && currVar.file_id}"></div>
  <!-- /ko -->
  <!-- /ko -->
</div>
<!-- ko if: hasPoints && !without_points -->
<!-- ko ifnot: answerPoints == maxPoints -->
<div class="poll-report-question__correct-answer">
  <div class="f-color-service mb-1">Правильный ответ:</div>
  <div data-bind="text: correctVariantsText"></div>
</div>
<!-- /ko -->
<!-- /ko -->
<!-- /ko -->
