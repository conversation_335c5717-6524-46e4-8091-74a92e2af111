<!-- ko using: question -->
<div class="poll-report-question__answer">
  <!-- ko if: (files.some(i => i.name.lastIndexOf(".mp3") >= 0 || i.name.lastIndexOf(".wmv") >= 0 || i.name.lastIndexOf(".ogg") >= 0 || i.name.lastIndexOf(".m4a") >= 0)) && files.filter(i => i.name.includes('.mp3') || i.name.includes('.wma') || i.name.includes('.ogg') || i.name.includes('.m4a')).length !== files.length -->
  <div class="poll-report-question__answer-file-type mb-10p">Фото/видео</div>
  <!-- /ko -->
  <div class="d-flex flex-wrap mx-n5p" data-bind="css: {'mb-n15p': $parents[3].questionsList.indexOf($parents[3].questionsList.find(i => i.question_id == $parent.question.id)) !== $parents[3].questionsList.length - 1  && !(files.some(i => i.name.lastIndexOf('.mp3') >= 0 || i.name.lastIndexOf('.wmv') >= 0 || i.name.lastIndexOf('.ogg') >= 0 || i.name.lastIndexOf('.m4a') >= 0))}">
    <!-- ko foreach: files -->
    <!-- ko let: {
      ext: $data.url.substr($data.url.lastIndexOf('.') + 1),
    } -->
    <div class="mx-5p mb-10p">
      <!-- ko if: !['png', 'jpg', 'jpeg', 'gif', 'mp3', 'wmv', 'ogg', 'm4a'].includes(ext) -->
      <!-- ko if: !$parents[3].printReportMode() -->
      <video-preview
        params="
          poster: $data.preview_url,
          gallery: $parent.gallery.map(i => {
            return {
              src: i.src.url
            }
          }),
          index: $index(),
        "
      ></video-preview>
      <!-- /ko -->
      <!-- ko if: $parents[3].printReportMode() -->
      <a target="_blank" class="d-block print-report-link-video" data-bind="attr: {href: window.location.origin + $data.url}">
        <img alt="" data-bind="attr: {src: window.location.origin + $data.preview_url}">
        <div class="file-loader-preview__bg-overlay">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 49 48" fill="none">
            <path d="M1.5 7C1.5 3.68629 4.18629 1 7.5 1H41.5C44.8137 1 47.5 3.68629 47.5 7V41C47.5 44.3137 44.8137 47 41.5 47H7.5C4.18629 47 1.5 44.3137 1.5 41V7Z" stroke="white" stroke-width="2"/>
            <path d="M32.1939 26.639C33.9354 25.6752 33.9354 23.2659 32.1939 22.3022L21.4184 16.3392C19.6769 15.3755 17.5 16.5801 17.5 18.5076V30.4336C17.5 32.3611 19.6769 33.5657 21.4184 32.602L32.1939 26.639Z" stroke="white" stroke-width="2"/>
          </svg>
        </div>
        <!-- <video-preview
        params="
          poster: window.location.origin + $data.preview_url,
          gallery: $parent.gallery.map(i => {
            return {
              src: i.src.url
            }
          }),
          index: $index(),
        "
      ></video-preview> -->
      </a>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko ifnot: !['png', 'jpg', 'jpeg', 'gif'].includes(ext) -->
      <!-- ko if: !$parents[3].printReportMode() -->
      <media-preview
        params="
          poster: $data.url,
          gallery: $parent.gallery.map(i => {
            return {
              src: i.src.url
            }
          }),
          index: $index(),
        "
      ></media-preview>
      <!-- /ko -->
      <!-- ko if: $parents[3].printReportMode() -->
      <a target="_blank" class="d-block" data-bind="attr: {href: window.location.origin + $data.url}">
        <media-preview
        params="
          poster: $data.url,
          gallery: $parent.gallery.map(i => {
            return {
              src: i.src.url
            }
          }),
          index: $index(),
        "
      ></media-preview>
      </a>
      <!-- /ko -->
      <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>
  <!-- ko if: (files.some(i => i.name.lastIndexOf(".mp3") >= 0 || i.name.lastIndexOf(".wmv") >= 0 || i.name.lastIndexOf(".ogg") >= 0 || i.name.lastIndexOf(".m4a") >= 0)) && files.filter(i => i.name.includes('.mp3') || i.name.includes('.wma') || i.name.includes('.ogg') || i.name.includes('.m4a')).length !== files.length -->
  <div class="poll-report-question__answer-file-type mt-20p mb-10p">Аудио</div>
  <!-- /ko -->
  <!-- ko foreach: files -->
    <!-- ko let: {
      ext: $data.url.substr($data.url.lastIndexOf('.') + 1),
    } -->
  <!-- ko if: ['mp3', 'wmv', 'ogg', 'm4a'].includes(ext) -->
  <audio class="fs-file-audio mb-10p" controls data-bind="attr: { src: $data.url }, css: {'d-none': $parents[3].printReportMode()}"></audio>
  <!-- ko if: !$parents[3].printReportMode() -->
  <div class="d-flex align-items-center mb-15p" data-bind="css: {'ml-15p': !$parents[3].printReportMode(), 'ml-10p': $parents[3].printReportMode()}">
    <svg class="mr-10p fs-file-audio-icon" data-bind="css: {'d-none': !$parents[3].printReportMode()}" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.5 8.33333L9.3356 7.34694C8.85341 7.4273 8.5 7.84449 8.5 8.33333H9.5ZM17.5 7H18.5C18.5 6.70605 18.3707 6.42698 18.1464 6.23698C17.9221 6.04698 17.6256 5.96528 17.3356 6.01361L17.5 7ZM4 2H21V0H4V2ZM23 4V21H25V4H23ZM21 23H4V25H21V23ZM2 21V4H0V21H2ZM4 23C2.89543 23 2 22.1046 2 21H0C0 23.2091 1.79086 25 4 25V23ZM23 21C23 22.1046 22.1046 23 21 23V25C23.2091 25 25 23.2091 25 21H23ZM21 2C22.1046 2 23 2.89543 23 4H25C25 1.79086 23.2091 0 21 0V2ZM4 0C1.79086 0 0 1.79086 0 4H2C2 2.89543 2.89543 2 4 2V0ZM10.5 17V8.33333H8.5V17H10.5ZM9.6644 9.31973L17.6644 7.98639L17.3356 6.01361L9.3356 7.34694L9.6644 9.31973ZM16.5 7V15.6667H18.5V7H16.5ZM8.5 17C8.5 17.5523 8.05228 18 7.5 18V20C9.15685 20 10.5 18.6569 10.5 17H8.5ZM7.5 18C6.94772 18 6.5 17.5523 6.5 17H4.5C4.5 18.6569 5.84315 20 7.5 20V18ZM6.5 17C6.5 16.4477 6.94772 16 7.5 16V14C5.84315 14 4.5 15.3431 4.5 17H6.5ZM7.5 16C8.05228 16 8.5 16.4477 8.5 17H10.5C10.5 15.3431 9.15685 14 7.5 14V16ZM16.5 15.6667C16.5 16.219 16.0523 16.6667 15.5 16.6667V18.6667C17.1569 18.6667 18.5 17.3235 18.5 15.6667H16.5ZM15.5 16.6667C14.9477 16.6667 14.5 16.219 14.5 15.6667H12.5C12.5 17.3235 13.8431 18.6667 15.5 18.6667V16.6667ZM14.5 15.6667C14.5 15.1144 14.9477 14.6667 15.5 14.6667V12.6667C13.8431 12.6667 12.5 14.0098 12.5 15.6667H14.5ZM15.5 14.6667C16.0523 14.6667 16.5 15.1144 16.5 15.6667H18.5C18.5 14.0098 17.1569 12.6667 15.5 12.6667V14.6667Z" fill="#A6B1BC"/>
    </svg>
    <div class="review-question-view__files-audio-name" data-bind="text: $data.name"></div>
  </div>
  <!-- /ko -->
  <!-- ko if: $parents[3].printReportMode() -->
  <a target="_blank" class="d-flex align-items-center mb-15p fs-file-audio-print-mode" data-bind="css: {'ml-15p': !$parents[3].printReportMode()}, attr: {href: window.location.origin + $data.url}">
    <svg class="mr-10p fs-file-audio-icon" data-bind="css: {'d-none': !$parents[3].printReportMode()}" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.5 8.33333L9.3356 7.34694C8.85341 7.4273 8.5 7.84449 8.5 8.33333H9.5ZM17.5 7H18.5C18.5 6.70605 18.3707 6.42698 18.1464 6.23698C17.9221 6.04698 17.6256 5.96528 17.3356 6.01361L17.5 7ZM4 2H21V0H4V2ZM23 4V21H25V4H23ZM21 23H4V25H21V23ZM2 21V4H0V21H2ZM4 23C2.89543 23 2 22.1046 2 21H0C0 23.2091 1.79086 25 4 25V23ZM23 21C23 22.1046 22.1046 23 21 23V25C23.2091 25 25 23.2091 25 21H23ZM21 2C22.1046 2 23 2.89543 23 4H25C25 1.79086 23.2091 0 21 0V2ZM4 0C1.79086 0 0 1.79086 0 4H2C2 2.89543 2.89543 2 4 2V0ZM10.5 17V8.33333H8.5V17H10.5ZM9.6644 9.31973L17.6644 7.98639L17.3356 6.01361L9.3356 7.34694L9.6644 9.31973ZM16.5 7V15.6667H18.5V7H16.5ZM8.5 17C8.5 17.5523 8.05228 18 7.5 18V20C9.15685 20 10.5 18.6569 10.5 17H8.5ZM7.5 18C6.94772 18 6.5 17.5523 6.5 17H4.5C4.5 18.6569 5.84315 20 7.5 20V18ZM6.5 17C6.5 16.4477 6.94772 16 7.5 16V14C5.84315 14 4.5 15.3431 4.5 17H6.5ZM7.5 16C8.05228 16 8.5 16.4477 8.5 17H10.5C10.5 15.3431 9.15685 14 7.5 14V16ZM16.5 15.6667C16.5 16.219 16.0523 16.6667 15.5 16.6667V18.6667C17.1569 18.6667 18.5 17.3235 18.5 15.6667H16.5ZM15.5 16.6667C14.9477 16.6667 14.5 16.219 14.5 15.6667H12.5C12.5 17.3235 13.8431 18.6667 15.5 18.6667V16.6667ZM14.5 15.6667C14.5 15.1144 14.9477 14.6667 15.5 14.6667V12.6667C13.8431 12.6667 12.5 14.0098 12.5 15.6667H14.5ZM15.5 14.6667C16.0523 14.6667 16.5 15.1144 16.5 15.6667H18.5C18.5 14.0098 17.1569 12.6667 15.5 12.6667V14.6667Z" fill="#A6B1BC"/>
    </svg>
    <div class="review-question-view__files-audio-name" data-bind="text: $data.name"></div>
  </a>
  <!-- /ko -->
  <!-- /ko -->
  <!-- /ko -->
  <!-- /ko -->
</div>
<!-- /ko -->
