import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);
    this.question = params.question;
    
    const rows = []
    for (const key in this.question.answer) {
      const element = this.question.answer[key]
      const currentRow = this.question.matrixElements.rows.find(i => i.id == key)
      const colls = []
      for (const k in element) {
        const currentColl = this.question.matrixElements.columns.find(i => i.id == k)
        const answ = element[k]
        const coll = {
          name: currentColl.name,
          answers: answ.includes('-1') ? [{id: -1, name:'Пропуск ответа'}] : currentColl.variants.filter(i => answ.includes(i.id +''))
        }
        colls.push(coll)
      }
      const obj = {
        name: currentRow.name,
        variants: colls
      }
      rows.push(obj)
    }
    this.question.detailRows = rows
  }
}
