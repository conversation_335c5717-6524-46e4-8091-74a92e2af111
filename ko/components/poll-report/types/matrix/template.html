<!-- ko using: question -->

<!-- ko if: hasPoints && !skipped -->
<div class="poll-report-question__mark">
  <correct-mark-controller params="value: answerPoints, max: maxPoints"></correct-mark>
</div>
<!-- /ko -->

<div class="poll-report-question__answer">
  <div>
    <!-- ko if: skipped -->
    <span class="poll-report-question__answer--skipped">
      Пропуск ответа
    </span>    
    <!-- /ko -->
    <!-- ko ifnot: skipped -->
    <!-- ko foreach: { data: matrix.rows, as: 'row' } -->
    <div class="poll-report-question__answer-text">
      <span data-bind="text: row"></span>
      <!-- ko text: ` — ` --><!-- /ko -->
      <!-- ko if: $parent.correctVariants[row].answer == 'null' -->
      <span class="poll-report-question__answer__skip-text">
        Пропуск ответа
      </span>
      <!-- /ko -->
      <!-- ko ifnot: $parent.correctVariants[row].answer == 'null' -->
      <span data-bind="text: $parent.correctVariants[row].answer"></span>
      <!-- /ko -->
    </div>
    <!-- ko if: $parent.clarifyingQuestion && $parent.clarifyingQuestion.length -->
    <div class="poll-report-question__clarifying-question">
      
      <!-- ko if: $parent.answerData.extra && $parent.answerData.extra[row] && $parent.answerData.extra[row].answer -->
      <div class="poll-report-question__answer-comment-header" data-bind="text: $parent.clarifyingQuestion"></div>
      <div data-bind="text: $parent.answerData.extra[row].answer"></div>
      <!-- /ko -->
      <!-- ko if: $parent.answerData.extra && $parent.answerData.extra[row] && !$parent.answerData.extra[row].answer -->
      <!-- ko foreach: Object.keys($parent.answerData.extra[row]) -->
      <!-- ko let: {
        extraVariantsList: $parents[1].clarifyingQuestionVariants,
        extraVariantAnswer: $parents[1].answerData.extra[row][$data],
      } -->
      <!-- ko if: $data !== 'self_variant' &&
        extraVariantsList && extraVariantsList.length &&
        extraVariantAnswer && extraVariantAnswer.length
      -->
      <!-- ko let: {
        extraVariant: extraVariantsList.find(el => el.id == extraVariantAnswer),
      } -->
      <!-- ko if: extraVariant && extraVariant.text && extraVariant.text.length -->
      <div class="poll-report-question__answer-comment-header" data-bind="text: $parent.clarifyingQuestion"></div>
      <div data-bind="text: extraVariant.text"></div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko if: $data === 'self_variant' -->
      <div class="poll-report-question__answer-comment-header" data-bind="text: $parent.clarifyingQuestion"></div>
      <div class="poll-report-question__answer-comment-header poll-report-question__answer-comment-header--self-variant">
        Свой вариант
      </div>
      <div data-bind="text: extraVariantAnswer"></div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- /ko -->
      <!-- /ko -->
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <!-- /ko -->
  </div>
</div>

<!-- ko if: comment && comment.length -->
<div class="poll-report-question__answer-comment">
  <div class="poll-report-question__answer-comment-header">
    Комментарий
  </div>
  <div class="poll-report-question__answer-comment-text" data-bind="text: comment"></div>
</div>
<!-- /ko -->

<!-- ko if: hasPoints && !skipped -->
<!-- ko ifnot: answerPoints == maxPoints -->
<div class="poll-report-question__correct-answer">
  <div class="poll-report-question__correct-answer-header">Правильный ответ:</div>
  <div
    class="poll-report-question__correct-answer-text"
    data-bind="text: correctVariantsString"
  ></div>
</div>
<!-- /ko -->
<!-- /ko -->

<!-- /ko -->
