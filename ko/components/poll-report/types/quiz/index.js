import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('poll-report-question-quiz', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('poll-report-question');
      element.classList.add('poll-report-question--quiz');

      return new ViewModel(params, element);
    },
  },
  template: html,
});
