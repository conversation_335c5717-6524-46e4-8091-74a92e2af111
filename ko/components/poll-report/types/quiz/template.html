<!-- ko using: question -->
<div class="poll-report-question__answer">
  <div>
    <!-- ko foreach: fields -->
    <!-- ko if: answer.isName -->
    <!-- ko let: {
      text: `${value.name}${value.name.length ? ' ' : ''}${value.surname}${value.surname.length ? ' ' : ''}${value.patronymic}`,
    } -->
    <!-- ko if: text.length -->
    <div class="quiz-field">
      <div class="f-fs-1 f-color-service" data-bind="text: label"></div>
      <div data-bind="text: text"></div>
    </div>
    <!-- /ko -->
    <!-- /ko -->
    <!-- /ko -->
    <!-- ko if: !answer.isName && value && value.length -->
    <div class="quiz-field">
      <div class="f-fs-1 f-color-service" data-bind="text: label"></div>
      <div data-bind="text: value"></div>
    </div>
    <!-- /ko -->
    <!-- /ko -->
  </div>
</div>
<!-- /ko -->
