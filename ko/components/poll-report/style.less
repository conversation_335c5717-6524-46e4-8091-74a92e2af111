@import 'Style/colors';
@import 'Style/breakpoints';

.poll-report {
  &__divider {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  &__result-title {
    color: #2E2F31;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: <PERSON>o;
    font-size: 19px;
    font-style: normal;
    font-weight: 700;
    line-height: 110%; /* 20.9px */
  }

  &__result-description {
    color: #73808D;
    font-feature-settings: 'clig' off, 'liga' off;
    /* text */
    font-family: Roboto;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 120%; /* 18px */
  }

  .poll-report__skip-text {
    color: #F96261;
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    margin-left: 40px;
  }

  .poll-report-question {
    &__header {
      display: flex;
      position: relative;
      padding-right: 60px;
      margin-bottom: 15px;

      font-size: 16px;
      font-weight: 700;
      &_skip {
        .poll-report-question__index, .poll-report-question__title {
          color: #73808D;
        }
      }
    }
    &__index {
      width: 25px;
      margin-right: 15px;
      flex-shrink: 0;
      text-align: right;
    }
    &__title {
      margin: 0;
      padding: 0;
      flex-grow: 1;
    }
    &__points {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 12px;
      font-weight: 400;

      b {
        font-size: 14px;
        font-weight: 700;
      }
    }

    &__content {
      padding-left: 40px;
      padding-right: 60px;
      position: relative;
      font-size: 14px;
    }

    &__mark {
      position: absolute;
      left: 0px;
      top: 0;
      width: 25px;
      display: flex;
      justify-content: flex-end;

      .svg-icon {
        width: 10px;
        height: 10px;
        margin-top: 2px;
      }
    }

    &__correct-answer {
      font-size: 12px;
      margin-top: 12px;
    }

    &--skipped {
      color: @f-color-service;
      
      .poll-report-question__answer {
        margin-top: -5px;
        font-size: 12px;
        font-style: italic;
        font-weight: 400;
        line-height: 14px;    
      }
    }

    .poll-report-question__self-variant-header {
      color: #73808d;
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      margin-bottom: 5px;
    }

    .poll-report-question__answer-item {
      margin-bottom: 10px;
      font-size: 14px;
      font-weight: 400;
      line-height: 17px;
      &.with-file {
        width: 85%;
        word-break: break-all;
        font-size: 12px;
      }
    }

    .poll-report-question__answer-comment {
      margin-bottom: 15px;
    }

    .poll-report-question__answer-comment-text {
      word-break: break-all;
    }

    .poll-report-question__answer-comment-header {
      color: #73808d;
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      margin-bottom: 5px;        
    }

    .poll-report-question__answer__skip-text {
      color: #F96261;
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
    }

    .poll-report-question__clarifying-question {
      margin-bottom: 8px;
      font-size: 12px;
      font-weight: 400;
      line-height: 17px;
      padding-left: 15px;

      .poll-report-question__answer-comment-header {
        margin-bottom: 6px;

        &.poll-report-question__answer-comment-header--self-variant {
          margin-bottom: 2px;
        }
      }

      > .poll-report-question__answer-comment-header {
        margin-top: 5px;
      }
    }

    .poll-report-question__answer-text {
      margin-bottom: 10px;
    }

    .poll-report-question__answer--skipped {
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      color: #F96261;
      font-style: normal;
    }

    .only-mobile({
      &__header {
        padding-right: 0;
        flex-wrap: wrap;
      }
      &__index {
        margin-right: 10px;
        position: absolute;
      }
      &__title {
        padding-left: 35px;
      }
      &__points {
        position: static;
        width: 100%;
        padding-left: 35px;
        margin-top: 4px;
      }
      &__content {
        padding-left: 35px;
        padding-right: 0;
      }
    })
  }
}