<div class="poll-report__container">
  <!-- ko if: loading -->
  <fc-spinner class="f-color-primary"></fc-spinner>
  <!-- /ko -->
  <!-- ko ifnot: loading -->
  <div class="poll-report-question poll-report-question__question-header">
    <header class="poll-report-question__header">
      <h1
        class="poll-report-question__title"
        data-bind="text: POLL.name"
      ></h1>
    </header>
    <hr class="poll-report__divider">
  </div>
  <!-- ko if: hasClientData -->
  <div class="poll-report-question poll-report-question__client">
    <p>
      <b data-bind="text: window.CLIENT_NAME"></b>
    </p>
    <!-- ko if: hasClientContacts -->
    <p>
      <b
        data-bind="
          text: `${window.CLIENT_PHONE}${hasClientPhone && hasClientEmail ? ',' : ''}`,
        "
      ></b>
      <!-- ko text: window.CLIENT_EMAIL --><!-- /ko -->
    </p>
    <!-- /ko -->
    <hr class="poll-report__divider">
  </div>
  <!-- /ko -->
  <!-- ko if: points -->
  <div>
    <div class="d-flex align-items-center">
      <div class="poll-report__result-title">Результаты тестирования</div>
      <div class="poll-report__result ml-auto d-flex align-items-center">
        <div class="poll-report__result-description">Набрано баллов:&nbsp;</div>
        <div class="font-weight-bold" data-bind="text: answerPoints"></div>
        <div>&nbsp;из&nbsp;</div>
        <div class="font-weight-bold" data-bind="text: maxPoints"></div>
        <div>&nbsp;</div>
        <div data-bind="text: '(' + percentPoints() + '%)'"></div>
      </div>

    </div>
    <hr class="poll-report__divider">
  </div>
  <!-- /ko -->
  <!-- ko foreach: filteredQuestions -->
  <!-- ko ifnot: !hasAnswer || $data.skipped -->
  <article class="poll-report-question">
    <header class="poll-report-question__header">
      <div
        class="poll-report-question__index"
        data-bind="text: $index() + 1 + '.'"
      ></div>
      <h2
        class="poll-report-question__title"
        data-bind="text: description || name"
      ></h2>
      <!-- ko if: hasAnswer && withPoints && hasPoints && (!$data || !$data.without_points) -->
      <div class="poll-report-question__points">
        <b data-bind="text: answerPoints"></b> / <span data-bind="text: maxPoints"></span>
      </div>
      <!-- /ko -->
    </header>
    <div
      data-bind="
        class: `poll-report-question__content poll-report-question__content--${typeName}`,
      "
    >
      <!-- ko ifnot: hasAnswer || $data.skipped -->
      <div class="poll-report-question__answer">
        Вопрос пропущен
      </div>
      <!-- /ko -->
      <!-- ko if: hasAnswer -->
      <div
        data-bind="
          component: {
            name: 'poll-report-question-' + typeName,
            params: {
              question: $data,
              printReportMode: $parent.printReportMode
            },
          },
        "
      ></div>
      <!-- /ko -->
    </div>
  </article>
  <hr
    class="poll-report__divider"
    data-bind="
      hidden: $index() == $parent.filteredQuestions().length - 1,
    "
  />
  <!-- /ko -->
  <!-- ko if: $data.skipped -->
  <header class="poll-report-question__header poll-report-question__header_skip">
    <div
      class="poll-report-question__index"
      data-bind="text: $index() + 1 + '.'"
    ></div>
    <h2
      class="poll-report-question__title"
      data-bind="text: description || name"
    ></h2>
    <!-- ko if: hasAnswer && withPoints && hasPoints && (!$data || !$data.without_points) -->
    <div class="poll-report-question__points">
      <b data-bind="text: answerPoints"></b> / <span data-bind="text: maxPoints"></span>
    </div>
    <!-- /ko -->
  </header>
  <div class="poll-report__skip-text">Пропуск ответа</div>
  <hr
    class="poll-report__divider"
    data-bind="
      hidden: $index() == $parent.filteredQuestions().length - 1,
    "
  />
  <!-- /ko -->
  <!-- /ko -->
  <div class="poll-report-question poll-report-question__footer">
    <a class="logo" href="/">
      <svg width="94" height="21" viewBox="0 0 94 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.4078 4.00251C11.3437 4.26867 11.1055 4.45623 10.8318 4.45623H4.44205V8.3984H10.3772L9.42721 12.2056H4.44205V19.6309H0V0.514063H11.4962C11.8796 0.514063 12.1621 0.872683 12.0722 1.24543L11.4078 4.00251ZM33.3119 9.99042C33.3119 16.0387 29.0012 20.1429 23.1661 20.1429C17.3573 20.1429 13.0466 16.0387 13.0466 9.99042C13.0466 3.86116 17.3573 0 23.1661 0C29.0012 0 33.3119 3.86116 33.3119 9.99042ZM28.5019 9.99065C28.5019 6.6425 26.3203 4.1584 23.1662 4.1584C20.0121 4.1584 17.8568 6.6425 17.8568 9.99065C17.8568 13.4468 20.0384 15.9309 23.1662 15.9309C26.2941 15.9309 28.5019 13.4468 28.5019 9.99065ZM57.2473 19.0373C57.2473 19.3645 56.982 19.6298 56.6547 19.6298H45.9187C40.0047 19.6298 35.9832 16.0927 35.9832 9.82842C35.9832 4.02317 40.3201 0 46.0764 0C51.859 0 56.0907 3.99617 56.0907 9.74742C56.0907 12.6635 54.6714 14.9046 53.3309 15.9577V16.0387L56.6425 15.9702C56.9744 15.9633 57.2473 16.2306 57.2473 16.5626V19.0373ZM51.3335 9.88318C51.3335 6.56204 49.1782 4.18594 46.0766 4.18594C43.0802 4.18594 40.8198 6.56204 40.8198 9.88318C40.8198 13.3123 42.9751 15.6884 46.0504 15.6884C49.0993 15.6884 51.3335 13.3123 51.3335 9.88318ZM75.9255 12.4216C75.9255 17.0118 72.7714 20.1439 67.9088 20.1439C63.0199 20.1439 59.9709 17.0118 59.9709 12.4216V1.1066C59.9709 0.779352 60.2362 0.514063 60.5634 0.514063H63.8467C64.1739 0.514063 64.4392 0.779352 64.4392 1.1066V12.0436C64.4392 14.0956 65.4118 15.9857 67.9351 15.9857C70.4846 15.9857 71.4309 14.0956 71.4309 12.0436V1.1066C71.4309 0.779352 71.6962 0.514063 72.0234 0.514063H75.3329C75.6602 0.514063 75.9255 0.779352 75.9255 1.1066V12.4216ZM93.9998 19.0383C93.9998 19.3656 93.7345 19.6309 93.4073 19.6309H79.9257C79.5984 19.6309 79.3332 19.3656 79.3332 19.0383V15.8406C79.3332 15.7073 79.3781 15.5778 79.4608 15.4732L87.4321 5.38922C87.7392 5.00071 87.4625 4.42923 86.9673 4.42923H80.136C79.8087 4.42923 79.5434 4.16394 79.5434 3.83669V1.1066C79.5434 0.779351 79.8087 0.514063 80.136 0.514063H93.2233C93.5505 0.514063 93.8158 0.779352 93.8158 1.1066V4.0066C93.8158 4.1404 93.7705 4.27025 93.6873 4.37504L85.5107 14.6737C85.2024 15.062 85.4789 15.6347 85.9748 15.6347H93.4073C93.7345 15.6347 93.9998 15.9 93.9998 16.2272V19.0383Z" fill="currentColor"/>
      </svg>
    </a>
    <div class="copyright">
      <div data-bind="text: `&copy; FOQUZ ${year}`"></div>
    </div>
  </div>
  <!-- /ko -->
</div>
