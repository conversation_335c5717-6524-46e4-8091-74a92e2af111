import { FoquzComponent } from 'Models/foquz-component';
import { ApiUrl } from 'Utils/url/api-url';
import { ReviewQuestionFactory } from '../../models/review/question';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.root = params.root;
    this.answerId = params.answerId;
    this.blocked = params.blocked;
    this.printReportMode = params.printReportMode;
    this.year = moment().year();

    this.questions = ko.observableArray([]);

    this.filteredQuestions = ko.pureComputed(() => {
      let blocked = ko.toJS(this.blocked);

      return this.questions().filter((q) => {
        if (q.isDeleted) return false;
        return !blocked.find((qId) => q.id == qId);
      });
    });

    this.hasClientPhone = window.CLIENT_PHONE && window.CLIENT_PHONE.length;
    this.hasClientEmail = window.CLIENT_EMAIL && window.CLIENT_EMAIL.length;
    this.hasClientContacts = this.hasClientEmail || this.hasClientPhone;
    this.hasClientData = this.hasClientContacts || (window.CLIENT_NAME && window.CLIENT_NAME.length);

    this.loading = ko.observable(false);
    this.points = ko.observable(null);
    this.maxPoints = ko.observable(null);
    this.answerPoints = ko.observable(null);
    this.percentPoints = ko.observable(null);
    this.load();
  }

  load() {
    this.loading(true);
    $.ajax({
      method: 'GET',
      url: ApiUrl('answers/get-results'),
      data: {
        auth_key: this.root.authKey,
      },
      success: (response) => {
        let questions = response.questions.map((q) => ReviewQuestionFactory(q, response.questions));
        console.log('ViewModel FoquzComponent questions', questions)
        this.questions(questions);
        this.points(response.points);
        this.maxPoints(response.points.points_max);
        this.answerPoints(response.points.answer_points);
        this.percentPoints(response.points.percent);
        this.loading(false);
        if (this.printReportMode()) {
          setTimeout(() => {
            window.print();
          }, 1000);
        }
      },
      error: (response) => {
        console.error(response.responseJSON);
        this.loading(false);
      }
    });
  }
}
