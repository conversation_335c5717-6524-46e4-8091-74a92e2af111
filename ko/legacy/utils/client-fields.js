/**
 * Загрузчик клиентских полей
 */

const apiUrl = '/foquz/foquz-question/contact-fields';

let loaded = ko.observable(false);
let loading = ko.observable(false);
let system = ko.observableArray([]);
let additional = ko.observableArray([]);
let changed = ko.observable().extend({ notify: 'always' });

function load() {
  return new Promise((res) => {
    loading(true);
    $.ajax({
      url: apiUrl,
      type: 'GET',
    }).done((data) => {
      // while (system().length) {
      //   system.pop();
      // }
      // data.system.forEach((f) => system.push(f));

      // while (additional().length) {
      //   additional.pop();
      // }
      // data.additional.forEach((f) => additional.push(f));

      system(data.system);
      additional(data.additional);

      loaded(true);
      loading(false);
      res(data);
      changed(true);
    });
  });
}

export function getClientFields(loadImmediately) {
  if (loadImmediately === undefined || loadImmediately) {
    if (!loaded() && !loading()) {
      load();
    }
  }

  return {
    loading,
    loaded,
    system,
    additional,
    load,
    changed,
  };
}
