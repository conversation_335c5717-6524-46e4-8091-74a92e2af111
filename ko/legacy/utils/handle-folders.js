export function handleFolders(response) {
  const items = response.items;
  const ids = {};
  items.forEach(item => ids[item.id] = {
      name: item.name,
      items: [],
      processed: false
  });

  items.forEach(item => {
      // TODO несуществующий id родительской папки
      if (!ids[item.folder_id]) {
        ids[item.id].noParent = true;
      } else {
        ids[item.folder_id].items.push(item.id);
      }
  });

  const trees = {

  };

  function createFolderTree(folderId, children) {
      if (trees[folderId]) {
          if (children) trees[folderId].children = true;
          return trees[folderId];
      }

      const folderData = ids[folderId];

      if (!folderData.items.length) {
          const tree = {
              id: folderId,
              name: folderData.name,
              items: [],
              children: children
          };
          trees[folderId] = tree;
          return tree;
      }

      const items = ids[folderId].items.map(childFolderId => createFolderTree(childFolderId, folderData.name));
      const tree = {
          id: folderId,
          name: folderData.name,
          children: children,
          items: items
      };
      trees[folderId] = tree;
      return tree;
  }

  const result = [];
  Object.keys(ids).forEach(folderId => {
      result.push(createFolderTree(folderId));
  });

  const tree = result.filter(f => !f.children).map(f => {
      delete f.children;
      return f;
  });

  function setParentName(folder, parentName) {
      parentName = parentName ? parentName + '/' : '';
      folder.parentName = parentName;

      folder.items.forEach(childFolder => {
          setParentName(childFolder, parentName + folder.name);
      })
  }

  tree.forEach(folder => setParentName(folder));

  return tree;
}
