export class Observer {
  constructor(callback, options) {
    this.target = null;
    this.callback = callback;
    this.options = options || {};

    this._instance = this._init();
  }

  _init() {
    return new IntersectionObserver(this.callback, this.options);
  }

  setTarget(target) {
    this.target = target;
  }

  activate() {
    if (!this.target) return;
    this._instance.observe(this.target);
  }

  deactivate() {
    if (!this.target) return;
    this._instance.unobserve(this.target);
    this.target = null;
  }
}
