export function folderOptionTemplate(state) {
  if (!state.id) {
    return state.text;
  }
  var level = parseInt($(state.element).data('level'));
  var parent = $(state.element).data('parent');
  return $(
    '<span class="folder-option">\n          <i class="folder-option__icon icon-folder icon-folder--light" style="margin-left: ' +
      level * 14 +
      'px"></i>\n          <span class="folder-option__name">' +
      state.text +
      '</span>\n        </span>',
  );
}

export function folderSelectionTemplate(state) {
  if (!state.id) {
      return state.text;
  }
  var level = parseInt($(state.element).data('level'));
  var parent = $(state.element).data('parent') || '';
  return $('<span class="folder-option">\n          <span class="folder-option__parent">' + parent + '</span>\n          <span class="folder-option__name">' + state.text + '</span>\n        </span>');
}
