import { getTypeName } from 'Data/question-types';

export function questionTypeOptionTemplate(state) {
  if (!state.id) {
    return state.text;
  }

  let text = $(`<span class="text-ellipsis">${state.text}</span>`);

  let typeName = '';
  if (state.id === 'start') {
    typeName = 'start';
  } else if (state.id === 'end') {
    typeName = 'end';
  } else {
    typeName = getTypeName(state.id);
  }


  let icon = $(
    `<span><svg><use href="#foquz-icon-question-${typeName}"></use></svg></span>`
  ).addClass([
    'f-icon',
    'f-icon-question-type',
    'f-icon-question-type--' + typeName
  ]);

  return $('<span>')
    .addClass([
      'question-type-select__option',
      'question-type-select__option--' + typeName
    ])
    .append(icon)
    .append(text);
}
