<script type="text/html"
        id="set-promocode-modal-content-template">
  <div data-bind="component: { name: 'set-promocode-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function () { close(true); } } }"
       role="document"></div>
</script>

<template id="set-promocode-modal-dialog-template">
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title">Добавить промокод</h2>

      <button type="button"
              class="close"
              aria-label="Close"
              data-bind="click: function() { cancel(); }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body">
      <div class="form-group">
        <div class="hat-radio-group form-control">
          <div class="hat-radio-group__radio">
            <input class="hat-radio-group__radio-input"
                   type="radio"
                   name="promo-type"
                   id="common-promo"
                   data-bind="checked: isPool, value: 0"
                   checked />
            <label class="hat-radio-group__radio-label"
                   for="common-promo">
              <span class="hat-radio-group__radio-indicator"></span>
              Один для всех
            </label>
          </div>

          <div class="hat-radio-group__radio">
            <input class="hat-radio-group__radio-input"
                   type="radio"
                   name="promo-type"
                   id="pool-promo"
                   data-bind="checked: isPool, value: 1" />
            <label class="hat-radio-group__radio-label"
                   for="pool-promo">
              <span class="hat-radio-group__radio-indicator"></span>
              Пул купонов
            </label>
          </div>
        </div>
      </div>
      <!-- ko ifnot: isPool -->
      <div class="form-group">
        <input class="form-control"
               data-bind="textInput: name, css: {
                  'is-invalid': formControlErrorStateMatcher(name),
              }">

        <!-- ko template: {
                  foreach: formControlErrorStateMatcher(name),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
        <div class="form-error"
             data-bind="text: $parent.name.error()"></div>
        <!-- /ko -->
      </div>
      <!-- /ko -->
      <!-- ko if: isPool -->
      <div class="form-group">
        <!-- ko if: pools.loaded -->
        <!-- ko if: pools.data().length -->
        <div class="select2-wrapper"
             data-bind="css: {
                  'is-invalid': formControlErrorStateMatcher(poolId),
              }">

          <select data-bind="value: poolId,
                  valueAllowUnset: true,
                  lazySelect2: {
                      containerCssClass: 'form-control',
                      wrapperCssClass: 'select2-container--form-control',
                      allowClear: true,
                      minimumResultsForSearch: 0,
                  }"
                  data-placeholder="Выберите пул купонов">
                  <!-- ko foreach: pools.data -->
                  <option data-bind="text: title, value: id"></option>
                  <!-- /ko -->
          </select>

          <!-- ko template: {
                  foreach: formControlErrorStateMatcher(poolId),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <div class="form-error"
               data-bind="text: $parent.poolId.error()"></div>
          <!-- /ko -->
        </div>
        <!-- /ko -->
        <!-- ko ifnot: pools.data().length -->
        <p>
        Нет данных для отображения.
        Для создания <b class="bold">пула купонов</b> <a href="/foquz/foquz-contact?tab=pools" target="_blank">перейдите по ссылке</a>.
        </p>
        <!-- /ko -->
        <!-- /ko -->
      </div>
      <!-- /ko -->
    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button"
                class="btn btn-link"
                data-bind="click: function() { cancel(); }">
          Отменить
        </button>

        <button type="submit"
                class="btn btn-success"
                data-bind="click: function() { submit(); }, disable: isPool() && !pools.data().length">
          Добавить
        </button>
      </div>
    </div>
  </div>
</template>
