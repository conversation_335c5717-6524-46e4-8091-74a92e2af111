const poolsDirectory = new Directory("discount-pool?all=1");
poolsDirectory.load();

export function Promocode(data) {
  this.isPool = ko.observable(data ? data.isPool : false);
  this.name = ko.observable(data ? data.name : "");
  this.poolId = ko.observable(data ? data.poolId : "");
  this.poolName = ko.observable(data ? data.poolName : "");

  if (this.isPool() && !this.poolName()) {
    poolsDirectory.onLoad(() => {
      if (!this.poolName()) {
        let pool = poolsDirectory.getById(this.poolId());
        if (pool) this.poolName(pool.title);
      }
    });
  }

  this.label = ko.pureComputed(() => {
    if (this.isPool()) {
      if (this.poolId()) return "Промокод: " + this.poolName();
      return "Промокод";
    } else {
      if (this.name()) return "Промокод: " + this.name();
      return "Промокод";
    }
  });
  this.isNew = ko.observable(!!data);
  this.name.subscribe((_) => this.isNew(false));
  this.poolId.subscribe((_) => this.isNew(false));
}

ko.components.register("set-promocode-modal-dialog", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        "modal-dialog",
        "modal-dialog-centered",
        "modal-dialog-md",
      ]);
      $element.parents(".modal").removeAttr("tabindex");

      const ViewModel = function () {
        this.submitted = ko.observable(false);

        this.promocode = params.data.promocode;

        if (params.data.pools) {
          this.pools = Directory.create(params.data.pools);
        } else {
          this.pools = poolsDirectory;
        }

        this.isPool = ko.observable(this.promocode.isPool() ? 1 : 0);
        this.name = ko.observable(this.promocode.name()).extend({
          required: {
            message: "Обязательное поле",
            onlyIf: () => !this.isPool(),
          },
        });
        this.poolId = ko.observable(this.promocode.poolId()).extend({
          required: {
            message: "Обязательное поле",
            onlyIf: () => this.isPool(),
          },
        });

        this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
          this.submitted
        );

        this.cancel = function () {
          if ("cancel" in params) {
            params.cancel();
          }
        };

        this.submit = function () {
          this.submitted(true);
          if (!this.name.isValid() || !this.poolId.isValid()) return;
          if ("submit" in params) {
            if (this.isPool()) {
              this.promocode.isPool(true);
              this.promocode.poolId(this.poolId());
              const pool = this.pools.getById(this.poolId());
              this.promocode.poolName(pool.title);
              this.promocode.name("");
            } else {
              this.promocode.isPool(false);
              this.promocode.name(this.name());
              this.promocode.poolName("");
            }

            params.submit();
          }
        };
      };

      const viewModel = new ViewModel();
      return viewModel;
    },
  },
  template: {
    element: "set-promocode-modal-dialog-template",
  },
});
