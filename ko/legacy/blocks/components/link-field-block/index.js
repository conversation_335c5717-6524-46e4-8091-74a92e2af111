let nextId = 0;
//const apiUrl = '/foquz/api/contact-fields?access-token='+ACCESS_TOKEN;
const apiUrl = '/foquz/foquz-question/contact-fields';

const clientFields = (function () {
  const loaded = ko.observable(false);
  const fields = {
    system: ko.observableArray([]),
    additional: ko.observableArray([]),
  };
  const defaultField = ko.observable('');

  function loadFields() {
    $.ajax({
      url: apiUrl,
      type: 'GET',
    }).done((data) => {
      fields.system = data.system;
      fields.additional = data.additional;
      defaultField(data.system[0].id);
      loaded(true);
    });
  }

  return {
    loaded,
    defaultField,
    get fields() {
      if (!loaded()) loadFields();
      return fields;
    },
  };
})();

ko.components.register('link-field-block', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass(['link-field-block']);

      function ViewModel(needLink, fieldName, rewrite) {
        this.id = nextId++;

        this.fields = clientFields.fields;
        this.loaded = clientFields.loaded;

        this.optionTemplate = (state) => {
          return $('<span>').text(state.text);
        };

        this.needLink = ko.observable(needLink());
        this.fieldName = ko.observable(fieldName());
        this.rewrite = ko.observable(rewrite());

        this.needLink.subscribe((value) => {
          needLink(value);

          if (!value) this.rewrite(false);
        });
        this.rewrite.subscribe((value) => {
          rewrite(value);
        });
        this.fieldName.subscribe((value) => {
          if (!value) fieldName(clientFields.defaultField());
          else fieldName(value);
        });

        this.initSelect = (el) => {
          if (!this.fieldName()) this.fieldName(clientFields.defaultField());
          el.value = this.fieldName();
          ko.applyBindingsToNode(el, {
            value: this.fieldName,
            select2: {
              templateSelection: this.optionTemplate,
              templateResult: this.optionTemplate,
              containerCssClass: 'form-control',
              wrapperCssClass:
                'select2-container--form-control link-field-block__select',
              dropdownCssClass: 'dense-form-group__dropdown',

              minimumResultsForSearch: 0,
            },
          });
        };
      }

      const needLink = params.needLink;
      const fieldName = params.fieldName;
      const rewrite = params.rewrite;

      const model = new ViewModel(needLink, fieldName, rewrite);

      return model;
    },
  },
  template: `
          <div class="link-field-block__wrapper">
        <div class="form-group">
            <div class="form-check link-field-block__need-link">
                <input type="checkbox" class="form-check-input" data-bind="checked: needLink,
                    attr: {id: 'link-field-block-need-link-' + id}">
                <label class="form-check-label" data-bind="attr: {'for': 'link-field-block-need-link-' + id}">
                    Связать с полем из раздела «Контакты»
                    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="В разделе «Контакты» у каждого контакта есть системные и пользовательские поля. С помощью этой опции можно автоматически заполнять эти поля, когда респондент даст ответ."></button>
                </label>
            </div>
        </div>

        <!-- ko template: {
          foreach: templateIf(loaded() && needLink(), $data),
          afterAdd: fadeAfterAddFactory(200, 200),
          beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
        <div class="form-group link-field-block__field-name">
            <label class="form-label">Связанное поле</label>

            <select data-bind="descendantsComplete: initSelect">
                <optgroup label="Системные">
                    <!-- ko foreach: {data: fields.system, as: 'field'} -->
                    <option data-bind="text: field.text, value: field.id"></option>
                    <!-- /ko -->
                </optgroup>
                <optgroup label="Пользовательские">
                    <!-- ko foreach: {data: fields.additional, as: 'field'} -->
                    <option data-bind="text: field.text, value: field.id"></option>
                    <!-- /ko -->
                </optgroup>
            </select>

        </div>
        <!-- /ko -->

        <div class="form-group">
            <div class="form-check link-field-block__rewrite">
                <input type="checkbox" class="form-check-input" data-bind="checked: rewrite, enable: needLink, attr: {id: 'link-field-block-rewrite-' + id}">
                <label class="form-check-label" data-bind="attr: {'for': 'link-field-block-rewrite-' + id}">Перезаписывать существующее значение
                </label>
            </div>
        </div>
    </div>
  `,
});
