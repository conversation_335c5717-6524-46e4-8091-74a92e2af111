ko.components.register('review-rating-modal-dialog', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        'modal-dialog',
        'modal-dialog-centered',
        'review-rating-modal-dialog',
        'review-rating-modal-dialog--initializing',
      ]);

      const viewModel = new (function () {
        this.answers = params.data.answers;

        this.cancel = function () {
          if ('cancel' in params) {
            params.cancel();
          }
        };

        this.submit = function () {
          if ('submit' in params) {
            params.submit();
          }
        };
      })();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass('review-rating-modal-dialog--initializing');
        viewModel.initializing(false);
      };

      return viewModel;
    },
  },
  template: `<!-- ko template: { afterRender: onInit } -->
          <div class="modal-content">
              <div class="modal-header">
                  <h2 class="modal-title">Комментарии к оценкам</h2>

                  <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
                      <span aria-hidden="true">&times;</span>
                  </button>
              </div>

              <div class="modal-body">
                  <div class="review-rating__table-wrapper" data-bind="scrollbar">
                      <table class="review-rating__table">
                          <tbody>
                              <!-- ko foreach: answers -->
                                  <tr class="review-rating__table-row">
                                      <td class="review-rating__point-cell">
                                          <div class="point"
                                              data-bind="text: rating, css: 'point--' + rating">
                                          </div>
                                      </td>
                                      <td data-bind="text: question.name"></td>
                                      <td>
                                          <!-- ko if: comment !== null -->
                                              <!-- ko text: comment --><!-- /ko -->
                                          <!-- /ko -->
                                      </td>
                                  </tr>
                              <!-- /ko -->
                          </tbody>
                      </table>
                  </div>
              </div>

              <div class="modal-footer">
                  <div class="modal-actions">
                      <button type="button" class="btn btn-link"
                              data-bind="click: function() { cancel(); }">
                          Закрыть
                      </button>
                  </div>
              </div>
          </div>
          <!-- /ko -->`,
});
