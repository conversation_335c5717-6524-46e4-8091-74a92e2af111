ko.bindingHandlers.tagInputPopover = {
  init: function (
    element,
    valueAccessor,
    allBindings,
    viewMode,
    bindingContext
  ) {
    const $element = $(element);

    $element.popover({
      html: true,
      sanitize: false,
      placement: "bottom",
      boundary: document,
      template: `
                    <div class="popover clients__tag-input-dropdown" role="tooltip">
                        <div class="popover-body clients__tag-input-dropdown-menu"></div>
                    </div>
                `,
      content: `
                <div class="">
                    <div class="clients__tag-input-dropdown-menu-control-wrapper">
                        <input class="form-control clients__tag-input-dropdown-menu-control"
                               data-bind="textInput: dropdownMenuSearchTerm">

                        <!-- ko if: dropdownMenuSearchTerm().length !== 0 -->
                        <button class="btn clients__tag-input-dropdown-menu-control-create-tag-button"
                                data-bind="click: function() {
                                  createTag();
                                 },
                                attr: {
                                  disabled: dropdownMenuControlCreateTagButtonDisabled() && dropdownMenuControlCreateTagError(),
                                  title: dropdownMenuControlCreateTagButtonTitle
                                }"></button>
                        <!-- /ko -->
                    </div>

                    <!-- ko if: dropdownMenuControlCreateTagError -->
                        <span class="form-error" data-bind="text: dropdownMenuControlCreateTagButtonTitle"></span>
                    <!-- /ko -->

                    <!-- ko if: dropdownMenuFilteredList().length > 0 -->
                    <div class="clients__tag-input-dropdown-menu-list" data-bind="fScrollbar">
                        <!-- ko template: {
                            foreach: dropdownMenuFilteredList,
                            afterRender: updatePopover
                        } -->
                        <a class="dropdown-item"
                           data-bind="click: function() { closePopover(); $component.addTag($data); }, text: $data.name"></a>
                        <!-- /ko -->
                    </div>
                    <!-- /ko -->

                    <!-- ko if: dropdownMenuFilteredList().length === 0 -->
                    <span class="clients__tag-input-dropdown-menu-message">Совпадений не найдено</span>
                    <!-- /ko -->
                </div>
                `,
    });

    const bodyClickHandler = function (e) {
      if (e.target === element) return;
      if ($(e.target).parents(".clients__tag-input-dropdown-menu").length)
        return;
      $(element).popover("hide");
    };

    $element.on("inserted.bs.popover", () => {
      const content = $($element.data("bs.popover").tip)
        .find(".popover-body")
        .children()[0];
      const context = bindingContext.extend({
        closePopover: function () {
          $(element).popover("hide");
        },
      });
      ko.applyBindings(context, content);
    });
    $element.on("shown.bs.popover", () => {
      $("body").on("click", bodyClickHandler);
    });

    $element.on("hidden.bs.popover", () => {
      $("body").off("click", bodyClickHandler);
    });

    $("body").on("show.bs.popover", (e) => {
      if (e.target !== element) {
        $(element).popover("hide");
      }
    });
  },
};

ko.components.register("clients-tag-input", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);

      $element.addClass(["clients__tag-input"]);

      const viewModel = new (function () {
        this.afterAddTag = params.afterAddTag;

        this.value = params.value;
        this.list = params.list;
        this.client_id = params.client_id;
        this.answer_id = params.answer_id;

        this.client = params.client;
        this.addButton =
          "addButton" in params
            ? params.addButton
            : {
                label: null,
              };

        this.popoverOptions = params.popover || {};

        this.dropdownMenuSearchTerm = ko.observable("");

        this.isWatcher = ko.observable(window.CURRENT_USER.watcher ?? 0);

        this.dropdownMenuFilteredList = ko.computed(() => {
          const value = this.value();
          if (typeof this.list === "function") {
            const list = this.list()
              .filter((t) => !t.isAuto)
              .filter((t) => !value.find((tag) => tag.name == t.name))
              .filter((t) => {
                if (!this.dropdownMenuSearchTerm()) return true;
                const term = this.dropdownMenuSearchTerm().toLowerCase();
                return t.name.toLowerCase().includes(term);
              });
            list.sort((a, b) => (a.name < b.name ? -1 : 1));
            return list;
          } else {
            return false;
          }
        });

        this.dropdownMenuFoundExactTagInFilteredList = ko.computed(() => {
          if (
            typeof this.dropdownMenuFilteredList === "function" &&
            this.dropdownMenuFilteredList()
          ) {
            return this.dropdownMenuFilteredList().find(
              (t) => this.dropdownMenuSearchTerm() === t.name
            );
          } else {
            return false;
          }
        });

        this.dropdownMenuFoundExactTagInValue = ko.computed(() => {
          return this.value().find(
            (t) => this.dropdownMenuSearchTerm() === t.name
          );
        });

        this.dropdownMenuControlCreateTagError = ko.observable(false);
        this.serverError = ko.observable("");
        this.dropdownMenuControlCreateTagButtonDisabled = ko.computed(() => {
          return (
            this.serverError() ||
            this.dropdownMenuFoundExactTagInFilteredList() ||
            this.dropdownMenuFoundExactTagInValue()
          );
        });

        this.dropdownMenuSearchTerm.subscribe((_) => {
          this.dropdownMenuControlCreateTagError(false);
          this.serverError("");
        });

        this.dropdownMenuControlCreateTagButtonTitle = ko.computed(() => {
          if (this.dropdownMenuFoundExactTagInValue()) {
            return "Тег уже выбран";
          } else if (this.dropdownMenuFoundExactTagInFilteredList()) {
            return "Тег уже существует";
          } else if (this.serverError()) {
            return this.serverError();
          } else {
            return "Создать новый тег";
          }
        });

        this.dropdownMenuHidden = function () {
          this.dropdownMenuSearchTerm("");
        };

        this.createTag = function () {
          if (this.dropdownMenuControlCreateTagButtonDisabled()) {
            this.dropdownMenuControlCreateTagError(true);
            return;
          }

          const newTag = {
            id: this.dropdownMenuSearchTerm(),
            name: this.dropdownMenuSearchTerm(),
          };
          const data = {
            tags: [newTag],
          };

          let url;

          if (this.client_id) {
            data.clients = [this.client_id];
            url = '/foquz/foquz-contact/add-tags';
          } else if (this.answer_id) {
            data.answers = [this.answer_id];
            url = `/foquz/api/poll/add-tags?access-token=${APIConfig.apiKey}`;
          }

          $.ajax({
            method: "POST",
            url,
            data,
            dataType: "json",
            success: () => {
              this.value.push(newTag);
              this.list.push(newTag);
              if (typeof this.afterAddTag === "function") {
                this.afterAddTag();
              }
              $(this.popover).popover("hide");
            },
            error: (response) => {
              this.serverError(response.responseJSON.errors.message);
              this.dropdownMenuControlCreateTagError(true);
            },
          });
        };

        this.removeTag = function (tag) {
          if (this.client_id || this.answer_id) {
            const data = {
              tags: [tag.name],
            };
            let url;
            if (this.client_id) {
              data.clients = [this.client_id];
              url = '/foquz/foquz-contact/remove-tags';
            } else if (this.answer_id) {
              data.answers = [this.answer_id];
              url = `/foquz/api/poll/remove-tags?access-token=${APIConfig.apiKey}`;
            }

            $.post(
              url,
              data,
              (response) => {
                if (response.success) {
                  this.value.remove(tag);
                }
              },
              "json"
            );
          } else {
            this.value.remove(tag);
          }
        };

        this.addTag = function (tag) {
          if (this.client_id || this.answer_id) {
            const data = {
              tags: [{ id: tag.name, name: tag.name }],
            };
            let url;
            if (this.client_id) {
              data.clients = [this.client_id];
              url = '/foquz/foquz-contact/add-tags';
            } else if (this.answer_id) {
              data.answers = [this.answer_id];
              url = `/foquz/api/poll/add-tags?access-token=${APIConfig.apiKey}`;
            }

            $.post(
              url,
              data,
              (response) => {
                if (response.success) {
                  this.value.push(tag);
                }
              },
              "json"
            );
          } else {
            this.value.push(tag);
          }
        };

        this.popover = null;
        this.updatePopover = window.utils.debounce(() => {
          this.popover.popover("update");
          setTimeout(() => {
            this.popover.popover("update");
          }, 100);
        }, 100);

        this.initializing = ko.observable(true);

        this.onInit = () => {
          const tagPopover = $element.find(
            ".clients__tag-input-add-tag-button-wrapper"
          );
          tagPopover.on("hidden.bs.popover", () => {
            viewModel.dropdownMenuHidden();
          });
          this.popover = tagPopover;
        };
      })();

      return viewModel;
    },
  },
  template: `
        <!-- ko template: { afterRender: onInit } -->
        <div class="clients__tag-input-content">
            <!-- ko foreach: { data: value, afterAdd: fadeAfterAddFactory(200), beforeRemove: fadeBeforeRemoveFactory(200) } -->
            <div class="clients__tag-input-item" data-bind="css: {
                'color-success': $data.isAuto
            }">
                <span data-bind="text: $data.name || $data.tag"></span>&nbsp;<!-- ko ifnot: $data.isAuto -->
                <button class="btn btn-icon btn-default clients__tag-input-item-remove-button" data-bind="click: function() { $component.removeTag($data); }, visible: !$parent.isWatcher()">
                </button>
                <!-- /ko -->
            </div>
            <!-- /ko -->

            <!-- ko if: addButton !== null && !isWatcher() -->
            <div class="clients__tag-input-add-tag-button-wrapper" data-bind="tagInputPopover, popoverOptions: popoverOptions" >
                <button type="button" class="btn btn-circle clients__tag-input-add-tag-button" title="Добавить теги">
                </button>

                <!-- ko if: addButton.label !== null && value().length === 0 -->
                <span class="clients__tag-input-add-tag-button-label"
                    data-bind="text: addButton.label"></span>
                <!-- /ko -->
            </div>
            <!-- /ko -->
        </div>
        <!-- /ko -->
    `,
});
