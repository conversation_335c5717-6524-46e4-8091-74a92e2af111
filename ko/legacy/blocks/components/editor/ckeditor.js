ko.bindingHandlers.CKEditor = {
  init: function (element, valueAccessor, allBindings) {
    const $element = $(element);
    $element.addClass(['editor', 'cke-wrapper']);

    const $editorField = $element.find('.editor__field')
    const $counter = $element.find('.chars-counter');

    const editorSettings = allBindings().editorSettings || {};
    const maxCount = allBindings().editorMaxCount || 0;

    const settings = {
      extraPlugins: 'hat-link',
      allowedContent: true,
      language: 'ru',
      skin: 'bootstrapck,../../assets/ckeditor-skins/bootstrapck/',
      height: '74px',
      enterMode: CKEDITOR.ENTER_BR,
      contentsCss: `
              @import url('https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i&display=swap&subset=cyrillic');

              body{
                  margin: 16px;

                  font-size: 14px;
                  line-height: 19px;
                  font-family: <PERSON><PERSON>, serif;
              }

              *::selection {
                  background: rgba(63, 101, 241, 0.22);
              }
          `,
      toolbar: [
        {
          name: 'basicstyles',
          items: ['Bold', 'Italic', 'Underline', 'Strike', 'HatLink']
        }
      ],
      removeButtons: 'Subscript,Superscript',
      removePlugins: 'contextmenu,tabletools,tableselection,elementspath,link',
      linkShowTargetTab: false,
      removeDialogTabs: 'image:advanced;image:Link;link:advanced;link:upload',
      ...editorSettings
    }

    const editor = CKEDITOR.replace($editorField.attr('id'), settings);

    if ($counter.length) {
      editor.on('instanceReady', function () {
        const getCount = () => {
          const snapshot = editor.getSnapshot();
          const dom = document.createElement('div');
          dom.innerHTML = snapshot;
          const plainText = (dom.textContent || dom.innerText);
          return plainText.length;
        };
        $counter.charsCounter({
          initCount: getCount(),
          maxCount: maxCount,
          registryCountFn: countFn => {
            editor.on('change', () => {
              countFn(getCount());
            });
          }
        });
      });
    }

    $element.click('.editor-variable', (event) => {
      const value = $(event.target).data('value');
      editor.insertText(value);
    });

    editor.on('change', () => {
      $editorField.val(editor.getData());
      $editorField.trigger('change');
    });
  }
};
