ko.bindingHandlers.basicEditor = {
  init: function (element) {
      const $formControl = $(element).find('.form-control');


      $(element).on('click', '[data-variable]', function () {

          const value = $(this).data('value');
          insertAtCaret($formControl.get()[0], value);
          $formControl.trigger('change');
      });

      $(element).on('set.variable', function (e, data) {
          insertAtCaret($formControl.get()[0], data);
          $formControl.trigger('change');
      })
  },
  update: function (element, valueAccessor) {
  }
};
