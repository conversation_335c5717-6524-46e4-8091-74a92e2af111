import './components/counter';
import ConditionsListViewModel from './components/condition-list'

ko.components.register('conditions-list', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      const viewModel = new ConditionsListViewModel(params, componentInfo);
      return viewModel;
    },
  },
  template: {
    element: 'conditions-list-template',
  },
});

ko.components.register('conditions-list-filters', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      const viewModel = new ConditionsListViewModel(params, componentInfo, {
        errors: {
          range: {
            required: 'Заполните интервал',
            invalid: 'Некорректный интервал',
          },
        },
      });
      return viewModel;
    },
  },
  template: {
    element: 'conditions-list-filters-template',
  },
});
