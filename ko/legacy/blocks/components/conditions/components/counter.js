
    ko.components.register('conditions-filters-counter', {
        viewModel: {
            createViewModel: function (params, componentInfo) {
                return new (function () {
                    this._$element = $(componentInfo.element);
                    this._inputElement = ko.observable(null);
                    this._inputAutosize = ko.observable(null);
                    this.value = params.value;

                    this.increment = function () {
                        this.value(+this.value() + 1);
                        this._inputAutosize().update();
                    };

                    this.decrement = function () {
                        this.value(+this.value() - 1);
                        this._inputAutosize().update();
                    };

                    (() => {
                        this._$element.addClass(['clients__counter']);
                    })();
                });
            }
        },
        template: `
        <button class="btn clients__counter-button clients__counter-decrement-button"
        data-bind="click: function() { decrement(); }, attr: { disabled: +value() === 0 }">
        </button>
        <input class="clients__counter-input" data-bind="numericField, textInput: value, element: _inputElement, autosizeInput: _inputAutosize" placeholder="0">
        <button class="btn clients__counter-button clients__counter-increment-button"
                data-bind="click: function() { increment(); }">
        </button>
        `
    });
