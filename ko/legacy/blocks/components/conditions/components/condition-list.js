/**
 * Список условий/фильтров
 */

import ConditionModel from '../models/condition';
import { Directory } from 'Legacy/utils/directory';
import { getClientFields } from "@/api/client/get-client-fields";
import './style.less';

let nextId = 0;

import ee from 'event-emitter';

export default function ConditionsListViewModel(params, componentInfo, config) {
  ee(this);

  this.blocked = params.blocked;

  this.id = nextId++;
  const directories = {
    dishes: new Directory('dish-categories'),
    orderTypes: new Directory('order-type'),
    orderSources: new Directory('order-source'),
    filials:  new Directory(window.POLL_ID ? `filials?pollID=${window.POLL_ID}` : 'filials', null, null, (params?.data && params?.data[0]?.tag_id) ? params.data[0].tag_id : null),
    pools: new Directory('discount-pool?all=1', (data) => {
      return data.items.map((i) => {
        return {
          ...i,
          name: i.title
        };
      });
    }),
  };

  this.dishesSelectHtml = document.createElement('div');
  this.dishesSelectHtmlRendered = ko.observable(false);

  directories.dishes.loaded.subscribe((v) => {
    if (v) {
      ko.renderTemplate(
        'dishes-select-template',
        {
          categories: directories.dishes.data
        },
        {},
        this.dishesSelectHtml
      );
      this.dishesSelectHtmlRendered(true);
    }
  });

  Object.keys(directories).forEach((key) => directories[key].load());

  config = config || {};
  config.orderInFilialShow = params.orderInFilialShow;

  this.title = params.title || 'Условия';
  this.subtitle = params.subtitle || '';
  this.addButtonName = params.addButtonName || 'Добавить условие';
  this.removeAll = params.removeAll || false;

  this.error = params.error || ko.observable('');

  this.directories = directories;

  this.clientFieldsList = {
    system: ko.observableArray([]),
    additional: ko.observableArray([])
  }
  this.clientFieldsLoaded = ko.observable(false);
  getClientFields().then(list => {
    const { system, additional } = list;
    this.clientFieldsList.system(system)
    this.clientFieldsList.additional(additional)
    this.clientFieldsLoaded(true)
  })

  this.showErrors = params.showErrors || ko.observable(false);
  this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
    this.showErrors
  );
  this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
    this.showErrors
  );

  this.timeList = [
    { index: 'Утро', text: 'Утро' },
    { index: 'День', text: 'День' },
    { index: 'Вечер', text: 'Вечер' },
    { index: 'Ночь', text: 'Ночь' }
  ];

  this.daysList = [
    { index: 'Понедельник', text: 'Понедельник' },
    { index: 'Вторник', text: 'Вторник' },
    { index: 'Среда', text: 'Среда' },
    { index: 'Четверг', text: 'Четверг' },
    { index: 'Пятница', text: 'Пятница' },
    { index: 'Суббота', text: 'Суббота' },
    { index: 'Воскресенье', text: 'Воскресенье' }
  ];

  this.periodPickerRanges = window.periodPickerConfig.ranges;

  this.conditionTypes = window.conditionTypes;

  // актуальный динамический список
  this.conditions = params.conditions;
  this.data = params.data || [];

  this.conditions.subscribe((_) => this.emit('change'));

  this.canAddCondition = ko.pureComputed(() => {
    return this.conditions().every((c) => c.type());
  });

  this.canAddConditionFilter = ko.pureComputed(() => {
    return this.conditions().every((c) => c.model.isValid());
  });

  // Добавить новое условие в список
  this.addCondition = (conditionData) => {
    const condition = this.createCondition(conditionData);
    this.conditions.push(condition);
  };
  this.addConditionFilter = (conditionData) => {
    if (this.canAddConditionFilter()) {
      this.addCondition(conditionData);
    }
  };

  this.createCondition = (conditionData) => {
    let model = new ConditionModel(conditionData, config, this);
    model.on('change', () => this.emit('change'));
    return model;
  };
  this.setConditions = (conditionsData) => {
    let currentLength = this.conditions().length;
    let newLength = conditionsData.length;

    while (currentLength > newLength) {
      this.conditions.pop();
      currentLength = this.conditions().length;
    }

    for (let i = 0; i < currentLength; i++) {
      let oldCondition = this.conditions()[i];
      oldCondition._setDefaultData(conditionsData[i]);
    }

    if (newLength > currentLength) {
      for (let i = currentLength; i < newLength; i++) {
        let newCondition = this.createCondition(conditionsData[i]);
        this.conditions.push(newCondition);
      }
    }
  };

  this.data.forEach((conditionData) => {
    this.addCondition(conditionData);
  });

  this.removeCondition = (condition) => {
    this.conditions.remove(condition);
  };

  this.reset = () => {
    this.conditions.removeAll();
  };

  this.fadeAfterAdd = () => {
    return function (el) {
      $(el).hide().delay(0).fadeIn(0);
    };
  };

  this.fadeBeforeRemove = () => {
    return function (el) {
      $(el).fadeOut(0, () => {
        $(el).remove();
      });
    };
  };

  if (params.model && ko.isObservable(params.model)) params.model(this);
}
