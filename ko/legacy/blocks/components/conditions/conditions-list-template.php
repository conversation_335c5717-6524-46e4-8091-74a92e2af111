<template id="conditions-list-last-order-date-template">
  <div class="condition-value condition-value--last-order-date">
    <div class="">
      <label class="form-label">Критерий</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Критерий">
      </button>

      <div class="d-flex flex-wrap">

        <div class="mb-4 mr-4 condition-value__select">
          <div class="select2-wrapper">
            <select data-bind="value: $data.criterion,
                select2: {
                    containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control',
                    allowClear: false,
                    placeholder: 'Не выбрано'
                }, disable: $component.blocked">
              <option value="1">Дней прошло</option>
              <option value="2">Период</option>
              <option value="3">До</option>
              <option value="4">После</option>
              <option value="5">Конкретная дата</option>
            </select>
          </div>
        </div>

        <div class="flex-grow-1">
          <!-- Дней прошло -->
          <!-- ko template: {
                    foreach: templateIf($data.criterion() == 1, $data),
                    afterAdd: fadeAfterAddFactory(200)
                } -->
          <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
            <div class="mb-4" data-bind="component: {
                            name: 'number-control',

                            params: {
                                className: 'number-control--small',
                                value: $data.from,
                                isInvalid: $component.formControlErrorStateMatcher($data.from),
                                min: 0,
                                isDisabled: $component.blocked
                            }
                        }">
            </div>
            <div class="mb-4 conditions-list__separator">
              –
            </div>
            <div class="mb-4  number-control--small" data-bind="component: {
                            name: 'number-control',
                            params: {
                              className: 'number-control--small',
                                value: $data.to,
                                isInvalid: $component.formControlErrorStateMatcher($data.to),
                                min: 0,
                                isDisabled: $component.blocked
                            }
                        }">
            </div>


          </div>
          <!-- ko template: {
                        foreach: $component.formControlErrorStateMatcher($data.from)() || $component.formControlErrorStateMatcher($data.to)(),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->
          <div class="form-error" data-bind="text: $parent.from.error() || $parent.to.error()"></div>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /Дней прошло -->

          <!-- Период -->
          <!-- ko template: {
                    foreach: templateIf($data.criterion() == 2, $data),
                    afterAdd: fadeAfterAddFactory(200),
                } -->
          <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
            <div class="mb-4 condition-value__period date-input-group">
              <input class="form-control" placeholder="00.00.0000-00.00.0000" data-bind="value: $data.period,
                            periodPicker,
                            periodPickerArrowPosition: { anchor: 'right', offset: -10 },
                            periodPickerRanges: $component.periodPickerRanges,
                            periodPickerApply: function() {},
                            periodPickerSeparator: '-',
                            css: {
                                'is-invalid': $component.formControlErrorStateMatcher($data.period)
                            }, disable: $component.blocked">
              <i class="date-input-group__icon"></i>
            </div>
          </div>
          <!-- ko template: {
                        foreach: $component.formControlErrorStateMatcher($data.period),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->
          <div class="form-error" data-bind="text: $parent.period.error()"></div>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /Период -->

          <!-- Одна дата (до/после/конкретная дата) -->
          <!-- ko template: {
                    foreach: templateIf($data.criterion() >= 3, $data),
                    afterAdd: fadeAfterAddFactory(200),
                } -->
          <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
            <div class="mb-4 condition-value__date date-input-group">
              <input class="form-control" data-bind="mask, maskPattern: '00.00.0000',
                           textInput:$data.date,
                           periodPicker,
                           periodPickerArrowPosition: { anchor: 'right', offset: -27 },
                           periodPickerSeparator: '-',
                           periodPickerOpens: 'left',
                           periodPickerDrops: 'up',
                           periodPickerSingle: true,
                           css: {
                                'is-invalid': $component.formControlErrorStateMatcher($data.date)
                            }, disable: $component.blocked" placeholder="00.00.0000">
              <i class="date-input-group__icon"></i>
            </div>


          </div>

          <!-- ko template: {
                        foreach: $component.formControlErrorStateMatcher($data.date),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->
          <div class="form-error" data-bind="text: $parent.date.error()"></div>
          <!-- /ko -->
          <!-- /ko -->
        </div>

      </div>
    </div>
  </div>
</template>

<template id="conditions-list-complaint-in-the-order-template">
  <div class="condition-value condition-value--complaint-in-the-order">
    <div class="">
      <label class="form-label">Заказ сделан</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Заказ сделан">
      </button>

      <div class="d-flex flex-wrap">

        <div class="mb-4 mr-4 condition-value__select">
          <div class="select2-wrapper">
            <select data-bind="value: $data.criterion,
                select2: {
                    containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control',
                    allowClear: false,
                    placeholder: 'Не выбрано'
                }, disable: $component.blocked">
              <option value="1">Дней прошло</option>
              <option value="2">Период</option>
              <option value="3">До</option>
              <option value="4">После</option>
              <option value="5">Конкретная дата</option>
            </select>
          </div>
        </div>

        <div class="flex-grow-1">
          <!-- Дней прошло -->
          <!-- ko template: {
                    foreach: templateIf($data.criterion() == 1, $data),
                    afterAdd: fadeAfterAddFactory(200)
                } -->
          <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
            <div class="mb-4" data-bind="component: {
                            name: 'number-control',

                            params: {
                                className: 'number-control--small',
                                value: $data.from,
                                isInvalid: $component.formControlErrorStateMatcher($data.from),
                                min: 0,
                                isDisabled: $component.blocked
                            }
                        }">
            </div>
            <div class="mb-4 conditions-list__separator">
              –
            </div>
            <div class="mb-4  number-control--small" data-bind="component: {
                            name: 'number-control',
                            params: {
                              className: 'number-control--small',
                                value: $data.to,
                                isInvalid: $component.formControlErrorStateMatcher($data.to),
                                min: 0,
                                isDisabled: $component.blocked
                            }
                        }">
            </div>


          </div>
          <!-- ko template: {
                        foreach: $component.formControlErrorStateMatcher($data.from)() || $component.formControlErrorStateMatcher($data.to)(),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->
          <div class="form-error" data-bind="text: $parent.from.error() || $parent.to.error()"></div>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /Дней прошло -->

          <!-- Период -->
          <!-- ko template: {
                    foreach: templateIf($data.criterion() == 2, $data),
                    afterAdd: fadeAfterAddFactory(200),
                } -->
          <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
            <div class="mb-4 condition-value__period date-input-group">
              <input class="form-control" placeholder="00.00.0000-00.00.0000" data-bind="value: $data.period,
                            periodPicker,
                            periodPickerArrowPosition: { anchor: 'right', offset: -10 },
                            periodPickerRanges: $component.periodPickerRanges,
                            periodPickerApply: function() {},
                            periodPickerSeparator: '-',
                            css: {
                                'is-invalid': $component.formControlErrorStateMatcher($data.period)
                            }, disable: $component.blocked">
              <i class="date-input-group__icon"></i>
            </div>
          </div>
          <!-- ko template: {
                        foreach: $component.formControlErrorStateMatcher($data.period),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->
          <div class="form-error" data-bind="text: $parent.period.error()"></div>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /Период -->

          <!-- Одна дата (до/после/конкретная дата) -->
          <!-- ko template: {
                    foreach: templateIf($data.criterion() >= 3, $data),
                    afterAdd: fadeAfterAddFactory(200),
                } -->
          <div class="d-flex flex-wrap align-items-center justify-content-start position-relative">
            <div class="mb-4 condition-value__date date-input-group">
              <input class="form-control" data-bind="mask, maskPattern: '00.00.0000',
                           textInput:$data.date,
                           periodPicker,
                           periodPickerArrowPosition: { anchor: 'right', offset: -27 },
                           periodPickerSeparator: '-',
                           periodPickerOpens: 'left',
                           periodPickerDrops: 'up',
                           periodPickerSingle: true,
                           css: {
                                'is-invalid': $component.formControlErrorStateMatcher($data.date)
                            }, disable: $component.blocked" placeholder="00.00.0000">
              <i class="date-input-group__icon"></i>
            </div>


          </div>

          <!-- ko template: {
                        foreach: $component.formControlErrorStateMatcher($data.date),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->
          <div class="form-error" data-bind="text: $parent.date.error()"></div>
          <!-- /ko -->
          <!-- /ko -->
        </div>

      </div>
    </div>
  </div>
</template>

<template id="conditions-list-orders-per-month-template">
  <div class="condition-value condition-value--orders-per-month">

    <div class="">
      <label class="form-label">Количество</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Количество">
      </button>

      <div class="d-flex flex-wrap">
        <div class="mb-4" data-bind="component: {
            name: 'number-control',
            params: {
                value: $data.from,
                isInvalid: $component.formControlErrorStateMatcher($data.from),
                                min: 0,
                                isDisabled: $component.blocked
            } }">
        </div>
        <div class="mb-4 conditions-list__separator">
          –
        </div>
        <div class="mb-4" data-bind="component: {
                name: 'number-control',
                params: {
                    value: $data.to,
                    isInvalid: $component.formControlErrorStateMatcher($data.to),
                                min: 0,
                                isDisabled: $component.blocked
                }
            }">
        </div>
      </div>

      <!-- ko template: {
        foreach: $component.formControlErrorStateMatcher($data.from)() || $component.formControlErrorStateMatcher($data.to)(),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
      <div class="form-error" data-bind="text: $parent.from.error() || $parent.to.error()"></div>
      <!-- /ko -->
    </div>



  </div>
</template>

<template id="conditions-list-days-template">
  <div class="condition-value condition-value--days">
    <div class="conditions-list__days">
      <div class="conditions-list__days-columns">
        <!--ko foreach: { data: $component.daysList } -->
        <div class="form-group form-group_to_check">
          <div class="form-check">
            <input type="checkbox" class="form-check-input" name="daysCheck" data-bind="
                              value: $data.index,
                              checked: $parent.days,
                              attr: { id: 'daysCheck_' + $index() },
                              disable: $component.blocked">
            <label class="form-check-label" data-bind="text: $data.text,
                           attr: { 'for': 'daysCheck_'+$index()}">
            </label>
          </div>
        </div>
        <!-- /ko -->
      </div>
      <!-- ko template: {
            foreach: $component.formControlErrorStateMatcher($data.days),
            afterAdd: fadeAfterAddFactory(200),
            beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
      <div class="form-error" data-bind="text: $parent.days.error()"></div>
      <!-- /ko -->
    </div>
  </div>
</template>

<template id="conditions-list-time-template">
  <div class="condition-value condition-value--time">
    <div class="conditions-list__time">
      <div class="conditions-list__time-columns">
        <!--ko foreach: { data: $component.timeList} -->
        <div class="form-group form-group_to_check">
          <div class="form-check">
            <input type="checkbox" class="form-check-input" name="timeCheck" data-bind="attr: {
                               id: 'timeCheck_'+$index(),
                            },
                            value: $data.index,
                            checked: $parent.time,
                            disable: $component.blocked">
            <label class="form-check-label" data-bind="text: $data.text,
                           attr: { 'for': 'timeCheck_'+$index() }">
            </label>
          </div>
        </div>
        <!-- /ko -->
      </div>
      <!-- ko template: {
            foreach: $component.formControlErrorStateMatcher($data.time),
            afterAdd: fadeAfterAddFactory(200),
            beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
      <div class="form-error" data-bind="text: $parent.time.error()"></div>
      <!-- /ko -->
    </div>
  </div>
</template>

<template id="conditions-list-dishes-price-template">
  <div class="condition-value condition-value--dishes-price">
    <div class="form-group">
      <label class="form-label">Min цена, ₽</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Min цена, ₽">
      </button>

      <input class="form-control condition-value__select" data-bind="textInput: $data.minPrice, numericField, disable: $component.blocked" placeholder="0" maxlength="10">
    </div>
  </div>
</template>

<template id="conditions-list-dishes-template">
  <div class="condition-value condition-value--dishes">
    <div class="">
      <label class="form-label">Блюда/категории</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Блюда/категории">
      </button>

      <div class="mb-4">
        <div class="" data-bind="descendantsComplete: function(el) { $data.renderDishesSelectBlock(el, $data) }">

          <div class="select2-wrapper  condition-value__select w-100" , data-bind="css: {
                    'is-invalid': $component.formControlErrorStateMatcher($data.dishes)
                }">
            <select multiple data-bind="visible: false, disable: $component.blocked" data-placeholder="Не выбрано">
            </select>
          </div>
        </div>
      </div>

      <!-- ko template: {
                        foreach: $component.formControlErrorStateMatcher($data.dishes),
                        afterAdd: fadeAfterAddFactory(200),
                        beforeRemove: fadeBeforeRemoveFactory(200)
                    } -->
      <div class="form-error mb-3" data-bind="text: $parent.dishes.error()"></div>
      <!-- /ko -->
    </div>
  </div>

</template>

<template id="conditions-list-income-template">
  <div class="condition-value condition-value--income">
    <div class="">
      <label class="form-label">Выручка, ₽</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Выручка, ₽">
      </button>

      <div class="d-flex flex-wrap ">
        <div class="mb-4" data-bind="component: {
                    name: 'number-control',
                    params: {
                        value: $data.from,
                        isCurrency:true,
                        isInvalid: $component.formControlErrorStateMatcher($data.from),
                                min: 0,
                                isDisabled: $component.blocked
                    }
                }">
        </div>
        <div class="mb-4 conditions-list__separator">
          –
        </div>
        <div class="mb-4" data-bind="component: {
                    name: 'number-control',
                    params: {
                        value: $data.to,
                        isCurrency:true,
                        isInvalid: $component.formControlErrorStateMatcher($data.to),
                                min: 0,
                                isDisabled: $component.blocked
                    }
                },">
        </div>
      </div>

      <!-- ko template: {
            foreach: $component.formControlErrorStateMatcher($data.from)() || $component.formControlErrorStateMatcher($data.to)(),
            afterAdd: fadeAfterAddFactory(200),
            beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
      <div class="form-error" data-bind="text: $parent.from.error() || $parent.to.error()"></div>
      <!-- /ko -->
    </div>
  </div>
</template>

<template id="conditions-list-method-template">
  <div class="condition-value condition-value--method">
    <div class="d-flex flex-wrap">
      <div class="mb-4 mr-4">
        <label class="form-label">Способ оформления</label>

        <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Способ оформления">
        </button>

        <div class="select2-wrapper condition-value__select">
          <select data-bind="value: $data.orderSource,
                select2: {
                    containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control',
                    placeholder: 'Не выбрано',
                    minimumResultsForSearch: 10, allowClear: false,
                },
                options: $component.directories.orderSources.data,
                optionsText: 'name',
                optionsValue: 'id',
                disable: $component.blocked">
          </select>
        </div>
      </div>
      <div class="mb-4">
        <label class="form-label">Заказ</label>

        <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Заказ">
        </button>
        <div class="select2-wrapper condition-value__select condition-value__select--lg">
          <select data-bind="value: $data.orderValue, select2: { containerCssClass: 'form-control', wrapperCssClass: 'select2-container--form-control',
                                        minimumResultsForSearch: 10, allowClear: false, placeholder: 'Не выбрано' }, disable: $component.blocked">
            <option value="1">Хотя бы один раз</option>
            <option value="2">Не заказывал ни разу</option>
          </select>
        </div>
      </div>
    </div>
  </div>

</template>

<template id="conditions-list-survey-template">
  <div class="condition-value condition-value--survey">
    <div class="">
      <label class="form-label">Участие в опросах, %</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Участие в опросах, %">
      </button>

      <div class="">
        <div class="condition-value__slider">
          <div class="condition-value__length-control">
            <span class="condition-value__length-control-boundary condition-value__length-control-min-boundary">
              0
            </span>

            <div class="condition-value__length-control-slider" data-bind="
                                                slider,
                                                sliderRange: true,
                                                sliderValue: $data.surveyArray,
                                                sliderMin: 0,
                                                sliderMax: 100,
                                                disabled: $component.blocked
                                             ">
              <div class="ui-slider-handle condition-value__length-control-slider-handle">
                <span class="condition-value__length-control-slider-handle-indicator" data-bind="text: $data.surveyArray()[0]">
                </span>
              </div>
              <div class="ui-slider-handle condition-value__length-control-slider-handle">
                <span class="condition-value__length-control-slider-handle-indicator" data-bind="text: $data.surveyArray()[1]">
                </span>
              </div>
            </div>

            <span class="condition-value__length-control-boundary condition-value__length-control-max-boundary">
              100
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<template id="conditions-list-codes-template">
  <div class="condition-value condition-value--survey">
    <div class="">
      <label class="form-label">Использовано, %</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Использовано, %">
      </button>

      <div class="">
        <div class="condition-value__slider">
          <div class="condition-value__length-control">
            <span class="condition-value__length-control-boundary condition-value__length-control-min-boundary">
              0
            </span>

            <div class="condition-value__length-control-slider" data-bind="
                                                slider,
                                                sliderRange: true,
                                                sliderValue: $data.codesArray,
                                                sliderMin: 0,
                                                sliderMax: 100,
                                                disabled: $component.blocked
                                             ">
              <div class="ui-slider-handle condition-value__length-control-slider-handle">
                <span class="condition-value__length-control-slider-handle-indicator" data-bind="text: $data.codesArray()[0]">
                </span>
              </div>
              <div class="ui-slider-handle condition-value__length-control-slider-handle">
                <span class="condition-value__length-control-slider-handle-indicator" data-bind="text: $data.codesArray()[1]">
                </span>
              </div>
            </div>

            <span class="condition-value__length-control-boundary condition-value__length-control-max-boundary">
              100
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<template id="conditions-list-type-template">
  <div class="condition-value condition-value--type">
    <div class="d-flex flex-wrap">
      <div class="mb-4 mr-4">
        <label class="form-label">Тип заказа</label>

        <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Тип заказа">
        </button>

        <div class='select2-wrapper condition-value__select'>
          <select data-bind="value: $data.orderType,
                select2: {
                    containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control',
                    placeholder: 'Не выбрано',
                    minimumResultsForSearch: 10,
                },
                options: $component.directories.orderTypes.data,
                optionsText: 'name',
                optionsValue: 'id',
                disable: $component.blocked" data-placeholder="Не выбрано">
          </select>
        </div>
      </div>
      <div class="mb-4">
        <label class="form-label">Заказ</label>

        <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Заказ">
        </button>
        <div class='select2-wrapper condition-value__select condition-value__select--lg'>
          <select data-bind="value: $data.orderValue,
                                    select2: {
                                        containerCssClass: 'form-control',
                                        wrapperCssClass: 'select2-container--form-control',
                                        placeholder: 'Не выбрано',
                                        minimumResultsForSearch: 10,
                                    }, disable: $component.blocked">
            <option value="1">Хотя бы один раз</option>
            <option value="2">Не заказывал ни разу</option>
          </select>
        </div>
      </div>
    </div>
  </div>

</template>

<template id="conditions-list-filial-type-template">
  <div class="condition-value condition-value--order-type" data-bind="log">

    <div class="mb-4">
      <label class="form-label">Заказ</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Заказ">
      </button>
      <div class='select2-wrapper condition-value__select'>
        <select data-bind="value: $data.filialsOrderType,
            select2: {
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                placeholder: 'Не выбрано',
                minimumResultsForSearch: 10,
            }, disable: $component.blocked">
          <option value="1">Хотя бы один раз</option>
          <option value="2">Ни разу</option>
          <!-- ko if: $data.orderInFilialShow -->
          <option value="3">Заказ в этом филиале</option>
          <!-- /ko -->
        </select>
      </div>
    </div>

  </div>
</template>

<template id="conditions-list-filial-select-template">
  <!-- ko template: {
    foreach: templateIf($component.directories.filials.loaded(), $data),
    afterAdd: fadeAfterAddFactory(200),
  } -->
  <div class="flex-grow-1 mb-2">
    <div class="form-group condition-value condition-value--filial">
      <label class="form-label">Филиал</label>

      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Филиал">
      </button>
      <div class='select2-wrapper select2-wrapper--with-flag condition-value__select condition-value__select--full' data-bind="css: {
        'is-invalid': $component.formControlErrorStateMatcher($data.filials),
        'is-valid': $component.formControlSuccessStateMatcher($data.filials),
      }">
        <fc-select
          class="select2-container--form-control"
          params="
            options: $component.directories.filials.data,
            value: $data.filials,
            placeholder: 'Все филиалы',
            multiple: true,
            blockSelectedGroup: true,
            disableFoldersSelect: true,
            optionTextProp: 'name',
          "
        ></fc-select>
        <div class="form-check" data-bind="stopEvents">
          <input type="checkbox" class="form-check-input" data-bind="checked: $data.filialsExclusionFlag,
          enable: !$component.blocked && $data.filials().length,
          attr: {
            id: 'filial-condition-exclusion-flag' + conditionIndex
          }">
          <label data-bind="attr: { 'for': 'filial-condition-exclusion-flag' + conditionIndex }" class="form-check-label">Все, кроме</label>
        </div>
      </div>
    </div>
    <!-- ko template: {
    foreach: templateIf($component.formControlErrorStateMatcher($data.filials)(), $data),
    afterAdd: fadeAfterAddFactory(200),
  } -->
    <div class="form-error" data-bind="html: $data.filials.error()"></div>
    <!-- /ko -->
  </div>

  <!-- /ko -->
</template>

<template id="conditions-list-promocode-used-template">
  <div class="condition-value  condition-value--promocode">
    <div class="form-group mr-4">
      <label class="form-label">
        Промокод
        <question-button params="text: 'Промокод'"></question-button>
      </label>
      <radio-group params="value: $data.promocodeUsed, options: [
        {value: '1', label: 'Использован' },
        {value: '0', label: 'Не использован' },
        ]">

      </radio-group>

    </div>
  </div>
</template>

<template id="conditions-list-promocode-pools-template">
  <!-- ko template: {
      foreach: templateIf($component.directories.pools.loaded(), $data),
      afterAdd: fadeAfterAddFactory(200)
    } -->
  <div class="condition-value  condition-value--pools">
    <div class="form-group ">

      <label class="form-label">Пул купонов
        <question-button params="text: 'Пул купонов'"></question-button>
      </label>

      <div class='select2-wrapper'>
        <select data-bind="selectedOptions: $data.pools,
          valueAllowUnset: true,
          select2: {
            containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control',
              placeholder: 'Любой',
              minimumResultsForSearch: 10,
          },
          options: $component.directories.pools.data,
          optionsText: 'name',
          optionsValue: 'id'" multiple>
        </select>
      </div>
    </div>
  </div>
  <!-- /ko -->

</template>

<template id="conditions-list-contact-data">
  <div class="condition-value  condition-value--contact">
    <div>

      <fc-label params="text: 'Данные контакта', hint: 'Данные контакта'"></fc-label>

      <div class="form-group">
        <fc-radio-group class="mb-15p" params="value: $data.contactDataFilled, options: [
            { id: '1', label: 'Заполнены' },
            { id: '2', label: 'Не заполнены' }
          ]"></fc-radio-group>
        <!-- ko if: $component.clientFieldsLoaded() -->
        <div class="select2-wrapper mt-3">
          <select multiple data-bind="
                        selectedOptions: $data.contactFields,
                        lazySelect2: {
                        
                          containerCssClass: 'form-control',
                          wrapperCssClass:
                            'select2-container--form-control',
                          minimumResultsForSearch: 0,
                          placeholder: 'Выберите поля'
                        }" data-placeholder="Выберите поля">
            <optgroup label="Системные">
              <!-- ko foreach: {data: $component.clientFieldsList.system, as: 'field'} -->
              <option data-bind="text: field.text, value: field.id"></option>
              <!-- /ko -->
            </optgroup>
            <optgroup label="Пользовательские">
              <!-- ko foreach: {data: $component.clientFieldsList.additional, as: 'field'} -->
              <option data-bind="text: field.text, value: field.id"></option>
              <!-- /ko -->
            </optgroup>
          </select>
        </div>
        <!-- /ko -->

        <fc-error params="show: $component.formControlErrorStateMatcher($data.contactFields), text: $data.contactFields.error"></fc-error>
      </div>
    </div>
  </div>
</template>


<template id="conditions-list-template">
  <section class="conditions-list">
    <div class="conditions-list__header mb-4">
      <div>
        <h3 class="conditions-list__title">
          <!-- ko text: title -->
          <!-- /ko -->

          <span class="conditions-list__count" data-bind="text: conditions().length"></span>
        </h3>
        <!--  ko if: subtitle -->
        <div class="conditions-list__subtitle service-text" data-bind="text: subtitle"></div>
        <!-- /ko -->
      </div>

      <!-- ko ifnot: blocked -->
      <button class="f-btn f-btn-text f-btn-success" data-bind="click: addCondition, disable: !canAddCondition()">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'plus'" class="svg-icon--sm"></svg-icon>
        </span>
        <!-- ko text: addButtonName -->
        <!-- /ko -->
      </button>
      <!-- /ko -->
    </div>

    <!-- ko template: {
      foreach: templateIf(error(), $data),
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
    <div class="form-error" data-bind="text: $parent.error"></div>
    <!-- /ko -->

    <div class="conditions-list__content">

      <!-- ko foreach: {
        data: conditions,
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
      <div class="conditions-list__item condition" data-bind="class: 'condition--' + $data.type(), let: { conditionIndex: $index() }">

        <div class="condition__row">
          <div class="condition__type">
            <div class="form-group">

              <label class="form-label">Условие</label>
              <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Условие">
              </button>

              <div class="select2-wrapper" data-bind="css: {
                    'is-invalid': $component.formControlErrorStateMatcher($data.type),
                    'is-valid': $component.formControlSuccessStateMatcher($data.type),
                  }">
                <select data-bind="value: $data.type,
                  valueAllowUnset: true,
                  lazySelect2: {
                    containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control',
                    minimumResultsForSearch: 0,
                    allowClear: true,
                    placeholder: 'Не выбрано'
                  }, disable: $component.blocked">
                  <option></option>
                  <!-- ko foreach: $component.conditionTypes -->
                  <option data-bind="value: $data.id, text: $data.text"></option>
                  <!-- /ko -->
                </select>


              </div>

              <!-- ko template: {
                  foreach: $component.formControlErrorStateMatcher($data.type),
                  afterAdd: fadeAfterAddFactory(200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
                } -->
              <div class="form-error mt-1" data-bind="text: $parent.type.error()"></div>
              <!-- /ko -->

            </div>

          </div>

          <div class="condition__value overflow-hidden">

            <!-- ko template: {
                  name: 'conditions-list-last-order-date-template',
                  foreach: templateIf(type()=='last-order-date', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-complaint-in-the-order-template',
                  foreach: templateIf(type()=='complaint-in-the-order', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-orders-per-month-template',
                  foreach: templateIf(type()=='avg-year-count-orders', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-orders-per-month-template',
                  foreach: templateIf(type()=='avg-month-count-orders' , $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-income-template',
                  foreach: templateIf(type()=='order-amount-from-client-by-year' || type() == 'order-amount-from-client-by-month', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-income-template',
                  foreach: templateIf(type()=='avg-check-customer-orders', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-days-template',
                  foreach: templateIf(type()=='main-orders-days', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-time-template',
                  foreach: templateIf(type()=='favorite-order-time', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-dishes-price-template',
                  foreach: templateIf(type()=='favorite-dish', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-survey-template',
                  foreach: templateIf(type()=='polls-participation', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-codes-template',
                  foreach: templateIf(type()=='codes-percent', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-type-template',
                  foreach: templateIf(type()=='order-type', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-method-template',
                  foreach: templateIf(type()=='source-type', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-filial-type-template',
                  foreach: templateIf(type()=='filial', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->

            <!-- ko template: {
                  name: 'conditions-list-promocode-used-template',
                  foreach: templateIf(type()=='promocode', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
            <!-- /ko -->
          </div>
        </div>

        <!-- ko template: {
          foreach: templateIf(type()=='filial', $data),
          afterAdd: fadeAfterAddFactory(200, 200),
          beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
        <div class="condition__row">
          <!-- ko template: {
            name: 'conditions-list-filial-select-template',
          } -->
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- ko template: {
          foreach: templateIf(type()=='complaint-in-the-order', $data),
          afterAdd: fadeAfterAddFactory(200, 200),
          beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
        <div class="condition__row">
          <div class="form-group condition-value condition-value--filial">
            <label class="form-label">Наличие жалобы</label>

            <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Наличие жалобы">
            </button>
            <div>
              <div class="d-inline-block">
                <div class="hat-radio-group form-control" style="width: 240px">
                  <div class="hat-radio-group__radio">
                    <input type="radio" class="hat-radio-group__radio-input" name="rdo" id="rdo-1" value="1" data-bind="checked: $data.complaint, disable: $component.blocked">
                    <label for="rdo-1" class="hat-radio-group__radio-label">
                      <span class="hat-radio-group__radio-indicator"></span>
                      Есть
                    </label>
                  </div>

                  <div class="hat-radio-group__radio">
                    <input type="radio" class="hat-radio-group__radio-input" name="rdo" id="rdo-2" value="0" data-bind="checked: $data.complaint, disable: $component.blocked">
                    <label for="rdo-2" class="hat-radio-group__radio-label">
                      <span class="hat-radio-group__radio-indicator"></span>
                      Нет
                    </label>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
        <!-- /ko -->

        <!-- ko template: {

              foreach: templateIf(type()=='favorite-dish', $data),
              afterAdd: fadeAfterAddFactory(200, 200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
        <div class="condition__row">
          <!-- ko template: {
              name: 'conditions-list-dishes-template',
          } -->
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- ko template: {

                foreach: templateIf(type()=='promocode', $data),
                afterAdd: fadeAfterAddFactory(200, 200),
                beforeRemove: fadeBeforeRemoveFactory(200)
            } -->
        <div class="condition__row">
          <!-- ko template: {
              name: 'conditions-list-promocode-pools-template',
          } -->
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- ko template: {
                foreach: templateIf(type()=='contact-data', $data),
                afterAdd: fadeAfterAddFactory(200, 200),
                beforeRemove: fadeBeforeRemoveFactory(200)
            } -->
        <div class="condition__row">
          <!-- ko template: {
              name: 'conditions-list-contact-data',
          } -->
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- ko ifnot: $parent.blocked -->
        <!-- ko if: $parent.removeAll || $parent.conditions().length > 1 -->
        <button type="submit" class="btn btn-danger btn-remove" title="Удалить" data-bind="click: $component.removeCondition, tooltip">
        </button>
        <!-- /ko -->
        <!-- /ko -->

      </div>
      <!-- /ko -->

      <!-- ko template: {
        foreach: templateIf(!blocked && conditions().length > 0, $data),
        afterAdd: fadeAfterAddFactory(200, 200),
        beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
      <footer class="conditions-list__footer">
        <button class="f-btn f-btn-text f-btn-success" data-bind="click: addCondition, disable: !canAddCondition()">
          <span class="f-btn-prepend">
            <svg-icon params="name: 'plus'" class="svg-icon--sm"></svg-icon>
          </span>
          <!-- ko text: addButtonName -->
          <!-- /ko -->
        </button>
      </footer>
      <!-- /ko -->
    </div>
  </section>

</template>