import ee from "event-emitter";

export default function ConditionModel(conditionData, config, root) {
  ee(this);
  this.root = root;
  this.touched = ko.observable(false);
  config = config || {};

  this.orderInFilialShow = config.orderInFilialShow;

  const errors = {
    type: {
      required: "Обязательное поле",
    },
    value: {
      required: "Обязательное поле",
      invalid: "Некорректное значение",
    },
    period: {
      required: "Обязательное поле",
    },
    range: {
      required: "Обязательное поле",
      invalid: "Некорректный формат",
    },
    days: {
      required: "Должен быть выбран хотя бы один день",
    },
    time: {
      required: "Должно быть выбрано хотя бы одно время",
    },
    dishes: {
      required: "Обязательное поле",
    },
    filials: {
      empty:
        'Список филиалов пустой. Для добавления условия необходимо <a href="/foquz/settings?tab=settings&setting=collections">добавить хотя бы один филиал</a>',
    },
    ...(config.errors || {}),
  };

  // Тип условия (для всех)
  this.type = ko.observable("").extend({
    required: {
      message: errors.type.required,
    },
  });

  /* Критерий Дата последнего заказа */
  this.criterion = ko.observable("1");
  /* Дата последнего заказа (до/после/конкретная дата) */
  this.date = ko.observable(null).extend({
    required: {
      message: errors.value.required,
    },
    validation: {
      validator: (v) => {
        return moment(v, "DD.MM.YYYY").format() !== "Invalid date";
      },
      message: errors.value.invalid,
    },
  });
  /* Дата последнего заказа (период) */
  this.period = ko.observable("").extend({
    required: {
      message: errors.period.required,
    },
    validation: window.periodPickerConfig.validation,
  });

  /*
      Дата последнего заказа (дней прошло)
      Среднее кол-во заказов
      Выручка от заказов
      Средний чек
      */
  this.from = ko.observable(0);
  /*
      Дата последнего заказа (дней прошло)
      Среднее кол-во заказов
      Выручка от заказов
      Средний чек
      */
  this.to = ko.observable(0);

  this.from.extend({
    required: {
      message: errors.range.required,
    },
    validation: [
      {
        validator: (v) => {
          return +v <= +this.to();
        },
        message: errors.range.invalid,
        onlyIf: () => this.to() > 0,
      },
    ],
  });

  this.to.extend({
    required: {
      message: errors.range.required,
    },
    validation: [
      {
        validator: (v) => {
          return v > 0;
        },
        message: errors.range.required,
      },
      {
        validator: (v) => {
          return +v >= +this.from();
        },
        message: errors.range.invalid,
        onlyIf: () => this.from() > 0,
      },
    ],
  });

  // Дни заказов
  this.days = ko.observableArray([]).extend({
    validation: {
      validator: (v) => {
        return v.length;
      },
      message: errors.days.required,
    },
  });

  // Время заказов
  this.time = ko.observableArray([]).extend({
    validation: {
      validator: (v) => {
        return v.length;
      },
      message: errors.time.required,
    },
  });

  // Любимое блюдо
  this.renderDishesSelectBlock = (el, $data) => {
    if (root.dishesSelectHtmlRendered())
      this._renderDishesSelectBlock(el, $data);
    else {
      root.dishesSelectHtmlRendered.subscribe((v) => {
        if (v) this._renderDishesSelectBlock(el, $data);
      });
    }
  };
  this._renderDishesSelectBlock = (el, $data) => {
    const select = el.querySelector("select");
    const selectClone = root.dishesSelectHtml.cloneNode(true);
    $(select).append($(selectClone).children());
    $(select)
      .find("option")
      .each(function (index, option) {
        option.selected = $data.dishes().includes(option.value);
      });
    ko.applyBindingsToNode(select, {
      selectedOptions: this.dishes,
      valueAllowUnset: true,
      select2: {
        containerCssClass: "form-control",
        wrapperCssClass: "select2-container--form-control",
        dropdownCssClass: "dense-form-group__dropdown",
        allowClear: false,
        placeholder: "Не выбрано",
        templateSelection: this.dishesAndCategoriesTemplateSelection,
        templateResult: this.dishesAndCategoriesTemplateResult,
        matcher: this.dishesAndCategoriesMatcher,
        minimumResultsForSearch: 0,
      },
      visible: true,
      event: {
        change: (_, event) => {
          this.dishesAndCategoriesChange(event);
        },
      },
    });
  };

  this.dishes = ko.observableArray([]).extend({
    validation: {
      validator: (v) => {
        return v.length;
      },
      message: errors.dishes.required,
    },
  });
  this.dishesAndCategoriesMatcher = ({ term }, data) => {
    if (!term) {
      return data;
    }

    term = term.toLowerCase();

    if (!data.id) {
      return null;
    }

    if (data.id[0] != "c") {
      const categoryId = data.element.dataset.category;
      const category = this.root.directories.dishes
        .data()
        .find((c) => c.id === categoryId);
      if (!category) return null;

      const categoryName = category.name.toLowerCase();
      const dishText = data.text.toLowerCase();

      const match = categoryName.includes(term) || dishText.includes(term);
      return match ? data : null;
    } else {
      const category = this.root.directories.dishes
        .data()
        .find((c) => c.id === data.id.slice(1));
      if (!category) return null;

      const categoryName = category.name.toLowerCase();

      const match =
        categoryName.includes(term) ||
        category.dishes.some((d) => {
          return d.name.toLowerCase().includes(term);
        });
      return match ? data : null;
    }
  };

  this.dishesAndCategoriesTemplateSelection =
    window.select2templates.dishes.selection;
  this.dishesAndCategoriesTemplateResult =
    window.select2templates.dishes.result;

  this.dishesAndCategoriesChange = window.select2templates.dishes.onChange;

  this.minPrice = ko.observable("");

  // Участие в опросах
  this.surveyArray = ko.observableArray([0, 100]);
  this.surveyFrom = ko.observable(0);
  this.surveyTo = ko.observable(100);
  this.surveyArray.subscribe((v) => {
    this.surveyFrom(v[0]);
    this.surveyTo(v[1]);
  });

  this.codesArray = ko.observableArray([0, 50]);
  this.codesFrom = ko.observable(0);
  this.codesTo = ko.observable(50);
  this.codesArray.subscribe((v) => {
    this.codesFrom(v[0]);
    this.codesTo(v[1]);
  });

  // Тип заказа
  this.orderType = ko.observable(0);

  // Способ оформления
  this.orderSource = ko.observable(0);

  // Тип заказа, Способ оформления
  this.orderValue = ko.observable("");

  // Филиал
  this.filials = ko.observableArray([]).extend({
    validation: {
      validator: () => {
        return this.root.directories.filials.data().length;
      },
      message: errors.filials.empty,
    },
  });
  this.filialsOrderType = ko.observable("");
  this.filialsExclusionFlag = ko.observable(false);
  this.filials.subscribe((v) => {
    if (!v.length) this.filialsExclusionFlag(false);
  });

  this.complaint = ko.observable("1");

  this.promocodeUsed = ko.observable("1");
  this.pools = ko.observableArray([]);

  this.contactDataFilled = ko.observable("1");
  this.contactFields = ko.observableArray([]).extend({
    required: {
      message: "Обязательное поле",
    },
  });

  [
    "type",
    "criterion",
    "date",
    "period",
    "from",
    "to",
    "days",
    "time",
    "dishes",
    "minPrice",
    "surveyFrom",
    "surveyTo",
    "codesFrom",
    "codesTo",
    "orderType",
    "orderSource",
    "orderValue",
    "filials",
    "complaint",
    "promocodeUsed",
    "pools",
    "contactDataFilled",
    "contactFields",
  ].forEach((key) => {
    this[key].subscribe((v) => this.emit("change"));
  });

  this._setDefaultData = (conditionData) => {
    this.type(conditionData.condition_id);

    const value = conditionData.value;
    switch (conditionData.condition_id) {
      case "last-order-date":
        this.criterion(value.criterion);
        if (value.criterion == 1) {
          this.from(value.value[0]);
          this.to(value.value[1]);
        } else if (value.criterion == 2) {
          this.period(window.periodPickerConfig.getRange(value.value));
        } else {
          this.date(value.value);
        }
        break;
      case "complaint-in-the-order":
        this.criterion(value.criterion);
        if (value.criterion == 1) {
          this.from(value.value[0]);
          this.to(value.value[1]);
        } else if (value.criterion == 2) {
          this.period(window.periodPickerConfig.getRange(value.value));
        } else {
          this.date(value.value);
        }
        this.complaint(value.complaint + "");
        break;
      case "avg-year-count-orders":
      case "avg-month-count-orders":
      case "order-amount-from-client-by-year":
      case "order-amount-from-client-by-month":
      case "avg-check-customer-orders":
        this.from(value.value[0]);
        this.to(value.value[1]);
        break;
      case "main-orders-days":
        this.days(value.days.slice());
        break;
      case "favorite-order-time":
        this.time(value.time.slice());
        break;
      case "favorite-dish":
        const dishes = [];
        if (value.categories && Array.isArray(value.categories))
          value.categories.forEach((cat) => dishes.push("c" + cat));
        if (value.dishes && Array.isArray(value.dishes))
          value.dishes.forEach((dish) => dishes.push("d" + dish));
        this.dishes(dishes);
        this.minPrice(value.minPrice);
        break;
      case "polls-participation":
        this.surveyArray(value.value);
        break;
      case "codes-percent":
        this.codesArray(value.value);
      case "order-type":
        this.orderType(value.orderType);
        this.orderValue(value.value);
        break;
      case "source-type":
        this.orderSource(value.sourceType);
        this.orderValue(value.value);
        break;
      case "filial":
        this.filials(value.filials?.map(i => +i) || []);
        this.filialsOrderType(value.type);
        let withoutSelected = parseInt(value.withoutSelected);
        this.filialsExclusionFlag(!!withoutSelected);
        break;
      case "promocode":
        this.pools(value.pools || []);
        this.promocodeUsed(value.used == 1 ? "1" : "0");
        break;
      case "contact-data":
        this.contactDataFilled(value.type == 1 ? "1" : "2");
        this.contactFields(value.values || []);
    }
  };

  this.reset = () => {
    if (this.type()) this.touched(false);

    this.criterion("");
    this.from(0);
    this.to(0);
    this.date("");
    this.period("");
    this.days.removeAll();
    this.time.removeAll();
    this.dishes.removeAll();
    this.minPrice("");
    this.surveyArray([0, 100]);
    this.codesArray([0, 50]);
    this.orderType("");
    this.orderSource("");
    this.orderValue("");

    this.filials([]);
    this.filialsOrderType("");
    this.filialsExclusionFlag(false);

    this.complaint("1");

    this.pools([]);
    this.promocodeUsed("1");
    this.contactDataFilled("1");
    this.contactFields([]);
  };
  this.type.subscribe(() => this.reset());

  if (conditionData) {
    this._setDefaultData(conditionData);
  }

  this.dataModel = {
    type: {
      value: this.type,
      if: ko.observable(true),
    },
    criterion: {
      value: this.criterion,
      if: ko.pureComputed(() => {
        return (
          this.type() === "last-order-date" ||
          this.type() === "complaint-in-the-order"
        );
      }),
    },
    from: {
      value: this.from,
      if: ko.pureComputed(() => {
        if (
          (this.type() === "last-order-date" ||
            this.type() === "complaint-in-the-order") &&
          this.criterion() == 1
        )
          return true;
        if (
          [
            "avg-year-count-orders",
            "avg-month-count-orders",
            "order-amount-from-client-by-year",
            "order-amount-from-client-by-month",
            "avg-check-customer-orders",
          ].includes(this.type())
        )
          return true;
      }),
    },
    to: {
      value: this.to,
      if: ko.pureComputed(() => {
        if (
          (this.type() === "last-order-date" ||
            this.type() === "complaint-in-the-order") &&
          this.criterion() == 1
        )
          return true;
        if (
          [
            "avg-year-count-orders",
            "avg-month-count-orders",
            "order-amount-from-client-by-year",
            "order-amount-from-client-by-month",
            "avg-check-customer-orders",
          ].includes(this.type())
        )
          return true;
      }),
    },
    period: {
      value: this.period,
      if: ko.pureComputed(() => {
        return (
          (this.type() === "last-order-date" ||
            this.type() === "complaint-in-the-order") &&
          this.criterion() == 2
        );
      }),
    },
    date: {
      value: this.date,
      if: ko.pureComputed(() => {
        return (
          (this.type() === "last-order-date" ||
            this.type() === "complaint-in-the-order") &&
          this.criterion() >= 3
        );
      }),
    },
    days: {
      value: this.days,
      if: ko.pureComputed(() => {
        return this.type() === "main-orders-days";
      }),
    },
    time: {
      value: this.time,
      if: ko.pureComputed(() => {
        return this.type() === "favorite-order-time";
      }),
    },
    dishes: {
      value: this.dishes,
      if: ko.pureComputed(() => {
        return this.type() === "favorite-dish";
      }),
    },
    minPrice: {
      value: this.minPrice,
      if: ko.pureComputed(() => {
        return this.type() === "favorite-dish";
      }),
    },
    surveyFrom: {
      value: this.surveyFrom,
      if: ko.pureComputed(() => {
        return this.type() === "polls-participation";
      }),
    },
    surveyTo: {
      value: this.surveyTo,
      if: ko.pureComputed(() => {
        return this.type() === "polls-participation";
      }),
    },
    codesFrom: {
      value: this.codesFrom,
      if: ko.pureComputed(() => {
        return this.type() === "codes-percent";
      }),
    },
    codesTo: {
      value: this.codesTo,
      if: ko.pureComputed(() => {
        return this.type() === "codes-percent";
      }),
    },
    orderType: {
      value: this.orderType,
      if: ko.pureComputed(() => {
        return this.type() === "order-type";
      }),
    },
    sourceType: {
      value: this.orderSource,
      if: ko.pureComputed(() => {
        return this.type() === "source-type";
      }),
    },
    orderValue: {
      value: this.orderValue,
      if: ko.pureComputed(() => {
        return ["order-type", "source-type"].includes(this.type());
      }),
    },
    filials: {
      value: this.filials,
      if: ko.pureComputed(() => {
        return this.type() === "filial";
      }),
    },
    filialsOrderType: {
      value: this.filialsOrderType,
      if: ko.pureComputed(() => {
        return this.type() === "filial";
      }),
    },
    filialsExclusionFlag: {
      value: this.filialsExclusionFlag,
      if: ko.pureComputed(() => {
        return this.type() === "filial";
      }),
    },
    complaint: {
      value: this.complaint,
      if: ko.pureComputed(() => {
        return this.type() === "complaint-in-the-order";
      }),
    },
    used: {
      value: this.promocodeUsed,
      if: ko.pureComputed(() => {
        return this.type() === "promocode";
      }),
    },
    pools: {
      value: this.pools,
      if: ko.pureComputed(() => {
        return this.type() === "promocode";
      }),
    },
    contactDataFilled: {
      value: this.contactDataFilled,
      if: ko.pureComputed(() => {
        return this.type() === "contact-data";
      }),
    },
    contactFields: {
      value: this.contactFields,
      if: ko.pureComputed(() => {
        return this.type() === "contact-data";
      }),
    },
  };

  this.currentModel = ko.pureComputed(() => {
    const model = Object.keys(this.dataModel)
      .filter((key) => {
        return this.dataModel[key].if();
      })
      .reduce((model, key) => {
        let value = this.dataModel[key].value;
        if (key == "period") {
          value = window.periodPickerConfig.getValue(value());
        }
        return {
          ...model,
          [key]: value,
        };
      }, {});
    return model;
  });

  this.model = ko.validatedObservable(this.currentModel(), {
    deep: true,
    live: true,
  });
  this.currentModel.subscribe((model) => {
    this.model(model);
  });

  this.getData = function () {
    const model = ko.toJS(this.currentModel);
    const data = {
      id: model.type,
    };
    [
      "criterion",
      "days",
      "time",
      "orderType",
      "sourceType",
      "filials",
      "complaint",
      "minPrice",
      "used",
      "pools",
    ].forEach((key) => {
      if (key in model) {
        data[key] = model[key];
      }
    });
    if ("orderValue" in model) {
      data.value = model.orderValue;
    }
    if ("period" in model) {
      data.value = model.period;
    }
    if ("date" in model) {
      data.value = model.date;
    }
    if ("from" in model) {
      data.value = [model.from, model.to];
    }
    if ("surveyFrom" in model) {
      data.value = [model.surveyFrom, model.surveyTo];
    }
    if ("codesFrom" in model) {
      data.value = [model.codesFrom, model.codesTo];
    }
    if ("dishes" in model) {
      let categories = [],
        dishes = [];
      model.dishes.forEach((dish) => {
        if (dish[0] === "c") categories.push(dish.slice(1));
        else dishes.push(dish.slice(1));
      });

      data.dishes = dishes;
      data.categories = categories;
    }
    if ("filialsExclusionFlag" in model) {
      data.withoutSelected = model.filialsExclusionFlag ? 1 : 0;
    }
    if ("filialsOrderType" in model) {
      data.type = model.filialsOrderType;
    }
    if ("contactDataFilled" in model) {
      data.type = model.contactDataFilled;
    }
    if ("contactFields" in model) {
      data.values = model.contactFields;
    }

    return data;
  };
}
