<template id="conditions-filters-days-template">
  <div class="condition-filter__filter">
    <div class="condition-filter__checkbox-list">
      <!--ko foreach: { data: $component.daysList } -->
      <div class="form-check condition-filter__checkbox" data-bind="let: {inputId: 'daysCheck_' + $component.id + '_' + $index() }">
        <input type="checkbox" class="form-check-input" name="daysCheck" data-bind="
                        value: $data.index,
                        checked: $parent.days,
                        attr: { id: inputId }">
        <label class="form-check-label" data-bind="text: $data.text,
                        attr: { 'for': inputId }">
        </label>

      </div>
      <!-- /ko -->
    </div>
  </div>
</template>

<template id="conditions-filters-dishes-template">
  <div class="condition-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Блюда/категории</label>

      <div class="" data-bind="descendantsComplete: function(el) { $data.renderDishesSelectBlock(el, $data) }">

        <div class="select2-wrapper  condition-value__select" , data-bind="css: {
                'is-invalid': $component.formControlErrorStateMatcher($data.dishes)
            }">
          <select multiple data-bind="visible: false" data-placeholder="Не выбрано">
          </select>
        </div>

      </div>
    </div>
  </div>

  <div class="condition-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Min цена, ₽</label>
      <div data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.minPrice
            }
        }">
      </div>
    </div>
  </div>
</template>

<template id="conditions-filters-income-template">
  <div class="condition-filter__range">
    <div data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.from
            }
        }">
    </div>
    <div class="conditions-list__separator">
      –
    </div>
    <div class="" data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.to,
            }
        }">
    </div>
  </div>

</template>

<template id="conditions-filters-last-order-date-template">
  <div class="conditions-filter__filter">
    <div class="form-group dense-form-group mr-4">
      <label class="form-label">Критерий</label>
      <div class="select2-wrapper">
        <select data-bind="value: $data.criterion,
                select2: {
                    wrapperCssClass: 'select2-container--form-control',
                    dropdownCssClass: 'dense-form-group__dropdown',
                    allowClear: false,
                    placeholder: 'Не выбрано'
                }">
          <option value="1">Дней прошло</option>
          <option value="2">Период</option>
          <option value="3">До</option>
          <option value="4">После</option>
          <option value="5">Конкретная дата</option>
        </select>
      </div>
    </div>
  </div>


  <!-- Дней прошло -->
  <!-- ko template: {
    foreach: templateIf($data.criterion() == 1, $data),
    afterAdd: fadeAfterAddFactory(200)
  } -->
  <div class="condition-filter__range">
    <div data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.from
            }
        }">
    </div>
    <div class="conditions-list__separator">
      –
    </div>
    <div class="" data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.to,
            }
        }">
    </div>
  </div>
  <!-- /ko -->
  <!-- /Дней прошло -->

  <!-- Период -->
  <!-- ko template: {
    foreach: templateIf($data.criterion() == 2, $data),
    afterAdd: fadeAfterAddFactory(200),
  } -->
  <div class="conditions-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Период</label>
      <div class="input-group date-input-group" data-bind="dateInputGroup">
        <!-- ko let: { autosizeInput: ko.observable(null) } -->
        <input class="form-control" placeholder="00.00.0000-00.00.0000" data-bind="value: $data.period,
                            autosizeInput: autosizeInput,
                            periodPicker,
                            periodPickerArrowPosition: { anchor: 'right', offset: -10 },
                            periodPickerRanges: $component.periodPickerRanges,
                            periodPickerSeparator: '-',
                            periodPickerApply: function() { autosizeInput().update(); }">
        <!-- /ko -->
        <i class="date-input-group__icon"></i>
      </div>
    </div>
  </div>
  <!-- /ko -->
  <!-- /Период -->

  <!-- Одна дата (до/после/конкретная дата) -->
  <!-- ko template: {
    foreach: templateIf($data.criterion() >= 3, $data),
    afterAdd: fadeAfterAddFactory(200),
  } -->
  <div class="conditions-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Дата</label>

      <div class="input-group date-input-group" data-bind="dateInputGroup">
        <!-- ko let: { autosizeInput: ko.observable(null) } -->
        <input class="form-control" data-bind="mask, maskPattern: '00.00.0000',
                           textInput:$data.date,
                           autosizeInput: autosizeInput,
                           periodPicker,
                           periodPickerArrowPosition: { anchor: 'right', offset: -12 },
                           periodPickerSeparator: '-',
                           periodPickerOpens: 'left',
                           periodPickerSingle: true,
                           css: {
                                'is-invalid': $component.formControlErrorStateMatcher($data.date)
                            }" placeholder="00.00.0000">
        <!-- /ko -->
        <i class="date-input-group__icon"></i>
      </div>
    </div>
  </div>
  <!-- /ko -->

</template>

<template id="conditions-filters-complaint-in-the-order-template">
  <div class="conditions-filter__filter">
    <div class="form-group dense-form-group mr-4">
      <label class="form-label">Заказ сделан</label>
      <div class="select2-wrapper">
        <select data-bind="value: $data.criterion,
                select2: {
                    wrapperCssClass: 'select2-container--form-control',
                    dropdownCssClass: 'dense-form-group__dropdown',
                    allowClear: false,
                    placeholder: 'Не выбрано'
                }">
          <option value="1">Дней прошло</option>
          <option value="2">Период</option>
          <option value="3">До</option>
          <option value="4">После</option>
          <option value="5">Конкретная дата</option>
        </select>
      </div>
    </div>
  </div>


  <!-- Дней прошло -->
  <!-- ko template: {
    foreach: templateIf($data.criterion() == 1, $data),
    afterAdd: fadeAfterAddFactory(200)
  } -->
  <div class="condition-filter__range">
    <div data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.from
            }
        }">
    </div>
    <div class="conditions-list__separator">
      –
    </div>
    <div class="" data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.to,
            }
        }">
    </div>
  </div>
  <!-- /ko -->
  <!-- /Дней прошло -->

  <!-- Период -->
  <!-- ko template: {
    foreach: templateIf($data.criterion() == 2, $data),
    afterAdd: fadeAfterAddFactory(200),
  } -->
  <div class="conditions-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Период</label>
      <div class="input-group date-input-group" data-bind="dateInputGroup">
        <!-- ko let: { autosizeInput: ko.observable(null) } -->
        <input class="form-control" placeholder="00.00.0000-00.00.0000" data-bind="value: $data.period,
                            autosizeInput: autosizeInput,
                            periodPicker,
                            periodPickerArrowPosition: { anchor: 'right', offset: -10 },
                            periodPickerRanges: $component.periodPickerRanges,
                            periodPickerSeparator: '-',
                            periodPickerApply: function() { autosizeInput().update(); }">
        <!-- /ko -->
        <i class="date-input-group__icon"></i>
      </div>
    </div>
  </div>
  <!-- /ko -->
  <!-- /Период -->

  <!-- Одна дата (до/после/конкретная дата) -->
  <!-- ko template: {
    foreach: templateIf($data.criterion() >= 3, $data),
    afterAdd: fadeAfterAddFactory(200),
  } -->
  <div class="conditions-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Дата</label>

      <div class="input-group date-input-group" data-bind="dateInputGroup">
        <!-- ko let: { autosizeInput: ko.observable(null) } -->
        <input class="form-control" data-bind="mask, maskPattern: '00.00.0000',
                           textInput:$data.date,
                           autosizeInput: autosizeInput,
                           periodPicker,
                           periodPickerArrowPosition: { anchor: 'right', offset: -12 },
                           periodPickerSeparator: '-',
                           periodPickerOpens: 'left',
                           periodPickerSingle: true,
                           css: {
                                'is-invalid': $component.formControlErrorStateMatcher($data.date)
                            }" placeholder="00.00.0000">
        <!-- /ko -->
        <i class="date-input-group__icon"></i>
      </div>
    </div>
  </div>
  <!-- /ko -->

  <div class="condition-filter__filter">
    <div class="form-group dense-form-group ml-4 mr-4">
      <label class="form-label">Наличие жалобы</label>

      <div class="select2-wrapper condition-value__select">
        <select data-bind="value: $data.complaint,
                select2: {
                    wrapperCssClass: 'select2-container--form-control',
                    placeholder: 'Не выбрано',
                    dropdownCssClass: 'dense-form-group__dropdown',
                    minimumResultsForSearch: 10, allowClear: false,
                }">
          <option value="1">Есть</option>
          <option value="0">Нет</option>

        </select>
      </div>
    </div>
  </div>

</template>

<template id="conditions-filters-method-template">
  <div class="condition-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Способ оформления</label>

      <div class="select2-wrapper condition-value__select">
        <select data-bind="value: $data.orderSource,
                select2: {
                    wrapperCssClass: 'select2-container--form-control',
                    placeholder: 'Не выбрано',
                    dropdownCssClass: 'dense-form-group__dropdown',
                    minimumResultsForSearch: 10, allowClear: false,
                },
                options: $component.directories.orderSources.data,
                optionsText: 'name',
                optionsValue: 'id'">
        </select>
      </div>
    </div>
  </div>

  <div class="condition-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Заказ сделан</label>

      <div class="select2-wrapper">
        <select data-bind="value: $data.orderValue, select2: {
                wrapperCssClass: 'select2-container--form-control',
                dropdownCssClass: 'dense-form-group__dropdown',
                minimumResultsForSearch: 10,
                allowClear: false, placeholder: 'Не выбрано' }">
          <option value="1">Хотя бы один раз</option>
          <option value="2">Не заказывал ни разу</option>
        </select>
      </div>
    </div>
  </div>

</template>

<template id="conditions-filters-orders-per-month-template">
  <div class="condition-filter__range">
    <div data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.from
            }
        }">
    </div>
    <div class="conditions-list__separator">
      –
    </div>
    <div class="" data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.to,
            }
        }">
    </div>
  </div>

</template>

<template id="conditions-filters-survey-template">
  <div class="condition-filter__range">
    <div data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.surveyFrom
            }
        }">
    </div>
    <div class="conditions-list__separator">
      –
    </div>
    <div class="" data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.surveyTo,
            }
        }">
    </div>
  </div>

</template>

<template id="conditions-filters-codes-template">
  <div class="condition-filter__range">
    <div data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.codesFrom
            }
        }">
    </div>
    <div class="conditions-list__separator">
      –
    </div>
    <div class="" data-bind="component: {
            name: 'conditions-filters-counter',
            params: {
                value: $data.codesTo,
            }
        }">
    </div>
  </div>

</template>

<template id="conditions-filters-time-template">
  <div class="condition-filter__filter">
    <div class="condition-filter__checkbox-list">
      <!--ko foreach: { data: $component.timeList} -->
      <div class="form-check condition-filter__checkbox" data-bind="let: { inputId: 'timeCheck_' + $component.id + '_' +$index() }">
        <input type="checkbox" class="form-check-input" name='timeCheck' , data-bind="attr: {
                            id: inputId
                        },
                        value: $data.index,
                        checked: $parent.time">
        <label class="form-check-label" data-bind="text: $data.text,
                        attr: { 'for': inputId }">
        </label>

      </div>
      <!-- /ko -->
    </div>
  </div>

</template>

<template id="conditions-filters-type-template">
  <div class="condition-filter__filter">
    <div class="form-group dense-form-group">

      <label class="form-label">Тип заказа</label>

      <div class='select2-wrapper condition-value__select'>
        <select data-bind="value: $data.orderType,
                select2: {
                    wrapperCssClass: 'select2-container--form-control',
                    placeholder: 'Не выбрано',
                    dropdownCssClass: 'dense-form-group__dropdown',
                    minimumResultsForSearch: 10,
                },
                options: $component.directories.orderTypes.data,
                optionsText: 'name',
                optionsValue: 'id'" data-placeholder="Не выбрано">
        </select>
      </div>
    </div>
  </div>

  <div class="condition-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Заказ</label>


      <div class='select2-wrapper'>
        <select data-bind="value: $data.orderValue,

                                    select2: {
                                        wrapperCssClass: 'select2-container--form-control',
                                        dropdownCssClass: 'dense-form-group__dropdown',
                                        placeholder: 'Не выбрано',
                                        minimumResultsForSearch: 10,
                                    }">
          <option value="1">Хотя бы один раз</option>
          <option value="2">Не заказывал ни разу</option>
        </select>
      </div>
    </div>
  </div>

</template>

<template id="conditions-filters-filial-template">
  <!-- ko template: {
      foreach: templateIf($component.directories.filials.loaded(), $data),
      afterAdd: fadeAfterAddFactory(200)
    } -->
  <div class="condition-filter__filter">
    <div class="form-group dense-form-group">

      <label class="form-label">Филиал</label>

      <div class='select2-wrapper condition-value__select'>
        <fc-select
          class="categorized"
          params="
            inline: true,
            options: $component.directories.filials.data,
            value: $data.filials,
            placeholder: 'Все',
            multiple: true,
            blockSelectedGroup: true,
            disableFoldersSelect: true,
            optionTextProp: 'name',
          "
        ></fc-select>
      </div>
    </div>
  </div>
  <!-- /ko -->

  <div class="condition-filter__filter">
    <div class="form-check condition-filter__checkbox" data-bind="let: { inputId: 'filial-filter-exclusion-flag_' + $component.id + '_' + $index()  }">
      <input type="checkbox" class="form-check-input" data-bind="
          checked: $data.filialsExclusionFlag,
          attr: { id: inputId }">
      <label class="form-check-label" data-bind="attr: { 'for': inputId }">
        Все, кроме<br>выбранных
      </label>
    </div>
  </div>

  <div class="condition-filter__filter">
    <div class="form-group dense-form-group">
      <label class="form-label">Заказ</label>


      <div class='select2-wrapper'>
        <select data-bind="value: $data.filialsOrderType,
          select2: {
              wrapperCssClass: 'select2-container--form-control',
              dropdownCssClass: 'dense-form-group__dropdown',
              placeholder: 'Не выбрано',
              minimumResultsForSearch: 10,
          }">
          <option value="1">Хотя бы один раз</option>
          <option value="2">Не заказывал ни разу</option>
        </select>
      </div>
    </div>
  </div>

</template>

<template id="conditions-filters-promocode-template">
  <div class="conditions-filter__filter" >
    <div class="form-group dense-form-group mr-4">
      <label class="form-label">Промокод</label>
      <div class="select2-wrapper">
        <select data-bind="value: $data.promocodeUsed,
                select2: {
                    wrapperCssClass: 'select2-container--form-control',
                    dropdownCssClass: 'dense-form-group__dropdown',
                    allowClear: false,
                    placeholder: 'Не выбрано'
                }">
          <option value="1">Использован</option>
          <option value="0">Не использован</option>
        </select>
      </div>
    </div>


  </div>

  <!-- ko template: {
      foreach: templateIf($component.directories.pools.loaded(), $data),
      afterAdd: fadeAfterAddFactory(200)
    } -->
  <div class="conditions-filter__filter">
    <div class="form-group dense-form-group">

      <label class="form-label">Пул купонов</label>

      <div class='select2-wrapper condition-value__select'>
        <select data-bind="selectedOptions: $data.pools,
          valueAllowUnset: true,
          select2: {
              wrapperCssClass: 'select2-container--form-control',
              placeholder: 'Любой',
              dropdownCssClass: 'dense-form-group__dropdown',
              minimumResultsForSearch: 10,
          },
          options: $component.directories.pools.data,
          optionsText: 'name',
          optionsValue: 'id'" multiple>
        </select>
      </div>
    </div>
  </div>
  <!-- /ko -->

</template>

<template id="conditions-list-filters-template">

  <section class="condition-filters" data-bind="css: {
    'condition-filters--empty': conditions().length === 0
  }">
    <!-- ko foreach: {
        data: conditions,
        afterAdd: fadeAfterAddFactory(200)
      } -->
    <div class="condition-filters__item condition-filter" data-bind="class: 'condition-filter--' + $data.type()">
      <div class="condition-filter__content">
        <div class="condition-filter__filters">

          <div class="condition-filter__filter">
            <div class="form-group dense-form-group">

              <label class="form-label">Условие</label>

              <div class="select2-wrapper" data-bind="css: {
                      'is-invalid': $component.formControlErrorStateMatcher($data.type),
                      'is-valid': $component.formControlSuccessStateMatcher($data.type),
                    }">
                <select data-bind="value: $data.type,
                valueAllowUnset: true,

                    lazySelect2: {
                      dropdownCssClass: 'dense-form-group__dropdown',
                      wrapperCssClass: 'select2-container--form-control',
                      minimumResultsForSearch: 0,
                      allowClear: true,
                      placeholder: 'Не выбрано'
                    }">
                  <option></option>
                  <!-- ko foreach: $component.conditionTypes -->
                  <option data-bind="value: $data.id, text: $data.text"></option>
                  <!-- /ko -->
                </select>
              </div>
            </div>
          </div>

          <!-- ko template: {
                  name: 'conditions-filters-last-order-date-template',
                  foreach: templateIf(type()=='last-order-date', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
              name: 'conditions-filters-complaint-in-the-order-template',
              foreach: templateIf(type()=='complaint-in-the-order', $data),
              afterAdd: fadeAfterAddFactory(200, 200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-orders-per-month-template',
                  foreach: templateIf(type()=='avg-year-count-orders', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-orders-per-month-template',
                  foreach: templateIf(type()=='avg-month-count-orders', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-income-template',
                  foreach: templateIf(type()=='order-amount-from-client-by-year' || type()=='order-amount-from-client-by-month', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-income-template',
                  foreach: templateIf(type()=='avg-check-customer-orders', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-days-template',
                  foreach: templateIf(type()=='main-orders-days', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-time-template',
                  foreach: templateIf(type()=='favorite-order-time', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-dishes-template',
                  foreach: templateIf(type()=='favorite-dish', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-survey-template',
                  foreach: templateIf(type()=='polls-participation', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-codes-template',
                  foreach: templateIf(type()=='codes-percent', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-type-template',
                  foreach: templateIf(type()=='order-type', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-method-template',
                  foreach: templateIf(type()=='source-type', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-filial-template',
                  foreach: templateIf(type()=='filial', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->

          <!-- ko template: {
                  name: 'conditions-filters-promocode-template',
                  foreach: templateIf(type()=='promocode', $data),
                  afterAdd: fadeAfterAddFactory(200, 200),
                  beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <!-- /ko -->
        </div>

        <!-- ko template: {
            foreach: templateIf((touched() || $component.showErrors()) && model.errors().length, $data),
            afterAdd: fadeAfterAddFactory(200, 200),
            beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
        <div class="form-error" data-bind="html: model.errors()[0]"></div>
        <!-- /ko -->
      </div>

      <div class="condition-filter__actions">
        <!-- ko if: $index() === $component.conditions().length - 1 -->
        <button class="btn btn-link btn-link-success" data-bind="click: function() {
        touched(true);
        $component.addConditionFilter();
      }, disable: touched() && !$component.canAddConditionFilter()">Добавить</button>
        <!-- /ko -->


        <button type="submit" class="btn btn-link-error btn-link ml-4" data-bind="click: $component.removeCondition">Удалить
        </button>
      </div>




    </div>
    <!-- /ko -->
    </div>

</template>
