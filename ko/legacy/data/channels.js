export const email = {
  id: 'email',
  name: 'Email',
  fullName: 'Email',
  isHTML: true,
};

export const sms = {
  id: 'sms',
  name: 'SMS',
  fullName: 'SMS',
  isHTML: false,
};

export const telegram = {
  id: 'telegram',
  name: 'Telegram',
  fullName: 'Telegram',
  isHTML: false,
};

export const viber = {
  id: 'viber',
  name: 'Viber',
  fullName: 'Viber',
  isHTML: false,
};

export const push = {
  id: 'push',
  name: 'Push-уведомления',
  fullName: 'Push-уведомления (FCM)',
  isHTML: true,
};

export const list = [email, sms, telegram, viber, push];

export const channelTypes = {
  'Email': 'email',
  'SMS': 'sms',
  'Viber': 'viber',
  'Telegram': 'telegram',
  'Push': 'push'
}

export const channels = {
  'email': email,
  'sms': sms,
  'viber': viber,
  'telegram': telegram,
  'push': push
}

export function getChannel(channelType) {
  return channels[channelType];
}

export function getChannelType(channelData) {
  if (!channelData || !channelData.name) return '';
  return channelTypes[channelData.name] || '';
}

export function getChannelName(channelType) {
  let channel = channels[channelType];
  return channel ? channel.name : '';
}
