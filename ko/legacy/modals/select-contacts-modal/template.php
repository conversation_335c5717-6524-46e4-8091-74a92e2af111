<?= $this->render('../../components/birthday-filter/template.php'); ?>

<template id="select-contacts-modal-dialog-template">
  <div class="modal-dialog modal-dialog-centered modal-dialog-md select-contacts-modal-dialog mailings__select-clients-modal-dialog 123" data-bind="component: {
      name: 'select-contacts-modal',
      params: {
        data: data,
        modalElement: modalElement,
        close: close
      }
    }" role="document"></div>
</template>

<template id="select-contacts-modal-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  <div class="modal-content select-contacts-modal" data-bind="
    style: {
      visible: initializing() ? 'hidden': '',
    }, let: {
      $modal: $component
    }">
    <div class="modal-header">
      <h2 class="modal-title">Выбрать контакты</h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: function() { close() }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body">
      <div class="form-group">
        <label class="form-label">Фамилия, телефон или email</label>

        <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: filters.query().length">
          <input class="form-control" data-bind="
                  textInput: filters.query" maxlength="50">
          <div class="chars-counter__value"></div>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">Пол</label>

        <div data-bind="component: {
            name: 'f-select',
            params: {
              selectedOptions: $modal.filters.genders
            }
          }">
          <select data-bind="lazySelect2: {
            dropdownCssClass: 'dense-form-group__dropdown f-select__dropdown',
            minimumResultsForSearch: 0,
          }">
            <option></option>
            <!-- ko foreach: [
              { id: '1', text: 'Мужской' },
              { id: '2', text: 'Женский' },
              { id: '3', text: 'Не указан' },
            ] -->
            <!-- ko ifnot: $modal.filters.genders().includes($data.id) -->
            <option data-bind="value: $data.id, text: $data.text"></option>
            <!-- /ko -->
            <!-- /ko -->
          </select>
        </div>
      </div>


      <!-- ko component: {
        name: 'birthday-filter',
        params: {
          isDense: false,
          model: $modal.birthdayFilter,
          showErrors: $modal.isSubmitted
        }
      } -->
      <!-- /ko -->


      <div class="form-group">
        <label class="form-label">Теги</label>

        <div data-bind="component: {
            name: 'f-select',
            params: {
              selectedOptions: $modal.filters.tags,
              selectionTemplate: function(data) {
                var tmp = $('<span>').text(data.text);
                if (data.element.hasAttribute('data-with-condition')) {
                  tmp.addClass('f-color-success');
                }
                return tmp.get(0);
              }
            }
          }">
          <!-- ko component: {
              name: 'tags-select-single',
              params: {
                directory: $modal.tagsDirectory,
                element: ko.observable(null),
                hiddenOptions: $modal.filters.tags,
                select2Options: {
                  dropdownCssClass: 'dense-form-group__dropdown tags-dropdown',
                  containerCss: { 'width': '0px', },
                }
              }
            } -->
          <!-- /ko -->
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">Данные контакта
          <button class="btn-question" data-bind="tooltip" title="Данные контакта"></button>
        </label>
        <div class="hat-radio-group form-control">
          <div class="hat-radio-group__radio">
            <input type="radio" class="hat-radio-group__radio-input" name="fields" id="filled-in" data-bind="value: 1, checked: $modal.filters.filled">
            <label for="filled-in" class="hat-radio-group__radio-label">
              <span class="hat-radio-group__radio-indicator"></span>
              Заполнены
            </label>
          </div>

          <div class="hat-radio-group__radio">
            <input type="radio" class="hat-radio-group__radio-input" name="fields" id="not-filled-in" data-bind="value: 0, checked: $modal.filters.filled">
            <label for="not-filled-in" class="hat-radio-group__radio-label">
              <span class="hat-radio-group__radio-indicator"></span>
              Не заполнены
            </label>
          </div>
        </div>

        <!-- ko if: $modal.clientFields.loaded() -->
        <div class="select2-wrapper mt-3">
          <select multiple data-bind="
            selectedOptions: $modal.filters.fields,
            lazySelect2: {
              //templateSelection: optionTemplate,
              //templateResult: optionTemplate,
              containerCssClass: 'form-control',
              wrapperCssClass:
                'select2-container--form-control',
              minimumResultsForSearch: 0,
              placeholder: 'Выберите поля'
            }"  data-placeholder="Выберите поля">
            <optgroup label="Системные">
              <!-- ko foreach: {data: $modal.clientFields.system, as: 'field'} -->
              <option data-bind="text: field.text, value: field.id"></option>
              <!-- /ko -->
            </optgroup>
            <optgroup label="Пользовательские">
              <!-- ko foreach: {data: $modal.clientFields.additional, as: 'field'} -->
              <option data-bind="text: field.text, value: field.id"></option>
              <!-- /ko -->
            </optgroup>
          </select>
        </div>
        <!-- /ko -->
      </div>
    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="btn btn-link" data-bind="click: function() { close() }">
          Отменить
        </button>

        <!-- ko ifnot: isAdding -->
        <button type="submit" class="btn btn-success" data-bind="click: submit, text: buttonText">

        </button>
        <!-- /ko -->

        <!-- ko if: isAdding -->
        <div data-bind="component: {
          name: 'progress-bar',
          params: { value: addingProgress(), theme: 'success' } }">
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>


</template>
