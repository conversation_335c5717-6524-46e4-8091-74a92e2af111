import { BirthdayFilterModel } from 'Legacy/components/birthday-filter';
import { getClientFields } from 'Legacy/utils/client-fields';

const STATUS_STARTING = 1;
const STATUS_PROCESSING = 2;
const STATUS_FAILED = 3;
const STATUS_DONE = 4;

class ViewModel {
  constructor(params) {
    this.close = params.close;
    console.log('select-contacts-modal', params);
    this.clientFields = getClientFields();
    this.isSubmitted = ko.observable(false);

    this.initializing = ko.observable(true);
    this.onSubmit = params.data.onSubmit;
    this.formatStats = params.data.formatStats;

    this.isContactsCountLoading = ko.observable(false);
    this.contactsCount = ko.observable('');
    this.statId = ko.observable(null);

    this.isAdding = ko.observable(false);
    this.addingProgress = ko.observable(0);
    this.statsRequests = [];

    this.filters = {
      query: ko.observable(''),
      genders: ko.observableArray([]),
      tags: ko.observableArray([]),
      filled: ko.observable(1),
      fields: ko.observableArray([]),
    };

    this.birthdayFilter = new BirthdayFilterModel({
      format: 'YYYY-MM-DD',
    });

    this.tagsDirectory = params.data.tagsDirectory;
    this.statsUrl =
      params.data.statsUrl ||
      `${APIConfig.baseApiUrlPath}mailing-lists/get-link-contacts-stat?access-token=${APIConfig.apiKey}`;

    const getCount = utils.debounce(() => {
      this.getCount();
    }, 400);

    this.isFiltersEmpty = ko.pureComputed(() => {
      if (this.filters.query().length >= 2) return false;
      if (this.filters.genders().length) return false;
      if (this.filters.tags().length) return false;
      if (this.birthdayFilter.type()) return false;
      if (this.filters.fields().length) return false;

      return true;
    });

    this.buttonText = ko.pureComputed(() => {
      if (this.isFiltersEmpty()) return 'Добавить все';
      if (this.isContactsCountLoading()) return 'Добавить ... контактов';

      return `Добавить ${this.contactsCount()} ${utils.declOfNum(
        this.contactsCount(),
        ['контакт', 'контакта', 'контактов'],
      )}`;
    });

    [
      this.filters.query,
      this.filters.genders,
      this.filters.tags,
      this.birthdayFilter,
      this.filters.filled,
      this.filters.fields,
    ].forEach((f) => f.subscribe((_) => getCount()));
  }

  getSearchParams() {
    let params = {
      contact: this.filters.query(),
      tagsOperation: 1,
      tags: this.filters.tags(),
      gender: this.filters.genders(),
      birthday: this.birthdayFilter.getParams(),
    };

    if (this.filters.fields().length) {
      params.records = {
        filled: this.filters.filled(),
        fields: this.filters.fields(),
      };
    }

    return params;
  }

  getCount() {
    if (
      !this.filters.tags().length &&
      !this.filters.genders().length &&
      !this.filters.fields().length &&
      !this.birthdayFilter.hasValue() &&
      this.filters.query().length < 2
    )
      return;

    this.isContactsCountLoading(true);
    return new Promise((res) => {
      let params = this.getSearchParams();
      $.ajax({
        url: `/foquz/foquz-contact/get-contacts-count`,
        data: {
          page: -1,
          advanced: {
            contact: params.contact,
            tags: params.tags,
            birthday: params.birthday,
            gender: params.gender,
          },
          records: params.records,
        },
        method: 'GET',
        success: (data) => {
          this.contactsCount(data.data);
          this.isContactsCountLoading(false);
        },
        error: (response) => {
          this.isContactsCountLoading(false);
          console.error(response.responseJSON);
        },
      });
    });
  }

  abortAllRequests() {
    this.statsRequests.forEach((xhr) => xhr.abort());
    this.statsRequests = [];
  }

  getStatRequest() {
    return new Promise((res, rej) => {
      const xhr = $.ajax({
        method: 'GET',
        url: this.statsUrl,
        data: {
          stat_id: this.statId(),
        },
        success: (data) => {
          if (!this.isAdding()) {
            rej();
            return;
          }

          if (typeof this.formatStats == 'function') {
            data = this.formatStats(data);
          }

          if (data.status != 'ok') {
            rej();
            return;
          }

          const stat = data.stat;
          const status = stat.status;

          if (status == STATUS_FAILED) {
            rej();
            return;
          }

          if (status == STATUS_DONE) {
            res({
              progress: 100,
            });
            return;
          }

          if (!('total' in stat)) {
            rej();
            return;
          }

          // if (stat.total == 0) {
          //   res({
          //     progress: 100,
          //   });
          //   return;
          // }

          // при загрузке ВСЕХ КОНТАКТОВ в поле total несколько первых раз может быть 0
          // поэтому нельзя прерывать опрашивание
          if (stat.total == 0) {
            res({
              progress: 0,
            });
            return;
          }

          let progress = Math.min(
            ((stat.inserted + stat.duplicated) * 100) / stat.total,
            100,
          );

          res({
            progress: progress,
          });
        },
        error: (response) => {
          rej();
        },
      });
      this.statsRequests.push(xhr);
    });
  }

  getStat() {
    let intervalId = setInterval(() => {
      this.getStatRequest()
        .then((res) => {
          if (res.progress >= 100) {
            this.addingProgress(100);
            this.isAdding(false);
            clearInterval(intervalId);
            this.close(true);
          } else {
            if (this.addingProgress() < res.progress)
              this.addingProgress(res.progress);
          }
        })
        .catch(() => {
          if (this.isAdding() == false) return;

          this.isAdding(false);
          clearInterval(intervalId);
          console.error('Ошибка загрузки данных');

          this.abortAllRequests();
        });
    }, 800);

    // setTimeout(() => {
    //   if (this.isAdding()) {
    //     this.isAdding(false);
    //     this.abortAllRequests();

    //     clearInterval(intervalId);
    //     alert('Превышено время загрузки данных');
    //   }
    // }, 60000);
  }

  submit() {
    this.isSubmitted(true);

    if (!this.birthdayFilter.isValid()) return false;

    this.isAdding(true);
    this.addingProgress(0);

    this.onSubmit(this.getSearchParams()).then((statId) => {
      this.statId(statId);
      this.getStat();
    });
  }

  onInit() {
    this.initializing(false);
  }
}

ko.components.register('select-contacts-modal', {
  viewModel: ViewModel,
  template: {
    element: 'select-contacts-modal-template',
  },
});
