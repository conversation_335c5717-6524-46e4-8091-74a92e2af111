class ViewModel {
  constructor(params) {
    this.close = params.close;

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted,
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted,
    );

    let resource = params.data.resource;
    this.resource = resource;

    this.serverErrors = {
      name: ko.observable(''),
      domain: ko.observable(''),
    };

    this.name = ko.observable('').extend({
      required: {
        message: 'Обязательное поле',
      },
      validation: {
        validator: () => !this.serverErrors.name(),
        message: () => this.serverErrors.name(),
      },
    });
    this.domain = ko.observable('').extend({
      required: {
        message: 'Обязательное поле',
      },
      validation: [
        {
          validator: (value) => {
            return validator.isFQDN(value);
          },
          message: 'Домен должен быть указан в формате "foquz.ru"',
        },
        {
          validator: () => !this.serverErrors.domain(),
          message: () => this.serverErrors.domain(),
        },
      ],
    });
    this.goodsUrl = ko.observable('').extend({
      validation: {
        validator: (value) => {
          if (!value) return true;
          return validator.isURL(value);
        },
        message: 'Неверный формат',
      },
    });
    this.actionsUrl = ko.observable('').extend({
      validation: {
        validator: (value) => {
          if (!value) return true;
          return validator.isURL(value);
        },
        message: 'Неверный формат',
      },
    });
    this.endpointId = ko.observable('');

    this.name.subscribe((_) => this.serverErrors.name(''));
    this.domain.subscribe((_) => this.serverErrors.domain(''));

    if (this.resource) {
      this.name(this.resource.name);
      this.domain(this.resource.domain);
      this.goodsUrl(this.resource.goodsUrl);
      this.actionsUrl(this.resource.actionsUrl);
      this.endpointId(this.resource.endpointId);
    }

    this.validatedObject = ko.validatedObservable(
      {
        name: this.name,
        domain: this.domain,
        goodsUrl: this.goodsUrl,
        actionsUrl: this.actionsUrl,
      },
      { deep: true, live: true },
    );
  }

  submit() {
    this.isSubmitted(true);

    if (!this.validatedObject.isValid()) return;

    let params = {
      title: this.name(),
      domen: this.domain(),
      products_url: this.goodsUrl(),
      discounts_url: this.actionsUrl(),
    };

    let url = this.resource
      ? `${APIConfig.baseApiUrlPath}external-resources/update?id=${this.resource.id}&access-token=${APIConfig.apiKey}`
      : `${APIConfig.baseApiUrlPath}external-resources/create?access-token=${APIConfig.apiKey}`;

    $.ajax({
      url: url,
      method: this.resource ? 'PUT' : 'POST',
      data: params,
      success: (data) => {
        this.close(data.resource);

        if (!this.resource) {
          window.modalOpens.push({
            dialogTemplateName: 'id-modal-dialog-template',
            data: {
              id: data.resource.endpoint_id,
            },
          });
        }
      },
      error: (response) => {
        let res = response.responseJSON;
        if (res.errors) {
          if (res.errors.title) {
            this.serverErrors.name(res.errors.title[0]);
          }
          if (res.errors.domen) {
            this.serverErrors.domain(res.errors.domen[0]);
          }
        }
      },
    });
  }
}

ko.components.register('resource-modal', {
  viewModel: ViewModel,
  template: {
    element: 'resource-modal-template',
  },
});
