<template id="resource-modal-dialog-template">
  <div class="modal-dialog modal-dialog-centered modal-dialog-md resource-modal-dialog" data-bind="component: {
    name: 'resource-modal',
    params: {
      data: data,
      modalElement: modalElement,
      close: close
    }
  }" role="document"></div>
</template>

<template id="resource-modal-template">

  <div class="modal-content resource-modal">
    <div class="modal-header">
      <h2 class="modal-title" data-bind="text: resource ? 'Редактировать ресурс' : 'Добавить ресурс'"></h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: function() { close() }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body">
      <div class="f-fs-1 f-color-service mb-4">
        <!-- ko ifnot: resource -->
        Для передачи событий с сайта используйте идентификатор endpoint_id &ndash; строка guid &ndash; <span class="bold">генерируется при сохранении данных настроек</span>
        <!-- /ko -->

        <!-- ko if: resource -->
        Для передачи событий с сайта используйте идентификатор endpoint_id &ndash; <span class="bold" data-bind="text: resource.endpointId"></span>
        <!-- /ko -->
      </div>

      <div class="form-group">
        <label class="form-label">Название ресурса</label>
        <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: name().length">
          <input class="form-control" data-bind="textInput: name,
          css: {
            'is-invalid': formControlErrorStateMatcher(name),
            'is-valid': formControlSuccessStateMatcher(name),
          }" maxlength="40">
          <div class="chars-counter__value"></div>
        </div>
        <!-- ko template: {
          foreach: formControlErrorStateMatcher(name),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200),
        } -->
        <div class="form-error" data-bind="text: $parent.name.error()"></div>
        <!-- /ko -->
      </div>

      <div class="form-group">
        <label class="form-label">Домен</label>
        <input type="text" class="form-control" data-bind="textInput: domain,
        css: {
            'is-invalid': formControlErrorStateMatcher(domain),
            'is-valid': formControlSuccessStateMatcher(domain),
          }" placeholder="foquz.ru">

        <!-- ko template: {
          foreach: formControlErrorStateMatcher(domain),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200),
        } -->
        <div class="form-error" data-bind="text: $parent.domain.error()"></div>
        <!-- /ko -->
      </div>

      <div class="form-group">
        <label class="form-label d-flex justify-content-between align-items-center">

          <span>
            URL для товаров
          </span>

          <span class="f-fs-1 f-color-service font-weight-normal">необязательное</span>

        </label>
        <input type="text" class="form-control" data-bind="textInput: goodsUrl, css: {
            'is-invalid': formControlErrorStateMatcher(goodsUrl),
            'is-valid': formControlSuccessStateMatcher(goodsUrl),
          }" placeholder="https://">

        <!-- ko template: {
          foreach: formControlErrorStateMatcher(goodsUrl),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200),
        } -->
        <div class="form-error" data-bind="text: $parent.goodsUrl.error()"></div>
        <!-- /ko -->
      </div>

      <div class="form-group">
        <label class="form-label d-flex justify-content-between align-items-center">

          <span>
            URL для акций
          </span>

          <span class="f-fs-1 f-color-service font-weight-normal">необязательное</span>

        </label>
        <input type="text" class="form-control" data-bind="textInput: actionsUrl, css: {
            'is-invalid': formControlErrorStateMatcher(actionsUrl),
            'is-valid': formControlSuccessStateMatcher(actionsUrl),
          }" placeholder="https://">

        <!-- ko template: {
          foreach: formControlErrorStateMatcher(actionsUrl),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200),
        } -->
        <div class="form-error" data-bind="text: $parent.actionsUrl.error()"></div>
        <!-- /ko -->
      </div>


    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="btn btn-link" data-bind="click: function() { close() }">
          Отменить
        </button>

        <button type="button" class="btn btn-success" data-bind="click: submit">
          Сохранить
        </button>
      </div>
    </div>
  </div>

</template>

<template id="id-modal-dialog-template">
  <div class="modal-dialog modal-dialog-centered modal-dialog-md id-modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Идентификатор сгенерирован</h2>

        <button type="button" class="close" aria-label="Close" data-bind="click: close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" data-bind="log">
        <div class="form-group">
          <label class="form-label">Идентификатор endpoint_id</label>
          <div class="f-input-group">
            <div class="f-input-group__append cursor-pointer" data-bind="tooltip, copyToClipboard: data.id" title="Скопировать в буфер">
              <span class="f-icon f-icon--copy">
                <svg>
                  <use href="#copy-icon"></use>
                </svg>
              </span>
            </div>
            <input type="text" class="form-control" data-bind="value: data.id" readonly onclick="this.select()">
          </div>

        </div>
      </div>
      <div class="modal-footer">
        <div class="d-flex justify-content-end">
          <button class="f-btn f-btn-link" type="button" data-bind="click: close">Закрыть</button>
        </div>
      </div>
    </div>
  </div>
</template>
