# Модальное окно Переместить объект в папку

## Данные

* `title` &lt;string&gt; (по умолчанию `Переместить объект в папку`)
* `foldersDirectory` &lt;Directory&gt;
* `onSubmit` &lt;Function.&lt;Promise&gt;&gt;
* `blocked` &lt;number[]&gt;

## Имя шаблона

```
move-to-folder-modal-dialog-template
```

## Код

```
modalOpens.push({
  dialogTemplateName: 'move-to-folder-modal-dialog-template',
  data: {
    title: 'Переместить объект в папку',
    foldersDirectory: new Directory('folders'),
    onSubmit: function(data) {
      // data - { parentFolderId }

      // При удачном добавлении
      return Promise.resolve();

      // При ошибках
      return Promise.reject(errors);
    }
  }
})
```
