<template id="folder-row-template">
  <!-- ko if: isFolderVisible(folder) -->
  <div class="folder-row" data-bind="let: { isBlocked: isFolderBlocked(folder) }">
    <div class="folders-list__item" data-bind="
              click: function() { !isBlocked ? parentFolder(folder.id) : null; },
              css: {
                'folders-list__item--active': parentFolder() == folder.id,
                'folders-list__item--blocked': isBlocked,
              },
              style: { 'padding-left': (level * 12) + 'px' },
            ">
      <span class="f-icon f-icon--folder f-icon-light mr-2">
        <svg>
          <use href="#folder-icon"></use>
        </svg>
      </span>
      <span data-bind="text: folder.name,"></span>
    </div>

    <div>
      <!-- ko foreach: folder.items -->
      <!-- ko template: {
              name: 'folder-row-template',
              data: {
                folder: $data,
                parentFolder: $parent.parentFolder,
                level: $parent.level + 1,
                isFolderVisible: $parent.isFolderVisible,
                isFolderBlocked: $parent.isFolderBlocked
              }
            } -->
      <!-- /ko -->
      <!-- /ko -->
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="move-to-folder-modal-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title" data-bind="text: title"></h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body" data-bind=" css: {
        'initializing': initializing
      }, let: {
          rootFolder: {
            name: _t('Главная страница'),
            id: 0,
            items: $component.foldersDirectory.sortByField('name')
          }
        }">
      <div class="form-group">
        <div class="f-input-group">
          <div class="f-input-group__prepend">
            <span class="f-icon f-icon--search">
              <svg>
                <use href="#search-icon"></use>
              </svg>
            </span>
          </div>

          <!-- ko if: term -->
          <div class="f-input-group__append cursor-pointer"
            data-bind="click: function() { term(''); }">
            <span class="f-icon f-icon--times f-icon-sm f-icon-danger">
              <svg>
                <use href="#times-icon"></use>
              </svg>
            </span>
          </div>
          <!-- /ko -->

          <input type="text" class="form-control" data-bind="textInput: term, attr: {
            placeholder: _t('Поиск по названию')
          }"  />
        </div>
      </div>


      <div class="folders-list"
        data-bind="fScrollbar: { gradient: true }">


          <!-- ko ifnot: $component.isFolderVisible(rootFolder) -->
            <div class="text-center f-color-service" data-bind="text: _t('Папки с таким названием в проекте нет')">

            </div>
          <!-- /ko -->

          <!-- ko if: $component.isFolderVisible(rootFolder) -->
          <!-- ko template: {
            name: 'folder-row-template',
            data: {
              folder: rootFolder,
              parentFolder: $component.parentFolder,
              level: 0,
              isFolderVisible: $component.isFolderVisible.bind($component),
              isFolderBlocked: $component.isFolderBlocked.bind($component),
            }
          } -->
          <!-- /ko -->
          <!-- /ko -->


      </div>


    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="f-btn f-btn-link" data-bind="click: close, text: _t('Отменить')">

        </button>

        <button type="submit" class="f-btn f-btn-success" data-bind="click: submit, text: _t('Сохранить')">

        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="move-to-folder-modal-dialog-template">
  <div class="modal-dialog modal-dialog-md modal-dialog-centered add-folder-modal-dialog" data-bind="
      component: {
        name: 'move-to-folder-modal',
        params: {
          data: data,
          close: close,
        }
      }" role="document">
  </div>
</template>
