/**
 * Модальное окно Переместить объект в папку
 */

class ViewModel {
  constructor(params) {
    this.initializing = ko.observable(true);

    let data = params.data;

    this.title = data.title || _t('Переместить объект в папку');
    this.foldersDirectory = data.foldersDirectory;
    this.blocked = data.blocked || [];

    this.term = ko.observable('');
    this.parentFolder = ko.observable(0);

    this.onSubmit = data.onSubmit;
    this.close = params.close;
  }

  isFolderVisible(folder) {
    let items = folder.items || [];
    let name = folder.name;

    return ko.computed(() => {
      let term = this.term().toLowerCase();
      if (name.toLowerCase().includes(term)) return true;
      return items.some((i) => this.isFolderVisible(i)());
    });
  }

  isFolderBlocked(folder) {
    return this.blocked.includes(folder.id);
  }

  submit() {
    let data = {
      parentFolderId: this.parentFolder(),
    };
    this.onSubmit(data)
      .then(() => {
        this.close();
      })
      .catch((errors) => {
        console.error(errors);
      });
  }

  onInit() {
    this.initializing(false);
  }
}

ko.components.register('move-to-folder-modal', {
  viewModel: ViewModel,
  template: {
    element: 'move-to-folder-modal-template',
  },
});
