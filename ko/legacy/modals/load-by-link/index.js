ko.components.register('load-by-link-dialog', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        'modal-dialog',
        'modal-dialog-centered',
        'survey-question__add-video-by-link-modal-dialog',
        'survey-question__add-video-by-link-modal-dialog--initializing'
      ]);

      const viewModel = new (function () {
        this.isSubmitted = ko.observable(false);
        this.link = ko.observable('').extend({
          required: {
            message: 'Обязательное поле'
          }
        });

        this.title = params.data.title || _t('Ссылка');
        this.note = params.data.note || '';

        this.youtubeSectionTitle =
          params.data.youtubeSectionTitle ||
          _t('Как вставить ссылку с Youtube?');

        this.cancel = function () {
          if ('cancel' in params) {
            params.cancel();
          }
        };

        this.submit = function () {
          this.isSubmitted(true);

          if (this.link.isValid()) {
            if ('submit' in params) {
              params.submit({
                link: this.link()
              });
            }
          }
        };

        this.formControlErrorStateMatcher = function (formControl) {
          return ko.computed(() => {
            return this.isSubmitted() && !formControl.isValid();
          });
        };
      })();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass(
          'survey-question__add-video-by-link-modal-dialog--initializing'
        );
        viewModel.initializing(false);
      };

      return viewModel;
    }
  },
  template: `
  <!-- ko template: { afterRender: onInit } -->
  <div class="modal-content">
    <div class="modal-header">
      <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body mt-0 pt-0">
      <div class="form-group survey-question__add-video-by-link-modal-dialog-link-form-group">
        <label class="bold mb-2 f-fs-3" data-bind="text: title"></label>
        <input class="form-control" data-bind="
                               textInput: link,
                               css: {'is-invalid': formControlErrorStateMatcher(link)}
                           " placeholder="https://...">

        <!-- ko if: formControlErrorStateMatcher(link) -->
        <div class="form-error" data-bind="text: link.error()"></div>
        <!-- /ko -->
      </div>

      <!-- ko if: note -->
      <div class="mt-2 color-service" style="font-size: 13px;" data-bind="text: note"></div>
      <!-- /ko -->

      <div data-bind="let: { isOpen: ko.observable(false) }" class="mt-2" style="font-size: 13px;">
        <!-- ko template: {
          foreach: templateIf(!isOpen(), $data),
          afterAdd: fadeAfterAddFactory(200, 200),
        } -->
        <a href="javascript: void(0)" data-bind="text: $component.youtubeSectionTitle, click: function() { isOpen(true); }"></a>
        <!-- /ko -->

        <!-- ko template: {
          foreach: templateIf(isOpen(), $data),
          afterAdd: slideAfterAddFactory(200),
          beforeRemove: slideBeforeRemoveFactory(200)
        } -->
        <p class="my-2" data-bind="text: _t('Чтобы вставить видео с <a href="https://www.youtube.com/" target="_blank" rel="noopener noreferrer nofollow">YouTube</a>, нужно на странице ролика нажать кнопку «Поделиться», скопировать ссылку из появившегося окна и вставить здесь.')"></p>
        <img class="my-2" src="/img/load-youtube/1.png" alt=""/>
        <img class="my-2" src="/img/load-youtube/2.png" alt=""/>
        <a href="javascript: void(0)" data-bind="click: function() { isOpen(false); }" style="font-size: 13px;" data-bind="text: _t('Свернуть')"></a>
        <!-- /ko -->
      </div>
    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="btn btn-link" data-bind="click: function() { cancel(); }, text: _t('Отменить')">

        </button>

        <button type="submit" class="btn btn-default" data-bind="click: function() { submit(); }, text: _t('Добавить')">

        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
  `
});
