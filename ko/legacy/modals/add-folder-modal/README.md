# Модальное окно Добавить папку

## Данные

* `title` &lt;string&gt; (по умолчанию `Новая папка`)
* `foldersDirectory` &lt;Directory&gt;
* `onSubmit` &lt;Function.&lt;Promise&gt;&gt;

## Имя шаблона

```
add-folder-modal-dialog-template
```

## Код

```
modalOpens.push({
  dialogTemplateName: 'add-folder-modal-dialog-template',
  data: {
    title: 'Новая папка',
    foldersDirectory: new Directory('folders'),
    onSubmit: function(data) {
      // data - { folderName, parentFolderId }

      // При удачном добавлении
      return Promise.resolve();

      // При ошибках
      return Promise.reject(errors);
    }
  }
})
```
