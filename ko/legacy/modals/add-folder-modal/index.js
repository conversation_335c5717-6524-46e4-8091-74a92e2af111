/**
 * Модальное окно Добавить папку
 */

class ViewModel {
  constructor(params) {
    let data = params.data;
    let close = params.close;

    this.initializing = ko.observable(true);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted,
    );

    this.title = data.title || _t('Новая папка');
    this.folderName = ko.observable('').extend({
      required: {
        message: _t('Обязательное поле'),
      },
    });
    this.foldersDirectory = data.foldersDirectory;
    this.parentFolder = ko.observable(0);
    this.onSubmit = data.onSubmit;

    this.folderResultTemplate = select2templates.folder.result;

    this.close = close;
  }

  submit() {
    this.isSubmitted(true);
    if (!this.folderName.isValid()) return;

    let data = {
      folderName: this.folderName(),
      parentFolderId: this.parentFolder() == 0 ? '' : this.parentFolder(),
    };

    this.onSubmit(data)
      .then(() => {
        this.close();
      })
      .catch((errors) => {
        console.error(errors);
      });
  }

  onInit() {
    this.initializing(false);
  }
}

ko.components.register('add-folder-modal', {
  viewModel: ViewModel,
  template: {
    element: 'add-folder-modal-template',
  },
});
