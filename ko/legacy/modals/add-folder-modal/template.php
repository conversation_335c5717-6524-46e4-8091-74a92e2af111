<template id="add-folder-modal-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title" data-bind="text: title"></h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body initializing" data-bind="css: { initializing: initializing}">
      <div class="form-group">
        <label class="form-label" data-bind="text: _t('Название')"></label>

        <input type="text" class="form-control" data-bind="textInput: folderName,
              css: {
                'is-invalid': formControlErrorStateMatcher(folderName)
              }" />

        <!-- ko template: {
                foreach: formControlErrorStateMatcher(folderName),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
        <div class="form-error" data-bind="text: $parent.folderName.error()"></div>
        <!-- /ko -->
      </div>

      <div class="form-group">
        <label class="form-label" data-bind="text: _t('Местоположение папки')"></label>

        <!-- ko if: foldersDirectory.loaded -->
        <div class="select2-wrapper">
          <select data-bind="
                  value: parentFolder,
                  lazySelect2: {
                    wrapperCssClass: 'select2-container--form-control',
                    containerCssClass: 'form-control',
                    allowClear: false,
                    templateResult: folderResultTemplate,
                    minimumResultsForSearch: 0,
                  }
                ">
            <option value="0" data-level="0" data-parent="" data-bind="text: _t('Главная страница')"></option>
            <!-- ko foreach: { data: foldersDirectory.sortByField('name'), as: 'folder' } -->
            <!-- ko template: {
                name: 'folder-template',
                data: {
                  folder: folder,
                  level: 1,
                }
            } -->
            <!-- /ko -->
            <!-- /ko -->
          </select>
        </div>
        <!-- /ko -->
      </div>
    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="f-btn f-btn-link" data-bind="click: close, text: _t('Отменить')">

        </button>

        <button type="submit" class="f-btn f-btn-success" data-bind="click: submit, text: _t('Сохранить')">
          
        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="add-folder-modal-dialog-template">
  <div class="modal-dialog modal-dialog-md modal-dialog-centered add-folder-modal-dialog"
  data-bind="component: {
      name: 'add-folder-modal',
      params: {
        data: data,
        modalElement: modalElement,
        close: close,
      }
    }" role="document">
  </div>
</template>
