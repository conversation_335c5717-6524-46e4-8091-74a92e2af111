<template id="columns-modal-dialog-template">
  <div class="modal-dialog modal-dialog-centered edit-columns-modal-dialog"
  data-bind="component: {
    name: 'columns-modal',
    params: {
      data: data,
      modalElement: modalElement,
      close: close
    }
  }" role="document"></div>
</template>

<template id="columns-modal-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  <div class="modal-content edit-columns-modal" data-bind="style: {
    visible: initializing() ? 'hidden': ''
  }">
    <div class="modal-header">
      <h2 class="modal-title" data-bind="text: _t('Настроить столбцы')"></h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body">
      <div class="edit-columns-modal__search-form">
        <i class="icon icon-search"></i>
        <input class="form-control" data-bind="textInput: searchTerm, attr: { placeholder: _t('Поиск по наименованию столбца')}">
        <!-- ko if: searchTerm().length > 0-->
        <button class="btn edit-columns-modal__clear-search" title="Отменить" data-bind="click: function() { searchTerm(''); }">
          <i class="icon icon-plus icon-plus--ligth"></i>
        </button>
        <!-- /ko -->
      </div>

      <div class="edit-columns-modal__columns">
        <!-- ko if: foundedColumns().length > 0 -->
        <div class="edit-columns-modal__columns-wrapper" data-bind="fScrollbar: {
          gradient: true
        }">
          <div class="edit-columns-modal__columns-list pb-2">
            <!-- ko foreach: foundedColumns -->
            <div class="edit-columns-modal__column">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" data-bind="checked: checked, attr: { id: 'column' + $index() }">
                <label class="form-check-label" data-bind="attr: { for: 'column' + $index() }, text: name"></label>
              </div>
            </div>
            <!-- /ko -->
          </div>
        </div>
        <!-- /ko -->

        <!-- ko if: foundedColumns().length === 0 -->
        <span data-bind="text: _t('Столбец с указанным наименованием не найден.')"></span>
        <!-- /ko -->
      </div>
    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="btn btn-link" data-bind="click: close, text: _t('Отменить')">

        </button>

        <button type="submit" class="btn btn-success" data-bind="click: submit, text: _t('Применить')">

        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>
