import { setTableSettings } from 'Utils/table';

class ViewModel {
  constructor(params) {
    this.initializing = ko.observable(true);

    this.searchTerm = ko.observable('');
    this.localStorageKey = params.data.localStorageKey;

    this.columns = params.data.columns.map((column) => {
      return {
        id: column.id,
        name: column.name,
        formattedName: column.name.toLowerCase(),
        checked: ko.observable(column.visible()),
        originalColumn: column
      };
    });

    this.foundedColumns = ko.pureComputed(() => {
      const term = this.searchTerm().toLowerCase();
      if (term.length < 2) return this.columns;
      return this.columns.filter((c) => {
        return c.formattedName.includes(term);
      });
    });

    this.close = params.close;
  }

  onInit() {
    this.initializing(false);
  }

  apply() {
    this.columns.forEach((c) => {
      c.originalColumn.visible(c.checked());
    });
  }

  save() {
    const value = _.chain(this.columns)
      .keyBy('id')
      .mapValues((column) => column.checked())
      .value();

    setTableSettings(this.localStorageKey, value);
  }

  submit() {
    this.apply();
    if (this.localStorageKey) this.save();
    this.close(true);
  }
}

ko.components.register('columns-modal', {
  viewModel: {
    createViewModel: function (params) {
      const viewModel = new ViewModel(params);

      return viewModel;
    }
  },
  template: {
    element: 'columns-modal-template'
  }
});
