<template id="launch-mailing-modal-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title">Запуск рассылки</h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: function() { $component.close(); }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body initializing" data-bind="css: { initializing: initializing}">
      <div class="form-group">
        <div class="hat-radio-group form-control">
          <div class="hat-radio-group__radio">
            <input type="radio" class="hat-radio-group__radio-input" name="launch-type" id="now" value="1" data-bind="checked: launchType">
            <label for="now" class="hat-radio-group__radio-label">
              <span class="hat-radio-group__radio-indicator"></span>
              Запустить сейчас
            </label>
          </div>

          <div class="hat-radio-group__radio">
            <input type="radio" class="hat-radio-group__radio-input" name="launch-type" id="defer" value="2" data-bind="checked: launchType">
            <label for="defer" class="hat-radio-group__radio-label">
              <span class="hat-radio-group__radio-indicator"></span>
              Выбрать дату
            </label>
          </div>

          <!-- ko if: canStop -->
          <div class="hat-radio-group__radio">
            <input type="radio" class="hat-radio-group__radio-input" name="launch-type" id="cancel" value="3" data-bind="checked: launchType">
            <label for="cancel" class="hat-radio-group__radio-label">
              <span class="hat-radio-group__radio-indicator"></span>
              Отменить запуск
            </label>
          </div>
          <!-- /ko -->
        </div>
      </div>

      <!-- ko template: {
        foreach: templateIf(launchType() == '1', $data),
        afterAdd: fadeAfterAddFactory(200),
      } -->
      <div>
        Рассылка будет запущена <b class="bold">прямо сейчас</b>
      </div>
      <!-- /ko -->

      <!-- ko template: {
        foreach: templateIf(launchType() == '2', $data),
        afterAdd: fadeAfterAddFactory(200),
      } -->
      <div>
        <div class="mb-4">
          Выберите дату и время запуска рассылки. До наступления указанной даты контакты в рассылке можно редактировать.
        </div>

        <div class="d-flex form-group">
          <div class="mr-4">
            <label class="form-label">Дата запуска</label>
            <input class="form-control" type="text" data-bind="value: date, periodPicker, periodPickerSingle: true, css: {
              'is-invalid': formControlErrorStateMatcher(date)
            }">
            <!-- ko template: {
              foreach: formControlErrorStateMatcher(date),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200),
            } -->
            <div class="form-error" data-bind="text: $parent.date.error"></div>
            <!-- /ko -->
          </div>
          <div >
            <label class="form-label">Время запуска</label>
            <input class="form-control" type="text" data-bind="textInput: time, mask, maskPattern: '00:00', css: {
              'is-invalid': formControlErrorStateMatcher(time)
            }" placeholder="00:00">
            <!-- ko template: {
              foreach: formControlErrorStateMatcher(time),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200),
            } -->
            <div class="form-error" data-bind="text: $parent.time.error"></div>
            <!-- /ko -->
          </div>
        </div>
      </div>
      <!-- /ko -->

      <!-- ko template: {
        foreach: templateIf(launchType() == '3', $data),
        afterAdd: fadeAfterAddFactory(200),
      } -->
      <div>
        Отложенный запуск будет <b class="bold">отменён</b>. Рассылку можно будет запустить в любое другое время.
      </div>
      <!-- /ko -->
    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="f-btn f-btn-link" data-bind="click: function() { $component.close(); }">
          Отменить
        </button>

        <button type="submit" class="f-btn f-btn-success" data-bind="click: submit">
          <!-- ko text: launchType() == '3' ? 'Сохранить' : 'Запустить' -->
          <!-- /ko -->
        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="launch-mailing-modal-dialog-template">
  <div class="modal-dialog modal-dialog-lg modal-dialog-centered launch-mailing-modal-dialog" data-bind="component: {
      name: 'launch-mailing-modal',
      params: {
      data: data,
      modalElement: modalElement,
      close: close,
    }
  }" role="document">
  </div>
</template>
