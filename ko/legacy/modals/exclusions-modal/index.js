let mailingsDirectory = new Directory('mailings?all=1&is_auto=1');

class ExclusionModel {
  constructor() {
    this.mailing = ko.observable('').extend({
      required: {
        message: _t('Обязательное поле'),
      },
    });
    this.mailingData = ko.observable();

    this.cachedMailing = ko.observable({
      id: null,
      title: null,
    })

    this.blockedReason = ko.computed(() => {
      let data = this.mailingData();
      if (!data) return false;
      if (data.isArchived) return _t('mailings', 'Рассылка добавлена в архив');
      if (data.stopped) return _t('mailings', 'Рассылка остановлена');
      return false;
    });
  }

  setData(data) {
    if (!data) return;
    this.mailing(data.mailing_id);
    this.mailingData(data.mailing);
    this.cachedMailing({
      id: data.mailing_id,
      title: data.mailing.title
    });
  }

  getData() {
    return {
      mailing_id: this.mailing(),
    };
  }
}
class ExceptionModel extends ExclusionModel {
  constructor(data) {
    super();

    this.setData(data);

    this.isValid = ko.computed(() => {
      return this.mailing.isValid();
    });
  }
}

class ProhibitionModel extends ExclusionModel {
  constructor(data) {
    super();

    this.days = ko.observable('').extend({
      required: {
        message: _t('Обязательное поле'),
      },
    });

    this.setData(data);

    this.isValid = ko.computed(() => {
      return this.mailing.isValid() && this.days.isValid();
    });
  }

  setData(data) {
    if (!data) return;
    super.setData(data);
    this.days(data.days);
  }

  getData() {
    return {
      ...super.getData(),
      days: this.days(),
    };
  }
}

class ExclusionsList {
  constructor(ItemModel, root) {
    this.ItemModel = ItemModel;
    this.items = ko.observableArray([]);
    this.mailingsDirectory = mailingsDirectory;

    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      root.isSubmitted,
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      root.isSubmitted,
    );

    //this.scrollbar = ko.observable(null);

    this.count = ko.pureComputed(() => {
      return this.items().length;
    });

    this.pureCount = ko.pureComputed(() => {
      return this.items().filter((i) => !i.blockedReason()).length;
    });

    this.isValid = ko.computed(() => {
      return !this.items().some((i) => !i.isValid());
    });
  }

  setItems(items) {
    this.items(items.map((i) => new this.ItemModel(i)));
  }

  getItems() {
    return this.items().map((i) => i.getData());
  }

  addItem() {
    this.items.push(new this.ItemModel());
    //let scrollTrack = this.scrollbar();
    // scrollTrack.updateScroll();
    // if (scrollTrack && scrollTrack.offsetHeight < scrollTrack.scrollHeight) {
    //   setTimeout(() => {
    //     scrollTrack.scrollTo({
    //       top: scrollTrack.scrollHeight,
    //       behavior: 'smooth',
    //     });
    //   }, 20);
    // }
  }

  removeItem(item) {
    this.items.remove(item);
    //this.scrollbar().updateScroll();
  }
}

class ViewModel {
  constructor(params) {
    this.close = params.close;
    this.modalElement = params.modalElement;

    this.mailingsDirectory = mailingsDirectory;
    this.mailingsDirectory.loaded(false);
    this.mailingsDirectory.load('force');

    this.loading = ko.observable(true);
    this.showEmptyMessage = ko.observable(false);
    this.isSubmitted = ko.observable(false);
    this.isSavedMessageShown = ko.observable(false);

    this.prohibitions = new ExclusionsList(ProhibitionModel, this);
    this.exceptions = new ExclusionsList(ExceptionModel, this);

    let mailingsLoaded = false;
    this.mailingsDirectory.onLoad(() => {
      if (mailingsLoaded) return;
      mailingsLoaded = true;

      if (this.mailingsDirectory.data().length) {
        this.loadExclusions().then((response) => {
          this.prohibitions.setItems(response.prohibitions);
          this.exceptions.setItems(response.exceptions);
          this.loading(false);
        });
      } else {
        this.showEmptyMessage(true);
        this.loading(false);
      }
    });
  }

  loadExclusions() {
    return new Promise((res) => {
      $.ajax({
        url: `${APIConfig.baseApiUrlPath}mailings/get-exception-settings?access-token=${APIConfig.apiKey}`,
        method: 'GET',
        success: (response) => {
          res(response);
        },
      });
    });
  }

  submit() {
    this.isSubmitted(true);

    if (!this.prohibitions.isValid() || !this.exceptions.isValid()) return;

    $.ajax({
      url: `${APIConfig.baseApiUrlPath}mailings/set-exception-settings?access-token=${APIConfig.apiKey}`,
      method: 'POST',
      data: {
        prohibitions: this.prohibitions.getItems(),
        exceptions: this.exceptions.getItems(),
      },
      success: (response) => {
        this.isSavedMessageShown(true);
      },
    });
  }

  onInit() {}
}

ko.components.register('exclusions-modal', {
  viewModel: ViewModel,
  template: {
    element: 'exclusions-modal-template',
  },
});
