<template id="exclusions-modal-dialog-template">
  <div class="modal-wrapper modal-wrapper--right">
    <div class="modal-actions">
      <button class="btn modal-close" data-bind="click: close">
        <i class="icon icon-close"></i>
      </button>
    </div>
    <div class="modal-page" role="document" data-bind="
      component: {
        name: 'exclusions-modal',
        params: {
          modalElement: modalElement,
          close: close,
        }
      }">
    </div>
  </div>
</template>

<template id="exclusions-modal-template">
  <div class="modal-page__content" data-bind="descendantsComplete: $component.onInit.bind($component)">

    <div class="modal-page__header pb-4">
      <h2 class="modal-title p-0" data-bind="text: _t('mailings', 'Настройка исключений')"></h2>
      <div class="f-color-service f-fs-1" data-bind="text: _t('mailings', 'Настроить запрет на отправку других рассылок и исключения к запретам можно только для автоматических рассылок')"></div>
    </div>

    <div class="modal-page__body f-card">
      <!-- ko if: loading -->
      <div class="f-card__section f-card__inner align-items-center justify-content-center">
        <i class="fa fa-spinner fa-pulse fa-2x f-color-primary"></i>
      </div>
      <!-- /ko -->

      <!-- ko ifnot: loading -->

      <!-- ko if: showEmptyMessage -->
      <div class="f-card__section f-card__inner align-items-center justify-content-center">
        <div data-bind="text: _t('main', 'У вас пока нет {something}', {
          'something': '<b class=\'bold\'>' + _t('автоматических рассылок') + '</b>'
        })">
        </div>
      </div>
      <!-- /ko -->

      <!-- ko ifnot: showEmptyMessage -->
      <div class="f-card__section f-card__grow py-0 h-100">
        <div class="row h-100">
          <div class="col-6 h-100" data-bind="using: prohibitions">
            <div class="h-100 f-card__inner mailing-exclusions">
              <div class="d-flex mb-3">
                <div class="flex-grow-1">
                  <h3 class="f-h2 mb-2" data-bind="text: _t('mailings', 'Запрет на отправку других рассылок')">
                    <span class="font-weight-normal f-color-service ml-2">
                      <!-- ko text: pureCount -->
                      <!-- /ko -->

                      <!-- ko if: count() && count() !== pureCount() -->

                      <!-- ko text: '(' + count() + ')' -->
                      <!-- /ko -->
                      <!-- /ko -->
                    </span>
                  </h3>
                  <div class="f-fs-1 f-color-service" data-bind="text: _t('mailings', 'Если респонденту была отправлена рассылка из данного списка, то другие рассылки не будут отправляться ему в течение указанного количества дней.')">

                  </div>
                </div>
                <div class="flex-shrink-0 ml-3">
                  <button class="btn btn-success btn-with-icon btn-add" data-bind="click: addItem, text: _t('Добавить')"></button>
                </div>
              </div>

              <div class="flex-grow-1 overflow-hidden mailing-exclusions__scroll">
                <div class="h-100" data-bind="nativeScrollbar">
                  <!-- ko foreach: {
                    data: items,
                    as: 'item',
                    afterAdd: fadeAfterAddFactory(400),
                    beforeRemove: fadeBeforeRemoveFactory(250)
                  } -->

                  <div class="mailing-exclusion mailing-prohibition">
                    <button type="submit" class="btn btn-danger btn-remove"data-bind="click: function() { $parent.removeItem(item) }, tooltip, tooltipText: _t('Удалить')" data-placement="top" data-fallbackPlacement="top" data-boundary="viewport">
                    </button>


                    <div class="mailing-exclusion__wrapper">
                      <div class="form-group mailing-exclusion__mailing">
                        <label class="form-label" data-bind="text: _t('Рассылка')"></label>

                        <div class="select2-wrapper" data-bind="css: {
                        	  'is-invalid': !item.blockedReason && $parent.formControlErrorStateMatcher(item.mailing)(),
                        	  'is-valid': !item.blockedReason && $parent.formControlSuccessStateMatcher(item.mailing)(),
                        	}">
                          <select data-bind="value: item.mailing,
                                valueAllowUnset: true,
                                disable: item.blockedReason,
                        	      lazySelect2: {
                        	        containerCssClass: 'form-control',
                        	        wrapperCssClass: 'select2-container--form-control',
                                  minimumResultsForSearch: 0,
                                  dropdownAutoWidth: false,
                                  placeholder: _t('Выберите рассылку')
                        	      },
                            ">
                            <!-- ko if: item.blockedReason -->
                            <option data-bind="value: item.cachedMailing().id, text: item.cachedMailing().title,"></option>
                            <!-- /ko -->

                            <!-- ko ifnot: item.blockedReason -->
                            <!-- ko foreach: $parent.mailingsDirectory.data -->
                            <option data-bind="value: id, text: title,"></option>
                            <!-- /ko -->
                            <!-- /ko -->

                          </select>



                          <!-- ko template: {
                        	    foreach: $parent.formControlErrorStateMatcher(item.mailing),
                        	    afterAdd: fadeAfterAddFactory(200),
                        	    beforeRemove: fadeBeforeRemoveFactory(200)
                        	  } -->
                          <div class="form-error" data-bind="text: item.mailing.error"></div>
                          <!-- /ko -->
                        </div>
                      </div>

                      <div class="form-group mailing-exclusion__days">
                        <label class="form-label">
                          <span data-bind="text: _t('mailings', 'Запрет, дней')"></span>
                          <button class="btn-question" data-bind="tooltip, tooltipText: _t('mailings', 'Запрет, дней')"></button>
                        </label>
                        <input type="text" class="form-control" data-bind="numericIntervalField: { min: 0, max: 366 }, textInput: item.days, css: {
                        	  'is-invalid': !item.blockedReason && $parent.formControlErrorStateMatcher(item.days)(),
                        	  'is-valid': !item.blockedReason && $parent.formControlSuccessStateMatcher(item.days)(),
                        	}, disable: item.blockedReason" placeholder="0">

                        <!-- ko template: {
                        	    foreach: $parent.formControlErrorStateMatcher(item.days),
                        	    afterAdd: fadeAfterAddFactory(200),
                        	    beforeRemove: fadeBeforeRemoveFactory(200)
                        	  } -->
                        <div class="form-error" data-bind="text: item.days.error"></div>
                        <!-- /ko -->
                      </div>

                    </div>

                    <!-- ko if: item.blockedReason -->
                    <div class="mailing-exclusion__message" data-bind="text: item.blockedReason"></div>
                    <!-- /ko -->


                  </div>
                  <!-- /ko -->
                </div>
              </div>
            </div>


          </div>
          <div class="col-6 h-100" data-bind="using: exceptions">
            <div class="h-100 f-card__inner  mailing-exclusions">
              <div class="d-flex mb-3">
                <div class="flex-grow-1">
                  <h3 class="f-h2 mb-2">
                    <span data-bind="text: _t('mailings', 'Исключения к запретам')"></span>
                    <span class="font-weight-normal f-color-service ml-2">
                      <!-- ko text: pureCount -->
                      <!-- /ko -->

                      <!-- ko if: count() && count() !== pureCount() -->

                      <!-- ko text: '(' + count() + ')' -->
                      <!-- /ko -->
                      <!-- /ko -->
                    </span>
                  </h3>
                  <div class="f-fs-1 f-color-service" data-bind="text: _t('mailings', 'Список рассылок, которые будут приходить респонденту вне зависимости от срабатывания других рассылок')">

                  </div>
                </div>
                <div class="flex-shrink-0 ml-3">
                  <button class="btn btn-success btn-with-icon btn-add" data-bind="click: addItem, text: _t('Добавить')"></button>
                </div>
              </div>

              <div class="flex-grow-1 overflow-hidden mailing-exclusions__scroll">
                <div class="h-100" data-bind="nativeScrollbar">
                  <!-- ko foreach: {
                  data: items,
                  as: 'item',
                  afterAdd: fadeAfterAddFactory(400),
                  beforeRemove: fadeBeforeRemoveFactory(250)
                } -->

                  <div class="mailing-exclusion mailing-exception">
                    <button type="submit" class="btn btn-danger btn-remove" data-bind="click: function() { $parent.removeItem(item) }, tooltip, tooltipText: _t('Удалить')" data-placement="top" data-fallbackPlacement="top" data-boundary="viewport">
                    </button>


                    <div class="mailing-exclusion__wrapper">
                      <div class="form-group mailing-exclusion__mailing">
                        <label class="form-label" data-bind="text: _t('Рассылка')"></label>

                        <div class="select2-wrapper" data-bind="css: {
                        'is-invalid': !item.blockedReason && $parent.formControlErrorStateMatcher(item.mailing)(),
                        'is-valid': !item.blockedReason && $parent.formControlSuccessStateMatcher(item.mailing)(),
                        }">
                          <select data-bind="value: item.mailing,
                            valueAllowUnset: true,
                            disable: item.blockedReason,
                            lazySelect2: {
                              containerCssClass: 'form-control',
                              wrapperCssClass: 'select2-container--form-control',
                              minimumResultsForSearch: 0,
                              dropdownAutoWidth: false,
                              placeholder: _t('Выберите рассылку')
                            },
                        ">
                            <!-- ko if: item.blockedReason -->
                            <option data-bind="value: item.cachedMailing().id, text: item.cachedMailing().title,"></option>
                            <!-- /ko -->

                            <!-- ko ifnot: item.blockedReason -->
                            <!-- ko foreach: $parent.mailingsDirectory.data -->
                            <option data-bind="value: id, text: title,"></option>
                            <!-- /ko -->
                            <!-- /ko -->
                          </select>



                          <!-- ko template: {
                          foreach: $parent.formControlErrorStateMatcher(item.mailing),
                          afterAdd: fadeAfterAddFactory(200),
                          beforeRemove: fadeBeforeRemoveFactory(200)
                        } -->
                          <div class="form-error" data-bind="text: item.mailing.error"></div>
                          <!-- /ko -->
                        </div>
                      </div>
                    </div>

                    <!-- ko if: item.blockedReason -->
                    <div class="mailing-exclusion__message" data-bind="text: item.blockedReason"></div>
                    <!-- /ko -->
                  </div>


                  <!-- /ko -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- /ko -->

      <!-- /ko -->



    </div>

    <div class="modal-page__footer fixed-footer fixed">
      <!-- ko component: { name: 'saved-message', params: {
          show: isSavedMessageShown
        } } -->
      <!-- /ko -->
      <div class="modal-page__actions p-0">
        <button type="submit" class="f-btn" data-bind="click: function() { close(); }">
          <span class="f-btn-prepend">
            <svg-icon params="name: 'bin'"></svg-icon>
          </span>
          <span data-bind="text: _t('Отменить')"></span>
        </button>

        <button type="submit" class="f-btn f-btn-success" data-bind="click: function() { submit(); }">
          <span class="f-btn-prepend">
            <svg-icon params="name: 'save'"></svg-icon>
          </span>
          <span data-bind="text: _t('Сохранить')"></span>
        </button>
      </div>
    </div>

  </div>
</template>
