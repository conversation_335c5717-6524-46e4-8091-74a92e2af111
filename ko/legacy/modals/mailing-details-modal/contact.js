function getGender(gender) {
  if (gender == 1) return 'муж.';
  if (gender == 2) return 'жен.';
  return '';
}

export class ContactModel {
  constructor(contactData) {
    this.checked = ko.observable(false);

    this.id = contactData.id;
    this.listContactId = contactData.list_contact_id;
    this.client = contactData.client_id;

    this.name = contactData.contactName;
    this.createdAt = moment(contactData.created_at, 'YYYY-MM-DD').format(
      'DD.MM.YYYY'
    );

    this.statusName = contactData.statusName;
    this.statusClass = {
      1: 'f-color-status--violet',
      2: 'f-color-status--primary',
      3: 'f-color-status--red'
    }[contactData.status];

    this.isPassed = contactData.status != 1;
    this.passedAt = contactData.passed_at
      ? moment(contactData.passed_at, 'YYYY-MM-DD').format('DD.MM.YYYY')
      : null;

    this.tags = [];

    if (contactData.contact) {
      this.tags = contactData.contact.tags || [];
      this.phone = contactData.contact.phone;
      this.email = contactData.contact.email;
      this.gender = getGender(contactData.contact.gender);
      let birthday = contactData.contact.birthday;
      if (birthday) {
        birthday = moment(birthday, 'YYYY-MM-DD');
        if (birthday.isValid()) birthday = birthday.format('DD.MM.YYYY');
      }
      this.birthday = birthday;
    }
  }
}
