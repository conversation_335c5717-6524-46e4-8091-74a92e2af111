import { TableListModel } from 'Legacy/models/table';
import 'Legacy/components/tags-select';
import { BirthdayFilterModel } from 'Legacy/components/birthday-filter';
import { Observer } from 'Legacy/utils/observer';
import { NEW_MAILING } from 'Legacy/data/mailing-statuses';
import { ContactModel } from './contact';
import { CheckedModel } from 'Legacy/models/table/checked';
import { GroupActions } from 'Legacy/models/table/group-actions';
// import '../select-contacts-modal';
import { getClientFields } from 'Legacy/utils/client-fields';
import 'Modals/select-contacts-modal-page';
class ViewModel {
  constructor(params) {
    this.modals = ko.observableArray([]);

    this.mailing = params.data.mailing;

    this.isLaunched = ko.pureComputed(() => {
      return this.mailing.status() != NEW_MAILING;
    });

    this.hasContacts = ko.pureComputed(() => {
      return this.mailing.contactsCount() > 0;
    });

    this.observer = new Observer(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            this.load();
          }
        });
      },
      {
        rootMargin: '50px'
      }
    );

    this.directories = {
      tags: new Directory('contact-tags?all=1')
    };

    Object.keys(this.directories).forEach((key) =>
      this.directories[key].load()
    );

    this.clientFields = getClientFields();

    this.isLoading = ko.observable(false);
    this.hasResults = ko.observable(true);
    this.isLastPage = ko.observable(false);
    this.page = ko.observable(1);
    this.items = ko.observableArray([]);

    this.totalCount = ko.observable(0);
    this.checked = new CheckedModel({
      items: this.items,
      totalCount: this.totalCount
    });

    this.groupActions = new GroupActions({
      selectedCount: this.checked.checkedCount,
      selectAll: () => {
        this.checked.checkAll();
      },
      actions: [{ id: 1, text: 'Удалить' }],
      apply: () => {
        // TODO
        let action = this.groupActions.action();
        if (!action) return;

        if (action == 1) {
          this.removeContacts();
        }

        this.checked.uncheckAll();
      },
      reset: () => {
        this.checked.uncheckAll();
      }
    });

    this.filters = {
      statuses: ko.observableArray([]),
      clientSearch: ko.observable(''),
      genders: ko.observableArray([]),
      tags: ko.observableArray([]),
      tagsOperation: ko.observable('1'),
      filled: ko.observable(''),
      fields: ko.observableArray([])
    };

    this.birthdayFilter = new BirthdayFilterModel({
      format: 'YYYY-MM-DD'
    });

    this.newMailingTable = {};
    this.launchedMailingTable = {};

    TableListModel(this.newMailingTable, {
      columns: [
        {
          id: 'name',
          name: 'ФИО',
          searchable: true,
          sortable: true
        },
        {
          id: 'phone',
          name: 'Телефон',
          searchable: true,
          sortable: true
        },
        {
          id: 'email',
          name: 'Email',
          searchable: true,
          sortable: true
        },
        {
          id: 'gender',
          name: 'Пол',
          searchable: true,
          sortable: true
        },
        {
          id: 'birthday',
          name: 'Дата рождения',
          searchable: true,
          sortable: true
        },
        {
          id: 'tags',
          name: 'Теги',
          searchable: true,
          sortable: true
        },
        {
          id: 'added_at',
          name: 'Добавлен',
          searchable: true,
          sortable: true
        }
      ],
      onChange: () => this.applyFilters(),
      localStorageKey: 'mailing-contacts-columns',
      searchParam: 'search',
      orderParam: 'order',
      defaultSort: 'added_at'
    });

    TableListModel(this.launchedMailingTable, {
      columns: [
        {
          id: 'sended_at',
          name: 'Отправлена',
          searchable: true,
          sortable: true
        },
        {
          id: 'status',
          name: 'Статус',
          searchable: true,
          sortable: true
        },
        {
          id: 'name',
          name: 'ФИО',
          searchable: true,
          sortable: true
        },
        {
          id: 'phone',
          name: 'Телефон',
          searchable: true,
          sortable: true
        },
        {
          id: 'email',
          name: 'Email',
          searchable: true,
          sortable: true
        }
      ],
      onChange: () => this.applyFilters(),
      localStorageKey: 'mailing-contacts-columns',
      searchParam: 'search',
      orderParam: 'order',
      defaultSort: 'sended_at'
    });

    this.activeTable = ko.pureComputed(() => {
      return this.mailing.isNew()
        ? this.newMailingTable
        : this.launchedMailingTable;
    });

    this.close = params.close;

    this.mailing.status.subscribe((v) => {
      this.applyFilters();
    });
  }

  removeContacts() {
    let params = {};

    if (this.checked.allItemsChecked()) {
      params.type = 'filter';
      params = {
        ...params,
        type: 'filter',
        ...this.getSearchParams()
      };
    } else {
      params.clients = this.checked.getChecked().map((c) => c.listContactId);
    }

    this.mailing.removeContacts(params).then(() => {
      this.applyFilters();
    });
  }

  getSearchParams() {
    const params = {
      order: this.activeTable().sort.getParams(),
      search: this.activeTable().search.getParams()
    };

    if (this.filters.clientSearch()) {
      params.client = this.filters.clientSearch();
    }

    if (this.mailing.isNew()) {
      params.tags = this.filters.tags();
      params.tagsOperation = this.filters.tagsOperation();
      params.gender = this.filters.genders();
      params.birthday = this.birthdayFilter.getParams();
      if (
        ['1', '0'].includes(this.filters.filled()) &&
        this.filters.fields().length
      ) {
        params.records = {
          filled: this.filters.filled(),
          fields: this.filters.fields()
        };
      }
    } else {
      params.statuses = this.filters.statuses();
    }

    return params;
  }

  resetFilters() {
    this.filters.statuses([]);
    this.filters.clientSearch('');
    this.filters.tags([]);
    this.filters.tagsOperation('1');
    this.filters.genders([]);
    this.filters.filled('');
    this.filters.fields([]);

    this.birthdayFilter.reset();

    this.activeTable().sort.reset();
    this.activeTable().search.reset();

    this.applyFilters();
  }

  applyFilters() {
    this.page(1);
    this.isLastPage(false);
    this.items.removeAll();
    this.load();
  }

  load() {
    return new Promise((res, rej) => {
      if (this.isLoading()) {
        res();
        return;
      }
      if (this.isLastPage()) {
        res();
        return;
      }

      this.isLoading(true);
      let params = this.getSearchParams();

      $.ajax({
        url: `${
          APIConfig.baseApiUrlPath
        }mailing-lists/get-contacts?access-token=${
          APIConfig.apiKey
        }&id=${this.mailing.id()}`,
        data: {
          ...params,
          page: this.page()
        },
        method: 'GET',
        success: (data) => {
          this.updateData(data);
          this.isLoading(false);
          res();
        },
        error: (response) => {
          console.error(response.responseJSON);
          this.isLoading(false);
          rej();
        }
      });
    });
  }

  updateData(data) {
    this.page(this.page() + 1);

    data.contacts.forEach((c) => {
      let contact = new ContactModel(c);
      this.items.push(contact);
      if (this.checked.allItemsChecked()) {
        contact.checked(true);
      }
    });
    if (!data.contacts.length) this.isLastPage(true);
    this.hasResults(this.items().length > 0);

    this.totalCount(data.filterContactsCount);

    // "success": true,
    // "allTags": []
  }

  removeMailing() {
    this.mailing.remove().then(() => {
      this.close();
    });
  }

  addContacts() {
    this.modals.push({
      name: 'select-contacts-modal-page',
      params: {
        data: {
          tagsDirectory: this.directories.tags,
          onSubmit: (params) => {
            return this.mailing.addContacts(params);
          }
        },
        onClose: () => {
          this.mailing.update().then(() => {
            this.initObserver();
            this.applyFilters();
          });
        }
      }
    });
  }

  initObserver() {
    let bottom = document.getElementById('mailing-contacts-bottom');
    this.observer.deactivate();
    this.observer.setTarget(bottom);
    this.observer.activate();
  }

  onInit() {
    this.load().then(() => {
      this.initObserver();
    });
  }
}

ko.components.register('mailing-details-modal', {
  viewModel: ViewModel,
  template: {
    element: 'mailing-details-modal-template'
  }
});
