import { getChannel } from 'Legacy/data/channels';

ko.components.register('no-channel-template-modal-dialog', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const viewModel = new (function () {
        this.channelName = getChannel(params.data.channelType).name;

        this.cancel = function () {
          if ('cancel' in params) {
            params.cancel();
          }
        };

        this.submit = function () {
          if ('submit' in params) {
            params.submit();
          }
        };

      })();

      viewModel.init = function () {
        const $element = $(componentInfo.element);
        $element.addClass(
          'modal-dialog modal-dialog-centered dont-have-template-modal-dialog',
        );
      };

      return viewModel;
    },
  },
  template: `
              <!-- ko template: { afterRender: init } -->
                  <div class="modal-content">
                      <div class="modal-header">
                          <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
                              <span aria-hidden="true">&times;</span>
                          </button>
                      </div>

                      <div class="modal-body">
                          Канал связи
                          <span class="bold" style='text-transform: capitalize'>"<!-- ko text: channelName --><!-- /ko -->"</span> не настроен. Для включения канала связи заполните обязательные параметры настройки канала.
                      </div>

                      <div class="modal-footer modal-btns">
                          <button type="button" class="f-btn f-btn-link"
                                  data-bind="click: function() { cancel(); }">
                              Закрыть
                          </button>
                      </div>
                  </div>
              <!-- /ko -->
          `,
});
