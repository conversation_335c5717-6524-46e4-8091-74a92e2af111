import { getChannel } from 'Legacy/data/channels';

ko.components.register('no-channel-settings-modal-dialog', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const viewModel = new (function () {
        this.channelName = getChannel(params.data.channelType).fullName;

        this.mode = params.data.mode;

        this.url = '/foquz/settings?tab=settings&setting=channels&channel=' + params.data.channelType;

        this.cancel = function () {
          if ('cancel' in params) {
            params.cancel();
          }
        };

        this.submit = function () {
          if ('submit' in params) {
            params.submit();
          }
        };

      })();

      viewModel.init = function () {
        const $element = $(componentInfo.element);
        $element.addClass(
          'modal-dialog modal-dialog-centered dont-have-global-settings-modal-dialog',
        );
      };

      return viewModel;
    },
  },
  template: `
              <!-- ko template: { afterRender: init } -->
                  <div class="modal-content">
                      <div class="modal-header">
                          <h2 class="modal-title"></h2>

                          <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
                              <span aria-hidden="true">&times;</span>
                          </button>
                      </div>

                      <div class="modal-body">
                          Канал связи
                          <span class="bold" style='text-transform: capitalize'>"<!-- ko text: channelName --><!-- /ko -->"</span>
                          для <!-- ko text: mode == 'mailing' ? 'рассылки' : 'опроса' --><!-- /ko -->
                          настроить нельзя, так как для него не добавлены глобальные настройки. <br> Для настройки канала <a data-bind="attr: {href: url}" target="_blank">перейдите по ссылке</a>.
                      </div>

                      <div class="modal-footer modal-btns">
                          <button type="button" class="f-btn f-btn-link"
                                  data-bind="click: function() { cancel(); }">
                              Закрыть
                          </button>
                      </div>
                  </div>
              <!-- /ko -->
          `,
});
