import './change-limit-modal';
import './change-user-limit-modal';

import 'Dialogs/tariff/tariff-dialog';
import 'Dialogs/tariff/answer-cost-dialog';
import 'Dialogs/tariff/tariff-cost-dialog';
import { DialogsModule } from 'Utils/dialogs-module';
import { CompanyChangeBonusesEvent } from '../../../utils/events/company';

import './style.less';

const CORPORATION = 3;
export class TariffModel {
  constructor(COMPANY, config = {}) {
    DialogsModule(this);

    this.companyId = null;

    this.canEdit = config.canEdit;

    this.currentTariffId = ko.observable('');
    this.tariffName = ko.observable('');
    this.fromDate = ko.observable('');
    this.toDate = ko.observable('');

    this.bonuses = ko.observable(''); // бонусный счет

    this.answersCount = ko.observable(''); // осталось анкет в текущем периоде
    this.answersLimit = ko.observable(''); // лимит на кол-во анкет
    this.answersUsed = ko.observable(''); // анкет использовано
    this.answersUnlimited = ko.observable(false);
    this.userLimit = ko.observable('');

    this.updatingAnswersCount = ko.observable(false);

    this.mailsCount = ko.observable(''); // осталось писем в текущем периоде
    this.mailsLimit = ko.observable(''); // лимит на кол-во писем
    this.mailsUnlimited = ko.observable(false);

    this.updatingMailsCount = ko.observable(false);

    this.cost = ko.observable(null);

    this.answerCost = ko.observable(null);
    this.hasAnswerCost = ko.pureComputed(() => {
      let cost = this.answerCost();
      if (cost === null) return false;
      return true;
    });

    this.isBlocked = ko.observable(false);

    // this.isBlocked = ko.computed(() => {
    //   if (this.answersUnlimited()) return false;
    //   if (this.bonuses() > 0) return false;
    //   return this.answersCount() <= 0;
    // });

    this.isMailsBlocked = ko.computed(() => {
      if (this.mailsUnlimited()) return false;
      return this.mailsCount() <= 0;
    });

    if (COMPANY) {
      this.setCompanyData(COMPANY);
    }

    CompanyChangeBonusesEvent.on(({ bonuses }) => {
      this.bonuses(bonuses);
      this.isBlocked(false);
    });
  }

  isUnlimited(unlimited) {
    if (unlimited > 0) return true;
    if (unlimited === 0) return false;

    if (unlimited == null) {
      return this.currentTariffId() == CORPORATION;
    }
  }

  setCompanyData(companyData) {
    this.companyId = companyData.id;

    this.currentTariffId(companyData.tariff.id);
    this.tariffName(companyData.tariff.title);

    let fromDate = moment(companyData.tariff_from, 'YYYY-MM-DD');
    let toDate = moment(companyData.tariff_to, 'YYYY-MM-DD');
    if (fromDate.isValid()) this.fromDate(fromDate.format('DD.MM.YYYY'));
    else this.fromDate('');
    if (toDate.isValid()) this.toDate(toDate.format('DD.MM.YYYY'));
    else this.toDate('');

    if ('bonuses' in companyData) {
      this.bonuses(companyData.bonuses);
    }

    this.cost(companyData.tariff.cost || '');

    let answersCount = companyData.answersLeft > 0 ? companyData.answersLeft : 0;
    this.answersCount(answersCount);
    this.answersUsed(companyData.answersTariffPeriodCount);
    this.userLimit(companyData.userLimit);
    this.answersLimit(companyData.answersLimit);
    this.answersUnlimited(
      !companyData.answersLimit || this.isUnlimited(companyData.unlimited)
    );

    this.mailsCount(companyData.mailsLeft);
    this.mailsLimit(companyData.mailsLimit);
    this.mailsUnlimited(
      !companyData.mailsLimit || this.isUnlimited(companyData.unlimited_mails)
    );

    this.answerCost(companyData.answer_cost);

    this.isBlocked(companyData.isAnswersLimitsOver);
  }

  change() {
    if (!this.companyId) return;

    this.openDialog({
      name: 'tariff-dialog',
      params: {
        tariff: {
          id: this.currentTariffId(),
          isBlocked: this.isBlocked()
        },
        companyId: this.companyId,
        canEdit: this.canEdit
      },
      events: {
        submit: (data) => {
          if (this.canEdit && data) {
            this.setCompanyData(data.company);
          }
        }
      }
    });
  }

  editMailsLimit() {
    window.modalOpens.push({
      dialogTemplateName: 'change-limit-modal-dialog-template',
      data: {
        companyId: this.companyId,
        limitType: 'mails',
        limit: this.mailsLimit(),
        unlimited: this.mailsUnlimited()
      },
      close: (company) => {
        if (company) {
          this.setCompanyData(company);
        }
      }
    });
  }

  editAnswerCost() {
    this.openDialog({
      name: 'answer-cost-dialog',
      params: {
        companyId: this.companyId,
        cost: this.answerCost()
      },
      events: {
        'change-cost': (companyData) => {
          this.setCompanyData(companyData);
        }
      }
    });
  }

  editCost() {
    this.openDialog({
      name: 'tariff-cost-dialog',
      params: {
        companyId: this.companyId,
        cost: this.cost() || 0
      },
      events: {
        'change-cost': (companyData) => {
          this.setCompanyData(companyData);
        }
      }
    });
  }

  editAnswersLimit() {
    window.modalOpens.push({
      dialogTemplateName: 'change-limit-modal-dialog-template',
      data: {
        companyId: this.companyId,
        limitType: 'answers',
        limit: this.answersLimit(),
        unlimited: this.answersUnlimited()
      },
      close: (company) => {
        if (company) {
          this.setCompanyData(company);
        }
      }
    });
  }

  editUsersLimit() {
    window.modalOpens.push({
      dialogTemplateName: 'change-user-limit-modal-dialog-template',
      data: {
        companyId: this.companyId,
        limit: this.userLimit(),
      },
      close: (company) => {
        if (company) {
          this.setCompanyData(company);
        }
      }
    });
  }

  updateMailsCount() {
    if (!this.companyId) return;

    this.updatingMailsCount(true);
    const delay = utils.delay(500);

    $.ajax({
      url: `${APIConfig.baseApiUrlPath}tariffs/refresh-mails-count?company_id=${this.companyId}&access-token=${APIConfig.apiKey}`,
      method: 'GET',
      success: (data) => {
        this.mailsCount(data.count || 0);
        delay.then(() => {
          this.updatingMailsCount(false);
        });
      }
    });
  }

  updateAnswersCount() {
    if (!this.companyId) return;

    this.updatingAnswersCount(true);
    const delay = utils.delay(500);

    $.ajax({
      url: `${APIConfig.baseApiUrlPath}tariffs/refresh-count?company_id=${this.companyId}&access-token=${APIConfig.apiKey}`,
      method: 'GET',
      success: (data) => {
        let count = data.count > 0 ? data.count : 0;
        let used = data.used > 0 ? data.used : 0;
        this.answersCount(count || 0);
        this.answersUsed(used);
        delay.then(() => {
          this.updatingAnswersCount(false);
        });
      }
    });
  }
}
