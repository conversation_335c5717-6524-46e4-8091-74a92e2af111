/**
 * Модальное окно Добавить папку
 */

class ViewModel {
  constructor(params) {
    let data = params.data;

    this.close = params.close;
    this.initializing = ko.observable(true);
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted,
    );

    this.companyId = data.companyId;
    this.limitType = data.limitType || 'answers';
    this.limit = ko.observable('' + data.limit).extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: {
        validator: v => {
          let value = ('' + v).replace(/\s/g, '');
          value = parseInt(value);
          return value > 0;
        },
        message: 'Обязательное поле'
      }
    });

    this.unlimit = ko.observable(!!data.unlimited);
    this.blocked = ko.observable(data.unlimited);

    this.unlimit.subscribe(v => {
      if (!v) this.blocked(false);
    })

    this.limit.subscribe(v => {
      if (v > 0) {
        this.unlimit(false);
      }
    })
  }

  submit() {
    this.isSubmitted(true);
    if (!this.limit.isValid()) return;

    let data = {};

    let limit = this.limit().replace(/\s/g, '');
    let unlimited  = this.unlimit() ? 1 : 0

    if (this.limitType == 'mails') {
      data.limit_mails = limit;
      data.unlimited_mails = unlimited;
    } else {
      data.limit_answers = limit;
      data.unlimited = unlimited;
    }

    $.ajax({
      url: `${APIConfig.baseApiUrlPath}tariffs/change-limits?company_id=${this.companyId}&access-token=${APIConfig.apiKey}`,
      method: 'POST',
      data,
      success: (response) => {
        this.close(response.company);
      }
    })
  }

  onInit() {
    this.initializing(false);
  }
}

ko.components.register('change-limit-modal', {
  viewModel: ViewModel,
  template: {
    element: 'change-limit-modal-template',
  },
});
