/**
 * Модальное окно Добавить папку
 */

class ViewModel {
  constructor(params) {
    let data = params.data;

    this.close = params.close;
    this.initializing = ko.observable(true);
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted,
    );

    this.companyId = data.companyId;
    this.limitType = data.limitType || 'answers';
    this.limit = ko.observable('' + data.limit).extend({
      required: {
        message: 'Обязательное поле'
      },
      validation: {
        validator: v => {
          let value = ('' + v).replace(/\s/g, '');
          value = parseInt(value);
          return value > 0;
        },
        message: 'Обязательное поле'
      }
    });

    this.unlimit = ko.observable(!!(data.limit === 0 || data.limit === "Без ограничений"));
    this.blocked = ko.observable(!!(data.limit === 0 || data.limit === "Без ограничений"));

    this.unlimit.subscribe(v => {
      if (!v) this.blocked(false);
    })

    this.limit.subscribe(v => {
      if (v > 0) {
        this.unlimit(false);
      }
    })

    this.isFormValid = ko.computed(() => {
      return this.unlimit() || this.limit() > 0;
    }, this);
  }

  submit() {
    this.isSubmitted(true);
    if (!this.limit.isValid()) return;

    let data = {};
    data.limit_users = this.unlimit() ? 0 : this.limit();

    $.ajax({
      url: `${APIConfig.baseApiUrlPath}tariffs/change-limits?company_id=${this.companyId}&access-token=${APIConfig.apiKey}`,
      method: 'POST',
      data,
      success: (response) => {
        this.close(response.company);
      }
    })
  }

  onInit() {
    this.initializing(false);
  }
}

ko.components.register('change-user-limit-modal', {
  viewModel: ViewModel,
  template: {
    element: 'change-user-limit-modal-template',
  },
});
