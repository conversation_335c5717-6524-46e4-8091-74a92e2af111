<template id="tariff-info-template">
  <div class="border-bottom info-row d-lg-none pt-2" data-bind="using: tariff">
    <div class="info-row__section">
      <div class="d-flex align-items-center">
        <div style="font-size: 19px;" class="mb-1">
          <span class="bold" data-bind="text: tariffName"></span>
        </div>
        <div class="f-fs-1 f-color-text font-weight-500 ml-3">
          <span class="f-color-service">Тариф действует:</span>
          <br>
          <span class="font-weight-700">

            <span data-bind="text: fromDate"></span> – до
            <span data-bind="text: toDate"></span>
          </span>
        </div>
      </div>
    </div>
  </div>

  <div class="info-row border-bottom" data-bind="using: tariff">
    <!-- название тарифа -->
    <div class="info-row__section d-none d-lg-flex">
      <div>
        <div style="font-size: 19px;" class="mb-1">
          <span class="bold" data-bind="text: tariffName"></span>
        </div>
        <div class="f-fs-1 f-color-text font-weight-500">
          <span class="f-color-service">Тариф действует:</span>
          <br>
          <span class="font-weight-700">

            <span data-bind="text: fromDate"></span> – до
            <span data-bind="text: toDate"></span>
          </span>
        </div>
      </div>
    </div>

    <!-- стоимость тарифа -->
    <div class="info-row__section  flex-grow-1 flex-lg-grow-0 f-border-right">
      <div class="stats-item">
        <div class="stats-item__value f-color-blue">
          <div class="d-flex align-items-center">

            <!-- ko if: cost -->
            <span>


              <!-- ko text: (cost() || '').toLocaleString() -->
              <!-- /ko -->
              ₽
            </span>
            <!-- /ko -->

            <!-- ko ifnot: cost -->
            <span>Бесплатно</span>
            <!-- /ko -->

            <!-- ko if: canEdit -->
            <button type="button" class="ml-1 f-icon f-icon-button f-icon--pencil" data-bind="click: editCost, tooltip" title="Изменить стоимость тарифа">
              <svg>
                <use href="#pencil-icon"></use>
              </svg>
            </button>
            <!-- /ko -->

          </div>
        </div>
        <div class="stats-item__label">

          Стоимость тарифа

        </div>
      </div>
    </div>

    <!-- лимит на кол-во ответов -->
    <div class="info-row__section flex-grow-1 flex-lg-grow-0">
      <div class="stats-item">
        <div class="stats-item__value f-color-violet">
          <div class="d-flex align-items-center">
            <span data-bind="text: answersUnlimited() ? 'Без ограничений' : ((answersLimit() || '').toLocaleString())">
            </span>

            <!-- ko if: canEdit -->
            <button type="button" class="ml-1 f-icon f-icon-button f-icon--pencil" data-bind="click: editAnswersLimit, tooltip" title="Изменить количество анкет">
              <svg>
                <use href="#pencil-icon"></use>
              </svg>
            </button>
            <!-- /ko -->
          </div>
        </div>
        <div class="stats-item__label">Лимит на кол-во<br>ответов</div>
      </div>
    </div>

    <!-- ответов осталось/использовано -->
    <div class="info-row__section flex-grow-1 flex-lg-grow-0">
      <div class="stats-item">
        <div class="stats-item__value f-color-blue">
          <div class="d-flex align-items-center">
            <!-- ko if: updatingAnswersCount -->
            <i class="fa fa-spinner fa-pulse color-active f-fs-2"></i>
            <!-- /ko -->

            <!-- ko ifnot: updatingAnswersCount -->
            <span>
              <!-- ko text: answersUnlimited() ? 'Без ограничений' : answersCount().toLocaleString() -->
              <!-- /ko -->
            </span>
            <span class="font-weight-normal f-fs-2" style="margin-top: 2px">/<span data-bind="text: answersUsed() || 0"></span>
            </span>
            <!-- /ko -->




            <button type="button" class="f-icon f-icon-button f-icon--repeats" data-bind="click: updateAnswersCount, disable: updatingAnswersCount">
              <svg>
                <use href="#repeats-icon"></use>
              </svg>
            </button>

          </div>
        </div>
        <div class="stats-item__label">
          <div class="d-flex align-items-center">
            <div>
              Ответов осталось/использовано<br>
              <span class="f-color-text font-weight-700">до
                <!-- ko text: toDate -->
                <!-- /ko -->
              </span>
            </div>
            <button class="btn-question" data-bind="tooltip" title="Заполненная анкета - это хотя бы один ответ на вопрос или жалоба"></button>
          </div>

        </div>
      </div>
    </div>


    <!-- стоимость ответов сверх лимита -->
    <div class="info-row__section  flex-grow-1 flex-lg-grow-0 f-border-right">
      <div class="stats-item">
        <div class="stats-item__value f-color-red">
          <div class="d-flex align-items-center">

            <!-- ko if: answerCost -->
            <span>
              <!-- ko text: (answerCost() || '').toLocaleString() -->
              <!-- /ko -->
              ₽
            </span>
            <!-- /ko -->

            <!-- ko ifnot: answerCost -->
            <span>Не установлено</span>
            <!-- /ko -->

            <!-- ko if: canEdit -->
            <button type="button" class="ml-1 f-icon f-icon-button f-icon--pencil" data-bind="click: editAnswerCost, tooltip" title="Изменить стоимость ответа">
              <svg>
                <use href="#pencil-icon"></use>
              </svg>
            </button>
            <!-- /ko -->

          </div>
        </div>
        <div class="stats-item__label">

          Стоимость ответов<br>сверх лимита

        </div>
      </div>
    </div>





    <?php /*
    <div class="info-row__section flex-grow-1 flex-lg-grow-0">
      <div class="stats-item">
        <div class="stats-item__value f-color-mint">
          <div class="d-flex align-items-center">
            <span data-bind="text: mailsUnlimited() ? 'Без ограничений' : mailsLimit().toLocaleString() ">
            </span>

            <!-- ko if: canEdit -->
            <button type="button" class="ml-1 f-icon f-icon-button f-icon--pencil" data-bind="click: editMailsLimit, tooltip" title="Изменить количество писем">
              <svg>
                <use href="#pencil-icon"></use>
              </svg>
            </button>
            <!-- /ko -->
          </div>
        </div>
        <div class="stats-item__label">Лимит на кол-во<br>писем</div>
      </div>
    </div>

    <div class="info-row__section flex-grow-1 flex-lg-grow-0">
      <div class="stats-item">
        <div class="stats-item__value f-color-gold">
          <div class="d-flex align-items-center">
            <!-- ko if: updatingMailsCount -->
            <i class="fa fa-spinner fa-pulse color-active f-fs-2"></i>
            <!-- /ko -->

            <!-- ko ifnot: updatingMailsCount -->
            <!-- ko text: mailsUnlimited() ? 'Без ограничений' : mailsCount().toLocaleString()  -->
            <!-- /ko -->
            <!-- /ko -->

            <!-- ko ifnot: mailsUnlimited  -->
            <button type="button" class="f-icon f-icon-button f-icon--repeats" data-bind="click: updateMailsCount, disable: updatingMailsCount">
              <svg>
                <use href="#repeats-icon"></use>
              </svg>
            </button>
            <!-- /ko -->
          </div>
        </div>
        <div class="stats-item__label">

          Писем осталось<br>
          <span class="f-color-text font-weight-700">до
            <!-- ko text: toDate -->
            <!-- /ko -->
          </span>


        </div>
      </div>
    </div>
    */ ?>

    <div class="info-row__section flex-grow-1 flex-lg-grow-0">
      <div class="stats-item">
        <div class="stats-item__value f-color-teal">
          <div class="d-flex align-items-center">
            <span data-bind="text: userLimit() === 0 ? 'Без ограничений' : userLimit()"></span>
            <!-- ko if: canEdit -->
            <button type="button" class="ml-1 f-icon f-icon-button f-icon--pencil" data-bind="click: editUsersLimit, tooltip" title="Изменить количество пользователей">
              <svg>
                <use href="#pencil-icon"></use>
              </svg>
            </button>
            <!-- /ko -->
          </div>
        </div>
        <div class="stats-item__label">
          Кол-во<br>пользователей
        </div>
      </div>
    </div>

    <div class="info-row__section flex-grow-1 flex-lg-grow-0">
      <div class="stats-item">
        <div class="stats-item__value f-color-primary">
          <div class="d-flex align-items-center">
            <span data-bind="text: bonuses().toLocaleString()"></span>
          </div>
        </div>
        <div class="stats-item__label">Бонусный счет<br>&nbsp;</div>
      </div>
    </div>

  </div>
</template>

<template id="tariff-blocked-message-template">

  <div class="f-color-danger">
    Тариф <span class="bold" data-bind="text: tariff.tariffName"></span> заблокирован до <span class="bold" data-bind="text: tariff.toDate"></span>
  </div>
  <div>Количество заполненных анкет в месяц превысило <span data-bind="text: tariff.answersLimit"></span>.
    Для продолжения работы сервиса необходимо изменить тариф.</div>
  </div>

</template>

<template id="tariff-mails-blocked-message-template">

  <div class="f-color-danger">
    Отправка писем по каналу Email заблокирована.
  </div>
  <div>Количество отправленных писем в месяц по каналу Email превысило <span data-bind="text: tariff.mailsLimit"></span>.
    Для продолжения использования отправки писем через Email свяжитесь с администратором.</div>
  </div>

</template>


<template id="change-limit-modal-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title">Изменить лимит</h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: function() {
        $component.close();
      }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body initializing" data-bind="css: { initializing: initializing}">
      <div class="form-group">
        <label class="form-label" data-bind="text: limitType == 'mails' ? 'Лимит на количество писем' : 'Лимит на количество заполненных анкет'"></label>

        <input data-bind="textInput: limit,
        disable: blocked,
        css: {
            'is-invalid': formControlErrorStateMatcher(limit)
          },
        inputMask: {
            alias: 'integer',
            rightAlign: false,
            groupSeparator: ' ',

            autoGroup: true,
            allowPlus: false,
            allowMinus: false,
          }" type="text" class="form-control">

        <!-- ko template: {
          foreach: formControlErrorStateMatcher(limit),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
        <div class="form-error" data-bind="text: $parent.limit.error()"></div>
        <!-- /ko -->
      </div>

      <div class="form-group sg-form-group switch-form-group">

        <label class="switch form-control">
          <input type="checkbox" data-bind="checked: unlimit" id="switch-input">
          <span class="switch__slider"></span>
        </label>
        <label class="form-label" for="switch-input" data-bind="css: {
          'form-label_checked': unlimit
        }">Безлимитный</label>
      </div>
    </div>


    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="f-btn f-btn-link" data-bind="click: function() { $component.close(); }">
          Отменить
        </button>

        <button type="submit" class="f-btn" data-bind="click: submit">
          Сохранить
        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="change-limit-modal-dialog-template">
  <div class="modal-dialog modal-dialog-md modal-dialog-centered change-limit-modal-dialog" data-bind="component: {
      name: 'change-limit-modal',
      params: {
        data: data,
        modalElement: modalElement,
        close: close,
      }
    }" role="document">
  </div>
</template>

<template id="change-user-limit-modal-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title">Изменить количество пользователей</h2>
      <button
        type="button"
        class="close"
        aria-label="Close"
        data-bind="
          click: function() {
            $component.close();
          }
        "
      >
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body initializing" data-bind="css: { initializing: initializing}">
      <div class="form-group">
        <label class="form-label">
          Кол-во пользователей
        </label>
        <input
          data-bind="
            textInput: limit,
            disable: blocked,
            css: {
              'is-invalid': formControlErrorStateMatcher(limit)
            },
            inputMask: {
              alias: 'integer',
              rightAlign: false,
              groupSeparator: ' ',

              autoGroup: true,
              allowPlus: false,
              allowMinus: false,
            },
            attr: {
              disabled: unlimit,
            },
          "
          type="text"
          class="form-control"
        />
        <!-- ko template: {
          foreach: formControlErrorStateMatcher(limit),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200)
        } -->
        <div class="form-error" data-bind="text: $parent.limit.error()"></div>
        <!-- /ko -->
      </div>
      <div class="form-group sg-form-group switch-form-group">
        <label class="switch form-control">
          <input type="checkbox" data-bind="checked: unlimit" id="switch-input">
          <span class="switch__slider"></span>
        </label>
        <label
          class="form-label"
          for="switch-input"
          data-bind="
            css: {
              'form-label_checked': unlimit
            }
          "
        >
          Без ограничений
        </label>
      </div>
    </div>
    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="f-btn f-btn-link" data-bind="click: function() { $component.close(); }">
          Отменить
        </button>
        <button
          type="submit"
          class="f-btn"
          data-bind="
            click: submit,
            css: {
              'f-btn-success': isFormValid,
            },
          "
        >
          Сохранить
        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="change-user-limit-modal-dialog-template">
  <div
    class="modal-dialog modal-dialog-md modal-dialog-centered change-limit-modal-dialog"
    data-bind="
      component: {
        name: 'change-user-limit-modal',
        params: {
          data: data,
          modalElement: modalElement,
          close: close,
        },
      }
    "
    role="document"
  ></div>
</template>
