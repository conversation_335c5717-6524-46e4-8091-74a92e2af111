<?= $this->render('./controllers/resources/template.php'); ?>


<template id="basic-repeat-nomedia-template">
  <div class="settings__conn-channels-channel-repeat">
    <!-- ko ifnot: blocked -->
    <button type="submit" class="btn btn-danger settings__conn-channels-channel-repeat-remove-button" title="Удалить" data-bind="click: function() { channel.removeRepeat(repeat); }">
    </button>
    <!-- /ko -->



    <div class="row">

      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <div class="settings__conn-channels-channel-row">
          <div class="settings__conn-channels-channel-basic-message-form-group mb-30p" data-bind="basicEditor">

            <label class="form-label">Текст сообщения</label>

            <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'С помощью переменных, указанных под текстом сообщения, можно добавить: ' + channel.getVariablesString() + '. Также можно в текст сообщения добавить изображение'
                },
                tooltip
              ">
            </button>

            <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: repeat.message().length">
              <textarea class="form-control" maxlength="500" data-bind="textInput: repeat.message, css: {
                'is-invalid': channel.formControlErrorStateMatcher(repeat.message),
                'is-valid': channel.formControlSuccessStateMatcher(repeat.message),
                }, disable: blocked">
              </textarea>

              <div class="chars-counter__value"></div>
            </div>

            <!-- ko template: {
                foreach: channel.formControlErrorStateMatcher(repeat.message),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
            } -->
            <div class="form-error" data-bind="text: $parent.repeat.message.error()"></div>
            <!-- /ko -->

            <!-- ko ifnot: blocked -->
            <div class="settings__variables">
              <!-- ko foreach: channel.variables -->
              <div class="settings__variable" data-bind="text: $parent.channel.getVariableLabel($data, $parent.repeat),
                click: function($data, event) {
                  console.log('repeat var click', $data, $parent);
                $parent.channel.getVariableAction($data, event, $parent.repeat);
              }"></div>
              <!-- /ko -->

            </div>
            <!-- /ko -->


          </div>

        </div>
      </div>

      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <div class="form-group">
          <label class="form-label">Задержка</label>

          <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': channel.isAuto ? 'Время после срабатывания триггера, по истечении которого будет отправлено сообщение' : 'Время после запуска рассылки, по истечении которого будет отправлено сообщение'
                },
                tooltip
              ">
          </button>

          <!-- ko component: {
            name: 'delay-group',
            params: {
                days: repeat.delayDays,
                time: repeat.delayTime,
                channel: channel,
                disabled: blocked
            }
        } -->
          <!-- /ko -->
        </div>
      </div>
    </div>

    <dnd-cover class="mx-n3" params="type: 'image'"></dnd-cover>
  </div>
</template>

<template id="basic-channel-nomedia-template">

  <!-- ko if: channel.withSubject -->
  <div class="row">
    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
      <div class="form-group">
        <label class="form-label">Отправитель</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'Выберите отправителя из списка. Добавить нового отправителя можно в разделе &quot;Каналы связи&quot;, который находится в настройках компании'
                },
                tooltip
              ">
        </button>

        <!-- ko if: channel.sendersLoaded -->

        <div class="select2-wrapper" data-bind="css: {
                        'is-invalid': channel.formControlErrorStateMatcher(channel.sender),
                        'is-valid': channel.formControlSuccessStateMatcher(channel.sender),
                        }">
          <select data-bind="
                            value: channel.sender,
                            valueAllowUnset: true,
                            disable: blocked,
                            lazySelect2: {
                                containerCssClass: 'form-control',
                                wrapperCssClass: 'select2-container--form-control',
                            }
                        ">
            <!-- ko foreach: channel.senders -->
            <option data-bind="value: $data.id">
              <!-- ko text: $data.name -->
              <!-- /ko -->
            </option>
            <!-- /ko -->
          </select>

          <!-- ko template: {
                        foreach: channel.formControlErrorStateMatcher(channel.sender),
                            afterAdd: fadeAfterAddFactory(200),
                            beforeRemove: fadeBeforeRemoveFactory(200)
                        } -->
          <div class="form-error" data-bind="text: $parent.channel.sender.error()"></div>
          <!-- /ko -->
        </div>

        <!-- /ko -->
      </div>
    </div>
    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
      <div class="form-group">
        <label class="form-label">Тема</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
            attr: {
              'data-original-title': 'Укажите тему письма',
            },
            tooltip
          ">
        </button>

        <div class="chars-counter chars-counter--type_input" data-bind="settingsCharsCounter: channel.subject">
          <input class="form-control" data-bind="textInput: channel.subject, css: {
                            'is-invalid': channel.formControlErrorStateMatcher(channel.subject),
                            'is-valid': channel.formControlSuccessStateMatcher(channel.subject),
                        }, disable: blocked," maxlength="150">
          <div class="chars-counter__value"></div>
        </div>

        <!-- ko template: {
                            foreach: channel.formControlErrorStateMatcher(channel.subject),
                            afterAdd: fadeAfterAddFactory(200),
                            beforeRemove: fadeBeforeRemoveFactory(200)
                        } -->
        <div class="form-error" data-bind="text: $parent.channel.subject.error()"></div>
        <!-- /ko -->
      </div>
    </div>
  </div>
  <!-- /ko -->
  <div class="row">
    <div class="col-6">
      <div class="settings__conn-channels-channel-row">
        <div class="form-group settings__conn-channels-channel-basic-message-form-group" data-bind="basicEditor">
          <label class="form-label">Текст сообщения</label>

          <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'С помощью переменных, указанных под текстом сообщения, можно добавить: ' + channel.getVariablesString() + '. Также можно в текст сообщения добавить изображение'
                },
                tooltip
              ">
          </button>

          <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: channel.message().length">
            <textarea class="form-control" maxlength="500" data-bind="textInput: channel.message, css: {
                            'is-invalid': channel.formControlErrorStateMatcher(channel.message),
                            'is-valid': channel.formControlSuccessStateMatcher(channel.message),
                        }, disable: blocked"></textarea>

            <div class="chars-counter__value"></div>
          </div>

          <!-- ko template: {
              foreach: channel.formControlErrorStateMatcher(channel.message),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
          <div class="form-error" data-bind="text: $parent.channel.message.error()"></div>
          <!-- /ko -->

          <!-- ko ifnot: blocked -->
          <div class="settings__variables">
            <!-- ko foreach: channel.variables -->
            <div class="settings__variable" data-bind="text: $parent.channel.getVariableLabel($data, $parent.channel),
            click: function($data, event) {

              $parent.channel.getVariableAction($data, event, $parent.channel);
            }"></div>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>

      </div>
    </div>

    <div class="col-6">
      <!-- ko ifnot: channel.withSubject -->
      <div class="form-group">
        <label class="form-label">Отправитель</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'Выберите отправителя из списка. Добавить нового отправителя можно в разделе &quot;Каналы связи&quot;, который находится в настройках компании'
                },
                tooltip
              ">
        </button>

        <!-- ko if: channel.sendersLoaded -->

        <div class="select2-wrapper" data-bind="css: {
                        'is-invalid': channel.formControlErrorStateMatcher(channel.sender),
                        'is-valid': channel.formControlSuccessStateMatcher(channel.sender),
                        }">
          <select data-bind="
                            value: channel.sender,
                            valueAllowUnset: true,
                            disable: blocked,
                            lazySelect2: {
                                containerCssClass: 'form-control',
                                wrapperCssClass: 'select2-container--form-control',
                            }
                        ">
            <!-- ko foreach: channel.senders -->
            <option data-bind="value: $data.id">
              <!-- ko text: $data.name -->
              <!-- /ko -->
            </option>
            <!-- /ko -->
          </select>

          <!-- ko template: {
                        foreach: channel.formControlErrorStateMatcher(channel.sender),
                            afterAdd: fadeAfterAddFactory(200),
                            beforeRemove: fadeBeforeRemoveFactory(200)
                        } -->
          <div class="form-error" data-bind="text: $parent.channel.sender.error()"></div>
          <!-- /ko -->
        </div>

        <!-- /ko -->
      </div>
      <!-- /ko -->

      <!-- ko ifnot: channel.config.hideDelay -->
      <div class="form-group">
        <label class="form-label">Задержка</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': channel.isAuto ? 'Время после срабатывания триггера, по истечении которого будет отправлено сообщение' : 'Время после запуска рассылки, по истечении которого будет отправлено сообщение'
                },
                tooltip
              ">
        </button>

        <!-- ko component: {
            name: 'delay-group',
            params: {
                days: channel.delayDays,
                time: channel.delayTime,
                channel: channel,
                disabled: blocked,
            }
        } -->
        <!-- /ko -->
      </div>
      <!-- /ko -->


      <!-- ko if: channel.isRequestProcessingEnabled -->
      <div class="form-group">
        <fc-label params="text: 'Время на обработку анкет', hint: 'Время на обработку анкет'"></fc-label>

        <fc-days-time params="days: channel.processingTime.days, time: channel.processingTime.time, fullLabel: true, showErrors: channel.showErrors, disabled: blocked"></fc-days-time>
        <div class="f-fs-1 f-color-service mt-2">При незаполненном параметре используется время для обработки анкет из настроек опроса</div>
      </div>
      <!-- /ko -->
    </div>

    <dnd-cover class="mt-n4" params="type: 'image'"></dnd-cover>
  </div>

  <!-- ko if: channel.config.repeats -->
  <!-- ko if: !blocked || channel.repeats().length > 0 -->
  <div class="settings__conn-channels-channel-repeats">
    <div class="settings__conn-channels-channel-repeats-header">
      <div class="settings__conn-channels-channel-repeats-leader">
        <div class="settings__conn-channels-channel-repeats-title">
          Повторы
          <!-- ko if: channel.repeats().length > 0 -->
          <span class="settings__conn-channels-channel-repeats-count" data-bind="text: channel.repeats().length">
          </span>
          <!-- /ko -->
        </div>

        <div class="settings__conn-channels-channel-repeats-sub-title">
          Отправка повторного сообщения, если по предыдущему сообщению не было совершено целевого действия
        </div>

        <!-- ko ifnot: blocked -->
        <button type="submit" class="f-btn f-btn-text f-btn-success" data-bind="click: function() { channel.addRepeat(); }">
          <span class="f-btn-prepend">
            <svg-icon params="name: 'plus'"></svg-icon>
          </span>
          Добавить повтор
        </button>
        <!-- /ko -->

      </div>


    </div>

    <!-- ko foreach: {
      data: channel.repeats,
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
    <!-- ko template: {
        name: 'basic-repeat-nomedia-template',
        data: {
          repeat: $data,
          channel: $parent.channel,
          blocked: $parent.blocked
        }
      } -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: !blocked && channel.repeats().length > 0 -->
    <div>
      <hr class="mt-0">
      <button type="submit" class="f-btn f-btn-text f-btn-success" data-bind="click: function() { channel.addRepeat(); }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'plus'"></svg-icon>
        </span>
        Добавить повтор
      </button>
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /ko -->
</template>



<template id="telegram-repeat-template">
  <div class="settings__conn-channels-channel-repeat" data-bind="dnd: function(files) {
    repeat.imageLoader.loadFile(files[0]);
                    }, dndDisabled: blocked || repeat.imageLoader.preview()">
    <!-- ko ifnot: blocked -->
    <button type="submit" class="btn btn-danger settings__conn-channels-channel-repeat-remove-button" title="Удалить" data-bind="click: function() { channel.removeRepeat(repeat); }">
    </button>
    <!-- /ko -->

    <div class="row">

      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <div class="settings__conn-channels-channel-row">
          <div class="settings__conn-channels-channel-basic-message-form-group mb-30p" data-bind="basicEditor">

            <label class="form-label">Текст сообщения</label>

            <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'С помощью переменных, указанных под текстом сообщения, можно добавить: ' + channel.getVariablesString() + '. Также можно в текст сообщения добавить изображение'
                },
                tooltip
              ">
            </button>

            <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: repeat.message().length">
              <textarea class="form-control" maxlength="500" data-bind="textInput: repeat.message, css: {
                'is-invalid': channel.formControlErrorStateMatcher(repeat.message),
                'is-valid': channel.formControlSuccessStateMatcher(repeat.message),
                }, disable: blocked">
              </textarea>

              <div class="chars-counter__value"></div>
            </div>

            <!-- ko template: {
                foreach: channel.formControlErrorStateMatcher(repeat.message),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
            } -->
            <div class="form-error" data-bind="text: $parent.repeat.message.error()"></div>
            <!-- /ko -->

            <!-- ko ifnot: blocked -->
            <div class="settings__variables">

              <!-- ko foreach: channel.variables -->
              <div class="settings__variable" data-bind="text: $parent.channel.getVariableLabel($data, $parent.repeat),
                click: function($data, event) {
                  console.log('click repeat variable', $data, $parent.repeat);
                $parent.channel.getVariableAction($data, event, $parent.repeat);
              }"></div>
              <!-- /ko -->

            </div>
            <!-- /ko -->


          </div>

          <!-- ko if: !blocked || repeat.imageLoader.preview() -->
          <div class="settings__conn-channels-channel-image-picker">
            <media-load-button params="loader: repeat.imageLoader, disabled: blocked">
              <svg-icon params="name: 'clip'" class="mb-10p"></svg-icon>


              .jpg .png<br>изображение



            </media-load-button>

            <file-loader-error style="width: 105px" params="error: repeat.imageLoader.error"></file-loader-error>
          </div>
          <!-- /ko -->
        </div>
      </div>

      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <div class="form-group">
          <label class="form-label">Задержка</label>

          <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': channel.isAuto ? 'Время после срабатывания триггера, по истечении которого будет отправлено сообщение' : 'Время после запуска рассылки, по истечении которого будет отправлено сообщение'
                },
                tooltip
              ">
          </button>

          <!-- ko component: {
            name: 'delay-group',
            params: {
                days: repeat.delayDays,
                time: repeat.delayTime,
                channel: channel,
                disabled: blocked
            }
        } -->
          <!-- /ko -->
        </div>
      </div>
    </div>

    <dnd-cover class="mx-n3" params="type: 'image'"></dnd-cover>
  </div>
</template>

<template id="telegram-channel-template">

  <div class="row" data-bind=" dnd: function(files) {
                channel.imageLoader.loadFile(files[0]);
            }, dndDisabled: blocked || channel.imageLoader.preview()">
    <div class="col-6">
      <div class="settings__conn-channels-channel-row">
        <div class="form-group settings__conn-channels-channel-basic-message-form-group" data-bind="basicEditor">
          <label class="form-label">Текст сообщения</label>

          <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'С помощью переменных, указанных под текстом сообщения, можно добавить: ' + channel.getVariablesString() + '. Также можно в текст сообщения добавить изображение'
                },
                tooltip
              ">
          </button>

          <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: channel.message().length">
            <textarea class="form-control" maxlength="500" data-bind="textInput: channel.message, css: {
                            'is-invalid': channel.formControlErrorStateMatcher(channel.message),
                            'is-valid': channel.formControlSuccessStateMatcher(channel.message),
                        }, disable: blocked"></textarea>

            <div class="chars-counter__value"></div>
          </div>

          <!-- ko template: {
              foreach: channel.formControlErrorStateMatcher(channel.message),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
          <div class="form-error" data-bind="text: $parent.channel.message.error()"></div>
          <!-- /ko -->

          <!-- ko ifnot: blocked -->
          <div class="settings__variables">
            <!-- ko foreach: channel.variables -->
            <div class="settings__variable" data-bind="text: $parent.channel.getVariableLabel($data, $parent.channel),
            click: function($data, event) {
              $parent.channel.getVariableAction($data, event, $parent.channel);
            }"></div>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>

        <!-- ko if: !blocked || channel.imageLoader.preview() -->
        <div class="settings__conn-channels-channel-image-picker ">
          <media-load-button params="loader: channel.imageLoader, disabled: blocked">
            <svg-icon params="name: 'clip'" class="mb-10p"></svg-icon>



            .jpg .png<br>изображение


          </media-load-button>

          <file-loader-error style="width: 105px" params="error: channel.imageLoader.error"></file-loader-error>
        </div>
        <!-- /ko -->

      </div>
    </div>

    <div class="col-6">
      <div class="form-group">
        <label class="form-label">Отправитель</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'Выберите отправителя из списка. Добавить нового отправителя можно в разделе &quot;Каналы связи&quot;, который находится в настройках компании'
                },
                tooltip
              ">
        </button>

        <!-- ko if: channel.sendersLoaded -->

        <div class="select2-wrapper" data-bind="css: {
                        'is-invalid': channel.formControlErrorStateMatcher(channel.sender),
                        'is-valid': channel.formControlSuccessStateMatcher(channel.sender),
                        }">
          <select data-bind="
                            value: channel.sender,
                            valueAllowUnset: true,
                            disable: blocked,
                            lazySelect2: {
                                containerCssClass: 'form-control',
                                wrapperCssClass: 'select2-container--form-control',
                            }
                        ">
            <!-- ko foreach: channel.senders -->
            <option data-bind="value: $data.id">
              <!-- ko text: $data.name -->
              <!-- /ko -->
            </option>
            <!-- /ko -->
          </select>

          <!-- ko template: {
                        foreach: channel.formControlErrorStateMatcher(channel.sender),
                            afterAdd: fadeAfterAddFactory(200),
                            beforeRemove: fadeBeforeRemoveFactory(200)
                        } -->
          <div class="form-error" data-bind="text: $parent.channel.sender.error()"></div>
          <!-- /ko -->
        </div>

        <!-- /ko -->
      </div>

      <!-- ko ifnot: channel.config.hideDelay -->
      <div class="form-group">
        <label class="form-label">Задержка</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': channel.isAuto ? 'Время после срабатывания триггера, по истечении которого будет отправлено сообщение' : 'Время после запуска рассылки, по истечении которого будет отправлено сообщение'
                },
                tooltip
              ">
        </button>

        <!-- ko component: {
            name: 'delay-group',
            params: {
                days: channel.delayDays,
                time: channel.delayTime,
                channel: channel,
                disabled: blocked
            }
        } -->
        <!-- /ko -->
      </div>
      <!-- /ko -->

      <!-- ko if: channel.isRequestProcessingEnabled -->
      <div class="form-group">
        <fc-label params="text: 'Время на обработку анкет', hint: 'Время на обработку анкет'"></fc-label>

        <fc-days-time params="days: channel.processingTime.days, time: channel.processingTime.time, fullLabel: true, showErrors:channel.showErrors, disabled: blocked"></fc-days-time>
        <div class="f-fs-1 f-color-service mt-2">При незаполненном параметре используется время для обработки анкет из настроек опроса</div>
      </div>
      <!-- /ko -->
    </div>

    <dnd-cover class="mt-n4" params="type: 'image'"></dnd-cover>
  </div>

  <!-- ko if: channel.config.repeats -->
  <!-- ko if: !blocked || channel.repeats().length > 0 -->
  <div class="settings__conn-channels-channel-repeats">
    <div class="settings__conn-channels-channel-repeats-header">
      <div class="settings__conn-channels-channel-repeats-leader">
        <div class="settings__conn-channels-channel-repeats-title">
          Повторы
          <!-- ko if: channel.repeats().length > 0 -->
          <span class="settings__conn-channels-channel-repeats-count" data-bind="text: channel.repeats().length">
          </span>
          <!-- /ko -->
        </div>

        <div class="settings__conn-channels-channel-repeats-sub-title">
          Отправка повторного сообщения, если по предыдущему сообщению не было совершено целевого действия
        </div>


        <!-- ko ifnot: blocked -->
        <button type="submit" class="f-btn f-btn-text f-btn-success" data-bind="click: function() { channel.addRepeat(); }">
          <span class="f-btn-prepend">
            <svg-icon params="name: 'plus'"></svg-icon>
          </span>
          Добавить повтор
        </button>
        <!-- /ko -->

      </div>


    </div>

    <!-- ko foreach: {
      data: channel.repeats,
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->

    <!-- ko template: {
        name: 'telegram-repeat-template',
        data: {
          repeat: $data,
          channel: $parent.channel,
          blocked: $parent.blocked
        }
      } -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: !blocked && channel.repeats().length > 0 -->
    <div>
      <hr class='mt-0'>
      <button type="submit" class="f-btn f-btn-text f-btn-success" data-bind="click: function() { channel.addRepeat(); }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'plus'"></svg-icon>
        </span>
        Добавить повтор
      </button>
    </div>
    <!-- /ko -->

  </div>
  <!-- /ko -->
  <!-- /ko -->
</template>


<template id="push-repeat-template">
  <div class="settings__conn-channels-channel-repeat">
    <!-- ko ifnot: blocked -->
    <button type="submit" class="btn btn-danger settings__conn-channels-channel-repeat-remove-button" title="Удалить" data-bind="click: function() { channel.removeRepeat(repeat); }">
    </button>
    <!-- /ko -->



    <div class="row">

      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <div class="settings__conn-channels-channel-row">
          <div class="settings__conn-channels-channel-basic-message-form-group mb-30p" data-bind="basicEditor">

            <label class="form-label">Текст сообщения</label>

            <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'С помощью переменных, указанных под текстом сообщения, можно добавить: ' + channel.getVariablesString() + '. Также можно в текст сообщения добавить изображение'
                },
                tooltip
              ">
            </button>

            <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: repeat.message().length">
              <textarea class="form-control" maxlength="500" data-bind="textInput: repeat.message, css: {
                'is-invalid': channel.formControlErrorStateMatcher(repeat.message),
                'is-valid': channel.formControlSuccessStateMatcher(repeat.message),
                }, disable: blocked">
              </textarea>

              <div class="chars-counter__value"></div>
            </div>

            <!-- ko template: {
                foreach: channel.formControlErrorStateMatcher(repeat.message),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
            } -->
            <div class="form-error" data-bind="text: $parent.repeat.message.error()"></div>
            <!-- /ko -->

            <!-- ko ifnot: blocked -->
            <div class="settings__variables">
              <!-- ko foreach: channel.variables -->
              <div class="settings__variable" data-bind="text: $parent.channel.getVariableLabel($data, $parent.repeat),
                click: function($data, event) {
                  console.log('repeat var click', $data, $parent);
                $parent.channel.getVariableAction($data, event, $parent.repeat);
              }"></div>
              <!-- /ko -->

            </div>
            <!-- /ko -->


          </div>

        </div>
      </div>

      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6" style="padding-right: 70px">
        <div class="form-group">
          <fc-label params="text: 'Действие по клику на уведомление', hint: 'Действие по клику на уведомление', optional: true"></fc-label>



          <input class="form-control" data-bind="textInput: repeat.link, disable: blocked,">


        </div>

        <div class="form-group settings__conn-channels-channel-basic-message-form-group">
          <fc-label params="text: 'Полезная нагрузка сообщения', hint: 'Полезная нагрузка сообщения', optional: true"></fc-label>


          <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: repeat.payload().length">
            <textarea class="form-control" maxlength="500" data-bind="textInput: repeat.payload, disable: blocked"></textarea>

            <div class="chars-counter__value"></div>
          </div>

        </div>

        <!-- ko ifnot: channel.config.hideDelay -->
        <div class="form-group">
          <label class="form-label">Задержка</label>

          <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': channel.isAuto ? 'Время после срабатывания триггера, по истечении которого будет отправлено сообщение' : 'Время после запуска рассылки, по истечении которого будет отправлено сообщение'
                },
                tooltip
              ">
          </button>

          <!-- ko component: {
            name: 'delay-group',
            params: {
                days: repeat.delayDays,
                time: repeat.delayTime,
                channel: channel,
                disabled: blocked
            }
        } -->
          <!-- /ko -->
        </div>
        <!-- /ko -->
      </div>
    </div>

  </div>
</template>

<template id="push-channel-template">


  <div class="row">
    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
      <div class="form-group">
        <label class="form-label">Отправитель</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'Выберите отправителя из списка. Добавить нового отправителя можно в разделе &quot;Каналы связи&quot;, который находится в настройках компании'
                },
                tooltip
              ">
        </button>

        <!-- ko if: channel.sendersLoaded -->

        <div class="select2-wrapper" data-bind="css: {
                        'is-invalid': channel.formControlErrorStateMatcher(channel.sender),
                        'is-valid': channel.formControlSuccessStateMatcher(channel.sender),
                        }">
          <select data-bind="
                            value: channel.sender,
                            valueAllowUnset: true,
                            disable: blocked,
                            lazySelect2: {
                                containerCssClass: 'form-control',
                                wrapperCssClass: 'select2-container--form-control',
                            }
                        ">
            <!-- ko foreach: channel.senders -->
            <option data-bind="value: $data.id">
              <!-- ko text: $data.name -->
              <!-- /ko -->
            </option>
            <!-- /ko -->
          </select>

          <!-- ko template: {
                        foreach: channel.formControlErrorStateMatcher(channel.sender),
                            afterAdd: fadeAfterAddFactory(200),
                            beforeRemove: fadeBeforeRemoveFactory(200)
                        } -->
          <div class="form-error" data-bind="text: $parent.channel.sender.error()"></div>
          <!-- /ko -->
        </div>

        <!-- /ko -->
      </div>
    </div>
    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
      <div class="form-group">
        <fc-label params="text: 'Действие по клику на уведомление', hint: 'Действие по клику на уведомление', optional: true"></fc-label>


        <input class="form-control" data-bind="textInput: channel.link, disable: blocked,">


      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-6">
      <div class="form-group">
        <label class="form-label">Тема</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
            attr: {
              'data-original-title': 'Укажите тему письма',
            },
            tooltip
          ">
        </button>

        <div class="chars-counter chars-counter--type_input" data-bind="settingsCharsCounter: channel.subject">
          <input class="form-control" data-bind="textInput: channel.subject, css: {
                            'is-invalid': channel.formControlErrorStateMatcher(channel.subject),
                            'is-valid': channel.formControlSuccessStateMatcher(channel.subject),
                        }, disable: blocked," maxlength="150">
          <div class="chars-counter__value"></div>
        </div>

        <!-- ko template: {
                            foreach: channel.formControlErrorStateMatcher(channel.subject),
                            afterAdd: fadeAfterAddFactory(200),
                            beforeRemove: fadeBeforeRemoveFactory(200)
                        } -->
        <div class="form-error" data-bind="text: $parent.channel.subject.error()"></div>
        <!-- /ko -->
      </div>
      <div class="settings__conn-channels-channel-row">
        <div class="form-group settings__conn-channels-channel-basic-message-form-group" data-bind="basicEditor">
          <label class="form-label">Текст сообщения</label>

          <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': 'С помощью переменных, указанных под текстом сообщения, можно добавить: ' + channel.getVariablesString() + '. Также можно в текст сообщения добавить изображение'
                },
                tooltip
              ">
          </button>

          <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: channel.message().length">
            <textarea class="form-control" maxlength="500" data-bind="textInput: channel.message, css: {
                            'is-invalid': channel.formControlErrorStateMatcher(channel.message),
                            'is-valid': channel.formControlSuccessStateMatcher(channel.message),
                        }, disable: blocked"></textarea>

            <div class="chars-counter__value"></div>
          </div>

          <!-- ko template: {
              foreach: channel.formControlErrorStateMatcher(channel.message),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
          <div class="form-error" data-bind="text: $parent.channel.message.error()"></div>
          <!-- /ko -->

          <!-- ko ifnot: blocked -->
          <div class="settings__variables">
            <!-- ko foreach: channel.variables -->
            <div class="settings__variable" data-bind="text: $parent.channel.getVariableLabel($data, $parent.channel),
            click: function($data, event) {

              $parent.channel.getVariableAction($data, event, $parent.channel);
            }"></div>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>

      </div>
    </div>
    <div class="col-6">

      <div class="form-group settings__conn-channels-channel-basic-message-form-group">
        <fc-label params="text: 'Полезная нагрузка сообщения', hint: 'Полезная нагрузка сообщения', optional: true"></fc-label>


        <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: channel.payload().length">
          <textarea class="form-control" maxlength="500" data-bind="textInput: channel.payload, disable: blocked"></textarea>

          <div class="chars-counter__value"></div>
        </div>

      </div>

      <!-- ko ifnot: channel.config.hideDelay -->
      <div class="form-group">
        <label class="form-label">Задержка</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
                attr: {
                  'data-original-title': channel.isAuto ? 'Время после срабатывания триггера, по истечении которого будет отправлено сообщение' : 'Время после запуска рассылки, по истечении которого будет отправлено сообщение'
                },
                tooltip
              ">
        </button>

        <!-- ko component: {
            name: 'delay-group',
            params: {
                days: channel.delayDays,
                time: channel.delayTime,
                channel: channel,
                disabled: blocked,
            }
        } -->
        <!-- /ko -->
      </div>
      <!-- /ko -->

      <!-- ko if: channel.isRequestProcessingEnabled -->
      <div class="form-group">
        <fc-label params="text: 'Время на обработку анкет', hint: 'Время на обработку анкет'"></fc-label>

        <fc-days-time params="days: channel.processingTime.days, time: channel.processingTime.time, fullLabel: true, showErrors: channel.showErrors, disabled: blocked"></fc-days-time>
        <div class="f-fs-1 f-color-service mt-2">При незаполненном параметре используется время для обработки анкет из настроек опроса</div>
      </div>
      <!-- /ko -->
    </div>
  </div>

  <!-- ko if: channel.config.repeats -->
  <!-- ko if: !blocked || channel.repeats().length > 0 -->
  <div class="settings__conn-channels-channel-repeats">
    <div class="settings__conn-channels-channel-repeats-header">
      <div class="settings__conn-channels-channel-repeats-leader">
        <div class="settings__conn-channels-channel-repeats-title">
          Повторы
          <!-- ko if: channel.repeats().length > 0 -->
          <span class="settings__conn-channels-channel-repeats-count" data-bind="text: channel.repeats().length">
          </span>
          <!-- /ko -->
        </div>

        <div class="settings__conn-channels-channel-repeats-sub-title">
          Отправка повторного сообщения, если по предыдущему сообщению не было совершено целевого действия
        </div>

        <!-- ko ifnot: blocked -->
        <button type="submit" class="f-btn f-btn-text f-btn-success" data-bind="click: function() { channel.addRepeat(); }">
          <span class="f-btn-prepend">
            <svg-icon params="name: 'plus'"></svg-icon>
          </span>
          Добавить повтор
        </button>
        <!-- /ko -->

      </div>


    </div>

    <!-- ko foreach: {
      data: channel.repeats,
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200)
    } -->
    <!-- ko template: {
        name: 'push-repeat-template',
        data: {
          repeat: $data,
          channel: $parent.channel,
          blocked: $parent.blocked
        }
      } -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: !blocked && channel.repeats().length > 0 -->
    <div>
      <hr class="mt-0">
      <button type="submit" class="f-btn f-btn-text f-btn-success" data-bind="click: function() { channel.addRepeat(); }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'plus'"></svg-icon>
        </span>
        Добавить повтор
      </button>
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /ko -->
</template>

<template id="html-repeat-template">
  <div class="settings__conn-channels-channel-repeat">
    <!-- ko ifnot: blocked -->
    <button type="submit" class="btn btn-danger settings__conn-channels-channel-repeat-remove-button" title="Удалить" data-bind="click: function() { channel.removeRepeat(repeat); }">
    </button>
    <!-- /ko -->

    <div class="row">
      <div class="col-lg-9 col-md-9 col-sm-9 col-xs-9">

        <div class="form-group settings__conn-channels-channel-html-message-form-group ">




          <div class="d-flex justify-content-between align-items-center">
            <label class="form-label">
              Текст сообщения

              <!-- <button type="button" class="btn-question" data-placement="top" data-bind="
            attr: {
              'data-original-title': 'С помощью переменных, указанных под текстом письма, можно добавить ' + channel.getVariablesString()
            },
            tooltip
          "> -->
              </button>
            </label>

            <!-- ko ifnot: channel.isNewEditor() -->
            <div style="min-height: 24px">
              <!-- ko if: channel.mode == 'poll' -->
              <button class="button-ghost px-0 pb-1 f-fs-1-5 d-flex align-items-center" data-bind="click: function() { repeat.preview() }">
                <span class="f-color-primary mr-1">Предварительный просмотр</span>
                <svg-icon params="name: 'expand'" class="svg-icon--sm f-color-service ml-2"></svg-icon>
              </button>
              <!-- /ko -->
            </div>
            <!-- /ko -->



          </div>

          <!-- ko if: repeat.isNewEditor -->
          <div class="unlayer-wrapper">
            <fc-unlayer params="ref: repeat.newEditor, tools: repeat.newEditorTools, data: repeat.newEditorData, tags: repeat.newEditorTags"></fc-unlayer>
            <!-- ko template: {
              foreach: channel.formControlErrorStateMatcher(repeat.newEditorMessage),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
            <div class="form-error" data-bind="text: $parent.repeat.newEditorMessage.error()"></div>
            <!-- /ko -->
          </div>
          <!-- /ko -->

          <!-- ko ifnot: channel.isNewEditor -->
          <div class="tinymce-wrapper" data-bind="tinymce: repeat.message, tinymceIsEmpty: repeat.isEmpty, css: {
                    'is-invalid': channel.formControlErrorStateMatcher(repeat.message),
                    'is-valid': channel.formControlSuccessStateMatcher(repeat.message),
                }, readOnly: blocked">
            <textarea class="form-control editor__field"></textarea>

            <!-- ko template: {
              foreach: channel.formControlErrorStateMatcher(repeat.message),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
            <div class="form-error" data-bind="text: $parent.repeat.message.error()"></div>
            <!-- /ko -->

            <!-- ko ifnot: blocked -->
            <div class="settings__variables">
              <!-- ko foreach: channel.variables -->
              <div class="settings__variable" data-bind="text: $parent.channel.getVariableLabel($data, $parent.repeat),
              click: function($data, event) {
                $parent.channel.getVariableAction($data, event, $parent.repeat);
              }"></div>
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>
      </div>

      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3">
        <div class="form-group">
          <label class="form-label">Задержка</label>

          <button type="button" class="btn-question" data-placement="top" data-bind="
            attr: {
              'data-original-title': channel.isAuto ? 'Время после срабатывания триггера, по истечении которого будет отправлено сообщение' : 'Время после запуска рассылки, по истечении которого будет отправлено сообщение',
            },
            tooltip
          ">
          </button>

          <!-- ko component: {
                            name: 'delay-group',
                            params: {
                                days: repeat.delayDays,
                                time: repeat.delayTime,
                                channel: channel,
                                disabled: blocked
                            }
                        } -->
          <!-- /ko -->
        </div>
      </div>
    </div>
</template>

<template id="html-channel-template">

  <div class="row html-channel-template">
    <div class="col-6">
      <div class="form-group">
        <label class="form-label">Отправитель</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
            attr: {
              'data-original-title': 'Выберите отправителя из списка. Добавить нового отправителя можно в разделе &quot;Каналы связи&quot;, который находится в настройках компании',
            },
            tooltip
          ">
        </button>

        <!-- ko if: channel.sendersLoaded -->

        <div class="select2-wrapper" data-bind="css: {
              'is-invalid': channel.formControlErrorStateMatcher(channel.sender),
              'is-valid': channel.formControlSuccessStateMatcher(channel.sender),
          }">
          <select data-bind="
                value: channel.sender,
                valueAllowUnset: true,
                disable: blocked,
                lazySelect2: {
                    containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control',
                }
            ">
            <!-- ko foreach: channel.senders -->
            <option data-bind="value: $data.id, text: $data.name"></option>
            <!-- /ko -->
          </select>

          <!-- ko template: {
              foreach: channel.formControlErrorStateMatcher(channel.sender),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
          <div class="form-error" data-bind="text: $parent.channel.sender.error()"></div>
          <!-- /ko -->
        </div>

        <!-- /ko -->

      </div>
    </div>

    <!-- ko ifnot: channel.config.hideDelay -->
    <div class="col-6">
      <div class="form-group">
        <label class="form-label">Задержка</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
              attr: {
                'data-original-title': channel.isAuto ? 'Время после срабатывания триггера, по истечении которого будет отправлено сообщение' : 'Время после запуска рассылки, по истечении которого будет отправлено сообщение',
              },
              tooltip
            ">
        </button>

        <!-- ko component: {
                              name: 'delay-group',
                              params: {
                                  days: channel.delayDays,
                                  time: channel.delayTime,
                                  channel: channel,
                                  disabled: blocked
                              }
                          } -->
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: channel.config.senderName -->

    <div class="col-6">
      <div class="form-group">
        <label class="form-label">Имя отправителя</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
              attr: {
                'data-original-title': 'Можно оставить отправителя из настроек канала, либо указать другого',
              },
              tooltip
            ">
        </button>

        <div class="chars-counter chars-counter--type_input">
          <input class="form-control" data-bind="textInput: channel.senderName, css: {
                              'is-invalid': channel.formControlErrorStateMatcher(channel.senderName),
                              'is-valid': channel.formControlSuccessStateMatcher(channel.senderName),
                          }, disable: blocked" maxlength="150">
          <div class="chars-counter__value"></div>
        </div>

        <!-- ko template: {
                          foreach: channel.formControlErrorStateMatcher(channel.senderName),
                          afterAdd: fadeAfterAddFactory(200),
                          beforeRemove: fadeBeforeRemoveFactory(200)
                      } -->
        <div class="form-error" data-bind="text: $parent.channel.senderName.error()"></div>
        <!-- /ko -->
      </div>
    </div>

    <!-- /ko -->




    <!-- ko if: channel.isRequestProcessingEnabled -->
    <div class="col-6">

      <div class="form-group">
        <fc-label params="text: 'Время на обработку анкет', hint: 'Время на обработку анкет'"></fc-label>

        <fc-days-time params="days: channel.processingTime.days, time: channel.processingTime.time, fullLabel: true, showErrors: channel.showErrors, disabled: blocked"></fc-days-time>
        <div class="f-fs-1 f-color-service mt-2">При незаполненном параметре используется время для обработки анкет из настроек опроса</div>
      </div>

    </div>
    <!-- /ko -->

    <div class="col-6">
      <div class="form-group">
        <label class="form-label">Тема</label>

        <button type="button" class="btn-question" data-placement="top" data-bind="
            attr: {
              'data-original-title': 'Укажите тему письма',
            },
            tooltip
          ">
        </button>

        <div class="chars-counter chars-counter--type_input" data-bind="settingsCharsCounter: channel.subject">
          <input class="form-control" data-bind="textInput: channel.subject, css: {
                            'is-invalid': channel.formControlErrorStateMatcher(channel.subject),
                            'is-valid': channel.formControlSuccessStateMatcher(channel.subject),
                        }, disable: blocked" maxlength="150">
          <div class="chars-counter__value"></div>
        </div>

        <!-- ko template: {
                            foreach: channel.formControlErrorStateMatcher(channel.subject),
                            afterAdd: fadeAfterAddFactory(200),
                            beforeRemove: fadeBeforeRemoveFactory(200)
                        } -->
        <div class="form-error" data-bind="text: $parent.channel.subject.error()"></div>
        <!-- /ko -->
      </div>
    </div>

    <div class="col-12">
      <div class="form-group settings__conn-channels-channel-html-message-form-group ">

        <div>
          <label class="form-label">
            Текст сообщения

            <!-- <button type="button" class="btn-question" data-placement="top" data-bind="
              attr: {
                'data-original-title': 'С помощью переменных, указанных под текстом письма, можно добавить ' + channel.getVariablesString()
              },
              tooltip
            ">
            </button> -->
          </label>
        </div>

        <!-- <div>
          <fc-switch params="label: 'Новый редактор', checked: channel.isNewEditor"></fc-switch>
        </div> -->

        <!-- ko ifnot: channel.isNewEditor -->
        <div class="d-flex justify-content-end align-items-center" style="min-height: 24px;">
          <!-- ko if: channel.mode == 'poll' -->

          <button class="button-ghost px-0 pb-1 f-fs-1-5 d-flex align-items-center" data-bind="click: function() { channel.preview() }">
            <span class="f-color-primary mr-1">Предварительный просмотр</span>
            <svg-icon params="name: 'expand'" class="svg-icon--sm f-color-service ml-2"></svg-icon>
          </button>

          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- ko if: channel.isNewEditor -->
        <div class="unlayer-wrapper">
          <fc-unlayer params="ref: channel.newEditor, tools: channel.newEditorTools, data: channel.newEditorData, tags: channel.newEditorTags"></fc-unlayer>
          <!-- ko template: {
              foreach: channel.formControlErrorStateMatcher(channel.newEditorMessage),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
          <div class="form-error" data-bind="text: $parent.channel.newEditorMessage.error()"></div>
          <!-- /ko -->

        </div>
        <!-- /ko -->

        <!-- ko ifnot: channel.isNewEditor -->

        <div class="tinymce-wrapper" data-bind="tinymce: channel.message, tinymceIsEmpty: channel.isEmpty, tinymceInstance: channel.editor, css: {
                    'is-invalid': channel.formControlErrorStateMatcher(channel.message),
                    'is-valid': channel.formControlSuccessStateMatcher(channel.message),
                }, readOnly: blocked">
          <textarea class="form-control editor__field"></textarea>

          <!-- ko template: {
              foreach: channel.formControlErrorStateMatcher(channel.message),
              afterAdd: fadeAfterAddFactory(200),
              beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
          <div class="form-error" data-bind="text: $parent.channel.message.error()"></div>
          <!-- /ko -->

          <!-- ko ifnot: blocked -->
          <div class="settings__variables">
            <!-- ko foreach: channel.variables -->
            <div class="settings__variable" data-bind="text: $parent.channel.getVariableLabel($data, $parent.channel),
            click: function($data, event) {
                $parent.channel.getVariableAction($data, event, $parent.channel);
              }"></div>
            <!-- /ko -->

          </div>
          <!-- /ko -->
        </div>
        <!-- /ko -->

      </div>
    </div>
  </div>

  <!-- ko if: channel.config.repeats -->
  <!-- ko if: !blocked || channel.repeats().length > 0 -->
  <div class="settings__conn-channels-channel-repeats">
    <div class="settings__conn-channels-channel-repeats-header">
      <div class="settings__conn-channels-channel-repeats-leader">
        <div class="settings__conn-channels-channel-repeats-title">
          Повторы
          <!-- ko if: channel.repeats().length > 0 -->
          <span class="settings__conn-channels-channel-repeats-count" data-bind="text: channel.repeats().length">
          </span>
          <!-- /ko -->
        </div>

        <div class="settings__conn-channels-channel-repeats-sub-title">
          Отправка повторного сообщения, если по предыдущему сообщению не было совершено целевого действия
        </div>

        <!-- ko ifnot: blocked -->
        <button type="submit" class="f-btn f-btn-text f-btn-success" data-bind="click: function() { channel.addRepeat(); }">
          <span class="f-btn-prepend">
            <svg-icon params="name: 'plus'"></svg-icon>
          </span>
          Добавить повтор
        </button>
        <!-- /ko -->

      </div>


    </div>

    <!-- ko foreach: {
      data: channel.repeats,
      afterAdd: fadeAfterAddFactory(200),
      beforeRemove: fadeBeforeRemoveFactory(200) } -->
    <!-- ko template: {
        name: 'html-repeat-template',
        data: {
          repeat: $data,
          channel: $parent.channel,
          blocked: $parent.blocked
        }
      } -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko if: !blocked && channel.repeats().length > 0 -->
    <div>
      <hr class="mt-0">
      <button type="submit" class="f-btn f-btn-text f-btn-success" data-bind="click: function() { channel.addRepeat(); }">
        <span class="f-btn-prepend">
          <svg-icon params="name: 'plus'"></svg-icon>
        </span>
        Добавить повтор
      </button>
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /ko -->

</template>

<template id="delay-template">
  <div class="delay-group mb-n2">
    <div class="d-flex flex-wrap align-items-start">
      <div class="d-flex align-items-center delay-group__days mb-2" style="margin-right: 34px;">
        <input class="form-control" data-bind="textInput: days, mask, maskPattern: '00',
                        css: {
                            'is-invalid': daysErrorStateMatcher,
                            'is-valid': daysSuccessStateMatcher,
                        },
                        event: {
                            blur: formatDays
                        }, disable: disabled" placeholder="0">
        <span style="margin-left: 13px;">дней</span>
      </div>
      <div class="delay-group__time mb-2">
        <div class="d-flex align-items-center">
          <input class="form-control" data-bind="value: time, mask, maskPattern: '00:00',
                        css: {
                            'is-invalid': timeErrorStateMatcher,
                            'is-valid': timeSuccessStateMatcher,
                        },
                        event: {
                            blur: formatTime
                        }, disable: disabled" placeholder="00:00">
          <span style="margin-left: 13px;" data-bind="text: short ? 'чч:мм' : 'часов : минут'"></span>
        </div>

        <!-- ko template: {
                    foreach: templateIf(isInvalid(), $data),
                    afterRender: fadeAfterAddFactory(200),
                    beforeRemove: fadeBeforeRemoveFactory(200),
                } -->
        <div class="form-error">Некорректный формат</div>
        <!-- /ko -->
      </div>
    </div>

  </div>
</template>