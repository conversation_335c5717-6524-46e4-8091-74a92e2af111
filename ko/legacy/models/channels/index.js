import { BasicChannelViewModel } from './basic-channel';
import { HTMLChannelViewModel } from './html-channel';

import 'Blocks/components/editor/basic';
import 'Components/editor/tinymce';

import 'Legacy/modals/no-channel-settings';
import 'Legacy/modals/no-channel-template';

import './components';

const channelTypes = {
  Email: 'email',
  SMS: 'sms',
  Viber: 'viber',
  Telegram: 'telegram',
  Push: 'push',
};

const channelIds = {
  email: 'Email',
  sms: 'SMS',
  viber: 'Viber',
  telegram: 'Telegram',
  push: 'Push',
};

const channelNames = {
  email: 'Email',
  sms: 'SMS',
  viber: 'Viber',
  telegram: 'Telegram',
  push: 'Push-уведомления',
};

const htmlChannels = ['email', 'push'];

export function isHTMLChannel(channelType, channelMode) {
  return channelType == 'email';
}

export function ChannelModelFactory(config, channelData) {
  let type;
  if (channelData) {
    config.name = channelData.name;
    type = channelTypes[channelData.name];
    config.type = type;
  } else {
    type = config.type;
    config.name = channelIds[type];
  }

  if (!type) return null;

  config.title = channelNames[type];

  let model = isHTMLChannel(type, config.mode)
    ? HTMLChannelViewModel
    : BasicChannelViewModel;

  switch (type) {
    case 'email':
      config = {
        ...config,
        senderName: true,
      };
      break;
  }

  return new model(config, channelData);
}
