ko.components.register("channelForm", {
  viewModel: function (params) {
    this.channel = params.channel;
    this.blocked = params.blocked;
    console.log("component channelFORM");

    let templateName = "basic-channel-nomedia-template";

    if (this.channel.isHTML) templateName = "html-channel-template";
    else if (this.channel.type === "telegram")
      templateName = "telegram-channel-template";
    else if (this.channel.type === "push")
      templateName = "push-channel-template";

    this.templateName = templateName;
    console.log({ templateName });
  },
  template: `
    <!-- ko template: {
      name: templateName,
      data: {
        channel: channel,
        blocked: blocked
      }
    } -->
    <!-- /ko -->
  `,
});
