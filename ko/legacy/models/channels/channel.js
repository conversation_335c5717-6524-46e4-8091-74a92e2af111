import { find as _find } from "lodash";

import { variables } from "./variables";
import "Legacy/modals/no-channel-settings";
import { RepeatViewModel } from "./repeat";
import { DelayDays, DelayTime, getDelayText } from "Legacy/models/delay";
import { PromocodeController } from "./controllers/promocode";

import { DialogsModule } from "Utils/dialogs-module";

import ee from "event-emitter";
import { getRequestProcessingSettings } from "Utils/project/request-processing-settings";
import "Presentation/views/fc-days-time";
import { FmDaysTime } from "@/entities/structures/fm-days-time";

export class ChannelViewModel {
  constructor(config, variablesSettings) {
    ee(this);
    DialogsModule(this);
    this.config = config;

    this.showErrors = config.showErrors;

    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      config.showErrors
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      config.showErrors
    );

    this.type = config.type;
    this.mode = config.mode;
    this.title = config.title;
    this.name = config.name;

    this.message = ko.observable("").extend(this.getMessageValidator(this));
    this.sender = ko.observable("");
    this.delayDays = DelayDays("");
    this.delayTime = DelayTime("");

    this.isRequestProcessingEnabled = ko.observable(false);
    this.processingTime = new FmDaysTime();

    if (!config.hideProcessing && this.mode === "poll") {
      getRequestProcessingSettings().then((data) => {
        this.isRequestProcessingEnabled(data.request_processing_enabled);
      });
    }

    this.isEmpty = ko.pureComputed(() => !this.message());

    this.senders = ko.observableArray(config.senders);
    if (!this.sender()) {
      let defaultSender = this.senders()[0];
      if (defaultSender) this.sender(defaultSender.id);
    }

    this.sendersLoaded = ko.observable(true);

    this.variablesSettings = variablesSettings;
    this.variables = ko.observableArray([]);
    this._setVariables();

    this.repeats = ko.observableArray([]);

    this.promocodeController = new PromocodeController();

    [
      "message",
      "sender",
      "delayDays",
      "delayTime",
      "senders",
      "repeats",
    ].forEach((key) => {
      this[key].subscribe((_) => this.emit("change", key));
    });

    this.promocodeController.on("change", () => {
      this.emit("change", "promocode");
    });
  }

  /* Проверки */

  _showNoGlobalSettingsMessage() {
    window.modalOpens.push({
      dialogTemplateName: "modal-dialog-template",
      data: {
        componentName: "no-channel-settings-modal-dialog",
        channelType: this.type,
        mode: this.mode,
      },
      close: (res) => {},
    });
  }

  _showNoTemplateMessage() {
    window.modalOpens.push({
      dialogTemplateName: "modal-dialog-template",
      data: {
        componentName: "no-channel-template-modal-dialog",
        channelType: this.type,
        mode: this.mode,
      },
      close: (res) => {},
    });
  }

  checkGlobalSettings(mute) {
    return new Promise((res, rej) => {
      $.ajax({
        method: "GET",
        url:
          "/foquz/api/channels/can-activate?channelName=" +
          this.name +
          "&access-token=" +
          window.APIConfig.apiKey,
        success: (data) => {
          if (data.can) res();
          else {
            if (!mute) this._showNoGlobalSettingsMessage();
            rej();
          }
        },
        error: function (data) {
          rej();
        },
      });
    });
  }

  checkTemplate(mute) {
    return new Promise((res, rej) => {
      if (this.isValid()) {
        res();
        return;
      }

      if (!mute) this._showNoTemplateMessage();
      rej();
    });
  }

  // Валидатор поля message
  getMessageValidator(ctx) {
    return {
      required: {
        message: "Обязательное поле",
      },
    };
  }

  // Валидатор поля sender
  getSenderValidator() {
    return {
      required: {
        message: "Обязательное поле",
      },
    };
  }

  // Объект валидации всего канала
  get _validationObject() {
    if (this._observableValidationObject)
      return this._observableValidationObject;
    this._observableValidationObject = ko.validatedObservable(
      this.validatedFields,
      {
        deep: true,
        live: true,
      }
    );
    return this._observableValidationObject;
  }

  // Валидируемые поля
  get validatedFields() {
    return {
      sender: this.sender,
      delayDays: this.delayDays,
      delayTime: this.delayTime,
      processingTime: this.processingTime,
    };
  }

  isEditorValid() {
    let isValid = this.message.isValid();
    return Promise.resolve(isValid);
  }

  isFieldsValid() {
    return Promise.resolve(this._validationObject.isValid());
  }

  isRepeatsValid() {
    if (!this.config.repeats) return Promise.resolve(true);

    return Promise.all(this.repeats().map((r) => r.isValid())).then(
      (repeats) => {
        return repeats.every(Boolean);
      }
    );
  }

  isValid() {
    return Promise.all([
      this.isEditorValid(),
      this.isFieldsValid(),
      this.isRepeatsValid(),
    ]).then((results) => {
      return results.every(Boolean);
    });
  }

  // Обновить данные канала
  updateData(data) {
    this.id = data.id;

    this.isAuto = data.isAuto;
    this.hasOrder = data.hasOrder;
    this._setVariables();

    this.message(data.message || "");

    if (data.sender) {
      this.sender(data.sender);
    }

    this.delayDays(data.delayDays || "");
    this.delayTime(data.delayTime || "");

    this.processingTime.value = data.processing_time_in_minutes;

    this.promocodeController.updateData(data);

    if (this.config.repeats) {
      this.updateRepeats(data.repeats);
    }
  }

  // Данные для отправки на сервер
  getData() {
    return new Promise((resolve) => {
      const promocode = this.promocodeController.getData();

      const data = {
        message: this.message(),
        delayDays: this.delayDays(),
        delayTime: this.delayTime(),
        sender: this.sender(),
        sender_id: this.sender(),

        ...promocode,
      };

      const processingTime = this.processingTime.value;
      data.processing_time_in_minutes = processingTime;

      resolve(data);
    });
  }

  getDelayText() {
    return getDelayText(this.delayDays(), this.delayTime());
  }

  getMessage() {
    return this.message();
  }

  /* Переменные */

  _setVariables() {
    let variablesSet = this.variablesSettings
      .filter((v) => {
        if (!ko.toJS(this.hasOrder) && v.order) return false;
        if (v.modes) {
          if (!v.modes.includes(this.mode)) return false;
        }

        if (v.types) {
          if (!v.types.includes(this.type)) return false;
        }

        return true;
      })
      .map((v) => v.id);
    this.variables(variablesSet.map((v) => variables[v]));
  }

  getVariablesString() {
    let str = this.variables()
      .filter((v) => v.id == "promocode")
      .map((v) => v.description)
      .filter(Boolean)
      .join(", ");
    if (this.variables().length > 1) str += ", а также промокод";
    else str += " и промокод";
    return str;
  }

  getVariableLabel(variable, ctx) {
    if (variable.id == "promocode")
      return ctx.promocodeController.promocode.label;
    if (variable.id == "barcode") {
      const promocode = ctx.promocodeController.promocode;
      const code = promocode.isPool()
        ? promocode.poolName()
        : promocode.reusableCouponName();
      return [variable.text, code].filter(Boolean).join(": ");
    }

    return variable.text;
  }

  getVariableAction(variable, event, ctx) {
    if (variable.id == "promocode") {
      ctx.promocodeController.setPromocode().then(() => {
        $(event.target).trigger("set.variable", variable.value);
      });

      return;
    }

    if (variable.id == "barcode") {
      ctx.promocodeController.setPromocode().then(() => {
        $(event.target).trigger("set.variable", variable.value);
      });

      return;
    }

    $(event.target).trigger("set.variable", variable.value);
  }

  /* Повторы */

  createRepeatModel(repeatData) {
    return new RepeatViewModel(repeatData, this);
  }

  getRepeats() {
    return Promise.all(this.repeats().map((repeat) => repeat.getData()));
  }

  getDefaultRepeatData() {
    return Promise.resolve({
      message: this.message(),
      delayDays: this.delayDays(),
      delayTime: this.delayTime(),

      ...this.promocodeController.getData(),
    });
  }

  addRepeat(data) {
    new Promise((resolve) => {
      if (data) resolve(data);
      else resolve(this.getDefaultRepeatData());
    }).then((data) => {
      let repeat = this.createRepeatModel(data || null);
      repeat.on("change", () => this.emit("change", "repeat"));
      this.repeats.push(repeat);
    });
  }

  removeRepeat(repeatModel) {
    this.repeats.remove(repeatModel);
  }

  updateRepeats(data) {
    if (!data) {
      this.repeats.removeAll();
      return;
    }

    let currentCount = this.repeats().length;
    let newCount = data.length;

    while (currentCount > newCount) {
      this.repeats.pop();
      currentCount = this.repeats().length;
    }

    let repeats = this.repeats();

    for (let i = 0; i < currentCount; i++) {
      let repeatData = data[i];
      repeats[i].updateData(repeatData);
    }

    if (newCount > currentCount) {
      for (let i = currentCount; i < newCount; i++) {
        let repeatData = data[i];
        this.addRepeat(repeatData);
      }
    }
  }
}
