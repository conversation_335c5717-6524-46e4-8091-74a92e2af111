import { ActionsController } from "./controllers/resources/actions";
import { ProductsController } from "./controllers/resources/products";

import { ApiUrl } from "Utils/url/api-url";
import "Dialogs/email-preview-dialog";

export function HTMLController(ctx) {
  ctx.editor = ko.observable(null);
  ctx.newEditor = ko.observable(null);

  ctx.newEditorIsEmpty = ko.observable(false);
  ctx.newEditorMessage = ko.observable(null).extend({
    validation: {
      validator: () => {
        return !ctx.newEditorIsEmpty();
      },
      message: "Обязательное поле",
    },
  });

  ctx.newEditorTools = ["promocode", "actions", "products"];
  ctx.newEditorTags = ["name", "unsubscribe"];
  ctx.newEditorData = {};

  if (ko.toJS(ctx.hasOrder)) {
    ctx.newEditorTags.push("order");
  }
  if (ctx.mode === "poll") {
    ctx.newEditorTools = [
      "promocode",
      "pollLink",
      "qr",
      "rating",
      "actions",
      "products",
    ];
    ctx.newEditorData = { pollId: ctx.config.poll.id };
  }

  let onNewEditorInit = null;
  let subscription = ctx.newEditor.subscribe((v) => {
    if (v) {
      subscription.dispose();
      if (typeof onNewEditorInit === "function") onNewEditorInit(v);

      v.isEmpty.subscribe((isEmpty) => {
        ctx.newEditorIsEmpty(ko.toJS(isEmpty));
      });
    }
  });

  ctx.getNewEditorDesign = () => {
    return new Promise((resolve) => {
      let newEditor = ctx.newEditor();
      if (newEditor) {
        newEditor.export().then(({ design, html }) => {
          resolve({ design, html });
        });
      } else {
        resolve(null);
      }
    });
  };

  ctx.setNewEditorDesign = (design) => {
    let newEditor = ctx.newEditor();
    if (newEditor) {
      newEditor.update(design);
    } else {
      onNewEditorInit = (api) => api.update(design);
    }
  };

  ctx.actionsController = new ActionsController();
  ctx.productsController = new ProductsController();

  let originalGetData = ctx.getData.bind(ctx);

  ctx.getData = () => {
    return originalGetData().then((data) => {
      const isNewEditor = ctx.isNewEditor();

      if (isNewEditor) {
        return ctx.getNewEditorDesign().then((newEditorData) => {
          let products = [];

          // categories []
          // count
          // id
          // products []
          // resourceId

          let discounts = [];

          // count
          // discounts []
          // id
          // resourceId
          // tempId

          newEditorData.design.body.rows.forEach((row) => {
            row.columns.forEach((column) => {
              column.contents.forEach((block) => {
                if (block.slug === "actions") {
                  let data = block.values.actions;
                  let action = {
                    resourceId: data.resource,
                    discounts: data.selected,
                    count: data.count,
                    id: data.blockId,
                  };
                  discounts.push(action);
                }

                if (block.slug === "products") {
                  let data = block.values.products;
                  let product = {
                    resourceId: data.resource,
                    count: data.count,
                    categories: data.selectedCategories.map((c) => c.slice(1)),
                    products: data.selectedProducts.map((p) => p.slice(1)),
                    id: data.blockId,
                  };
                  products.push(product);
                }
              });
            });
          });

          return {
            ...data,
            message: newEditorData.html,
            newEditor: newEditorData.design,
            product_block: products,
            discount_block: discounts,
          };
        });
      } else {
        let $message = document.createElement("div");
        $message.innerHTML = data.message;

        ctx.actionsController.prepareForServer($message);
        ctx.productsController.prepareForServer($message);

        const result = {
          ...data,
          message: `<!DOCTYPE html>
          <html>
          <head></head>
          <body>
          ${$message.innerHTML}
          </body>
          </html>`,

          product_block: ctx.productsController.getBlocks(),
          discount_block: ctx.actionsController.getBlocks(),
        };

        return result;
      }
    });
  };

  let originalUpdateData = ctx.updateData.bind(ctx);
  ctx.updateData = (data) => {
    originalUpdateData(data);

    ctx.actionsController.updateData(data);
    ctx.productsController.updateData(data);

    let $message = document.createElement("div");
    $message.innerHTML = data.message || "";

    ctx.actionsController.prepareForClient($message);
    ctx.productsController.prepareForClient($message);

    ctx.message(`<!DOCTYPE html>
    <html>
    <head></head>
    <body>
    ${$message.innerHTML}
    </body>
    </html>`);

    ctx.setNewEditorDesign(data.newEditor);
  };

  let originalIsEditorValid = ctx.isEditorValid.bind(ctx);
  ctx.isEditorValid = () => {
    if (ctx.isNewEditor()) {
      return new Promise((resolve) => {
        resolve(ctx.newEditorMessage.isValid());
      });
    }
    return originalIsEditorValid();
  };

  ctx.preview = () => {
    let url = ApiUrl("channels/email-preview", { pollId: ctx.config.poll.id });

    ctx.getData().then((data) => {
      $.ajax({
        method: "POST",
        url,
        data: {
          data: data,
        },
        success: (response) => {
          ctx.openDialog({
            name: "email-preview-dialog",
            params: {
              html: response.html,
            },
          });
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      });
    });
  };
}
