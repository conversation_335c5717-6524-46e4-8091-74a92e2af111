import { DelayDays, DelayTime } from "Legacy/models/delay";
import ee from "event-emitter";
import { PromocodeController } from "./controllers/promocode";
import { DialogsModule } from "Utils/dialogs-module";

export class RepeatViewModel {
  constructor(channel) {
    ee(this);
    DialogsModule(this);
    this.message = ko.observable("").extend(channel.getMessageValidator(this));
    this.delayDays = DelayDays("");
    this.delayTime = DelayTime("");
    this.link = ko.observable("");
    this.payload = ko.observable("");

    this.mode = channel.mode;

    ["message", "delayDays", "delayTime", "link", "payload"].forEach((key) => {
      this[key].subscribe((_) => this.emit("change"));
    });

    this.promocodeController = new PromocodeController();

    this.promocodeController.on("change", () => this.emit("change"));

    this.isEmpty = ko.pureComputed(() => !this.message());
  }

  getData() {
    const promocode = this.promocodeController.getData();
    const data = {
      message: this.message(),
      delayDays: this.delayDays(),
      delayTime: this.delayTime(),
      link: this.link(),
      payload: this.payload(),

      ...promocode,
    }
    console.log("GET REPEAT DATA", this, { data })
    return Promise.resolve(data);
  }

  updateData(data) {
    this.message(data.message || "");

    this.delayDays(data.delayDays || "");
    this.delayTime(data.delayTime || "");

    if ('link' in data) {
      this.link(data.link || "");
    }

    if ('payload' in data) {
      this.payload(data.payload || "")
    }

    console.log("UPDATE REPEAT DATA", { data })

    this.promocodeController.updateData(data);
  }

  // Объект валидации всего канала
  get _validationObject() {
    if (this._observableValidationObject)
      return this._observableValidationObject;
    this._observableValidationObject = ko.validatedObservable(
      this.validatedFields,
      {
        deep: true,
        live: true,
      }
    );
    return this._observableValidationObject;
  }

  get validatedFields() {
    return {
      delayDays: this.delayDays,
      delayTime: this.delayTime,
    };
  }

  isFieldsValid() {
    return Promise.resolve(this._validationObject.isValid());
  }

  isEditorValid() {
    return Promise.resolve(this.message.isValid());
  }

  isValid() {
    return Promise.all([this.isEditorValid(), this.isFieldsValid()]).then(
      (results) => results.every(Boolean)
    );
  }
}
