import { getLinkButton } from "@/constants/mailings/variables";

export const variables = {
  name: {
    id: "name",
    value: "{ФИО}",
    text: "ФИО",
    description: "ФИО контакта",
  },
  firstname: {
    id: "firstname",
    value: "{Имя}",
    text: "Имя",
    description: "Имя клиента",
  },
  lastname: {
    id: "lastname",
    value: "{Фамилия}",
    text: "Фамилия",
    description: "Фамилия клиента",
  },
  patronymic: {
    id: "patronymic",
    value: "{Отчество}",
    text: "Отчество",
    description: "Отчество клиента",
  },
  orderNumber: {
    id: "orderNumber",
    value: "{№}",
    text: "Номер заказа",
    description: "номер заказа",
  },
  orderTime: {
    id: "orderTime",
    value: "{Дата}",
    text: "Дата/время заказа",
    description: "дату и время заказа",
  },
  qr: {
    id: "qr",
    value: "{QR}",
    text: "QR-код",
    description: "QR-код",
  },
  link: {
    id: "link",
    value: "{Ссылка}",
    text: "Полная ссылка на опрос",
    description: "ссылку на опрос",
  },
  shortLink: {
    id: "shortLink",
    value: "{Короткая ссылка}",
    text: "Короткая ссылка на опрос",
    description: "короткую ссылку на опрос",
  },
  htmlLink: {
    id: "htmlLink",
    text: "Полная ссылка на опрос",
    description: "ссылку на опрос",
    value: getLinkButton(),
  },
  htmlShortLink: {
    id: "htmlShortLink",
    text: "Короткая ссылка на опрос",
    description: "короткую ссылку на опрос",
    value: getLinkButton({ link: "Короткая ссылка" }),
  },
  unsubscribe: {
    id: "unsubscribe",
    text: "Отписаться от рассылки",
    description: "возможность отписаться от рассылки",
    value:
      '<a class="unsubscribe-link" href="{Отписаться от рассылки}">Отписаться от рассылки</a>',
  },
  promocode: {
    id: "promocode",
    text: "Промокод",
    value: "{Промокод}",
  },
  actions: {
    id: "actions",
    text: "Акции",
    value: "",
    description: "блок акций",
  },
  products: {
    id: "products",
    text: "Товары",
    value: "",
    description: "блок товаров",
  },
  rating: {
    id: "rating",
    text: "Шкала оценок",
    value: "{Шкала оценок}",
  },
  barcode: {
    id: "barcode",
    text: "Штрихкод промокода",
    value: "<div>{Штрихкод промокода}</div>",
  },
};
