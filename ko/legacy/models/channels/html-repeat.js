import { RepeatViewModel } from "./repeat";
import { ActionsController, ProductsController } from "./controllers/resources";
import { HTMLController } from "./html-controller";

export class HTMLRepeatViewModel extends RepeatViewModel {
  constructor(data, channel) {
    super(channel);
    this.config = channel.config;

    this.isNewEditor = channel.isNewEditor;

    HTMLController(this);
    // убрала в шаблоне ref на редактор (repeat.editor),
    // почему-то возникала ошибка при его инициализации (tinymce)
    this.isEmpty = ko.observable(true);

    if (data) {
      this.updateData(data);
    }
  }

  updateData(data) {
    super.updateData(data);

    this.isEmpty(window.utils.string.isHTMLEmpty(this.message()));
  }
}
