import { FoquzLoaderWithFile } from "Models/file-loader/loader-with-file";
import { RepeatViewModel } from "./repeat";

export class BasicRepeatViewModel extends RepeatViewModel {
  constructor(data, channel) {
    super(channel);

    this.imageLoader = new FoquzLoaderWithFile(null, null);

    if (data) {
      this.updateData(data);
    }
  }

  getData() {
    return super.getData().then((data) => {
      data.image = this.imageLoader.getFile() || "delete";
      return data;
    });
  }

  updateData(data) {
    super.updateData(data);

    this.imageLoader.setFile(
      !data.image || data.image == "delete" ? "" : data.image
    );
    this.imageLoader.clearErrors();
  }
}
