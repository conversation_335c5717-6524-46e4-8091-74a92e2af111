import { ChannelViewModel } from "./channel";
import { HTMLRepeatViewModel } from "./html-repeat";
import { HTMLController } from "./html-controller";

import "Dialogs/channel-rating-dialog";

import "@/presentation/components/fc-unlayer";

export class HTMLChannelViewModel extends ChannelViewModel {
  constructor(config, data) {
    const { hidePollVariables } = config;

    let variablesSet = [
      { id: "name", order: false, modes: null, types: null },
      { id: "firstname", order: false, modes: null, types: null },
      { id: "lastname", order: false, modes: null, types: null },
      { id: "patronymic", order: false, modes: null, types: null },
      { id: "orderNumber", order: true, modes: null, types: null },
      { id: "orderTime", order: true, modes: null, types: null },
      !hidePollVariables && {
        id: "htmlLink",
        order: false,
        modes: ["poll"],
        types: ["email"],
      },
      !hidePollVariables && {
        id: "htmlShortLink",
        order: false,
        modes: ["poll"],
        types: ["email"],
      },
      !hidePollVariables && { id: "qr", order: false, modes: ["poll"], types: ["email"] },
      { id: "unsubscribe", order: false, modes: null, types: null },

      // { id: "actions", order: false, modes: null, types: null },
      // { id: "products", order: false, modes: null, types: null },
      { id: "promocode", order: false, modes: null, types: null },
      { id: "barcode", order: false, modes: null, types: null },

      !hidePollVariables && { id: "rating", order: false, modes: ["poll"], types: ["email"] },
    ].filter(Boolean);

    super(config, variablesSet);

    this.isNewEditor = ko.observable(false);

    HTMLController(this);

    this.isHTML = true;

    this.senderName = ko.observable("");
    this.subject = ko.observable("").extend({
      required: {
        message: "Обязательное поле",
      },
    });

    ["senderName", "subject"].forEach((key) => {
      this[key].subscribe((_) => this.emit("change", key));
    });

    if (this.config.senderName) {
      this.senderName.extend({
        required: {
          message: "Обязательное поле",
        },
      });

      this.sender.subscribe((v) => {
        if (!v) return;
        let senderData = this.senders().find((s) => s.id == v);
        if (!senderData) return;
        this.senderName(senderData.senderName);
      });
    }

    this.isEmpty = ko.observable(false).extend({ notify: "always" });

    if (data) {
      this.updateData(data);
    }
  }

  /* Проверки */

  get validatedFields() {
    return {
      message: this.message,
      sender: this.sender,
      senderName: this.senderName,
      subject: this.subject,
      delayDays: this.delayDays,
      delayTime: this.delayTime,
      repeats: this.repeats,
      processingTime: this.processingTime,
    };
  }

  getMessageValidator(ctx) {
    ctx = ctx || this;

    return {
      validation: {
        validator: () => false,
        message: "Обязательное поле",
        onlyIf: () => {
          return ctx.isEmpty && ctx.isEmpty();
        },
      },
    };
  }

  /* Данные */

  getData() {
    return super.getData().then((data) => {
      data = {
        ...data,
        subject: this.subject(),
        senderName: this.senderName(),
      };

      return data;
    });
  }

  getDefaultRepeatData() {
    return Promise.all([
      super.getDefaultRepeatData(),
      this.getNewEditorDesign(),
    ]).then(([data, newEditorData]) => {
      data = {
        ...data,
        newEditor: newEditorData,
        isNewEditor: this.isNewEditor(),
      };

      return data;
    });
  }

  updateData(data) {
    super.updateData(data);

    this.message(data.message || "");

    if (data.sender) {
      this.sender(data.sender);
    }

    if (data.senderName) {
      this.senderName(data.senderName);
    }

    this.subject(data.subject);

    this.isEmpty(window.utils.string.isHTMLEmpty(this.message()));
  }

  getMessage() {
    return "—";
  }

  /* Переменные */

  setActions() {
    return new Promise((res) => {});
  }

  setProducts() {
    return new Promise((res) => {});
  }

  getVariableAction(variable, event, ctx) {
    if (variable.id == "rating") {
      this.openDialog({
        name: "channel-rating-dialog",
      });
      $(event.target).trigger("set.variable", variable.value);
      return;
    }

    if (variable.id == "actions") {
      ctx.actionsController.open().then((blockData) => {
        let code = ctx.actionsController.generateCode(blockData);
        $(event.target).trigger("set.variable", code);
      });
      return;
    }

    if (variable.id == "products") {
      ctx.productsController.open().then((blockData) => {
        if (blockData) {
          let code = ctx.productsController.generateCode(blockData);
          $(event.target).trigger("set.variable", code);
        }
      });
      return;
    }

    super.getVariableAction(variable, event, ctx);
  }

  /* Повторы */

  createRepeatModel(repeatData) {
    return new HTMLRepeatViewModel(repeatData, this);
  }
}
