import { ChannelViewModel } from "./channel";
import { BasicRepeatViewModel } from "./basic-repeat";
import { FoquzLoaderWithFile } from "Models/file-loader/loader-with-file";

export class BasicChannelViewModel extends ChannelViewModel {
  constructor(config, data) {
    const { hidePollVariables } = config;

    let variablesSet = [
      { id: "name", order: false, modes: null, types: null },
      { id: "orderNumber", order: true, modes: null, types: null },
      { id: "orderTime", order: true, modes: null, types: null },
      //{ id: 'qr', order: false, modes: ['poll'], types: null },
      !hidePollVariables && { id: "link", order: false, modes: ["poll"], types: null },
      !hidePollVariables && { id: "shortLink", order: false, modes: ["poll"], types: null },
      { id: "promocode", order: false, modes: null, types: null },
      { id: "barcode", order: false, modes: null, types: null },
    ].filter(Boolean);

    super(config, variablesSet);

    this.imageLoader = new FoquzLoaderWithFile(null, null);

    this.withSubject = this.type == "push";
    this.link = ko.observable("");
    this.payload = ko.observable("");

    this.subject = ko.observable("").extend({
      required: {
        message: "Обязательное поле",
        onlyIf: () => this.withSubject,
      },
    });

    if (data) {
      this.updateData(data);
    }
  }

  get validatedFields() {
    if (this.withSubject) {
      return {
        ...super.validatedFields,
        subject: this.subject,
      };
    }
    return super.validatedFields;
  }

  getData() {
    return super.getData().then((data) => {
      if (this.withSubject) data.subject = this.subject();

      if (this.type === "push") {
        data.link = this.link();
        data.payload = this.payload();
      }

      data.image = this.imageLoader.file() || "delete";

      console.log("GET CHANNEL DATA", { data });

      return data;
    });
  }

  updateData(data) {
    super.updateData(data);

    console.log("UPDATE CHANNEL DATA", { data });

    if (this.withSubject) this.subject(data.subject);

    if (this.type === "push") {
      this.link(data.link || "");
      this.payload(data.payload || "");
    }

    this.imageLoader.setFile(data.image == "delete" ? "" : data.image);
    this.imageLoader.clearErrors();
  }

  createRepeatModel(repeatData) {
    return new BasicRepeatViewModel(repeatData, this);
  }

  getDefaultRepeatData() {
    return super.getDefaultRepeatData().then((data) => {
      data.image = this.imageLoader.file();
      if (this.type === "push") {
        data.link = this.link();
        data.payload = this.payload();
      }
      console.log("GET REPEAT DATA", { data })
      return data;
    });
  }

  onVariableClick() {}
}
