import { DiscountPool } from '@/entities/models/discount-pool';
import { DialogsModule } from 'Utils/dialogs-module';
import 'Dialogs/promocode-dialog';
import ee from 'event-emitter';
export class PromocodeController {
  constructor() {
    ee(this);
    DialogsModule(this);
    this.promocode = new DiscountPool();
    this.promocode.on('change', () => {
      this.emit('change');
    });
  }

  setPromocode() {
    return new Promise((res) => {
      this.openDialog({
        name: 'promocode-dialog',
        params: {
          promocode: this.promocode
        },
        events: {
          submit: () => {
            res();
          }
        }
      });

    });
  }

  updateData(data) {
    this.promocode.update({
      poolId: data.promocodePoolId,
      name: data.promocodeName
    });
  }

  getData() {
    const promocode = ko.toJS(this.promocode);

    return {
      promocodeIsPool: promocode.isPool,
      promocodePoolId: promocode.poolId
    };
  }
}
