import { ResourcesController } from '.';
import { ProductsFormatter } from './products-formatter';

export class ProductsController extends ResourcesController {
  constructor() {
    super();
    this.mode = 'products';

    this.categoriesCache = {};

    this.productsList = ko.observableArray([]);
    this.selected.subscribe((v) => {
      let products = [];
      v.forEach((i) => {
        let id = i.slice(1);
        if (i[0] !== 'c') products.push(id);
        else {
          let category = this.list().find((c) => c.id == id);
          products = [...products, ...category.products.map((p) => p.id)];
        }
      });
      this.productsList(products);
    });

    this.selectedCount = ko.pureComputed(() => {
      return this.productsList().length;
    });
  }

  get _formatter() {
    return new ProductsFormatter(this);
  }

  get texts() {
    return {
      title: 'Добавить товары',
      invalidResourceMessage: `Добавление товаров с сайта невозможно, так как у выбранного ресурса не добавлен URL адрес для товаров.`,
      count: 'Кол-во товаров',
      items: 'Товары',
      rotationMessage: `Отображение товаров в письме будет ротироваться`,
    };
  }

  _loadItems() {
    return new Promise((res) => {
      $.ajax({
        url: `${APIConfig.baseApiUrlPath}external-resources/categories?id=${
          this.resource().id
        }&access-token=${APIConfig.apiKey}`,
        method: 'GET',
        success: (data) => {
          res(data.items);
        },
      });
    });
  }

  getBlockProductsList(block) {
    let products = block.products;
    let result = [];
    products.forEach((p) => {
      if (p[0] == 'i') result.push(p.slice(1));
      else {
        let categoryProducts = this.categoriesCache[p.slice(1)];
        result = [...result, ...categoryProducts];
      }
    });
    return result;
  }

  saveSelectedInCache() {
    this.productsList().forEach((id) => {
      if (this.cache[id]) return;

      let list = this.list();

      for (let i = 0, count = list.length; i < count; i++) {
        let category = list[i];
        for (let j = 0, count = category.products.length; j < count; j++) {
          let product = category.products[j];
          if (product.id == id) {
            this.cache[id] = product;
          }
        }
      }
    });
  }

  getBlockModel() {
    return {
      id: 0,
      count: this.count(),
      products: this.selected(),
      productsList: this.productsList(),
      resourceId: this.resourceId(),
    };
  }

  getBlocks() {
    return this.blocks().map((block) => {
      let products = block.products.filter((p) => p[0] != 'c');
      let categories = block.products.filter((p) => p[0] == 'c');
      return {
        id: block.id,
        count: block.count,
        products: products.map((p) => p.slice(1)),
        categories: categories.map((p) => p.slice(1)),
        resourceId: block.resourceId,
      };
    });
  }

  getItem(itemId) {
    if (itemId in this.cache) {
      return this.cache[itemId];
    }

    let list = this.list();
    for (let i = 0, count = list.length; i < count; i++) {
      let category = list[i];
      let items = category.products;

      for (let j = 0, count = items.length; j < count; j++) {
        let product = items[j];
        if (product.id == itemId) {
          this.cache[itemId] = product;
          return product;
        }
      }
    }

    return null;
  }

  updateData(data) {
    let blocks = data.products_blocks || data.product_block || [];
    this.blocks(
      blocks.map((blockData) => {
        let products = blockData.products;
        let categories = blockData.categories;
        categories.forEach((categoryData) => {
          this.categoriesCache[categoryData.id] = [];

          (categoryData.products || []).forEach((p) => {
            this.categoriesCache[categoryData.id].push(p.id);
            if (!(p.id in this.cache)) {
              let categoryName = categoryData.name;
              p.categoryName = categoryName;
              this.cache[p.id] = p;
            }
          });
        });
        products.forEach((productData) => {
          if (!(productData.id in this.cache)) {
            let categoryName = '';
            if (productData.categoryName)
              categoryName = productData.categoryName;
            else if (productData.category)
              categoryName = productData.category.name;
            productData.categoryName = categoryName;
            this.cache[productData.id] = productData;
          }
        });

        return {
          id: blockData.id,
          count: parseInt(blockData.count),
          products: [
            ...products.map((p) => 'i' + p.id),
            ...categories.map((c) => 'c' + c.id),
          ],
        };
      }),
    );
  }

  _isResourceValid() {
    return this.resource() && this.resource().products_url;
  }
}
