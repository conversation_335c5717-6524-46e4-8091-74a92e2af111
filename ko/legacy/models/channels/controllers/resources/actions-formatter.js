import { Formatter } from './formatter';

export class ActionsFormatter extends Formatter {
  get itemName() {
    return 'discount';
  }

  get blockName() {
    return 'discounts';
  }

  get itemTag() {
    return 'action';
  }

  get blockTag() {
    return 'actions';
  }

  /**
   * Код одной акции
   * Каждая акция имеет атрибут data-discount
   * Ссылки на акцию отмечены атрибутом data-link
   * Изображение акции отмечено атрибутом data-img
   * Название акции отмечено атрибутом data-name
   * Описание акции отмечено атрибутом data-description
   */
  _generateItemCode(itemId) {
    let item = this.controller.getItem(itemId);

    let img = `<a data-link href="${item.url}" style="display: block; width: 235px; font-size: 0;">
      <img data-img src="${item.picture}" alt="${item.name}" width="235" style="display: block; border-radius: 6px; width: 235px;" />
    </a>`;

    let name = `<a data-link data-name href="${item.url}"
      style="display: block; font-size: 22px; line-height: 1; font-weight: bold; margin-bottom: 8px; text-decoration: none; color: #2E2F31;">${item.name}</a>`;

    let description = `<div data-description
    style="font-size: 14px; font-weight: normal; color: #2E2F31;">${item.description}</div>`;

    return `
    <tr class="discount-block" data-discount>
      <td style="padding-bottom: 8px;">
        <table style="border: 1px solid #ECEFF1; border-radius: 9px; border-collapse: separate;">
          <tbody>
            <tr>
              <td valign="top" width="255" style="padding: 10px">
                ${img}
              </td>
              <td valign="top" width="100%" valign="top"
              style="padding: 20px 10px; font-family: Arial, Helvetica, sans-serif; line-height: 1.2;">
                ${name}
                ${description}
              </td>
            </tr>
          </tbody>
        </table>
      </td>
    </tr>`;
  }

  /**
   * Код нового блока акций
   * Каждый блок имеет атрибут data-actions={block_id}
   */
  _createBlock(blockData) {
    let selected = blockData.discounts;
    let count = blockData.count;
    let id = blockData.tempId;

    let items = selected.slice(0, count);

    return `
    <table class="discounts" data-discounts="${id}" data-count="${count}" data-items="${items}" width="100%" style="max-width: 740px">
      ${items.map((i) => this._generateItemCode(i)).join('')}
    </table>`;
  }
}
