let unique = 1;

export class Formatter {
  constructor(controller) {
    this.controller = controller;
  }

  get itemName() {
    return 'item';
  }

  get itemTag() {
    return 'item';
  }

  get itemAttr() {
    return `data-${this.itemName}`;
  }

  get blockName() {
    return 'items';
  }

  get blockTag() {
    return 'items';
  }

  get blockAttr() {
    return `data-${this.blockName}`;
  }

  createBlock(blockData) {
    return `<table  width="100%">
    <tbody>
      <tr>
        <td align="center">

            ${this._createBlock(blockData)}

        </td>
      </tr>
    </tbody>
    </table>`;
  }

  /**
   * Подготовка акции для сохранения
   * Замена данных на теги
   */
  _prepareItemForServer(element) {
    element.setAttribute(this.itemAttr, unique++);
    let links = element.querySelectorAll('[data-link]');
    let imgs = element.querySelectorAll('[data-img]');
    let names = element.querySelectorAll('[data-name]');
    let descriptions = element.querySelectorAll('[data-description]');

    links.forEach((e) => e.setAttribute('href', `{action.url}`));
    imgs.forEach((e) => {
      e.setAttribute('src', `{action.image}`);
      e.setAttribute('alt', '{action.name}');
    });
    names.forEach((e) => (e.textContent = `{action.name}`));
    descriptions.forEach((e) => (e.textContent = `{action.description}`));
  }

  /**
   * Подготовка блока для сохранения
   * Обозначение границ блока и акций
   * Проверка количества акций в блоке
   */
  _prepareBlockForServer(block, data) {
    let items = block.querySelectorAll(`[${this.itemAttr}]`);

    let ids = [];

    items.forEach((item) => {
      this._prepareItemForServer(item);

      let id = item.dataset[this.itemName];
      ids.push(id);
      item.insertAdjacentHTML('beforebegin', `{${this.itemTag}}`);
      item.insertAdjacentHTML('afterend', `{/${this.itemTag}}`);
    });

    if (items.length < data.count) {
      this.controller.updateBlock(data.id, {
        count: items.length,
      });
    }

    let idsAttr = ids.join(',');
    let startTag = `{${this.blockTag} count="${items.length}"
    goods="${idsAttr}" siteId="${data.resourceId}"}`;

    block.insertAdjacentHTML('beforebegin', startTag);
    block.insertAdjacentHTML('afterend', `{/${this.blockTag}}`);
  }

  /**
   * Подготовка кода письма для сохранения
   * Проверка наличия всех сохраненных блоков
   */
  prepareForServer(codeBlock) {
    let blocks = [];

    let itemsBlocks = codeBlock.querySelectorAll(`[${this.blockAttr}]`);
    itemsBlocks.forEach((block) => {
      let blockId = block.dataset[this.blockName];
      blocks.push('' + blockId);

      let blockData = this.controller.getBlock(blockId);
      this._prepareBlockForServer(block, blockData);
    });

    let removedBlocks = [];
    this.controller.blocks().forEach((block) => {
      if (!blocks.includes('' + block.id) && !blocks.includes('' + block.tempId)) {
        removedBlocks.push(block);
      }
    });

    removedBlocks.forEach((block) => {
      this.controller.blocks.remove(block);
    });

    return codeBlock;
  }

  /**
   * Подготовка акции к выводу в редактор
   * Замена тегов на данные
   */
  _prepareItemForClient(element, itemId) {
    let data = this.controller.getItem(itemId);

    if (!data) return;

    let names = element.querySelectorAll('[data-name]');
    let descriptions = element.querySelectorAll('[data-description]');
    let imgs = element.querySelectorAll('[data-img]');
    let links = element.querySelectorAll('[data-link]');

    links.forEach((e) => e.setAttribute('href', data.link));
    names.forEach((e) => (e.textContent = data.name));
    descriptions.forEach((e) => (e.textContent = data.description));
    imgs.forEach((e) => {
      e.setAttribute('src', data.picture);
      e.setAttribute('alt', data.name);
    });
  }

  /**
   * Подготовка блока к выводу в редактор
   * Выбор элементов для вывода
   */
  _prepareBlockForClient(block, data) {
    let itemsData = data[this.blockName];
    let count = Math.min(itemsData.length, data.count);
    let items = itemsData.slice(0, count);

    let elements = block.querySelectorAll(`[${this.itemAttr}]`);
    elements.forEach((item, index) => {
      this._prepareItemForClient(item, items[index]);
    });

    // удалить лишние элементы и данные
  }

  /**
   * Подготовка сохраненного кода для вывода в редактор
   * Удаление тегов
   */
  prepareForClient(codeBlock) {

    let codeString = codeBlock.innerHTML;

    let blockStartRegex = new RegExp(`{${this.blockTag}[^}]*}`, 'g');
    let blockEndRegex = new RegExp(`{\/${this.blockTag}}`, 'g');
    let itemStartRegex = new RegExp(`{${this.itemTag}[^}]*}`, 'g');
    let itemEndRegex = new RegExp(`{\/${this.itemTag}}`, 'g');

    codeString = codeString.replace(blockStartRegex, '');
    codeString = codeString.replace(blockEndRegex, '');
    codeString = codeString.replace(itemStartRegex, '');
    codeString = codeString.replace(itemEndRegex, '');
    codeBlock.innerHTML = codeString;

    let blocksData = this.controller.blocks();
    let blocksEls = [];

    let blocks = codeBlock.querySelectorAll(`[${this.blockAttr}]`);
    blocks.forEach((block, index) => {
      let blockData = blocksData[index];

      if (!blockData) {
        block.removeAttribute(this.blockAttr);
      } else {
        block.setAttribute(this.blockAttr, blockData.id);
        blocksEls.push(blockData.id);
        this._prepareBlockForClient(block, blockData);
      }
    });

    // удалить лишние блоки из данных

    return codeBlock;
  }
}
