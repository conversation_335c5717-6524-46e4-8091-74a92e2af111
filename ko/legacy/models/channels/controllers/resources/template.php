<template id="resource-items-modal-template">

  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title" data-bind="text: controller.texts.title">
      </h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: function() { close(); }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body">
      <div class="form-group">
        <label class="form-label">Ресурс</label>

        <button class="btn-question" data-bind="tooltip" type="button" title="Ресурс">
        </button>

        <!-- ko if: controller.resourcesDirectory.loaded -->
        <div class="select2-wrapper">
          <select data-bind="
              value: controller.resourceId,
              lazySelect2: {
                wrapperCssClass: 'select2-container--form-control',
                containerCssClass: 'form-control',
                allowClear: false,
              }
            ">
            <!-- ko foreach: controller.resourcesDirectory.data -->
            <option data-bind="value: id, text: title"></option>
            <!-- /ko -->
          </select>
        </div>
        <!-- /ko -->

        <!-- ko template: {
          foreach: templateIf(blocked(), $data),
          afterAdd: fadeAfterAddFactory(200),
        } -->
        <div class="mt-3 f-fs-1">
          <!-- ko text: controller.texts.invalidResourceMessage -->
          <!-- /ko -->
          <br>
          <a href="/foquz/settings?tab=resources">Перейти к добавлению URL</a>
        </div>
        <!-- /ko -->
      </div>

      <div class="form-group" data-bind="css: {
        'form-group--disabled': blocked
      }">
        <label class="form-label" data-bind="text: controller.texts.count"></label>
        <div class="mb-4  number-control--small" data-bind="component: {
              name: 'number-control',
              params: {
                className: 'number-control--small',
                  value: controller.count,
                  isInvalid: $component.formControlErrorStateMatcher(controller.count),
                  isDisabled: blocked
              }
          }">
        </div>
      </div>

      <div class="form-group" data-bind="css: {
        'form-group--disabled': blocked
      }">
        <label class="form-label" data-bind="text: controller.texts.items"></label>

        <button class="btn-question" data-bind="tooltip" type="button" title="Акции">
        </button>

        <!-- ko template: {
          foreach: templateIf(controller.loading(), $data),
        } -->
        <div class="spinner-block" style="height: 48px">
          <i class="fa fa-spinner fa-pulse fa-2x fa-fw color-active"></i>
        </div>
        <!-- /ko -->

        <!-- ko template: {
          foreach: templateIf(!controller.loading(), $data),
        } -->
        <div class="select2-wrapper">
          <!-- ko ifnot: controller.mode == 'products' -->
          <select multiple data-bind="
            disable: blocked,
            selectedOptions: controller.selected,
            lazySelect2: {
                wrapperCssClass: 'select2-container--form-control',
                containerCssClass: 'form-control',
                minimumResultsForSearch: 0,
              }
            " data-placeholder="Все">

            <!-- ko foreach: controller.list -->
            <option data-bind="value: id, text: name"></option>
            <!-- /ko -->


          </select>
          <!-- /ko -->

          <!-- ko if: controller.mode == 'products' -->
            <!-- ko component: {
              name: 'categories-items-select',
              params: {
                selectedOptions: controller.selected,
                disable: blocked,
                list: controller.list,
                itemsProperty: 'products'
              }
            } -->
            <!-- /ko -->
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- ko template: {
          foreach: willBeRotate(),
          afterAdd: fadeAfterAddFactory(200),
        } -->
        <div class="mt-3 form-hint" data-bind="text: $parent.controller.texts.rotationMessage"></div>
        <!-- /ko -->
      </div>


    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="f-btn f-btn-link" data-bind="click: function() { close() }">
          Отменить
        </button>

        <button type="submit" class="f-btn f-btn-success" data-bind="click: submit">
          Добавить
        </button>
      </div>
    </div>
  </div>

</template>

<template id="resource-items-modal-dialog-template">
  <div class="modal-dialog modal-dialog-md modal-dialog-centered resource-items-modal-dialog" data-bind="component: {
      name: 'resource-items-modal',
      params: {
        data: data,
        modalElement: modalElement,
        close: close,
      }
    }" role="document">
  </div>
</template>
