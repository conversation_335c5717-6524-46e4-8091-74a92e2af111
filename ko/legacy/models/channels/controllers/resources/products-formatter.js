import { Formatter } from './formatter';

const currencies = {
  RUR: '₽',
  USD: '$',
  EUR: '€',
};

export class ProductsFormatter extends Formatter {
  get itemName() {
    return 'product';
  }

  get blockName() {
    return 'products';
  }

  get itemTag() {
    return 'good';
  }

  get blockTag() {
    return 'goods';
  }

  /**
   * Код одного продукта
   * Каждый продукт имеет атрибут data-product
   * Ссылки на продукт отмечены атрибутом data-link
   * Изображение продукта отмечено атрибутом data-img
   * Название продукта отмечено атрибутом data-name
   * Описание продукта отмечено атрибутом data-description
   * Категория продукта отмечена атрибутом data-category
   * Цена продукта отмечена атрибутом data-price
   * Старая цена продукта отмечена атрибутом data-old-price
   */
  _generateItemCode(itemId) {
    let item = this.controller.getItem(itemId);

    let img = `<a data-link href="${item.url}"
        style="display: block; width: 100%; font-size: 0; margin-bottom: 15px;">
        <img data-img src="${item.picture}"
          alt="${item.name}" width="220"
          style="border-radius: 6px; width: 100%; display: block;" />
      </a>`;

    let name = `<a data-link data-name
      style="display: block; font-size: 17px; font-weight: bold; margin-bottom: 8px; text-decoration: none; color: #2E2F31; line-height: 1.1;">
        ${item.name}
      </a>`;

    let category =
      item.category && item.category.name
        ? `<div data-category
      style="font-size: 13px; font-weight: normal; margin-bottom: 10px; text-decoration: none; color: #73808D;">
        ${item.category.name}
      </div>`
        : '';

    let descriptionText = '';
    if (item.description) {
      descriptionText =
      item.description.length > 72
        ? item.description.slice(0, 73) + '...'
        : item.description;
    }


    let description = `<div data-description
      style="font-size: 13px; font-weight: normal; margin-bottom: 10px; text-decoration: none; height: 48px;">
        ${descriptionText || ''}
      </div>`;

    let price = `<div>
        <span style="font-size: 22px; font-weight: bold; color: #2E2F31; vertical-align: top; margin-right: 10px;">
          <span data-price>${item.price}</span>
          <span data-currency>${currencies[item.currencyId]}</span>
        </span>
        <span style="font-size: 15px; font-weight: bold; color: #73808D; text-decoration: line-through; vertical-align: top;">
          <span style="vertical-align: top" data-old-price>${item.oldprice}</span>
          <span style="vertical-align: top" data-currency>${currencies[item.currencyId]}</span>
        </span>
      </div>`;

    let code = `
      <td valign="top" align="center" class="product-block" data-product>
        <table style="border: 1px solid #ECEFF1; border-radius: 9px; width: 100%; max-width: 240px; height: 100%; border-collapse: separate;">
          <tbody>
            <tr>
              <td style="padding: 10px; font-family: Arial, Helvetica, sans-serif">
                ${img}
                <div style="height: 140px; line-height: 1.2;">
                  ${name}
                  ${category}
                  ${description}
                </div>

                ${price}
              </td>
            </tr>
          </tbody>
        </table>
      </td>
    `;
    return code;
  }

  /**
   * Код одного ряда продуктов
   */
  _generateRowCode(items) {
    return `
      <tr>
        ${items.map((i) => this._generateItemCode(i)).join('')}
      </tr>
    `;
  }

  /**
   * Код нового блока продуктов
   * Каждый блок имеет атрибут data-products={block_id}
   */
  _createBlock(blockData) {
    let selected = blockData.productsList;
    let count = blockData.count;
    let id = blockData.tempId;

    let items = selected.slice(0, count);
    let rows = [];

    for (let i = 0; i < items.length; i += 3) {
      let row = [items[i], items[i + 1], items[i + 2]].filter(Boolean);
      rows.push(row);
    }

    return `
    <table class="actions" data-products="${id}" data-count="${count}" data-items="${items}" style="max-width: 740px;">
      <tbody>
        ${rows.map((r) => this._generateRowCode(r)).join('')}
      </tbody>
    </table>`;
  }

  /**
   * Подготовка продукта для сохранения
   * Замена данных на теги
   */
  _prepareItemForServer(element) {
    super._prepareItemForServer(element);

    let categories = element.querySelectorAll('[data-category]');
    let oldPrices = element.querySelectorAll('[data-old-price]');
    let prices = element.querySelectorAll('[data-price]');

    categories.forEach((e) => (e.textContent = `{good.category}`));
    oldPrices.forEach((e) => (e.textContent = `{good.oldprice}`));
    prices.forEach((e) => (e.textContent = `{good.price}`));
  }

  /**
   * Подготовка продукта к выводу в редактор
   * Замена тегов на данные
   */
  _prepareItemForClient(element, productId) {
    super._prepareItemForClient(element, productId);

    let data = this.controller.getItem(productId);
    if (!data) return;

    let categories = element.querySelectorAll('[data-category]');
    let oldPrices = element.querySelectorAll('[data-old-price]');
    let prices = element.querySelectorAll('[data-price]');

    categories.forEach((e) => (e.textContent = data.category ? data.category.name : ''));
    oldPrices.forEach((e) => (e.textContent = data.oldprice));
    prices.forEach((e) => (e.textContent = data.price));
  }

  _prepareBlockForClient(block, data) {
    //let itemsData = data[this.blockName];
    //let count = Math.min(itemsData.length, data.count);
    let items = this.controller.getBlockProductsList(data);

    let elements = block.querySelectorAll(`[${this.itemAttr}]`);
    elements.forEach((item, index) => {
      this._prepareItemForClient(item, items[index]);
    });
  }
}
