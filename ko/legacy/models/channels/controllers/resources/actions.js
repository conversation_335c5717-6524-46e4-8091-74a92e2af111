import { ResourcesController } from '.';
import { ActionsFormatter } from './actions-formatter';

export class ActionsController extends ResourcesController {
  constructor() {
    super();
    this.mode = 'actions';
  }

  get _formatter() {
    return new ActionsFormatter(this);
  }

  get texts() {
    return {
      title: 'Добавить акции',
      invalidResourceMessage: `Добавление акций с сайта невозможно, так как у выбранного ресурса не добавлен URL адрес для акций.`,
      count: 'Кол-во акций',
      items: 'Акции',
      rotationMessage: `Отображение акций в письме будет ротироваться`,
    };
  }

  _isResourceValid() {
    return this.resource() && this.resource().discounts_url;
  }

  _loadItems() {
    return new Promise((res) => {
      $.ajax({
        url: `${APIConfig.baseApiUrlPath}external-resources/discounts?id=${
          this.resource().id
        }&access-token=${APIConfig.apiKey}`,
        method: 'GET',
        success: (data) => {
          res(data.items);
        },
      });
    });
  }

  getBlockModel() {
    return {
      id: 0,
      count: this.count(),
      discounts: this.selected(),
      resourceId: this.resourceId()
    };
  }

  updateData(data) {
    let blocks = data.discount_blocks || data.discount_block || [];
    this.blocks(
      blocks.map((blockData) => {
        let discounts = blockData.discounts;
        discounts.forEach((discountData) => {
          if (!(discountData.id in this.cache)) {
            this.cache[discountData.id] = discountData;
          }
        });

        return {
          id: blockData.id,
          count: parseInt(blockData.count),
          discounts: discounts.map(d => d.id),
        };
      }),
    );
    
  }




}
