const resourcesDirectory = new Directory('external-resources');

import 'Legacy/components/categories-items-select';

let counter = 0;
export class ResourcesController {
  constructor() {
    this.blocks = ko.observableArray([]);
    this.cache = {};

    this.resourcesDirectory = resourcesDirectory;
    this.resourcesDirectory.load();

    this.resourceId = ko.observable(null);
    this.count = ko.observable(1);
    this.selected = ko.observableArray([]);
    this.selectedCount = ko.pureComputed(() => {
      return this.selected().length;
    });

    this.list = ko.observableArray([]);
    this.loading = ko.observable(false);
    this.loadItems = utils.debounce(() => {
      this.loading(true);
      this._loadItems().then((items) => {
        this.list(items);
        this.loading(false);
      });
    }, 200);

    this.isResourceValid = ko.observable(true);

    this.resource = ko.pureComputed(() => {
      let resourceId = this.resourceId();
      if (!resourceId) return null;

      return this.resourcesDirectory.getById(resourceId);
    });

    this.resourceId.subscribe((v) => {
      this.selected([]);
      this.list([]);
    });

    this.resource.subscribe((_) => {
      this.updateResource();
    });

    this.formatter = this._formatter;
  }

  open() {
    return new Promise((res) => {
      this.resourcesDirectory.onLoad(() => {
        if (this.resourcesDirectory.data().length) {
          window.modalOpens.push({
            dialogTemplateName: 'resource-items-modal-dialog-template',
            data: {
              controller: this,
            },
            close: (result) => {
              if (result) {
                let block = this.getBlockModel();
                this.saveSelectedInCache();
                this.reset();
                block.tempId = 'tmp' + counter++;
                this.blocks.push(block);
                res(block);
              } else {
                this.reset();
              }
            },
          });
        } else {
        }
      });
    });
  }

  _isResourceValid() {
    return true;
  }

  updateResource() {
    this.isResourceValid(this._isResourceValid());

    if (this.isResourceValid()) {
      this.loadItems();
    }
  }

  _loadItems() {
    return Promise.resolve([]);
  }

  generateCode(blockData) {
    return this.formatter.createBlock(blockData);
  }

  getBlockModel() {
    return {
      id: 0,
      count: this.count(),
      items: this.selected(),
    };
  }

  saveSelectedInCache() {
    this.selected().forEach((i) => {
      let item = this.list().find((item) => item.id == i);
      if (item) {
        this.cache[i] = item;
      }
    });
  }

  reset() {
    this.selected([]);
    this.resourceId('');
    this.count(1);
  }

  apply() {
    // TODO проверить на валидность
    return Promise.resolve();
  }

  updateData(data) {}

  getItem(itemId) {
    if (itemId in this.cache) {
      return this.cache[itemId];
    }

    return null;
  }

  getBlock(blockId) {
    return this.blocks().find((block) => {
      return block.id == blockId || block.tempId == blockId;
    });
  }

  updateBlock(blockId, blockData) {
    let block = this.getBlock(blockId);
    if (blockData.count) {
      block.count = blockData.count;
    }
  }


  getBlocks() {
    return this.blocks();
  }

  prepareForServer(codeBlock) {
    return this.formatter.prepareForServer(codeBlock);
  }
  prepareForClient(codeBlock) {
    return this.formatter.prepareForClient(codeBlock);
  }
}

ko.components.register('resource-items-modal', {
  viewModel: function (params) {
    this.close = params.close;
    this.controller = params.data.controller;
    this.isSubmitted = ko.observable(false);

    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted,
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted,
    );

    this.submit = () => {
      this.isSubmitted(true);

      this.controller.apply().then(() => {
        this.close(true);
      });
    };

    this.blocked = ko.pureComputed(() => {
      return !this.controller.isResourceValid();
    });

    this.willBeRotate = ko.pureComputed(() => {
      return this.controller.count() < this.controller.selectedCount();
    });
  },
  template: {
    element: 'resource-items-modal-template',
  },
});
