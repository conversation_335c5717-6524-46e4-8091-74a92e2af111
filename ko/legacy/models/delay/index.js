export function DelayDays(days) {
  return ko.observable(days).extend({});
}

export function DelayTime(time) {
  return ko.observable(time).extend({
    validation: {
      validator: (v) => {
        if (!v) return true;
        return moment(v, "HH:mm").format("HH:mm") !== "Invalid date";
      },
    },
  });
}

export function getDelayText(days = 0, time = "") {
  let delayString = "";

  const delayDays = parseInt(days) * 24;
  const delayTime = time ? time.split(":") : [0, 0];

  const delayTimeHours = parseInt(delayTime[0]);
  const delayTimeMinutes = parseInt(delayTime[1]);

  if (delayDays + delayTimeHours > 0)
    delayString = delayDays + delayTimeHours + "ч. ";
  if (delayTimeMinutes > 0) delayString += delayTimeMinutes + "мин.";

  return delayString || "";
}

ko.components.register("delay-group", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const viewModel = new (function () {
        this.disabled = params.disabled;

        this.days = params.days;
        this.time = params.time;
        this.errorMatcher = params.errorMatcher;
        this.successMatcher = params.successMatcher;
        this.short = params.short;

        if (params.channel) {
          this.errorMatcher = params.channel.formControlErrorStateMatcher;
          this.successMatcher = params.channel.formControlSuccessStateMatcher;
        }

        this.daysErrorStateMatcher = this.errorMatcher(this.days);
        this.daysSuccessStateMatcher = this.successMatcher(this.days);

        this.timeErrorStateMatcher = this.errorMatcher(this.time);
        this.timeSuccessStateMatcher = this.successMatcher(this.time);

        this.isInvalid = ko.pureComputed(() => {
          return this.daysErrorStateMatcher() || this.timeErrorStateMatcher();
        });

        this.formatDays = () => {};

        this.formatTime = () => {
          const time = this.time();
          if (!time) return;
          let digits = time.replace(":", "").split("");
          if (digits.length < 4) {
            digits.splice(0, 0, ...[...Array(4 - digits.length)].map((_) => 0));
          }
          digits.length = 4;
          digits.splice(2, 0, ":");
          this.time(digits.join(""));
        };
      })();

      return viewModel;
    },
  },
  template: {
    element: "delay-template",
  },
});
