export class GroupActions {
  constructor(config) {
    this.selectedCount = config.selectedCount;
    this.selectAll = config.selectAll;
    this.actions = config.actions;
    this.apply = config.apply;
    this.reset = config.reset;
    this.action = ko.observable("");

    this.folderResultTemplate = select2templates.folder.result;
    this.folderSelectionTemplate = select2templates.folder.selection;

    this.foldersLoaded = ko.observable(false);
    this.folders = ko.observableArray([]);
    this.folder = ko.observable("");

    this.shown = ko.observable(false);
    this.selectedCount.subscribe((v) => {
      this.shown(v > 0);
    });
    this.shown.subscribe((v) => {
      if (v) {
        this.foldersLoaded = ko.observable(false);
        if (typeof config.getFolders == "function") {
          config.getFolders().then((folders) => {
            this.folders(folders);
            this.foldersLoaded(true);
          });
        }
      } else {
        this.action("");
      }
    });

    this.shown(this.selectedCount() > 0);

    this.type = ko.computed(() => {
      let action = this.action();
      if (!action) return "";
      let actionData = this.actions.find((a) => a.id == action);
      if (!actionData) return "";
      return actionData.type || "";
    });

    this.disabled = ko.pureComputed(() => {
      if (!this.action()) return true;
      if (this.type() == "folder" && !this.folder() && this.folder() !== 0)
        return true;
      return false;
    });
  }

  getFolderName(folderId, folders) {
    if (!folderId) return "";

    let items = folders || this.folders();

    for (let i = 0, count = items.length; i < count; i++) {
      const folder = items[i];
      if (folder.id == folderId) return folder.name;
      if (folder.items) {
        const name = this.getFolderName(folder.id, folder.items);
        if (name) return name;
      }
    }

    return false;
  }

  getData() {
    if (this.type() == "folder") {
      let folderId = this.folder();
      let folderName = this.getFolderName(folderId);

      return {
        folderId,
        folderName,
      };
    }
    return {};
  }

  applyAction() {
    let action = this.action();
    let data = this.getData();
    this.apply(action, data);
  }
}

ko.components.register("group-actions", {
  viewModel: function (params) {
    this.controller = params.controller;
  },
  template: {
    element: "group-actions-template",
  },
});
