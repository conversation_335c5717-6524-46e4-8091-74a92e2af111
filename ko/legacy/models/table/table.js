import "Legacy/modals/columns-modal";
import { getTableSettings, setTableSettings } from "Utils/table";

import "./scroll-container";

function getColumnsState(localStorageKey) {
  return getTableSettings(localStorageKey);
}

class Column {
  constructor(id, columnName, visible, placeholder) {
    this.id = id;
    this.name = columnName;
    this.visible = ko.observable(!!visible);
    this.placeholder = placeholder ? placeholder : "";
  }
}

export class TableModel {
  constructor(config) {
    this.localStorageKey = config.localStorageKey;

    this.columns = {};
    this.columnsList = config.columns.map((c) => c[0]);

    config.columns.forEach((column) => {
      this.columns[column[0]] = new Column(
        column[0],
        column[1],
        true,
        column[3]
      );
    });

    getColumnsState(this.localStorageKey).then((defaultState) => {
      if (defaultState) {
        this.columnsList.forEach((cId) => {
          let c = this.columns[cId];
          if (cId in defaultState) {
            c.visible(defaultState[cId]);
          }
        });
      }
    });
  }

  editColumns() {
    window.modalOpens.push({
      dialogTemplateName: "columns-modal-dialog-template",
      data: {
        columns: Object.keys(this.columns).map(
          (columnName) => this.columns[columnName]
        ),
        localStorageKey: this.localStorageKey,
      },
    });
  }

  columnsCount() {
    return Object.keys(this.columns).filter((key) => {
      return this.columns[key].visible();
    }).length;
  }
}
