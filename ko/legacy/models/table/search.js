/**
 * Поиск в таблице
 */

class SearchParamModel {
  constructor(id, value) {
    this.id = id;
    this.value = ko.observable(value || '');
  }
}

export class SearchModel {
  constructor(config) {
    this.fields = config.fields;
    this.name = config.paramName || 'search';

    this.fields.forEach((fieldName) => {
      this[fieldName] = new SearchParamModel(fieldName, '');
    });

    this.onChange = config.onChange;
  }

  setDefault(searchParams) {
    searchParams = searchParams || new URLSearchParams(location.search);
    this.fields.forEach((fieldName) => {
      this[fieldName].value(searchParams.get(`${this.name}[${fieldName}]`));
    });
  }

  saveParams() {

  }

  getParams() {
    const params = {};
    this.fields.forEach((fieldName) => {
      let field = this[fieldName];
      if (!field.value()) return;
      params[field.id] = field.value();
    });
    return params;
  }

  reset() {
    this.fields.forEach((fieldName) => {
      let field = this[fieldName];
      field.value('');
    });
  }
}
