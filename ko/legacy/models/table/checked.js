export class CheckedModel {
  constructor(config) {
    this.items = config.items;
    this.totalCount = config.totalCount;

    this.allItemsChecked = ko.observable(false);
    this.checkedCount = ko.observable(0);

    this.items.subscribe((v) => {
      if (!v.length) {
        this.allItemsChecked(false);
        this.checkedCount(0);
      }
    });

    this.onChange = ko.observable(true).extend({ notify: 'always' })
  }

  checkAll() {
    this.items().forEach((i) => {
      i.checked(true);
    });
    this.allItemsChecked(true);
    this.checkedCount(this.totalCount());
    this.onChange(true);
  }

  uncheckAll() {
    this.items().forEach((i) => {
      i.checked(false);
    });
    this.allItemsChecked(false);
    this.checkedCount(0);
    this.onChange(true);
  }

  toggleAll() {
    if (this.items().length == 0) return;

    if (this.allItemsChecked()) {
      this.uncheckAll();
    } else {
      this.checkAll();
    }
  }

  checkItem(item) {
    if (!item.checked()) {
      item.checked(true);
      this.checkedCount(this.checkedCount() + 1);
      if (this.checkedCount() == this.totalCount()) this.allItemsChecked(true);
      this.onChange(true);
    }
  }

  uncheckItem(item) {
    if (item.checked()) {
      item.checked(false);
      this.checkedCount(this.checkedCount() - 1);
      if (this.checkedCount() == 0) this.allItemsChecked(false);
      this.onChange(true);
    }
  }

  toggleItem(item) {
    if (item.checked()) {
      item.checked(false);
      this.checkedCount(this.checkedCount() - 1);
      if (this.checkedCount() == 0) this.allItemsChecked(false);
    } else {
      item.checked(true);
      this.checkedCount(this.checkedCount() + 1);
      if (this.checkedCount() == this.totalCount()) this.allItemsChecked(true);
    }
    this.onChange(true);
  }

  getChecked() {
    return this.items().filter(i => i.checked())
  }
}
