/**
 * Сортировка в таблице
 */

function formatSortString(str) {
  let value = '';
  let asc = false;

  if (str[0] === '-') {
    value = str.slice(1);
  } else {
    value = str;
    asc = true;
  }

  return {
    value,
    asc,
  };
}

export class SortModel {
  constructor(config) {
    this.fields = config.fields;
    this.name = config.paramName;

    this.defaultValue = config.defaultValue;

    let { value, asc } = formatSortString(config.defaultValue);

    this.field = ko.observable(value);
    this.asc = ko.observable(asc);

    this.onChange = config.onChange;
  }

  setDefault(searchParams) {
    searchParams = searchParams || new URLSearchParams(location.search);
    const sortParam = searchParams.get(this.name);

    if (sortParam) {
      let { value, asc } = formatSortString(sortParam);
      if (this.fields.includes(value)) {
        this.field = ko.observable(value);
        this.asc = ko.observable(asc);
      }
    }
  }

  getParams() {
    return `${this.asc() ? '' : '-'}${this.field()}`;
  }

  saveParams() {

  }

  sortBy(fieldName) {
    if (!this.fields.includes(fieldName)) return;

    if (fieldName === this.field()) {
      this.asc(!this.asc());
    } else {
      this.asc(true);
      this.field(fieldName);
    }

    this.onChange();
  }

  reset() {
    let { value, asc } = formatSortString(this.defaultValue);

    this.field(value);
    this.asc(asc);
  }
}
