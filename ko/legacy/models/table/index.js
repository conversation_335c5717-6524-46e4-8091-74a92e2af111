import { TableModel } from "./table";
import { SortModel } from "./sort";
import { SearchModel } from "./search";

export function TableListModel(ctx, config) {
  const columns = config.columns;

  ctx.table = new TableModel({
    localStorageKey: config.localStorageKey,
    columns: columns.map((c) => {
      return [c.id, c.name, true, c.placeholder];
    }),
  });

  ctx.sort = new SortModel({
    paramName: config.sortParam || "order",
    fields: columns.filter((c) => c.sortable).map((c) => c.id),
    defaultValue: config.defaultSort,
    onChange: config.onChange,
  });

  ctx.search = new SearchModel({
    paramName: config.searchParam || "search",
    fields: columns.filter((c) => c.searchable).map((c) => c.id),
    onChange: config.onChange,
  });
}
