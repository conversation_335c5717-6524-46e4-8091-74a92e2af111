<?= $this->render('../../modals/columns-modal/template.php'); ?>
<?= $this->render('./group-actions-template.php'); ?>

<template id="table-head-cell-template">
  <!-- ko if: column.visible -->
  <th class="f-table__head-cell f-table__cell" data-bind="class: 'f-table__cell--' + column.id, attr: {
    width: $data.width
  }">
    <div class="f-table__cell-content">
      <div class="f-table__cell-name" data-bind="
        click: function() { sortModel.sortBy(column.id) },
        css: { 'cursor-pointer': sortModel.fields.includes(column.id), 
          'foq-table__head-cell-title--active': sortModel.field() == column.id, }">

        <!-- ko text: column.name -->
        <!-- /ko -->

        <span class="f-table__sorting-order f-icon f-icon--sorting-order f-transform--transition ml-1" data-bind="css: {
          'f-transform-rotate-180': sortModel.asc() && sortModel.field() == column.id,
          'f-icon-primary': sortModel.field() == column.id,
          'f-icon--sorting-order--default': sortModel.field() !== column.id
        }">
          <svg>
            <use href="#sorting-order-icon"></use>
          </svg>
        </span>

        <!-- ko if: searchModel[column.id] -->
        <!-- ko if: searchModel[column.id].value -->

        <span class=" f-table__filter f-icon f-icon--filter f-icon-primary">
          <svg>
            <use href="#filter-icon"></use>
          </svg>
        </span>
        <!-- /ko -->
        <!-- /ko -->


      </div>

      <!-- ko if: searchModel[column.id] -->
      <input class="f-table__search my-1" data-bind="textInput: searchModel[column.id].value, onEnter: onSearch, attr: {placeholder: column.placeholder}">
      <!-- /ko -->
    </div>
  </th>
  <!-- /ko -->
</template>