<template id="group-actions-template">
  <div class="foq-table__group-actions-wrapper mailings__details-modal-dialog-table-group-actions-wrapper" data-bind="using: controller">
    <div class="foq-table__group-actions mailings__details-modal-dialog-table-group-actions" data-bind="fade: shown">
      <span class="foq-table__group-actions-counter">
        <span class="foq-table__group-actions-counter-label" data-bind="text: _t('Выбрано')"></span>
        <span class="foq-table__group-actions-counter-value" data-bind="text: selectedCount()"></span>
      </span>

      <!-- ko if: selectAll -->
      <button type="submit" class="btn btn-default foq-table__group-actions-select-all-button" data-bind="click: selectAll, text: _t('Выбрать все')"></button>
      <!-- /ko -->

      <select data-bind="value: action,
      lazySelect2: {
          containerCssClass: 'form-control',
          wrapperCssClass: 'select2-container--form-control foq-table__group-actions-selector',
          dropdownCssClass: 'foq-table__group-actions-selector-dropdown',
          minimumResultsForSearch: 0,
          allowClear: true,
          placeholder: _t('Выберите действие')
      }">
        <option></option>
        <!-- ko foreach: actions -->
        <option data-bind="value: id, text: text"></option>
        <!-- /ko -->
      </select>

      <!-- ko template: {
        foreach: templateIf(type() == 'folder' && foldersLoaded(), $data),
        afterAdd: fadeAfterAddFactory(250),
        beforeRemove: fadeBeforeRemoveFactory(250)
      } -->
      <select style="width: 200px" data-bind="value: folder,
        lazySelect2: {
            containerCssClass: 'form-control',
            wrapperCssClass: 'select2-container--form-control foq-table__group-actions-selector',
            dropdownCssClass: 'foq-table__group-actions-selector-dropdown group-actions-folders-dropdown',
            minimumResultsForSearch: 0,
            allowClear: true,
            templateResult: folderResultTemplate,
            templateSelection: folderSelectionTemplate,
            placeholder: _t('Выберите папку'),
            width: '200px',
            // dropdownAutoWidth: false
        }" >
          <option></option>
          <!-- ko foreach: folders -->
          <!-- ko template: {
                  name: 'folder-template',
                  data: {
                  folder: $data,
                  level: $data.level
                  }
              } -->
          <!-- /ko -->
          <!-- /ko -->
        </select>
      <!-- /ko -->


      <button type="submit" class="btn btn-danger foq-table__group-actions-dismiss-button" data-bind="click: reset">
      </button>
      <button type="submit" class="btn btn-success foq-table__group-actions-apply-button" data-bind="click: applyAction, attr: { disabled: disabled }">
      </button>
    </div>
  </div>
</template>
