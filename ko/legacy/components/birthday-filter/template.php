<template id="birthday-filter-template">
  <div class="row">
    <div class="col-6">
      <div class="form-group">
        <label class="form-label">По дате рождения</label>
        <select data-bind="
            value: model.type,
            select2: {
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                allowClear: true,
                placeholder: 'Не выбрано'
            }
        ">
          <option></option>
          <option value="1">День рождения</option>
          <option value="2">Период</option>
          <option value="3">Исполнилось на дату</option>
          <option value="4">Месяц рождения</option>
          <option value="5">Дата рождения</option>
        </select>
      </div>
    </div>

    <div class="col-6">
      <div class="row flex-nowrap">

        <div class="col-12">
          <!-- ko template: {
            foreach: templateIf(model.type() === '1', $data),
            afterAdd: fadeAfterAddFactory(200)
          } -->
          <div class="">
            <div class=" form-group">
              <label class="form-label">Дата</label>
              <date-picker params="value: model.date"></date-picker>
            </div>
          </div>
          <!-- /ko -->

          <!-- ko template: {
            foreach: templateIf(model.type() === '2', $data),
            afterAdd: fadeAfterAddFactory(200)
          } -->
          <div class="">
            <div class="form-group">
              <label class="form-label">Период</label>
              <period-picker params="value: model.period, ranges: true"></period-picker>
            </div>
          </div>
          <!-- /ko -->

          <!-- ko template: {
            foreach: templateIf(model.type() === '3', $data),
            afterAdd: fadeAfterAddFactory(200)
          } -->
          <div class="w-100 overflow-hidden d-flex">
            <div class="mr-10p flex-shrink-1">
              <div class="form-group">
                <label class="form-label">Дата</label>
                <date-picker params="value: model.date"></date-picker>
              </div>
            </div>
            <div class="">
              <div class="form-group" style="width: 100px">
                <label class="form-label">Возраст, лет</label>
                <input class="form-control" placeholder="00" data-bind="textInput: model.age, mask, maskPattern: '00'">
              </div>
            </div>
          </div>
          <!-- /ko -->

          <!-- ko template: {
            foreach: templateIf(model.type() == '4', $data),
            afterAdd: fadeAfterAddFactory(200)
          } -->
          <div class="">

            <div class="form-group">
              <label class="form-label">Месяц</label>
              <select data-bind="
              value: model.month,
              select2: {
                containerCssClass: 'form-control',
                  wrapperCssClass: 'select2-container--form-control',
                  minimumResultsForSearch: 0,
              }
              ">
                <option value="1">Январь</option>
                <option value="2">Февраль</option>
                <option value="3">Март</option>
                <option value="4">Апрель</option>
                <option value="5">Май</option>
                <option value="6">Июнь</option>
                <option value="7">Июль</option>
                <option value="8">Август</option>
                <option value="9">Сентябрь</option>
                <option value="10">Октябрь</option>
                <option value="11">Ноябрь</option>
                <option value="12">Декабрь</option>
              </select>
            </div>
          </div>
          <!-- /ko -->

          <!-- ko template: {
            foreach: templateIf(model.type() === '5', $data),
            afterAdd: fadeAfterAddFactory(200)
          } -->
          <div class="d-flex">
            <div class="mr-10p">
              <div class="form-group" style="width: 55px">
                <label class="form-label">День</label>
                <input class="form-control px-1 text-center" placeholder="00" data-bind="textInput: model.day, mask, maskPattern: '00', numericIntervalField: { min: 1, max: 31 }, css: {
                'is-invalid': $component.formControlErrorStateMatcher(model.day),
              }">
              </div>
            </div>
            <div class="flex-grow-1">
              <div class="form-group">
                <label class="form-label">Месяц</label>
                <select data-bind="
                value: model.month,
                select2: {
                  containerCssClass: 'form-control',
                    wrapperCssClass: 'select2-container--form-control',
                    minimumResultsForSearch: 0,
                }
                ">
                  <option value="1">Январь</option>
                  <option value="2">Февраль</option>
                  <option value="3">Март</option>
                  <option value="4">Апрель</option>
                  <option value="5">Май</option>
                  <option value="6">Июнь</option>
                  <option value="7">Июль</option>
                  <option value="8">Август</option>
                  <option value="9">Сентябрь</option>
                  <option value="10">Октябрь</option>
                  <option value="11">Ноябрь</option>
                  <option value="12">Декабрь</option>
                </select>
              </div>
            </div>
          </div>
          <!-- /ko -->


        </div>

      </div>
      <!-- ko template: {
        foreach: $component.formControlErrorStateMatcher($component.model),
        afterAdd: fadeAfterAddFactory(200),
        beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
      <div class="form-error" data-bind="text: $parent.model.error" style="top: -26px; position: relative;"></div>
      <!-- /ko -->
    </div>
  </div>

</template>


<template id="birthday-filter-template-old">
  <div class="row mailings__select-clients-modal-dialog-birthday-row">
    <div class="col col-6">
      <div class="form-group">
        <label class="form-label">По дате рождения</label>

        <select data-bind="
                                value: model.type,
                                select2: {
                                    containerCssClass: 'form-control',
                                    wrapperCssClass: 'select2-container--form-control',
                                    allowClear: true,
                                    placeholder: 'Выберите условие'
                                }
                            ">
          <option></option>
          <option value="1">День рождения</option>
          <option value="2">Период</option>
          <option value="3">Исполнилось на дату</option>
          <option value="4">Месяц рождения</option>
        </select>
      </div>
    </div>

    <div class="col col-6 mailings__select-clients-modal-dialog-birthday-row-additional-controls-col">
      <div class="mailings__select-clients-modal-dialog-birthday-row-additional-controls-group-wrapper">
        <div class="mailings__select-clients-modal-dialog-birthday-row-additional-controls-group" data-bind="fade: model.type() === '1'">
          <div class="row mailings__select-clients-modal-dialog-birthday-row">
            <div class="col mailings__select-clients-modal-dialog-birthday-row-date-col">
              <div class="form-group">
                <label class="form-label">Дата</label>

                <div class="input-group date-input-group" data-bind="dateInputGroup">
                  <!-- ko let: { autosizeInput: ko.observable(null) } -->
                  <input class="form-control" placeholder="00.00.0000" style="min-width: 80px" data-bind="
                                                        value: model.date,
                                                        mask,
                                                        maskPattern: '00.00.0000',
                                                        autosizeInput: autosizeInput,
                                                        periodPicker,
                                                        periodPickerArrowPosition: { anchor: 'right', offset: -27 },
                                                        periodPickerSingle: true,
                                                        periodPickerShowDropdowns: true,
                                                        periodPickerApply: function () { autosizeInput().update(); }
                                                    ">
                  <!-- /ko -->

                  <i class="date-input-group__icon"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mailings__select-clients-modal-dialog-birthday-row-additional-controls-group" data-bind="fade: model.type() === '2'">
          <div class="form-group">
            <label class="form-label">Период</label>

            <div class="input-group date-input-group" data-bind="dateInputGroup">
              <!-- ko let: { autosizeInput: ko.observable(null) } -->
              <input class="form-control" placeholder="00.00.0000-00.00.0000" style="min-width: 80px" data-bind="
                                                value: model.period,
                                                mask,
                                                maskPattern: '00.00.0000-00.00.0000',
                                                autosizeInput: autosizeInput,
                                                periodPicker,
                                                periodPickerArrowPosition: { anchor: 'right', offset: -27 },
                                                periodPickerSeparator: '-',
                                                periodPickerDrops: 'up',
                                                periodPickerApply: function () { autosizeInput().update(); }
                                            ">
              <!-- /ko -->

              <i class="date-input-group__icon"></i>
            </div>
          </div>
        </div>

        <div class="mailings__select-clients-modal-dialog-birthday-row-additional-controls-group" data-bind="fade: model.type() === '3'">
          <div class="row mailings__select-clients-modal-dialog-birthday-row">
            <div class="col mailings__select-clients-modal-dialog-birthday-row-date-col">
              <div class="form-group">
                <label class="form-label">Дата</label>

                <div class="input-group date-input-group" data-bind="dateInputGroup">
                  <!-- ko let: { autosizeInput: ko.observable(null) } -->
                  <input class="form-control" placeholder="00.00.0000" style="min-width: 80px" data-bind="
                                                        value: model.date,
                                                        mask,
                                                        maskPattern: '00.00.0000',
                                                        autosizeInput: autosizeInput,
                                                        periodPicker,
                                                        periodPickerArrowPosition: { anchor: 'right', offset: -27 },
                                                        periodPickerSingle: true,
                                                        periodPickerShowDropdowns: true,
                                                        periodPickerApply: function () { autosizeInput().update(); }
                                                    ">
                  <!-- /ko -->

                  <i class="date-input-group__icon"></i>
                </div>
              </div>
            </div>

            <div class="col col-3 mailings__select-clients-modal-dialog-birthday-row-age-col" data-bind="fade: model.type() === '3'">
              <div class="form-group">
                <label class="form-label">Возраст, лет</label>

                <input class="form-control" placeholder="00" data-bind="value: model.age, mask, maskPattern: '00'">
              </div>
            </div>
          </div>
        </div>

        <div class="mailings__select-clients-modal-dialog-birthday-row-additional-controls-group" data-bind="fade: model.type() === '4'">
          <div class="form-group">
            <label class="form-label">Месяц</label>

            <select data-bind="
                                    value: model.month,
                                    select2: {
                                        containerCssClass: 'form-control',
                                        wrapperCssClass: 'select2-container--form-control',
                                        dropdownParent: modalElement,
                                        minimumResultsForSearch: 0
                                    }
                                ">
              <option value="1">Январь</option>
              <option value="2">Февраль</option>
              <option value="3">Март</option>
              <option value="4">Апрель</option>
              <option value="5">Май</option>
              <option value="6">Июнь</option>
              <option value="7">Июль</option>
              <option value="8">Август</option>
              <option value="9">Сентябрь</option>
              <option value="10">Октябрь</option>
              <option value="11">Ноябрь</option>
              <option value="12">Декабрь</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
