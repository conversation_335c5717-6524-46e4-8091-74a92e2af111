export class BirthdayFilterModel {
	constructor(config = {}) {
		this.config = config;

		this.type = ko.observable('');
		this.date = ko.observable('');
		this.period = ko.observable('');
		this.age = ko.observable('');
		this.month = ko.observable('');
		this.day = ko.observable('').extend({
			required: {
				onlyIf: () => this.type() == '5',
				message: 'Обязательное поле'
			}
		});

		this.callbacks = [];

		this.hasValue = ko.pureComputed(() => {
			if (!this.type()) return false;

			if (this.type() == '1') {
				return this.date();
			}

			if (this.type() == '2') {
				return this.period();
			}

			if (this.type() == '3') {
				return this.date() && this.age();
			}

			if (this.type() == '4') {
				return this.month();
			}

			if (this.type() == '5') {
				return this.day() && this.month();
			}
		});

		this.validationObject = ko.observable().extend({
			validation: [
				{
					validator: () => this.day.isValid(),
					message: () => this.day.error(),
				},
			],
		});

    this.isValid = this.validationObject.isValid;
    this.error = this.validationObject.error;

		[
			this.type,
			this.date,
			this.period,
			this.age,
			this.month,
			this.day,
		].forEach((f) =>
			f.subscribe((_) => {
				this.callbacks.forEach((cb) => cb());
			})
		);
	}

	formatDate(date) {
		if (!date) return '';
		let format = this.config.format || 'DD.MM.YYYY';
		return moment(date, 'DD.MM.YYYY').format(format);
	}

	getParams() {
		let period = utils.date.formatClientPeriod(this.period());
		return {
			type: this.type(),
			date: this.formatDate(this.date()),
			period: {
				date1: period[0],
				date2: period[1],
			},
			age: this.age(),
			month: this.month(),
			day: this.day(),
		};
	}

	setParams(params) {
		let type = params.type;
		if (['1', '2', '3', '4', '5'].includes(type)) {
			this.type(type);
		}
		let date = params.date;
		if (date) this.date(date);

		let period = params.period;
		if (period) this.period(period);

		let age = params.age;
		if (age) this.age(age);

		let month = params.month;
		if (month) this.month(month);

		let day = params.day;
		if (day) this.day(day);
	}

	reset() {
		this.type('');
		this.date('');
		this.period('');
		this.age('');
		this.month('');
		this.day('');
	}

	subscribe(cb) {
		this.callbacks.push(cb);
	}
}

ko.components.register('birthday-filter', {
	viewModel: function (params) {
		this.model = params.model;
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(params.showErrors);
	},
	template: {
		element: 'birthday-filter-template',
	},
});

ko.components.register('birthday-filter-dense', {
	viewModel: function (params) {
		this.model = params.model;
	},
	template: {
		element: 'birthday-filter-dense-template',
	},
});
