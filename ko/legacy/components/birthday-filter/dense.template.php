<template id="birthday-filter-dense-template">
  <div class="d-flex">
    <div class="mailings__details-modal-dialog-filter">
      <div class="form-group dense-form-group">
        <label class="form-label">По дате рождения</label>

        <select data-bind="
            value: model.type,
            select2: {
                wrapperCssClass: 'select2-container--form-control',
                dropdownCssClass: 'dense-form-group__dropdown',
                allowClear: true,
                placeholder: 'Не выбрано'
            }
        ">
          <option></option>
          <option value="1">День рождения</option>
          <option value="2">Период</option>
          <option value="3">Исполнилось на дату</option>
          <option value="4">Месяц рождения</option>
          <option value="5">Дата рождения</option>
        </select>
      </div>
    </div>

    <!-- ko template: {
      foreach: templateIf(['1', '3'].includes(model.type()), $data),
      afterAdd: fadeAfterAddFactory(200)
    } -->
    <div class="mailings__details-modal-dialog-filter ml-4">
      <div class="form-group dense-form-group">
        <label class="form-label">Дата</label>

        <div class="input-group date-input-group" data-bind="dateInputGroup">
          <!-- ko let: { autosizeInput: ko.observable(null) } -->
          <input class="form-control" placeholder="00.00.0000" style="min-width: 80px" data-bind="value: model.date, mask, maskPattern: '00.00.0000', autosizeInput: autosizeInput, periodPicker, periodPickerArrowPosition: { anchor: 'right', offset: -10 }, periodPickerSingle: true, periodPickerShowDropdowns: true, periodPickerApply: function () { autosizeInput().update(); }">
          <!-- /ko -->
          <i class="date-input-group__icon"></i>
        </div>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(model.type() === '2', $data),
      afterAdd: fadeAfterAddFactory(200)
    } -->
    <div class="mailings__details-modal-dialog-filter ml-4">
      <div class="form-group dense-form-group">
        <label class="form-label">Период</label>

        <div class="input-group date-input-group" data-bind="dateInputGroup">
          <!-- ko let: { autosizeInput: ko.observable(null) } -->
          <input class="form-control" placeholder="00.00.0000-00.00.0000" style="min-width: 80px" data-bind="value: model.period, mask, maskPattern: '00.00.0000-00.00.0000', autosizeInput: autosizeInput, periodPicker, periodPickerArrowPosition: { anchor: 'right', offset: -10 }, periodPickerSeparator: '-', periodPickerApply: function () { autosizeInput().update(); }">
          <!-- /ko -->
          <i class="date-input-group__icon"></i>
        </div>
      </div>
    </div>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(model.type() === '3', $data),
      afterAdd: fadeAfterAddFactory(200)
    } -->
    <div class="mailings__details-modal-dialog-filter ml-4">
      <div class="form-group dense-form-group">
        <label class="form-label">Возраст, лет</label>

        <input class="form-control" placeholder="00" data-bind="textInput: model.age, mask, maskPattern: '00'" style="width: 80px">
      </div>
    </div>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(model.type() === '5', $data),
      afterAdd: fadeAfterAddFactory(200)
    } -->
    <div class="mailings__details-modal-dialog-filter ml-4 ">

      <div class="form-group dense-form-group">
        <label class="form-label">День</label>
        <input class="form-control" placeholder="00" data-bind="textInput: model.day, mask, maskPattern: '00', numericIntervalField: { min: 1, max: 31 }" style="width: 30px;">
      </div>
    </div>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(['4', '5'].includes(model.type()), $data),
      afterAdd: fadeAfterAddFactory(200)
    } -->
    <div class="mailings__details-modal-dialog-filter ml-4 ">

      <div class="form-group dense-form-group">
        <label class="form-label">Месяц</label>
        <select data-bind="
              value: model.month,
              select2: {
                  wrapperCssClass: 'select2-container--form-control',
                  minimumResultsForSearch: 0,
                  dropdownCssClass: 'dense-form-group__dropdown'
              }
          ">
          <option value="1">Январь</option>
          <option value="2">Февраль</option>
          <option value="3">Март</option>
          <option value="4">Апрель</option>
          <option value="5">Май</option>
          <option value="6">Июнь</option>
          <option value="7">Июль</option>
          <option value="8">Август</option>
          <option value="9">Сентябрь</option>
          <option value="10">Октябрь</option>
          <option value="11">Ноябрь</option>
          <option value="12">Декабрь</option>
        </select>
      </div>
    </div>
    <!-- /ko -->
  </div>

</template>
