ko.components.register('sendings-history', {
  viewModel: function(params) {
    this.history = params.history;
  },
  template: `

  <div class="dropdown" data-bind="popper">
    <div class="" data-popper-toggler data-bind="template: {
      nodes: $componentTemplateNodes
    }">

    </div>

    <div class="dropdown-menu" data-popper-dropdown>
      <div class="mailings__details-modal-dialog-table-sending-list-table-wrapper">
        <table class="table foq-table mailings__details-modal-dialog-table-sending-list-table">
          <thead>
            <tr>
              <th>Отправлена</th>
              <th>Каналы</th>
              <th class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-repeats-head-cell">
                Повторы
              </th>
              <th class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-response-head-cell">
                Ответ
              </th>
            </tr>
          </thead>

          <tbody>
            <!-- ko foreach: history -->
            <tr class="font-weight-normal">
              <td data-bind="text: sended"></td>
              <td>
                <div class="d-flex align-items-center">
                  <span class="f-icon f-icon-channel mr-2" data-bind="css: 'f-icon-channel--' + type">
                    <svg>
                      <use data-bind="attr: {
                        href: '#channel-icon-' + type
                      }"></use>
                    </svg>
                  </span>
                  <!-- ko text: name -->
                  <!-- /ko -->
                </div>
              </td>
              <td class="">
                <div class="d-flex align-items-center justify-content-center">
                  <span class="f-icon f-icon-channel f-icon-channel--repeats" style="margin-right: 20px;">
                    <svg>
                      <use href="#channel-icon-repeats"></use>
                    </svg>
                  </span>
                  <!-- ko text: repeats -->
                  <!-- /ko -->
                </div>

              </td>
              <td class="mailings__details-modal-dialog-table-sending-list-dropdown-menu-table-response-cell" data-bind="text: hasResponse ? 'есть' : '—'">
              </td>
            </tr>
            <!-- /ko -->
          </tbody>
        </table>
      </div>
    </div>
  </div>


  `

})
