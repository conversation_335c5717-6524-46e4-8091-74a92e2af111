import './style.less';

class ViewModel {
  constructor(params) {

    this.id = params.id;
    this.className = [params.className, 'img-rating--' + this.id].filter(Boolean).join(' ');
    this.icons = params.icons;
    this.value = ko.isObservable(params.value) ? params.value : ko.observable(params.value);
    this.label = ko.pureComputed(() => {
      let activeIconId = this.value();
      if (!activeIconId) return '';

      let icon = this.icons.find(i => i.id == activeIconId);
      return icon ? icon.label : '';
    });
    this.showLabels = params.showLabels;
  }
}

ko.components.register('img-rating', {
  viewModel: ViewModel,
  template: `<div class="img-rating" data-bind="css: {
    'img-rating--inited': !!value(),
  }, class: className,
  attr: { 'data-icons': icons.length }">
    <div class="d-flex align-items-center justify-content-center img-rating__icons">
      <!-- ko foreach: icons -->
        
          <div class="img-rating__icon smile" data-bind="
            click: function() {
              $component.value(id);
            },
            css: {
              'active': $component.value() == id
            },
            attr: {
              'data-id': id
            }
          ">
            <img data-bind="attr: { src: url, alt: label || '' }">
            <!-- ko if: $component.value() == id -->
            <span class="active"></span>
            <!-- /ko -->

           
          </div>
         
        
      <!-- /ko -->
    </div>
    <!-- ko if: label() -->
    <div class="img-rating__label" data-bind="text: label">
    </div>
    <!-- /ko -->
    
  </div>`
})
