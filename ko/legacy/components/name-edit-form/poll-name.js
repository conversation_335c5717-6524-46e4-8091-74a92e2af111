import { ViewModel, template } from './model';

/** Редактирование названия опроса */
ko.components.register('poll-name', {
  viewModel: {
    createViewModel: function (params) {
      return new ViewModel(params.name, params.folders, (newValue) => {
        $.post('/foquz/foquz-poll/update?id=' + params.id, {
          FoquzPoll: {
            name: newValue,
          },
        });
        if (typeof params.onEdit === 'function') params.onEdit(newValue);
      });
    },
  },
  template: `<div class="page-header mb-4">${template}</div>`,
});
