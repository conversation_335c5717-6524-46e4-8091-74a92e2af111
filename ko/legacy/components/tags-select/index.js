class ViewModel {
  constructor(params) {
    this.selectedOptions = params.selectedOptions;
    this.element = params.element;

    this.directory = params.directory;

    this.directory.loading.subscribe((v) => {
      if (!v) {
        this.directory.loaded(false);
        this.directory.loaded(true);
      }
    });

    this.select2Options = {
      ...this.defaultOptions,
      ...(params.select2Options || {})
    };

    this.tagsWithConditions = this.getTagsWithConditions();
    this.tagsWithoutConditions = this.getTagsWithoutConditions();
  }

  get defaultOptions() {
    return {
      templateResult: this.templateResult,
      minimumResultsForSearch: 0
    };
  }

  templateResult(state) {
    let span = $('<span>').text(state.text);

    if (state.element && state.element.hasAttribute('data-auto'))
      span.addClass('color-success');
    else if (state.children) span.addClass('color-service');
    else if (state.id) span.addClass('color-text');

    return span;
  }

  getTagsWithConditions() {
    return ko.pureComputed(() => {
      let tags = this.directory.data().filter((t) => t.isAuto);

      tags.sort((a, b) => (a.name < b.name ? -1 : 1));

      return tags;
    });
  }

  getTagsWithoutConditions() {
    return ko.pureComputed(() => {
      let tags = this.directory.data().filter((t) => !t.isAuto);

      tags.sort((a, b) => {
        return a.name < b.name ? -1 : 1;
      });
      return tags;
    });
  }
}

ko.components.register('tags-select', {
  viewModel: ViewModel,
  template: `

    <!-- ko if: directory.loaded -->
      <select multiple data-bind="
        element: element,
        selectedOptions: selectedOptions,
        lazySelect2: select2Options,
      " data-placeholder="Все">
        <!-- ko if: tagsWithConditions().length > 0 -->
        <optgroup data-auto label="С условием">
            <!-- ko foreach: {
              data: tagsWithConditions
             } -->
            <option data-bind="text: name, value: id"></option>
            <!-- /ko -->
        </optgroup>
        <!-- /ko -->
        <!-- ko if: {
          data: tagsWithoutConditions().length > 0
         } -->
        <optgroup label="Без условия">
            <!-- ko foreach: tagsWithoutConditions -->
            <option data-bind="text: name, value: id"></option>
            <!-- /ko -->
        </optgroup>
        <!-- /ko -->
      </select>
    <!-- /ko -->
  `
});

class ViewModelSingle extends ViewModel {
  constructor(params) {
    super(params);

    this.hiddenOptions = params.hiddenOptions || ko.observableArray([]);
  }

  templateResult(state) {
    if (state.element && state.element.disabled) return null;

    let span = $('<span>').text(state.text);

    if (state.element && state.element.hasAttribute('data-auto'))
      span.addClass('color-success');
    else if (state.children) span.addClass('color-service');
    else if (state.id) span.addClass('color-text');

    return span;
  }

  matcher(params, data) {
    if ($.trim(params.term) === '') {
      return data;
    }

    if (typeof data.children === 'undefined') {
      return null;
    }

    var filteredChildren = [];
    $.each(data.children, function (idx, child) {
      if (child.element.disabled) return;
      if (child.text.toUpperCase().indexOf(params.term.toUpperCase()) == 0) {
        filteredChildren.push(child);
      }
    });

    if (filteredChildren.length) {
      var modifiedData = $.extend({}, data, true);
      modifiedData.children = filteredChildren;

      return modifiedData;
    }

    return null;
  }

  get defaultOptions() {
    return {
      templateResult: this.templateResult,
      matcher: this.matcher,
      minimumResultsForSearch: 0
    };
  }
}

ko.components.register('tags-select-single', {
  viewModel: ViewModelSingle,
  template: `
  <div data-bind="text: selectedOptions"></div>
  <!-- ko if: directory.loaded -->
  <select data-bind="
    element: element,
    lazySelect2: select2Options,
  " data-placeholder="Все">

    <option></option>
    <!-- ko if: tagsWithConditions().length > 0 -->
        <optgroup data-auto label="С условием">
            <!-- ko foreach: {
              data: tagsWithConditions
             } -->
            <option data-with-condition data-bind="text: name, value: id,
            disable: $parent.hiddenOptions().includes(id)"></option>
            <!-- /ko -->
        </optgroup>
        <!-- /ko -->
        <!-- ko if: {
          data: tagsWithoutConditions().length > 0
         } -->
        <optgroup label="Без условия">
            <!-- ko foreach: tagsWithoutConditions -->
            <option data-without-condition data-bind="text: name, value: id, disable: $parent.hiddenOptions().includes(id)"></option>
            <!-- /ko -->
        </optgroup>
    <!-- /ko -->
  </select>
<!-- /ko -->
  `
});
