class CardsGroup {
  constructor(data) {
    this.element = ko.observable(null).extend({ notify: 'always' });
    this.element.subscribe(v => {
      if (v) this.update();
    })

    this.count = data.count;
    this.name = data.name;

    this.list = data.list;
    this.cardComponentName = data.cardComponentName;

    this.mode = data.mode || 'cards';

    this.opened = ko.observable(true);
    this.moreButtonVisible = ko.observable(true);

    this.hasHiddenItems = ko.observable(false);

    this.opened.subscribe((v) => {
      if (v) this.update();
    });

    this.list.subscribe(_ => this.update());

    this.onExpand = data.onExpand;

    $(window).on('resize', () => this.update());
  }

  update() {
    if (!this.count()) return;
    if (!this.element()) return;

    let cardsList = this.element().querySelector('.cards-group__list');
    if (!cardsList) return;
    let items = [...cardsList.querySelectorAll('.cards-group__item')];

    if (!items.length) return;

    let visible = 1;


    let top = items[0].offsetTop;
    for (let i = 1; i < items.length; i++) {
      let diff = items[i].offsetTop - top;
      if (diff > 10) {
        items[i].classList.add('hidden');
      } else {
        items[i].classList.remove('hidden');
        visible++;
      }
    }


    this.hasHiddenItems(visible < this.count());
  }
}

ko.components.register('cards-group', {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      let model = new CardsGroup(params);
      model.onInit = () => {
        model.update();
      };
      return model;
    },
  },
  template: `
    <!-- ko if: count -->
    <section class="cards-group"
    data-bind="
      descendantsComplete: onInit,
      element: element,
    ">
      <header class="cards-group__header"
      data-bind="
        click: function() { opened(!opened()); }
      ">
        <h2 class="f-h2 cards-group__title">
          <span class="font-weight-normal mr-2" data-bind="text: count"></span>
          <span data-bind="text: name"></span>
          <span class="f-icon f-icon-arrow f-icon-arrow--top f-transform f-transform--transition ml-2"
            data-bind="
              css: {
                'f-transform-rotate-180': !opened()
              }
            ">
            <svg>
              <use href="#arrow-top-icon"></use>
            </svg>
          </span>
        </h2>
      </header>

      <!-- ko template: {
        foreach: templateIf(opened(), $data),
        afterAdd: slideAfterAddFactory(200),
        beforeRemove: slideBeforeRemoveFactory(200),
        afterRender: $component.update.bind($component),
      } -->

        <div class="cards-group__list">
          <!-- ko foreach: {
            data: list.slice(0, 5),
            afterAdd: $component.update.bind($component)
           } -->
            <div class="cards-group__item" >

              <div data-bind="component: {
                name: $component.cardComponentName($data),
                params: $data,
              }"></div>

            </div>
          <!-- /ko -->
        </div>

        <!-- ko if: hasHiddenItems -->
        <footer class="cards-group__footer">
          <button class="f-btn f-btn--block" data-bind="click: onExpand,text: _t('Показать все')"></button>
        </footer>
        <!-- /ko -->

      <!-- /ko -->
    </section>
    <!-- /ko -->
  `,
});
