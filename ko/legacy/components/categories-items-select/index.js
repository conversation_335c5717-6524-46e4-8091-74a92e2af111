class ViewModel {
  constructor(params) {
    this.loading = ko.observable(false);
    this.list = params.list;
    this.selectedOptions = params.selectedOptions;

    this.disable = params.disable;

    this.itemsProperty = params.itemsProperty || 'items';
    this.categoryValueProperty = params.categoryValueProperty || 'id';
    this.categoryNameProperty = params.categoryNameProperty || 'name';
    this.itemValueProperty = params.itemValueProperty || 'id';
    this.itemNameProperty = params.itemNameProperty || 'name';

    this.categoryTmp = (categoryData) => {
      let nodes = [];

      let categoryOption = $('<option>')
        .attr('data-category', categoryData[this.categoryValueProperty])
        .val('c' + categoryData[this.categoryValueProperty])
        .text(categoryData[this.categoryNameProperty] || 'Без категории');

      nodes.push(categoryOption.get(0));

      let items = categoryData[this.itemsProperty] || [];
      items.forEach((itemData) => {
        let option = $('<option>')
          .attr('data-category', categoryData[this.categoryValueProperty])
          .val('i' + itemData[this.itemValueProperty])
          .text(itemData[this.itemNameProperty]);

        nodes.push(option.get(0));
      });

      return nodes;
    };

    this.options = ko.pureComputed(() => {
      this.loading(true);
      let nodes = [];
      this.list().forEach((category) => {
        let categoriesNodes = this.categoryTmp(category);
        nodes = [...nodes, ...categoriesNodes];
      });
      this.loading(false);
      return nodes;
    });
  }

  templateSelection(state) {
    if (!state.id) {
      return state.text;
    }
    var $result = $('<span>').text(state.text);

    if (state.id[0] == 'c') {
      $result.addClass('category-value');
    }
    return $result;
  }

  templateResult(state) {
    if (!state.id) {
      return state.text;
    }

    var $result = $('<span>').text(state.text);

    if (state.id[0] == 'c') {
      $result.addClass('category-option');
    } else {
      $result.addClass('item-option');
    }

    return $result;
  }

  onChange(event) {
    var options = $(event.target).find('option').get();
    var value = $(event.target).val();

    if (value) {
      var selectedCategories = value.filter(function (v) {
        return v[0] == 'c';
      });
      var selectedOptions = value.filter(function (v) {
        return v[0] != 'c';
      });

      options.forEach(function (option) {
        var optionValue = $(option).attr('value');
        if (!optionValue) return;

        var isCategory = optionValue[0] == 'c';

        if (!isCategory) {
          var categoryId = $(option).data('category');
          if (selectedCategories.includes('c' + categoryId)) {
            if (selectedOptions.includes(optionValue)) {
              var index = selectedOptions.indexOf(optionValue);
              selectedOptions.splice(index, 1);
            }
            $(option).attr('disabled', true);
          } else {
            $(option).attr('disabled', false);
          }
        }
      });

      var newValue = [].concat(
        _toConsumableArray(selectedCategories),
        _toConsumableArray(selectedOptions),
      );

      $(event.target).val(newValue).trigger('change.select2');
    }
  }

  matcher({ term }, data) {
    if (!term) {
      return data;
    }

    term = term.toLowerCase();

    if (!data.id) {
      return null;
    }

    if (data.id[0] != 'c') {
      const categoryId = data.element.dataset.category;
      const category = this.list().find((c) => c[this.categoryValueProperty] === categoryId);
      if (!category) return null;

      const categoryName = category[this.categoryNameProperty].toLowerCase();
      const itemText = data.text.toLowerCase();

      const match = categoryName.includes(term) || itemText.includes(term);
      return match ? data : null;
    } else {
      const category = this.list().find((c) => c[this.categoryValueProperty] === data.id.slice(1));
      if (!category) return null;

      const categoryName = category[this.categoryNameProperty].toLowerCase();

      const match =
        categoryName.includes(term) ||
        category[this.itemsProperty].some((d) => {
          return d[this.itemNameProperty].toLowerCase().includes(term);
        });
      return match ? data : null;
    }
  }
}

ko.components.register('categories-items-select', {
  viewModel: ViewModel,
  template: `
  <select multiple
    data-bind="selectedOptions: selectedOptions,
    valueAllowUnset: true,
    disable: disable,
    lazySelect2: {
      wrapperCssClass: 'select2-container--form-control',
      containerCssClass: 'form-control',
      minimumResultsForSearch: 0,

      templateSelection: templateSelection,
      templateResult: templateResult,
      matcher: matcher,
      placeholder: 'Не выбрано'
    },
    event: {
      change: function(_, event) {
        onChange(event);
      }
    }">

    <!-- ko template: {
      nodes: options()
    } -->
    <!-- /ko -->

  </select>

  `,
});
