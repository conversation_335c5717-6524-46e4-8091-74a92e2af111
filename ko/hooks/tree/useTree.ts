import { CheckboxStates } from "@/constants/checkbox";
import { getTreeNodeChildren } from "@/utils/tree/get-children";
import { getTreeNodeParents } from "@/utils/tree/get-parents";
import { getTreeStructure } from "@/utils/tree/get-structure";
import { Tree, TreeStructure } from "@/utils/tree/types";

const { observableArray, observable } = ko;

export class CheckedTree {
  _value: KnockoutObservableArray<string>;
  structure: TreeStructure;
  state: {
    [key: string]: KnockoutObservable<CheckboxStates>;
  };
  viewState: {
    [key: string]: KnockoutObservable<boolean>;
  };

  constructor(tree: Tree) {
    this._value = observableArray([]);
    this.structure = getTreeStructure(tree);
    this.state = {};
    this.viewState = {};

    Object.keys(this.structure).forEach((id) => {
      this.state[id] = observable(CheckboxStates.Unchecked);
      this.viewState[id] = observable(false);
    });
  }

  setValue(value: string[]) {
    Object.entries(this.state).forEach(([id, checked]) => {
      if (value.includes(id)) checked(CheckboxStates.Checked);
      else checked(CheckboxStates.Unchecked);
    });
  }

  toggleElementState(elementId: string, checked: boolean) {
    if (!this.state[elementId]) return;

    const state = checked ? CheckboxStates.Checked : CheckboxStates.Unchecked;

    this.state[elementId](state);
    const elementChildren = getTreeNodeChildren(elementId, this.structure);
    elementChildren.forEach((id) => {
      this.state[id](state);
    });

    const parents = getTreeNodeParents(elementId, this.structure);
    parents.forEach((id) => this.updateElementState(id));
  }

  updateElementState(elementId: string) {
    if (!this.state[elementId]) return;

    const children = getTreeNodeChildren(elementId, this.structure);
    let allChecked = children.every((id) => this.state[id]());
    let allUnchecked = children.every((id) => !this.state[id]());

    if (allChecked) this.state[elementId](CheckboxStates.Checked);
    else if (allUnchecked) this.state[elementId](CheckboxStates.Unchecked);
    else this.state[elementId](CheckboxStates.PartialChecked);
  }

  toggleElementView(viewState: boolean);
  toggleElementView(elementId: string, viewState: boolean);
  toggleElementView(
    elementIdOrViewState: boolean | string,
    viewState?: boolean
  ) {
    if (viewState !== undefined) {
      const elementId = elementIdOrViewState as string;
      if (!this.viewState[elementId]) return;
      this.viewState[elementId](viewState);

      const children = getTreeNodeChildren(elementId, this.structure);
      children.forEach((id) => {
        this.viewState[id](viewState);
      });
    } else {
      const _viewState = elementIdOrViewState as boolean;
      Object.values(this.viewState).forEach((state) => state(_viewState));
    }
  }
}

