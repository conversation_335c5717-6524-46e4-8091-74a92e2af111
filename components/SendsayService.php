<?php

namespace app\components;

class SendsayService
{
    private $login;
    private $password;
    private $session = null;
    const URL = "https://api.sendsay.ru/general/api/v100/json/";

    public function __construct($login, $password, $session=null)
    {
        if (empty($session)) {
            $this->login = $login;
            $this->password = $password;
            $this->login();
        } else {
            $this->login = $login;
            $this->password = $password;
            $this->session = $session;
        }
    }

    private function _request($data)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL            => self::URL . $this->login,
            CURLOPT_POST           => true,
            CURLOPT_HTTPHEADER     => ["Content-Type: application/json"],
            CURLOPT_POSTFIELDS     => json_encode($data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 150,
            CURLOPT_CONNECTTIMEOUT => 150,
        ]);
        //print_r(curl_getinfo($ch));
       $res = curl_exec($ch);
       return $res;
    }

    public function getSession()
    {
        return $this->session;
    }

    public function login()
    {
        if ($this->login && $this->password) {
            $data = [
                "action"   => "login",
                "login"    => $this->login,
                "sublogin" => $this->login,
                "passwd"   => $this->password,
            ];

            $response = @json_decode($this->_request($data), true);
            if (is_array($response) && isset($response['session'])) {
                $this->session = $response["session"];
                return true;
            }
        }
        return false;
    }

    public function sendEmail($from, $fromName, $to, $subject, $html, $replyEmail=null, $replyName=null)
    {
        if ($this->session) {
            $data = [
                "action"   => "issue.send",
                "group"    => "personal",
                "sendwhen" => "now",
                "email"    => $to,
                "session"  => $this->session,
                "letter"   => [
                    "from.email" => $from,
                    "subject"    => $subject,
                    "from.name"  => $fromName,
                    "message"    => [
                        "html" => $html,
                    ],
                ],
            ];
            if ($replyEmail) $data["letter"]["reply.email"] = $replyEmail;
            if ($replyName) $data["letter"]["reply.name"] = $replyName;

            $response = @json_decode($this->_request($data), true);
            return $response;
        }

        return false;
    }

    public function trackStatus($id)
    {
        if ($this->session) {
            $data = [
                "action"   => "track.get",
                "id"    => $id,
                "session"  => $this->session,
            ];
            $response = @json_decode($this->_request($data), true);
            return $response;
        }

        return false;

    }

}
