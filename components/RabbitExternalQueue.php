<?php

namespace app\components;

class RabbitExternalQueue extends \yii\queue\amqp_interop\Queue
{
    public function unserializeMessage($serialized): array
    {
        $job = $this->serializer->unserialize($serialized);
        if (is_array($job) && !empty($job['job'])) {
            $classname = 'app\\modules\\foquz\\queue\\' . $job['job'];
            if (class_exists($classname)) {
                $jobObjet = new $classname();
                $fields = array_keys(get_class_vars(get_class($jobObjet)));
                foreach ($job as $key => $value) {
                    if (in_array($key, $fields)) {
                        $jobObjet->$key = $value;
                    }
                }
                $serialized = $this->serializer->serialize($jobObjet);
            }
        }
        return parent::unserializeMessage($serialized);
    }
}