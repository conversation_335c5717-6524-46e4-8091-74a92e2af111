<?php

namespace app\components;
use Yii;

class UtmService
{
    private static $utm_tags = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        '_ym_uid',
        '_ym_counter',
        'roistat',
        'fbclid',
        'yclid',
        'gclid',
        'openstat_source',
        'openstat_ad',
        'openstat_campaign',
        'openstat_service',
    ];

    /**
     * Запись меток в сессию.
     */
    public function setUtm(): void
    {
        foreach (self::$utm_tags as $utm_tag) {
            if ($value = Yii::$app->request->get($utm_tag)) {
                Yii::$app->session->set($utm_tag, $value);
            }
        }
    }

    /**
     * Проверяем есть ли хотя бы одна метка в сессии.
     * @return bool
     */
    public function checkUtmTags(): bool
    {
        foreach (self::$utm_tags as $utm_tag) {
            if (isset(Yii::$app->session[$utm_tag])) {
                return true;
            }
        }

        return false;
    }


    /**
     * Проверяем есть ли хотя бы одна метка в сессии.
     * @return array
     */
    public static function getUtmTags()
    {
        $result = [];
        foreach (self::$utm_tags as $utm_tag) {
            if (isset(Yii::$app->session[$utm_tag])) {
                $result[$utm_tag]=Yii::$app->session[$utm_tag];
            }
        }

        return $result;
    }    
}