<?php

namespace app\components\cache;

use yii\caching\Cache;

/**
 * Класс для работы с in-memory-bd Redis.
 * Ключи для хранения данных указывать в виде констант. Звёздочку (*) в имени константы не указываем.
 */
class Redis extends Cache
{
    public const WORKER_AMOUNT = 'worker_amount';

    /** @var  \Redis */
    public $redis;
    /** @var string */
    public $hostname = 'localhost';
    /** @var integer */
    public $port = 6379;
    /** @var boolean */
    public $persist  = false;
    /** @var float */
    public $timeout  = 0.0;
    /** @var integer */
    public $database = 0;
    /** @var string */
    public $password;

    /**
     * @throws \RedisException
     */
    public function init(): void
    {
        parent::init();
        $this->redis = new \Redis();
        if ($this->persist) {
            $hash = $this->hostname . $this->port . $this->timeout . $this->password . $this->database;
            $this->redis->pconnect($this->hostname, $this->port, $this->timeout, md5($hash));
        } else {
            $this->redis->connect($this->hostname, $this->port, $this->timeout);
        }

        if ($this->password) {
            $this->redis->auth($this->password);
        }
        $this->redis->select($this->database);
    }

    /**
     * @throws \RedisException
     */
    protected function getValue($key)
    {
        return $this->redis->get($key);
    }

    /**
     * @throws \RedisException
     */
    protected function setValue($key, $value, $duration)
    {
        if ($duration === 0) {
            return $this->redis->set($key, $value);
        }
        return $this->redis->set($key, $value, $duration);
    }

    /**
     * @throws \RedisException
     */
    protected function addValue($key, $value, $duration)
    {
        if ($duration === 0) {
            return $this->redis->setnx($key, $value);
        }
        if ($this->exists($key)) {
            return false;
        }
        return $this->redis->setex($key, $duration, $value);
    }

    /**
     * @throws \RedisException
     */
    protected function deleteValue($key)
    {
        return $this->redis->del($key);
    }

    /**
     * @throws \RedisException
     */
    protected function flushValues()
    {
        return $this->redis->flushDB();
    }
}
