<?php


namespace app\components;


use AmoCRM\OAuth2\Client\Provider\AmoCRM;
use GuzzleHttp\Exception\GuzzleException;
use League\OAuth2\Client\Grant\RefreshToken;
use League\OAuth2\Client\Token\AccessToken;

class AmoCrmComponent
{
    private $provider;
    CONST responsible_user_id=8026408;

    public function __construct()
    {
        $this->provider = new AmoCRM([
            'clientId' => \Yii::$app->params['amo_client_id'],
            'clientSecret' => \Yii::$app->params['amo_client_secret'],
            'redirectUri' => 'https://foquz.ru',
        ]);
        $this->provider->setBaseDomain(\Yii::$app->params['amo_domain']);
    }

    public function createCompany($name, $domain, $console=false)
    {
        $accessToken = $this->getToken();
        if (!$accessToken) {
            return null;
        }

        if ($accessToken->hasExpired()) {
            $accessToken = $this->refreshToken($accessToken);
        }
    

        try {
            $data = $this->provider->getHttpClient()
                ->request('POST', $this->provider->urlAccount() . 'api/v4/companies', [
                    'headers' => $this->provider->getHeaders($accessToken),
                    'form_params' => [
                        [
                            'name' => $name,
                            'custom_fields_values' => [
                                [
                                    'field_code' => 'WEB',
                                    'values' => [
                                        [
                                            'value' => $domain
                                        ]
                                    ]
                                ]
                            ]
                        ],
                    ]
                ]);

            $parsedBody = json_decode($data->getBody()->getContents(), true);

            return $parsedBody['_embedded']['companies'][0]['id'];
       
        } catch (GuzzleException $e) {
            if ($console) print_r($e->getResponse()->getBody()->getContents());
            \Yii::error($e->getResponse()->getBody()->getContents(), __METHOD__);
            return null;
        }
    }




    public function getUsers()
    {
        $accessToken = $this->getToken();

        try {
            $data = $this->provider->getHttpClient()
                ->request('GET', $this->provider->urlAccount() . '/api/v4/users', [
                    'headers' => $this->provider->getHeaders($accessToken),
                ]);

            //return json_decode($data->getBody()->getContents(), true);

            $parsedBody = json_decode($data->getBody()->getContents(), true);

            return $parsedBody['_embedded']['users'];

        } catch (GuzzleException $e) {
            \Yii::error($e->getResponse()->getBody()->getContents(), __METHOD__);
            return null;
        }
    }


    public function getLeadFields()
    {
        $accessToken = $this->getToken();

        try {
            $data = $this->provider->getHttpClient()
                ->request('GET', $this->provider->urlAccount() . 'api/v4/leads/custom_fields', [
                    'headers' => $this->provider->getHeaders($accessToken),
                ]);

            //return json_decode($data->getBody()->getContents(), true);

            $parsedBody = json_decode($data->getBody()->getContents(), true);

            return $parsedBody['_embedded']['custom_fields'];

        } catch (GuzzleException $e) {
            \Yii::error($e->getResponse()->getBody()->getContents(), __METHOD__);
            return null;
        }
    }

    public function getCompanyFields()
    {
        $accessToken = $this->getToken();

        if($accessToken->hasExpired()) {
            $accessToken = $this->refreshToken($accessToken);
        }
    

        try {
            $data = $this->provider->getHttpClient()
                ->request('GET', $this->provider->urlAccount() . 'api/v4/companies/custom_fields', [
                    'headers' => $this->provider->getHeaders($accessToken),
                ]);

            return json_decode($data->getBody()->getContents(), true);
        } catch (GuzzleException $e) {
            \Yii::error($e->getResponse()->getBody()->getContents(), __METHOD__);
            return null;
        }
    }


    public function getContactFields()
    {
        $accessToken = $this->getToken();


        if($accessToken->hasExpired()) {
            $accessToken = $this->refreshToken($accessToken);
        }
    
        //try {
            $data = $this->provider->getHttpClient()
                ->request('GET', $this->provider->urlAccount() . 'api/v4/contacts/custom_fields', [
                    'headers' => $this->provider->getHeaders($accessToken),
                ]);


            print_r($data);
            return json_decode($data->getBody()->getContents(), true);
        //} catch (GuzzleException $e) {

          //  \Yii::error($e->getResponse()->getBody()->getContents(), __METHOD__);
           // return null;
        //}
    }

    public function createContact($name, $email, $phone, $companyId=null, $console=false)
    {

        $accessToken = $this->getToken();
        //if($accessToken->hasExpired()) {
          //  $accessToken = $this->refreshToken($accessToken);
        //}

        try {
            $customFieldsValues = [];
            if($email != '') {
                $customFieldsValues[] = [
                    'field_code' => 'EMAIL',
                    'values' => [
                        [
                            'value' => $email
                        ]
                    ]
                ];
            }
            if($phone != '') {
                $customFieldsValues[] = [
                    'field_code' => 'PHONE',
                    'values' => [
                        [
                            'enum_code' => 'WORK',
                            'value' => $phone
                        ]
                    ]
                ];
            }

            if ($console) print_r($customFieldsValues);

            $data = $this->provider->getHttpClient()
                ->request('POST', $this->provider->urlAccount() . 'api/v4/contacts', [
                    'headers' => $this->provider->getHeaders($accessToken),
                    'form_params' => [
                        [
                            'name' => $name,
                            'custom_fields_values' => $customFieldsValues
                        ],
                    ]
                ]);

            $parsedBody = json_decode($data->getBody()->getContents(), true);

            $contactId = $parsedBody['_embedded']['contacts'][0]['id'];

            if ($companyId) {
                $headers = [
                    'Authorization: Bearer ' . $accessToken->getToken(),
                    'Content-Type:application/json'
                ];

                $data = [
                    [
                        'to_entity_id' => $companyId,
                        'to_entity_type' => 'companies'
                    ],
                ];

                $curl = curl_init();
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl,CURLOPT_USERAGENT,'amoCRM-oAuth-client/1.0');
                curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
                curl_setopt($curl, CURLOPT_URL, $this->provider->urlAccount() . 'api/v4/contacts/'.$contactId.'/link');
                curl_setopt($curl,CURLOPT_HTTPHEADER, $headers);
                curl_setopt($curl, CURLOPT_HEADER, false);
                curl_setopt($curl,CURLOPT_CUSTOMREQUEST, 'POST');
                curl_setopt($curl,CURLOPT_POSTFIELDS, json_encode($data));
                curl_setopt($curl,CURLOPT_SSL_VERIFYPEER, 1);
                curl_setopt($curl,CURLOPT_SSL_VERIFYHOST, 2);
                $out = curl_exec($curl);
                curl_close($curl);
            }

            return $contactId;
        } catch (GuzzleException $e) {
            if ($console) print_r($e->getResponse()->getBody()->getContents());

            \Yii::error($e->getResponse()->getBody()->getContents(), __METHOD__);
            return null;
        }
    }

    public function updateContact($contactId, $email, $phone)
    {
        $accessToken = $this->getToken();
        if($accessToken->hasExpired()) {
            $accessToken = $this->refreshToken($accessToken);
        }

        try {
            $customFieldsValues = [];
            if($email != '') {
                $customFieldsValues[] = [
                    'field_code' => 'EMAIL',
                    'values' => [
                        [
                            'value' => $email
                        ]
                    ]
                ];
            }
            if($phone != '') {
                $customFieldsValues[] = [
                    'field_code' => 'PHONE',
                    'values' => [
                        [
                            'enum_code' => 'WORK',
                            'value' => $phone
                        ]
                    ]
                ];
            }
            $data = $this->provider->getHttpClient()
                ->request('PATCH', $this->provider->urlAccount() . 'api/v4/contacts/'.$contactId, [
                    'headers' => $this->provider->getHeaders($accessToken),
                    'form_params' => [
                        [
                            'custom_fields_values' => $customFieldsValues
                        ],
                    ]
                ]);

            $parsedBody = json_decode($data->getBody()->getContents(), true);
        } catch (GuzzleException $e) {
            \Yii::error($e->getResponse()->getBody()->getContents(), __METHOD__);
            return null;
        }
    }

    public function findContact($query)
    {
        $accessToken = $this->getToken();
        if($accessToken->hasExpired()) {
            $accessToken = $this->refreshToken($accessToken);
        }

        try {
            $data = $this->provider->getHttpClient()
                ->request('GET', $this->provider->urlAccount() . 'api/v4/contacts?query='.$query, [
                    'headers' => $this->provider->getHeaders($accessToken),
                ]);
            $parsedBody = json_decode($data->getBody()->getContents(), true);

            return $parsedBody ? $parsedBody['_embedded']['contacts'][0] : false;
        } catch (GuzzleException $e) {
            \Yii::error($e->getMessage(), __METHOD__);
            return null;
        }
    }


    public function createLeadNew($name, $sum, $companyId=null, $contactId=null, $params=[], $typeContact='')
    {
        $accessToken = $this->getToken();

        if (!$accessToken) {
            return false;
        }
        $headers = [
            'Authorization: Bearer ' . $accessToken->getToken(),
            'Content-Type:application/json'
        ];

        $data = [
            [
                'name' => $name,
                'price' => $sum ?? 0,
                'responsible_user_id' => self::responsible_user_id
            ]
        ];

        $customFieldsValues = [];        
        $fields = $this->getLeadFields();
        if (is_array($fields)) {
            foreach ($params as $key => $value) {
                foreach ($fields as $field) {
                    if ($field['name']==$key && $value) {
                        $customFieldsValues[] = [
                            //'field_code' => $field['code'],
                            'field_id' => intval($field['id']),
                            'values' => [['value' => $value]]
                        ];
                    }
                }
            }
            if ($typeContact) {
                foreach ($fields as $field) {
                    if ($field['name']=="Тип контакта") {
                        foreach ($field['enums'] as $enum) {
                            if ($enum["value"]==$typeContact) {
                                $customFieldsValues[] = [
                                    //'field_code' => $field['code'],
                                    'field_id' => intval($field['id']),
                                    'values' => [['enum_id' => $enum["id"]]]
                                ];                                
                                break;
                            }
                            
                        }
                        break;
                    }
                }
            }
        }
        
        if (count($customFieldsValues)>0) {
            $data[0]['custom_fields_values'] = $customFieldsValues;
        }


        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl,CURLOPT_USERAGENT,'amoCRM-oAuth-client/1.0');
        curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
        curl_setopt($curl, CURLOPT_URL, $this->provider->urlAccount() . 'api/v4/leads');
        curl_setopt($curl,CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl,CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl,CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($curl,CURLOPT_SSL_VERIFYPEER, 1);
        curl_setopt($curl,CURLOPT_SSL_VERIFYHOST, 2);
        $out = json_decode(curl_exec($curl), true);
        curl_close($curl);

        $leadId = @$out['_embedded']['leads'][0]['id'];


        if ($leadId && $companyId) {
            $data = [
                [
                    'to_entity_id' => $companyId,
                    'to_entity_type' => 'companies'
                ],
            ];

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl,CURLOPT_USERAGENT,'amoCRM-oAuth-client/1.0');
            curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
            curl_setopt($curl, CURLOPT_URL, $this->provider->urlAccount() . 'api/v4/leads/'.$leadId.'/link');
            curl_setopt($curl,CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl, CURLOPT_HEADER, false);
            curl_setopt($curl,CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl,CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($curl,CURLOPT_SSL_VERIFYPEER, 1);
            curl_setopt($curl,CURLOPT_SSL_VERIFYHOST, 2);
            $out = curl_exec($curl);
            curl_close($curl);
        }

        if ($leadId && $contactId) {
            $data = [
                [
                    'to_entity_id' => $contactId,
                    'to_entity_type' => 'contacts'
                ],
            ];

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl,CURLOPT_USERAGENT,'amoCRM-oAuth-client/1.0');
            curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
            curl_setopt($curl, CURLOPT_URL, $this->provider->urlAccount() . 'api/v4/leads/'.$leadId.'/link');
            curl_setopt($curl,CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl, CURLOPT_HEADER, false);
            curl_setopt($curl,CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl,CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($curl,CURLOPT_SSL_VERIFYPEER, 1);
            curl_setopt($curl,CURLOPT_SSL_VERIFYHOST, 2);
            $out = curl_exec($curl);
            curl_close($curl);
        }



        return $leadId;
    }
    public function createLead($name, $sum, $companyId=null, $contactId=null, $params=[])
    {
        $accessToken = $this->getToken();
        if($accessToken->hasExpired()) {
            $accessToken = $this->refreshToken($accessToken);
        }

        if (!$accessToken) {
            return false;
        }
        $headers = [
            'Authorization: Bearer ' . $accessToken->getToken(),
            'Content-Type:application/json'
        ];

        $data = [
            [
                'name' => $name,
                'price' => $sum ?? 0,
            ]
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl,CURLOPT_USERAGENT,'amoCRM-oAuth-client/1.0');
        curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
        curl_setopt($curl, CURLOPT_URL, $this->provider->urlAccount() . 'api/v4/leads');
        curl_setopt($curl,CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl,CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl,CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($curl,CURLOPT_SSL_VERIFYPEER, 1);
        curl_setopt($curl,CURLOPT_SSL_VERIFYHOST, 2);
        $out = json_decode(curl_exec($curl), true);
        curl_close($curl);

        $leadId = $out['_embedded']['leads'][0]['id'];

        $data = [
            [
                'to_entity_id' => $companyId,
                'to_entity_type' => 'companies'
            ],
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl,CURLOPT_USERAGENT,'amoCRM-oAuth-client/1.0');
        curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
        curl_setopt($curl, CURLOPT_URL, $this->provider->urlAccount() . 'api/v4/leads/'.$leadId.'/link');
        curl_setopt($curl,CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl,CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl,CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($curl,CURLOPT_SSL_VERIFYPEER, 1);
        curl_setopt($curl,CURLOPT_SSL_VERIFYHOST, 2);
        $out = curl_exec($curl);
        curl_close($curl);

        return $leadId;
    }

    public function createTask($text, $time, $entityId, $entityType='leads')
    {
        $accessToken = $this->getToken();
        if($accessToken->hasExpired()) {
            $accessToken = $this->refreshToken($accessToken);
        }

        if (!$accessToken) {
            return false;
            
        }
        $headers = [
            'Authorization: Bearer ' . $accessToken->getToken(),
            'Content-Type:application/json'
        ];

        $data = [
            [
                'text' => $text,
                'responsible_user_id' => \Yii::$app->params['amo_task_user_id'],
                'entity_id' => $entityId,
                'entity_type' => $entityType,
                'complete_till' => $time,
            ],
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl,CURLOPT_USERAGENT,'amoCRM-oAuth-client/1.0');
        curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
        curl_setopt($curl, CURLOPT_URL, $this->provider->urlAccount() . 'api/v4/tasks');
        curl_setopt($curl,CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl,CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl,CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($curl,CURLOPT_SSL_VERIFYPEER, 1);
        curl_setopt($curl,CURLOPT_SSL_VERIFYHOST, 2);
        $out = json_decode(curl_exec($curl), true);
        curl_close($curl);

        $taskId = $out['_embedded']['tasks'][0]['id'];

        return $taskId;
    }

    public function linkContactWithCompany($contact_id, $company_id)
    {
        $accessToken = $this->getToken();
        if($accessToken->hasExpired()) {
            $accessToken = $this->refreshToken($accessToken);
        }

        $headers = [
            'Authorization: Bearer ' . $accessToken->getToken(),
            'Content-Type:application/json'
        ];

        $data = [
            [
                'to_entity_id' => $company_id,
                'to_entity_type' => 'companies'
            ],
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl,CURLOPT_USERAGENT,'amoCRM-oAuth-client/1.0');
        curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
        curl_setopt($curl, CURLOPT_URL, $this->provider->urlAccount() . 'api/v4/contacts/'.$contact_id.'/link');
        curl_setopt($curl,CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl , CURLOPT_HEADER, false);
        curl_setopt($curl,CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl,CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($curl,CURLOPT_SSL_VERIFYPEER, 1);
        curl_setopt($curl,CURLOPT_SSL_VERIFYHOST, 2);
        $out = curl_exec($curl);
        curl_close($curl);
    }

    public function refreshToken($accessToken)
    {
        try {
            $accessToken = $this->provider->getAccessToken(new RefreshToken(), [
                'refresh_token' => $accessToken->getRefreshToken(),
            ]);

            //print_r($accessToken);
            $this->saveToken([
                'accessToken' => $accessToken->getToken(),
                'refreshToken' => $accessToken->getRefreshToken(),
                'expires' => $accessToken->getExpires(),
                'baseDomain' => $this->provider->getBaseDomain(),
            ]);

            return $this->getToken();

        } catch (\Exception $e) {
            \Yii::error((string)$e, __METHOD__);
            return null;
       }
    }

    private function saveToken($accessToken)
    {
        if (
            isset($accessToken)
            && isset($accessToken['accessToken'])
            && isset($accessToken['refreshToken'])
            && isset($accessToken['expires'])
            && isset($accessToken['baseDomain'])
        ) {
            $data = [
                'accessToken' => $accessToken['accessToken'],
                'expires' => $accessToken['expires'],
                'refreshToken' => $accessToken['refreshToken'],
                'baseDomain' => $accessToken['baseDomain'],
            ];


            file_put_contents(\Yii::getAlias('@app').'/libs/amo/credentials.json', json_encode($data));
        } else {
            \Yii::error('Invalid access token ' . var_export($accessToken, true), __METHOD__);
        }
    }

    public function checkRefreshToken()
    {
        $accessToken = json_decode(file_get_contents(\Yii::getAlias('@app').'/libs/amo/credentials.json'), true);
        $token = new AccessToken([
            'access_token' => $accessToken['accessToken'],
            'refresh_token' => $accessToken['refreshToken'],
            'expires' => $accessToken['expires'],
            'baseDomain' => $accessToken['baseDomain'],
        ]);
        print_r($accessToken);
        $accessToken = $this->refreshToken($token);
        print_r($accessToken);
    } 

    private function getToken()
    {
        $accessToken = json_decode(@file_get_contents(\Yii::getAlias('@app').'/libs/amo/credentials.json'), true) ?? [];
        if (!$accessToken) {
            return null;
        }
        //print_r(date('Y-m-d H:i:s', $accessToken['expires'])); exit;
        if ($accessToken && date('Y-m-d H:i:s', $accessToken['expires'])<date('Y-m-d H:i:s')) {
            $token = new AccessToken([
                'access_token' => $accessToken['accessToken'],
                'refresh_token' => $accessToken['refreshToken'],
                'expires' => $accessToken['expires'],
                'baseDomain' => $accessToken['baseDomain'],
            ]);
            $accessToken = $this->refreshToken($token);
            $accessToken = json_decode(file_get_contents(\Yii::getAlias('@app').'/libs/amo/credentials.json'), true);
        }

        if (
            isset($accessToken)
            && isset($accessToken['accessToken'])
            && isset($accessToken['refreshToken'])
            && isset($accessToken['expires'])
            && isset($accessToken['baseDomain'])
        ) {
            return new AccessToken([
                'access_token' => $accessToken['accessToken'],
                'refresh_token' => $accessToken['refreshToken'],
                'expires' => $accessToken['expires'],
                'baseDomain' => $accessToken['baseDomain'],
            ]);
        } else {
            \Yii::error('Invalid access token ' . var_export($accessToken, true), __METHOD__);
        }
    }
}