<?php

namespace app\components;

use app\models\company\Company;
use yii\base\Application;
use yii\base\BootstrapInterface;
use yii\web\NotFoundHttpException;


/**
 * Class GetHostComponent
 * @package app\components
 */
class GetHostComponent implements BootstrapInterface
{
    /**
     * Bootstrap method to be called during application bootstrap stage.
     * @param Application $app the application currently running
     * @throws NotFoundHttpException
     */
    public function bootstrap($app)
    {
        $hostname = $app->getRequest()->getHostName();
        
        $organization = null;
        if ($hostname=="hatimakitelegram.ru") {

        } else if (null === ($organization = Company::findOne(['alias' => $app->getRequest()->getHostName()]))) {
           // throw new NotFoundHttpException('Unknown domain');
        }

        $app->params['company'] = $organization;
    }
}
