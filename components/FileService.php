<?php

namespace app\components;

use app\models\File;
use yii\helpers\FileHelper;
use yii\web\UploadedFile;
use Yii;
use yii\helpers\StringHelper;

class FileService
{
    public function saveFile(UploadedFile $file, $model)
    {
        $path = $this->getUploadPath($file, $model);
        if (is_string($path) && FileHelper::createDirectory(Yii::getAlias('@app/web' . dirname($path)))) {
            $file->saveAs(Yii::getAlias('@app/web' .$path), true);
        }
        $fileModel = new File();

        $fileModel->filename = $path;
        $fileModel->mime = FileHelper::getMimeTypeByExtension($path);
        $fileModel->save();

        return $fileModel;
    }

    public function getUploadPath(UploadedFile $file, $model)
    {
        $folder = mb_strtolower(StringHelper::basename(get_class($model)));
        $path = Yii::getAlias('@web/uploads/' . $folder . '/' . $model->id);
        $ext = $file->getExtension();

        return $path . '/' . $this->getRandomFileName($path, $ext) . '.' . $ext;
    }

    public function getRandomFileName($path, $extension = '')
    {
        $extension = $extension ? '.' . $extension : '';
        $path = $path ? $path . '/' : '';

        do {
            $name = substr(md5(microtime() . rand(0, 9999)), 0, 20);
            $file = $path . $name . $extension;
        } while (file_exists($file));

        return $name;
    }
}