<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%sender_log}}`.
 */
class m190525_192500_create_sender_log_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%sender_log}}', [
            'id' => $this->primaryKey(),
            'created_at' => $this->integer(11)->notNull(),
            'updated_at' => $this->integer(11)->notNull(),
            'service' => $this->string(255)->notNull(),
            'to' => $this->string(255)->notNull(),
            'message' => $this->text()->notNull(),
            'add_params' => $this->text(),
            'status' => $this->string(255)->notNull(),
            'status_response' => $this->text(),
            'service_code' => $this->string(255)->null(),
            'message_id' => $this->string(255)->null()
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%sender_log}}');
    }
}
