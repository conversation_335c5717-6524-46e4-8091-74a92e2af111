<?php

namespace app\components\messenger\models;

use app\modules\foquz\models\BaseModel;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "sender_log".
 *
 * @property int $id
 * @property int $created_at
 * @property int $updated_at
 * @property string $service
 * @property string $to
 * @property string $message
 * @property string $add_params
 * @property string $status
 * @property string $status_response
 * @property string $message_id
 * @property string $service_code
 */
class SenderLog extends BaseModel
{
    const STATUS_SUCCESS = 10;
    const STATUS_ERROR = 20;

    const SERVICE_TELEGRAM = 'telegram';
    const SERVICE_VIBER = 'viber';

    /** @inheritDoc */
    public function behaviors()
    {
        return [
            TimestampBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sender_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['service', 'to', 'message', 'status'], 'required'],
            [['created_at', 'updated_at'], 'integer'],
            [['message', 'add_params', 'status_response'], 'string'],
            [['service', 'to', 'status','service_code','message_id'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'service' => 'Service',
            'to' => 'To',
            'message' => 'Message',
            'add_params' => 'Add Params',
            'status' => 'Status',
            'status_response' => 'Status Response',
        ];
    }
}
