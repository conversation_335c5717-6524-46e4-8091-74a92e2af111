<?php


namespace app\components\messenger;

use app\components\messenger\models\SenderLog;
use app\modules\foquz\models\channels\GlobalViberSettings;
use app\modules\foquz\models\FoquzContact;
use Yii;
use yii\base\Component;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\httpclient\Client;

/**
 * Class ViberSender
 * @package app\components\messenger
 */
class ViberService extends Component
{
    const API_URL = 'https://gateway.api.sc';

    public $login;
    public $psw;
    public $globalSetting;
    /** @var SenderLog */
    private $logger;

    /** @var Client */
    private $client;

    public function init()
    {
        $this->client = new Client(['baseUrl' => self::API_URL]);
        $this->login=ArrayHelper::getValue(Yii::$app->params, 'smsc.login');
        $this->psw=ArrayHelper::getValue(Yii::$app->params,'smsc.password');

    }

    /**
     * @return SenderLog
     */
    public function getLogger()
    {
        return $this->logger;
    }

    /**
     * @param SenderLog $logger
     */
    public function setLogger(SenderLog $logger)
    {
        $this->logger = $logger;
    }

    public function getGlobalSetting($globalSetting)
    {
        return $this->globalSetting;
    }

    public function setGlobalSetting($globalSetting)
    {
        $this->globalSetting = $globalSetting;
    }

    public function send($to, $message, array $addParams = [])
    {

        switch($this->globalSetting->gateway) {
            case GlobalViberSettings::STREAM_GATEWAY:
                $client = new Client();

                $response = $client->createRequest()
                    ->setMethod('POST')
                    ->setUrl('https://gateway.api.sc/rest/Send/SendIM/ViberOne/')
                    ->addHeaders(['content-type' => 'application/x-www-form-urlencoded'])
                    ->setData([
                        'login' => $this->globalSetting->login,
                        'pass' => $this->globalSetting->password,
                        'sourceAddressIM' =>  $this->globalSetting->sender_name,
                        'textIM' => $message,
                        'phone' => $to,
                        'validityPeriod' => 7200,
                    ])
                    ->send();
                //return $response->content;
                break;
            case GlobalViberSettings::SMSPROFI_GATEWAY:
                $ch = curl_init();
                curl_setopt_array($ch, [
                    CURLOPT_URL => "https://lcab.smsprofi.ru/json/v1.0/viber/send/text",
                    CURLOPT_POST => true,
                    CURLOPT_HTTPHEADER => [
                        "X-Token: ".$this->globalSetting->api_key,
                        "Content-Type: application/json"
                    ],
                    CURLOPT_POSTFIELDS => json_encode([
                        'messages' => [
                            [
                                'recipient' => FoquzContact::preformatPhone($to),
                                'text' => $message,
                            ]
                        ],
                        'source' => $this->globalSetting->sender,
                    ]),
                    CURLOPT_RETURNTRANSFER => true
                ]);

                $response = @json_decode(curl_exec($ch), true);
                if (is_array($response) && isset($response["success"]) && !$response["success"] && isset($response["error"])) {
                    return $response["error"]["descr"];
                }
                
                break;
        }

        return true;
    }
}
