<?php

namespace app\components\messenger;

use app\components\messenger\models\SenderLog;
use app\modules\foquz\models\channels\GlobalSmsSettings;
use app\modules\foquz\models\FoquzContact;
use Yii;
use yii\base\Component;
use yii\helpers\ArrayHelper;
use yii\httpclient\Client;

/**
 * Class ViberSender
 * @package app\components\messenger
 */
class SMSService extends Component
{
    const API_URL = 'https://gateway.api.sc';

    public $login;
    public $psw;
    public $globalSetting;
    /** @var SenderLog */
    private $logger;

    /** @var Client */
    private $client;

    public function init()
    {
        $this->client = new Client(['baseUrl' => self::API_URL]);
        $this->login  = ArrayHelper::getValue(Yii::$app->params, 'smsc.login');
        $this->psw    = ArrayHelper::getValue(Yii::$app->params, 'smsc.password');
    }

    /**
     * @return SenderLog
     */
    public function getLogger()
    {
        return $this->logger;
    }

    /**
     * @param SenderLog $logger
     */
    public function setLogger(SenderLog $logger)
    {
        $this->logger = $logger;
    }

    public function getGlobalSetting($globalSetting)
    {
        return $this->globalSetting;
    }

    public function setGlobalSetting($globalSetting)
    {
        $this->globalSetting = $globalSetting;
    }

    public function send($to, $message, array $addParams = [])
    {
        $response = null;


        switch ($this->globalSetting['gateway']) {
            case GlobalSmsSettings::MAIL365_GATEWAY:
                $response = [];
                break;
            case GlobalSmsSettings::SMSC_GATEWAY:
                $client = new Client();
                $url    = "http://smsc.ru/sys/send.php?login=" . urlencode($this->globalSetting['login']) . "&psw=" . urlencode($this->globalSetting['password']) . "&charset=utf-8&phones=" . urlencode($to) . "&mes=" . urlencode($message) . "&sender=" . urlencode($this->globalSetting['sender']);

                $response = $client->createRequest()
                    ->setMethod('GET')
                    ->setUrl($url)
                    ->send();

                return $response->content;
            case GlobalSmsSettings::MTS_GATEWAY:
                $req = [
                    "messages" => [
                        [
                            "content" => [
                                "short_text" => $message,
                            ],
                            "to"      => [
                                [
                                    "msisdn" => $to,
                                ],
                            ],
                        ],
                    ],
                    "options"  => [
                        "class" => 1,
                        "from"  => [
                            "sms_address" => $this->globalSetting['sender_name'],
                        ],
                    ],
                ];
                $auth =  "Basic " . base64_encode($this->globalSetting['login'] . ":" . $this->globalSetting['password']);
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, "https://omnichannel.mts.ru/http-api/v1/messages");
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                //curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($req));
                $headers = array_merge([], ["Authorization: " . $auth, "Content-Type: application/json; charset=utf-8"]);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
                $response = curl_exec($ch);
                die($response);
        //if (curl_error($ch)) {
          //  trigger_error('Curl Error:' . curl_error($ch));
        //}
                curl_close($ch);

            case GlobalSmsSettings::SMSPROFI_GATEWAY:
                $ch = curl_init();
                curl_setopt_array($ch, [
                    CURLOPT_URL            => "https://lcab.smsprofi.ru/json/v1.0/sms/send/text",
                    CURLOPT_POST           => true,
                    CURLOPT_HTTPHEADER     => [
                        "X-Token: " . $this->globalSetting['api_key'],
                        "Content-Type: application/json",
                    ],
                    CURLOPT_POSTFIELDS     => json_encode([
                        'messages' => [
                            [
                                'recipient' => FoquzContact::preformatPhone($to),
                                'text'      => $message,
                                'source'    => $this->globalSetting['sender'],
                            ],
                        ],
                    ]),
                    CURLOPT_RETURNTRANSFER => true,
                ]);

                print_r(json_encode([
                    'messages' => [
                        [
                            'recipient' => FoquzContact::preformatPhone($to),
                            'text'      => $message,
                            'source'    => $this->globalSetting['sender'],
                        ],
                    ],
                ]));

                $response = curl_exec($ch);
                break;
            case GlobalSmsSettings::MEGAFON_GATEWAY:

                $curl       = curl_init();
                $postfields = [
                    "from"    => $this->globalSetting['sender_name'],
                    "message" => $message,
                    "to"      => (int) FoquzContact::preformatPhone($to),
                ];
                curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                curl_setopt($curl, CURLOPT_POST, 1);
                curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($postfields));
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
                curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                curl_setopt($curl, CURLOPT_USERPWD, $this->globalSetting['login'] . ":" . $this->globalSetting['password']);
                curl_setopt($curl, CURLOPT_URL, "https://a2p-api.megalabs.ru/sms/v1/sms");
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

                $response = curl_exec($curl);

                curl_close($curl);

                break;
            case GlobalSmsSettings::SMSRU_GATEWAY:
                $ch = curl_init("https://sms.ru/sms/send");
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(array(
                    "api_id" => $this->globalSetting['api_key'],
                    "to"     => FoquzContact::preformatPhone($to),
                    "msg"    => $message,
                    "json"   => 1,
                )));
                $response = curl_exec($ch);
                curl_close($ch);
                break;
            case GlobalSmsSettings::EDNARU_GATEWAY:
                $ch = curl_init();
                curl_setopt_array($ch, [
                    //CURLOPT_URL => "https://sms.edna.ru/connector_sme/api/smsOutMessage",
                    CURLOPT_URL            => "https://app.edna.ru/api/old/smsOutMessage",
                    CURLOPT_POST           => true,
                    CURLOPT_HTTPHEADER     => [
                        "X-API-KEY: " . $this->globalSetting['api_key'],
                        "Content-Type: application/json",
                    ],
                    CURLOPT_POSTFIELDS     => json_encode(
                        [
                            'id'          => md5('foquz' . $to . microtime()),
                            'address'     => FoquzContact::preformatPhone($to),
                            'content'     => $message,
                            "contentType" => "text",
                            'subject'     => $this->globalSetting['sender'],
                        ]
                    ),
                    CURLOPT_RETURNTRANSFER => true,
                ]);

                $response = curl_exec($ch);
                //print_r($response, bool:return=false)
                break;
            case GlobalSmsSettings::SMS4B_GATEWAY:
                $client = new Client();
                $httpResponse = $client->createRequest()
                    ->setMethod('POST')
                    ->setUrl('https://api.sms4b.ru/v1/sms')
                    ->setHeaders(['Content-Type' => 'application/json', 'Authorization' => $this->globalSetting['api_key']])
                    ->setFormat(Client::FORMAT_JSON)
                    ->setData([
                        'sender' => $this->globalSetting['sender'],
                        'messages' => [
                            [
                                'number' => FoquzContact::preformatPhone($to),
                                'text' => $message,
                            ]
                        ],
                    ])
                    ->setOptions(['timeout' => 10])
                    ->send();

                $response = $httpResponse->content;
                break;
        }

        return $response;
        //print($response."\n");
    }
}
