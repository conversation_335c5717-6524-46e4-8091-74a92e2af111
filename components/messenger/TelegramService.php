<?php


namespace app\components\messenger;


use app\components\messenger\models\SenderLog;
use CURLFile;
use Yii;
use yii\base\Component;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use app\models\TelegramChat;

/**
 * Class TelegramService
 * @package app\components\messenger
 */
class TelegramService extends Component
{
    /** @var SenderLog */
    private $logger;
    private $botToken;

    /** @inheritDoc */
    public function init()
    {
        $this->botToken = ArrayHelper::getValue(Yii::$app->params, 'telegram.bot_token');
        $this->botToken = "962711467:AAG9h3tAIK1Ol0XU5GvKeMvYYYOQ7Sax_Qo";
    }

    /**
     * @return SenderLog
     */
    public function getLogger()
    {
        return $this->logger;
    }

    /**
     * @param SenderLog $logger
     */
    public function setLogger(SenderLog $logger)
    {
        $this->logger = $logger;
    }

    public function send($to, $message, array $addParams = [], $token=null)
    {
        $to = preg_replace("@[^\d]@", "", $to);
        $to = preg_replace("@^[87]@", "", $to);

        $ch = TelegramChat::find()->where(["like", "phone", $to])->one();
        print($to."\n");
        print_r($ch);
        if ($ch) {
           // print("https://api.telegram.org/bot" . ($token ? $token : $this->botToken) . "/sendMessage?chat_id=" . $ch->chat_id . '&text=' . $message."\n");
            $jsonResponse = $this->curlCall("https://api.telegram.org/bot" . ($token ? $token : $this->botToken) . "/sendMessage?chat_id=" . $ch->chat_id . '&text=' . urlencode($message), $addParams);

            $res = json_decode($jsonResponse, true);
            return $res;
          /*  $this
                ->logger
                ->setAttributes([
                    'to' => $to,
                    'message' => $message,
                    'addParams' => Json::encode($addParams),
                    'status' => $res['ok'] ? $this->logger::STATUS_SUCCESS : $this->logger::STATUS_ERROR,
                    'service' => $this->logger::SERVICE_TELEGRAM,
                    'status_response' => ArrayHelper::getValue($res, 'description'),
                    'service_code' => ArrayHelper::getValue($res, 'error_code'),
                    'message_id' => ArrayHelper::getValue($res, 'result.message_id'),
                ]);

            $this
                ->logger
                ->save();*/
        }
    }

    private function curlCall($url, $option = array())
    {
        $attachments = ['photo', 'sticker', 'audio', 'document', 'video'];
        print($url."\n");
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, "PostManGoBot 1.0");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);


        if (count($option)) {
            curl_setopt($ch, CURLOPT_POST, true);

            foreach ($attachments as $attachment) {
                if (isset($option[$attachment])) {
                    $option[$attachment] = $this->curlFile($option[$attachment]);
                    break;
                }
            }
            curl_setopt($ch, CURLOPT_POSTFIELDS, $option);
        }
        $r = curl_exec($ch);
        if ($r == false) {
            $text = 'eroror ' . curl_error($ch);
            $myfile = fopen("error_telegram.log", "w") or die("Unable to open file!");
            fwrite($myfile, $text);
            fclose($myfile);
        }
        curl_close($ch);
        return $r;
    }

    private function curlFile($path)
    {
        if (is_array($path))
            return $path['file_id'];

        $realPath = realpath($path);

        if (class_exists('CURLFile'))
            return new CURLFile($realPath);

        return '@' . $realPath;
    }
}
