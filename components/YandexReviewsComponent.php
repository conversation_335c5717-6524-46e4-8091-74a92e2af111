<?php

namespace app\components;

use app\modules\foquz\models\FilialReview;
use KubAT\PhpSimple\HtmlDomParser;

class YandexReviewsComponent
{
    private $url;

    const REVIEW_SELECTOR = '.business-review-view';
    const DATE_SELECTOR = '.business-review-view__date';
    const DATE_SELECTOR_VALUE = '.business-review-view__date meta';
    const STAR_SELECTOR = '.business-rating-badge-view__star';
    const EMPTY_STAR_SELECTOR = '._empty';
    const AUTHOR_SELECTOR = '.business-review-view__author a';
    const AUTHOR_SELECTOR_WITHOUT_LINK = '.business-review-view__author';
    const AUTHOR_SELECTOR_WITHOUT_LINK_VALUE = '.business-review-view__author span';
    const AUTHOR_SELECTOR_VALUE = '.business-review-view__author a span';
    const AUTHOR_IMAGE_SELECTOR = '.business-review-view__author meta';
    const REVIEW_TEXT_SELECTOR = '.business-review-view__body-text';

    public function __construct($orgId)
    {
        $this->url = "https://yandex.ru/maps/org/{$orgId}/reviews/";
    }

    public function parse()
    {
        $response = $this->request($this->url);

        //print($this->url);
        //exit;

        $reviews = [];
//$pos=strpos($response,'<div class="reviews-view__reviews">');

        $pos=strpos($response,'<div class="business-reviews-card-view__reviews-container">');
        $part=substr($response,$pos);

       // print($part);
       // exit;
        $pos2=strpos($part, '</section>');
        //$part=substr($part,0,$pos2);
        $html = HtmlDomParser::str_get_html($part);
        if(is_bool($html))
            return $reviews;

        if(count($html->find(self::REVIEW_SELECTOR))){

            foreach($html->find(self::REVIEW_SELECTOR) as $div){
                $review=[];
                if(count($div->find(self::DATE_SELECTOR))){
                    $dd = $div->find(self::DATE_SELECTOR_VALUE);
                    if (count($dd)==0) {
                        print($div->content."\n");
                        print ("no created_at\n");
                        continue;
                    }
                    $reviewDate=$div->find(self::DATE_SELECTOR_VALUE)[0]->content;
                    $review['created_at']= date('Y-m-d H:i:s', strtotime($reviewDate));
                }
                $review['rating'] =
                    count($div->find(self::STAR_SELECTOR)) -
                    count($div->find(self::EMPTY_STAR_SELECTOR));
                if(count($div->find(self::AUTHOR_SELECTOR))){
                    $reviewAuthor=$div->find(self::AUTHOR_SELECTOR_VALUE)[0]->innertext;
                    $review['user_fio']=$reviewAuthor;
                }
                if(!isset($review['user_fio'])) {
                    if(count($div->find(self::AUTHOR_SELECTOR_WITHOUT_LINK))){
                        $reviewAuthor=$div->find(self::AUTHOR_SELECTOR_WITHOUT_LINK_VALUE)[0]->innertext;
                        $review['user_fio']=$reviewAuthor;
                    }
                }
                if(count($div->find(self::AUTHOR_SELECTOR))){
                    $reviewAuthor=$div->find(self::AUTHOR_SELECTOR)[0]->href;
                    $review['user_link']=$reviewAuthor;
                }
                if(count($div->find(self::AUTHOR_IMAGE_SELECTOR))){
                    $reviewImage=$div->find(self::AUTHOR_IMAGE_SELECTOR)[0]->content;
                    $review['user_avatar']=$reviewImage;
                }
                if(count($div->find(self::REVIEW_TEXT_SELECTOR))){
                    $reviewText=$div->find(self::REVIEW_TEXT_SELECTOR)[0]->innertext;
                    $review['text']=$reviewText;
                }
                $review['source'] = FilialReview::SOURCE_YANDEX;

                $reviews[]=$review;
            }
        }

       // print_r($reviews); exit;
        return $reviews;
    }

    private function request($link)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $link);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $server_output = curl_exec($ch);

        curl_close($ch);

        return $server_output;
    }
}