<?php

namespace app\components\helpers;

use app\models\Dictionary;
use app\models\DictionaryElement;
use app\models\Filial;
use app\modules\foquz\exceptions\DictionariesNotFoundException;
use app\modules\foquz\models\CompanyFeedbackTheme;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingCompensation;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingEmployee;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingFine;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingReason;
use yii\helpers\ArrayHelper;

class DictionariesHelper
{
    /**
     * function buildTree
     * @param array $elements
     * @param int $parentId
     * @return array
     */
    public static function buildTree(array $elements, int $parentId = 0): array
    {
        //print_r($elements); exit;
        $branch = [];
        /** @var \app\models\DictionaryElement $element */
        foreach ($elements as $element) {
            if ($element->parent_id == $parentId) {
                $children = self::buildTree($elements, $element->id);
                if (!empty($children)) {
                    $element->setAttribute('elements', $children);
                }
                $branch[] = $element;
            }
        }

        return $branch;
    }

    /**
     * @throws \app\modules\foquz\exceptions\DictionariesNotFoundException
     */
    public static function getDictionaryById($id): Dictionary
    {
        $dictionary = Dictionary::find()
            ->andWhere(['=', 'id', $id])
            ->andWhere(['=', 'company_id', \Yii::$app->user->identity->company->id])
            ->andWhere(['=', 'deleted', 0])
            ->one();

        if (!$dictionary) {
            throw new DictionariesNotFoundException('Невозможно найти справочник');
        }

        return $dictionary;
    }


    /**
     * @throws \app\modules\foquz\exceptions\DictionariesNotFoundException
     */
    public static function getDictionaryByIdAndCompanyId($id, $companyId): Dictionary
    {
        $dictionary = Dictionary::find()
            ->andWhere(['=', 'id', $id])
            ->andWhere(['=', 'company_id', $companyId])
            ->andWhere(['=', 'deleted', 0])
            ->one();

        if (!$dictionary) {
            throw new DictionariesNotFoundException('Невозможно найти справочник');
        }

        return $dictionary;
    }

    public static function getLastPosition(DictionaryElement $element)
    {
        $model = DictionaryElement::find()
            ->where(['dictionary_id' => $element->dictionary_id, 'parent_id' => $element->parent_id])
            ->andWhere(['<>', 'deleted', 1])
            ->orderBy('position DESC')
            ->one();

        return $model->position ?? null;
    }

    /**
     * @return array[]
     */
    public static function listSystemDictionary(): array
    {
        $userCompanyId = \Yii::$app->user->identity->company->id;

        $dictionaries = [
            [
                'id' => 'compensations',
                'name' => 'Компенсации',
                'count' => (int)FoquzPollAnswerProcessingCompensation::find()->where(['company_id' => $userCompanyId, 'deleted' => 0])->count(),
                'description' => '',
                'system' => 1,
                'used' => 0,
                'is_active' => 1,
            ],
            [
                'id' => 'employees',
                'name' => 'Сотрудники компании',
                'count' => (int)FoquzPollAnswerProcessingEmployee::find()
                    ->where(['company_id' => $userCompanyId, 'deleted' => 0])
                    ->andWhere("hrm_deleted is null or hrm_deleted != 'Удалён'")
                    ->count(),
                'description' => '',
                'system' => 1,
                'used' => 0,
                'is_active' => 1,
            ],
            [
                'id' => 'reasons',
                'name' => 'Типы причин',
                'count' => (int)FoquzPollAnswerProcessingReason::find()->where(['company_id' => $userCompanyId, 'deleted' => 0])->count(),
                'description' => '',
                'system' => 1,
                'used' => 0,
                'is_active' => 1,
            ],
            [
                'id' => 'fines',
                'name' => 'Взыскания или нарушения',
                'count' => (int)FoquzPollAnswerProcessingFine::find()->where(['company_id' => $userCompanyId, 'deleted' => 0])->count(),
                'description' => '',
                'system' => 1,
                'used' => 0,
                'is_active' => 1,
            ],
            [
                'id' => 'filials',
                'name' => 'Филиалы',
                'count' => (int)Filial::find()->where(['company_id' => $userCompanyId, 'is_active' => true])->count(),
                'description' => '',
                'system' => 1,
                'used' => 0,
                'is_active' => 1,
            ],
            [
                'id' => 'themes',
                'name' => 'Темы виджета обратной связи',
                'count' => (int)CompanyFeedbackTheme::find()->where(['company_id' => $userCompanyId])
                    ->andWhere('deleted is null or deleted = 0')
                    ->count(),
                'description' => '',
                'system' => 1,
                'used' => 0,
                'is_active' => 1,
            ]
        ];

        return $dictionaries;
    }

    /**
     * @param array $elements
     * @param int $element_id
     * @return array
     */
    public static function getTreeForElement(array $elements, int $element_id)
    {
        $elements = ArrayHelper::index($elements, 'id');
        $element = $elements[$element_id];
        if (!$element) {
            return [];
        }

        return self::getElementWithParent($elements, $element);
    }

    /**
     * @param array $elements
     * @param array $tree
     * @return array
     */
    public static function getElementWithParent(array $elements, array $tree): array
    {
        if (!$elements[$tree['id']]['parent_id']) {
            return $tree;
        }

        $result = $elements[$elements[$tree['id']]['parent_id']];
        $result['childs'] = $tree;

        return self::getElementWithParent($elements, $result);
    }

    public static function compactTree(array $tree, &$compactedTree = [])
    {
        if (empty($tree['childs'])) {
            return [$compactedTree, $tree];
        }
        $element = $tree;
        unset($element['childs']);
        $compactedTree[] = $element;
        return self::compactTree($tree['childs'], $compactedTree);
    }

    public static function buildApiElements($dictionaryID, $selectedItems)
    {
        $elements = DictionaryElement::find()->where(['dictionary_id' => $dictionaryID])->asArray()->all();
        $apiTree = [];
        foreach ($selectedItems as $selectedItem) {
            $tree = self::getTreeForElement($elements, $selectedItem);
            self::getApiItemTree($tree, $apiTree);
        }
        self::prepareApiTree($apiTree);
        return $apiTree;
    }

    /**
     * @param DictionaryElement[] $elements
     * @param int[] $selectedItems
     * @return array
     */
    public static function buildApiV2Elements(array $elements, array $selectedItems): array
    {
        $elements = ArrayHelper::toArray($elements);
        $elements = ArrayHelper::index($elements, 'id');
        self::getTreeIDS($elements, $selectedItems);
        $selectedItems = array_unique($selectedItems);
        $elements = array_filter($elements, static function ($element) use ($selectedItems) {
            return in_array($element['id'], $selectedItems);
        });
        $tree = self::buildTreeLight($elements);
        $apiTree = [];
        self::prepareApiV2Tree($tree, $apiTree);
        return $apiTree;
    }

    /**
     * @param array $elements
     * @param array $ids
     * @return void
     */
    public static function getTreeIDS(array $elements, array &$ids): void
    {
        foreach ($ids as $id) {
            if (!empty($elements[$id]['parent_id'])) {
                $newID = [$elements[$id]['parent_id']];
                self::getTreeIDS($elements, $newID);
                $ids = array_merge($ids, $newID);
            }
        }
    }

    /**
     * @param array $elements
     * @param int $parentId
     * @return array
     */
    public static function buildTreeLight(array $elements, int $parentId = 0): array
    {
        $branch = [];
        foreach ($elements as $element) {
            if ($element['parent_id'] == $parentId) {
                $children = self::buildTreeLight($elements, $element['id']);
                if ($children) {
                    $element['childs'] = $children;
                } else {
                    $element['childs'] = null;
                }
                $branch[] = $element;
            }
        }
        return $branch;
    }


    /**
     * @param array $tree
     * @param array $apiTree
     * @return void
     */
    public static function prepareApiV2Tree(array $tree, array &$apiTree): void
    {
        foreach ($tree as $element) {
            $item = [
                'title' => $element['title'],
            ];
            if ($element['type'] === DictionaryElement::TYPE_CATEGORY) {
                $item['categories'] = [];
                self::prepareApiV2Tree($element['childs'], $item);
                $apiTree['categories'][] = $item;
            } else {
                $apiTree['items'][] = $item;
            }
        }
    }

    /**
     * @param array $tree
     * @param array $apiTree
     * @param array $langs
     * @return void
     */
    public static function prepareTreeForQuestion(array $tree, array &$apiTree, array $langs): void
    {
        foreach ($tree as $position => $element) {
            $item = [
                'id' =>             $element['id'],
                'title' =>          $element['title'],
                'description' =>    $element['description'],
                'langs' =>          $langs[$element['id']] ?? null,
                'position' =>       $element['position']  ?: $position,
            ];
            if ($element['type'] === DictionaryElement::TYPE_CATEGORY) {
                $item['isCategory'] = true;
                $item['children'] = [];
                self::prepareTreeForQuestion($element['childs'] ?? [], $item, $langs);
            } else {
                $item['isCategory'] = false;
            }
            $apiTree['children'][$element['title']] = $item;
        }
    }

    public static function getApiItemTree($tree, &$apiTree): void
    {
        if (empty($tree['childs'])) {
            $apiTree['items'][$tree['id']] = [
                'title' => $tree['title'],
            ];
            return;
        }
        $apiTree['categories'][$tree['id']] = [
            'title' => $tree['title'],
        ];
        self::getApiItemTree($tree['childs'], $apiTree['categories'][$tree['id']]);
    }

    public static function prepareApiTree(&$apiTree)
    {
        $item = [];
        if (!empty($apiTree['title'])) {
            $item['title'] = $apiTree['title'];
        }
        $item['categories'] = $apiTree['categories'] ?? [];
        $item['items'] = $apiTree['items'] ?? [];
        $apiTree = $item;
        $apiTree['items'] = array_values($apiTree['items']);
        $apiTree['categories'] = array_values($apiTree['categories']);
        foreach ($apiTree['categories'] as &$category) {
            self::prepareApiTree($category);
        }
    }

    public static function filterElements($elements, $filter)
    {
        $elements = array_filter($elements, function ($element) use ($filter) {
            return in_array($element['id'], $filter);
        });
        foreach ($elements as $key => $element) {
            if (!empty($element['elements'])) {
                $elements[$key]['elements'] = self::filterElements($element['elements'], $filter);
            }
        }
        return $elements;
    }
}
