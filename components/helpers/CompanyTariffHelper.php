<?php

namespace app\components\helpers;

use app\components\dto\AddCompanyTariffDto;
use app\components\exceptions\AddCompanyTariffException;
use app\modules\foquz\models\CompanyTariff;

class CompanyTariffHelper
{
    /**
     * @param \app\components\dto\AddCompanyTariffDto $addCompanyTariffDto
     * @return \app\modules\foquz\models\CompanyTariff
     * @throws \app\components\exceptions\AddCompanyTariffException
     */
    public static function addCompanyTariff(AddCompanyTariffDto $addCompanyTariffDto): CompanyTariff
    {
        // TODO: через сервис бы....
        $companyTariff = new CompanyTariff();
        $companyTariff->company_id = $addCompanyTariffDto->companyId;
        $companyTariff->tariff_id = $addCompanyTariffDto->tariffId;
        $companyTariff->from = $addCompanyTariffDto->from;
        $companyTariff->to = $addCompanyTariffDto->to;
        $companyTariff->payment_type = $addCompanyTariffDto->paymentType;
        $companyTariff->cost = $addCompanyTariffDto->cost;

        if (false == $companyTariff->save()) {
            throw new AddCompanyTariffException();
        }

        return $companyTariff;
    }
}
