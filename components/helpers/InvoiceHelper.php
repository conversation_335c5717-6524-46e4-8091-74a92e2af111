<?php

namespace app\components\helpers;

use app\modules\foquz\models\Invoice;

class InvoiceHelper
{
    public static function createFromCompanyTariff($companyTariff, $total = 0): Invoice
    {
        return Invoice::getOrCreateInvoice($companyTariff, $total = 0);
    }

    public static function getTotalCost($companyTariff)
    {
        return $companyTariff->have_answers * (
        $companyTariff->answer_cost ?
            $companyTariff->answer_cost :
            ($companyTariff->company->answer_cost ? $companyTariff->company->answer_cost : $companyTariff->tariff->answer_cost));
    }
}
