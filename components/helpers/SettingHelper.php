<?php

namespace app\components\helpers;

class SettingHelper
{
    // Отправка уведомления об оплате на почту
    const SEND_PAYMENT_NOTIFICATION_EMAIL = 'send_payment_notification_email';

    // Отправка уведомления об оплате на почту дней
    const SEND_PAYMENT_NOTIFICATION_EMAIL_DAY = 'send_payment_notification_email_day';

    // Показывать уведомление об оплате в личном кабинете
    const SHOW_PAYMENT_NOTIFICATION_LK = 'show_payment_notification_lk';

    // Показывать уведомление об оплате в личном кабинете дней
    const SHOW_PAYMENT_NOTIFICATION_LK_DAY = 'show_payment_notification_lk_day';

    // Блокировать тариф при превышении лимита ответов
    const BLOCK_TARIFF_IF_EXCEED_ANSWER_LIMIT = 'block_tariff_if_exceed_answer_limit';

    // Блокировать, если предоплата не внесена в течение периода
    const BLOCK_IF_PREPAYMENT_IS_NOT_MADE_WITHIN_PERIOD = 'block_if_prepayment_is_not_made_within_period';

    // Блокировать, если предоплата не внесена в течение периода дней
    const BLOCK_IF_PREPAYMENT_IS_NOT_MADE_WITHIN_PERIOD_DAY = 'block_if_prepayment_is_not_made_within_period_day';

    // Период постоплаты
    const POSTPAID_PERIOD_DAY = 'postpaid_period_day';
}
