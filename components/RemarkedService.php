<?php

namespace app\components;

class RemarkedService
{
    private $login;
    private $password;
    private $point;
    private $tokem;
    const URL = "https://app.remarked.ru/api/v1/";

    public function __construct($point)
    {
        $this->point    = $point;
        //$this->password = $password;
        $this->login();
    }

    private function _request($data, $path=null, $method=null)
    {
        $url = self::URL;
        if ($path) $url .= $path;
        $data["request_id"] = time();
        if ($method) $data["method"] = $method;
        $ch = curl_init();
        print_r(json_encode($data)."\n");
        print($url."\n");
        curl_setopt_array($ch, [
            CURLOPT_URL            => $url,
            CURLOPT_POST           => true,
            CURLOPT_HTTPHEADER     => ["Content-Type: application/json"],
            CURLOPT_POSTFIELDS     => json_encode($data),
            CURLOPT_RETURNTRANSFER => true,
        ]);
        $res = curl_exec($ch);
        print($res."\n");
        return $res;
    }

    public function login()
    {
        if ($this->point) {
            $data = [
                "point"   =>  $this->point,
            ];

            $response = @json_decode($this->_request($data, "ApiReservesWidget", "GetToken"), true);
            print_r($response);
            exit;


            if (is_array($response) && isset($response['session'])) {
                $this->session = $response["session"];
                return true;
            }
        }
        return false;
    }

    public function sendEmail($from, $fromName, $to, $subject, $html, $replyEmail=null, $replyName=null)
    {
        if ($this->session) {
            $data = [
                "action"   => "issue.send",
                "group"    => "personal",
                "sendwhen" => "now",
                "email"    => $to,
                "session"  => $this->session,
                "letter"   => [
                    "from.email" => $from,
                    "subject"    => $subject,
                    "from.name"  => $fromName,
                    "message"    => [
                        "html" => $html,
                    ],
                ],
            ];
            if ($replyEmail) $data["letter"]["reply.email"] = $replyEmail;
            if ($replyName) $data["letter"]["reply.name"] = $replyName;

            $response = @json_decode($this->_request($data), true);
            return $response;
        }

        return false;
    }

    public function trackStatus($id)
    {
        if ($this->session) {
            $data = [
                "action"   => "track.get",
                "id"    => $id,
                "session"  => $this->session,
            ];
            $response = @json_decode($this->_request($data), true);
            return $response;
        }

        return false;

    }

}
