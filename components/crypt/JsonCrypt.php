<?php

declare(strict_types=1);

namespace app\components\crypt;


use Yii;
use yii\helpers\ArrayHelper;

class JsonCrypt
{
    private IEncryptor $encryptor;

    // Доступные шифровальщики. Активный выбирается по ключу из params['json_crypt']
    private array $encryptors = [
        'openssl-aes-256' => [
            'class' => OpenSSLEncryptor::class,
            'id' => 'a3ca5eb96a47755e04ac7a46b81f6283', // идентификатор версии шифрования, пишется в файл
        ],
        'openssl-aes-128' => [
            'class' => OpenSSLEncryptor::class,
            'id' => 'cf905bbd97143ffa84ed596e1b6f43d2',
        ],
        'base64' => [
            'class' => Base64Encryptor::class,
            'id' => '83e96b0db853391de17cdd99277506e9',
        ],
        'dummy' => [
            'class' => DummyEncryptor::class,
            'id' => '2ced193b316c28ecde131f5cbd7b19ec',
        ],
    ];

    private array $params;

    public function __construct()
    {
        try {
            $this->params = Yii::$app->params['json_crypt'];
            $encryptorClass = $this->getEncryptorClassByAlgoName($this->params['cipher_algo_name']);
            // инстанс шифровальщика по умолчанию
            $this->encryptor = new $encryptorClass(
                $this->encryptors[$this->params['cipher_algo_name']]['id'],
                $this->params['secret_key']
            );

        } catch (\Throwable $e) {
            Yii::error($e->getMessage());
            throw new \InvalidArgumentException($e->getMessage());
        }
    }

    public function encrypt(string $data): string
    {
        return $this->encryptor->encrypt($data);
    }

    public function decrypt(string $data): string
    {
        if (!$this->isJson($data)) {
            // в первых 32 символах находится идентификатор версии шифрования
            $alg = substr($data, 0, 32);
            $encryptorClass = $this->getEncryptorClassById($alg);

            // инстанс шифровальщика по идентификатору версии из файла
            $this->encryptor = new $encryptorClass(
                $alg,
                $this->params['secret_key']
            );

            $data = $this->encryptor->decrypt($data);
        }
        // это обычный нешифрованный json
        return $data;
    }

    /**
     * получить имя класса шифровальщика по названию алгоритма
     * @param string $cipherAlgoName
     * @return string
     */
    private function getEncryptorClassByAlgoName(string $cipherAlgoName): string
    {
        if (!isset($this->encryptors[$cipherAlgoName]) || strlen($this->encryptors[$cipherAlgoName]['id']) !== 32) {
            throw new \InvalidArgumentException('Cipher algorithm not supported');
        }
        return $this->encryptors[$cipherAlgoName]['class'];
    }

    /**
     * получить имя класса шифровальщика по 32-символьной метке
     * @param string $cipherAlgoId
     * @return string
     */
    private function getEncryptorClassById(string $cipherAlgoId): string
    {
        $ids = ArrayHelper::index($this->encryptors, 'id');
        if (!isset($ids[$cipherAlgoId])) {
            throw new \InvalidArgumentException('Cipher algorithm not supported');
        }
        return $ids[$cipherAlgoId]['class'];
    }

    private function isJson(string $data): bool
    {
        json_decode($data);
        return json_last_error() === JSON_ERROR_NONE;
    }
}