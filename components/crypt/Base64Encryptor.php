<?php

declare(strict_types=1);

namespace app\components\crypt;

readonly class Base64Encryptor implements IEncryptor
{
    public function __construct(protected string $cipherAlgoId)
    {}

    public function encrypt(string $data): string
    {
        return $this->cipherAlgoId . base64_encode($data);
    }

    public function decrypt(string $data): string
    {
        $data = substr($data, 32);
        return base64_decode($data);
    }
}