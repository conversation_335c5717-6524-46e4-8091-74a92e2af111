<?php

declare(strict_types=1);

namespace app\components\crypt;

class OpenSSLEncryptor implements IEncryptor
{
    // идентификаторы алгоритмов шифрования
    protected const CIPHER_ALGOS = [
        'a3ca5eb96a47755e04ac7a46b81f6283' => 'aes-256-cbc',
        'cf905bbd97143ffa84ed596e1b6f43d2' => 'aes-128-ctr',
    ];

    // метод шифрования
    protected string $cipherAlgo;
    protected string $secretKey;

    public function __construct(
        protected readonly string $cipherAlgoId,
        string $secretKey)
    {
        if (!isset(self::CIPHER_ALGOS[$this->cipherAlgoId])) {
            throw new \InvalidArgumentException('Cipher algorithm not supported');
        }
        $this->cipherAlgo = self::CIPHER_ALGOS[$this->cipherAlgoId];
        $this->secretKey = substr(hash('sha256', $secretKey), 0, 32);
    }

    public function encrypt(string $data): string
    {
        // случайный вектор инициализации для уникальности зашифрованного текста
        $ivlen = openssl_cipher_iv_length($this->cipherAlgo);
        $iv = openssl_random_pseudo_bytes($ivlen);

        $data = openssl_encrypt($data, $this->cipherAlgo, $this->secretKey, OPENSSL_RAW_DATA, $iv);
        if ($data !== false) {
            return $this->cipherAlgoId . base64_encode($data  . $iv);
        }
        throw new \Exception('Encryption failed');
    }

    public function decrypt(string $data): string
    {
        $alg = substr($data, 0, 32);
        $this->cipherAlgo = self::CIPHER_ALGOS[$alg];
        // данные с вектором инициализации
        $data = substr($data, 32);

        $data = base64_decode($data);
        // выделяем вектор
        $ivlen = openssl_cipher_iv_length($this->cipherAlgo);
        $iv = substr($data, -$ivlen);
        // выделяем данные
        $data = substr($data, 0, -$ivlen);

        if (false !== $data = openssl_decrypt($data, $this->cipherAlgo, $this->secretKey, OPENSSL_RAW_DATA, $iv)) {
            return $data;
        }
        throw new \Exception('Decryption failed');
    }
}