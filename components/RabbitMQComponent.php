<?php

namespace app\components;

use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Exchange\AMQPExchangeType;
use PhpAmqpLib\Message\AMQPMessage;
use Yii;
use yii\base\Component;

class RabbitMQComponent extends Component
{
    public const DEFAULT_PRIORITY = 1024;

    public string $hostname = 'localhost';
    public int $port = 5672;
    public string $user = 'guest';
    public string $password = 'guest';
    public string $vhost = '/';
    public float $connectionTimeout = 3.0;
    public float $readWriteTimeout = 3.0;
    public bool $keepalive = false;
    public int $heartbeat = 0;
    private AMQPStreamConnection $connection;
    private AMQPChannel $channel;
    private string $queueName = 'queue';
    private string $exchangeName = 'router';
    private int $priority = self::DEFAULT_PRIORITY;
    private string $messageType;
    private string $class;
    private string $callbackQueue;
    private string $correlationId;
    private mixed $msgBody = null;
    private mixed $rpc = false;


    /**
     * @throws \Exception
     */
    public function __construct($config = [])
    {
        parent::__construct($config);
        $this->connect();
        $this->correlationId = uniqid('', true);
    }

    /**
     * @throws \Exception
     */
    public function __destruct()
    {
        $this->disconnect();
    }

    /**
     * Выполняет отправку сообщения в очередь
     * @param array $messageBody
     * @return void
     */
    public function push(array $messageBody): void
    {
        if (empty($this->messageType)) {
            throw new \RuntimeException('Message type is not set');
        }
        $this->exchangeName = $this->queueName;
        $messageBody = [
            'type' => $this->messageType,
            'body' => $messageBody,
        ];
        $this->channel->queue_declare($this->queueName, false, true, false, false);
        $this->channel->exchange_declare($this->exchangeName, AMQPExchangeType::DIRECT, false, true, false);
        $this->channel->queue_bind($this->queueName, $this->exchangeName);
        $messageProperties = [
            'content_type' => 'text/plain',
            'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT,
        ];
        if ($this->correlationId && $this->rpc) {
            $messageProperties['correlation_id'] = $this->correlationId;
        }
        $message = new AMQPMessage(serialize($messageBody), $messageProperties);
        $this->channel->basic_publish($message, $this->exchangeName);
    }

    public function callRPC(array $messageBody): mixed
    {
        if (empty($this->messageType)) {
            throw new \RuntimeException('Message type is not set');
        }
        $this->exchangeName = $this->queueName;
        $messageBody = [
            'type' => $this->messageType,
            'body' => $messageBody,
        ];
        [$this->callbackQueue,,] = $this->channel->queue_declare(
            '',
            false,
            false,
            true,
            false,
        );
        $this->channel->basic_consume(
            $this->queueName,
            '',
            false,
            true,
            false,
            false,
            array(
                $this,
                'parseRPCResponse'
            )
        );
        $message = new AMQPMessage(serialize($messageBody), array(
            'content_type' => 'text/plain',
            'correlation_id' => $this->correlationId,
            'reply_to' => $this->callbackQueue,
        ));
        $this->channel->basic_publish($message, $this->exchangeName);

        $startTime = time();

        while ($this->msgBody === null && time() - $startTime < 60) {
            $this->channel->wait();
        }

        if ($this->msgBody === null) {
            throw new \RuntimeException('RPC timeout');
        }

        return $this->msgBody;
    }

    /**
     * Осуществляет подключение к брокеру сообщений
     * @return void
     * @throws \Exception
     */
    private function connect(): void
    {
        try {
            $this->connection = new AMQPStreamConnection(
                $this->hostname,
                $this->port,
                $this->user,
                $this->password,
                $this->vhost,
                false,
                'AMQPLAIN',
                null,
                'en_US',
                $this->connectionTimeout,
                $this->readWriteTimeout,
                null,
                $this->keepalive,
                $this->heartbeat,
            );
            $channel = $this->connection->channel();
        } catch (\Exception $e) {
            throw new \Exception('Could not connect to the message broker:' . $e->getMessage());
        }
        $this->channel = $channel;
    }

    /**
     * Осуществляет отключение от брокера сообщений
     * @throws \Exception
     */
    private function disconnect(): void
    {
        $this->channel->close();
        $this->connection->close();
    }

    /**
     * Устанавливает имя очереди
     * @param string $name
     * @return $this
     */
    public function queue(string $name): static
    {
        $this->queueName = $name;
        return $this;
    }

    /**
     * @param $rpc
     * @return $this
     */
    public function rpc($rpc): static
    {
        $this->rpc = $rpc;
        return $this;
    }

    /**
     * @param string $correlationId
     * @return $this
     */
    public function correlationID(string $correlationId): static
    {
        $this->correlationId = $correlationId;
        return $this;
    }

    /**
     * Устанавливает тип сообщения
     * @param string $type
     * @return $this
     */
    public function type(string $type): static
    {
        $this->messageType = $type;
        return $this;
    }

    /**
     * Устанавливает имя обменника
     * @param string $name
     * @return $this
     */
    public function exchange(string $name): static
    {
        $this->exchangeName = $name;
        return $this;
    }

    /**
     * Устанавливает класс, который будет обрабатывать сообщения
     * @param string $class
     * @return $this
     */
    public function class(string $class): static
    {
        $this->class = $class;
        if (!class_exists($class)) {
            throw new \RuntimeException('Class ' . $class . ' not found');
        }
        if (!method_exists($class, 'execute')) {
            throw new \RuntimeException('Method execute not found in class ' . $class);
        }
        return $this;
    }

    /**
     * Осуществляет прослушивание очереди
     * @return void
     */
    public function listen(): void
    {
        $this->channel->queue_declare($this->queueName, false, true, false, false);
        $this->channel->exchange_declare($this->exchangeName, AMQPExchangeType::DIRECT, false, true, false);

        $class = new $this->class();
        $this->channel->basic_consume(
            $this->queueName,
            '',
            false,
            true,
            false,
            false,
            function ($message) use ($class) {
                $class->execute($this->queueName, $message);
            }
        );
        while($this->channel->is_consuming()) {
            $this->channel->wait();
        }
    }

    /**
     * Разбирает сообщение из очереди
     * @param string $message
     * @return array
     */
    public static function parseMessage(string $message): array
    {
        $message = unserialize($message, ['allowed_classes' => false]);
        if (!isset($message['type'], $message['body'])) {
            throw new \RuntimeException('Invalid message format');
        }
        return [$message['type'], $message['body']];
    }

    public function parseRPCResponse(AMQPMessage $message): void
    {
        if ($message->get('correlation_id') === $this->correlationId) {
            $message->ack();
            $this->msgBody = unserialize($message->getBody(), ['allowed_classes' => false]);
        }
        $message->reject();
    }

    public static function probe(): void
    {
        $file = Yii::getAlias(Yii::$app->params['queueDiagnosticFile']);
        if (!file_exists($file)) {
            print("ok");
            return;
        }

        $lastStartTime = file_get_contents($file);
        if (!empty($lastStartTime) && $lastStartTime <
            (time() - Yii::$app->params['queueMaxMessageExecutionTime'] * 60)) {
            throw new \Exception('Probe failed: ' .
                'last start time is more than ' .
                Yii::$app->params['queueMaxMessageExecutionTime'] . ' minutes ago');
        }
        print("ok");
    }
}
