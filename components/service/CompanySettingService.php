<?php

namespace app\components\service;

use app\models\company\CompanySetting;
use app\models\Setting;

class CompanySettingService extends AbstractSettingService
{
    private $companyId;
    public function __construct() {
        $this->companyId = \Yii::$app->user->identity->company->id;
    }

    /**
     * @param CompanySetting $setting
     * @param $key
     * @param $value
     * @return void
     */
    protected function save($setting, $key, $value): bool
    {
        $setting->company_id = $this->companyId;
        $setting->setting_key = $key;
        $setting->setting_value = $value;
        return $setting->save();
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    protected function baseQuery(): \yii\db\ActiveQuery
    {
        return CompanySetting::find()->where(['=', 'company_id', \Yii::$app->user->identity->company->id]);
    }

    protected function getModel(): string
    {
        return CompanySetting::class;
    }
}
