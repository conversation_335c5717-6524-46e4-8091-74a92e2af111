<?php

namespace app\components\service;

use app\components\exception\SettingException;
use app\components\exception\SettingNotFoundException;
use app\models\company\CompanySetting;
use app\models\Setting;

abstract class AbstractSettingService
{
    /**
     * @param string $key
     * @param string $default
     * @return string
     */
    public function getValueByKey(string $key, string $default = ''): string
    {
        $setting = $this->baseQuery()->andWhere(['=', 'setting_key', $key])->one();
        if ($setting) {
            return $setting->setting_value;
        }

        return $default;
    }

    /**
     * @throws \app\components\exception\SettingNotFoundException
     */
    public function getByKey(string $key)
    {
        $setting = $this->baseQuery()->andWhere(['=', 'setting_key', $key])->one();
        if ($setting) {
            return $setting;
        }

        throw new SettingNotFoundException();
    }

    /**
     * @param string $key
     * @return bool
     */
    public function existByKey(string $key): bool
    {
        return $this->baseQuery()->andWhere(['=', 'setting_key', $key])->exists();
    }


    /**
     * @param $key
     * @param $value
     * @return void
     */
    public function create($key, $value): bool {
        if (false == $this->existByKey($key)) {
            $setting = new ($this->getModel());
            return $this->save($setting, $key, $value);
        }

        return false;
    }

    /**
     * @param $key
     * @param $value
     * @return false
     * @throws \app\components\exception\SettingNotFoundException
     */
    public function update($key, $value): bool
    {
        if (true == $this->existByKey($key)) {
            $setting = $this->getByKey($key);
            return $this->save($setting, $key, $value);
        }

        return false;
    }

    /**
     * @param $key
     * @param $value
     * @return false
     */
    public function updateOrCreate($key, $value): bool {
        try {
            $setting = $this->getByKey($key);
            return $this->save($setting, $key, $value);
        } catch (SettingException $exception) {
            return $this->create($key, $value);
        }
    }

    /**
     * @param \app\models\Setting $setting
     * @param $key
     * @param $value
     * @return void
     */
    protected function save($setting, $key, $value): bool
    {
        $setting->setting_key = $key;
        $setting->setting_value = $value;
        return $setting->save();
    }

    abstract protected function getModel(): string;
    abstract protected function baseQuery(): \yii\db\ActiveQuery;
}
