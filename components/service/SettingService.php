<?php

namespace app\components\service;

use app\models\Setting;

class SettingService extends AbstractSettingService
{
    public function __construct() {}

    /**
     * @return string
     */
    protected function getModel(): string
    {
        return Setting::class;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    protected function baseQuery(): \yii\db\ActiveQuery
    {
        return Setting::find();
    }
}
