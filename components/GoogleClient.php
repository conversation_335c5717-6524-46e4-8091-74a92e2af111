<?php

namespace app\components;

/** @deprecated  */
class GoogleClient
{
/*    public static function getBusinessClient()
    {
        //putenv('GOOGLE_APPLICATION_CREDENTIALS='.\Yii::getAlias('@app').'/libs/google-api-php-client/service-credentials.json');
        $client = new \Google_Client();
        $client->setApplicationName('Foquz Map Reviews');
        //$client->useApplicationDefaultCredentials();
        $client->setAuthConfig(\Yii::getAlias('@app').'/libs/google-api-php-client/credentials-business.json');
        $client->setRedirectUri('https://developers.google.com/oauthplayground');
        $client->addScope("https://www.googleapis.com/auth/business.manage");
        //$client->setSubject('<EMAIL>');
        $client->setAccessType('offline');
        $client->setPrompt("consent");
        $client->setIncludeGrantedScopes(true);

        $tokenPath = \Yii::getAlias('@app').'/libs/google-api-php-client/token-business.json';

        if (file_exists($tokenPath)) {
            $accessToken = json_decode(file_get_contents($tokenPath), true);
            $client->setAccessToken($accessToken);
        }

        if ($client->isAccessTokenExpired()) {
            // Refresh the token if possible, else fetch a new one.
            if ($client->getRefreshToken()) {
                $client->fetchAccessTokenWithRefreshToken($client->getRefreshToken());
            } else {
                // Request authorization from the user.
                $authUrl = $client->createAuthUrl();
                printf("Open the following link in your browser:\n%s\n", $authUrl);
                print 'Enter verification code: ';
                $authCode = trim(fgets(STDIN));

                // Exchange authorization code for an access token.
                $accessToken = $client->fetchAccessTokenWithAuthCode($authCode);
                $client->setAccessToken($accessToken);

                // Check to see if there was an error.
                if (array_key_exists('error', $accessToken)) {
                    throw new Exception(join(', ', $accessToken));
                }
            }
            // Save the token to a file.
            if (!file_exists(dirname($tokenPath))) {
                mkdir(dirname($tokenPath), 0700, true);
            }
            file_put_contents($tokenPath, json_encode($client->getAccessToken()));
            //$client->verifyIdToken();
        }

        $httpClient = $client->authorize();
        return $httpClient;
    }*/
}