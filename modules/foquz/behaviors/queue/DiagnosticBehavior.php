<?php

namespace app\modules\foquz\behaviors\queue;

use Yii;
use yii\queue\JobEvent;
use yii\queue\Queue;

class DiagnosticBehavior extends yii\base\Behavior
{
    public string $file;

    public function events(): array
    {
        return [
            Queue::EVENT_BEFORE_EXEC => 'beforeExecute',
            Queue::EVENT_AFTER_EXEC => 'afterExecute',
            Queue::EVENT_AFTER_ERROR => 'afterExecute',
        ];
    }

    public function beforeExecute(JobEvent $event): void
    {
        $file = Yii::getAlias(Yii::$app->params['queueDiagnosticFile']);
        file_put_contents($file, time());
    }

    public function afterExecute(JobEvent $event): void
    {
        $file = Yii::getAlias(Yii::$app->params['queueDiagnosticFile']);
        file_put_contents($file, '');
    }
}