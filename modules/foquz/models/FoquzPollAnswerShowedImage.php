<?php

declare(strict_types=1);

namespace app\modules\foquz\models;



use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "foquz_poll_answer_showed_image".
 *
 * @property int $id
 * @property int $foquz_poll_answer_id
 * @property int $foquz_question_id
 * @property bool $showed
 *
 * @property FoquzPollAnswer $foquzPollAnswer
 * @property FoquzQuestion $foquzQuestion
 */
class FoquzPollAnswerShowedImage extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'foquz_poll_answer_showed_image';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['foquz_poll_answer_id', 'foquz_question_id'], 'required'],
            [['foquz_poll_answer_id', 'foquz_question_id'], 'integer'],
            [['showed'], 'boolean'],
            [['foquz_poll_answer_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPollAnswer::class, 'targetAttribute' => ['foquz_poll_answer_id' => 'id']],
            [['foquz_question_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestion::class, 'targetAttribute' => ['foquz_question_id' => 'id']],
        ];
    }

    public static function createOrUpdate(int $answerId, int $questionId, bool $showed = true): self
    {
        $model = self::findOne(['foquz_poll_answer_id' => $answerId, 'foquz_question_id' => $questionId]);
        if (!$model) {
            $model = new self();
            $model->foquz_poll_answer_id = $answerId;
            $model->foquz_question_id = $questionId;

        }
        $model->showed = $showed;
        return $model;
    }

    public function fields(): array
    {
        return [
            'showed' => static function ($model) {
                return $model->showed ? 1 : 0;
            }
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'foquz_poll_answer_id' => 'Foquz Poll Answer ID',
            'foquz_question_id' => 'Foquz Question ID',
            'showed' => 'Showed',
        ];
    }

    /**
     * Gets query for [[FoquzPollAnswer]].
     *
     * @return ActiveQuery
     */
    public function getFoquzPollAnswer(): ActiveQuery
    {
        return $this->hasOne(FoquzPollAnswer::class, ['id' => 'foquz_poll_answer_id']);
    }

    /**
     * Gets query for [[FoquzQuestion]].
     *
     * @return ActiveQuery
     */
    public function getFoquzQuestion(): ActiveQuery
    {
        return $this->hasOne(FoquzQuestion::class, ['id' => 'foquz_question_id']);
    }
}
