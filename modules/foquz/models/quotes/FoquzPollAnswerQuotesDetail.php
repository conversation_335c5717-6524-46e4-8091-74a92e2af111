<?php

namespace app\modules\foquz\models\quotes;

use app\models\User;
use app\modules\foquz\models\BaseModel;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "foquz_poll_answer_quotes_detail".
 *
 * @property int $id
 * @property int|null $quote_id Id квоты на ответ
 * @property int|null $group_id Id группы
 * @property int|null $question_id Id вопроса
 * @property int|null $behavior Варианты действий респондента
 * @property array|null $variants Варианты ответов
 * @property string $created_at
 * @property string $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @property int|null $deleted_by
 * @property int|null $position
 *
 * @property User $createdBy
 * @property User $deletedBy
 * @property FoquzPollAnswerQuotesGroup $group
 * @property FoquzQuestion $question
 * @property FoquzPollAnswerQuotes $quote
 * @property User $updatedBy
 */
class FoquzPollAnswerQuotesDetail extends BaseModel
{
    use SoftDelete;


    public const BEHAVIOR_CHOOSE = 1;   // выбрал вариант
    public const BEHAVIOR_DONT_CHOOSE = 2; // не выбрал вариант
    public const BEHAVIOR_SKIP = 3; // пропустил вопрос (когда вопрос необязательный и респондент пропустил через кнопку Далее)
    public const BEHAVIOR_DONT_ASK = 4; // затруднился ответить (респондент пропустил через опцию Пропуск ответа)

    public static function create(int $quoteId, int $questionId, int|null $groupId, int $behavior, array $variants, int $position): self
    {
        $detail = new self();
        $detail->quote_id = $quoteId;
        $detail->question_id = $questionId;
        $detail->group_id = $groupId;
        $detail->behavior = $behavior;
        $detail->variants = $variants;
        $detail->position = $position;

        return $detail;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'foquz_poll_answer_quotes_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['quote_id', 'group_id', 'question_id', 'created_by', 'updated_by', 'deleted_by', 'position'], 'integer'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            ['behavior', 'integer', 'min' => self::BEHAVIOR_CHOOSE, 'max' => self::BEHAVIOR_DONT_ASK],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
            [['deleted_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['deleted_by' => 'id']],
            [['group_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPollAnswerQuotesGroup::class, 'targetAttribute' => ['group_id' => 'id']],
            ['question_id', 'validateQuestionId'],
            ['variants', 'validateVariants'],
            [['quote_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPollAnswerQuotes::class, 'targetAttribute' => ['quote_id' => 'id']],
            [['updated_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['updated_by' => 'id']],
        ];
    }

    public function validateQuestionId($attribute): void
    {
        // вопрос должен принадлежать типу Варианты ответов
        if (!FoquzQuestion::find()
            ->where(['id' => $this->$attribute])
            ->andWhere(['main_question_type' => FoquzQuestion::TYPE_VARIANTS])
            ->exists()) {
            $this->addError($attribute, 'Неправильный тип вопроса');
        }
    }
    public function validateVariants($attribute): void
    {
        if ($this->behavior === self::BEHAVIOR_CHOOSE || $this->behavior === self::BEHAVIOR_DONT_CHOOSE) {
            if (is_array($this->$attribute)) {
                if (empty($this->$attribute)) {
                    $this->addError($attribute, 'Неправильный вариант ответа');
                }
                foreach ($this->$attribute as $item) {
                    if ((int)$item === -1) {
                        continue;
                    }
                    if (!FoquzQuestionDetail::find()
                        ->where(['foquz_question_id' => $this->question_id])
                        ->andWhere(['id' => $item])
                        ->exists()) {
                        $this->addError($attribute, 'Неправильный вариант ответа');
                    }
                }

            } else {
                $this->addError($attribute, 'Неправильный вариант ответа');
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'quote_id' => 'Quote ID',
            'group_id' => 'Group ID',
            'question_id' => 'Question ID',
            'behavior' => 'Behavior',
            'variants' => 'Variants',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'deleted_at' => 'Deleted At',
            'deleted_by' => 'Deleted By',
            'position'   => 'Position',
        ];
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => (new \DateTime('now'))->format('Y-m-d H:i:s'),
            ],
            BlameableBehavior::class,
        ];
    }

    public function fields(): array
    {
        return [
            'id',
            'quote_id',
            'group_id',
            'question_id',
            'behavior',
            'variants',
            'position',
        ];
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return ActiveQuery
     */
    public function getCreatedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[DeletedBy]].
     *
     * @return ActiveQuery
     */
    public function getDeletedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'deleted_by']);
    }

    /**
     * Gets query for [[Group]].
     *
     * @return ActiveQuery
     */
    public function getGroup(): ActiveQuery
    {
        return $this->hasOne(FoquzPollAnswerQuotesGroup::class, ['id' => 'group_id']);
    }

    /**
     * Gets query for [[Question]].
     *
     * @return ActiveQuery
     */
    public function getQuestion(): ActiveQuery
    {
        return $this->hasOne(FoquzQuestion::class, ['id' => 'question_id']);
    }

    /**
     * Gets query for [[Quote]].
     *
     * @return ActiveQuery
     */
    public function getQuote(): ActiveQuery
    {
        return $this->hasOne(FoquzPollAnswerQuotes::class, ['id' => 'quote_id']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return ActiveQuery
     */
    public function getUpdatedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }
}
