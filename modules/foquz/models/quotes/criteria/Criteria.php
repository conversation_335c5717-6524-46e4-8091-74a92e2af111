<?php

declare(strict_types=1);

namespace app\modules\foquz\models\quotes\criteria;

use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;

class Criteria
{
    public int $questionId;
    public int $behavior;

    // варианты ответов заданные в квоте
    // [0 => [0 => 96490]]                  один вариант ответа
    // [0 => [0 => 96490, 1 => 96491]]      два варианта ответа
    // [0 => [0 => 96490, 1 => -1]]         один вариант ответа и свой вариант
    // [0 => []]                            пропуск ответа
    // [0 => [96490], 1 => [0 => 96491] объединение условий для одинакового вопроса
    public array $variants;

    // флаг то что условие сработало
    public bool $checked = false;

    // кол-во ответов с такими же параметрами если условие сработало
    public int $answersCount = 0;

    private AnswerCounter $counter;

    private int $commonLogicOperation;

    public function __construct(AnswerCounter $answerCounter)
    {
        $this->counter = $answerCounter;
    }

    public static function Create(int $questionId, int $behavior, array $variants, $answerCounter, $commonLogicOperation): self
    {
        $criteria = new self($answerCounter);
        $criteria->questionId = $questionId;
        $criteria->behavior = $behavior;
        $criteria->variants[] = $variants;
        $criteria->commonLogicOperation = $commonLogicOperation;


        return $criteria;
    }

    public function addVariants(array $variants): void
    {
        sort($variants);
        $this->variants[] = $variants;
    }

    /**
     * Проверяет условие и ставит флаг checked
     * @param array $answer
     * @return void
     */
    public function check(array $answer): void
    {
        // респондент пропустил ответ через опцию Пропуск ответа
        $skipped = isset($answer['skipped']) && (int)$answer['skipped'] === 1;
        // варианты ответа выбранные респондентом
        $variants = $answer['detail_item'] ?? [];
        // варианты ответа заданные в условии
        $criteriaVariants = $this->variants;
        // респондент выбрал "свой вариант"
        foreach ($variants as $idx => $variant) {
            if ($variant === 'is_self_answer') {
                $variants[$idx] = '-1';
            }
        }
        $this->checked = false;

        sort($variants);
        sort($criteriaVariants);

        // затруднился ответить (респондент пропустил через опцию Пропуск ответа)
        if ($skipped && (count($variants) === 0) && ($this->behavior === FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK)) {
            $this->checked = true;
        }

        // пропустил вопрос (когда вопрос необязательный и респондент пропустил через кнопку Далее)
        if (!$skipped && (count($variants) === 0) && $this->behavior === FoquzPollAnswerQuotesDetail::BEHAVIOR_SKIP) {
            $this->checked = true;
        }

        // выбрал варианты
        if ($this->isSelected($variants, $criteriaVariants) && $this->behavior === FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE) {
            $this->checked = true;
        }

        // не выбрал варианты
        if (!$this->isSelected($variants, $criteriaVariants) && $this->behavior === FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_CHOOSE) {
            $this->checked = true;
        }

        if ($this->checked) {
            $this->answersCount = $this->counter->getAnswersCount($this->questionId, $this->behavior, $variants, $criteriaVariants, $this->commonLogicOperation);
        }
    }

    private function isSelected(array $variants, array $criteriaVariants): bool
    {
        foreach ($criteriaVariants as $idx => $variant) {
            if ($variant === $variants) {
                return true;
            }
        }
        return false;
    }
}