<?php

declare(strict_types=1);

namespace app\modules\foquz\models\quotes\criteria;

use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use yii\caching\CacheInterface;
use yii\db\Expression;
use yii\db\Query;

class AnswerCounter
{
    private int $foquzPollId;
    private CacheInterface $cache;
    private const CACHE_DURATION = 3600 * 24;

    const POLL_STATUSES = ['done'];

    public function __construct(int $foquzPollId)
    {
        $this->foquzPollId = $foquzPollId;
        $this->cache = \Yii::$app->cache;
    }

    public function getAllAnswersCount(): int
    {
        $q = (new Query())
            ->select("count(*)")
            ->from('foquz_poll_answer_item ansitem')
            ->innerJoin("foquz_poll_answer ans", "ans.id = ansitem.foquz_poll_answer_id")
            ->where(['ans.foquz_poll_id' => $this->foquzPollId])
            ->andWhere(['in', 'ans.status',  self::POLL_STATUSES]);
        return $q->scalar();
    }

    /**
     * @param int $questionId
     * @param int $behavior 1-выбрал вариант, 2-не выбрал вариант, 3-пропустил вопрос, 4-затруднился ответить
     * @param array $variants массив вариантов ответа от респондента
     * @param array $criteriaVariants - массив вариантов из условия
     * @param int $logicOperation
     * @return int
     */
    public function getAnswersCount(int $questionId, int $behavior, array $variants, array $criteriaVariants,int $logicOperation): int
    {
        //$pollStatuses = ['done'];

        $jsonContains = []; $selfVariantInAnswers = []; $selfVariantInCriteria = [];
        $jsonVariants = ''; $jsonCriteriaVariants = '{}';$isSelfAnswer = false; $isSelfAnswerInCriteria = false;
        if (count($criteriaVariants) > 1) {
            // [0 => [0 => 96490], 1 => [0 => 96491] объединение условий для одинакового вопроса

            foreach ($criteriaVariants as $idx => $criteriaVariant) {
                $isSelfAnswer = isset($criteriaVariant[0]) && $criteriaVariant[0] === '-1';
                $isSelfAnswerInCriteria = isset($criteriaVariants[0]) && $criteriaVariants[0] === '-1';

                if ($isSelfAnswer) {
                    $selfVariantInAnswers[$idx] = true;
                }
                if ($selfVariantInCriteria) {
                    $selfVariantInCriteria[$idx] = true;
                }

                $criteriaVariant = array_values(array_diff($criteriaVariant, ['-1']));
                $jsonVariants = json_encode($criteriaVariant);
                if (!$jsonVariants) {
                    $jsonVariants = '{}';
                }
                $jsonContains[$idx] = $jsonVariants;
            }

        } else {
            $isSelfAnswer = isset($variants[0]) && $variants[0] === '-1';
            $isSelfAnswerInCriteria = isset($criteriaVariants[0]) && $criteriaVariants[0] === '-1';

            $variants = array_values(array_diff($variants, ['-1']));
            if (is_array($variants) && count($variants)) {
                $jsonVariants = json_encode($variants);
            }
            if (!$jsonVariants) {
                $jsonVariants = '{}';
            }

            if (isset($criteriaVariants[0]) && $criteriaVariants[0] !== '-1') {
                $jsonCriteriaVariants = json_encode($criteriaVariants[0]);
            }
        }

        $lastAnsCountCacheKey = "lastAnsCount:{$this->foquzPollId}:$questionId";
        $answersCountCacheKey = 'answersCount:' . $this->foquzPollId . ':' . $questionId . ':' . $behavior . ':' . json_encode($variants) . ':' . json_encode($criteriaVariants);

        $qBase = (new Query())
            ->select("count(*)")
            ->from('foquz_poll_answer_item ansitem')
            ->innerJoin("foquz_poll_answer ans", "ans.id = ansitem.foquz_poll_answer_id")
            ->where(['ans.foquz_poll_id' => $this->foquzPollId])
            ->andWhere(["ansitem.foquz_question_id" => $questionId])
            ->andWhere(['in', 'ans.status',  self::POLL_STATUSES]);
        $ansCount = $qBase->scalar();

        $lastAnsCount = (int)$this->cache->get($lastAnsCountCacheKey);

        if ($ansCount === $lastAnsCount) {
            $count = $this->cache->get($answersCountCacheKey);
            if ($count !== false) {
                //echo 'count for questionId (cached):' . $questionId. ', behavior:' . $behavior . ': ' . $count . PHP_EOL;
                return $count;
            }
        } else {
            $this->cache->set($lastAnsCountCacheKey, $ansCount, self::CACHE_DURATION);
        }

        $count = $this->process($qBase, $behavior, $isSelfAnswer, $jsonContains, $logicOperation, $jsonVariants, $jsonCriteriaVariants, $isSelfAnswerInCriteria);
        $this->cache->set($answersCountCacheKey, $count, self::CACHE_DURATION);
        //echo 'count for questionId:' . $questionId. ', behavior:' . $behavior . ': ' . $count . PHP_EOL;
        return $count;
    }

    private function process(
        Query $qBase,
        int $behavior,
        bool $isSelfAnswer,
        array $jsonContains,
        int $logicOperation,
        string $jsonVariants,
        string $jsonCriteriaVariants,
        bool $isSelfAnswerInCriteria
    ): int
    {
        switch ($behavior) {
            case FoquzPollAnswerQuotesDetail::BEHAVIOR_CHOOSE:
                if ($isSelfAnswer) {
                    $qBase->andWhere(['ansitem.is_self_variant' => 1]);
                }
                if (count($jsonContains)) {

                    if ($logicOperation === FoquzPollAnswerQuotes::LOGIC_OPERATION_OR) {
                        $countMax = 0;
                        foreach ($jsonContains as $idx => $jsonVariant) {
                            $expr = new Expression("JSON_CONTAINS(JSON_VALUE(`ansitem`.`detail_item`, '$'), '" . $jsonVariant."')");
                            $q = (clone $qBase)->andWhere($expr);
                            if (isset($selfVariantInAnswers[$idx])) {
                                $q->andWhere(['ansitem.is_self_variant' => 1]);
                            }
                            $count = (int)$q->scalar();
                            if ($count > $countMax) {
                                $countMax = $count;
                            }
                        }
                        return $countMax;


                    } else {
                        $countSumm = 0;
                        foreach ($jsonContains as $idx => $jsonVariant) {
                            $expr = new Expression("JSON_CONTAINS(JSON_VALUE(`ansitem`.`detail_item`, '$'), '" . $jsonVariant."')");
                            $q = (clone $qBase)->andWhere($expr);
                            if (isset($selfVariantInAnswers[$idx])) {
                                $q->andWhere(['ansitem.is_self_variant' => 1]);
                            }
                            $countSumm += (int)$q->scalar();
                        }
                        return $countSumm;
                    }
                } else {
                    if ($jsonVariants !== '{}') {
                        $qBase->andWhere("JSON_CONTAINS(JSON_VALUE(`ansitem`.`detail_item`, '$'), '" . $jsonVariants . "')");
                    }
                }

                break;
            case FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_CHOOSE:
                if ($isSelfAnswerInCriteria) {
                    $qBase->andWhere(['ansitem.is_self_variant' => 0]);
                }

                if (count($jsonContains)) {
                    if ($logicOperation === FoquzPollAnswerQuotes::LOGIC_OPERATION_OR) {
                        $countMax = 0;
                        foreach ($jsonContains as $idx => $jsonVariant) {
                            $expr = new Expression("NOT JSON_CONTAINS(JSON_VALUE(`ansitem`.`detail_item`, '$'), '" . $jsonVariant."')");
                            $q = (clone $qBase)->andWhere($expr);

                            if (isset($selfVariantInCriteria[$idx])) {
                                $q->andWhere(['ansitem.is_self_variant' => 0]);
                            }
                            $count = (int)$q->scalar();
                            if ($count > $countMax) {
                                $countMax = $count;
                            }
                        }
                        return $countMax;
                    } else {
                        $countSumm = 0;
                        foreach ($jsonContains as $idx => $jsonVariant) {
                            $expr = new Expression("NOT JSON_CONTAINS(JSON_VALUE(`ansitem`.`detail_item`, '$'), '" . $jsonVariant."')");
                            $q = (clone $qBase)->andWhere($expr);
                            if (isset($selfVariantInCriteria[$idx])) {
                                $q->andWhere(['ansitem.is_self_variant' => 0]);
                            }
                            $countSumm += (int)$q->scalar();
                        }
                        return $countSumm;
                    }
                } else {
                    $qBase->andWhere("NOT JSON_CONTAINS(JSON_VALUE(`ansitem`.`detail_item`, '$'), '" . $jsonCriteriaVariants."')");
                }
                break;
            case FoquzPollAnswerQuotesDetail::BEHAVIOR_SKIP: // пропустил вопрос (когда вопрос необязательный и респондент пропустил через кнопку Далее)
                $qBase->andWhere(['ansitem.skipped' => 0]);
                $qBase->andWhere(['ansitem.detail_item' => null]);
                break;
            case FoquzPollAnswerQuotesDetail::BEHAVIOR_DONT_ASK: // затруднился ответить (респондент пропустил через опцию Пропуск ответа)
                $qBase->andWhere(['ansitem.skipped' => 1]);
                $qBase->andWhere(['ansitem.detail_item' => null]);
                break;
            default:
                throw new \InvalidArgumentException();
        }
        return (int)$qBase->scalar();
    }
}