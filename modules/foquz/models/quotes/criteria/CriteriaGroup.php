<?php

declare(strict_types=1);

namespace app\modules\foquz\models\quotes\criteria;

use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;

class CriteriaGroup
{
    public int $groupId;
    /**
     * @var Criteria[]
     */
    private array $criteria;
    // логика работы условий в группе 0-И, 1-ИЛИ
    private int $logicOperation;
    // флаг все условия группы выполнены
    public bool $checked = false;
    // лимит ответов по квоте
    private int $answersLimit = 0;


    public static function Create(int $groupId, int $logicOperation, int $answersLimit): self
    {
        $group = new self();
        $group->groupId = $groupId;
        $group->logicOperation = $logicOperation;
        $group->answersLimit = $answersLimit;
        return $group;
    }

    public function addCriteria(Criteria $criteria): void
    {
        $this->criteria[] = $criteria;
    }

    public function addCriterias(array $criterias): void
    {
        $this->criteria = $criterias;
    }


    public function check(array $answers): void
    {
        $allChecked = true;
        foreach ($this->criteria as $criteria) {

            // еще не было ответа
            if (!isset($answers[$criteria->questionId])) {
                $allChecked = false;
                continue;
            }

            $criteria->check($answers[$criteria->questionId]);
            $checked = $criteria->checked && ($criteria->answersCount >= $this->answersLimit);

            if ($this->logicOperation === FoquzPollAnswerQuotes::LOGIC_OPERATION_OR) {
                if ($checked) {
                    $this->checked = true;
                    return;
                }
            } else {
                if (!$checked) {
                    $allChecked = false;
                }
            }
        }
        $this->checked = $allChecked;
    }
}