<?php

declare(strict_types=1);


namespace app\modules\foquz\models\quotes;

use Yii;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\db\StaleObjectException;

trait SoftDelete
{
    protected static string $deletedAtAttribute = 'deleted_at';
    protected static string $deletedByAttribute = 'deleted_by';

    public function delete(): bool
    {
        return $this->softDelete();
    }

    public function softDelete(): bool
    {
        $this->{static::$deletedAtAttribute} = date('Y-m-d H:i:s');
        $this->blame();
        return $this->save();
    }

    /**
     * @throws \Throwable
     * @throws StaleObjectException
     */
    public function hardDelete(): false|int
    {
        $this->blame();
        return parent::delete();
    }

    public function restore(): bool
    {
        $this->{static::$deletedAtAttribute} = null;
        $this->{static::$deletedByAttribute} = null;
        return $this->save();
    }

    public function isTrashed(): bool
    {
        return !empty($this->{static::$deletedAtAttribute});
    }

    public static function find(): ActiveQuery
    {
        $q = Yii::createObject(ActiveQuery::class, [get_called_class()]);
        $q->andWhere(['is', static::$deletedAtAttribute, null]);
        return $q;
    }

    /**
     * @throws InvalidConfigException
     */
    public static function findWithTrashed(): ActiveQuery
    {
        return Yii::createObject(ActiveQuery::class, [get_called_class()]);
    }

    private function blame(): void
    {
        $userId = Yii::$app->get('user')->id;
        if ($userId) {
            $this->{static::$deletedByAttribute} = $userId;
        }
    }
}