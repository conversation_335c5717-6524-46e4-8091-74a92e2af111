<?php

namespace app\modules\foquz\models\quotes;

use app\models\User;
use app\modules\foquz\exceptions\ValidationException;
use app\modules\foquz\models\BaseModel;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSetting;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;
use yii\web\NotFoundHttpException;

/**
 * This is the model class for table "foquz_poll_answer_quotes".
 *
 * @property int $id
 * @property int $link_quote_id Id квоты на ссылку
 * @property int|null $answers_limit Лимит количества ответов
 * @property int $logic_operation Логика работы условий/групп
 * @property string $created_at
 * @property string $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @property int|null $deleted_by
 * @property int|null $end_screen Конечный экран
 *
 * @property User $createdBy
 * @property User $deletedBy
 * @property FoquzPollAnswerQuotesDetail[] $foquzPollAnswerQuotesDetails
 * @property FoquzPollAnswerQuotesDetail[] $getFoquzPollAnswerQuotesDetailsAll
 * @property FoquzPollAnswerQuotesGroup[] $foquzPollAnswerQuotesGroups
 * @property FoquzPollLinkQuotes $linkQuote
 * @property User $updatedBy
 */
class FoquzPollAnswerQuotes extends BaseModel
{
    use SoftDelete;

    public const LOGIC_OPERATION_AND = 0;
    public const LOGIC_OPERATION_OR = 1;

    public static function create(int $linkQuoteId, int|null $answerLimit, int $logicOperation, int|null $end_screen): self
    {
        $quote = new self();
        $quote->link_quote_id = $linkQuoteId;
        $quote->answers_limit = $answerLimit;
        $quote->logic_operation = $logicOperation;
        $quote->end_screen = $end_screen;
        return $quote;
    }


    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'foquz_poll_answer_quotes';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['link_quote_id'], 'required'],
            [['link_quote_id', 'logic_operation', 'created_by', 'updated_by', 'deleted_by', 'end_screen'], 'integer'],
            [['answers_limit'],'integer', 'max' => 999999999],
            [['logic_operation'], 'in', 'range' => [self::LOGIC_OPERATION_AND, self::LOGIC_OPERATION_OR]],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
            [['deleted_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['deleted_by' => 'id']],
            [['link_quote_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPollLinkQuotes::class, 'targetAttribute' => ['link_quote_id' => 'id']],
            [['updated_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['updated_by' => 'id']],
            [['end_screen'], 'validateEndScreen'],
        ];
    }

    public function validateEndScreen($attribute)
    {
        $val = (int)$this->$attribute;

        if ($val === 0 || $val === -1) {
            if ($val === 0) {
                $this->end_screen = null;
            }
            return;
        }
        try {

            $linkQuote = FoquzPollLinkQuotes::findOne($this->link_quote_id);
            if (!$linkQuote) {
                throw new \Exception('Неверное значение');
            }
            $hasVariant = FoquzQuestionIntermediateBlockSetting::find()
                ->joinWith('question')
                ->where(['foquz_question_intermediate_block_setting.question_id' => $val])
                ->andWhere(['foquz_question.poll_id' => $linkQuote->poll_id])
                ->exists();
            if (!$hasVariant) {
                throw new \Exception('Неверное значение');
            }

        } catch (\Exception $ex) {
            $this->addError($attribute, $ex->getMessage());
        }
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'link_quote_id' => 'Link Quote ID',
            'answers_limit' => 'Answers Limit',
            'logic_operation' => 'Logic Operation',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'deleted_at' => 'Deleted At',
            'deleted_by' => 'Deleted By',
            'end_screen' => 'End Screen',
        ];
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => (new \DateTime('now'))->format('Y-m-d H:i:s'),
            ],
            BlameableBehavior::class,
        ];
    }

    public function fields(): array
    {
        return [
            'id',
            'link_quote_id',
            'answers_limit',
            'logic_operation',
            'groups' => 'foquzPollAnswerQuotesGroups',
            'criteria' => 'foquzPollAnswerQuotesDetails',
            'end_screen',
        ];
    }


    /**
     * Обработка массива с квотами на ответы
     *
     * @param array $answerQuotesData
     * @param int $linkQuoteId
     * @return void
     * @throws NotFoundHttpException
     * @throws ValidationException
     * @throws \yii\db\Exception
     */
    public static function saveAnswerQuotes(array $answerQuotesData, int $linkQuoteId): void
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $touchedIds = [];
            foreach ($answerQuotesData as $answerQuoteData) {
                $answerQuoteId = $answerQuoteData['id'] ?? null;

                if ($answerQuoteId) {
                    $answerQuote = FoquzPollAnswerQuotes::findOne($answerQuoteId);
                    if (!$answerQuote) {
                        throw new NotFoundHttpException('Квота ' . $answerQuoteId . ' не найдена');
                    }
                    $answerQuote->editAnswerQuote($answerQuoteData);
                    $touchedIds[] = $answerQuoteId;
                } else {
                    $touchedIds[] = FoquzPollAnswerQuotes::createAnswerQuote($answerQuoteData, $linkQuoteId);
                }
            }

            // delete untouched quotes
            $answerQuotesToDelete = FoquzPollAnswerQuotes::find()
                ->where(['link_quote_id' => $linkQuoteId])
                ->andWhere(['not in', 'id', $touchedIds])
                ->all();
            /** @var FoquzPollAnswerQuotes $item */
            foreach ($answerQuotesToDelete as $item) {
                $item->deleteWithRelations();
            }
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    public static function allowCreateQuote(array $questions): bool
    {
        $allowCreateQuote = false;
        foreach ($questions as $question) {
            if ((int)$question->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                $allowCreateQuote = true;
                break;
            }
        }
        return $allowCreateQuote;
    }

    private function editAnswerQuote(array $data): void
    {
        $this->answers_limit = $data['answers_limit'];
        $this->logic_operation = $data['logic_operation'];
        $this->end_screen = $data['end_screen'];
        if (!$this->save()) {
            throw ValidationException::make($this->getFirstErrors());
        }

        $touchedGroups = [];
        $groups = $data['groups'] ?? [];
        foreach ($groups as $group) {
            $groupId = $group['id'];
            if ($groupId) {
                $quoteGroup = FoquzPollAnswerQuotesGroup::findOne($groupId);
                if (!$quoteGroup) {
                    throw ValidationException::make(['groups' => 'Группа не найдена']);
                }
                $quoteGroup->logic_operation = $group['logic_operation'];
                $quoteGroup->position = $group['position'] ?? 0;
                if (!$quoteGroup->save()) {
                    throw new \Exception(implode(', ', $quoteGroup->getFirstErrors()));
                }
                $touchedGroups[] = $groupId;

                $touchedGroupCriteria = [];
                $criterias = $group['criteria'] ?? [];
                foreach ($criterias as $criteria) {
                    $criteriaId = $criteria['id'];
                    if ($criteriaId) {
                        $quoteCriteria = FoquzPollAnswerQuotesDetail::findOne($criteriaId);
                        if (!$quoteCriteria) {
                            throw ValidationException::make(['criteria' => 'Условие ' . $criteriaId . ' не найдено']);
                        }
                        $quoteCriteria->question_id = $criteria['question_id'];
                        $quoteCriteria->behavior = $criteria['behavior'];
                        $quoteCriteria->variants = $criteria['variants'] ?? [];
                        $quoteCriteria->position = $criteria['position'] ?? 0;
                        if (!$quoteCriteria->save()) {
                            throw new \Exception(implode(', ', $quoteCriteria->getFirstErrors()));
                        }

                        $touchedGroupCriteria[] = $criteriaId;
                    } else {
                        $quoteCriteria = FoquzPollAnswerQuotesDetail::create(
                            quoteId: $data['id'], questionId: $criteria['question_id'],
                            groupId: $groupId, behavior: $criteria['behavior'],
                            variants: $criteria['variants'] ?? [],
                            position: $criteria['position'] ?? 0,

                        );
                        if (!$quoteCriteria->save()) {
                            throw ValidationException::make($quoteCriteria->getFirstErrors());
                        }
                        $touchedGroupCriteria[] = $quoteCriteria->id;
                    }
                }

                // delete untouched criteria in group
                $groupCriteriaToDelete = FoquzPollAnswerQuotesDetail::find()
                    ->where(['quote_id' => $data['id']])
                    ->andWhere(['group_id' => $groupId])
                    ->andWhere(['not in', 'id', $touchedGroupCriteria])
                    ->all();
                foreach ($groupCriteriaToDelete as $item) {
                    $item->delete();
                }


            } else {

                $quoteGroup = FoquzPollAnswerQuotesGroup::create($data['id'], $group['logic_operation'], $group['position'] ?? 0);
                if (!$quoteGroup->save()) {
                    throw ValidationException::make($quoteGroup->getFirstErrors());
                }
                $criterias = $group['criteria'] ?? [];
                foreach ($criterias as $criteria) {
                    $quoteCriteria = FoquzPollAnswerQuotesDetail::create(
                        quoteId: $data['id'], questionId: $criteria['question_id'],
                        groupId: $quoteGroup->id, behavior: $criteria['behavior'],
                        variants: $criteria['variants'] ?? [],
                        position: $criteria['position'] ?? 0,

                    );
                    if (!$quoteCriteria->save()) {
                        throw ValidationException::make($quoteCriteria->getFirstErrors());
                    }
                    $touchedGroupCriteria[] = $quoteCriteria->id;
                }
                $touchedGroups[] = $quoteGroup->id;
            }
        }

        // delete untouched groups
        $quoteGroupsToDelete = FoquzPollAnswerQuotesGroup::find()
            ->where(['quote_id' => $data['id']])
            ->andWhere(['not in', 'id', $touchedGroups])
            ->all();
        foreach ($quoteGroupsToDelete as $item) {
            $item->deleteWithRelations();
        }

        $touchedCriteria = [];
        $criterias = $data['criteria'] ?? [];
        foreach ($criterias as $criteria) {
            $criteriaId = $criteria['id'];
            if ($criteriaId) {
                $quoteCriteria = FoquzPollAnswerQuotesDetail::findOne($criteriaId);
                if (!$quoteCriteria) {
                    throw ValidationException::make(['criteria' => 'Условие ' . $criteriaId . ' не найдено']);
                }
                $quoteCriteria->question_id = $criteria['question_id'];
                $quoteCriteria->behavior = $criteria['behavior'];
                $quoteCriteria->variants = $criteria['variants'] ?? [];
                if (!$quoteCriteria->save()) {
                    throw ValidationException::make($quoteCriteria->getFirstErrors());
                }
                $touchedCriteria[] = $criteriaId;
            } else {

                $quoteCriteria = FoquzPollAnswerQuotesDetail::create(
                    quoteId: $data['id'], questionId: $criteria['question_id'],
                    groupId: null, behavior: $criteria['behavior'],
                    variants: $criteria['variants'] ?? [],
                    position: $criteria['position'] ?? 0,
                );
                if (!$quoteCriteria->save()) {
                    throw ValidationException::make($quoteCriteria->getFirstErrors());
                }
                $touchedCriteria[] = $quoteCriteria->id;
            }
        }
        // delete untouched criteria
        $answerCriteriaToDelete = FoquzPollAnswerQuotesDetail::find()
            ->where(['quote_id' => $data['id']])
            ->andWhere(['is', 'group_id', null])
            ->andWhere(['not in', 'id', $touchedCriteria])
            ->all();
        foreach ($answerCriteriaToDelete as $item) {
            $item->delete();
        }
    }

    private static function createAnswerQuote(array $data, int $linkQuoteId): int
    {
        $answerQuote = self::create(
            linkQuoteId: $linkQuoteId,
            answerLimit: $data['answers_limit'],
            logicOperation: $data['logic_operation'],
            end_screen: $data['end_screen']);
        if (!$answerQuote->save()) {
            throw ValidationException::make($answerQuote->getFirstErrors());
        }

        $groups = $data['groups'] ?? [];
        foreach ($groups as $group) {
            $quoteGroup = FoquzPollAnswerQuotesGroup::create(
                $answerQuote->id,
                $group['logic_operation'],
                $group['position'] ?? 0
            );
            if (!$quoteGroup->save()) {
                throw ValidationException::make($quoteGroup->getFirstErrors());
            }
            foreach ($group['criteria'] as $criteria) {

                $quoteCriteria = FoquzPollAnswerQuotesDetail::create(
                    quoteId: $answerQuote->id, questionId: $criteria['question_id'],
                    groupId: $quoteGroup->id, behavior: $criteria['behavior'],
                    variants: $criteria['variants'] ?? [],
                    position: $criteria['position'] ?? 0,
                );
                if (!$quoteCriteria->save()) {
                    throw ValidationException::make($quoteCriteria->getFirstErrors());
                }
            }
        }
        $criterias = $data['criteria'] ?? [];
        foreach ($criterias as $criteria) {
            $quoteCriteria = FoquzPollAnswerQuotesDetail::create(
                quoteId: $answerQuote->id, questionId: $criteria['question_id'],
                groupId: null, behavior: $criteria['behavior'],
                variants: $criteria['variants'] ?? [],
                position: $criteria['position'] ?? 0,
            );
            if (!$quoteCriteria->save()) {
                throw ValidationException::make($quoteCriteria->getFirstErrors());
            }
        }

        return $answerQuote->id;
    }

    private function deleteWithRelations(): void
    {
        $groups = FoquzPollAnswerQuotesGroup::find()->where(['quote_id' => $this->id])->all();
        foreach ($groups as $group) {
            $group->delete();
        }
        $criteria = FoquzPollAnswerQuotesDetail::find()->where(['quote_id' => $this->id])->all();
        foreach ($criteria as $item) {
            $item->delete();
        }
        $this->delete();
    }


    /**
     * Gets query for [[CreatedBy]].
     *
     * @return ActiveQuery
     */
    public function getCreatedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[DeletedBy]].
     *
     * @return ActiveQuery
     */
    public function getDeletedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'deleted_by']);
    }

    /**
     * Gets query for [[FoquzPollAnswerQuotesDetails]].
     *
     * @return ActiveQuery
     */
    public function getFoquzPollAnswerQuotesDetails(): ActiveQuery
    {
        return $this->hasMany(FoquzPollAnswerQuotesDetail::class, ['quote_id' => 'id'])
            ->andWhere(['is', 'group_id', null]);
    }

    public function getFoquzPollAnswerQuotesDetailsAll(): ActiveQuery
    {
        return $this->hasMany(FoquzPollAnswerQuotesDetail::class, ['quote_id' => 'id']);
    }

    /**
     * Gets query for [[FoquzPollAnswerQuotesGroups]].
     *
     * @return ActiveQuery
     */
    public function getFoquzPollAnswerQuotesGroups(): ActiveQuery
    {
        return $this->hasMany(FoquzPollAnswerQuotesGroup::class, ['quote_id' => 'id']);
    }

    /**
     * Gets query for [[LinkQuote]].
     *
     * @return ActiveQuery
     */
    public function getLinkQuote(): ActiveQuery
    {
        return $this->hasOne(FoquzPollLinkQuotes::class, ['id' => 'link_quote_id']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return ActiveQuery
     */
    public function getUpdatedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }
}
