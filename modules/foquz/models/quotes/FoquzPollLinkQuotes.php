<?php

namespace app\modules\foquz\models\quotes;

use app\models\Filial;
use app\modules\foquz\models\BaseModel;
use app\modules\foquz\models\FilialPollKey;
use app\modules\foquz\models\FoquzPoll;
use DateTimeZone;
use InvalidArgumentException;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Exception;


/**
 * This is the model class for table "foquz_poll_link_quotes".
 *
 * @property int $id
 * @property int|null $parent_id
 * @property int|null $poll_id
 * @property string $key Ключ ссылки
 * @property string $name Название ссылки
 * @property int|null $limit Максимум ответов по ссылке
 * @property string|null $datetime_end Дата окончания приёма ответов по ссылке
 * @property boolean $active Активность ссылки
 * @property string $created_at
 * @property string $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property-read FoquzPoll $foquzPoll
 * @property-read FilialPollKey $filialPollKey
 * @property-read FoquzPollAnswerQuotes[] $answerQuotes квоты на ответы
 */
class FoquzPollLinkQuotes extends BaseModel
{
    public const LINK_ACTIVE = 1;
    public const LINK_INACTIVE = 0;

    public const QUOTE_INACTIVE = 'QUOTE_INACTIVE';
    public const QUOTE_TIME_IS_OVER = 'QUOTE_TIME_IS_OVER';
    public const QUOTE_LIMIT_IS_OVER = 'QUOTE_LIMIT_IS_OVER';

    /**
     * Создание квоты для ссылки без филиала
     * @param int $pollId
     * @param int $userId
     * @param int|null $limit
     * @param int|null $datetimeEnd
     * @return self
     */
    public static function createQuote(
        int $pollId,
        int $userId,
        int|null $limit = null,
        int|null $datetimeEnd = null,
        string|null $key = null
    ): self
    {
        $quote = new self();
        $quote->poll_id = $pollId;
        $quote->key = empty($key) ? uniqid('F') : $key;
        $quote->name = 'Ссылка ' . (self::getTotal($pollId) + 1);
        $quote->limit = $limit;
        $quote->datetime_end = $datetimeEnd;
        $quote->created_by = $userId;
        $quote->updated_by = $userId;
        return $quote;
    }

    /**
     * Создание квоты для ссылки с филиалом
     * @param int $pollId
     * @param string $filialName
     * @param int $userId
     * @param int $parentId
     * @param int|null $limit
     * @param int|null $datetimeEnd
     * @return self
     */
    public static function createFilialQuote(
        int $pollId,
        string $filialName,
        int $userId,
        int $parentId,
        int|null $limit = null,
        int|null $datetimeEnd = null
    ): self
    {
        $quote = new self();
        $quote->parent_id = $parentId;
        $quote->poll_id = $pollId;
        $quote->key = null;
        $quote->name = 'Ссылка для филиала ' . $filialName;
        $quote->limit = $limit;
        $quote->datetime_end = $datetimeEnd;
        $quote->created_by = $userId;
        $quote->updated_by = $userId;
        return $quote;
    }

    public static function createFilialsQuotes($poll, int $quoteId): void
    {
        if(!$poll->is_auto && Filial::find()->where(['company_id' => $poll->company_id])->count() > 0) {

            $filials = Filial::find()->where(['company_id' => $poll->company_id])->all();
            foreach($filials as $filial) {
                $filialPollKey = FilialPollKey::find()
                    ->where(['foquz_poll_id' => $poll->id, 'filial_id' => $filial->id, 'quote_id' => $quoteId])
                    ->exists();
                if (!$filialPollKey) {
                    $filialPollKey = new FilialPollKey([
                        'foquz_poll_id' => $poll->id,
                        'filial_id' => $filial->id,
                        'key' => uniqid('F'.$poll->id.$filial->id),
                        'quote_id' => $quoteId, // используем квоту основной ссылки
                    ]);
                    $filialPollKey->save();
                }
            }
        }
    }


    public static function getTotal(int $pollId): int
    {
        return self::find()->where(['poll_id' => $pollId])->andWhere(['is not','key', null])->count();
    }


    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%foquz_poll_link_quotes}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['poll_id', 'updated_by', 'created_by'], 'integer'],
            [['parent_id',], 'integer'],
            [['name'], 'filter', 'filter' => 'trim'],
            [['name',], 'string', 'max' => 255],
            [['key',], 'string', 'max' => 255],
            [['limit',], 'integer'],
            [['datetime_end'], 'safe'],
            [['active'], 'boolean'],
            [['poll_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPoll::class, 'targetAttribute' => ['poll_id' => 'id']],
            [['parent_id'], 'exist', 'skipOnError' => true, 'targetClass' => self::class, 'targetAttribute' => ['parent_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'poll_id' => 'Foquz Poll ID',
            'name' => 'Name',
            'limit' => 'Limit',
            'datetime_end' => 'Datetime End',
            'active' => 'Active',
        ];
    }

    /**
     * @throws Exception
     */
    public function setActive(int $userId): bool
    {
        $this->active = self::LINK_ACTIVE;
        $this->updated_by = $userId;
        return $this->save();
    }

    /**
     * @throws Exception
     */
    public function setInactive(int $userId): bool
    {
        $this->active = self::LINK_INACTIVE;
        $this->updated_by = $userId;
        return $this->save();
    }

    /**
     * @throws Exception
     */
    public function edit(int $limit, string|null $datetimeEnd, int $userId): bool
    {
        if ($datetimeEnd) {
            $localDate = new \DateTime($datetimeEnd);
            $localDate->setTimezone(new DateTimeZone(Yii::$app->timeZone));
            $datetimeEnd = $localDate->format('Y-m-d H:i:00');
        }

        $this->limit = ($limit > 0) ? $limit : null;
        $this->datetime_end = $datetimeEnd;
        $this->updated_by = $userId;
        return $this->save();
    }

    /**
     * @throws Exception
     */
    public function editName(string $name, int $userId): bool
    {
        $this->name = $name;
        $this->updated_by = $userId;
        return $this->save();
    }

    public static function findQuote(int $quoteId, int $pollId): array|ActiveRecord
    {
        $quote = self::find()->where(['id' => $quoteId, 'poll_id' => $pollId])->one();
        if (!$quote) {
            throw new InvalidArgumentException('Не найдена квота');
        }
        return $quote;
    }

    /**
     * Получить квоту по ключу
     * @param string $key
     * @param int $pollId
     * @return self|null
     */
    public static function getQuoteByKey(string $key, int $pollId): self|null
    {
        $quote = self::findOne(['key' => $key, 'poll_id' => $pollId]);
        if (!$quote) {
            $filialPollKey = FilialPollKey::find()->where(['key' => $key, 'foquz_poll_id' => $pollId])->one();
            if ($filialPollKey) {
                $quote = $filialPollKey->quote;
            }
        }
        return $quote;
    }

    public function getFoquzPoll(): ActiveQuery
    {
        return $this->hasOne(FoquzPoll::class, ['id' => 'poll_id']);
    }

    public function getFilialPollKey(): ActiveQuery
    {
        return $this->hasMany(FilialPollKey::class, ['quote_id' => 'id']);
    }

    public function getAnswerQuotes(): ActiveQuery
    {
        return $this->hasMany(FoquzPollAnswerQuotes::class, ['link_quote_id' => 'id']);
    }
}