<?php

namespace app\modules\foquz\models\quotes;

use app\models\User;
use app\modules\foquz\models\BaseModel;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use InvalidArgumentException;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "foquz_poll_answer_quotes_group".
 *
 * @property int $id
 * @property int|null $quote_id Id квоты на ответ
 * @property int $logic_operation Логика работы условий в группе
 * @property string $created_at
 * @property string $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @property int|null $deleted_by
 * @property int|null $position
 *
 * @property User $createdBy
 * @property User $deletedBy
 * @property FoquzPollAnswerQuotesDetail[] $foquzPollAnswerQuotesDetails
 * @property FoquzPollAnswerQuotes $quote
 * @property User $updatedBy
 */
class FoquzPollAnswerQuotesGroup extends BaseModel
{
    use SoftDelete;

    public static function create(int $quoteId, int $logicOperation, int $position): self
    {
        $group = new self();
        $group->quote_id = $quoteId;
        $group->logic_operation = $logicOperation;
        $group->position = $position;

        return $group;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'foquz_poll_answer_quotes_group';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['quote_id', 'logic_operation', 'created_by', 'updated_by', 'deleted_by', 'position'], 'integer'],
            [['logic_operation'], 'in', 'range' => [FoquzPollAnswerQuotes::LOGIC_OPERATION_AND, FoquzPollAnswerQuotes::LOGIC_OPERATION_OR]],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
            [['deleted_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['deleted_by' => 'id']],
            [['quote_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPollAnswerQuotes::class, 'targetAttribute' => ['quote_id' => 'id']],
            [['updated_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['updated_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'quote_id' => 'Quote ID',
            'logic_operation' => 'Logic Operation',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'deleted_at' => 'Deleted At',
            'deleted_by' => 'Deleted By',
            'position'   => 'Position',
        ];
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => (new \DateTime('now'))->format('Y-m-d H:i:s'),
            ],
            BlameableBehavior::class,
        ];
    }

    public function fields(): array
    {
        return [
            'id',
            'quote_id',
            'logic_operation',
            'criteria' => 'foquzPollAnswerQuotesDetails',
            'position',
        ];
    }

    public static function findGroup(int $quoteId, int $groupId): array|\yii\db\ActiveRecord
    {
        $group = self::find()->where(['id' => $groupId, 'quote_id' => $quoteId])->one();
        if (!$group) {
            throw new InvalidArgumentException('Не найдена группа условий');
        }
        return $group;
    }

    public function deleteWithRelations(): void
    {
        $criteria = FoquzPollAnswerQuotesDetail::find()
            ->where(['quote_id' => $this->quote_id])
            ->andWhere(['group_id' => $this->id])
            ->all();
        foreach ($criteria as $item) {
            $item->delete();
        }
        $this->delete();
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return ActiveQuery
     */
    public function getCreatedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[DeletedBy]].
     *
     * @return ActiveQuery
     */
    public function getDeletedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'deleted_by']);
    }

    /**
     * Gets query for [[FoquzPollAnswerQuotesDetails]].
     *
     * @return ActiveQuery
     */
    public function getFoquzPollAnswerQuotesDetails(): ActiveQuery
    {
        return $this->hasMany(FoquzPollAnswerQuotesDetail::class, ['group_id' => 'id']);
    }

    /**
     * Gets query for [[Quote]].
     *
     * @return ActiveQuery
     */
    public function getQuote(): ActiveQuery
    {
        return $this->hasOne(FoquzPollAnswerQuotes::class, ['id' => 'quote_id']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return ActiveQuery
     */
    public function getUpdatedBy(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }
}
