<?php

namespace app\modules\foquz\models;

use Yii;

/**
 * This is the model class for table "foquz_question_first_click_area_lang".
 *
 * @property int $id
 * @property int $setting_id
 * @property int $poll_lang_id
 * @property string|null $name
 *
 * @property PollLang $pollLang
 * @property FoquzQuestionFirstClickArea $setting
 */
class FoquzQuestionFirstClickAreaLang extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'foquz_question_first_click_area_lang';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['setting_id', 'poll_lang_id'], 'required'],
            [['setting_id', 'poll_lang_id'], 'integer'],
            [['name'], 'string', 'max' => 255],
            [['poll_lang_id'], 'exist', 'skipOnError' => true, 'targetClass' => PollLang::class, 'targetAttribute' => ['poll_lang_id' => 'id']],
            [['setting_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestionFirstClickArea::class, 'targetAttribute' => ['setting_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'setting_id' => 'Setting ID',
            'poll_lang_id' => 'Poll Lang ID',
            'name' => 'Name',
        ];
    }

    public static function createOrUpdate(int $settingId, int $pollLangId, string $name): self
    {
        $m = self::findOne([
            'setting_id' => $settingId,
            'poll_lang_id' => $pollLangId
        ]);
        if (!$m) {
            $m = new self();
            $m->setting_id = $settingId;
            $m->poll_lang_id = $pollLangId;
        }
        $m->name = $name;

        return $m;
    }

    /**
     * Gets query for [[PollLang]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPollLang()
    {
        return $this->hasOne(PollLang::class, ['id' => 'poll_lang_id']);
    }

    /**
     * Gets query for [[Setting]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSetting()
    {
        return $this->hasOne(FoquzQuestionFirstClickArea::class, ['id' => 'setting_id']);
    }
}
