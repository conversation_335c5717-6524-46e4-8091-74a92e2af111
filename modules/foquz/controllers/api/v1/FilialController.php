<?php

namespace app\modules\foquz\controllers\api\v1;

use app\modules\foquz\controllers\api\ApiController;
use app\modules\foquz\services\api\dictionaries\ImportFilialService;
use app\modules\foquz\services\ValidateException;
use Yii;
use yii\filters\auth\QueryParamAuth;
use yii\filters\ContentNegotiator;
use yii\filters\VerbFilter;
use yii\web\Response;

class FilialController extends ApiController
{
    public $serializer = [
        'class'              => 'yii\rest\Serializer',
        'collectionEnvelope' => 'items',
    ];

    public function behaviors(): array
    {
        $behaviors = parent::behaviors();
        $behaviors['authenticator'] = [
            'class' => QueryParamAuth::class
        ];
        $behaviors['verbs'] = [
            'class'   => VerbFilter::class,
            'actions' => [
                'update' => ['POST']
            ]
        ];
        $behaviors['contentNegotiator'] = [
            'class'   => ContentNegotiator::class,
            'formats' => [
                'application/json' => Response::FORMAT_JSON,
            ],
        ];
        return $behaviors;
    }

    /**
     * @OA\Post (
     *     path="/foquz/api/v1/filial/update",
     *     tags={"Филиалы"},
     *     summary="Обновление справочника филиалов",
     *     security={ {"api_key": {}} },
     *     @OA\Parameter(
     *         name="access-token",
     *         in="query",
     *         description="Токен доступа",
     *         required=true,
     *         example="vZGkGCGTTDkxTlv",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             encoding="text/plain",
     *             @OA\Schema(
     *                  @OA\Property(
     *                      property="filial",
     *                      description="Поля с данными филиала. Кроме поля «Названия филиала» все поля необязательные. Если задано значение поля «Внешний ID» и найден филиал с таким значением, то поле «Название филиала» необязательно - в этом случае значение «Название филиала» останется прежним.",
     *                      type="array",
     *                      @OA\Items(
     *                          anyOf={@OA\Schema(
     *                              type="object",
     *                              title="Массив из полей филиала",
     *                              required={"name"},
     *                              @OA\Property(
     *                                  property="name",
     *                                  description="Название филиала",
     *                                  type="string",
     *                                  example="Филиал 1",
     *                              ),
     *                              @OA\Property(
     *                                  property="category",
     *                                  description="Категория филиала",
     *                                  type="string",
     *                                  example="Москва",
     *                              ),
     *                              @OA\Property(
     *                                  property="address",
     *                                  description="Адрес филиала",
     *                                  type="string",
     *                                  example="ул. Ленина, дом 5",
     *                              ),
     *                              @OA\Property(
     *                                  property="add_param1",
     *                                  description="Дополнительный параметр 1",
     *                                  type="string",
     *                                  example="123",
     *                              ),
     *                              @OA\Property(
     *                                  property="add_param2",
     *                                  description="Дополнительный параметр 2",
     *                                  type="string",
     *                                  example="123",
     *                              ),
     *                              @OA\Property(
     *                                  property="add_param3",
     *                                  description="Дополнительный параметр 3",
     *                                  type="string",
     *                                  example="123",
     *                              ),
     *                              @OA\Property(
     *                                  property="external_id",
     *                                  description="Внешний ID",
     *                                  type="string",
     *                                  example="151-1",
     *                              ),
     *                          )}
     *                      )
     *                  ),
     *             ),
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success",
     *         @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     *                   type="object",
     *                      @OA\Property(
     *                      description="Результат отправки",
     *                      property="success",
     *                      type="boolean",
     *                      example=true,
     *                      enum={true},
     *                  ),
     *              ),
     *        )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Не авторизован",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Ошибка в запросе",
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  type="array",
     *                  @OA\Items(oneOf={
     *                      @OA\Schema(
     *                          type="object",
     *                          @OA\Property(
     *                              property="success",
     *                              description="Результат отправки",
     *                              type="boolean",
     *                              example=false,
     *                              enum={false},
     *                          ),
     *                          @OA\Property(
     *                              property="errors",
     *                              description="Ошибки",
     *                              type="array",
     *                              example={"name":"У филиала отсутствует название"},
     *                              @OA\Items(oneOf={
     *                                  @OA\Schema(
     *                                      type="object",
     *                                      @OA\AdditionalProperties(
     *                                          type="string",
     *                                          title="Название поля и ошибка",
     *                                      ),
     *                                  ),
     *                              }),
     *                          ),
     *                      ),
     *                  }),
     *              ),
     *          ),
     *      ),
     *  )
     */
    public function actionUpdate()
    {
        $importFilialService = new ImportFilialService(null, Yii::$app->user->identity->company->id);
        $filial = $importFilialService->setDefaultFields(\Yii::$app->request->post('filial'));

        try {
            if ($importFilialService->isNonValidName($filial['external_id'], $filial['name'])) {
                throw ValidateException::make('name', 'У филиала отсутствует название');
            }
            if ($filial['external_id'] === 'null') {
                throw ValidateException::make('external_id', 'Внешний ID не может быть null');
            }
            if (!$importFilialService->isUniqueCrmId($filial['external_id'])) {
                throw ValidateException::make('external_id', 'Неуникальный внешний ID');
            }

            $importFilialService->addOrUpdateOneFilial(0, $filial);
            return $this->response(200, [
                'success' => true,
            ]);
        } catch (ValidateException $ex) {
            return $this->response(400, [
                'success' => false,
                'errors'  => $ex->getError()
            ]);
        } catch (\Throwable $ex) {
            return $this->response(400, [
                'success' => false,
                'errors'  => $ex->getMessage()
            ]);
        }
    }
}