<?php

namespace app\modules\foquz\queue;

use yii\base\BaseObject;
use yii\queue\JobInterface;

/**
 * Класс-воркер для тестирования работы очередей. Запрашивает переданную ссылку. Используйте http://ident.me/ - возвращает IP адрес и всё.
 */
class TestJob extends BaseObject implements JobInterface
{
    public $url = 'http://ident.me/';

    public function execute($queue)
    {
        echo 'Start work' . PHP_EOL;
        $context = stream_context_create([
            'http' => [
                'timeout' => 5 // Timeout in seconds
            ]
        ]);

        $data = @file_get_contents($this->url, false, $context);
        if ($data === false) {
            echo 'Ошибка при получении данных по ссылке' . $this->url . PHP_EOL;
        } else {
            echo 'Данные по ссылке $this->url: ' . $data . PHP_EOL;
        }
    }
}
