<?php

namespace app\modules\foquz\services;

use app\models\User;
use app\modules\foquz\models\FilialPollKey;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollShortLink;
use DateTime;
use DateTimeZone;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\IdentityInterface;

class LinkService
{
    public function __construct(private FoquzPoll $poll)
    {}


    public function grabLinkItems(User $user, int $linkQuoteId = 0): array
    {
        $items = [];
        foreach ($this->poll->quotes as $quote) {
            if ($linkQuoteId > 0 && $quote->id != $linkQuoteId) {
                continue;
            }

            $dateTimeEnd = null;
            if (!is_null($quote->datetime_end)) {
                $date = new DateTime($quote->datetime_end);
                $date->setTimezone(new DateTimeZone(Yii::$app->timeZone));
                $dateTimeEnd = Yii::$app->formatter->asDatetime($date, 'php:Y-m-d\TH:i:sP');
            }

            $items[] = [
                'quote_id' => $quote->id,
                'filial_id' => null,
                'filial_name' => 'Без филиала',
                'filial_category_id' => null,
                'key' => $quote->key,
                'link' => Url::to(['/foquz/default/anonymous', 'id' => $quote->key], 'https'),
                'qr' => Url::to(['/foquz/foquz-poll/qr', 'v' => Url::to(['/foquz/default/anonymous', 'id' => $quote->key], true), 'label' => $user->company->name], 'https'),
                'limit' => $quote->limit,
                'link_name' => $quote->name,
                'datetime_end' => $dateTimeEnd,
                'active' => $quote->active,
                'answer_quotes' => count($quote->answerQuotes) ? $quote->answerQuotes : null,
            ];
        }

        $service = new ShortLinkService($this->poll);

        $shortLinks = FoquzPollShortLink::find()
            ->where(['in', 'link', $service->collectLinks()])
            ->indexBy('link')
            ->all();

        foreach ($items as &$item) {
            $link = $service->getLink($item['key']);

            /** @var FoquzPollShortLink[] $shortLinks */
            if (isset($shortLinks[$link])) {
                $item['short_link'] = $service->getShortLink($shortLinks[$link]->code);
            }else {
                $item['short_link'] = $service->getShortLink($service->saveOne($link));
            }
        }

        return $items;
    }
}