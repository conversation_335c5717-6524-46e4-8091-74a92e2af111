<?php

declare(strict_types=1);

namespace app\modules\foquz\services\quotes;

use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use yii\caching\Dependency;

class QuoteUpdatedAtDependency extends Dependency
{

    protected function generateDependencyData($cache)
    {
        $maxUpdatedAtQuotes = FoquzPollAnswerQuotes::find()->max('updated_at');
        $maxUpdatedAtQuotesDetail = FoquzPollAnswerQuotesDetail::find()->max('updated_at');

        return max($maxUpdatedAtQuotes, $maxUpdatedAtQuotesDetail);
    }
}