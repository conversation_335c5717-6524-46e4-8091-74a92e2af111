<?php

declare(strict_types=1);

namespace app\modules\foquz\services\quotes;

use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\quotes\criteria\AnswerCounter;
use app\modules\foquz\models\quotes\criteria\Criteria;
use app\modules\foquz\models\quotes\criteria\CriteriaGroup;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use Yii;
use yii\caching\CacheInterface;

class QuoteService
{

    private FoquzPollLinkQuotes $linkQuote;
    private CacheInterface $cache;
    private const CACHE_DURATION = 3600 * 24;

    public function __construct(FoquzPollLinkQuotes $linkQuote){
        $this->linkQuote = $linkQuote;
        $this->cache = Yii::$app->cache;
    }


    /**
     * Проверка квоты по ссылкам
     * @return array
     */
    public function checkLinkQuote(): array
    {
        if (!$this->linkQuote->active) {
            return ['result' => false, 'reason' => FoquzPollLinkQuotes::QUOTE_INACTIVE];
        }
        if ($this->linkQuote->datetime_end) {
            if ($this->linkQuote->datetime_end <= date("Y-m-d H:i:s")) {
                return ['result' => false, 'reason' => FoquzPollLinkQuotes::QUOTE_TIME_IS_OVER];
            }
        }
        if ($this->linkQuote->limit) {
            $answersCount = FoquzPollAnswer::find()
                ->where(['quote_id' => $this->linkQuote->id])
                ->andWhere(['status' => FoquzPollAnswer::STATUS_DONE])
                ->count();
            if ($answersCount >= $this->linkQuote->limit) {
                return ['result' => false, 'reason' => FoquzPollLinkQuotes::QUOTE_LIMIT_IS_OVER];
            }
        }

        return ['result' => true, 'reason' => 'OK'];
    }


    private function findCriteria($question_id, $behavior, $criteriaArr)
    {
        foreach ($criteriaArr as $idx => $criteria) {
            if ($criteria->questionId == $question_id &&  $criteria->behavior == $behavior) {
                return $idx;
            }
        }
        return false;
    }

    /**
     * Проверка всех квот по ответам, привязанных к этой квоте по ссылке
     * @param int $pollId
     * @param int $answerId
     * @param array $answersForSave
     * @param FoquzQuestion $currentQuestion
     * @return array
     */
    public function isAnswerQuotesLimitsOver(int $pollId, int $answerId, array $answersForSave, FoquzQuestion $currentQuestion): array
    {
        $answerCounter = new AnswerCounter($pollId);
        foreach ($this->linkQuote->answerQuotes as $aq) {
            $allQuotesResult[$aq->id] = true;
            if ($aq->answers_limit) {
                $dependency = new QuoteUpdatedAtDependency();
                // массив отдельных условий
                $criteriaArr = $this->cache->get('answerQuote:' . $aq->id . ':criteriaArr');
                // массив групп
                $groupsArr = $this->cache->get('answerQuote:' . $aq->id . ':groupsArr');

                if ($criteriaArr === false || $groupsArr === false) {
                    $criteriaArr = is_array($criteriaArr) ? $criteriaArr : [];
                    $groupsArr = is_array($groupsArr) ? $groupsArr : [];
                    $details = $aq->foquzPollAnswerQuotesDetails;
                    foreach ($details as $detail) {
                        $variants = is_array($detail->variants) ? $detail->variants : [];
                        sort($variants);

                        // если вопрос и behavior у условия совпадают с уже созданным Criteria - добавляем
                        // иначе создаем новое условие
                        if (false !== $idx = $this->findCriteria($detail->question_id, $detail->behavior, $criteriaArr)) {
                            $criteriaArr[$idx]->addVariants($variants);
                        } else {
                            $criteriaArr[$idx] = Criteria::Create($detail->question_id, $detail->behavior, $variants, $answerCounter, $aq->logic_operation);
                        }
                    }
                    $groups = $aq->foquzPollAnswerQuotesGroups;
                    foreach ($groups as $group) {
                        $criteriaGroup = CriteriaGroup::Create($group->id, $group->logic_operation, $aq->answers_limit);
                        $details = $group->foquzPollAnswerQuotesDetails;
                        $grCriterias = [];
                        foreach ($details as $detail) {
                            $variants = is_array($detail->variants) ? $detail->variants : [];
                            sort($variants);
                            // если вопрос и behavior у условия совпадают с уже созданным Critetia - добавляем
                            // иначе создаем новое условие
                            if (false !== $idx = $this->findCriteria($detail->question_id, $detail->behavior, $grCriterias)) {
                                $grCriterias[$idx]->addVariants($variants);
                            } else {
                                $grCriterias[] = Criteria::Create($detail->question_id, $detail->behavior, $variants, $answerCounter, $aq->logic_operation);
                            }
                        }

                        $criteriaGroup->addCriterias($grCriterias);

                        $groupsArr[] = $criteriaGroup;
                    }

                    $this->cache->set('answerQuote:' . $aq->id . ':criteriaArr', $criteriaArr, self::CACHE_DURATION, $dependency);
                    $this->cache->set('answerQuote:' . $aq->id . ':groupsArr', $groupsArr, self::CACHE_DURATION, $dependency);
                }

                $answers = $this->getAnswers($answerId, $answersForSave, $currentQuestion);

                // если не заданы условия - учитываем все анкеты
                if(count($criteriaArr) + count($groupsArr) === 0) {

                    if ($answerCounter->getAllAnswersCount() >= $aq->answers_limit) {
                        return [
                            'result' => true,
                            'end_screen' => $aq->end_screen,
                        ];
                    }
                    $allQuotesResult[$aq->id] = false;
                    continue;
                }

                // проверяем отдельные условия
                foreach ($criteriaArr as $criteria) {
                    // еще не было ответа
                    if (!isset($answers[$criteria->questionId])) {
                        $allQuotesResult[$aq->id] = false;
                        continue;
                    }

                    $criteria->check($answers[$criteria->questionId]);
                    $checked = $criteria->checked && ($criteria->answersCount >= $aq->answers_limit);
                    if ($aq->logic_operation === FoquzPollAnswerQuotes::LOGIC_OPERATION_OR) {
                        if ($checked) {
                            return [
                                'result' => true,
                                'end_screen' => $aq->end_screen,
                            ];
                        }
                    } else {
                        if (!$checked) {
                            $allQuotesResult[$aq->id] = false;
                        }
                    }
                }

                // проверяем группы
                foreach ($groupsArr as $group) {
                    $group->check($answers);
                    $checked = $group->checked;
                    if ($aq->logic_operation === FoquzPollAnswerQuotes::LOGIC_OPERATION_OR) {
                        if ($checked) {
                            return [
                                'result' => true,
                                'end_screen' => $aq->end_screen,
                            ];
                        }
                    } else {
                        if (!$checked) {
                            $allQuotesResult[$aq->id] = false;
                        }
                    }
                }

                if ($allQuotesResult[$aq->id] === true) {
                    // логика через И и все условия выполнены
                    if ($aq->logic_operation === FoquzPollAnswerQuotes::LOGIC_OPERATION_AND) {
                        return [
                            'result' => true,
                            'end_screen' => $aq->end_screen,
                        ];
                    }
                }
            }
        }

        return [
            'result' => false,
            'end_screen' => null,
        ];
    }

    /**
     * Получить все ответы с типом "Варианты ответа" на текущий опрос
     * @param int $answerId
     * @param array $answersForSave
     * @param $currentQuestion
     * @return array
     */
    private function getAnswers(int $answerId, array $answersForSave, $currentQuestion): array
    {
        $answerItems = FoquzPollAnswerItem::find()
            ->joinWith('foquzQuestion fq')
            ->joinWith('foquzPollAnswer fa')
            ->where(['foquz_poll_answer_id' => $answerId])
            ->andWhere(['fq.main_question_type' => FoquzQuestion::TYPE_VARIANTS])
            ->andWhere(['fa.status' => FoquzPollAnswer::STATUS_IN_PROGRESS])
            ->indexBy('foquz_question_id')
            ->orderBy(['fq.position' => SORT_ASC])
            ->all()
        ;
        $answers = [];
        /** @var  FoquzPollAnswerItem $answerItem */
        foreach ($answerItems as $questionId => $answerItem) {
            if (!$answerItem->detail_item) {
                if ($answerItem->skipped) {
                    $answers[$questionId] = ['skipped' => 1];
                } else {
                    $answers[$questionId] = [];
                }
            } else {
                if ($answerItem->is_self_variant) {
                    $answers[$questionId]['detail_item'][] = 'is_self_answer';
                } else {
                    /** @var array|string $detailItem */
                    $detailItem = $answerItem->detail_item;
                    if (is_array($detailItem)) {
                        $answers[$questionId]['detail_item'] = $detailItem;
                    } else {
                        $answers[$questionId]['detail_item'] = json_decode($detailItem, true) ?? [];
                    }
                }

            }
            $answers[$questionId]['position'] = $answerItem->foquzQuestion->position;
        }

        // + ответ на текущий вопрос
        if ($currentQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
            $answersForSave[$currentQuestion->id]['position'] = $currentQuestion->position;
            $answers[$currentQuestion->id] = $answersForSave[$currentQuestion->id];
        }


        // убираем ответы после текущего, респондент мог вернуться на предыдущие вопросы
        foreach ($answers as $questionId => $answer) {
            if ($answer['position'] > $currentQuestion->position) {
                unset($answers[$questionId]);
            }
        }

        return $answers;
    }
}