<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\stat\questions;

use app\models\Filial;
use app\modules\foquz\models\FoquzQuestion;
use yii\helpers\ArrayHelper;

class DistibutionScaleQuestionStat extends QuestionStat
{
    /** @var Filial[] $filials */
    private array $filials;
    private array $statistics = [];

    /** @var array $defaultStat Начальная структура для расчета статистики. */
    private array $defaultStat = [];
    private array $variants = [];

    private static ?array $cacheFilials = null;

    public function __construct(FoquzQuestion $question, int $companyId)
    {
        parent::__construct($question);
        $this->donor = $this->question->getMainDonor();
        if (is_null(self::$cacheFilials)) {
            self::$cacheFilials = ArrayHelper::index(Filial::find()
                ->where(['company_id' => $companyId])
                ->with('category')
                ->all(), 'id');
        }
        $this->filials = self::$cacheFilials;
        $this->setDefaultStat();
    }

    public function getItemFields(): array
    {
        return [QuestionStat::FIELD_FILIAL_ID, QuestionStat::FIELD_RATING, QuestionStat::FIELD_ANSWER];
    }

    /**
     * Полное название филиала.
     * @param $filialId
     * @return string
     */
    private function getFilialName($filialId): string
    {
        if (empty($filialId) || empty($this->filials[$filialId])) {
            return 'anonymous';
        }
        $filial = $this->filials[$filialId];
        $name = $filial->category ? $filial->category->name . "/" : '';
        return $name . $filial->name;
    }

    public function countItem(array $item): void
    {
        $filialName = $this->getFilialName($item[QuestionStat::FIELD_FILIAL_ID]);

        if (!isset($this->statistics[$filialName])) {
            $this->statistics[$filialName] = ['count' => 0, 'scale-ratings' => []];
        }
        $this->statistics[$filialName]['count']++;
        $this->answersCount++;

        $answers = $item[QuestionStat::FIELD_ANSWER];

        if (empty($answers)) {
            foreach ($this->question->questionDetails as $questionDetail) {
                $this->statistics[$filialName]['scale-ratings'][$questionDetail->id] = $this->defaultStat;
            }
            $variants = $this->question->getQuestionDetails()->asArray()->all();
            foreach ($variants as $variant) {
                $this->variants[$variant['id']] = $this->getVariant($variant['id']);
            }
            return;
        }
        $answers = json_decode($answers, true);
        if (!is_array($answers)) {
            return;
        }

        //шкала
        foreach ($answers as $idVariant => $rate) {
            $this->collectStats($filialName, $rate, $idVariant);
            if (isset($this->variants[$idVariant])) {
                continue;
            }
            $variant = $this->getVariant($idVariant);
            if (!empty($variant)) {
                $this->variants[$idVariant] = $this->getVariant($idVariant);
            }
        }

        // аггрегация результатов
        foreach ($this->statistics[$filialName]['ratings'] as $idVariant => $rating) {
            $this->statistics[$filialName]['aggregate'][$idVariant]['min'] = min($rating);
            $this->statistics[$filialName]['aggregate'][$idVariant]['max'] = max($rating);
            $this->statistics[$filialName]['aggregate'][$idVariant]['summ'] = array_sum($rating);
            $this->statistics[$filialName]['aggregate'][$idVariant]['mediana'] = $this->getMediana($rating);
            $this->statistics[$filialName]['aggregate'][$idVariant]['count'] = count($rating);
            $this->statistics[$filialName]['aggregate'][$idVariant]['avg'] = $this->statistics[$filialName]['aggregate'][$idVariant]['summ'] / count($rating);
        }
    }

    /**
     * Рассчет медианы для значений массива
     * @param array $item
     * @return float|int|mixed
     */
    private function getMediana(array $item): mixed
    {
        sort($item);
        $count = count($item);
        if ($count % 2 == 0) { // если четное количество - возвращаем среднее арифметическое двух средних элементов
            $mid1 = $item[$count / 2 - 1];
            $mid2 = $item[$count / 2];
            return ($mid1 + $mid2) / 2;
        } else { // просто возвращаем элемент из середины массива
            return $item[floor($count / 2)];
        }
    }

    /**
     * Расчет статистики для филиала.
     * @param string $filialName
     * @param string|int|null $rate
     * @param int $numberVariant
     * @return void
     */
    function collectStats(string $filialName, string|int|null $rate, int $numberVariant): void
    {
        $data = $this->statistics[$filialName]['scale-ratings'];
        $data = $data[$numberVariant] ?? null;

        if (empty($data)) {
            $data = $this->defaultStat;
        }

        $k_prev = 0;
        foreach ($data as $k => $v) {

            if ($k == 0) {
                if ($rate === 0) {
                    $data[$k]++;
                }
            } else {
                if ($rate > $k_prev && $rate <= $k) {
                    $data[$k]++;
                }
            }
            $k_prev = $k;
        }

        $this->statistics[$filialName]['ratings'][$numberVariant][] = $rate;
        $this->statistics[$filialName]['scale-ratings'][$numberVariant] = $data;
    }

    /**
     * Начальная структура для статистика.
     * @return void
     */
    private function setDefaultStat(): void
    {
        $settings = $this->question->scaleRatingSetting;
        $end = $settings->end;
        $st = $end / 10;

        for ($i = 0; $i <= $end; $i += $st) {
            $this->defaultStat[(string)$i] = 0;
        }
    }

    protected function getData(): array
    {
        $data = ['statistics' => $this->statistics];
        $data['variants'] = $this->variants;
        return $data;
    }
}